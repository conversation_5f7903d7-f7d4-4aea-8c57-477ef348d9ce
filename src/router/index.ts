import { createRouter, createWebHistory } from 'vue-router'
import { setupRouteLevelSplitting, createCodeSplittingPlugin } from '@/utils/code-splitting'
import { setupRouterGuards } from './guards'
import { routes } from './routes'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 应用路由级别代码分割优化
router.options.routes = setupRouteLevelSplitting([...router.options.routes])

// 设置路由守卫
setupRouterGuards(router)

export default router
