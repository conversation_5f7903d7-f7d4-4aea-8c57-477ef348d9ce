import { createRouter, createWebHistory } from 'vue-router'
// import HomeView from '../views/HomeView.vue'
import PlaceholderView from '../views/PlaceholderView.vue'
import { setupRouteLevelSplitting, createCodeSplittingPlugin } from '@/utils/code-splitting'
import { setupRouterGuards } from './guards'

// Temporary placeholder component for all routes
const HomeView = PlaceholderView

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/login/index.vue'),
      meta: { title: '登录', hidden: true }
    },
    {
      path: '/',
      name: 'home',
      component: HomeView,
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('@/views/dashboard/index.vue'),
      meta: { title: '仪表板' }
    },
    {
      path: '/organization',
      name: 'organization',
      redirect: '/organization/structure',
      meta: { title: '组织管理' },
      children: [
        {
          path: 'structure',
          name: 'organization-structure',
          component: () => import('../views/organization/index.vue'),
          meta: { title: '组织架构管理' }
        },
        {
          path: 'change',
          name: 'organization-change',
          component: () => import('../views/organization/change.vue'),
          meta: { title: '变更管理' }
        },
        {
          path: 'query',
          name: 'organization-query',
          component: () => import('../views/organization/query/index.vue'),
          meta: { title: '组织查询' }
        },
        {
          path: 'statistics',
          name: 'organization-statistics',
          component: () => import('../views/organization/statistics/index.vue'),
          meta: { title: '组织统计分析' }
        }
      ]
    },
    {
      path: '/position',
      name: 'position',
      component: () => import('../views/organization/position/index.vue'),
      meta: { title: '岗位管理' }
    },
    {
      path: '/establishment',
      name: 'establishment',
      component: () => import('../views/organization/establishment/index.vue'),
      meta: { title: '编制管理' }
    },
    {
      path: '/employee',
      name: 'employee',
      redirect: '/employee/management',
      meta: { title: '员工管理' },
      children: [
        {
          path: 'management',
          name: 'employee-management',
          component: () => import('../views/employee/EmployeeManagement.vue'),
          meta: { title: '员工基础管理' }
        },
        {
          path: 'list',
          name: 'employee-list',
          component: () => import('../views/employee/list/index.vue'),
          meta: { title: '员工列表' }
        },
        {
          path: 'add',
          name: 'employee-add',
          component: () => import('../views/employee/add/index.vue'),
          meta: { title: '新增员工' }
        },
        {
          path: 'detail/:id',
          name: 'employee-detail',
          component: () => import('../views/employee/detail/index.vue'),
          meta: { title: '员工详情' }
        },
        {
          path: 'edit/:id',
          name: 'employee-edit',
          component: () => import('../views/employee/edit/index.vue'),
          meta: { title: '编辑员工' }
        },
        {
          path: 'import',
          name: 'employee-import',
          component: () => import('../views/employee/import/index.vue'),
          meta: { title: '批量导入' }
        },
        {
          path: 'tag',
          name: 'employee-tag',
          component: () => import('../views/employee/tag/index.vue'),
          meta: { title: '标签管理' }
        },
        {
          path: 'talent-honors',
          name: 'talent-honors',
          component: () => import('../views/employee/TalentHonorsManagement.vue'),
          meta: { title: '人才荣誉管理' }
        },
        {
          path: 'education-history',
          name: 'education-history',
          component: () => import('../views/employee/EducationHistoryManagement.vue'),
          meta: { title: '教育经历管理' }
        },
        {
          path: 'family-members',
          name: 'family-members',
          component: () => import('../views/employee/FamilyMemberManagement.vue'),
          meta: { title: '家庭成员管理' }
        },
        {
          path: 'awards-punishments',
          name: 'awards-punishments',
          component: () => import('../views/employee/AwardsPunishmentsManagement.vue'),
          meta: { title: '奖惩记录管理' }
        },
        {
          path: 'vocational-qualifications',
          name: 'vocational-qualifications',
          component: () => import('../views/employee/VocationalQualificationManagement.vue'),
          meta: { title: '职业资格管理' }
        },
        {
          path: 'teacher-qualifications',
          name: 'teacher-qualifications',
          component: () => import('../views/employee/TeacherQualificationManagement.vue'),
          meta: { title: '教师资格管理' }
        },
        {
          path: 'data-change-logs',
          name: 'data-change-logs',
          component: () => import('../views/employee/DataChangeLogManagement.vue'),
          meta: { title: '数据变更日志' }
        }
      ]
    },
    {
      path: '/institution-change-log',
      name: 'institution-change-log',
      component: () => import('../views/institutionChangeLog/InstitutionChangeLogManagement.vue'),
      meta: { title: '机构变更日志管理' }
    },
    {
      path: '/institution-change-log-test',
      name: 'institution-change-log-test',
      component: () => import('../views/institutionChangeLog/InstitutionChangeLogTest.vue'),
      meta: { title: '机构变更日志测试' }
    },
    {
      path: '/ad-hoc-institution',
      name: 'ad-hoc-institution',
      component: () => import('../views/adHocInstitution/AdHocInstitutionManagement.vue'),
      meta: { title: '临时机构管理' }
    },
    {
      path: '/ad-hoc-institution-test',
      name: 'ad-hoc-institution-test',
      component: () => import('../views/adHocInstitution/AdHocInstitutionTest.vue'),
      meta: { title: '临时机构测试' }
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/dashboard/StatisticsDashboard.vue'),
      meta: { title: '统计仪表板' }
    },
    {
      path: '/dashboard-test',
      name: 'dashboard-test',
      component: () => import('../views/dashboard/DashboardTest.vue'),
      meta: { title: '仪表板测试' }
    },
    {
      path: '/staffing',
      name: 'staffing',
      redirect: '/staffing/plans',
      meta: { title: '编制管理' },
      children: [
        {
          path: 'plans',
          name: 'staffing-plans',
          component: () => import('../views/staffing/StaffingPlanManagement.vue'),
          meta: { title: '编制规划管理' }
        },
        {
          path: 'statistics',
          name: 'staffing-statistics',
          component: () => import('../views/staffing/StaffingStatistics.vue'),
          meta: { title: '编制统计分析' }
        },
        {
          path: 'dashboard',
          name: 'staffing-dashboard',
          component: () => import('../views/staffing/StaffingDashboard.vue'),
          meta: { title: '编制管理仪表板' }
        },
        {
          path: 'test',
          name: 'staffing-test',
          component: () => import('../views/staffing/StaffingTest.vue'),
          meta: { title: '编制管理测试' }
        }
      ]
    },
    {
      path: '/workflow',
      name: 'workflow',
      redirect: '/workflow/approval-center',
      meta: { title: '工作流管理' },
      children: [
        // 审批中心
        {
          path: 'approval-center',
          name: 'workflow-approval-center',
          component: () => import('../views/workflow/ApprovalCenter.vue'),
          meta: { title: '审批中心' }
        },
        {
          path: 'my-tasks',
          name: 'workflow-my-tasks',
          component: () => import('../views/workflow/MyTasks.vue'),
          meta: { title: '我的待办' }
        },
        {
          path: 'my-applications',
          name: 'workflow-my-applications',
          component: () => import('../views/workflow/MyApplications.vue'),
          meta: { title: '我的申请' }
        },
        {
          path: 'my-approvals',
          name: 'workflow-my-approvals',
          component: () => import('../views/workflow/MyApprovals.vue'),
          meta: { title: '我的已办' }
        },
        // 流程设计
        {
          path: 'designer/:id?',
          name: 'workflow-designer',
          component: () => import('../views/workflow/ProcessDesigner.vue'),
          meta: { title: '流程设计器' }
        },
        {
          path: 'management',
          name: 'workflow-management',
          component: () => import('../views/workflow/WorkflowManagement.vue'),
          meta: { title: '流程定义管理' }
        },
        // 流程监控
        {
          path: 'monitoring',
          name: 'workflow-monitoring',
          component: () => import('../views/workflow/ProcessMonitoring.vue'),
          meta: { title: '流程监控' }
        },
        {
          path: 'instances',
          name: 'workflow-instances',
          component: () => import('../views/workflow/ProcessInstances.vue'),
          meta: { title: '流程实例管理' }
        },
        {
          path: 'tasks',
          name: 'workflow-tasks',
          component: () => import('../views/workflow/TaskManagement.vue'),
          meta: { title: '任务管理' }
        },
        // 核心流程
        {
          path: 'onboarding',
          name: 'workflow-onboarding',
          component: () => import('../views/workflow/processes/OnboardingProcess.vue'),
          meta: { title: '入职流程' }
        },
        {
          path: 'leave',
          name: 'workflow-leave',
          component: () => import('../views/workflow/processes/LeaveProcess.vue'),
          meta: { title: '请假流程' }
        },
        {
          path: 'transfer',
          name: 'workflow-transfer',
          component: () => import('../views/workflow/processes/TransferProcess.vue'),
          meta: { title: '调岗流程' }
        },
        {
          path: 'resignation',
          name: 'workflow-resignation',
          component: () => import('../views/workflow/processes/ResignationProcess.vue'),
          meta: { title: '离职流程' }
        },
        // 流程详情
        {
          path: 'task/:taskId',
          name: 'workflow-task-detail',
          component: () => import('../views/workflow/TaskDetail.vue'),
          meta: { title: '任务详情' }
        },
        {
          path: 'instance/:instanceId',
          name: 'workflow-instance-detail',
          component: () => import('../views/workflow/InstanceDetail.vue'),
          meta: { title: '流程实例详情' }
        },
        // 设计器演示页面
        {
          path: 'designer-demos/custom-task',
          name: 'workflow-custom-task-demo',
          component: () => import('../views/workflow/designer/CustomTaskDemo.vue'),
          meta: { title: '自定义任务节点演示' }
        },
        {
          path: 'designer-demos/custom-gateway',
          name: 'workflow-custom-gateway-demo',
          component: () => import('../views/workflow/designer/CustomGatewayDemo.vue'),
          meta: { title: '自定义网关节点演示' }
        },
        {
          path: 'designer-demos/import-export',
          name: 'workflow-import-export-demo',
          component: () => import('../views/workflow/designer/ProcessImportExportDemo.vue'),
          meta: { title: '流程导入导出演示' }
        },
        {
          path: 'designer-demos/validation',
          name: 'workflow-validation-demo',
          component: () => import('../views/workflow/designer/ProcessValidationDemo.vue'),
          meta: { title: '流程验证演示' }
        }
      ]
    },
    {
      path: '/notification',
      name: 'notification',
      redirect: '/notification/message-center',
      meta: { title: '通知管理' },
      children: [
        {
          path: 'message-center',
          name: 'message-center',
          component: () => import('../views/notification/MessageCenter.vue'),
          meta: { title: '消息中心' }
        },
        {
          path: 'templates',
          name: 'notification-templates',
          component: () => import('../views/notification/TemplateManagement.vue'),
          meta: { title: '消息模板管理' }
        },
        {
          path: 'settings',
          name: 'notification-settings',
          component: () => import('../views/notification/NotificationSettings.vue'),
          meta: { title: '通知设置' }
        },
        {
          path: 'statistics',
          name: 'notification-statistics',
          component: () => import('../views/notification/NotificationStatistics.vue'),
          meta: { title: '通知统计' }
        }
      ]
    },
    {
      path: '/system',
      name: 'system',
      redirect: '/system/user-management',
      meta: { title: '系统管理' },
      children: [
        {
          path: 'user-management',
          name: 'user-management',
          component: () => import('../views/system/UserManagement.vue'),
          meta: { title: '用户管理' }
        },
        {
          path: 'role-management',
          name: 'role-management',
          component: () => import('../views/system/RoleManagement.vue'),
          meta: { title: '角色管理' }
        },
        {
          path: 'permission-management',
          name: 'permission-management',
          component: () => import('../views/system/PermissionManagement.vue'),
          meta: { title: '权限管理' }
        },
        {
          path: 'system-monitoring',
          name: 'system-monitoring',
          component: () => import('../views/system/SystemMonitoring.vue'),
          meta: { title: '系统监控' }
        },
        {
          path: 'system-settings',
          name: 'system-settings',
          component: () => import('../views/system/SystemSettings.vue'),
          meta: { title: '系统设置' }
        }
      ]
    },
    {
      path: '/personnel-change',
      name: 'personnel-change',
      redirect: '/personnel-change/management',
      meta: { title: '人事变动管理' },
      children: [
        {
          path: 'management',
          name: 'personnel-change-management',
          component: () => import('../views/personnel-change/PersonnelChangeManagement.vue'),
          meta: { title: '人事变动管理' }
        },
        {
          path: 'new-employee-onboarding',
          name: 'personnel-change-new-employee-onboarding',
          component: () => import('../views/personnel-change/NewEmployeeOnboarding.vue'),
          meta: { title: '新教工入职管理' }
        },
        {
          path: 'position-adjustment',
          name: 'personnel-change-position-adjustment',
          component: () => import('../views/personnel-change/PositionAdjustment.vue'),
          meta: { title: '校内岗位调整' }
        },
        {
          path: 'retirement-management',
          name: 'personnel-change-retirement-management',
          component: () => import('../views/personnel-change/RetirementManagement.vue'),
          meta: { title: '退休管理' }
        },
        {
          path: 'leave-school-management',
          name: 'personnel-change-leave-school-management',
          component: () => import('../views/personnel-change/LeaveSchoolManagement.vue'),
          meta: { title: '离校管理' }
        },
        {
          path: 'reemployment-management',
          name: 'personnel-change-reemployment-management',
          component: () => import('../views/personnel-change/ReemploymentManagement.vue'),
          meta: { title: '退休返聘管理' }
        },
        {
          path: 'statistics',
          name: 'personnel-change-statistics',
          component: () => import('../views/personnel-change/PersonnelChangeStatistics.vue'),
          meta: { title: '人事变动统计' }
        }
      ]
    },
    {
      path: '/contract',
      name: 'contract',
      redirect: '/contract/management',
      meta: { title: '合同管理' },
      children: [
        {
          path: 'management',
          name: 'contract-management',
          component: () => import('../views/contract/ContractManagement.vue'),
          meta: { title: '合同管理' }
        },
        {
          path: 'contract-information',
          name: 'contract-contract-information',
          component: () => import('../views/contract/ContractInformationManagement.vue'),
          meta: { title: '合同信息管理' }
        },
        {
          path: 'expiry-reminder',
          name: 'contract-expiry-reminder',
          component: () => import('../views/contract/ContractExpiryReminder.vue'),
          meta: { title: '合同到期提醒' }
        },
        {
          path: 'external-staff-contract',
          name: 'contract-external-staff-contract',
          component: () => import('../views/contract/ExternalStaffContractManagement.vue'),
          meta: { title: '编外人员合同管理' }
        },
        {
          path: 'template-management',
          name: 'contract-template-management',
          component: () => import('../views/contract/ContractTemplateManagement.vue'),
          meta: { title: '合同模板管理' }
        },
        {
          path: 'approval-workflow',
          name: 'contract-approval-workflow',
          component: () => import('../views/contract/ContractApprovalWorkflow.vue'),
          meta: { title: '合同审批工作流' }
        },
        {
          path: 'statistics',
          name: 'contract-statistics',
          component: () => import('../views/contract/ContractStatistics.vue'),
          meta: { title: '合同统计分析' }
        }
      ]
    },
    {
      path: '/attendance',
      name: 'attendance',
      redirect: '/attendance/management',
      meta: { title: '考勤管理' },
      children: [
        {
          path: 'management',
          name: 'attendance-management',
          component: () => import('../views/attendance/AttendanceManagement.vue'),
          meta: { title: '考勤管理首页' }
        },
        {
          path: 'rule-config',
          name: 'attendance-rule-config',
          component: () => import('../views/attendance/AttendanceRuleConfig.vue'),
          meta: { title: '考勤规则设置' }
        },
        {
          path: 'shift-management',
          name: 'attendance-shift-management',
          component: () => import('../views/attendance/ShiftManagement.vue'),
          meta: { title: '排班管理' }
        },
        {
          path: 'clock-in-record',
          name: 'attendance-clock-in-record',
          component: () => import('../views/attendance/ClockInRecordManagement.vue'),
          meta: { title: '打卡记录管理' }
        },
        {
          path: 'mobile-clock-in',
          name: 'attendance-mobile-clock-in',
          component: () => import('../views/attendance/MobileClockIn.vue'),
          meta: { title: '移动打卡' }
        },
        {
          path: 'dashboard',
          name: 'attendance-dashboard',
          component: () => import('../views/attendance/AttendanceDashboard.vue'),
          meta: { title: '考勤看板' }
        },
        {
          path: 'leave-application',
          name: 'attendance-leave-application',
          component: () => import('../views/attendance/LeaveApplication.vue'),
          meta: { title: '请假申请' }
        },
        {
          path: 'leave-application/:id',
          name: 'attendance-leave-application-detail',
          component: () => import('../views/attendance/LeaveApplicationManagement.vue'),
          meta: { title: '请假申请详情' }
        },
        {
          path: 'overtime-application',
          name: 'attendance-overtime-application',
          component: () => import('../views/attendance/OvertimeApplication.vue'),
          meta: { title: '加班申请' }
        },
        {
          path: 'supplement-application',
          name: 'attendance-supplement-application',
          component: () => import('../views/attendance/ClockInSupplementApplication.vue'),
          meta: { title: '补卡申请' }
        },
        {
          path: 'field-work-application',
          name: 'attendance-field-work-application',
          component: () => import('../views/attendance/FieldWorkApplication.vue'),
          meta: { title: '外勤申请' }
        },
        {
          path: 'statistics',
          name: 'attendance-statistics',
          component: () => import('../views/attendance/AttendanceStatistics.vue'),
          meta: { title: '考勤统计分析' }
        },
        {
          path: 'abnormal-analysis',
          name: 'attendance-abnormal-analysis',
          component: () => import('../views/attendance/AbnormalAttendanceAnalysis.vue'),
          meta: { title: '异常考勤分析' }
        },
        {
          path: 'overtime-statistics',
          name: 'attendance-overtime-statistics',
          component: () => import('../views/attendance/OvertimeStatistics.vue'),
          meta: { title: '加班统计分析' }
        },
        {
          path: 'monthly-report',
          name: 'attendance-monthly-report',
          component: () => import('../views/attendance/MonthlyAttendanceReport.vue'),
          meta: { title: '月度考勤报表' }
        },
        {
          path: 'annual-report',
          name: 'attendance-annual-report',
          component: () => import('../views/attendance/AnnualAttendanceReport.vue'),
          meta: { title: '年度考勤报表' }
        },
        {
          path: 'record',
          name: 'attendance-record',
          component: () => import('../views/attendance/AttendanceRecordManagement.vue'),
          meta: { title: '考勤记录管理' }
        },
        {
          path: 'report',
          name: 'attendance-report',
          component: () => import('../views/attendance/AttendanceReportManagement.vue'),
          meta: { title: '考勤报表管理' }
        },
        {
          path: 'report/:id',
          name: 'attendance-report-detail',
          component: () => import('../views/attendance/AttendanceReportManagement.vue'),
          meta: { title: '考勤报表详情' }
        },
        {
          path: 'config',
          name: 'attendance-config',
          component: () => import('../views/attendance/AttendanceConfigManagement.vue'),
          meta: { title: '考勤配置管理' }
        },
        {
          path: 'machine-sync',
          name: 'attendance-machine-sync',
          component: () => import('../views/attendance/AttendanceMachineSync.vue'),
          meta: { title: '考勤机数据同步' }
        },
        {
          path: 'dingtalk-integration',
          name: 'attendance-dingtalk-integration',
          component: () => import('../views/attendance/DingTalkIntegration.vue'),
          meta: { title: '钉钉考勤集成' }
        },
        {
          path: 'data-import',
          name: 'attendance-data-import',
          component: () => import('../views/attendance/AttendanceDataImport.vue'),
          meta: { title: '考勤数据导入' }
        }
      ]
    },
    {
      path: '/salary',
      name: 'salary',
      redirect: '/salary/management',
      meta: { title: '薪酬福利管理' },
      children: [
        {
          path: 'management',
          name: 'salary-management',
          component: () => import('../views/salary/SalaryManagement.vue'),
          meta: { title: '薪酬管理首页' }
        },
        {
          path: 'query',
          name: 'salary-query',
          component: () => import('../views/salary/SalaryQueryManagement.vue'),
          meta: { title: '工资查询管理' }
        },
        {
          path: 'summary',
          name: 'salary-summary',
          component: () => import('../views/salary/SalarySummaryManagement.vue'),
          meta: { title: '薪酬汇总导出' }
        },
        {
          path: 'analysis',
          name: 'salary-analysis',
          component: () => import('../views/salary/SalaryAnalysisManagement.vue'),
          meta: { title: '薪酬分析管理' }
        },
        {
          path: 'config',
          name: 'salary-config',
          component: () => import('../views/salary/SalaryConfigManagement.vue'),
          meta: { title: '薪酬系统配置' }
        },
        {
          path: 'structure',
          name: 'salary-structure',
          component: () => import('../views/salary/SalaryStructure.vue'),
          meta: { title: '薪资结构管理' }
        },
        {
          path: 'standards',
          name: 'salary-standards',
          component: () => import('../views/salary/SalaryStandard.vue'),
          meta: { title: '薪酬标准管理' }
        },
        {
          path: 'personal',
          name: 'salary-personal',
          component: () => import('../views/salary/PersonalSalary.vue'),
          meta: { title: '个人薪资管理' }
        },
        {
          path: 'calculation-test',
          name: 'salary-calculation-test',
          component: () => import('../views/salary/SalaryCalculationTest.vue'),
          meta: { title: '薪资计算测试' }
        },
        {
          path: 'salary-slip',
          name: 'salary-slip',
          component: () => import('../views/salary/SalarySlipView.vue'),
          meta: { title: '工资条查询' }
        },
        {
          path: 'benefits',
          name: 'salary-benefits',
          component: () => import('../views/salary/BenefitManagement.vue'),
          meta: { title: '福利管理' }
        },
        {
          path: 'payroll',
          name: 'salary-payroll',
          component: () => import('../views/salary/PayrollApproval.vue'),
          meta: { title: '工资发放审批' }
        },
        {
          path: 'adjustments',
          name: 'salary-adjustments',
          component: () => import('../views/salary/SalaryAdjustment.vue'),
          meta: { title: '薪酬调整管理' }
        },
        {
          path: 'tax',
          name: 'salary-tax',
          component: () => import('../views/salary/TaxManagement.vue'),
          meta: { title: '税务管理' }
        },
        {
          path: 'comparison',
          name: 'salary-comparison',
          component: () => import('../views/salary/SalaryComparison.vue'),
          meta: { title: '薪资对比分析' }
        },
        {
          path: 'total-analysis',
          name: 'salary-total-analysis',
          component: () => import('../views/salary/SalaryTotalAnalysis.vue'),
          meta: { title: '薪资总额分析' }
        },
        {
          path: 'structure-analysis',
          name: 'salary-structure-analysis',
          component: () => import('../views/salary/SalaryStructureAnalysis.vue'),
          meta: { title: '薪资结构分析' }
        },
        {
          path: 'trend-analysis',
          name: 'salary-trend-analysis',
          component: () => import('../views/salary/SalaryTrendAnalysis.vue'),
          meta: { title: '薪资趋势分析' }
        },
        {
          path: 'payroll-report',
          name: 'salary-payroll-report',
          component: () => import('../views/salary/PayrollReport.vue'),
          meta: { title: '工资发放表' }
        },
        {
          path: 'tax-report',
          name: 'salary-tax-report',
          component: () => import('../views/salary/TaxReport.vue'),
          meta: { title: '个税申报表' }
        },
        {
          path: 'data-import',
          name: 'salary-data-import',
          component: () => import('../views/salary/SalaryDataImport.vue'),
          meta: { title: '薪资数据导入' }
        },
        {
          path: 'bank-payment-export',
          name: 'salary-bank-payment-export',
          component: () => import('../views/salary/BankPaymentExport.vue'),
          meta: { title: '银行代发导出' }
        },
        {
          path: 'data-backup',
          name: 'salary-data-backup',
          component: () => import('../views/salary/SalaryDataBackup.vue'),
          meta: { title: '薪资数据备份' }
        },
        {
          path: 'subsidy-application',
          name: 'salary-subsidy-application',
          component: () => import('../views/salary/SubsidyApplication.vue'),
          meta: { title: '补贴申请流程' }
        },
        {
          path: 'salary-dispute',
          name: 'salary-dispute',
          component: () => import('../views/salary/SalaryDispute.vue'),
          meta: { title: '薪资异议处理' }
        }
      ]
    },
    // P2优先级模块路由
    {
      path: '/recruitment',
      name: 'recruitment',
      redirect: '/recruitment/management',
      meta: { title: '招聘管理' },
      children: [
        {
          path: 'management',
          name: 'recruitment-management',
          component: () => import('../views/recruitment/RecruitmentManagement.vue'),
          meta: { title: '招聘管理首页' }
        },
        {
          path: 'plans',
          name: 'recruitment-plans',
          component: () => import('../views/recruitment/RecruitmentPlanManagement.vue'),
          meta: { title: '招聘计划管理' }
        },
        {
          path: 'positions',
          name: 'recruitment-positions',
          component: () => import('../views/recruitment/JobPositionManagement.vue'),
          meta: { title: '岗位信息管理' }
        },
        {
          path: 'applicants',
          name: 'recruitment-applicants',
          component: () => import('../views/recruitment/ApplicantManagement.vue'),
          meta: { title: '应聘人员管理' }
        },
        {
          path: 'applications',
          name: 'recruitment-applications',
          component: () => import('../views/recruitment/JobApplicationManagement.vue'),
          meta: { title: '申请流程管理' }
        },
        {
          path: 'talent-pool',
          name: 'recruitment-talent-pool',
          component: () => import('../views/recruitment/TalentPoolManagement.vue'),
          meta: { title: '人才库管理' }
        }
      ]
    },
    {
      path: '/faculty-development',
      name: 'faculty-development',
      redirect: '/faculty-development/management',
      meta: { title: '师资发展管理' },
      children: [
        {
          path: 'management',
          name: 'faculty-development-management',
          component: () => import('../views/faculty-development/FacultyDevelopmentManagement.vue'),
          meta: { title: '师资发展管理首页' }
        },
        {
          path: 'training-plans',
          name: 'faculty-development-training-plans',
          component: () => import('../views/faculty-development/TrainingPlanManagement.vue'),
          meta: { title: '培训计划管理' }
        },
        {
          path: 'training-records',
          name: 'faculty-development-training-records',
          component: () => import('../views/faculty-development/TrainingRecordManagement.vue'),
          meta: { title: '培训记录管理' }
        },
        {
          path: 'further-study',
          name: 'faculty-development-further-study',
          component: () => import('../views/faculty-development/FurtherStudyManagement.vue'),
          meta: { title: '在职学历进修' }
        },
        {
          path: 'social-practice',
          name: 'faculty-development-social-practice',
          component: () => import('../views/faculty-development/SocialPracticeManagement.vue'),
          meta: { title: '社会实践管理' }
        }
      ]
    },
    {
      path: '/appraisal',
      name: 'appraisal',
      redirect: '/appraisal/management',
      meta: { title: '考核管理' },
      children: [
        {
          path: 'management',
          name: 'appraisal-management',
          component: () => import('../views/appraisal/AppraisalManagement.vue'),
          meta: { title: '考核管理首页' }
        },
        {
          path: 'schemes',
          name: 'appraisal-schemes',
          component: () => import('../views/appraisal/AppraisalSchemeManagement.vue'),
          meta: { title: '考核方案管理' }
        },
        {
          path: 'templates',
          name: 'appraisal-templates',
          component: () => import('../views/appraisal/AppraisalTemplateManagement.vue'),
          meta: { title: '考核模板管理' }
        },
        {
          path: 'records',
          name: 'appraisal-records',
          component: () => import('../views/appraisal/AppraisalRecordManagement.vue'),
          meta: { title: '考核记录管理' }
        },
        {
          path: 'statistics',
          name: 'appraisal-statistics',
          component: () => import('../views/appraisal/AppraisalStatisticsManagement.vue'),
          meta: { title: '统计分析' }
        }
      ]
    },
    {
      path: '/title-evaluation',
      name: 'title-evaluation',
      redirect: '/title-evaluation/management',
      meta: { title: '职称评聘管理' },
      children: [
        {
          path: 'management',
          name: 'title-evaluation-management',
          component: () => import('../views/title-evaluation/TitleEvaluationManagement.vue'),
          meta: { title: '职称评聘管理首页' }
        },
        {
          path: 'applications',
          name: 'title-evaluation-applications',
          component: () => import('../views/title-evaluation/TitleApplicationManagement.vue'),
          meta: { title: '职称申报管理' }
        },
        {
          path: 'experts',
          name: 'title-evaluation-experts',
          component: () => import('../views/title-evaluation/ExpertManagement.vue'),
          meta: { title: '评审专家管理' }
        },
        {
          path: 'reviews',
          name: 'title-evaluation-reviews',
          component: () => import('../views/title-evaluation/ReviewProcessManagement.vue'),
          meta: { title: '评审流程管理' }
        },
        {
          path: 'standards',
          name: 'title-evaluation-standards',
          component: () => import('../views/title-evaluation/TitleStandardManagement.vue'),
          meta: { title: '职称标准管理' }
        }
      ]
    },
    {
      path: '/comprehensive-service',
      name: 'comprehensive-service',
      redirect: '/comprehensive-service/management',
      meta: { title: '综合服务管理' },
      children: [
        {
          path: 'management',
          name: 'comprehensive-service-management',
          component: () => import('../views/comprehensive-service/ComprehensiveServiceManagement.vue'),
          meta: { title: '综合服务管理首页' }
        },
        {
          path: 'votes',
          name: 'comprehensive-service-votes',
          component: () => import('../views/comprehensive-service/VoteManagement.vue'),
          meta: { title: '投票管理' }
        },
        {
          path: 'travel-applications',
          name: 'comprehensive-service-travel-applications',
          component: () => import('../views/comprehensive-service/TravelApplicationManagement.vue'),
          meta: { title: '出国申请管理' }
        },
        {
          path: 'document-loans',
          name: 'comprehensive-service-document-loans',
          component: () => import('../views/comprehensive-service/DocumentLoanManagement.vue'),
          meta: { title: '证照借还管理' }
        },
        {
          path: 'travel-records',
          name: 'comprehensive-service-travel-records',
          component: () => import('../views/comprehensive-service/TravelRecordManagement.vue'),
          meta: { title: '出境记录管理' }
        }
      ]
    },
    {
      path: '/dashboard-management',
      name: 'dashboard-management',
      redirect: '/dashboard-management/overview',
      meta: { title: '领导驾驶舱' },
      children: [
        {
          path: 'overview',
          name: 'dashboard-management-overview',
          component: () => import('../views/dashboard/ExecutiveDashboard.vue'),
          meta: { title: '领导驾驶舱首页' }
        },
        {
          path: 'reports',
          name: 'dashboard-management-reports',
          component: () => import('../views/dashboard/ReportManagement.vue'),
          meta: { title: '报表管理' }
        },
        {
          path: 'statistics',
          name: 'dashboard-management-statistics',
          component: () => import('../views/dashboard/StatisticsAnalysis.vue'),
          meta: { title: '统计分析' }
        },
        {
          path: 'real-time',
          name: 'dashboard-management-real-time',
          component: () => import('../views/dashboard/RealTimeMonitoring.vue'),
          meta: { title: '实时监控' }
        }
      ]
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/test/organization-chart',
      name: 'organization-chart-test',
      component: () => import('../views/test/OrganizationChartTest.vue'),
      meta: { title: '组织架构图测试' }
    },
    {
      path: '/demo/notification',
      name: 'notification-demo',
      component: () => import('../views/demo/NotificationDemo.vue'),
      meta: { title: '消息推送演示' }
    },
    {
      path: '/demo/offline',
      name: 'offline-demo',
      component: () => import('../views/demo/OfflineDemo.vue'),
      meta: { title: '离线缓存演示' }
    },
    {
      path: '/demo/advanced-search',
      name: 'advanced-search-demo',
      component: () => import('../views/demo/AdvancedSearchDemo.vue'),
      meta: { title: '高级搜索组件演示' }
    },
    {
      path: '/demo/search-experience',
      name: 'search-experience-demo',
      component: () => import('../views/demo/SearchExperienceDemo.vue'),
      meta: { title: '搜索体验优化演示' }
    },
    {
      path: '/demo/data-export',
      name: 'data-export-demo',
      component: () => import('../views/demo/DataExportDemo.vue'),
      meta: { title: '数据导出组件演示' }
    },
    {
      path: '/demo/file-upload',
      name: 'file-upload-demo',
      component: () => import('../views/demo/FileUploadDemo.vue'),
      meta: { title: '文件上传组件演示' }
    },
    {
      path: '/demo/upload-progress',
      name: 'upload-progress-demo',
      component: () => import('../views/demo/UploadProgressDemo.vue'),
      meta: { title: '上传进度管理演示' }
    },
    {
      path: '/demo/lazy-load',
      name: 'lazy-load-demo',
      component: () => import('../views/demo/LazyLoadDemo.vue'),
      meta: { title: '图片懒加载演示' }
    },
    {
      path: '/demo/code-splitting',
      name: 'code-splitting-demo',
      component: () => import('../views/demo/CodeSplittingDemo.vue'),
      meta: { title: '代码分割优化演示' }
    },
    {
      path: '/demo/cache-strategy',
      name: 'cache-strategy-demo', 
      component: () => import('../views/demo/CacheStrategyDemo.vue'),
      meta: { title: '缓存策略优化演示' }
    },
    {
      path: '/demo/first-screen',
      name: 'first-screen-demo',
      component: () => import('../views/demo/FirstScreenDemo.vue'),
      meta: { title: '首屏加载优化演示' }
    },
    {
      path: '/demo/accessibility',
      name: 'accessibility-demo',
      component: () => import('../views/demo/AccessibilityDemo.vue'),
      meta: { title: '无障碍功能演示' }
    },
    {
      path: '/demo/color-contrast',
      name: 'color-contrast-demo',
      component: () => import('../views/demo/ColorContrastDemo.vue'),
      meta: { title: '颜色对比度检查' }
    },
    {
      path: '/demo/date-range-picker',
      name: 'date-range-picker-demo',
      component: () => import('../views/demo/DateRangePickerDemo.vue'),
      meta: { title: '日期范围选择器演示' }
    },
    {
      path: '/demo/time-range-picker',
      name: 'time-range-picker-demo',
      component: () => import('../views/demo/TimeRangePickerDemo.vue'),
      meta: { title: '时间范围选择器演示' }
    },
    {
      path: '/demo/user-selector',
      name: 'user-selector-demo',
      component: () => import('../views/demo/UserSelectorDemo.vue'),
      meta: { title: '用户选择器演示' }
    },
    {
      path: '/demo/department-selector',
      name: 'department-selector-demo',
      component: () => import('../views/demo/DepartmentSelectorDemo.vue'),
      meta: { title: '部门选择器演示' }
    },
    {
      path: '/demo/virtual-list',
      name: 'virtual-list-demo',
      component: () => import('../views/demo/VirtualListDemo.vue'),
      meta: { title: '虚拟滚动列表演示' }
    },
    {
      path: '/demo/virtual-table',
      name: 'virtual-table-demo',
      component: () => import('../views/demo/VirtualTableDemo.vue'),
      meta: { title: '虚拟滚动表格演示' }
    },
    {
      path: '/demo/data-table-enhanced',
      name: 'data-table-enhanced-demo',
      component: () => import('../views/demo/DataTableEnhancedDemo.vue'),
      meta: { title: '数据表格增强演示' }
    },
    {
      path: '/demo/table-toolbar',
      name: 'table-toolbar-demo',
      component: () => import('../views/demo/TableToolbarDemo.vue'),
      meta: { title: '表格工具栏演示' }
    },
    {
      path: '/demo/editable-table',
      name: 'editable-table-demo',
      component: () => import('../views/demo/EditableTableDemo.vue'),
      meta: { title: '可编辑表格演示' }
    },
    {
      path: '/demo/skeleton',
      name: 'skeleton-demo',
      component: () => import('../views/demo/SkeletonDemo.vue'),
      meta: { title: '骨架屏加载演示' }
    },
    {
      path: '/demo/attachment-manager',
      name: 'attachment-manager-demo',
      component: () => import('../views/demo/AttachmentManagerDemo.vue'),
      meta: { title: '附件管理器演示' }
    },
    {
      path: '/demo/file-preview',
      name: 'file-preview-demo',
      component: () => import('../views/demo/FilePreviewDemo.vue'),
      meta: { title: '文件预览演示' }
    },
    {
      path: '/demo/animation-components',
      name: 'animation-components-demo',
      component: () => import('../views/demo/AnimationComponentsDemo.vue'),
      meta: { title: '动画组件演示' }
    },
    {
      path: '/demo/list-optimization',
      name: 'list-optimization-demo',
      component: () => import('../views/demo/ListOptimizationDemo.vue'),
      meta: { title: '列表优化演示' }
    },
    {
      path: '/employee/list-optimized',
      name: 'employee-list-optimized',
      component: () => import('../views/employee/list/OptimizedIndex.vue'),
      meta: { title: '员工列表（优化版）' }
    },
    {
      path: '/demo/list-filter',
      name: 'list-filter-demo',
      component: () => import('../views/demo/ListFilterDemo.vue'),
      meta: { title: '列表筛选优化演示' }
    },
    {
      path: '/demo/form-validation',
      name: 'form-validation-demo',
      component: () => import('../views/demo/FormValidationDemo.vue'),
      meta: { title: '表单验证增强演示' }
    },
    {
      path: '/demo/form-builder',
      name: 'form-builder-demo',
      component: () => import('../views/demo/FormBuilderDemo.vue'),
      meta: { title: '表单布局优化演示' }
    },
    {
      path: '/demo/error-handling',
      name: 'error-handling-demo',
      component: () => import('../views/demo/ErrorHandlingDemo.vue'),
      meta: { title: '错误提示优化演示' }
    },
    {
      path: '/demo/operation-feedback',
      name: 'operation-feedback-demo',
      component: () => import('../views/demo/OperationFeedbackDemo.vue'),
      meta: { title: '操作反馈优化演示' }
    },
    {
      path: '/demo/empty-state',
      name: 'empty-state-demo',
      component: () => import('../views/demo/EmptyStateDemo.vue'),
      meta: { title: '空状态设计演示' }
    },
    {
      path: '/demo/batch-operation',
      name: 'batch-operation-demo',
      component: () => import('../views/demo/BatchOperationDemo.vue'),
      meta: { title: '批量操作优化演示' }
    },
    {
      path: '/demo/data-entry',
      name: 'data-entry-demo',
      component: () => import('../views/demo/DataEntryDemo.vue'),
      meta: { title: '数据录入优化演示' }
    },
    {
      path: '/demo/detail-layout',
      name: 'detail-layout-demo',
      component: () => import('../views/demo/DetailLayoutDemo.vue'),
      meta: { title: '详情页面布局演示' }
    },
    {
      path: '/demo/step-form',
      name: 'step-form-demo',
      component: () => import('../views/demo/StepFormDemo.vue'),
      meta: { title: '分步表单优化演示' }
    },
    {
      path: '/demo/mobile-list',
      name: 'mobile-list-demo',
      component: () => import('../views/demo/MobileListDemo.vue'),
      meta: { title: '移动端列表优化演示' }
    },
    {
      path: '/demo/tablet-layout',
      name: 'tablet-layout-demo',
      component: () => import('../views/demo/TabletLayoutDemo.vue'),
      meta: { title: '平板布局优化演示' }
    },
    {
      path: '/demo/scroll-behavior',
      name: 'scroll-behavior-demo',
      component: () => import('../views/demo/ScrollBehaviorDemo.vue'),
      meta: { title: '滚动行为优化演示' }
    },
    {
      path: '/demo/keyboard-navigation',
      name: 'keyboard-navigation-demo',
      component: () => import('../views/demo/KeyboardNavigationDemo.vue'),
      meta: { title: '键盘导航支持演示' }
    },
    {
      path: '/demo/context-menu',
      name: 'context-menu-demo',
      component: () => import('../views/demo/ContextMenuDemo.vue'),
      meta: { title: '右键菜单演示' }
    },
    {
      path: '/demo/pwa-enhancement',
      name: 'pwa-enhancement-demo',
      component: () => import('../views/demo/PWAEnhancementDemo.vue'),
      meta: { title: 'PWA功能增强演示' }
    },
    {
      path: '/demo/print-optimization',
      name: 'print-optimization-demo',
      component: () => import('../views/demo/PrintOptimizationDemo.vue'),
      meta: { title: '打印样式优化演示' }
    }
  ],
})

// 应用路由级别代码分割优化
router.options.routes = setupRouteLevelSplitting(router.options.routes)

// 设置路由守卫
setupRouterGuards(router)

export default router
