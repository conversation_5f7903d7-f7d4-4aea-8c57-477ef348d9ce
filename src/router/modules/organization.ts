import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layouts/index.vue'

const organizationRoutes: RouteRecordRaw = {
  path: '/organization',
  component: Layout,
  name: 'Organization',
  meta: {
    title: '组织管理',
    icon: 'OfficeBuilding',
    order: 1
  },
  children: [
    {
      path: 'structure',
      name: 'OrganizationStructure',
      component: () => import('@/views/organization/index.vue'),
      meta: {
        title: '组织架构',
        icon: 'Share',
        permissions: ['organization:view']
      }
    },
    {
      path: 'position',
      name: 'PositionManagement',
      component: () => import('@/views/organization/position/index.vue'),
      meta: {
        title: '岗位管理',
        icon: 'Postcard',
        permissions: ['position:view']
      }
    },
    {
      path: 'establishment',
      name: 'EstablishmentManagement',
      component: () => import('@/views/organization/establishment/index.vue'),
      meta: {
        title: '编制管理',
        icon: 'Tickets',
        permissions: ['establishment:view']
      }
    },
    {
      path: 'change',
      name: 'OrganizationChange',
      component: () => import('@/views/organization/change/index.vue'),
      meta: {
        title: '组织变更',
        icon: 'Switch',
        permissions: ['organization:change:view']
      }
    }
  ]
}

export default organizationRoutes
