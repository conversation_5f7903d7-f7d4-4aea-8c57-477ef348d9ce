import type { RouteRecordRaw } from 'vue-router'

const demoRoutes: RouteRecordRaw[] = [
  {
    path: '/demo',
    name: 'Demo',
    component: () => import('@/layouts/default/index.vue'),
    meta: { title: '功能演示', icon: 'Guide'
  },
    children: [
      {
        path: 'workflow',
        name: 'WorkflowDemo',
        meta: { title: '工作流演示'
  },
        children: [
          {
            path: 'import-export',
            name: 'ImportExportDemo',
            component: () => import('@/views/demo/workflow/ImportExportDemo.vue'),
            meta: { title: '导入导出演示' }
          },
          {
            path: 'validation-panel',
            name: 'ValidationPanelDemo',
            component: () => import('@/views/demo/workflow/ValidationPanelDemo.vue'),
            meta: { title: '流程验证演示' }
          },
          {
            path: 'history-panel',
            name: 'HistoryPanelDemo',
            component: () => import('@/views/demo/workflow/HistoryPanelDemo.vue'),
            meta: { title: '历史记录演示' }
          },
          {
            path: 'shortcut-panel',
            name: 'ShortcutPanelDemo',
            component: () => import('@/views/demo/workflow/ShortcutPanelDemo.vue'),
            meta: { title: '快捷键面板演示' }
          },
          {
            path: 'process-minimap',
            name: 'ProcessMinimapDemo',
            component: () => import('@/views/demo/workflow/ProcessMinimapDemo.vue'),
            meta: { title: '小地图导航演示' }
          },
          {
            path: 'alignment-guides',
            name: 'AlignmentGuidesDemo',
            component: () => import('@/views/demo/workflow/AlignmentGuidesDemo.vue'),
            meta: { title: '对齐辅助线演示' }
          },
          {
            path: 'template-manager',
            name: 'TemplateManagerDemo',
            component: () => import('@/views/demo/workflow/TemplateManagerDemo.vue'),
            meta: { title: '模板管理演示' }
          }
        ]
      },
      {
        path: 'file-upload',
        name: 'FileUploadDemo',
        component: () => import('@/views/demo/FileUploadDemo.vue'),
        meta: { 
          title: '文件上传演示',
          requiresAuth: true
        }
      },
      {
        path: 'advanced-search',
        name: 'AdvancedSearchDemo',
        component: () => import('@/views/demo/AdvancedSearchDemo.vue'),
        meta: { 
          title: '高级搜索演示',
          requiresAuth: true
        }
      },
      {
        path: 'data-export',
        name: 'DataExportDemo',
        component: () => import('@/views/demo/DataExportDemo.vue'),
        meta: { 
          title: '数据导出演示',
          requiresAuth: true
        }
      },
      {
        path: 'search-experience',
        name: 'SearchExperienceDemo',
        component: () => import('@/views/demo/SearchExperienceDemo.vue'),
        meta: { 
          title: '搜索体验演示',
          requiresAuth: true
        }
      },
      {
        path: 'upload-progress',
        name: 'UploadProgressDemo',
        component: () => import('@/views/demo/UploadProgressDemo.vue'),
        meta: { 
          title: '上传进度演示',
          requiresAuth: true
        }
      },
      {
        path: 'attachment-manager',
        name: 'AttachmentManagerDemo',
        component: () => import('@/views/demo/AttachmentManagerDemo.vue'),
        meta: { 
          title: '附件管理器演示',
          requiresAuth: true
        }
      },
      {
        path: 'file-preview',
        name: 'FilePreviewDemo',
        component: () => import('@/views/demo/FilePreviewDemo.vue'),
        meta: { 
          title: '文件预览演示',
          requiresAuth: true
        }
      },
      {
        path: 'screen-reader',
        name: 'ScreenReaderDemo',
        component: () => import('@/views/demo/ScreenReaderDemo.vue'),
        meta: { 
          title: '屏幕阅读器支持演示',
          requiresAuth: true
        }
      },
      {
        path: 'i18n',
        name: 'I18nDemo',
        component: () => import('@/views/demo/I18nDemo.vue'),
        meta: { 
          title: '国际化演示',
          requiresAuth: true
        }
      },
      {
        path: 'theme',
        name: 'ThemeDemo',
        component: () => import('@/views/demo/ThemeDemo.vue'),
        meta: { 
          title: '主题系统演示',
          requiresAuth: true
        }
      },
      {
        path: 'export-progress',
        name: 'ExportProgressDemo',
        component: () => import('@/views/demo/ExportProgressDemo.vue'),
        meta: { 
          title: '导出进度演示',
          requiresAuth: true
        }
      },
      {
        path: 'virtual-table',
        name: 'VirtualTableDemo',
        component: () => import('@/views/demo/VirtualTableDemo.vue'),
        meta: { 
          title: '虚拟表格演示',
          requiresAuth: true
        }
      },
      {
        path: 'lazy-image',
        name: 'LazyImageDemo',
        component: () => import('@/views/demo/LazyImageDemo.vue'),
        meta: { 
          title: '图片懒加载演示',
          requiresAuth: true
        }
      }
    ]
  }
]

export default demoRoutes