/**
 * 工作流路由配置
 * @module router/modules/workflow
 */

import type { RouteRecordRaw } from 'vue-router'

/**
 * 工作流路由配置
 */
const workflowRoutes: RouteRecordRaw = {
  path: '/workflow',
  name: 'workflow',
  redirect: '/workflow/approval-center',
  meta: {
    title: '工作流管理',
    icon: 'workflow',
    requiresAuth: true,
    permissions: ['workflow:view']
  },
  children: [
    // 审批中心模块
    {
      path: 'approval-center',
      name: 'workflow-approval-center',
      component: () => import('@/views/workflow/ApprovalCenter.vue'),
      meta: {
        title: '审批中心',
        icon: 'approval',
        requiresAuth: true,
        permissions: ['workflow:approval:view']
      }
    },
    {
      path: 'approval/todo',
      name: 'workflow-approval-todo',
      component: () => import('@/views/workflow/approval/TodoTasks.vue'),
      meta: {
        title: '待办任务',
        icon: 'todo',
        requiresAuth: true,
        permissions: ['workflow:approval:todo']
      }
    },
    {
      path: 'approval/completed',
      name: 'workflow-approval-completed',
      component: () => import('@/views/workflow/approval/CompletedTasks.vue'),
      meta: {
        title: '已办任务',
        icon: 'done',
        requiresAuth: true,
        permissions: ['workflow:approval:completed']
      }
    },
    {
      path: 'approval/start',
      name: 'workflow-approval-start',
      component: () => import('@/views/workflow/approval/StartProcess.vue'),
      meta: {
        title: '发起申请',
        icon: 'start',
        requiresAuth: true,
        permissions: ['workflow:approval:start']
      }
    },
    {
      path: 'approval/query',
      name: 'workflow-approval-query',
      component: () => import('@/views/workflow/approval/ProcessQuery.vue'),
      meta: {
        title: '流程查询',
        icon: 'search',
        requiresAuth: true,
        permissions: ['workflow:approval:query']
      }
    },
    {
      path: 'my-tasks',
      name: 'workflow-my-tasks',
      component: () => import('@/views/workflow/MyTasks.vue'),
      meta: {
        title: '我的待办',
        icon: 'todo',
        requiresAuth: true,
        permissions: ['workflow:task:view']
      }
    },
    {
      path: 'my-applications',
      name: 'workflow-my-applications',
      component: () => import('@/views/workflow/MyApplications.vue'),
      meta: {
        title: '我的申请',
        icon: 'application',
        requiresAuth: true,
        permissions: ['workflow:application:view']
      }
    },
    {
      path: 'my-approvals',
      name: 'workflow-my-approvals',
      component: () => import('@/views/workflow/MyApprovals.vue'),
      meta: {
        title: '我的已办',
        icon: 'done',
        requiresAuth: true,
        permissions: ['workflow:approval:history']
      }
    },

    // 流程设计模块
    {
      path: 'designer/:id?',
      name: 'workflow-designer',
      component: () => import('@/views/workflow/ProcessDesigner.vue'),
      meta: {
        title: '流程设计器',
        icon: 'designer',
        requiresAuth: true,
        permissions: ['workflow:designer:edit'],
        hidden: true // 不在菜单中显示
      }
    },
    {
      path: 'management',
      name: 'workflow-management',
      component: () => import('@/views/workflow/WorkflowManagement.vue'),
      meta: {
        title: '流程定义管理',
        icon: 'process',
        requiresAuth: true,
        permissions: ['workflow:definition:manage']
      }
    },
    {
      path: 'model-management',
      name: 'workflow-model-management',
      component: () => import('@/views/workflow/ModelManagement.vue'),
      meta: {
        title: '流程模型管理',
        icon: 'model',
        requiresAuth: true,
        permissions: ['workflow:model:manage']
      }
    },

    // 流程监控模块
    {
      path: 'monitoring',
      name: 'workflow-monitoring',
      component: () => import('@/views/workflow/ProcessMonitoring.vue'),
      meta: {
        title: '流程监控',
        icon: 'monitor',
        requiresAuth: true,
        permissions: ['workflow:monitor:view']
      }
    },
    {
      path: 'instances',
      name: 'workflow-instances',
      component: () => import('@/views/workflow/ProcessInstances.vue'),
      meta: {
        title: '流程实例管理',
        icon: 'instance',
        requiresAuth: true,
        permissions: ['workflow:instance:manage']
      }
    },
    {
      path: 'tasks',
      name: 'workflow-tasks',
      component: () => import('@/views/workflow/TaskManagement.vue'),
      meta: {
        title: '任务管理',
        icon: 'task',
        requiresAuth: true,
        permissions: ['workflow:task:manage']
      }
    },

    // 核心流程模块
    {
      path: 'processes',
      name: 'workflow-processes',
      redirect: '/workflow/processes/onboarding',
      meta: {
        title: '核心流程',
        icon: 'core-process',
        requiresAuth: true,
        permissions: ['workflow:process:view']
      },
      children: [
        {
          path: 'onboarding',
          name: 'workflow-onboarding',
          component: () => import('@/views/workflow/processes/OnboardingProcess.vue'),
          meta: {
            title: '入职流程',
            icon: 'onboarding',
            requiresAuth: true,
            permissions: ['workflow:process:onboarding']
          }
        },
        {
          path: 'onboarding/department-approval',
          name: 'DepartmentApprovalList',
          component: () => import('@/workflow/views/approval/DepartmentApprovalList.vue'),
          meta: {
            title: '部门审批任务',
            requiresAuth: true,
            permissions: ['workflow:approval:department'],
            hidden: true
          }
        },
        {
          path: 'onboarding/department-approval/:id',
          name: 'DepartmentApproval',
          component: () => import('@/workflow/components/onboarding/DepartmentApproval.vue'),
          meta: {
            title: '部门审批',
            requiresAuth: true,
            permissions: ['workflow:approval:department'],
            hidden: true
          }
        },
        {
          path: 'onboarding/hr-approval',
          name: 'HRApprovalList',
          component: () => import('@/workflow/views/approval/HRApprovalList.vue'),
          meta: {
            title: '人事审批任务',
            requiresAuth: true,
            permissions: ['workflow:approval:hr'],
            hidden: true
          }
        },
        {
          path: 'onboarding/hr-review/:id',
          name: 'HRReview',
          component: () => import('@/workflow/components/onboarding/HRReview.vue'),
          meta: {
            title: '人事初审',
            requiresAuth: true,
            permissions: ['workflow:approval:hr'],
            hidden: true
          }
        },
        {
          path: 'onboarding/hr-final/:id',
          name: 'HRFinalApproval',
          component: () => import('@/workflow/components/onboarding/HRFinalApproval.vue'),
          meta: {
            title: '人事终审',
            requiresAuth: true,
            permissions: ['workflow:approval:hr:final'],
            hidden: true
          }
        },
        {
          path: 'leave',
          name: 'workflow-leave',
          component: () => import('@/views/workflow/processes/LeaveProcess.vue'),
          meta: {
            title: '请假流程',
            icon: 'leave',
            requiresAuth: true,
            permissions: ['workflow:process:leave']
          }
        },
        {
          path: 'leave/school-leader-approval/:id',
          name: 'SchoolLeaderApproval',
          component: () => import('@/views/workflow/process/leave/SchoolLeaderApproval.vue'),
          meta: {
            title: '校领导审批',
            requiresAuth: true,
            permissions: ['workflow:approval:school-leader'],
            hidden: true
          }
        },
        {
          path: 'leave/records',
          name: 'workflow-leave-records',
          component: () => import('@/views/workflow/process/leave/LeaveRecordManagement.vue'),
          meta: {
            title: '请假记录管理',
            requiresAuth: true,
            permissions: ['workflow:leave:records']
          }
        },
        {
          path: 'leave/statistics',
          name: 'workflow-leave-statistics',
          component: () => import('@/views/workflow/leave/statistics/LeaveStatisticsReport.vue'),
          meta: {
            title: '请假统计报表',
            requiresAuth: true,
            permissions: ['workflow:leave:statistics']
          }
        },
        {
          path: 'transfer',
          name: 'workflow-transfer',
          component: () => import('@/views/workflow/processes/TransferProcess.vue'),
          meta: {
            title: '调岗流程',
            icon: 'transfer',
            requiresAuth: true,
            permissions: ['workflow:process:transfer']
          }
        },
        {
          path: 'transfer/apply',
          name: 'workflow-transfer-apply',
          component: () => import('@/views/workflow/transfer/TransferApplication.vue'),
          meta: {
            title: '发起调岗申请',
            requiresAuth: true,
            permissions: ['workflow:transfer:apply'],
            hidden: true
          }
        },
        {
          path: 'transfer/edit/:id',
          name: 'workflow-transfer-edit',
          component: () => import('@/views/workflow/transfer/TransferApplication.vue'),
          meta: {
            title: '编辑调岗申请',
            requiresAuth: true,
            permissions: ['workflow:transfer:edit'],
            hidden: true
          }
        },
        {
          path: 'transfer/detail/:id',
          name: 'workflow-transfer-detail',
          component: () => import('@/views/workflow/transfer/TransferDetail.vue'),
          meta: {
            title: '调岗申请详情',
            requiresAuth: true,
            permissions: ['workflow:transfer:view'],
            hidden: true
          }
        },
        {
          path: 'transfer/original-dept-tasks',
          name: 'workflow-transfer-original-dept-tasks',
          component: () => import('@/views/workflow/transfer/OriginalDeptApprovalList.vue'),
          meta: {
            title: '原部门审批任务',
            requiresAuth: true,
            permissions: ['workflow:transfer:approve']
          }
        },
        {
          path: 'transfer/original-dept-approval/:taskId/:applicationId',
          name: 'workflow-transfer-original-dept-approval',
          component: () => import('@/views/workflow/transfer/OriginalDeptApproval.vue'),
          meta: {
            title: '原部门审批',
            requiresAuth: true,
            permissions: ['workflow:transfer:approve'],
            hidden: true
          }
        },
        {
          path: 'transfer/target-dept-tasks',
          name: 'workflow-transfer-target-dept-tasks',
          component: () => import('@/views/workflow/transfer/TargetDeptApprovalList.vue'),
          meta: {
            title: '目标部门审批任务',
            requiresAuth: true,
            permissions: ['workflow:transfer:approve']
          }
        },
        {
          path: 'transfer/target-dept-approval/:taskId/:applicationId',
          name: 'workflow-transfer-target-dept-approval',
          component: () => import('@/views/workflow/transfer/TargetDeptApproval.vue'),
          meta: {
            title: '目标部门审批',
            requiresAuth: true,
            permissions: ['workflow:transfer:approve'],
            hidden: true
          }
        },
        {
          path: 'transfer/hr-tasks',
          name: 'workflow-transfer-hr-tasks',
          component: () => import('@/views/workflow/transfer/HRApprovalList.vue'),
          meta: {
            title: '人事审批任务',
            requiresAuth: true,
            permissions: ['workflow:transfer:hr:approve']
          }
        },
        {
          path: 'transfer/hr-approval/:taskId/:applicationId',
          name: 'workflow-transfer-hr-approval',
          component: () => import('@/views/workflow/transfer/HRApproval.vue'),
          meta: {
            title: '人事审批',
            requiresAuth: true,
            permissions: ['workflow:transfer:hr:approve'],
            hidden: true
          }
        },
        {
          path: 'transfer/handover-tasks',
          name: 'workflow-transfer-handover-tasks',
          component: () => import('@/views/workflow/transfer/HandoverTaskList.vue'),
          meta: {
            title: '工作交接任务',
            requiresAuth: true,
            permissions: ['workflow:transfer:handover']
          }
        },
        {
          path: 'transfer/handover/:id',
          name: 'workflow-transfer-handover',
          component: () => import('@/views/workflow/transfer/WorkHandover.vue'),
          meta: {
            title: '工作交接',
            requiresAuth: true,
            permissions: ['workflow:transfer:handover'],
            hidden: true
          }
        },
        {
          path: 'transfer/info-update-list',
          name: 'workflow-transfer-info-update-list',
          component: () => import('@/views/workflow/transfer/InfoUpdateList.vue'),
          meta: {
            title: '信息更新监控',
            requiresAuth: true,
            permissions: ['workflow:transfer:update']
          }
        },
        {
          path: 'transfer/info-update/:id',
          name: 'workflow-transfer-info-update',
          component: () => import('@/views/workflow/transfer/InfoUpdate.vue'),
          meta: {
            title: '信息更新',
            requiresAuth: true,
            permissions: ['workflow:transfer:update'],
            hidden: true
          }
        },
        {
          path: 'resignation',
          name: 'workflow-resignation',
          component: () => import('@/views/workflow/processes/ResignationProcess.vue'),
          meta: {
            title: '离职流程',
            icon: 'resignation',
            requiresAuth: true,
            permissions: ['workflow:process:resignation']
          }
        },
        {
          path: 'resignation/apply',
          name: 'workflow-resignation-apply',
          component: () => import('@/views/workflow/resignation/ResignationApplication.vue'),
          meta: {
            title: '发起离职申请',
            requiresAuth: true,
            permissions: ['workflow:resignation:apply'],
            hidden: true
          }
        },
        {
          path: 'resignation/edit/:id',
          name: 'workflow-resignation-edit',
          component: () => import('@/views/workflow/resignation/ResignationApplication.vue'),
          meta: {
            title: '编辑离职申请',
            requiresAuth: true,
            permissions: ['workflow:resignation:edit'],
            hidden: true
          }
        },
        {
          path: 'resignation/my-applications',
          name: 'workflow-resignation-my-applications',
          component: () => import('@/views/workflow/resignation/MyResignationList.vue'),
          meta: {
            title: '我的离职申请',
            requiresAuth: true,
            permissions: ['workflow:resignation:view']
          }
        },
        {
          path: 'resignation/dept-approval-tasks',
          name: 'workflow-resignation-dept-approval-tasks',
          component: () => import('@/views/workflow/resignation/DeptApprovalList.vue'),
          meta: {
            title: '部门审批任务',
            requiresAuth: true,
            permissions: ['workflow:resignation:dept:approve']
          }
        },
        {
          path: 'resignation/dept-approval/:taskId/:applicationId',
          name: 'workflow-resignation-dept-approval',
          component: () => import('@/views/workflow/resignation/DeptApproval.vue'),
          meta: {
            title: '部门审批',
            requiresAuth: true,
            permissions: ['workflow:resignation:dept:approve'],
            hidden: true
          }
        },
        {
          path: 'resignation/handover-tasks',
          name: 'workflow-resignation-handover-tasks',
          component: () => import('@/views/workflow/resignation/HandoverTaskList.vue'),
          meta: {
            title: '工作交接任务',
            requiresAuth: true,
            permissions: ['workflow:resignation:handover']
          }
        },
        {
          path: 'resignation/handover/:id',
          name: 'workflow-resignation-handover',
          component: () => import('@/views/workflow/resignation/WorkHandover.vue'),
          meta: {
            title: '工作交接',
            requiresAuth: true,
            permissions: ['workflow:resignation:handover'],
            hidden: true
          }
        },
        {
          path: 'resignation/asset-handover-tasks',
          name: 'workflow-resignation-asset-handover-tasks',
          component: () => import('@/views/workflow/resignation/AssetHandoverList.vue'),
          meta: {
            title: '资产交接任务',
            requiresAuth: true,
            permissions: ['workflow:resignation:asset:manage']
          }
        },
        {
          path: 'resignation/asset-handover/:id',
          name: 'workflow-resignation-asset-handover',
          component: () => import('@/views/workflow/resignation/AssetHandover.vue'),
          meta: {
            title: '资产交接',
            requiresAuth: true,
            permissions: ['workflow:resignation:asset:view'],
            hidden: true
          }
        },
        {
          path: 'resignation/hr-tasks',
          name: 'workflow-resignation-hr-tasks',
          component: () => import('@/views/workflow/resignation/HRApprovalList.vue'),
          meta: {
            title: '人事审批任务',
            requiresAuth: true,
            permissions: ['workflow:resignation:hr:approve']
          }
        },
        {
          path: 'resignation/hr-approval/:taskId/:applicationId',
          name: 'workflow-resignation-hr-approval',
          component: () => import('@/views/workflow/resignation/HRApproval.vue'),
          meta: {
            title: '人事审批',
            requiresAuth: true,
            permissions: ['workflow:resignation:hr:approve'],
            hidden: true
          }
        },
        {
          path: 'resignation/account-cancellation-tasks',
          name: 'workflow-resignation-account-cancellation-tasks',
          component: () => import('@/views/workflow/resignation/AccountCancellationList.vue'),
          meta: {
            title: '账号注销任务',
            requiresAuth: true,
            permissions: ['workflow:resignation:account:manage']
          }
        },
        {
          path: 'resignation/account-cancellation/:id',
          name: 'workflow-resignation-account-cancellation',
          component: () => import('@/views/workflow/resignation/AccountCancellation.vue'),
          meta: {
            title: '账号注销',
            requiresAuth: true,
            permissions: ['workflow:resignation:account:view'],
            hidden: true
          }
        },
        {
          path: 'resignation/certificate-tasks',
          name: 'workflow-resignation-certificate-tasks',
          component: () => import('@/views/workflow/resignation/CertificateTaskList.vue'),
          meta: {
            title: '离职证明管理',
            requiresAuth: true,
            permissions: ['workflow:resignation:certificate:manage']
          }
        },
        {
          path: 'resignation/certificate/:id',
          name: 'workflow-resignation-certificate',
          component: () => import('@/views/workflow/resignation/ResignationCertificate.vue'),
          meta: {
            title: '生成离职证明',
            requiresAuth: true,
            permissions: ['workflow:resignation:certificate:generate'],
            hidden: true
          }
        },
        {
          path: 'resignation/archive/:id',
          name: 'workflow-resignation-archive',
          component: () => import('@/views/workflow/resignation/EmployeeArchive.vue'),
          meta: {
            title: '员工档案归档',
            requiresAuth: true,
            permissions: ['workflow:resignation:archive:manage'],
            hidden: true
          }
        },
        {
          path: 'employee-number',
          name: 'workflow-employee-number',
          component: () => import('@/views/workflow/processes/EmployeeNumberManagement.vue'),
          meta: {
            title: '工号管理',
            icon: 'number',
            requiresAuth: true,
            permissions: ['workflow:employee-number:manage']
          }
        },
        {
          path: 'account',
          name: 'workflow-account',
          component: () => import('@/views/workflow/processes/AccountManagement.vue'),
          meta: {
            title: '账号管理',
            icon: 'account',
            requiresAuth: true,
            permissions: ['workflow:account:manage']
          }
        },
        {
          path: 'account/create',
          name: 'workflow-account-create',
          component: () => import('@/views/workflow/processes/AccountManagement.vue'),
          meta: {
            title: '开通账号',
            requiresAuth: true,
            permissions: ['workflow:account:create'],
            hidden: true,
            activeMenu: '/workflow/processes/account'
          }
        },
        {
          path: 'account/list',
          name: 'workflow-account-list',
          component: () => import('@/views/workflow/processes/AccountManagement.vue'),
          meta: {
            title: '账号列表',
            requiresAuth: true,
            permissions: ['workflow:account:view'],
            hidden: true,
            activeMenu: '/workflow/processes/account'
          }
        }
      ]
    },

    // 流程详情（隐藏路由）
    {
      path: 'task/:id',
      name: 'workflow-task-detail',
      component: () => import('@/views/workflow/TaskDetail.vue'),
      meta: {
        title: '任务详情',
        requiresAuth: true,
        permissions: ['workflow:task:view'],
        hidden: true,
        activeMenu: '/workflow/approval-center' // 激活的菜单项
      }
    },
    {
      path: 'instance/:instanceId',
      name: 'workflow-instance-detail',
      component: () => import('@/views/workflow/InstanceDetail.vue'),
      meta: {
        title: '流程实例详情',
        requiresAuth: true,
        permissions: ['workflow:instance:view'],
        hidden: true,
        activeMenu: '/workflow/instances'
      }
    },

    // 流程统计分析
    {
      path: 'statistics',
      name: 'workflow-statistics',
      component: () => import('@/views/workflow/ProcessStatistics.vue'),
      meta: {
        title: '流程统计分析',
        icon: 'statistics',
        requiresAuth: true,
        permissions: ['workflow:statistics:view']
      }
    },

    // 流程配置
    {
      path: 'configuration',
      name: 'workflow-configuration',
      component: () => import('@/views/workflow/ProcessConfiguration.vue'),
      meta: {
        title: '流程配置',
        icon: 'config',
        requiresAuth: true,
        permissions: ['workflow:config:manage']
      }
    },

    // 测试验证页面
    {
      path: 'test',
      name: 'workflow-test',
      component: () => import('@/views/workflow/test/ProcessTestDashboard.vue'),
      meta: {
        title: '流程测试验证',
        icon: 'test',
        requiresAuth: true,
        permissions: ['workflow:test:view']
      }
    },

    // 演示页面（开发环境可见）
    ...(process.env.NODE_ENV === 'development'
      ? [
          {
            path: 'demos',
            name: 'workflow-demos',
            redirect: '/workflow/demos/approver-config',
            meta: {
              title: '组件演示',
              icon: 'demo',
              requiresAuth: true,
              permissions: ['workflow:demo:view']
            },
            children: [
              {
                path: 'approver-config',
                name: 'workflow-demo-approver-config',
                component: () => import('@/views/workflow/demos/ApproverConfigDemo.vue'),
                meta: {
                  title: '审批人配置演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'condition-config',
                name: 'workflow-demo-condition-config',
                component: () => import('@/views/workflow/demos/ConditionConfigDemo.vue'),
                meta: {
                  title: '条件配置演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'form-designer',
                name: 'workflow-demo-form-designer',
                component: () => import('@/views/workflow/demos/FormDesignerDemo.vue'),
                meta: {
                  title: '表单设计器演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'deadline-config',
                name: 'workflow-demo-deadline-config',
                component: () => import('@/views/workflow/demos/DeadlineConfigDemo.vue'),
                meta: {
                  title: '时限配置演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'notification-config',
                name: 'workflow-demo-notification-config',
                component: () => import('@/workflow/views/demos/NotificationConfigDemo.vue'),
                meta: {
                  title: '通知配置演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'task-list',
                name: 'workflow-demo-task-list',
                component: () => import('@/workflow/views/demos/TaskListDemo.vue'),
                meta: {
                  title: '任务列表演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'task-detail',
                name: 'workflow-demo-task-detail',
                component: () => import('@/views/workflow/demo/TaskDetailDemo.vue'),
                meta: {
                  title: '任务详情演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'approval-form',
                name: 'workflow-demo-approval-form',
                component: () => import('@/views/workflow/demo/ApprovalFormDemo.vue'),
                meta: {
                  title: '审批表单演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'process-history',
                name: 'workflow-demo-process-history',
                component: () => import('@/views/workflow/demo/ProcessHistoryDemo.vue'),
                meta: {
                  title: '流程历史演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'process-tracker',
                name: 'workflow-demo-process-tracker',
                component: () => import('@/views/workflow/demo/ProcessTrackerDemo.vue'),
                meta: {
                  title: '流程图追踪演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'task-claim',
                name: 'workflow-demo-task-claim',
                component: () => import('@/views/workflow/demo/TaskClaimDemo.vue'),
                meta: {
                  title: '任务认领演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'task-delegate',
                name: 'workflow-demo-task-delegate',
                component: () => import('@/views/workflow/demo/TaskDelegateDemo.vue'),
                meta: {
                  title: '任务委托演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'task-rollback',
                name: 'workflow-demo-task-rollback',
                component: () => import('@/views/workflow/demo/TaskRollbackDemo.vue'),
                meta: {
                  title: '任务回退演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'add-remove-sign',
                name: 'workflow-demo-add-remove-sign',
                component: () => import('@/views/workflow/demo/AddRemoveSignDemo.vue'),
                meta: {
                  title: '加减签演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'task-urge',
                name: 'workflow-demo-task-urge',
                component: () => import('@/views/workflow/demo/TaskUrgeDemo.vue'),
                meta: {
                  title: '任务催办演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'batch-approval',
                name: 'workflow-demo-batch-approval',
                component: () => import('@/views/workflow/demo/BatchApprovalDemo.vue'),
                meta: {
                  title: '批量审批演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              },
              {
                path: 'comment-template',
                name: 'workflow-demo-comment-template',
                component: () => import('@/views/workflow/demo/CommentTemplateDemo.vue'),
                meta: {
                  title: '意见模板演示',
                  requiresAuth: true,
                  permissions: ['workflow:demo:view']
                }
              }
            ]
          }
        ]
      : [])
  ]
}

export default workflowRoutes
