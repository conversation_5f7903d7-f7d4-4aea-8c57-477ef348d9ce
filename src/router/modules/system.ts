import type { RouteRecordRaw } from 'vue-router'

/**
 * 系统管理模块路由配置
 * 使用路由懒加载优化性能
 */
const systemRoutes: RouteRecordRaw[] = [
  {
    path: '/system',
    name: 'System',
    redirect: '/system/user-management',
    meta: {
      title: '系统管理',
      icon: 'Setting',
      requiresAuth: true,
      permissions: ['system:view']
    },
    // 使用函数式导入实现懒加载
    component: () => import(/* webpackChunkName: "system-layout" */ '@/layouts/default/index.vue'),
    children: [
      {
        path: 'user-management',
        name: 'UserManagement',
        component: () =>
          import(
            /* webpackChunkName: "system-user" */
            /* webpackPrefetch: true */
            '@/views/system/UserManagement.vue'
          ),
        meta: {
          title: '用户管理',
          permissions: ['system:user:view']
        }
      },
      {
        path: 'role-management',
        name: 'RoleManagement',
        component: () =>
          import(
            /* webpackChunkName: "system-role" */
            /* webpackPrefetch: true */
            '@/views/system/RoleManagement.vue'
          ),
        meta: {
          title: '角色管理',
          permissions: ['system:role:view']
        }
      },
      {
        path: 'permission-management',
        name: 'PermissionManagement',
        component: () =>
          import(
            /* webpackChunkName: "system-permission" */
            '@/views/system/PermissionManagement.vue'
          ),
        meta: {
          title: '权限管理',
          permissions: ['system:permission:view']
        }
      },
      {
        path: 'menu-management',
        name: 'MenuManagement',
        component: () =>
          import(
            /* webpackChunkName: "system-menu" */
            '@/views/system/MenuManagement.vue'
          ),
        meta: {
          title: '菜单管理',
          permissions: ['system:menu:view']
        }
      },
      {
        path: 'dict-management',
        name: 'DictManagement',
        component: () =>
          import(
            /* webpackChunkName: "system-dict" */
            '@/views/system/DictManagement.vue'
          ),
        meta: {
          title: '字典管理',
          permissions: ['system:dict:view']
        }
      },
      {
        path: 'config-management',
        name: 'ConfigManagement',
        component: () =>
          import(
            /* webpackChunkName: "system-config" */
            '@/views/system/ConfigManagement.vue'
          ),
        meta: {
          title: '参数配置',
          permissions: ['system:config:view']
        }
      },
      {
        path: 'log-management',
        name: 'LogManagement',
        redirect: '/system/log-management/operation',
        meta: {
          title: '日志管理',
          permissions: ['system:log:view']
        },
        children: [
          {
            path: 'operation',
            name: 'OperationLog',
            component: () =>
              import(
                /* webpackChunkName: "system-log-operation" */
                '@/views/system/log/OperationLog.vue'
              ),
            meta: {
              title: '操作日志',
              permissions: ['system:log:operation']
            }
          },
          {
            path: 'login',
            name: 'LoginLog',
            component: () =>
              import(
                /* webpackChunkName: "system-log-login" */
                '@/views/system/log/LoginLog.vue'
              ),
            meta: {
              title: '登录日志',
              permissions: ['system:log:login']
            }
          },
          {
            path: 'error',
            name: 'ErrorLog',
            component: () =>
              import(
                /* webpackChunkName: "system-log-error" */
                '@/views/system/log/ErrorLog.vue'
              ),
            meta: {
              title: '错误日志',
              permissions: ['system:log:error']
            }
          }
        ]
      },
      {
        path: 'system-monitoring',
        name: 'SystemMonitoring',
        component: () =>
          import(
            /* webpackChunkName: "system-monitor" */
            '@/views/system/SystemMonitoring.vue'
          ),
        meta: {
          title: '系统监控',
          permissions: ['system:monitor:view']
        }
      },
      {
        path: 'online-users',
        name: 'OnlineUsers',
        component: () =>
          import(
            /* webpackChunkName: "system-online" */
            '@/views/system/OnlineUsers.vue'
          ),
        meta: {
          title: '在线用户',
          permissions: ['system:online:view']
        }
      },
      {
        path: 'scheduled-tasks',
        name: 'ScheduledTasks',
        component: () =>
          import(
            /* webpackChunkName: "system-task" */
            '@/views/system/ScheduledTasks.vue'
          ),
        meta: {
          title: '定时任务',
          permissions: ['system:task:view']
        }
      },
      {
        path: 'data-backup',
        name: 'DataBackup',
        component: () =>
          import(
            /* webpackChunkName: "system-backup" */
            '@/views/system/DataBackup.vue'
          ),
        meta: {
          title: '数据备份',
          permissions: ['system:backup:view']
        }
      },
      {
        path: 'system-settings',
        name: 'SystemSettings',
        component: () =>
          import(
            /* webpackChunkName: "system-settings" */
            '@/views/system/SystemSettings.vue'
          ),
        meta: {
          title: '系统设置',
          permissions: ['system:settings:view']
        }
      },
      {
        path: 'cache-management',
        name: 'CacheManagement',
        component: () =>
          import(
            /* webpackChunkName: "system-cache" */
            '@/views/system/CacheManagement.vue'
          ),
        meta: {
          title: '缓存管理',
          permissions: ['system:cache:view']
        }
      },
      {
        path: 'api-docs',
        name: 'ApiDocs',
        component: () =>
          import(
            /* webpackChunkName: "system-api" */
            '@/views/system/ApiDocs.vue'
          ),
        meta: {
          title: 'API文档',
          permissions: ['system:api:view']
        }
      }
    ]
  }
]

export default systemRoutes
