import type { RouteRecordRaw } from 'vue-router'

/**
 * 员工管理模块路由配置
 * 使用路由懒加载优化性能
 */
const employeeRoutes: RouteRecordRaw[] = [
  {
    path: '/employee',
    name: 'Employee',
    redirect: '/employee/list',
    meta: {
      title: '员工管理',
      icon: 'User',
      requiresAuth: true,
      permissions: ['employee:view']
    },
    component: () =>
      import(/* webpackChunkName: "employee-layout" */ '@/layouts/default/index.vue'),
    children: [
      {
        path: 'list',
        name: 'EmployeeList',
        component: () =>
          import(
            /* webpackChunkName: "employee-list" */
            /* webpackPrefetch: true */
            '@/views/employee/list/index.vue'
          ),
        meta: {
          title: '员工列表',
          permissions: ['employee:list']
        }
      },
      {
        path: 'add',
        name: 'EmployeeAdd',
        component: () =>
          import(
            /* webpackChunkName: "employee-edit" */
            '@/views/employee/add/index.vue'
          ),
        meta: {
          title: '新增员工',
          permissions: ['employee:add']
        }
      },
      {
        path: 'detail/:id',
        name: 'EmployeeDetail',
        component: () =>
          import(
            /* webpackChunkName: "employee-detail" */
            '@/views/employee/detail/index.vue'
          ),
        meta: {
          title: '员工详情',
          hidden: true,
          permissions: ['employee:view']
        }
      },
      {
        path: 'edit/:id',
        name: 'EmployeeEdit',
        component: () =>
          import(
            /* webpackChunkName: "employee-edit" */
            '@/views/employee/edit/index.vue'
          ),
        meta: {
          title: '编辑员工',
          hidden: true,
          permissions: ['employee:edit']
        }
      },
      {
        path: 'import',
        name: 'EmployeeImport',
        component: () =>
          import(
            /* webpackChunkName: "employee-import" */
            '@/views/employee/import/index.vue'
          ),
        meta: {
          title: '批量导入',
          permissions: ['employee:import']
        }
      },
      {
        path: 'export',
        name: 'EmployeeExport',
        component: () =>
          import(
            /* webpackChunkName: "employee-export" */
            '@/views/employee/export/index.vue'
          ),
        meta: {
          title: '批量导出',
          permissions: ['employee:export']
        }
      },
      {
        path: 'tag',
        name: 'EmployeeTag',
        component: () =>
          import(
            /* webpackChunkName: "employee-tag" */
            '@/views/employee/tag/index.vue'
          ),
        meta: {
          title: '标签管理',
          permissions: ['employee:tag']
        }
      },

      // 员工信息子集
      {
        path: 'subsets',
        name: 'EmployeeSubsets',
        redirect: '/employee/subsets/education',
        meta: {
          title: '信息子集',
          permissions: ['employee:subsets']
        },
        children: [
          {
            path: 'education',
            name: 'EducationHistory',
            component: () =>
              import(
                /* webpackChunkName: "employee-education" */
                '@/views/employee/subsets/EducationHistory.vue'
              ),
            meta: { title: '教育经历' }
          },
          {
            path: 'work',
            name: 'WorkExperience',
            component: () =>
              import(
                /* webpackChunkName: "employee-work" */
                '@/views/employee/subsets/WorkExperience.vue'
              ),
            meta: { title: '工作经历' }
          },
          {
            path: 'family',
            name: 'FamilyMembers',
            component: () =>
              import(
                /* webpackChunkName: "employee-family" */
                '@/views/employee/subsets/FamilyMembers.vue'
              ),
            meta: { title: '家庭成员' }
          },
          {
            path: 'rewards',
            name: 'RewardsPunishments',
            component: () =>
              import(
                /* webpackChunkName: "employee-rewards" */
                '@/views/employee/subsets/RewardsPunishments.vue'
              ),
            meta: { title: '奖惩记录' }
          },
          {
            path: 'qualifications',
            name: 'Qualifications',
            component: () =>
              import(
                /* webpackChunkName: "employee-qualifications" */
                '@/views/employee/subsets/Qualifications.vue'
              ),
            meta: { title: '职业资格' }
          },
          {
            path: 'training',
            name: 'TrainingRecords',
            component: () =>
              import(
                /* webpackChunkName: "employee-training" */
                '@/views/employee/subsets/TrainingRecords.vue'
              ),
            meta: { title: '培训记录' }
          },
          {
            path: 'talent',
            name: 'TalentHonors',
            component: () =>
              import(
                /* webpackChunkName: "employee-talent" */
                '@/views/employee/subsets/TalentHonors.vue'
              ),
            meta: { title: '人才荣誉' }
          }
        ]
      },

      // 员工变更管理
      {
        path: 'changes',
        name: 'EmployeeChanges',
        redirect: '/employee/changes/log',
        meta: {
          title: '变更管理',
          permissions: ['employee:changes']
        },
        children: [
          {
            path: 'log',
            name: 'ChangeLog',
            component: () =>
              import(
                /* webpackChunkName: "employee-changes-log" */
                '@/views/employee/changes/ChangeLog.vue'
              ),
            meta: { title: '变更日志' }
          },
          {
            path: 'batch',
            name: 'BatchChange',
            component: () =>
              import(
                /* webpackChunkName: "employee-changes-batch" */
                '@/views/employee/changes/BatchChange.vue'
              ),
            meta: { title: '批量变更' }
          },
          {
            path: 'approval',
            name: 'ChangeApproval',
            component: () =>
              import(
                /* webpackChunkName: "employee-changes-approval" */
                '@/views/employee/changes/ChangeApproval.vue'
              ),
            meta: { title: '变更审批' }
          }
        ]
      },

      // 员工档案
      {
        path: 'archives',
        name: 'EmployeeArchives',
        component: () =>
          import(
            /* webpackChunkName: "employee-archives" */
            '@/views/employee/archives/index.vue'
          ),
        meta: {
          title: '员工档案',
          permissions: ['employee:archives']
        }
      },

      // 员工统计
      {
        path: 'statistics',
        name: 'EmployeeStatistics',
        component: () =>
          import(
            /* webpackChunkName: "employee-statistics" */
            '@/views/employee/statistics/index.vue'
          ),
        meta: {
          title: '员工统计',
          permissions: ['employee:statistics']
        }
      },

      // 员工画像
      {
        path: 'portrait/:id',
        name: 'EmployeePortrait',
        component: () =>
          import(
            /* webpackChunkName: "employee-portrait" */
            '@/views/employee/portrait/index.vue'
          ),
        meta: {
          title: '员工画像',
          hidden: true,
          permissions: ['employee:portrait']
        }
      }
    ]
  }
]

export default employeeRoutes
