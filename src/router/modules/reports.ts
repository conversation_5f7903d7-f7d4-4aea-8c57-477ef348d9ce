import type { RouteRecordRaw } from 'vue-router'

/**
 * 报表管理模块路由配置
 * 使用路由懒加载优化性能
 */
const reportRoutes: RouteRecordRaw[] = [
  {
    path: '/reports',
    name: 'Reports',
    redirect: '/reports/dashboard',
    meta: {
      title: '报表中心',
      icon: 'DataAnalysis',
      requiresAuth: true,
      permissions: ['reports:view']
    },
    // 使用函数式导入实现懒加载
    component: () => import(/* webpackChunkName: "reports-layout" */ '@/layouts/default/index.vue'),
    children: [
      // 综合报表
      {
        path: 'dashboard',
        name: 'ReportsDashboard',
        component: () =>
          import(
            /* webpackChunkName: "reports-dashboard" */
            /* webpackPrefetch: true */
            '@/views/reports/Dashboard.vue'
          ),
        meta: {
          title: '报表总览',
          permissions: ['reports:dashboard:view']
        }
      },
      // 人事报表
      {
        path: 'personnel',
        name: 'PersonnelReports',
        redirect: '/reports/personnel/overview',
        meta: {
          title: '人事报表',
          permissions: ['reports:personnel:view']
        },
        children: [
          {
            path: 'overview',
            name: 'PersonnelOverview',
            component: () =>
              import(
                /* webpackChunkName: "reports-personnel-overview" */
                '@/views/reports/personnel/Overview.vue'
              ),
            meta: { title: '人员概况' }
          },
          {
            path: 'structure',
            name: 'PersonnelStructure',
            component: () =>
              import(
                /* webpackChunkName: "reports-personnel-structure" */
                '@/views/reports/personnel/Structure.vue'
              ),
            meta: { title: '人员结构分析' }
          },
          {
            path: 'changes',
            name: 'PersonnelChanges',
            component: () =>
              import(
                /* webpackChunkName: "reports-personnel-changes" */
                '@/views/reports/personnel/Changes.vue'
              ),
            meta: { title: '人事变动统计' }
          },
          {
            path: 'distribution',
            name: 'PersonnelDistribution',
            component: () =>
              import(
                /* webpackChunkName: "reports-personnel-distribution" */
                '@/views/reports/personnel/Distribution.vue'
              ),
            meta: { title: '人员分布图表' }
          }
        ]
      },
      // 考勤报表
      {
        path: 'attendance',
        name: 'AttendanceReports',
        redirect: '/reports/attendance/summary',
        meta: {
          title: '考勤报表',
          permissions: ['reports:attendance:view']
        },
        children: [
          {
            path: 'summary',
            name: 'AttendanceSummary',
            component: () =>
              import(
                /* webpackChunkName: "reports-attendance-summary" */
                '@/views/reports/attendance/Summary.vue'
              ),
            meta: { title: '考勤汇总' }
          },
          {
            path: 'monthly',
            name: 'AttendanceMonthly',
            component: () =>
              import(
                /* webpackChunkName: "reports-attendance-monthly" */
                '@/views/reports/attendance/Monthly.vue'
              ),
            meta: { title: '月度考勤报表' }
          },
          {
            path: 'annual',
            name: 'AttendanceAnnual',
            component: () =>
              import(
                /* webpackChunkName: "reports-attendance-annual" */
                '@/views/reports/attendance/Annual.vue'
              ),
            meta: { title: '年度考勤报表' }
          },
          {
            path: 'abnormal',
            name: 'AttendanceAbnormal',
            component: () =>
              import(
                /* webpackChunkName: "reports-attendance-abnormal" */
                '@/views/reports/attendance/Abnormal.vue'
              ),
            meta: { title: '异常考勤分析' }
          },
          {
            path: 'overtime',
            name: 'AttendanceOvertime',
            component: () =>
              import(
                /* webpackChunkName: "reports-attendance-overtime" */
                '@/views/reports/attendance/Overtime.vue'
              ),
            meta: { title: '加班统计报表' }
          },
          {
            path: 'leave',
            name: 'AttendanceLeave',
            component: () =>
              import(
                /* webpackChunkName: "reports-attendance-leave" */
                '@/views/reports/attendance/Leave.vue'
              ),
            meta: { title: '请假统计报表' }
          }
        ]
      },
      // 薪资报表
      {
        path: 'salary',
        name: 'SalaryReports',
        redirect: '/reports/salary/summary',
        meta: {
          title: '薪资报表',
          permissions: ['reports:salary:view']
        },
        children: [
          {
            path: 'summary',
            name: 'SalarySummary',
            component: () =>
              import(
                /* webpackChunkName: "reports-salary-summary" */
                '@/views/reports/salary/Summary.vue'
              ),
            meta: { title: '薪资汇总' }
          },
          {
            path: 'payroll',
            name: 'SalaryPayroll',
            component: () =>
              import(
                /* webpackChunkName: "reports-salary-payroll" */
                '@/views/reports/salary/Payroll.vue'
              ),
            meta: { title: '工资发放表' }
          },
          {
            path: 'analysis',
            name: 'SalaryAnalysis',
            component: () =>
              import(
                /* webpackChunkName: "reports-salary-analysis" */
                '@/views/reports/salary/Analysis.vue'
              ),
            meta: { title: '薪资分析报表' }
          },
          {
            path: 'tax',
            name: 'SalaryTax',
            component: () =>
              import(
                /* webpackChunkName: "reports-salary-tax" */
                '@/views/reports/salary/Tax.vue'
              ),
            meta: { title: '个税申报表' }
          },
          {
            path: 'cost',
            name: 'SalaryCost',
            component: () =>
              import(
                /* webpackChunkName: "reports-salary-cost" */
                '@/views/reports/salary/Cost.vue'
              ),
            meta: { title: '人力成本分析' }
          },
          {
            path: 'trend',
            name: 'SalaryTrend',
            component: () =>
              import(
                /* webpackChunkName: "reports-salary-trend" */
                '@/views/reports/salary/Trend.vue'
              ),
            meta: { title: '薪资趋势分析' }
          }
        ]
      },
      // 招聘报表
      {
        path: 'recruitment',
        name: 'RecruitmentReports',
        redirect: '/reports/recruitment/summary',
        meta: {
          title: '招聘报表',
          permissions: ['reports:recruitment:view']
        },
        children: [
          {
            path: 'summary',
            name: 'RecruitmentSummary',
            component: () =>
              import(
                /* webpackChunkName: "reports-recruitment-summary" */
                '@/views/reports/recruitment/Summary.vue'
              ),
            meta: { title: '招聘汇总' }
          },
          {
            path: 'efficiency',
            name: 'RecruitmentEfficiency',
            component: () =>
              import(
                /* webpackChunkName: "reports-recruitment-efficiency" */
                '@/views/reports/recruitment/Efficiency.vue'
              ),
            meta: { title: '招聘效率分析' }
          },
          {
            path: 'channels',
            name: 'RecruitmentChannels',
            component: () =>
              import(
                /* webpackChunkName: "reports-recruitment-channels" */
                '@/views/reports/recruitment/Channels.vue'
              ),
            meta: { title: '渠道效果分析' }
          },
          {
            path: 'cost',
            name: 'RecruitmentCost',
            component: () =>
              import(
                /* webpackChunkName: "reports-recruitment-cost" */
                '@/views/reports/recruitment/Cost.vue'
              ),
            meta: { title: '招聘成本分析' }
          }
        ]
      },
      // 培训报表
      {
        path: 'training',
        name: 'TrainingReports',
        redirect: '/reports/training/summary',
        meta: {
          title: '培训报表',
          permissions: ['reports:training:view']
        },
        children: [
          {
            path: 'summary',
            name: 'TrainingSummary',
            component: () =>
              import(
                /* webpackChunkName: "reports-training-summary" */
                '@/views/reports/training/Summary.vue'
              ),
            meta: { title: '培训汇总' }
          },
          {
            path: 'completion',
            name: 'TrainingCompletion',
            component: () =>
              import(
                /* webpackChunkName: "reports-training-completion" */
                '@/views/reports/training/Completion.vue'
              ),
            meta: { title: '培训完成率' }
          },
          {
            path: 'effectiveness',
            name: 'TrainingEffectiveness',
            component: () =>
              import(
                /* webpackChunkName: "reports-training-effectiveness" */
                '@/views/reports/training/Effectiveness.vue'
              ),
            meta: { title: '培训效果评估' }
          },
          {
            path: 'cost',
            name: 'TrainingCost',
            component: () =>
              import(
                /* webpackChunkName: "reports-training-cost" */
                '@/views/reports/training/Cost.vue'
              ),
            meta: { title: '培训成本分析' }
          }
        ]
      },
      // 绩效报表
      {
        path: 'performance',
        name: 'PerformanceReports',
        redirect: '/reports/performance/summary',
        meta: {
          title: '绩效报表',
          permissions: ['reports:performance:view']
        },
        children: [
          {
            path: 'summary',
            name: 'PerformanceSummary',
            component: () =>
              import(
                /* webpackChunkName: "reports-performance-summary" */
                '@/views/reports/performance/Summary.vue'
              ),
            meta: { title: '绩效汇总' }
          },
          {
            path: 'distribution',
            name: 'PerformanceDistribution',
            component: () =>
              import(
                /* webpackChunkName: "reports-performance-distribution" */
                '@/views/reports/performance/Distribution.vue'
              ),
            meta: { title: '绩效分布分析' }
          },
          {
            path: 'trend',
            name: 'PerformanceTrend',
            component: () =>
              import(
                /* webpackChunkName: "reports-performance-trend" */
                '@/views/reports/performance/Trend.vue'
              ),
            meta: { title: '绩效趋势分析' }
          },
          {
            path: 'department',
            name: 'PerformanceDepartment',
            component: () =>
              import(
                /* webpackChunkName: "reports-performance-department" */
                '@/views/reports/performance/Department.vue'
              ),
            meta: { title: '部门绩效对比' }
          }
        ]
      },
      // 自定义报表
      {
        path: 'custom',
        name: 'CustomReports',
        redirect: '/reports/custom/builder',
        meta: {
          title: '自定义报表',
          permissions: ['reports:custom:view']
        },
        children: [
          {
            path: 'builder',
            name: 'ReportBuilder',
            component: () =>
              import(
                /* webpackChunkName: "reports-custom-builder" */
                '@/views/reports/custom/Builder.vue'
              ),
            meta: { title: '报表设计器' }
          },
          {
            path: 'templates',
            name: 'ReportTemplates',
            component: () =>
              import(
                /* webpackChunkName: "reports-custom-templates" */
                '@/views/reports/custom/Templates.vue'
              ),
            meta: { title: '报表模板' }
          },
          {
            path: 'scheduled',
            name: 'ScheduledReports',
            component: () =>
              import(
                /* webpackChunkName: "reports-custom-scheduled" */
                '@/views/reports/custom/Scheduled.vue'
              ),
            meta: { title: '定时报表' }
          }
        ]
      },
      // 报表导出
      {
        path: 'export',
        name: 'ReportExport',
        component: () =>
          import(
            /* webpackChunkName: "reports-export" */
            '@/views/reports/Export.vue'
          ),
        meta: {
          title: '报表导出',
          permissions: ['reports:export']
        }
      },
      // 报表订阅
      {
        path: 'subscription',
        name: 'ReportSubscription',
        component: () =>
          import(
            /* webpackChunkName: "reports-subscription" */
            '@/views/reports/Subscription.vue'
          ),
        meta: {
          title: '报表订阅',
          permissions: ['reports:subscription']
        }
      }
    ]
  }
]

export default reportRoutes
