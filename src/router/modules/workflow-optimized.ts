import type { RouteRecordRaw } from 'vue-router'

/**
 * 工作流模块路由配置 - 优化版
 * 使用路由懒加载和代码分割优化性能
 */
const workflowRoutes: RouteRecordRaw[] = [
  {
    path: '/workflow',
    name: 'Workflow',
    redirect: '/workflow/approval-center',
    meta: {
      title: '工作流管理',
      icon: 'Connection',
      requiresAuth: true,
      permissions: ['workflow:view']
    },
    // 使用函数式导入实现懒加载
    component: () =>
      import(/* webpackChunkName: "workflow-layout" */ '@/layouts/default/index.vue'),
    children: [
      // 审批中心
      {
        path: 'approval-center',
        name: 'WorkflowApprovalCenter',
        component: () =>
          import(
            /* webpackChunkName: "workflow-approval" */
            /* webpackPrefetch: true */
            '@/views/workflow/ApprovalCenter.vue'
          ),
        meta: {
          title: '审批中心',
          permissions: ['workflow:approval:view']
        }
      },
      {
        path: 'my-tasks',
        name: 'WorkflowMyTasks',
        component: () =>
          import(
            /* webpackChunkName: "workflow-tasks" */
            /* webpackPrefetch: true */
            '@/views/workflow/MyTasks.vue'
          ),
        meta: {
          title: '我的待办',
          permissions: ['workflow:task:view']
        }
      },
      {
        path: 'my-applications',
        name: 'WorkflowMyApplications',
        component: () =>
          import(
            /* webpackChunkName: "workflow-applications" */
            '@/views/workflow/MyApplications.vue'
          ),
        meta: {
          title: '我的申请',
          permissions: ['workflow:application:view']
        }
      },
      {
        path: 'my-approvals',
        name: 'WorkflowMyApprovals',
        component: () =>
          import(
            /* webpackChunkName: "workflow-approvals" */
            '@/views/workflow/MyApprovals.vue'
          ),
        meta: {
          title: '我的已办',
          permissions: ['workflow:approval:history']
        }
      },

      // 流程设计
      {
        path: 'designer',
        name: 'WorkflowDesigner',
        redirect: '/workflow/designer/list',
        meta: {
          title: '流程设计',
          permissions: ['workflow:designer:view']
        },
        children: [
          {
            path: 'list',
            name: 'WorkflowDesignerList',
            component: () =>
              import(
                /* webpackChunkName: "workflow-designer-list" */
                '@/views/workflow/designer/ProcessList.vue'
              ),
            meta: { title: '流程列表' }
          },
          {
            path: 'create',
            name: 'WorkflowDesignerCreate',
            component: () =>
              import(
                /* webpackChunkName: "workflow-designer-editor" */
                '@/views/workflow/designer/ProcessDesigner.vue'
              ),
            meta: { title: '创建流程' }
          },
          {
            path: 'edit/:id',
            name: 'WorkflowDesignerEdit',
            component: () =>
              import(
                /* webpackChunkName: "workflow-designer-editor" */
                '@/views/workflow/designer/ProcessDesigner.vue'
              ),
            meta: { title: '编辑流程', hidden: true }
          },
          {
            path: 'templates',
            name: 'WorkflowTemplates',
            component: () =>
              import(
                /* webpackChunkName: "workflow-designer-templates" */
                '@/views/workflow/designer/ProcessTemplates.vue'
              ),
            meta: { title: '流程模板' }
          }
        ]
      },

      // 流程管理
      {
        path: 'management',
        name: 'WorkflowManagement',
        component: () =>
          import(
            /* webpackChunkName: "workflow-management" */
            '@/views/workflow/WorkflowManagement.vue'
          ),
        meta: {
          title: '流程定义管理',
          permissions: ['workflow:definition:manage']
        }
      },

      // 流程监控
      {
        path: 'monitoring',
        name: 'WorkflowMonitoring',
        redirect: '/workflow/monitoring/overview',
        meta: {
          title: '流程监控',
          permissions: ['workflow:monitor:view']
        },
        children: [
          {
            path: 'overview',
            name: 'MonitoringOverview',
            component: () =>
              import(
                /* webpackChunkName: "workflow-monitor-overview" */
                '@/views/workflow/monitoring/Overview.vue'
              ),
            meta: { title: '监控总览' }
          },
          {
            path: 'instances',
            name: 'ProcessInstances',
            component: () =>
              import(
                /* webpackChunkName: "workflow-monitor-instances" */
                '@/views/workflow/monitoring/ProcessInstances.vue'
              ),
            meta: { title: '流程实例' }
          },
          {
            path: 'tasks',
            name: 'TaskManagement',
            component: () =>
              import(
                /* webpackChunkName: "workflow-monitor-tasks" */
                '@/views/workflow/monitoring/TaskManagement.vue'
              ),
            meta: { title: '任务管理' }
          },
          {
            path: 'performance',
            name: 'ProcessPerformance',
            component: () =>
              import(
                /* webpackChunkName: "workflow-monitor-performance" */
                '@/views/workflow/monitoring/Performance.vue'
              ),
            meta: { title: '性能分析' }
          }
        ]
      },

      // 核心流程
      {
        path: 'processes',
        name: 'CoreProcesses',
        redirect: '/workflow/processes/onboarding',
        meta: {
          title: '核心流程',
          permissions: ['workflow:process:view']
        },
        children: [
          // 入职流程
          {
            path: 'onboarding',
            name: 'OnboardingProcess',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-onboarding" */
                '@/views/workflow/processes/onboarding/index.vue'
              ),
            meta: { title: '入职流程' }
          },
          {
            path: 'onboarding/apply',
            name: 'OnboardingApply',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-onboarding-apply" */
                '@/views/workflow/processes/onboarding/Apply.vue'
              ),
            meta: { title: '发起入职申请', hidden: true }
          },
          {
            path: 'onboarding/approval/:taskId',
            name: 'OnboardingApproval',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-onboarding-approval" */
                '@/views/workflow/processes/onboarding/Approval.vue'
              ),
            meta: { title: '入职审批', hidden: true }
          },

          // 请假流程
          {
            path: 'leave',
            name: 'LeaveProcess',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-leave" */
                '@/views/workflow/processes/leave/index.vue'
              ),
            meta: { title: '请假流程' }
          },
          {
            path: 'leave/apply',
            name: 'LeaveApply',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-leave-apply" */
                '@/views/workflow/processes/leave/Apply.vue'
              ),
            meta: { title: '发起请假申请', hidden: true }
          },
          {
            path: 'leave/approval/:taskId',
            name: 'LeaveApproval',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-leave-approval" */
                '@/views/workflow/processes/leave/Approval.vue'
              ),
            meta: { title: '请假审批', hidden: true }
          },

          // 调岗流程
          {
            path: 'transfer',
            name: 'TransferProcess',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-transfer" */
                '@/views/workflow/processes/transfer/index.vue'
              ),
            meta: { title: '调岗流程' }
          },
          {
            path: 'transfer/apply',
            name: 'TransferApply',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-transfer-apply" */
                '@/views/workflow/processes/transfer/Apply.vue'
              ),
            meta: { title: '发起调岗申请', hidden: true }
          },
          {
            path: 'transfer/approval/:taskId',
            name: 'TransferApproval',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-transfer-approval" */
                '@/views/workflow/processes/transfer/Approval.vue'
              ),
            meta: { title: '调岗审批', hidden: true }
          },

          // 离职流程
          {
            path: 'resignation',
            name: 'ResignationProcess',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-resignation" */
                '@/views/workflow/processes/resignation/index.vue'
              ),
            meta: { title: '离职流程' }
          },
          {
            path: 'resignation/apply',
            name: 'ResignationApply',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-resignation-apply" */
                '@/views/workflow/processes/resignation/Apply.vue'
              ),
            meta: { title: '发起离职申请', hidden: true }
          },
          {
            path: 'resignation/approval/:taskId',
            name: 'ResignationApproval',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-resignation-approval" */
                '@/views/workflow/processes/resignation/Approval.vue'
              ),
            meta: { title: '离职审批', hidden: true }
          },
          {
            path: 'resignation/handover/:id',
            name: 'ResignationHandover',
            component: () =>
              import(
                /* webpackChunkName: "workflow-process-resignation-handover" */
                '@/views/workflow/processes/resignation/Handover.vue'
              ),
            meta: { title: '工作交接', hidden: true }
          }
        ]
      },

      // 流程统计
      {
        path: 'statistics',
        name: 'WorkflowStatistics',
        redirect: '/workflow/statistics/dashboard',
        meta: {
          title: '流程统计',
          permissions: ['workflow:statistics:view']
        },
        children: [
          {
            path: 'dashboard',
            name: 'StatisticsDashboard',
            component: () =>
              import(
                /* webpackChunkName: "workflow-stats-dashboard" */
                '@/views/workflow/statistics/Dashboard.vue'
              ),
            meta: { title: '统计看板' }
          },
          {
            path: 'efficiency',
            name: 'ProcessEfficiency',
            component: () =>
              import(
                /* webpackChunkName: "workflow-stats-efficiency" */
                '@/views/workflow/statistics/Efficiency.vue'
              ),
            meta: { title: '流程效率' }
          },
          {
            path: 'workload',
            name: 'ApprovalWorkload',
            component: () =>
              import(
                /* webpackChunkName: "workflow-stats-workload" */
                '@/views/workflow/statistics/Workload.vue'
              ),
            meta: { title: '审批工作量' }
          },
          {
            path: 'trends',
            name: 'ProcessTrends',
            component: () =>
              import(
                /* webpackChunkName: "workflow-stats-trends" */
                '@/views/workflow/statistics/Trends.vue'
              ),
            meta: { title: '流程趋势' }
          }
        ]
      },

      // 流程配置
      {
        path: 'configuration',
        name: 'WorkflowConfiguration',
        redirect: '/workflow/configuration/general',
        meta: {
          title: '流程配置',
          permissions: ['workflow:config:manage']
        },
        children: [
          {
            path: 'general',
            name: 'GeneralConfig',
            component: () =>
              import(
                /* webpackChunkName: "workflow-config-general" */
                '@/views/workflow/configuration/General.vue'
              ),
            meta: { title: '基础配置' }
          },
          {
            path: 'approvers',
            name: 'ApproverConfig',
            component: () =>
              import(
                /* webpackChunkName: "workflow-config-approvers" */
                '@/views/workflow/configuration/Approvers.vue'
              ),
            meta: { title: '审批人配置' }
          },
          {
            path: 'notifications',
            name: 'NotificationConfig',
            component: () =>
              import(
                /* webpackChunkName: "workflow-config-notifications" */
                '@/views/workflow/configuration/Notifications.vue'
              ),
            meta: { title: '通知配置' }
          },
          {
            path: 'forms',
            name: 'FormConfig',
            component: () =>
              import(
                /* webpackChunkName: "workflow-config-forms" */
                '@/views/workflow/configuration/Forms.vue'
              ),
            meta: { title: '表单配置' }
          },
          {
            path: 'integration',
            name: 'IntegrationConfig',
            component: () =>
              import(
                /* webpackChunkName: "workflow-config-integration" */
                '@/views/workflow/configuration/Integration.vue'
              ),
            meta: { title: '集成配置' }
          }
        ]
      },

      // 详情页面（隐藏路由）
      {
        path: 'task/:taskId',
        name: 'WorkflowTaskDetail',
        component: () =>
          import(
            /* webpackChunkName: "workflow-detail-task" */
            '@/views/workflow/TaskDetail.vue'
          ),
        meta: {
          title: '任务详情',
          hidden: true,
          permissions: ['workflow:task:view']
        }
      },
      {
        path: 'instance/:instanceId',
        name: 'WorkflowInstanceDetail',
        component: () =>
          import(
            /* webpackChunkName: "workflow-detail-instance" */
            '@/views/workflow/InstanceDetail.vue'
          ),
        meta: {
          title: '流程实例详情',
          hidden: true,
          permissions: ['workflow:instance:view']
        }
      }
    ]
  }
]

export default workflowRoutes
