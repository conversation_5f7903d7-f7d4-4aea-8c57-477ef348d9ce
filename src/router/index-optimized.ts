import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'
import { setupRouteLevelSplitting, createCodeSplittingPlugin } from '@/utils/code-splitting'

// 静态路由（不需要权限）
const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import(/* webpackChunkName: "home" */ '@/views/HomeView.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import(/* webpackChunkName: "login" */ '@/views/login/index.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import(/* webpackChunkName: "error-404" */ '@/views/error/404.vue'),
    meta: { title: '页面不存在', hidden: true }
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import(/* webpackChunkName: "error-403" */ '@/views/error/403.vue'),
    meta: { title: '无权限', hidden: true }
  }
]

// 异步路由模块（需要权限）
const asyncRouteModules: Record<string, () => Promise<{ default: RouteRecordRaw[] }>> = {
  // 核心模块
  organization: () =>
    import(/* webpackChunkName: "routes-organization" */ './modules/organization'),
  employee: () => import(/* webpackChunkName: "routes-employee" */ './modules/employee'),

  // 业务模块 - 使用优化的懒加载配置
  system: () => import(/* webpackChunkName: "routes-system" */ './modules/system'),
  workflow: () => import(/* webpackChunkName: "routes-workflow" */ './modules/workflow-optimized'),
  reports: () => import(/* webpackChunkName: "routes-reports" */ './modules/reports'),

  // 其他模块
  attendance: () => import(/* webpackChunkName: "routes-attendance" */ './modules/attendance'),
  salary: () => import(/* webpackChunkName: "routes-salary" */ './modules/salary'),
  recruitment: () => import(/* webpackChunkName: "routes-recruitment" */ './modules/recruitment'),
  contract: () => import(/* webpackChunkName: "routes-contract" */ './modules/contract'),
  notification: () =>
    import(/* webpackChunkName: "routes-notification" */ './modules/notification'),

  // 演示模块
  demo: () => import(/* webpackChunkName: "routes-demo" */ './modules/demo')
}

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 动态加载路由模块
let isRoutesLoaded = false

export async function loadAsyncRoutes(modules?: string[]) {
  if (isRoutesLoaded) return

  const asyncRoutes: RouteRecordRaw[] = []

  // 加载指定模块或所有模块
  const modulesToLoad = modules || Object.keys(asyncRouteModules)

  for (const moduleName of modulesToLoad) {
    if (asyncRouteModules[moduleName]) {
      try {
        const module = await asyncRouteModules[moduleName]()
        if (module.default && Array.isArray(module.default)) {
          asyncRoutes.push(...module.default)
        }
      } catch (__error) {
        }
    }
  }

  // 添加路由
  asyncRoutes.forEach(route => {
    router.addRoute(route)
  })

  // 添加404路由（必须放在最后）
  router.addRoute({
    path: '/:pathMatch(.*)*',
    redirect: '/404',
    meta: { hidden: true }
  })

  isRoutesLoaded = true
}

// 重置路由
export function resetRouter() {
  const newRouter = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: constantRoutes
  })

  // 重置路由匹配器
  ;(router as any).matcher = (newRouter as any).matcher
  isRoutesLoaded = false
}

// 获取所有路由
export function getRoutes() {
  return router.getRoutes()
}

// 应用路由级别代码分割优化
const optimizedRoutes = setupRouteLevelSplitting(router.options.routes || [])
router.options.routes = optimizedRoutes

// 设置路由守卫
setupRouterGuards(router)

// 路由性能监控
if (import.meta.env.DEV) {
  router.beforeEach((to, from) => {
    return true
  })

  router.afterEach(_to => {
    })
}

export default router
