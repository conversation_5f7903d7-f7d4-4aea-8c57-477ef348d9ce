/**
 * 路由守卫
 * 处理权限验证、动态路由加载等
 */

import type { Router } from 'vue-router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { useUserStore, usePermissionStore } from '@/stores'
import { markPerformance, measurePerformance } from '@/utils/performance'

// 配置进度条
NProgress.configure({ showSpinner: false })

// 白名单路由（不需要登录）
const whiteList = ['/login', '/404', '/403']

// 设置路由守卫
export function setupRouterGuards(router: Router) {
  // 前置守卫
  router.beforeEach(async (to, from, next) => {
    // 开始进度条
    NProgress.start()

    // 开发环境调试
    if (import.meta.env.DEV) {
    }

    // 记录路由开始时间
    markPerformance(`route-start-${to.name || to.path}`)

    // 设置页面标题
    document.title = `${to.meta.title || '杭科院人事管理系统'}`

    const userStore = useUserStore()
    const permissionStore = usePermissionStore()

    // 判断是否已登录
    const hasToken = userStore.token

    if (hasToken) {
      if (to.path === '/login') {
        // 已登录，跳转到首页
        next({ path: '/' })
        NProgress.done()
      } else {
        // 判断是否已获取用户信息
        const hasUserInfo = userStore.userInfo && userStore.isLogin

        if (hasUserInfo) {
          // 已有用户信息，直接放行
          next()
        } else {
          // 开发环境Mock模式下，暂时跳过用户信息获取
          if (import.meta.env.DEV && import.meta.env.VITE_ENABLE_MOCK === 'true') {
            // 设置简单的用户信息
            userStore.$patch({
              userInfo: {
                userId: '100001',
                username: 'admin',
                name: '系统管理员',
                realName: '系统管理员',
                role: 'admin',
                department: '信息中心',
                position: '系统管理员'
              },
              isLogin: true,
              permissions: ['*:*:*'],
              roles: [{ code: 'admin', name: '管理员' }]
            })
            next()
          } else {
            try {
              // 获取用户信息
              await userStore.getUserInfo()

              // 生成动态路由
              const accessRoutes = await permissionStore.generateRoutes()

              // 动态添加路由
              accessRoutes.forEach(route => {
                router.addRoute(route)
              })

              // 添加404路由（必须放在最后）
              router.addRoute({
                path: '/:pathMatch(.*)*',
                redirect: '/404',
                meta: { hidden: true }
              })

              // hack方法 确保addRoute已完成
              next({ ...to, replace: true })
            } catch (__error) {
              // 获取用户信息失败，清除token并跳转到登录页
              console.error('获取用户信息失败:', __error)
              await userStore.resetUser()
              ElMessage.error('获取用户信息失败，请重新登录')
              next({ path: '/login', query: { redirect: to.path } })
              NProgress.done()
            }
          }
        }
      }
    } else {
      // 未登录
      if (whiteList.includes(to.path)) {
        // 在白名单中，直接放行
        next()
      } else {
        // 不在白名单中，跳转到登录页
        next({ path: '/login', query: { redirect: to.path } })
        NProgress.done()
      }
    }
  })

  // 后置守卫
  router.afterEach((to, from) => {
    // 结束进度条
    NProgress.done()

    // 记录路由结束时间
    markPerformance(`route-end-${to.name || to.path}`)

    // 测量路由切换时间
    measurePerformance(
      `route-transition-${from.name || from.path}-to-${to.name || to.path}`,
      `route-start-${to.name || to.path}`,
      `route-end-${to.name || to.path}`
    )

    // 等待DOM更新后测量页面渲染时间
    requestIdleCallback(() => {
      markPerformance(`route-rendered-${to.name || to.path}`)
      measurePerformance(
        `route-render-${to.name || to.path}`,
        `route-end-${to.name || to.path}`,
        `route-rendered-${to.name || to.path}`
      )
    })
  })

  // 错误处理
  router.onError(_error => {
    NProgress.done()
  })
}

// 重置路由（用于退出登录）
export function resetRouter(router: Router) {
  const permissionStore = usePermissionStore()

  // 重置权限store
  permissionStore.resetPermission()

  // 重新加载路由
  // 注意：Vue Router 4中没有直接的重置方法，需要手动处理
  // 这里的处理方式是刷新页面
  location.reload()
}
