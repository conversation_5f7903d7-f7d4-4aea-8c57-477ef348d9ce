import type { RouteRecordRaw } from 'vue-router'

// 主布局内的路由
export const mainRoutes: RouteRecordRaw[] = [
  {
    path: 'dashboard',
    name: 'dashboard',
    component: () => import('@/views/dashboard/index.vue'),
    meta: { title: '仪表板' }
  },
  // 组织管理
  {
    path: 'organization',
    name: 'organization',
    redirect: '/organization/structure',
    meta: { title: '组织管理' },
    children: [
      {
        path: 'structure',
        name: 'organization-structure',
        component: () => import('@/views/organization/index.vue'),
        meta: { title: '组织架构管理' }
      },
      {
        path: 'change',
        name: 'organization-change',
        component: () => import('@/views/organization/change.vue'),
        meta: { title: '变更管理' }
      },
      {
        path: 'query',
        name: 'organization-query',
        component: () => import('@/views/organization/query/index.vue'),
        meta: { title: '组织查询' }
      },
      {
        path: 'statistics',
        name: 'organization-statistics',
        component: () => import('@/views/organization/statistics/index.vue'),
        meta: { title: '组织统计分析' }
      }
    ]
  },
  // 岗位管理
  {
    path: 'position',
    name: 'position',
    component: () => import('@/views/position/PositionManagement.vue'),
    meta: { title: '岗位管理' }
  },
  // 编制管理
  {
    path: 'establishment',
    name: 'establishment',
    component: () => import('@/views/establishment/EstablishmentManagement.vue'),
    meta: { title: '编制管理' }
  },
  // 员工管理
  {
    path: 'employee',
    name: 'employee',
    redirect: '/employee/management',
    meta: { title: '员工管理' },
    children: [
      {
        path: 'management',
        name: 'employee-management',
        component: () => import('@/views/employee/EmployeeManagement.vue'),
        meta: { title: '员工基础管理' }
      },
      {
        path: 'list',
        name: 'employee-list',
        component: () => import('@/views/employee/list/index.vue'),
        meta: { title: '员工列表' }
      },
      {
        path: 'add',
        name: 'employee-add',
        component: () => import('@/views/employee/add/index.vue'),
        meta: { title: '新增员工' }
      },
      {
        path: 'detail/:id',
        name: 'employee-detail',
        component: () => import('@/views/employee/detail/index.vue'),
        meta: { title: '员工详情' }
      },
      {
        path: 'edit/:id',
        name: 'employee-edit',
        component: () => import('@/views/employee/edit/index.vue'),
        meta: { title: '编辑员工' }
      },
      {
        path: 'import',
        name: 'employee-import',
        component: () => import('@/views/employee/import/index.vue'),
        meta: { title: '批量导入' }
      },
      {
        path: 'tag',
        name: 'employee-tag',
        component: () => import('@/views/employee/tag/index.vue'),
        meta: { title: '标签管理' }
      },
      {
        path: 'talent-honors',
        name: 'talent-honors',
        component: () => import('@/views/employee/TalentHonorsManagement.vue'),
        meta: { title: '人才荣誉管理' }
      },
      {
        path: 'education-history',
        name: 'education-history',
        component: () => import('@/views/employee/EducationHistoryManagement.vue'),
        meta: { title: '教育经历管理' }
      }
    ]
  },
  // 编制管理
  {
    path: 'staffing',
    name: 'staffing',
    redirect: '/staffing/plans',
    meta: { title: '编制管理' },
    children: [
      {
        path: 'plans',
        name: 'staffing-plans',
        component: () => import('@/views/staffing/StaffingPlanManagement.vue'),
        meta: { title: '编制规划管理' }
      },
      {
        path: 'statistics',
        name: 'staffing-statistics',
        component: () => import('@/views/staffing/StaffingStatistics.vue'),
        meta: { title: '编制统计分析' }
      }
    ]
  },
  // 人事变动
  {
    path: 'personnel-change',
    name: 'personnel-change',
    redirect: '/personnel-change/management',
    meta: { title: '人事变动管理' },
    children: [
      {
        path: 'management',
        name: 'personnel-change-management',
        component: () => import('@/views/personnel-change/PersonnelChangeManagement.vue'),
        meta: { title: '人事变动管理' }
      },
      {
        path: 'new-employee-onboarding',
        name: 'personnel-change-new-employee-onboarding',
        component: () => import('@/views/personnel-change/NewEmployeeOnboarding.vue'),
        meta: { title: '新教工入职管理' }
      },
      {
        path: 'position-adjustment',
        name: 'personnel-change-position-adjustment',
        component: () => import('@/views/personnel-change/PositionAdjustment.vue'),
        meta: { title: '校内岗位调整' }
      },
      {
        path: 'retirement-management',
        name: 'personnel-change-retirement-management',
        component: () => import('@/views/personnel-change/RetirementManagement.vue'),
        meta: { title: '退休管理' }
      },
      {
        path: 'leave-school-management',
        name: 'personnel-change-leave-school-management',
        component: () => import('@/views/personnel-change/LeaveSchoolManagement.vue'),
        meta: { title: '离校管理' }
      }
    ]
  },
  // 合同管理
  {
    path: 'contract',
    name: 'contract',
    redirect: '/contract/management',
    meta: { title: '合同管理' },
    children: [
      {
        path: 'management',
        name: 'contract-management',
        component: () => import('@/views/contract/ContractManagement.vue'),
        meta: { title: '合同管理' }
      },
      {
        path: 'contract-information',
        name: 'contract-contract-information',
        component: () => import('@/views/contract/ContractInformationManagement.vue'),
        meta: { title: '合同信息管理' }
      },
      {
        path: 'expiry-reminder',
        name: 'contract-expiry-reminder',
        component: () => import('@/views/contract/ContractExpiryReminder.vue'),
        meta: { title: '合同到期提醒' }
      },
      {
        path: 'template-management',
        name: 'contract-template-management',
        component: () => import('@/views/contract/ContractTemplateManagement.vue'),
        meta: { title: '合同模板管理' }
      }
    ]
  },
  // 考勤管理
  {
    path: 'attendance',
    name: 'attendance',
    redirect: '/attendance/management',
    meta: { title: '考勤管理' },
    children: [
      {
        path: 'management',
        name: 'attendance-management',
        component: () => import('@/views/attendance/AttendanceManagement.vue'),
        meta: { title: '考勤管理首页' }
      },
      {
        path: 'rule-config',
        name: 'attendance-rule-config',
        component: () => import('@/views/attendance/AttendanceRuleConfig.vue'),
        meta: { title: '考勤规则设置' }
      },
      {
        path: 'shift-management',
        name: 'attendance-shift-management',
        component: () => import('@/views/attendance/ShiftManagement.vue'),
        meta: { title: '排班管理' }
      },
      {
        path: 'clock-in-record',
        name: 'attendance-clock-in-record',
        component: () => import('@/views/attendance/ClockInRecordManagement.vue'),
        meta: { title: '打卡记录管理' }
      },
      {
        path: 'leave-application',
        name: 'attendance-leave-application',
        component: () => import('@/views/attendance/LeaveApplication.vue'),
        meta: { title: '请假申请' }
      },
      {
        path: 'statistics',
        name: 'attendance-statistics',
        component: () => import('@/views/attendance/AttendanceStatistics.vue'),
        meta: { title: '考勤统计分析' }
      }
    ]
  },
  // 薪酬福利
  {
    path: 'salary',
    name: 'salary',
    redirect: '/salary/management',
    meta: { title: '薪酬福利管理' },
    children: [
      {
        path: 'management',
        name: 'salary-management',
        component: () => import('@/views/salary/SalaryManagement.vue'),
        meta: { title: '薪酬管理首页' }
      },
      {
        path: 'structure',
        name: 'salary-structure',
        component: () => import('@/views/salary/SalaryStructure.vue'),
        meta: { title: '薪资结构管理' }
      },
      {
        path: 'standards',
        name: 'salary-standards',
        component: () => import('@/views/salary/SalaryStandardManagement.vue'),
        meta: { title: '薪酬标准管理' }
      },
      {
        path: 'salary-slip',
        name: 'salary-slip',
        component: () => import('@/views/salary/SalarySlipView.vue'),
        meta: { title: '工资条查询' }
      },
      {
        path: 'benefits',
        name: 'salary-benefits',
        component: () => import('@/views/salary/BenefitManagement.vue'),
        meta: { title: '福利管理' }
      },
      {
        path: 'analysis',
        name: 'salary-analysis',
        component: () => import('@/views/salary/SalaryAnalysisManagement.vue'),
        meta: { title: '薪酬分析管理' }
      }
    ]
  },
  // 招聘管理
  {
    path: 'recruitment',
    name: 'recruitment',
    redirect: '/recruitment/management',
    meta: { title: '招聘管理' },
    children: [
      {
        path: 'management',
        name: 'recruitment-management',
        component: () => import('@/views/recruitment/RecruitmentManagement.vue'),
        meta: { title: '招聘管理首页' }
      },
      {
        path: 'plans',
        name: 'recruitment-plans',
        component: () => import('@/views/recruitment/RecruitmentPlanManagement.vue'),
        meta: { title: '招聘计划管理' }
      },
      {
        path: 'positions',
        name: 'recruitment-positions',
        component: () => import('@/views/recruitment/JobPositionManagement.vue'),
        meta: { title: '岗位信息管理' }
      },
      {
        path: 'applicants',
        name: 'recruitment-applicants',
        component: () => import('@/views/recruitment/ApplicantManagement.vue'),
        meta: { title: '应聘人员管理' }
      },
      {
        path: 'talent-pool',
        name: 'recruitment-talent-pool',
        component: () => import('@/views/recruitment/TalentPoolManagement.vue'),
        meta: { title: '人才库管理' }
      }
    ]
  },
  // 工作流
  {
    path: 'workflow',
    name: 'workflow',
    redirect: '/workflow/approval-center',
    meta: { title: '工作流管理' },
    children: [
      {
        path: 'approval-center',
        name: 'workflow-approval-center',
        component: () => import('@/views/workflow/ApprovalCenter.vue'),
        meta: { title: '审批中心' }
      },
      {
        path: 'my-tasks',
        name: 'workflow-my-tasks',
        component: () => import('@/views/workflow/MyTasks.vue'),
        meta: { title: '我的待办' }
      },
      {
        path: 'my-applications',
        name: 'workflow-my-applications',
        component: () => import('@/views/workflow/MyApplications.vue'),
        meta: { title: '我的申请' }
      },
      {
        path: 'designer/:id?',
        name: 'workflow-designer',
        component: () => import('@/views/workflow/ProcessDesigner.vue'),
        meta: { title: '流程设计器' }
      },
      {
        path: 'management',
        name: 'workflow-management',
        component: () => import('@/views/workflow/WorkflowManagement.vue'),
        meta: { title: '流程定义管理' }
      }
    ]
  },
  // 系统管理
  {
    path: 'system',
    name: 'system',
    redirect: '/system/user-management',
    meta: { title: '系统管理' },
    children: [
      {
        path: 'user-management',
        name: 'user-management',
        component: () => import('@/views/system/UserManagement.vue'),
        meta: { title: '用户管理' }
      },
      {
        path: 'role-management',
        name: 'role-management',
        component: () => import('@/views/system/RoleManagement.vue'),
        meta: { title: '角色管理' }
      },
      {
        path: 'permission-management',
        name: 'permission-management',
        component: () => import('@/views/system/PermissionManagement.vue'),
        meta: { title: '权限管理' }
      },
      {
        path: 'system-monitoring',
        name: 'system-monitoring',
        component: () => import('@/views/system/SystemMonitoring.vue'),
        meta: { title: '系统监控' }
      },
      {
        path: 'system-settings',
        name: 'system-settings',
        component: () => import('@/views/system/SystemSettings.vue'),
        meta: { title: '系统设置' }
      }
    ]
  }
]

// 开发工具路由（仅开发环境）
export const devRoutes: RouteRecordRaw[] = import.meta.env.DEV
  ? [
      {
        path: 'dev',
        name: 'dev-tools',
        redirect: '/dev/mock-manager',
        meta: { title: '开发工具', hidden: true },
        children: [
          {
            path: 'mock-manager',
            name: 'mock-manager',
            component: () => import('@/views/dev/mock-manager/index.vue'),
            meta: { title: 'Mock数据管理' }
          }
        ]
      }
    ]
  : []

// 完整路由配置
export const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    redirect: '/dashboard',
    children: [...mainRoutes, ...devRoutes]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面未找到', hidden: true }
  }
]
