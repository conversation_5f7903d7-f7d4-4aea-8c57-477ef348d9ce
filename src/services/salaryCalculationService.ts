/**
 * 薪资计算服务
 * 整合公式解析器和计算引擎，提供完整的薪资计算功能
 */

import { FormulaParser } from '@/utils/salary/formulaParser'
import {
  CalculationEngine,
  TaxCalculator,
  SocialInsuranceCalculator
} from '@/utils/salary/calculationEngine'
import type {
  SalaryStructure,
  SalaryStandard,
  PersonalSalary,
  SalaryCalculateParams,
  SalaryCalculateResult,
  PersonalSalaryItem
} from '@/types/salary'

export class SalaryCalculationService {
  private parser: FormulaParser
  private engine: CalculationEngine

  constructor() {
    this.parser = new FormulaParser()
    this.engine = new CalculationEngine()
  }

  /**
   * 计算员工薪资
   */
  public async calculateSalary(params: SalaryCalculateParams): Promise<SalaryCalculateResult> {
    try {
      // 1. 获取薪资结构和标准
      const structure = await this.getSalaryStructure(params.structureId)
      const standard = await this.getSalaryStandard(params.employeeId, params.structureId)

      // 2. 准备计算上下文
      this.prepareContext(params, standard)

      // 3. 计算各薪资项目
      const items: PersonalSalaryItem[] = []
      let totalIncome = 0
      let totalDeduction = 0

      for (const structureItem of structure.items) {
        const value = await this.calculateItem(structureItem, standard)

        items.push({
          itemId: structureItem.id || structureItem.tempId || '',
          itemName: structureItem.name,
          itemCode: structureItem.code,
          itemType: structureItem.type,
          value,
          formula: structureItem.formula,
          visible: structureItem.visible,
          sort: structureItem.sort
        })

        // 累计收入和扣除
        if (structureItem.type === 'deduction') {
          totalDeduction += value
        } else {
          totalIncome += value
        }
      }

      // 4. 计算社保公积金
      const socialInsurance = this.calculateSocialInsurance(params, totalIncome)
      totalDeduction += socialInsurance.total

      // 5. 计算个税
      const incomeTax = this.calculateIncomeTax(totalIncome, totalDeduction, params)
      totalDeduction += incomeTax

      // 添加社保和个税到项目列表
      items.push(...this.createDeductionItems(socialInsurance, incomeTax))

      // 6. 计算实发工资
      const netSalary = totalIncome - totalDeduction

      return {
        employeeId: params.employeeId,
        period: params.period,
        items,
        totalIncome,
        totalDeduction,
        netSalary,
        warnings: this.validateCalculation(totalIncome, totalDeduction, netSalary)
      }
    } catch (__error) {
      throw new Error(`薪资计算失败: ${error}`)
    }
  }

  /**
   * 批量计算薪资
   */
  public async batchCalculate(
    employeeIds: string[],
    period: string,
    structureId: string
  ): Promise<SalaryCalculateResult[]> {
    const results: SalaryCalculateResult[] = []

    for (const employeeId of employeeIds) {
      try {
        const result = await this.calculateSalary({
          employeeId,
          period,
          structureId,
          baseData: await this.getEmployeeBaseData(employeeId, period)
        })
        results.push(result)
      } catch (__error) {
        // 继续处理其他员工
      }
    }

    return results
  }

  /**
   * 准备计算上下文
   */

  private prepareContext(params: SalaryCalculateParams, standard?: unknown): void {
    // 设置基础变量
    this.engine.setVariables({
      // 基础数据
      ATTENDANCEDAYS: params.baseData?.attendanceDays || 22,
      WORK_DAYS: params.baseData?.workDays || 22,
      OVERTIME_HOURS: params.baseData?.overtimeHours || 0,
      LEAVE_DAYS: params.baseData?.leaveDays || 0,

      // 标准值
      BASE_SALARY_STANDARD: standard?.baseSalary || 0,
      POSITION_SALARY_STANDARD: standard?.positionSalary || 0,
      PERFORMANCE_STANDARD: standard?.performanceSalary || 0,

      // 系数
      PERFORMANCE_RATE: params.baseData?.performanceRate || 1,
      ATTENDANCE_RATE: (params.baseData?.attendanceDays || 22) / (params.baseData?.workDays || 22),

      // 其他
      YEAR: new Date(params.period).getFullYear(),
      MONTH: new Date(params.period).getMonth() + 1
    })
  }

  /**
   * 计算单个薪资项目
   */

  private async calculateItem(item: unknown, standard: unknown): Promise<number> {
    // 如果是固定值类型，直接返回标准值
    if (item.type === 'fixed') {
      const standardItem = standard?.items?.find((s: unknown) => s.itemId === item.id)
      return standardItem?.standardValue || 0
    }

    // 如果是计算类型，解析并执行公式
    if (item.type === 'calculated' && item.formula) {
      try {
        const ast = this.parser.parse(item.formula)
        const result = this.engine.evaluate(ast)
        return Math.round(result * 100) / 100 // 保留两位小数
      } catch (__error) {
        return 0
      }
    }

    return 0
  }

  /**
   * 计算社保公积金
   */

  private calculateSocialInsurance(params: SalaryCalculateParams, salary: number): unknown {
    const base = Math.min(salary, 30000) // 假设缴纳基数上限为30000
    const result = SocialInsuranceCalculator.calculate(base)

    return {
      pension: result.employee.pension,
      medical: result.employee.medical,
      unemployment: result.employee.unemployment,
      injury: result.employee.injury,
      maternity: result.employee.maternity,
      housing: result.employee.housing,
      total: result.total.employee
    }
  }

  /**
   * 计算个人所得税
   */
  private calculateIncomeTax(
    income: number,
    deductions: number,
    params: SalaryCalculateParams
  ): number {
    // 专项附加扣除（示例）
    const specialDeductions = TaxCalculator.calculateSpecialDeductions({
      childEducation: params.baseData?.childrenCount || 0,
      housingLoan: params.baseData?.hasHousingLoan ? 1 : 0,
      elderlySupport: params.baseData?.elderlyCount || 0
    })

    return TaxCalculator.calculate(income, deductions + specialDeductions)
  }

  /**
   * 创建扣除项目
   */

  private createDeductionItems(socialInsurance: unknown, incomeTax: number): PersonalSalaryItem[] {
    const items: PersonalSalaryItem[] = []
    let sort = 100

    // 社保项目
    const insuranceItems = [
      { code: 'pension_insurance', name: '养老保险', value: socialInsurance.pension },
      { code: 'medical_insurance', name: '医疗保险', value: socialInsurance.medical },
      { code: 'unemployment_insurance', name: '失业保险', value: socialInsurance.unemployment },
      { code: 'housing_fund', name: '住房公积金', value: socialInsurance.housing }
    ]

    insuranceItems.forEach(item => {
      if (item.value > 0) {
        items.push({
          itemId: `deduction_${item.code}`,
          itemName: item.name,
          itemCode: item.code,
          itemType: 'deduction',
          value: item.value,
          visible: true,
          sort: sort++
        })
      }
    })

    // 个人所得税
    if (incomeTax > 0) {
      items.push({
        itemId: 'deduction_income_tax',
        itemName: '个人所得税',
        itemCode: 'income_tax',
        itemType: 'deduction',
        value: incomeTax,
        visible: true,
        sort: sort++
      })
    }

    return items
  }

  /**
   * 验证计算结果
   */
  private validateCalculation(income: number, deduction: number, netSalary: number): string[] {
    const warnings: string[] = []

    if (income <= 0) {
      warnings.push('应发工资为0或负数，请检查计算公式')
    }

    if (deduction > income) {
      warnings.push('扣除金额大于应发工资')
    }

    if (netSalary < 0) {
      warnings.push('实发工资为负数')
    }

    if (Math.abs(income - deduction - netSalary) > 0.01) {
      warnings.push('计算结果存在误差')
    }

    return warnings
  }

  /**
   * 模拟获取薪资结构
   */
  private async getSalaryStructure(structureId: string): Promise<SalaryStructure> {
    // 实际应该从API获取
    return {
      id: structureId,
      name: '教师薪资结构',
      type: 'teacher',
      items: [
        {
          id: '1',
          tempId: '',
          name: '基本工资',
          code: 'base_salary',
          type: 'fixed',
          formula: '',
          visible: true,
          sort: 1
        },
        {
          id: '2',
          tempId: '',
          name: '岗位工资',
          code: 'position_salary',
          type: 'fixed',
          formula: '',
          visible: true,
          sort: 2
        },
        {
          id: '3',
          tempId: '',
          name: '绩效工资',
          code: 'performance_salary',
          type: 'calculated',
          formula: 'PERFORMANCE_STANDARD * PERFORMANCE_RATE * ATTENDANCE_RATE',
          visible: true,
          sort: 3
        },
        {
          id: '4',
          tempId: '',
          name: '加班费',
          code: 'overtime_pay',
          type: 'calculated',
          formula: 'BASE_SALARY_STANDARD / WORK_DAYS / 8 * 1.5 * OVERTIME_HOURS',
          visible: true,
          sort: 4
        }
      ],
      status: 'active',
      remark: '',
      createdBy: 'admin',
      createdAt: '2024-01-01',
      updatedBy: 'admin',
      updatedAt: '2024-01-01'
    }
  }

  /**
   * 模拟获取薪资标准
   */
  private async getSalaryStandard(employeeId: string, structureId: string): Promise<unknown> {
    // 实际应该从API获取
    return {
      baseSalary: 8000,
      positionSalary: 5000,
      performanceSalary: 3000,
      items: []
    }
  }

  /**
   * 模拟获取员工基础数据
   */
  private async getEmployeeBaseData(
    employeeId: string,
    period: string
  ): Promise<Record<string, number>> {
    // 实际应该从API获取
    return {
      attendanceDays: 22,
      workDays: 22,
      overtimeHours: 10,
      leaveDays: 0,
      performanceRate: 0.95,
      childrenCount: 1,
      hasHousingLoan: 1,
      elderlyCount: 2
    }
  }
}
