<template>
  <div class="external-staff-contract-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>编外人员合同管理</h2>
      <p>管理劳务协议、劳务派遣、兼职等编外人员合同的登记、导入和管理</p>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="function-tabs">
      <!-- 劳务协议 -->
      <el-tab-pane label="劳务协议" name="labor-agreement">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">劳务协议管理</span>
            <div class="tab-actions">
              <el-button type="success" @click="handleAddContract('LABOR_AGREEMENT')">
                <el-icon><Plus /></el-icon>
                新增协议
              </el-button>
              <el-button @click="handleImportContracts('LABOR_AGREEMENT')">
                <el-icon><Upload /></el-icon>
                批量导入
              </el-button>
              <el-button @click="handleExportContracts('LABOR_AGREEMENT')">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>

          <!-- 搜索筛选 -->
          <div class="filter-form">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model="laborAgreementFilter.keyword"
                  placeholder="搜索姓名、身份证号、合同编号"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="laborAgreementFilter.status" placeholder="合同状态" clearable>
                  <el-option label="有效" value="ACTIVE"  />
                  <el-option label="即将到期" value="EXPIRING"  />
                  <el-option label="已到期" value="EXPIRED"  />
                  <el-option label="已终止" value="TERMINATED"  />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="laborAgreementFilter.department" placeholder="用工部门" clearable>
                  <el-option label="全部部门" value=""  />
                  <el-option
                    v-for="dept in departmentOptions"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.id"
                   />
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-date-picker
                  v-model="laborAgreementFilter.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                 />
              </el-col>
              <el-col :span="6">
                <el-button type="primary" @click="handleFilterLaborAgreement">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleResetLaborAgreementFilter">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 统计卡片 -->
          <el-row :gutter="20" class="stats-row">
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon total">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ laborAgreementStats.total }}</div>
                    <div class="stats-label">协议总数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon active">
                    <el-icon><Check /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ laborAgreementStats.active }}</div>
                    <div class="stats-label">有效协议</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon expiring">
                    <el-icon><Warning /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ laborAgreementStats.expiring }}</div>
                    <div class="stats-label">即将到期</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon thisMonth">
                    <el-icon><Star /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ laborAgreementStats.thisMonth }}</div>
                    <div class="stats-label">本月新增</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 劳务协议列表 -->
          <el-table :data="laborAgreementList" style="width: 100%" @selection-change="handleLaborAgreementSelectionChange">
            <el-table-column type="selection" width="55"  />
            <el-table-column prop="contractNumber" label="协议编号" width="120"  />
            <el-table-column prop="staffName" label="姓名" width="100"  />
            <el-table-column prop="idNumber" label="身份证号" width="150" show-overflow-tooltip  />
            <el-table-column prop="department" label="用工部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="position" label="岗位" width="120"  />
            <el-table-column prop="startDate" label="开始日期" width="100"  />
            <el-table-column prop="endDate" label="结束日期" width="100"  />
            <el-table-column prop="remainingDays" label="剩余天数" width="100">
              <template #default="scope">
                <el-tag :type="getRemainingDaysTag(scope.row.remainingDays)" size="small">
                  {{ scope.row.remainingDays }}天
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="monthlyFee" label="月费用(元)" width="120"  />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusTag(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewContract(scope.row)">
                  查看
                </el-button>
                <el-button size="small" type="success" link @click="handleEditContract(scope.row)">
                  编辑
                </el-button>
                <el-button size="small" type="warning" link @click="handleRenewContract(scope.row)">
                  续签
                </el-button>
                <el-button size="small" type="danger" link @click="handleTerminateContract(scope.row)">
                  终止
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 劳务派遣 -->
      <el-tab-pane label="劳务派遣" name="labor-dispatch">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">劳务派遣合同管理</span>
            <div class="tab-actions">
              <el-button type="success" @click="handleAddContract('LABOR_DISPATCH')">
                <el-icon><Plus /></el-icon>
                新增合同
              </el-button>
              <el-button @click="handleImportContracts('LABOR_DISPATCH')">
                <el-icon><Upload /></el-icon>
                批量导入
              </el-button>
              <el-button @click="handleExportContracts('LABOR_DISPATCH')">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>

          <!-- 派遣公司管理 -->
          <div class="dispatch-companies">
            <h4>派遣公司管理</h4>
            <el-row :gutter="20">
              <el-col :span="8" v-for="company in dispatchCompanies" :key="company.id">
                <el-card class="company-card" shadow="hover">
                  <div class="company-info">
                    <h5>{{ company.name }}</h5>
                    <p>联系人：{{ company.contact }}</p>
                    <p>电话：{{ company.phone }}</p>
                    <p>在职人数：{{ company.staffCount }}人</p>
                  </div>
                  <div class="company-actions">
                    <el-button size="small" type="primary" link @click="handleViewCompanyContracts(company)">
                      查看合同
                    </el-button>
                    <el-button size="small" type="success" link @click="handleEditCompany(company)">
                      编辑
                    </el-button>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="add-company-card" shadow="hover" @click="handleAddDispatchCompany">
                  <div class="add-company-content">
                    <el-icon class="add-icon"><Plus /></el-icon>
                    <span>添加派遣公司</span>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <!-- 劳务派遣合同列表 -->
          <el-table :data="laborDispatchList" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="contractNumber" label="合同编号" width="120"  />
            <el-table-column prop="staffName" label="姓名" width="100"  />
            <el-table-column prop="idNumber" label="身份证号" width="150" show-overflow-tooltip  />
            <el-table-column prop="dispatchCompany" label="派遣公司" width="150" show-overflow-tooltip  />
            <el-table-column prop="department" label="用工部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="position" label="岗位" width="120"  />
            <el-table-column prop="startDate" label="开始日期" width="100"  />
            <el-table-column prop="endDate" label="结束日期" width="100"  />
            <el-table-column prop="monthlyFee" label="月费用(元)" width="120"  />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusTag(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewContract(scope.row)">
                  查看
                </el-button>
                <el-button size="small" type="success" link @click="handleEditContract(scope.row)">
                  编辑
                </el-button>
                <el-button size="small" type="warning" link @click="handleRenewContract(scope.row)">
                  续签
                </el-button>
                <el-button size="small" type="danger" link @click="handleTerminateContract(scope.row)">
                  终止
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 兼职协议 -->
      <el-tab-pane label="兼职协议" name="part-time">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">兼职协议管理</span>
            <div class="tab-actions">
              <el-button type="success" @click="handleAddContract('PART_TIME')">
                <el-icon><Plus /></el-icon>
                新增协议
              </el-button>
              <el-button @click="handleImportContracts('PART_TIME')">
                <el-icon><Upload /></el-icon>
                批量导入
              </el-button>
              <el-button @click="handleExportContracts('PART_TIME')">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>

          <!-- 兼职类型统计 -->
          <el-row :gutter="20" class="part-time-stats">
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon teaching">
                    <el-icon><Reading /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ partTimeStats.teaching }}</div>
                    <div class="stats-label">教学兼职</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon research">
                    <el-icon><DataAnalysis /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ partTimeStats.research }}</div>
                    <div class="stats-label">科研兼职</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon management">
                    <el-icon><Management /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ partTimeStats.management }}</div>
                    <div class="stats-label">管理兼职</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon other">
                    <el-icon><MoreFilled /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ partTimeStats.other }}</div>
                    <div class="stats-label">其他兼职</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 兼职协议列表 -->
          <el-table :data="partTimeList" style="width: 100%">
            <el-table-column prop="contractNumber" label="协议编号" width="120"  />
            <el-table-column prop="staffName" label="姓名" width="100"  />
            <el-table-column prop="idNumber" label="身份证号" width="150" show-overflow-tooltip  />
            <el-table-column prop="partTimeType" label="兼职类型" width="120">
              <template #default="scope">
                <el-tag :type="getPartTimeTypeTag(scope.row.partTimeType)" size="small">
                  {{ getPartTimeTypeText(scope.row.partTimeType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="department" label="用工部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="workContent" label="工作内容" show-overflow-tooltip  />
            <el-table-column prop="startDate" label="开始日期" width="100"  />
            <el-table-column prop="endDate" label="结束日期" width="100"  />
            <el-table-column prop="hourlyRate" label="时薪(元)" width="100"  />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusTag(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewContract(scope.row)">
                  查看
                </el-button>
                <el-button size="small" type="success" link @click="handleEditContract(scope.row)">
                  编辑
                </el-button>
                <el-button size="small" type="warning" link @click="handleRenewContract(scope.row)">
                  续签
                </el-button>
                <el-button size="small" type="danger" link @click="handleTerminateContract(scope.row)">
                  终止
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 项目合作 -->
      <el-tab-pane label="项目合作" name="project-cooperation">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">项目合作协议管理</span>
            <div class="tab-actions">
              <el-button type="success" @click="handleAddContract('PROJECT_COOPERATION')">
                <el-icon><Plus /></el-icon>
                新增协议
              </el-button>
              <el-button @click="handleImportContracts('PROJECT_COOPERATION')">
                <el-icon><Upload /></el-icon>
                批量导入
              </el-button>
              <el-button @click="handleExportContracts('PROJECT_COOPERATION')">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>

          <!-- 项目合作协议列表 -->
          <el-table :data="projectCooperationList" style="width: 100%">
            <el-table-column prop="contractNumber" label="协议编号" width="120"  />
            <el-table-column prop="projectName" label="项目名称" width="200" show-overflow-tooltip  />
            <el-table-column prop="staffName" label="合作人员" width="100"  />
            <el-table-column prop="cooperationType" label="合作类型" width="120">
              <template #default="scope">
                <el-tag size="small">{{ scope.row.cooperationType }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="department" label="负责部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="startDate" label="开始日期" width="100"  />
            <el-table-column prop="endDate" label="结束日期" width="100"  />
            <el-table-column prop="totalAmount" label="合作金额(元)" width="120"  />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusTag(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewContract(scope.row)">
                  查看
                </el-button>
                <el-button size="small" type="success" link @click="handleEditContract(scope.row)">
                  编辑
                </el-button>
                <el-button size="small" type="warning" link @click="handleRenewContract(scope.row)">
                  续签
                </el-button>
                <el-button size="small" type="danger" link @click="handleTerminateContract(scope.row)">
                  终止
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 合同详情/编辑对话框 -->
    <ExternalContractDialog
      v-model:visible="contractDialogVisible"
      :contract="currentContract"
      :contract-type="currentContractType"
      :mode="contractDialogMode"
      @success="handleContractDialogSuccess"
    />

    <!-- 派遣公司管理对话框 -->
    <DispatchCompanyDialog
      v-model:visible="companyDialogVisible"
      :company="currentCompany"
      :mode="companyDialogMode"
      @success="handleCompanyDialogSuccess"
    />

    <!-- 批量导入对话框 -->
    <ContractImportDialog
      v-model:visible="importDialogVisible"
      :contract-type="currentContractType"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  Document,
  Check,
  Warning,
  Star,
  Reading,
  DataAnalysis,
  Management,
  MoreFilled
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'

// 组件引入（需要创建）
// import ExternalContractDialog from './components/ExternalContractDialog.vue'
// import DispatchCompanyDialog from './components/DispatchCompanyDialog.vue'
// import ContractImportDialog from './components/ContractImportDialog.vue'

// 响应式数据
const activeTab = ref('labor-agreement')

// 筛选条件
const laborAgreementFilter = reactive({
  keyword: '',
  status: '',
  department: '',
  dateRange: []
})

// 统计数据
const laborAgreementStats = reactive({
  total: 156,
  active: 128,
  expiring: 18,
  thisMonth: 12
})

const partTimeStats = reactive({
  teaching: 45,
  research: 23,
  management: 15,
  other: 8
})

// 派遣公司列表
const dispatchCompanies = ref([
  {
    id: '1',
    name: 'HrHr杭州人力资源服务有限公司',
    contact: '张经理',
    phone: '0571-12345678',
    staffCount: 25
  },
  {
    id: '2',
    name: '浙江劳务派遣集团',
    contact: '李经理',
    phone: '0571-87654321',
    staffCount: 18
  }
])

// 合同列表数据
const laborAgreementList = ref([
  {
    id: '1',
    contractNumber: 'LA2025001',
    staffName: '张三',
    idNumber: '330102199001011234',
    department: '后勤服务中心',
    position: '保洁员',
    startDate: '2025-01-01',
    endDate: '2025-12-31',
    remainingDays: 195,
    monthlyFee: 3500,
    status: 'ACTIVE'
  },
  {
    id: '2',
    contractNumber: 'LA2025002',
    staffName: '李四',
    idNumber: '330102199002021234',
    department: '食堂管理中心',
    position: '厨师',
    startDate: '2025-03-01',
    endDate: '2025-08-31',
    remainingDays: 73,
    monthlyFee: 4500,
    status: 'EXPIRING'
  }
])

const laborDispatchList = ref([
  {
    id: '1',
    contractNumber: 'LD2025001',
    staffName: '王五',
    idNumber: '330102199003031234',
    dispatchCompany: '杭州人力资源服务有限公司',
    department: '信息技术中心',
    position: '网络管理员',
    startDate: '2025-01-01',
    endDate: '2025-12-31',
    monthlyFee: 6500,
    status: 'ACTIVE'
  }
])

const partTimeList = ref([
  {
    id: '1',
    contractNumber: 'PT2025001',
    staffName: '赵六',
    idNumber: '330102199004041234',
    partTimeType: 'TEACHING',
    department: '计算机学院',
    workContent: '《数据结构》课程教学',
    startDate: '2025-03-01',
    endDate: '2025-07-31',
    hourlyRate: 150,
    status: 'ACTIVE'
  }
])

const projectCooperationList = ref([
  {
    id: '1',
    contractNumber: 'PC2025001',
    projectName: '智慧校园建设项目',
    staffName: '孙七',
    cooperationType: '技术顾问',
    department: '信息技术中心',
    startDate: '2025-01-01',
    endDate: '2025-12-31',
    totalAmount: 50000,
    status: 'ACTIVE'
  }
])

// 对话框相关
const contractDialogVisible = ref(false)
const companyDialogVisible = ref(false)
const importDialogVisible = ref(false)
const contractDialogMode = ref<'view' | 'add' | 'edit'>('add')
const companyDialogMode = ref<'view' | 'add' | 'edit'>('add')
const currentContract = ref(null)
const currentCompany = ref(null)
const currentContractType = ref('')

// 部门列表
const departmentOptions = ref<any[]>([])

// 新增合同
const handleAddContract = (contractType: string) => {
  currentContract.value = null
  currentContractType.value = contractType
  contractDialogMode.value = 'add'
  contractDialogVisible.value = true
}

// 查看合同
   
const handleViewContract = (contract: unknown) => {
  currentContract.value = contract
  contractDialogMode.value = 'view'
  contractDialogVisible.value = true
}

// 编辑合同
   
const handleEditContract = (contract: unknown) => {
  currentContract.value = contract
  contractDialogMode.value = 'edit'
  contractDialogVisible.value = true
}

// 续签合同
   
const handleRenewContract = (contract: unknown) => {
  ElMessage.info(`为 ${contract.staffName} 办理合同续签`)
}

// 终止合同
   
const handleTerminateContract = async (contract: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要终止 "${contract.staffName}" 的合同吗？`,
      '确认终止',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('合同终止成功')
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('合同终止失败')
    }
  }
}

// 批量导入
const handleImportContracts = (contractType: string) => {
  currentContractType.value = contractType
  importDialogVisible.value = true
}

// 导出数据
const handleExportContracts = (contractType: string) => {
  ElMessage.info(`导出${getContractTypeText(contractType)}数据功能开发中...`)
}

// 筛选劳务协议
const handleFilterLaborAgreement = () => {
  ElMessage.info('筛选劳务协议功能开发中...')
}

// 重置筛选
const handleResetLaborAgreementFilter = () => {
  Object.assign(laborAgreementFilter, {
    keyword: '',
    status: '',
    department: '',
    dateRange: []
  })
}

// 劳务协议选择变化
   
const handleLaborAgreementSelectionChange = (selection: unknown[]) => {
  // 处理选择变化
}

// 查看公司合同
   
const handleViewCompanyContracts = (company: unknown) => {
  ElMessage.info(`查看 ${company.name} 的所有合同`)
}

// 编辑派遣公司
   
const handleEditCompany = (company: unknown) => {
  currentCompany.value = company
  companyDialogMode.value = 'edit'
  companyDialogVisible.value = true
}

// 添加派遣公司
const handleAddDispatchCompany = () => {
  currentCompany.value = null
  companyDialogMode.value = 'add'
  companyDialogVisible.value = true
}

// 对话框成功回调
const handleContractDialogSuccess = () => {
  ElMessage.success('操作成功')
  // 刷新对应的列表数据
}

const handleCompanyDialogSuccess = () => {
  ElMessage.success('操作成功')
  // 刷新派遣公司列表
}

const handleImportSuccess = () => {
  ElMessage.success('导入成功')
  // 刷新对应的列表数据
}

// 获取剩余天数标签
const getRemainingDaysTag = (days: number) => {
  if (days <= 30) return 'danger'
  if (days <= 90) return 'warning'
  return 'success'
}

// 获取状态标签
const getStatusTag = (status: string) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'EXPIRING': return 'warning'
    case 'EXPIRED': return 'danger'
    case 'TERMINATED': return 'info'
    default: return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'ACTIVE': return '有效'
    case 'EXPIRING': return '即将到期'
    case 'EXPIRED': return '已到期'
    case 'TERMINATED': return '已终止'
    default: return status
  }
}

// 获取兼职类型标签
const getPartTimeTypeTag = (type: string) => {
  switch (type) {
    case 'TEACHING': return 'primary'
    case 'RESEARCH': return 'success'
    case 'MANAGEMENT': return 'warning'
    case 'OTHER': return 'info'
    default: return ''
  }
}

// 获取兼职类型文本
const getPartTimeTypeText = (type: string) => {
  switch (type) {
    case 'TEACHING': return '教学兼职'
    case 'RESEARCH': return '科研兼职'
    case 'MANAGEMENT': return '管理兼职'
    case 'OTHER': return '其他兼职'
    default: return type
  }
}

// 获取合同类型文本
const getContractTypeText = (type: string) => {
  switch (type) {
    case 'LABOR_AGREEMENT': return '劳务协议'
    case 'LABOR_DISPATCH': return '劳务派遣合同'
    case 'PART_TIME': return '兼职协议'
    case 'PROJECT_COOPERATION': return '项目合作协议'
    default: return type
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  // 初始化数据
  fetchDepartments()
})
</script>

<style scoped>
.external-staff-contract-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.function-tabs {
  margin-top: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tab-actions {
  display: flex;
  gap: 8px;
}

.filter-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stats-row,
.part-time-stats {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.expiring {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.thisMonth {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.teaching {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.research {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.management {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.other {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.dispatch-companies {
  margin-bottom: 20px;
}

.dispatch-companies h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.company-card {
  height: 140px;
  cursor: pointer;
  transition: all 0.3s;
}

.company-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.company-info h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.company-info p {
  margin: 0 0 4px 0;
  color: #606266;
  font-size: 12px;
}

.company-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.add-company-card {
  height: 140px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px dashed #dcdfe6;
}

.add-company-card:hover {
  border-color: #409eff;
  transform: translateY(-2px);
}

.add-company-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.add-icon {
  font-size: 32px;
  margin-bottom: 8px;
}
</style>