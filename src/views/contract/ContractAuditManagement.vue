<template>
  <div class="contract-audit-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同审计管理</h2>
      <p>全面监控合同管理过程，确保合规性和风险防控</p>
    </div>

    <!-- 审计控制台 -->
    <el-card class="audit-console" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>审计控制台</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleStartAudit">
              <el-icon><Search /></el-icon>
              开始审计
            </el-button>
          </div>
        </div>
      </template>

      <!-- 审计配置 -->
      <div class="audit-config">
        <el-form :inline="true" :model="auditConfig" size="small">
          <el-form-item label="审计类型">
            <el-select v-model="auditConfig.auditType" placeholder="请选择审计类型">
              <el-option label="全面审计" value="comprehensive"  />
              <el-option label="合规性审计" value="compliance"  />
              <el-option label="程序性审计" value="procedural"  />
              <el-option label="时效性审计" value="timeliness"  />
            </el-select>
          </el-form-item>
          <el-form-item label="审计范围">
            <el-select v-model="auditConfig.auditScope" placeholder="请选择审计范围">
              <el-option label="全部合同" value="all"  />
              <el-option label="新签合同" value="new"  />
              <el-option label="续签合同" value="renewal"  />
              <el-option label="变更合同" value="modified"  />
              <el-option label="终止合同" value="terminated"  />
            </el-select>
          </el-form-item>
          <el-form-item label="审计周期">
            <el-select v-model="auditConfig.auditPeriod" placeholder="请选择审计周期">
              <el-option label="实时审计" value="realtime"  />
              <el-option label="每日审计" value="daily"  />
              <el-option label="每周审计" value="weekly"  />
              <el-option label="每月审计" value="monthly"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleRunAudit">
              <el-icon><Operation /></el-icon>
              执行审计
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 审计统计 -->
      <div class="audit-stats">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ auditStats.total }}</div>
                <div class="stat-label">审计总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card passed">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ auditStats.passed }}</div>
                <div class="stat-label">审计通过</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card warning">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ auditStats.warning }}</div>
                <div class="stat-label">审计预警</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card failed">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ auditStats.failed }}</div>
                <div class="stat-label">审计失败</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card risk">
              <div class="stat-icon">
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ auditStats.risk }}</div>
                <div class="stat-label">风险项目</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card compliance">
              <div class="stat-icon">
                <el-icon><Medal /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ auditStats.complianceRate }}%</div>
                <div class="stat-label">合规率</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 审计进度 -->
      <div class="audit-progress">
        <h4>审计进度</h4>
        <div class="progress-container">
          <el-progress
            :percentage="auditProgress.percentage"
            :status="auditProgress.status"
            :stroke-width="20"
            :show-text="true"
           />
          <div class="progress-info">
            <span>{{ auditProgress.current }} / {{ auditProgress.total }}</span>
            <span class="progress-status">{{ auditProgress.statusText }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 审计日志 -->
    <el-card class="audit-logs" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>审计日志</h3>
          <div class="header-actions">
            <el-button @click="handleExportLogs">
              <el-icon><Download /></el-icon>
              导出日志
            </el-button>
            <el-button type="primary" @click="handleLogConfig">
              <el-icon><Setting /></el-icon>
              日志配置
            </el-button>
          </div>
        </div>
      </template>

      <!-- 日志筛选 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="日志类型">
            <el-select v-model="filterForm.logType" placeholder="请选择日志类型" clearable>
              <el-option label="操作日志" value="operation"  />
              <el-option label="审计日志" value="audit"  />
              <el-option label="异常日志" value="error"  />
              <el-option label="安全日志" value="security"  />
            </el-select>
          </el-form-item>
          <el-form-item label="日志级别">
            <el-select v-model="filterForm.logLevel" placeholder="请选择日志级别" clearable>
              <el-option label="INFO" value="info"  />
              <el-option label="WARN" value="warn"  />
              <el-option label="ERROR" value="error"  />
              <el-option label="FATAL" value="fatal"  />
            </el-select>
          </el-form-item>
          <el-form-item label="操作人">
            <el-input 
              v-model="filterForm.operator" 
              placeholder="请输入操作人"
              clearable
              />
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.timeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
             />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 日志表格 -->
      <el-table :data="auditLogs" style="width: 100%">
        <el-table-column prop="timestamp" label="时间" width="160"  />
        <el-table-column prop="logType" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getLogTypeColor(scope.row.logType)" size="small">
              {{ getLogTypeText(scope.row.logType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="logLevel" label="级别" width="80">
          <template #default="scope">
            <el-tag :type="getLogLevelColor(scope.row.logLevel)" size="small">
              {{ scope.row.logLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="操作人" width="100"  />
        <el-table-column prop="operation" label="操作" width="120"  />
        <el-table-column prop="contractNumber" label="合同编号" width="130"  />
        <el-table-column prop="ipAddress" label="IP地址" width="120"  />
        <el-table-column prop="result" label="结果" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.result === 'success' ? 'success' : 'danger'" size="small">
              {{ scope.row.result === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip  />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" @click="handleViewDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 审计报告 -->
    <el-card class="audit-reports" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>审计报告</h3>
          <div class="header-actions">
            <el-button @click="handleGenerateReport">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
            <el-button type="primary" @click="handleReportConfig">
              <el-icon><Setting /></el-icon>
              报告配置
            </el-button>
          </div>
        </div>
      </template>

      <!-- 报告列表 -->
      <el-table :data="auditReports" style="width: 100%">
        <el-table-column prop="reportName" label="报告名称"  />
        <el-table-column prop="reportType" label="报告类型" width="120">
          <template #default="scope">
            <el-tag :type="getReportTypeColor(scope.row.reportType)" size="small">
              {{ getReportTypeText(scope.row.reportType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reportPeriod" label="报告周期" width="120"  />
        <el-table-column prop="generateTime" label="生成时间" width="150"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getReportStatusColor(scope.row.status)" size="small">
              {{ getReportStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小" width="100"  />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleViewReport(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="primary" @click="handleDownloadReport(scope.row)">
              下载
            </el-button>
            <el-button size="small" type="warning" @click="handleShareReport(scope.row)">
              分享
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="审计日志详情" width="800px">
      <div class="log-detail" v-if="currentLogDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志ID">{{ currentLogDetail.logId }}</el-descriptions-item>
          <el-descriptions-item label="时间">{{ currentLogDetail.timestamp }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="getLogTypeColor(currentLogDetail.logType)" size="small">
              {{ getLogTypeText(currentLogDetail.logType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag :type="getLogLevelColor(currentLogDetail.logLevel)" size="small">
              {{ currentLogDetail.logLevel }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作人">{{ currentLogDetail.operator }}</el-descriptions-item>
          <el-descriptions-item label="操作">{{ currentLogDetail.operation }}</el-descriptions-item>
          <el-descriptions-item label="合同编号">{{ currentLogDetail.contractNumber }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ currentLogDetail.ipAddress }}</el-descriptions-item>
          <el-descriptions-item label="用户代理">{{ currentLogDetail.userAgent }}</el-descriptions-item>
          <el-descriptions-item label="会话ID">{{ currentLogDetail.sessionId }}</el-descriptions-item>
          <el-descriptions-item label="结果">
            <el-tag :type="currentLogDetail.result === 'success' ? 'success' : 'danger'" size="small">
              {{ currentLogDetail.result === 'success' ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行时间">{{ currentLogDetail.executionTime }}ms</el-descriptions-item>
        </el-descriptions>

        <div class="log-content">
          <h4>详细信息</h4>
          <el-input
            v-model="currentLogDetail.description"
            type="textarea"
            :rows="4"
            readonly
            />
        </div>

        <div class="log-context" v-if="currentLogDetail.context">
          <h4>上下文信息</h4>
          <el-input
            v-model="currentLogDetail.context"
            type="textarea"
            :rows="6"
            readonly
            />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractAuditManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Search,
  Operation,
  Download,
  Setting,
  Document,
  CircleCheck,
  Warning,
  CircleClose,
  InfoFilled,
  Medal
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const detailDialogVisible = ref(false)
const currentLogDetail = ref(null)

// 审计配置
const auditConfig = reactive({
  auditType: 'comprehensive',
  auditScope: 'all',
  auditPeriod: 'daily'
})

// 审计统计
const auditStats = reactive({
  total: 1256,
  passed: 1089,
  warning: 124,
  failed: 43,
  risk: 67,
  complianceRate: 87
})

// 审计进度
const auditProgress = reactive({
  percentage: 85,
  status: 'active',
  current: 1068,
  total: 1256,
  statusText: '审计中...'
})

// 筛选表单
const filterForm = reactive({
  logType: '',
  logLevel: '',
  operator: '',
  timeRange: []
})

// 审计日志数据
const auditLogs = ref([
  {
    logId: 'LOG2025001',
    timestamp: '2025-01-23 14:30:15',
    logType: 'operation',
    logLevel: 'INFO',
    operator: '张三',
    operation: '合同签署',
    contractNumber: 'HKY2025001',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 Chrome/96.0',
    sessionId: 'SES123456',
    result: 'success',
    executionTime: 250,
    description: '用户张三成功签署合同HKY2025001',
    context: '{"userId": "U001", "role": "employee", "department": "技术部"}'
  },
  {
    logId: 'LOG2025002',
    timestamp: '2025-01-23 14:25:08',
    logType: 'audit',
    logLevel: 'WARN',
    operator: '李四',
    operation: '合同审批',
    contractNumber: 'HKY2025002',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 Chrome/96.0',
    sessionId: 'SES123457',
    result: 'success',
    executionTime: 180,
    description: '合同审批流程中发现潜在风险',
    context: '{"riskLevel": "medium", "riskType": "薪资风险"}'
  },
  {
    logId: 'LOG2025003',
    timestamp: '2025-01-23 14:20:33',
    logType: 'error',
    logLevel: 'ERROR',
    operator: '王五',
    operation: '合同变更',
    contractNumber: 'HKY2025003',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 Chrome/96.0',
    sessionId: 'SES123458',
    result: 'failed',
    executionTime: 500,
    description: '合同变更操作失败：权限不足',
    context: '{"errorCode": "AUTH_001", "errorMessage": "用户权限不足"}'
  }
])

// 审计报告数据
const auditReports = ref([
  {
    id: '1',
    reportName: '2025年1月合同审计报告',
    reportType: 'monthly',
    reportPeriod: '2025-01',
    generateTime: '2025-01-23 10:00:00',
    status: 'completed',
    fileSize: '2.5MB'
  },
  {
    id: '2',
    reportName: '合同合规性专项审计报告',
    reportType: 'compliance',
    reportPeriod: '2025-Q1',
    generateTime: '2025-01-22 15:30:00',
    status: 'processing',
    fileSize: '1.8MB'
  },
  {
    id: '3',
    reportName: '合同风险评估报告',
    reportType: 'risk',
    reportPeriod: '2025-01-15 至 2025-01-22',
    generateTime: '2025-01-21 09:15:00',
    status: 'completed',
    fileSize: '3.2MB'
  }
])

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleStartAudit = () => {
  auditProgress.percentage = 0
  auditProgress.status = 'active'
  auditProgress.statusText = '开始审计...'
  
  // 模拟审计进度
  const timer = setInterval(() => {
    auditProgress.percentage += 5
    if (auditProgress.percentage >= 100) {
      auditProgress.percentage = 100
      auditProgress.status = 'success'
      auditProgress.statusText = '审计完成'
      clearInterval(timer)
      ElMessage.success('审计任务完成')
    }
  }, 200)
}

const handleRunAudit = () => {
  ElMessage.success('审计任务已启动')
}

const handleExportLogs = () => {
  ElMessage.success('导出审计日志')
}

const handleLogConfig = () => {
  ElMessage.success('日志配置')
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    logType: '',
    logLevel: '',
    operator: '',
    timeRange: []
  })
  ElMessage.success('筛选条件已重置')
}

   
const handleViewDetail = (row: unknown) => {
  currentLogDetail.value = row
  detailDialogVisible.value = true
}

const handleGenerateReport = () => {
  ElMessage.success('生成审计报告')
}

const handleReportConfig = () => {
  ElMessage.success('报告配置')
}

   
const handleViewReport = (row: unknown) => {
  ElMessage.success(`查看报告: ${row.reportName}`)
}

   
const handleDownloadReport = (row: unknown) => {
  ElMessage.success(`下载报告: ${row.reportName}`)
}

   
const handleShareReport = (row: unknown) => {
  ElMessage.success(`分享报告: ${row.reportName}`)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

const getLogTypeColor = (type: string) => {
  const colorMap = {
    'operation': 'primary',
    'audit': 'success',
    'error': 'danger',
    'security': 'warning'
  }
  return colorMap[type] || 'info'
}

const getLogTypeText = (type: string) => {
  const textMap = {
    'operation': '操作日志',
    'audit': '审计日志',
    'error': '异常日志',
    'security': '安全日志'
  }
  return textMap[type] || '未知'
}

const getLogLevelColor = (level: string) => {
  const colorMap = {
    'INFO': 'primary',
    'WARN': 'warning',
    'ERROR': 'danger',
    'FATAL': 'danger'
  }
  return colorMap[level] || 'info'
}

const getReportTypeColor = (type: string) => {
  const colorMap = {
    'monthly': 'primary',
    'compliance': 'success',
    'risk': 'warning',
    'performance': 'info'
  }
  return colorMap[type] || 'info'
}

const getReportTypeText = (type: string) => {
  const textMap = {
    'monthly': '月度报告',
    'compliance': '合规报告',
    'risk': '风险报告',
    'performance': '绩效报告'
  }
  return textMap[type] || '未知'
}

const getReportStatusColor = (status: string) => {
  const colorMap = {
    'completed': 'success',
    'processing': 'primary',
    'failed': 'danger',
    'pending': 'warning'
  }
  return colorMap[status] || 'info'
}

const getReportStatusText = (status: string) => {
  const textMap = {
    'completed': '已完成',
    'processing': '生成中',
    'failed': '失败',
    'pending': '待生成'
  }
  return textMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  total.value = auditLogs.value.length
})
</script>

<style scoped>
.contract-audit-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.audit-console,
.audit-logs,
.audit-reports {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.audit-config {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.audit-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.passed .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.failed .stat-icon {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stat-card.risk .stat-icon {
  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
}

.stat-card.compliance .stat-icon {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.audit-progress {
  margin-top: 20px;
}

.audit-progress h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.progress-status {
  color: #909399;
  font-size: 12px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.log-detail {
  margin-top: 20px;
}

.log-content {
  margin-top: 30px;
}

.log-content h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.log-context {
  margin-top: 30px;
}

.log-context h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>