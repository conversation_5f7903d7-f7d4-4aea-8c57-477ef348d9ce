<template>
  <div class="contract-termination-flow">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同解除流程</h2>
      <p>管理合同解除全流程，包括解除申请、审批、手续办理和档案归档</p>
    </div>

    <!-- 解除申请列表 -->
    <el-card class="termination-list" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>解除申请列表</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleStartTermination">
              <el-icon><Plus /></el-icon>
              发起解除
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="员工姓名">
            <el-input 
              v-model="filterForm.employeeName" 
              placeholder="请输入员工姓名"
              clearable
              />
          </el-form-item>
          <el-form-item label="解除类型">
            <el-select v-model="filterForm.terminationType" placeholder="请选择解除类型" clearable>
              <el-option label="员工主动离职" value="resignation"  />
              <el-option label="单位解除" value="dismissal"  />
              <el-option label="协商解除" value="mutual_agreement"  />
              <el-option label="合同到期" value="expiration"  />
              <el-option label="试用期解除" value="probation_end"  />
            </el-select>
          </el-form-item>
          <el-form-item label="解除状态">
            <el-select v-model="filterForm.terminationStatus" placeholder="请选择状态" clearable>
              <el-option label="待审批" value="pending"  />
              <el-option label="审批中" value="approving"  />
              <el-option label="已通过" value="approved"  />
              <el-option label="已拒绝" value="rejected"  />
              <el-option label="办理中" value="processing"  />
              <el-option label="已完成" value="completed"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 解除统计 -->
      <div class="termination-stats">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card resignation">
              <div class="stat-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ terminationStats.resignation }}</div>
                <div class="stat-label">主动离职</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card dismissal">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ terminationStats.dismissal }}</div>
                <div class="stat-label">单位解除</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card mutual">
              <div class="stat-icon">
                <el-icon><Handshake /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ terminationStats.mutual }}</div>
                <div class="stat-label">协商解除</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card expiration">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ terminationStats.expiration }}</div>
                <div class="stat-label">合同到期</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card processing">
              <div class="stat-icon">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ terminationStats.processing }}</div>
                <div class="stat-label">办理中</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card completed">
              <div class="stat-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ terminationStats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 解除表格 -->
      <el-table :data="terminationList" style="width: 100%">
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="contractNumber" label="合同编号" width="150"  />
        <el-table-column prop="terminationType" label="解除类型" width="120">
          <template #default="scope">
            <el-tag :type="getTerminationTypeColor(scope.row.terminationType)" size="small">
              {{ getTerminationTypeText(scope.row.terminationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="terminationStatus" label="解除状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.terminationStatus)" size="small">
              {{ getStatusText(scope.row.terminationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申请人" width="100"  />
        <el-table-column prop="applicationDate" label="申请时间" width="150"  />
        <el-table-column prop="terminationDate" label="解除日期" width="120"  />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleViewTermination(scope.row)">
              查看
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleProcessTermination(scope.row)"
              v-if="scope.row.terminationStatus === 'pending'"
            >
              处理
            </el-button>
            <el-button 
              size="small" 
              type="success" 
              @click="handleApproveTermination(scope.row)"
              v-if="scope.row.terminationStatus === 'approving'"
            >
              审批
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleExecuteTermination(scope.row)"
              v-if="scope.row.terminationStatus === 'approved'"
            >
              执行
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 解除申请对话框 -->
    <el-dialog v-model="terminationDialogVisible" title="合同解除申请" width="900px">
      <div class="termination-form">
        <el-form ref="terminationFormRef" :model="terminationForm" :rules="terminationRules" label-width="120px">
          <el-tabs v-model="activeTerminationTab" type="card">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="员工姓名" prop="employeeName">
                    <el-input v-model="terminationForm.employeeName" placeholder="请输入员工姓名"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同编号" prop="contractNumber">
                    <el-input v-model="terminationForm.contractNumber" placeholder="请输入合同编号"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="解除类型" prop="terminationType">
                    <el-select v-model="terminationForm.terminationType" placeholder="请选择解除类型">
                      <el-option label="员工主动离职" value="resignation"  />
                      <el-option label="单位解除" value="dismissal"  />
                      <el-option label="协商解除" value="mutual_agreement"  />
                      <el-option label="合同到期" value="expiration"  />
                      <el-option label="试用期解除" value="probation_end"  />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="解除日期" prop="terminationDate">
                    <el-date-picker
                      v-model="terminationForm.terminationDate"
                      type="date"
                      placeholder="选择解除日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                     />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="解除原因" prop="terminationReason">
                    <el-select v-model="terminationForm.terminationReason" placeholder="请选择解除原因">
                      <el-option label="个人发展" value="personal_development"  />
                      <el-option label="工作调整" value="work_adjustment"  />
                      <el-option label="违反纪律" value="discipline_violation"  />
                      <el-option label="能力不符" value="incompetence"  />
                      <el-option label="经济性裁员" value="economic_layoff"  />
                      <el-option label="其他原因" value="other"  />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否支付补偿" prop="hasCompensation">
                    <el-radio-group v-model="terminationForm.hasCompensation">
                      <el-radio value="yes">是</el-radio>
                      <el-radio value="no">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 补偿信息 -->
            <el-tab-pane label="补偿信息" name="compensation" v-if="terminationForm.hasCompensation === 'yes'">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="经济补偿金" prop="economicCompensation">
                    <el-input-number
                      v-model="terminationForm.economicCompensation"
                      :min="0"
                      :precision="2"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="代通知金" prop="noticePayment">
                    <el-input-number
                      v-model="terminationForm.noticePayment"
                      :min="0"
                      :precision="2"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="未休年假补偿" prop="vacationCompensation">
                    <el-input-number
                      v-model="terminationForm.vacationCompensation"
                      :min="0"
                      :precision="2"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="其他补偿" prop="otherCompensation">
                    <el-input-number
                      v-model="terminationForm.otherCompensation"
                      :min="0"
                      :precision="2"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="补偿总额">
                    <el-input :value="totalCompensation" disabled>
                      <template #append>元</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 手续办理 -->
            <el-tab-pane label="手续办理" name="procedures">
              <div class="procedures-checklist">
                <h4>离职手续清单</h4>
                <el-checkbox-group v-model="terminationForm.procedures">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-checkbox value="work_handover">工作交接</el-checkbox>
                    </el-col>
                    <el-col :span="8">
                      <el-checkbox value="equipment_return">设备归还</el-checkbox>
                    </el-col>
                    <el-col :span="8">
                      <el-checkbox value="documents_return">证件归还</el-checkbox>
                    </el-col>
                    <el-col :span="8">
                      <el-checkbox value="salary_settlement">薪资结算</el-checkbox>
                    </el-col>
                    <el-col :span="8">
                      <el-checkbox value="social_security">社保转移</el-checkbox>
                    </el-col>
                    <el-col :span="8">
                      <el-checkbox value="archive_handling">档案处理</el-checkbox>
                    </el-col>
                    <el-col :span="8">
                      <el-checkbox value="certificate_issue">证明开具</el-checkbox>
                    </el-col>
                    <el-col :span="8">
                      <el-checkbox value="tax_settlement">个税清算</el-checkbox>
                    </el-col>
                    <el-col :span="8">
                      <el-checkbox value="system_access">系统权限</el-checkbox>
                    </el-col>
                  </el-row>
                </el-checkbox-group>
              </div>
              
              <div class="procedure-details">
                <h4>手续办理详情</h4>
                <el-table :data="procedureList" style="width: 100%">
                  <el-table-column prop="procedure" label="手续项目" width="150"  />
                  <el-table-column prop="responsible" label="负责人" width="120"  />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === 'completed' ? 'success' : 'info'" size="small">
                        {{ scope.row.status === 'completed' ? '已完成' : '待办理' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="deadline" label="截止时间" width="120"  />
                  <el-table-column prop="notes" label="备注" show-overflow-tooltip  />
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button size="small" @click="handleCompleteProcedure(scope.row)">
                        {{ scope.row.status === 'completed' ? '查看' : '完成' }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 审批信息 -->
            <el-tab-pane label="审批信息" name="approval">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="审批人" prop="approver">
                    <el-select v-model="terminationForm.approver" placeholder="请选择审批人">
                      <el-option label="直接上级" value="direct_supervisor"  />
                      <el-option label="部门经理" value="dept_manager"  />
                      <el-option label="人事经理" value="hr_manager"  />
                      <el-option label="总经理" value="general_manager"  />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="抄送人" prop="ccRecipients">
                    <el-select v-model="terminationForm.ccRecipients" multiple placeholder="请选择抄送人">
                      <el-option label="人事专员" value="hr_specialist"  />
                      <el-option label="财务经理" value="finance_manager"  />
                      <el-option label="法务专员" value="legal_specialist"  />
                      <el-option label="IT管理员" value="it_admin"  />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="申请说明" prop="applicationDescription">
                    <el-input
                      v-model="terminationForm.applicationDescription"
                      type="textarea"
                      :rows="4"
                      placeholder="请详细说明解除申请的原因和相关情况"
                      />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 附件材料 -->
            <el-tab-pane label="附件材料" name="attachments">
              <el-form-item label="附件上传">
                <el-upload
                  ref="uploadRef"
                  :file-list="terminationForm.attachments"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :before-upload="() => false"
                  multiple
                  drag
                >
                  <el-icon class="el-icon--upload">
                    <UploadFilled />
                  </el-icon>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持上传离职申请表、证明文件等，单个文件大小不超过10MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="terminationDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitTermination">提交申请</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 解除详情对话框 -->
    <el-dialog v-model="terminationDetailDialogVisible" title="解除详情" width="800px">
      <div class="termination-detail" v-if="currentTermination">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="员工姓名">{{ currentTermination.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="合同编号">{{ currentTermination.contractNumber }}</el-descriptions-item>
          <el-descriptions-item label="解除类型">
            <el-tag :type="getTerminationTypeColor(currentTermination.terminationType)" size="small">
              {{ getTerminationTypeText(currentTermination.terminationType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="解除状态">
            <el-tag :type="getStatusType(currentTermination.terminationStatus)" size="small">
              {{ getStatusText(currentTermination.terminationStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentTermination.applicant }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ currentTermination.applicationDate }}</el-descriptions-item>
          <el-descriptions-item label="解除日期">{{ currentTermination.terminationDate }}</el-descriptions-item>
          <el-descriptions-item label="解除原因">{{ currentTermination.terminationReason }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="termination-timeline">
          <h4>解除进度</h4>
          <el-timeline>
            <el-timeline-item
              v-for="milestone in terminationMilestones"
              :key="milestone.id"
              :timestamp="milestone.timestamp"
              :type="milestone.type"
            >
              <div class="milestone-content">
                <div class="milestone-title">{{ milestone.title }}</div>
                <div class="milestone-description">{{ milestone.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog v-model="approvalDialogVisible" title="解除审批" width="600px">
      <div class="approval-form">
        <el-form ref="approvalFormRef" :model="approvalForm" :rules="approvalRules" label-width="100px">
          <el-form-item label="审批结果" prop="result">
            <el-radio-group v-model="approvalForm.result">
              <el-radio value="approved">通过</el-radio>
              <el-radio value="rejected">拒绝</el-radio>
              <el-radio value="pending">待定</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批意见" prop="comment">
            <el-input
              v-model="approvalForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入审批意见"
              />
          </el-form-item>
          <el-form-item label="审批人" prop="approver">
            <el-input v-model="approvalForm.approver" placeholder="请输入审批人姓名"   />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approvalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitApproval">提交审批</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractTerminationFlow'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Plus,
  Search,
  User,
  Warning,
  Handshake,
  Clock,
  Loading,
  Check,
  UploadFilled
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const terminationDialogVisible = ref(false)
const terminationDetailDialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const activeTerminationTab = ref('basic')
const terminationFormRef = ref()
const approvalFormRef = ref()
const uploadRef = ref()
const currentTermination = ref(null)

// 筛选表单
const filterForm = reactive({
  employeeName: '',
  terminationType: '',
  terminationStatus: ''
})

// 解除统计
const terminationStats = reactive({
  resignation: 15,
  dismissal: 3,
  mutual: 8,
  expiration: 12,
  processing: 5,
  completed: 28
})

// 解除申请表单
const terminationForm = reactive({
  employeeName: '',
  contractNumber: '',
  terminationType: '',
  terminationDate: '',
  terminationReason: '',
  hasCompensation: 'no',
  economicCompensation: 0,
  noticePayment: 0,
  vacationCompensation: 0,
  otherCompensation: 0,
  procedures: [],
  approver: '',
  ccRecipients: [],
  applicationDescription: '',
  attachments: []
})

// 解除申请验证规则
const terminationRules = {
  employeeName: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' }
  ],
  contractNumber: [
    { required: true, message: '请输入合同编号', trigger: 'blur' }
  ],
  terminationType: [
    { required: true, message: '请选择解除类型', trigger: 'change' }
  ],
  terminationDate: [
    { required: true, message: '请选择解除日期', trigger: 'change' }
  ],
  terminationReason: [
    { required: true, message: '请选择解除原因', trigger: 'change' }
  ],
  applicationDescription: [
    { required: true, message: '请输入申请说明', trigger: 'blur' }
  ]
}

// 手续办理列表
const procedureList = ref([
  {
    procedure: '工作交接',
    responsible: '直接上级',
    status: 'pending',
    deadline: '2025-02-01',
    notes: '完成所有在手项目的交接'
  },
  {
    procedure: '设备归还',
    responsible: 'IT管理员',
    status: 'pending',
    deadline: '2025-02-01',
    notes: '归还电脑、门禁卡等设备'
  },
  {
    procedure: '薪资结算',
    responsible: '财务专员',
    status: 'pending',
    deadline: '2025-02-05',
    notes: '结算最后一个月工资和补偿'
  },
  {
    procedure: '社保转移',
    responsible: '人事专员',
    status: 'pending',
    deadline: '2025-02-10',
    notes: '办理社保转移手续'
  }
])

// 审批表单
const approvalForm = reactive({
  result: 'approved',
  comment: '',
  approver: ''
})

// 审批验证规则
const approvalRules = {
  result: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' }
  ],
  approver: [
    { required: true, message: '请输入审批人姓名', trigger: 'blur' }
  ]
}

// 解除列表数据
const terminationList = ref([
  {
    id: '1',
    employeeName: '张三',
    contractNumber: 'HKY2025001',
    terminationType: 'resignation',
    terminationStatus: 'pending',
    applicant: '员工本人',
    applicationDate: '2025-01-23 09:00:00',
    terminationDate: '2025-02-01',
    terminationReason: 'personal_development'
  },
  {
    id: '2',
    employeeName: '李四',
    contractNumber: 'HKY2025002',
    terminationType: 'dismissal',
    terminationStatus: 'approving',
    applicant: '人事经理',
    applicationDate: '2025-01-22 14:30:00',
    terminationDate: '2025-01-31',
    terminationReason: 'discipline_violation'
  },
  {
    id: '3',
    employeeName: '王五',
    contractNumber: 'HKY2025003',
    terminationType: 'expiration',
    terminationStatus: 'processing',
    applicant: '人事专员',
    applicationDate: '2025-01-21 10:00:00',
    terminationDate: '2025-01-31',
    terminationReason: 'contract_expiration'
  }
])

// 解除进度里程碑
const terminationMilestones = ref([
  {
    id: '1',
    timestamp: '2025-01-23 09:00:00',
    type: 'primary',
    title: '解除申请提交',
    description: '申请人提交解除申请，等待审批'
  },
  {
    id: '2',
    timestamp: '2025-01-23 11:00:00',
    type: 'success',
    title: '部门审批通过',
    description: '部门经理审批通过，提交人事审批'
  },
  {
    id: '3',
    timestamp: '2025-01-23 15:00:00',
    type: 'success',
    title: '人事审批通过',
    description: '人事部门审批通过，开始办理手续'
  },
  {
    id: '4',
    timestamp: '2025-01-24 09:00:00',
    type: 'warning',
    title: '手续办理中',
    description: '正在办理离职手续和工作交接'
  }
])

// 计算属性
const totalCompensation = computed(() => {
  const {economicCompensation: _economicCompensation, noticePayment: _noticePayment, vacationCompensation: _vacationCompensation, otherCompensation: _otherCompensation} =  terminationForm
  return (economicCompensation + noticePayment + vacationCompensation + otherCompensation).toFixed(2)
})

// 方法
const handleRefresh 
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.termination-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.termination-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.resignation .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.dismissal .stat-icon {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stat-card.mutual .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.expiration .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.processing .stat-icon {
  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
}

.stat-card.completed .stat-icon {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.termination-form {
  margin-top: 20px;
}

.procedures-checklist {
  margin-bottom: 30px;
}

.procedures-checklist h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.procedure-details {
  margin-top: 30px;
}

.procedure-details h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.termination-detail {
  margin-top: 20px;
}

.termination-timeline {
  margin-top: 30px;
}

.termination-timeline h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.milestone-content {
  padding: 8px 0;
}

.milestone-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.milestone-description {
  color: #606266;
  font-size: 14px;
}

.approval-form {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>