<template>
  <div class="contract-type-analysis">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同类型分析</h2>
      <p>分析不同合同类型的分布和趋势，提供决策支持</p>
    </div>

    <!-- 分析控制台 -->
    <el-card class="analysis-console" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>类型分析控制台</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleAnalyze">
              <el-icon><TrendCharts /></el-icon>
              开始分析
            </el-button>
          </div>
        </div>
      </template>

      <!-- 分析配置 -->
      <div class="analysis-config">
        <el-form :inline="true" :model="analysisConfig" size="small">
          <el-form-item label="分析维度">
            <el-select v-model="analysisConfig.dimension" placeholder="请选择分析维度">
              <el-option label="合同类型" value="contractType"  />
              <el-option label="部门分布" value="department"  />
              <el-option label="时间趋势" value="timeTrend"  />
              <el-option label="薪资分布" value="salaryRange"  />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-select v-model="analysisConfig.timeRange" placeholder="请选择时间范围">
              <el-option label="最近1年" value="1year"  />
              <el-option label="最近2年" value="2years"  />
              <el-option label="最近3年" value="3years"  />
              <el-option label="全部时间" value="all"  />
            </el-select>
          </el-form-item>
          <el-form-item label="部门筛选">
            <el-select v-model="analysisConfig.department" placeholder="请选择部门">
              <el-option label="全部部门" value="all"  />
              <el-option label="技术部" value="tech"  />
              <el-option label="人事部" value="hr"  />
              <el-option label="财务部" value="finance"  />
              <el-option label="市场部" value="marketing"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleRunAnalysis">
              <el-icon><Operation /></el-icon>
              执行分析
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 类型统计 -->
      <div class="type-stats">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ typeStats.total }}</div>
                <div class="stat-label">合同总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card labor">
              <div class="stat-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ typeStats.labor }}</div>
                <div class="stat-label">劳动合同</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card employment">
              <div class="stat-icon">
                <el-icon><Briefcase /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ typeStats.employment }}</div>
                <div class="stat-label">聘用合同</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card temporary">
              <div class="stat-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ typeStats.temporary }}</div>
                <div class="stat-label">临时合同</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card intern">
              <div class="stat-icon">
                <el-icon><School /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ typeStats.intern }}</div>
                <div class="stat-label">实习合同</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card growth">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ typeStats.growthRate }}%</div>
                <div class="stat-label">增长率</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 饼图统计 -->
    <el-card class="pie-chart-analysis" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>合同类型分布</h3>
          <div class="header-actions">
            <el-button @click="handleExportChart">
              <el-icon><Download /></el-icon>
              导出图表
            </el-button>
          </div>
        </div>
      </template>

      <div class="chart-container">
        <div class="chart-tabs">
          <el-tabs v-model="activeChartTab" type="card">
            <el-tab-pane label="类型分布" name="distribution">
              <div class="pie-chart-section">
                <div class="chart-main">
                  <div class="pie-chart-placeholder">
                    <div class="pie-chart-visual">
                      <svg width="300" height="300" viewBox="0 0 300 300">
                        <circle cx="150" cy="150" r="80" fill="none" stroke="#e2e8f0" stroke-width="40"/>
                        <circle cx="150" cy="150" r="80" fill="none" stroke="#4299e1" stroke-width="40" 
                                stroke-dasharray="188.4" stroke-dashoffset="37.68" transform="rotate(-90 150 150)"/>
                        <circle cx="150" cy="150" r="80" fill="none" stroke="#48bb78" stroke-width="40" 
                                stroke-dasharray="125.6" stroke-dashoffset="62.8" transform="rotate(126 150 150)"/>
                        <circle cx="150" cy="150" r="80" fill="none" stroke="#ed8936" stroke-width="40" 
                                stroke-dasharray="75.36" stroke-dashoffset="37.68" transform="rotate(216 150 150)"/>
                        <circle cx="150" cy="150" r="80" fill="none" stroke="#9f7aea" stroke-width="40" 
                                stroke-dasharray="37.68" stroke-dashoffset="18.84" transform="rotate(306 150 150)"/>
                      </svg>
                      <div class="chart-center">
                        <div class="center-title">合同类型</div>
                        <div class="center-total">{{ typeStats.total }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="chart-legend">
                  <div class="legend-item" v-for="(item, index) in pieChartData" :key="index">
                    <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                    <div class="legend-content">
                      <div class="legend-name">{{ item.name }}</div>
                      <div class="legend-stats">
                        <span class="legend-count">{{ item.count }}</span>
                        <span class="legend-percentage">{{ item.percentage }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="部门分布" name="department">
              <div class="department-distribution">
                <div class="dept-chart">
                  <div class="dept-item" v-for="(dept, index) in departmentData" :key="index">
                    <div class="dept-name">{{ dept.name }}</div>
                    <div class="dept-bars">
                      <div class="bar-item" v-for="(type, typeIndex) in dept.types" :key="typeIndex">
                        <div class="bar-label">{{ type.name }}</div>
                        <div class="bar-container">
                          <div class="bar-fill" :style="{ width: (type.count / dept.total) * 100 + '%', backgroundColor: type.color }"></div>
                        </div>
                        <div class="bar-count">{{ type.count }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="趋势分析" name="trend">
              <div class="trend-analysis">
                <div class="trend-chart">
                  <h4>合同类型趋势变化</h4>
                  <div class="trend-lines">
                    <div class="trend-line" v-for="(trend, index) in trendData" :key="index">
                      <div class="line-header">
                        <div class="line-name" :style="{ color: trend.color }">{{ trend.name }}</div>
                        <div class="line-change" :class="trend.change >= 0 ? 'positive' : 'negative'">
                          {{ trend.change >= 0 ? '+' : '' }}{{ trend.change }}%
                        </div>
                      </div>
                      <div class="line-chart">
                        <svg width="400" height="60" viewBox="0 0 400 60">
                          <polyline 
                            :points="getTrendPoints(trend.data)" 
                            fill="none" 
                            :stroke="trend.color" 
                            stroke-width="2"
                          />
                          <circle v-for="(point, pointIndex) in trend.data" :key="pointIndex"
                            :cx="pointIndex * 40 + 20" 
                            :cy="60 - (point / trend.max) * 50" 
                            r="3" 
                            :fill="trend.color"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-card>

    <!-- 详细分析 -->
    <el-card class="detailed-analysis" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>详细分析</h3>
          <div class="header-actions">
            <el-button @click="handleExportAnalysis">
              <el-icon><Download /></el-icon>
              导出分析
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="合同类型">
            <el-select v-model="filterForm.contractType" placeholder="请选择合同类型" clearable>
              <el-option label="劳动合同" value="labor"  />
              <el-option label="聘用合同" value="employment"  />
              <el-option label="临时合同" value="temporary"  />
              <el-option label="实习合同" value="intern"  />
            </el-select>
          </el-form-item>
          <el-form-item label="部门">
            <el-select v-model="filterForm.department" placeholder="请选择部门" clearable>
              <el-option label="技术部" value="tech"  />
              <el-option label="人事部" value="hr"  />
              <el-option label="财务部" value="finance"  />
              <el-option label="市场部" value="marketing"  />
            </el-select>
          </el-form-item>
          <el-form-item label="签署时间">
            <el-date-picker
              v-model="filterForm.signDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 分析表格 -->
      <el-table :data="analysisData" style="width: 100%">
        <el-table-column prop="contractType" label="合同类型" width="120">
          <template #default="scope">
            <el-tag :type="getContractTypeColor(scope.row.contractType)" size="small">
              {{ getContractTypeText(scope.row.contractType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="数量" width="80"  />
        <el-table-column prop="percentage" label="占比" width="80">
          <template #default="scope">
            <span class="percentage-text">{{ scope.row.percentage }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="avgDuration" label="平均期限" width="100"  />
        <el-table-column prop="avgSalary" label="平均薪资" width="100"  />
        <el-table-column prop="renewalRate" label="续签率" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.renewalRate"
              :color="getProgressColor(scope.row.renewalRate)"
              :show-text="false"
              :stroke-width="6"
             />
            <span class="rate-text">{{ scope.row.renewalRate }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskLevelColor(scope.row.riskLevel)" size="small">
              {{ getRiskLevelText(scope.row.riskLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="trend" label="趋势" width="80">
          <template #default="scope">
            <el-icon :class="getTrendClass(scope.row.trend)">
              <component :is="getTrendIcon(scope.row.trend)" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="recommendation" label="建议" show-overflow-tooltip  />
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  TrendCharts,
  Operation,
  Document,
  User,
  Briefcase,
  Timer,
  School,
  Download,
  Search,
  Top,
  Bottom,
  Minus
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const activeChartTab = ref('distribution')

// 分析配置
const analysisConfig = reactive({
  dimension: 'contractType',
  timeRange: '1year',
  department: 'all'
})

// 类型统计
const typeStats = reactive({
  total: 1246,
  labor: 758,
  employment: 312,
  temporary: 124,
  intern: 52,
  growthRate: 12.5
})

// 筛选表单
const filterForm = reactive({
  contractType: '',
  department: '',
  signDateRange: []
})

// 饼图数据
const pieChartData = ref([
  { name: 'HrHr劳动合同', count: 758, percentage: 60.8, color: '#4299e1' },
  { name: '聘用合同', count: 312, percentage: 25.0, color: '#48bb78' },
  { name: '临时合同', count: 124, percentage: 10.0, color: '#ed8936' },
  { name: '实习合同', count: 52, percentage: 4.2, color: '#9f7aea' }
])

// 部门数据
const departmentData = ref([
  {
    name: '技术部',
    total: 320,
    types: [
      { name: '劳动合同', count: 220, color: '#4299e1' },
      { name: '聘用合同', count: 80, color: '#48bb78' },
      { name: '临时合同', count: 15, color: '#ed8936' },
      { name: '实习合同', count: 5, color: '#9f7aea' }
    ]
  },
  {
    name: '人事部',
    total: 85,
    types: [
      { name: '劳动合同', count: 45, color: '#4299e1' },
      { name: '聘用合同', count: 35, color: '#48bb78' },
      { name: '临时合同', count: 5, color: '#ed8936' },
      { name: '实习合同', count: 0, color: '#9f7aea' }
    ]
  },
  {
    name: '财务部',
    total: 68,
    types: [
      { name: '劳动合同', count: 48, color: '#4299e1' },
      { name: '聘用合同', count: 18, color: '#48bb78' },
      { name: '临时合同', count: 2, color: '#ed8936' },
      { name: '实习合同', count: 0, color: '#9f7aea' }
    ]
  },
  {
    name: '市场部',
    total: 156,
    types: [
      { name: '劳动合同', count: 98, color: '#4299e1' },
      { name: '聘用合同', count: 42, color: '#48bb78' },
      { name: '临时合同', count: 12, color: '#ed8936' },
      { name: '实习合同', count: 4, color: '#9f7aea' }
    ]
  }
])

// 趋势数据
const trendData = ref([
  {
    name: '劳动合同',
    color: '#4299e1',
    change: 8.5,
    max: 80,
    data: [45, 52, 58, 65, 72, 78, 75, 68, 71, 76]
  },
  {
    name: '聘用合同',
    color: '#48bb78',
    change: 15.2,
    max: 35,
    data: [18, 22, 25, 28, 32, 35, 31, 28, 30, 33]
  },
  {
    name: '临时合同',
    color: '#ed8936',
    change: -5.8,
    max: 15,
    data: [12, 15, 13, 11, 9, 8, 10, 12, 11, 9]
  },
  {
    name: '实习合同',
    color: '#9f7aea',
    change: 28.6,
    max: 8,
    data: [2, 3, 4, 5, 6, 8, 7, 6, 7, 8]
  }
])

// 分析数据
const analysisData = ref([
  {
    contractType: 'labor',
    count: 758,
    percentage: 60.8,
    avgDuration: '3.2年',
    avgSalary: '12,500',
    renewalRate: 85,
    riskLevel: 'low',
    trend: 'up',
    recommendation: '主力合同类型，建议保持稳定增长'
  },
  {
    contractType: 'employment',
    count: 312,
    percentage: 25.0,
    avgDuration: '2.8年',
    avgSalary: '8,900',
    renewalRate: 72,
    riskLevel: 'medium',
    trend: 'up',
    recommendation: '续签率有提升空间，建议优化福利待遇'
  },
  {
    contractType: 'temporary',
    count: 124,
    percentage: 10.0,
    avgDuration: '0.8年',
    avgSalary: '5,200',
    renewalRate: 45,
    riskLevel: 'high',
    trend: 'down',
    recommendation: '流动性较高，建议评估项目需求'
  },
  {
    contractType: 'intern',
    count: 52,
    percentage: 4.2,
    avgDuration: '0.5年',
    avgSalary: '2,800',
    renewalRate: 65,
    riskLevel: 'medium',
    trend: 'up',
    recommendation: '人才储备重要渠道，建议增加转正机会'
  }
])

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleAnalyze = () => {
  ElMessage.success('开始分析')
}

const handleRunAnalysis = () => {
  ElMessage.success('执行分析')
}

const handleExportChart = () => {
  ElMessage.success('导出图表')
}

const handleExportAnalysis = () => {
  ElMessage.success('导出分析')
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    contractType: '',
    department: '',
    signDateRange: []
  })
  ElMessage.success('筛选条件已重置')
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

const getTrendPoints = (data: number[]) => {
  return data.map((value, index) => `${index * 40 + 20},${60 - (value / Math.max(...data)) * 50}`).join(' ')
}

const getContractTypeColor = (type: string) => {
  const colorMap = {
    'labor': 'primary',
    'employment': 'success',
    'temporary': 'warning',
    'intern': 'info'
  }
  return colorMap[type] || 'info'
}

const getContractTypeText = (type: string) => {
  const textMap = {
    'labor': '劳动合同',
    'employment': '聘用合同',
    'temporary': '临时合同',
    'intern': '实习合同'
  }
  return textMap[type] || '未知'
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getRiskLevelColor = (level: string) => {
  const colorMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return colorMap[level] || 'info'
}

const getRiskLevelText = (level: string) => {
  const textMap = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return textMap[level] || '未知'
}

const getTrendClass = (trend: string) => {
  const classMap = {
    'up': 'trend-up',
    'down': 'trend-down',
    'stable': 'trend-stable'
  }
  return classMap[trend] || 'trend-stable'
}

const getTrendIcon = (trend: string) => {
  const iconMap = {
    'up': Top,
    'down': Bottom,
    'stable': Minus
  }
  return iconMap[trend] || Minus
}

// 生命周期
onMounted(() => {
  total.value = analysisData.value.length
})
</script>

<style scoped>
.contract-type-analysis {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.analysis-console,
.pie-chart-analysis,
.detailed-analysis {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.analysis-config {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.type-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.labor .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.employment .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.temporary .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.intern .stat-icon {
  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
}

.stat-card.growth .stat-icon {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.chart-container {
  padding: 20px;
}

.pie-chart-section {
  display: flex;
  align-items: center;
  gap: 40px;
}

.chart-main {
  flex: 1;
  display: flex;
  justify-content: center;
}

.pie-chart-placeholder {
  position: relative;
  width: 300px;
  height: 300px;
}

.pie-chart-visual {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.center-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.center-total {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.legend-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.legend-name {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.legend-stats {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.legend-count {
  color: #303133;
  font-weight: 600;
}

.legend-percentage {
  color: #606266;
}

.department-distribution {
  padding: 20px;
}

.dept-chart {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dept-item {
  display: flex;
  align-items: center;
  gap: 20px;
}

.dept-name {
  width: 100px;
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.dept-bars {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bar-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bar-label {
  width: 80px;
  font-size: 12px;
  color: #606266;
}

.bar-container {
  flex: 1;
  max-width: 200px;
  height: 16px;
  background: #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.bar-count {
  width: 40px;
  font-size: 12px;
  color: #303133;
  font-weight: 600;
  text-align: right;
}

.trend-analysis {
  padding: 20px;
}

.trend-chart h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.trend-lines {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.trend-line {
  display: flex;
  align-items: center;
  gap: 20px;
}

.line-header {
  width: 120px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.line-name {
  font-size: 14px;
  font-weight: 600;
}

.line-change {
  font-size: 12px;
}

.line-change.positive {
  color: #67c23a;
}

.line-change.negative {
  color: #f56c6c;
}

.line-chart {
  flex: 1;
  max-width: 400px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.percentage-text {
  font-weight: 600;
  color: #303133;
}

.rate-text {
  margin-left: 8px;
  font-size: 12px;
  color: #606266;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.trend-stable {
  color: #909399;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>