<template>
  <div class="contract-compliance-check">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同合规检查</h2>
      <p>智能检查合同合规性，识别风险点，确保合同符合法律法规要求</p>
    </div>

    <!-- 检查控制台 -->
    <el-card class="check-console" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>合规检查控制台</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleStartCheck">
              <el-icon><Search /></el-icon>
              开始检查
            </el-button>
          </div>
        </div>
      </template>

      <!-- 检查配置 -->
      <div class="check-config">
        <el-form :inline="true" :model="checkConfig" size="small">
          <el-form-item label="检查类型">
            <el-select v-model="checkConfig.checkType" placeholder="请选择检查类型">
              <el-option label="全面检查" value="comprehensive"  />
              <el-option label="基础检查" value="basic"  />
              <el-option label="风险检查" value="risk"  />
              <el-option label="自定义检查" value="custom"  />
            </el-select>
          </el-form-item>
          <el-form-item label="检查范围">
            <el-select v-model="checkConfig.checkScope" placeholder="请选择检查范围">
              <el-option label="所有合同" value="all"  />
              <el-option label="新签合同" value="new"  />
              <el-option label="续签合同" value="renewal"  />
              <el-option label="变更合同" value="changed"  />
            </el-select>
          </el-form-item>
          <el-form-item label="检查规则">
            <el-select v-model="checkConfig.ruleSet" placeholder="请选择检查规则">
              <el-option label="标准规则" value="standard"  />
              <el-option label="严格规则" value="strict"  />
              <el-option label="宽松规则" value="loose"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleRunCheck">
              <el-icon><Play /></el-icon>
              执行检查
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 检查统计 -->
      <div class="check-stats">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ checkStats.total }}</div>
                <div class="stat-label">检查总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card passed">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ checkStats.passed }}</div>
                <div class="stat-label">通过检查</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card warning">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ checkStats.warning }}</div>
                <div class="stat-label">警告</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card error">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ checkStats.error }}</div>
                <div class="stat-label">错误</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card critical">
              <div class="stat-icon">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ checkStats.critical }}</div>
                <div class="stat-label">严重</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card rate">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ checkStats.passRate }}%</div>
                <div class="stat-label">通过率</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 检查结果列表 -->
    <el-card class="check-results" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>检查结果</h3>
          <div class="header-actions">
            <el-button @click="handleExportResults">
              <el-icon><Download /></el-icon>
              导出结果
            </el-button>
            <el-button type="primary" @click="handleBatchProcess">
              <el-icon><Operation /></el-icon>
              批量处理
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="检查状态">
            <el-select v-model="filterForm.checkStatus" placeholder="请选择状态" clearable>
              <el-option label="通过" value="passed"  />
              <el-option label="警告" value="warning"  />
              <el-option label="错误" value="error"  />
              <el-option label="严重" value="critical"  />
            </el-select>
          </el-form-item>
          <el-form-item label="风险等级">
            <el-select v-model="filterForm.riskLevel" placeholder="请选择风险等级" clearable>
              <el-option label="低风险" value="low"  />
              <el-option label="中风险" value="medium"  />
              <el-option label="高风险" value="high"  />
              <el-option label="严重风险" value="critical"  />
            </el-select>
          </el-form-item>
          <el-form-item label="检查时间">
            <el-date-picker
              v-model="filterForm.checkDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 检查结果表格 -->
      <el-table :data="checkResults" style="width: 100%">
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="contractNumber" label="合同编号" width="150"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="checkType" label="检查类型" width="100"  />
        <el-table-column prop="checkStatus" label="检查状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.checkStatus)" size="small">
              {{ getStatusText(scope.row.checkStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskLevelType(scope.row.riskLevel)" size="small">
              {{ getRiskLevelText(scope.row.riskLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="issueCount" label="问题数量" width="100"  />
        <el-table-column prop="checkTime" label="检查时间" width="150"  />
        <el-table-column prop="checkScore" label="合规评分" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.checkScore"
              :color="getScoreColor(scope.row.checkScore)"
              :show-text="true"
              :stroke-width="8"
             />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="handleViewDetail(scope.row)">
              查看详情
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleFixIssues(scope.row)"
              v-if="scope.row.checkStatus !== 'passed'"
            >
              处理问题
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 检查详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="检查详情" width="1000px">
      <div class="check-detail" v-if="currentCheckResult">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="合同编号">{{ currentCheckResult.contractNumber }}</el-descriptions-item>
          <el-descriptions-item label="员工姓名">{{ currentCheckResult.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="检查类型">{{ currentCheckResult.checkType }}</el-descriptions-item>
          <el-descriptions-item label="检查状态">
            <el-tag :type="getStatusType(currentCheckResult.checkStatus)" size="small">
              {{ getStatusText(currentCheckResult.checkStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="风险等级">
            <el-tag :type="getRiskLevelType(currentCheckResult.riskLevel)" size="small">
              {{ getRiskLevelText(currentCheckResult.riskLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="合规评分">
            <el-progress
              :percentage="currentCheckResult.checkScore"
              :color="getScoreColor(currentCheckResult.checkScore)"
              :show-text="true"
              :stroke-width="8"
             />
          </el-descriptions-item>
          <el-descriptions-item label="检查时间">{{ currentCheckResult.checkTime }}</el-descriptions-item>
          <el-descriptions-item label="问题数量">{{ currentCheckResult.issueCount }}</el-descriptions-item>
          <el-descriptions-item label="检查人">{{ currentCheckResult.checker }}</el-descriptions-item>
        </el-descriptions>

        <div class="issues-section">
          <h4>问题清单</h4>
          <el-table :data="checkIssues" style="width: 100%">
            <el-table-column prop="category" label="问题类别" width="120"  />
            <el-table-column prop="level" label="问题级别" width="100">
              <template #default="scope">
                <el-tag :type="getIssueLevelType(scope.row.level)" size="small">
                  {{ getIssueLevelText(scope.row.level) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="问题描述" show-overflow-tooltip  />
            <el-table-column prop="suggestion" label="修复建议" show-overflow-tooltip  />
            <el-table-column prop="status" label="处理状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'resolved' ? 'success' : 'warning'" size="small">
                  {{ scope.row.status === 'resolved' ? '已解决' : '待处理' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button size="small" @click="handleResolveIssue(scope.row)">
                  {{ scope.row.status === 'resolved' ? '查看' : '处理' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="compliance-report">
          <h4>合规报告</h4>
          <div class="report-content">
            <div class="report-section">
              <h5>总体评价</h5>
              <p>{{ complianceReport.overall }}</p>
            </div>
            <div class="report-section">
              <h5>主要问题</h5>
              <ul>
                <li v-for="issue in complianceReport.mainIssues" :key="issue">{{ issue }}</li>
              </ul>
            </div>
            <div class="report-section">
              <h5>改进建议</h5>
              <ul>
                <li v-for="suggestion in complianceReport.suggestions" :key="suggestion">{{ suggestion }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 规则配置对话框 -->
    <el-dialog v-model="ruleConfigDialogVisible" title="规则配置" width="800px">
      <div class="rule-config">
        <el-tabs v-model="activeRuleTab" type="card">
          <el-tab-pane label="基础规则" name="basic">
            <div class="rule-section">
              <h4>合同基础信息检查</h4>
              <el-checkbox-group v-model="ruleConfig.basic">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-checkbox value="contract_number">合同编号规范性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="party_info">合同双方信息完整性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="date_validity">日期有效性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="term_length">合同期限合理性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="salary_info">薪资信息完整性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="signature_validity">签名有效性</el-checkbox>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </div>
          </el-tab-pane>

          <el-tab-pane label="法律规则" name="legal">
            <div class="rule-section">
              <h4>法律法规合规性检查</h4>
              <el-checkbox-group v-model="ruleConfig.legal">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-checkbox value="labor_law">劳动法合规性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="contract_law">合同法合规性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="social_security">社保公积金合规性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="probation_period">试用期规定合规性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="working_hours">工作时间合规性</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="termination_terms">解除条款合规性</el-checkbox>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </div>
          </el-tab-pane>

          <el-tab-pane label="风险规则" name="risk">
            <div class="rule-section">
              <h4>风险识别规则</h4>
              <el-checkbox-group v-model="ruleConfig.risk">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-checkbox value="salary_risk">薪资风险</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="term_risk">期限风险</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="clause_risk">条款风险</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="compliance_risk">合规风险</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="financial_risk">财务风险</el-checkbox>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox value="legal_risk">法律风险</el-checkbox>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="ruleConfigDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveRuleConfig">保存配置</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractComplianceCheck'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Search,
  Play,
  Download,
  Operation,
  Document,
  CircleCheck,
  Warning,
  CircleClose,
  Close,
  TrendCharts
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const detailDialogVisible = ref(false)
const ruleConfigDialogVisible = ref(false)
const activeRuleTab = ref('basic')
const currentCheckResult = ref(null)

// 检查配置
const checkConfig = reactive({
  checkType: 'comprehensive',
  checkScope: 'all',
  ruleSet: 'standard'
})

// 检查统计
const checkStats = reactive({
  total: 156,
  passed: 128,
  warning: 18,
  error: 8,
  critical: 2,
  passRate: 82
})

// 筛选表单
const filterForm = reactive({
  checkStatus: '',
  riskLevel: '',
  checkDateRange: []
})

// 规则配置
const ruleConfig = reactive({
  basic: ['contract_number', 'party_info', 'date_validity'],
  legal: ['labor_law', 'contract_law', 'social_security'],
  risk: ['salary_risk', 'term_risk', 'clause_risk']
})

// 检查结果数据
const checkResults = ref([
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '张三',
    checkType: '全面检查',
    checkStatus: 'warning',
    riskLevel: 'medium',
    issueCount: 3,
    checkTime: '2025-01-23 10:30:00',
    checkScore: 78,
    checker: '合规系统'
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '李四',
    checkType: '基础检查',
    checkStatus: 'passed',
    riskLevel: 'low',
    issueCount: 0,
    checkTime: '2025-01-23 11:00:00',
    checkScore: 95,
    checker: '合规系统'
  },
  {
    id: '3',
    contractNumber: 'HKY2025003',
    employeeName: '王五',
    checkType: '风险检查',
    checkStatus: 'error',
    riskLevel: 'high',
    issueCount: 5,
    checkTime: '2025-01-23 11:30:00',
    checkScore: 65,
    checker: '合规系统'
  }
])

// 检查问题数据
const checkIssues = ref([
  {
    category: '合同条款',
    level: 'warning',
    description: '试用期条款表述不够明确',
    suggestion: '建议明确试用期的具体时间和考核标准',
    status: 'pending'
  },
  {
    category: '薪资信息',
    level: 'error',
    description: '薪资结构不符合最低工资标准',
    suggestion: '调整基本工资至符合当地最低工资标准',
    status: 'pending'
  },
  {
    category: '法律合规',
    level: 'critical',
    description: '缺少必要的社保条款',
    suggestion: '补充社会保险和住房公积金相关条款',
    status: 'pending'
  }
])

// 合规报告
const complianceReport = reactive({
  overall: '该合同整体合规性良好，但存在一些需要改进的问题。建议及时处理风险点，确保合同的合法性和有效性。',
  mainIssues: [
    '试用期条款表述不够明确，可能导致争议',
    '薪资结构不符合最低工资标准',
    '缺少必要的社保条款'
  ],
  suggestions: [
    '完善试用期相关条款，明确考核标准',
    '调整薪资结构，确保符合法律要求',
    '补充完整的社保公积金条款',
    '定期进行合规性检查，及时发现问题'
  ]
})

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleStartCheck = () => {
  ruleConfigDialogVisible.value = true
}

const handleRunCheck = () => {
  ElMessage.success('检查任务已启动，请稍后查看结果')
}

const handleExportResults = () => {
  ElMessage.success('导出检查结果')
}

const handleBatchProcess = () => {
  ElMessage.success('批量处理已开始')
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    checkStatus: '',
    riskLevel: '',
    checkDateRange: []
  })
  ElMessage.success('筛选条件已重置')
}

   
const handleViewDetail = (row: unknown) => {
  currentCheckResult.value = row
  detailDialogVisible.value = true
}

   
const handleFixIssues = (row: unknown) => {
  ElMessage.success(`开始处理合同问题: ${row.contractNumber}`)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

   
const handleResolveIssue = (row: unknown) => {
  if (row.status === 'resolved') {
    ElMessage.info(`查看问题: ${row.description}`)
  } else {
    ElMessageBox.confirm(`确定要处理此问题吗？`, '确认处理', {
      type: 'warning'
    }).then(() => {
      row.status = 'resolved'
      ElMessage.success('问题已处理')
    }).catch(() => {
      ElMessage.info('已取消处理')
    })
  }
}

const handleSaveRuleConfig = () => {
  ruleConfigDialogVisible.value = false
  ElMessage.success('规则配置已保存')
}

const getStatusType = (status: string) => {
  const statusMap = {
    'passed': 'success',
    'warning': 'warning',
    'error': 'danger',
    'critical': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'passed': '通过',
    'warning': '警告',
    'error': '错误',
    'critical': '严重'
  }
  return statusMap[status] || '未知'
}

const getRiskLevelType = (level: string) => {
  const levelMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  }
  return levelMap[level] || 'info'
}

const getRiskLevelText = (level: string) => {
  const levelMap = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险',
    'critical': '严重风险'
  }
  return levelMap[level] || '未知'
}

const getIssueLevelType = (level: string) => {
  const levelMap = {
    'info': 'info',
    'warning': 'warning',
    'error': 'danger',
    'critical': 'danger'
  }
  return levelMap[level] || 'info'
}

const getIssueLevelText = (level: string) => {
  const levelMap = {
    'info': '信息',
    'warning': '警告',
    'error': '错误',
    'critical': '严重'
  }
  return levelMap[level] || '未知'
}

const getScoreColor = (score: number) => {
  if (score >= 90) return '#67c23a'
  if (score >= 80) return '#e6a23c'
  if (score >= 70) return '#f56c6c'
  return '#f56c6c'
}

// 生命周期
onMounted(() => {
  total.value = checkResults.value.length
})
</script>

<style scoped>
.contract-compliance-check {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.check-console,
.check-results {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.check-config {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.check-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.passed .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.error .stat-icon {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stat-card.critical .stat-icon {
  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
}

.stat-card.rate .stat-icon {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.check-detail {
  margin-top: 20px;
}

.issues-section {
  margin-top: 30px;
}

.issues-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.compliance-report {
  margin-top: 30px;
}

.compliance-report h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.report-content {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.report-section {
  margin-bottom: 20px;
}

.report-section h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.report-section p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.report-section ul {
  margin: 0;
  padding-left: 20px;
}

.report-section li {
  margin-bottom: 4px;
  color: #606266;
}

.rule-config {
  margin-top: 20px;
}

.rule-section {
  margin-bottom: 30px;
}

.rule-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>