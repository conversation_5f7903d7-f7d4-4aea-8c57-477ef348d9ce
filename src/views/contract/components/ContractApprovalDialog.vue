<template>
  <el-dialog
    v-model="dialogVisible"
    title="合同审批"
    width="70%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="approval-container">
      <!-- 合同基本信息 -->
      <el-card class="contract-info-card" shadow="never">
        <template #header>
          <span>合同基本信息</span>
        </template>
        <el-descriptions v-if="contract" :column="3" border>
          <el-descriptions-item label="合同编号">
            {{ contract.contractNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="员工姓名">
            {{ contract.employeeName }}
          </el-descriptions-item>
          <el-descriptions-item label="合同类型">
            {{ contract.contractType }}
          </el-descriptions-item>
          <el-descriptions-item label="申请部门">
            {{ contract.department }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ contract.submitTime }}
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityTag(contract.priority)" size="small">
              {{ getPriorityText(contract.priority) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 审批流程 -->
      <el-card class="workflow-card" shadow="never">
        <template #header>
          <span>审批流程</span>
        </template>
        <el-steps :active="currentStepIndex" direction="vertical" class="approval-steps">
          <el-step
            v-for="(step, index) in workflowSteps"
            :key="index"
            :title="step.stepName"
            :description="step.description"
            :status="getStepStatus(step, index)"
          >
            <template #icon>
              <el-icon v-if="step.status === 'COMPLETED'"><Check /></el-icon>
              <el-icon v-else-if="step.status === 'CURRENT'"><Clock /></el-icon>
              <el-icon v-else-if="step.status === 'REJECTED'"><Close /></el-icon>
              <el-icon v-else><User /></el-icon>
            </template>
            <template #description>
              <div class="step-detail">
                <div class="step-info">
                  <span class="approver">审批人：{{ step.approverName }}</span>
                  <span v-if="step.approvalTime" class="approval-time">
                    审批时间：{{ step.approvalTime }}
                  </span>
                </div>
                <div v-if="step.comment" class="step-comment">
                  审批意见：{{ step.comment }}
                </div>
              </div>
            </template>
          </el-step>
        </el-steps>
      </el-card>

      <!-- 合同内容 -->
      <el-card class="contract-content-card" shadow="never">
        <template #header>
          <div class="content-header">
            <span>合同内容</span>
            <el-button size="small" @click="handleViewFullContract">
              <el-icon><View /></el-icon>
              查看完整合同
            </el-button>
          </div>
        </template>
        <div class="contract-content">
          {{ contract?.contractContent || '合同内容加载中...' }}
        </div>
      </el-card>

      <!-- 审批操作 -->
      <el-card class="approval-action-card" shadow="never">
        <template #header>
          <span>审批操作</span>
        </template>
        <el-form ref="approvalFormRef" :model="approvalForm" :rules="approvalRules" label-width="100px">
          <el-form-item label="审批结果" prop="result">
            <el-radio-group v-model="approvalForm.result">
              <el-radio value="APPROVED">
                <el-icon class="approval-icon approved"><Check /></el-icon>
                通过
              </el-radio>
              <el-radio value="REJECTED">
                <el-icon class="approval-icon rejected"><Close /></el-icon>
                驳回
              </el-radio>
              <el-radio value="RETURNED">
                <el-icon class="approval-icon returned"><Back /></el-icon>
                退回
              </el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="审批意见" prop="comment">
            <el-input
              v-model="approvalForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入审批意见"
              maxlength="500"
              show-word-limit
              />
          </el-form-item>

          <el-form-item v-if="approvalForm.result === 'RETURNED'" label="退回到">
            <el-select v-model="approvalForm.returnToStep" placeholder="选择退回步骤" style="width: 100%">
              <el-option
                v-for="(step, index) in previousSteps"
                :key="index"
                :label="step.stepName"
                :value="index"
               />
            </el-select>
          </el-form-item>

          <el-form-item v-if="approvalForm.result === 'APPROVED'" label="下一步骤">
            <div class="next-step-info">
              <span v-if="nextStep">
                将转到：{{ nextStep.stepName }} ({{ nextStep.approverName }})
              </span>
              <span v-else class="final-step">
                这是最后一个审批步骤，通过后合同将生效
              </span>
            </div>
          </el-form-item>

          <el-form-item label="附件">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :file-list="approvalForm.attachments"
              :on-change="handleAttachmentChange"
              :on-remove="handleAttachmentRemove"
              multiple
            >
              <el-button size="small">
                <el-icon><Upload /></el-icon>
                上传附件
              </el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传审批相关的附件文档
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 审批历史 -->
      <el-card class="approval-history-card" shadow="never">
        <template #header>
          <span>审批历史</span>
        </template>
        <el-timeline class="approval-timeline">
          <el-timeline-item
            v-for="(history, index) in approvalHistory"
            :key="index"
            :timestamp="history.approvalTime"
            :type="getTimelineType(history.result)"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="step-name">{{ history.stepName }}</span>
                <el-tag :type="getResultTag(history.result)" size="small">
                  {{ getResultText(history.result) }}
                </el-tag>
              </div>
              <div class="timeline-body">
                <p><strong>审批人：</strong>{{ history.approverName }}</p>
                <p v-if="history.comment"><strong>审批意见：</strong>{{ history.comment }}</p>
                <p><strong>处理时长：</strong>{{ history.processingTime }}</p>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSaveDraft">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmitApproval" :loading="loading">
          提交审批
        </el-button>
      </div>
    </template>

    <!-- 完整合同查看对话框 -->
    <el-dialog
      v-model="fullContractVisible"
      title="完整合同内容"
      width="80%"
      append-to-body
    >
      <div class="full-contract-content">
        {{ contract?.fullContractContent || contract?.contractContent }}
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractApprovalDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  Close,
  Clock,
  User,
  View,
  Back,
  Upload
} from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
   
  contract?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  contract: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const approvalFormRef = ref()
const uploadRef = ref()
const loading = ref(false)
const fullContractVisible = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 审批表单
const approvalForm = reactive({
  result: '',
  comment: '',
  returnToStep: '',
  attachments: [] as unknown[] // 修复类型：never[] → any[]
})

// 审批表单验证规则
const approvalRules = {
  result: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    { min: 10, message: '审批意见至少10个字符', trigger: 'blur' }
  ]
}

// 模拟工作流步骤数据
const workflowSteps = ref([
  {
    stepName: '部门初审',
    approverName: '张主任',
    status: 'COMPLETED',
    approvalTime: '2025-06-18 10:30:00',
    comment: '材料齐全，同意提交上级审批',
    description: '部门负责人初步审核'
  },
  {
    stepName: '人事处审批',
    approverName: '李处长',
    status: 'CURRENT',
    approvalTime: '',
    comment: '',
    description: '人事处负责人审批'
  },
  {
    stepName: '院长审批',
    approverName: '王院长',
    status: 'PENDING',
    approvalTime: '',
    comment: '',
    description: '院长最终审批'
  }
])

// 审批历史
const approvalHistory = ref([
  {
    stepName: '提交申请',
    approverName: '系统',
    result: 'SUBMITTED',
    approvalTime: '2025-06-18 09:00:00',
    comment: '合同申请已提交',
    processingTime: '-'
  },
  {
    stepName: '部门初审',
    approverName: '张主任',
    result: 'APPROVED',
    approvalTime: '2025-06-18 10:30:00',
    comment: '材料齐全，同意提交上级审批',
    processingTime: '1小时30分钟'
  }
])

// 当前步骤索引
const currentStepIndex = computed(() => {
  return workflowSteps.value.findIndex(step => step.status === 'CURRENT')
})

// 前面的步骤（用于退回选择）
const previousSteps = computed(() => {
  return workflowSteps.value.slice(0, currentStepIndex.value)
})

// 下一个步骤
const nextStep = computed(() => {
  const nextIndex = currentStepIndex.value + 1
  return nextIndex < workflowSteps.value.length ? workflowSteps.value[nextIndex] : null
})

// 获取优先级标签
const getPriorityTag = (priority: string) => {
  switch (priority) {
    case 'URGENT': return 'danger'
    case 'HIGH': return 'warning'
    case 'MEDIUM': return 'primary'
    case 'LOW': return 'info'
    default: return ''
  }
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'URGENT': return '紧急'
    case 'HIGH': return '高'
    case 'MEDIUM': return '中'
    case 'LOW': return '低'
    default: return priority
  }
}

// 获取步骤状态
   
const getStepStatus = (step: unknown, index: number) => {
  switch (step.status) {
    case 'COMPLETED': return 'success'
    case 'CURRENT': return 'process'
    case 'REJECTED': return 'error'
    default: return 'wait'
  }
}

// 获取时间线类型
const getTimelineType = (result: string) => {
  switch (result) {
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    case 'RETURNED': return 'warning'
    default: return 'primary'
  }
}

// 获取结果标签
const getResultTag = (result: string) => {
  switch (result) {
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    case 'RETURNED': return 'warning'
    case 'SUBMITTED': return 'info'
    default: return ''
  }
}

// 获取结果文本
const getResultText = (result: string) => {
  switch (result) {
    case 'APPROVED': return '通过'
    case 'REJECTED': return '驳回'
    case 'RETURNED': return '退回'
    case 'SUBMITTED': return '已提交'
    default: return result
  }
}

// 查看完整合同
const handleViewFullContract = () => {
  fullContractVisible.value = true
}

// 附件变化处理
   
const handleAttachmentChange = (file: unknown, fileList: unknown[]) => {
  approvalForm.attachments = fileList
}

// 移除附件
   
const handleAttachmentRemove = (file: unknown, fileList: unknown[]) => {
  approvalForm.attachments = fileList
}

// 保存草稿
const handleSaveDraft = async () => {
  try {
    loading.value = true
    
    // 模拟保存草稿
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('草稿保存成功')
  } catch (__error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败')
  } finally {
    loading.value = false
  }
}

// 提交审批
const handleSubmitApproval = async () => {
  try {
    await approvalFormRef.value.validate()
    
    const confirmMessage = `确定要${getResultText(approvalForm.result)}这个合同吗？`
    await ElMessageBox.confirm(confirmMessage, '确认审批', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    loading.value = true
    
    // 模拟提交审批
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('审批提交成功')
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('提交审批失败:', error)
      ElMessage.error('提交审批失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  Object.assign(approvalForm, {
    result: '',
    comment: '',
    returnToStep: '',
    attachments: [] as unknown[] // 修复类型：never[] → any[]
  })
  approvalFormRef.value?.resetFields()
}
</script>

<style scoped>
.approval-container {
  max-height: 70vh;
  overflow-y: auto;
}

.contract-info-card,
.workflow-card,
.contract-content-card,
.approval-action-card,
.approval-history-card {
  margin-bottom: 16px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.contract-content {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
  max-height: 200px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.approval-steps {
  margin: 16px 0;
}

.step-detail {
  margin-top: 8px;
}

.step-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #606266;
}

.step-comment {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.approval-icon {
  margin-right: 4px;
}

.approval-icon.approved {
  color: #67c23a;
}

.approval-icon.rejected {
  color: #f56c6c;
}

.approval-icon.returned {
  color: #e6a23c;
}

.next-step-info {
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  font-size: 14px;
  color: #1e40af;
}

.final-step {
  color: #059669;
  font-weight: 600;
}

.approval-timeline {
  margin-top: 16px;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.step-name {
  font-weight: 600;
  color: #303133;
}

.timeline-body p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.full-contract-content {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
  max-height: 60vh;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.8;
  white-space: pre-wrap;
}

.dialog-footer {
  text-align: right;
}
</style>
