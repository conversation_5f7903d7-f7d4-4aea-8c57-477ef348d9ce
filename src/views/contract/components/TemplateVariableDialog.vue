<template>
  <el-dialog
    v-model="dialogVisible"
    title="变量管理"
    width="70%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="variable-container">
      <!-- 变量操作工具栏 -->
      <div class="variable-toolbar">
        <div class="toolbar-left">
          <span class="template-info">
            {{ template?.templateName }} 的变量管理
          </span>
        </div>
        <div class="toolbar-right">
          <el-button size="small" type="success" @click="handleAddVariable">
            <el-icon><Plus /></el-icon>
            添加变量
          </el-button>
          <el-button size="small" @click="handleImportVariables">
            <el-icon><Upload /></el-icon>
            导入变量
          </el-button>
          <el-button size="small" @click="handleExportVariables">
            <el-icon><Download /></el-icon>
            导出变量
          </el-button>
        </div>
      </div>

      <!-- 变量统计 -->
      <el-row :gutter="20" class="variable-stats">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ variableStats.total }}</div>
                <div class="stats-label">变量总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon required">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ variableStats.required }}</div>
                <div class="stats-label">必填变量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon optional">
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ variableStats.optional }}</div>
                <div class="stats-label">可选变量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon unused">
                <el-icon><QuestionFilled /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ variableStats.unused }}</div>
                <div class="stats-label">未使用</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 变量筛选 -->
      <div class="filter-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="filterForm.keyword"
              placeholder="搜索变量名、显示名称"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filterForm.type" placeholder="变量类型" clearable>
              <el-option label="文本" value="TEXT"  />
              <el-option label="数字" value="NUMBER"  />
              <el-option label="日期" value="DATE"  />
              <el-option label="选择" value="SELECT"  />
              <el-option label="布尔" value="BOOLEAN"  />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filterForm.required" placeholder="是否必填" clearable>
              <el-option label="必填" value="true"  />
              <el-option label="可选" value="false"  />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filterForm.usage" placeholder="使用状态" clearable>
              <el-option label="已使用" value="used"  />
              <el-option label="未使用" value="unused"  />
            </el-select>
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              筛选
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 变量列表 -->
      <el-table 
        :data="filteredVariables" 
        style="width: 100%" 
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="variableName" label="变量名" width="150">
          <template #default="scope">
            <code class="variable-name">{{ scope.row.variableName }}</code>
          </template>
        </el-table-column>
        <el-table-column prop="variableLabel" label="显示名称" width="150"  />
        <el-table-column prop="variableType" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getVariableTypeTag(scope.row.variableType)" size="small">
              {{ getVariableTypeText(scope.row.variableType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isRequired" label="必填" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.isRequired ? 'danger' : 'info'" size="small">
              {{ scope.row.isRequired ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="defaultValue" label="默认值" width="120" show-overflow-tooltip  />
        <el-table-column prop="usageCount" label="使用次数" width="100" align="center"  />
        <el-table-column prop="isUsed" label="使用状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isUsed ? 'success' : 'warning'" size="small">
              {{ scope.row.isUsed ? '已使用' : '未使用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleEditVariable(scope.row)">
              编辑
            </el-button>
            <el-button size="small" type="success" link @click="handleTestVariable(scope.row)">
              测试
            </el-button>
            <el-button size="small" type="info" link @click="handleCopyVariable(scope.row)">
              复制
            </el-button>
            <el-button 
              v-if="!scope.row.isUsed"
              size="small" 
              type="danger" 
              link 
              @click="handleDeleteVariable(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div v-if="selectedVariables.length > 0" class="batch-actions">
        <span class="batch-info">已选择 {{ selectedVariables.length }} 个变量</span>
        <el-button size="small" type="danger" @click="handleBatchDelete">
          批量删除
        </el-button>
        <el-button size="small" type="primary" @click="handleBatchExport">
          批量导出
        </el-button>
      </div>
    </div>

    <!-- 变量编辑对话框 -->
    <VariableEditorDialog
      v-model:visible="editorDialogVisible"
      :variable="currentVariable"
      :mode="editorMode"
      @success="handleVariableSuccess"
    />

    <!-- 变量测试对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="变量测试"
      width="500px"
      append-to-body
    >
      <div v-if="testVariable" class="test-container">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="变量名">
            <code>{{ testVariable.variableName }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="显示名称">
            {{ testVariable.variableLabel }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            {{ getVariableTypeText(testVariable.variableType) }}
          </el-descriptions-item>
          <el-descriptions-item label="是否必填">
            {{ testVariable.isRequired ? '是' : '否' }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="test-input">
          <h4>测试输入</h4>
          <el-input
            v-if="testVariable.variableType === 'TEXT'"
            v-model="testValue"
            :placeholder="`请输入${testVariable.variableLabel}`"
            />
          <el-input-number
            v-else-if="testVariable.variableType === 'NUMBER'"
            v-model="testValue"
            :placeholder="`请输入${testVariable.variableLabel}`"
            style="width: 100%"
            />
          <el-date-picker
            v-else-if="testVariable.variableType === 'DATE'"
            v-model="testValue"
            type="date"
            :placeholder="`请选择${testVariable.variableLabel}`"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
           />
          <el-select
            v-else-if="testVariable.variableType === 'SELECT'"
            v-model="testValue"
            :placeholder="`请选择${testVariable.variableLabel}`"
            style="width: 100%"
          >
            <el-option
              v-for="option in testVariable.options"
              :key="option"
              :label="option"
              :value="option"
             />
          </el-select>
          <el-select
            v-else-if="testVariable.variableType === 'BOOLEAN'"
            v-model="testValue"
            :placeholder="`请选择${testVariable.variableLabel}`"
            style="width: 100%"
          >
            <el-option label="是" value="true"  />
            <el-option label="否" value="false"  />
          </el-select>
        </div>

        <div class="test-result">
          <h4>测试结果</h4>
          <div class="result-box">
            <p><strong>变量占位符：</strong> <code>{{ testVariablePlaceholder }}</code></p>
            <p><strong>替换后结果：</strong> {{ testValue || testVariable.defaultValue || '[空值]' }}</p>
          </div>
        </div>
      </div>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSaveVariables">
          保存变量
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TemplateVariableDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  Collection,
  Warning,
  InfoFilled,
  QuestionFilled,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import VariableEditorDialog from './VariableEditorDialog.vue'

// Props
interface Props {
  visible: boolean
   
  template?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  template: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const editorDialogVisible = ref(false)
const testDialogVisible = ref(false)
const editorMode = ref<'add' | 'edit'>('add')
const currentVariable = ref(null)
const testVariable = ref<unknown>(null) // 修复类型：null → any
const testValue = ref('')
const selectedVariables = ref<any[]>([]) // 修复类型：never[] → any[]

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 筛选表单
const filterForm = reactive({
  keyword: '',
  type: '',
  required: '',
  usage: ''
})

// 模拟变量数据
const variableList = ref([
  {
    id: '1',
    variableName: 'employeeName',
    variableLabel: '员工姓名',
    variableType: 'TEXT',
    isRequired: true,
    defaultValue: '',
    usageCount: 15,
    isUsed: true,
    validation: '^[a-zA-Z\\u4e00-\\u9fa5]+$'
  },
  {
    id: '2',
    variableName: 'startDate',
    variableLabel: '开始日期',
    variableType: 'DATE',
    isRequired: true,
    defaultValue: '',
    usageCount: 15,
    isUsed: true
  },
  {
    id: '3',
    variableName: 'department',
    variableLabel: '部门',
    variableType: 'SELECT',
    isRequired: true,
    defaultValue: '计算机学院',
    options: ['计算机学院', '机械工程学院', '管理学院'],
    usageCount: 12,
    isUsed: true
  },
  {
    id: '4',
    variableName: 'salary',
    variableLabel: '薪资',
    variableType: 'NUMBER',
    isRequired: false,
    defaultValue: '5000',
    usageCount: 0,
    isUsed: false
  }
])

// 变量统计
const variableStats = computed(() => {
  const total = variableList.value.length
  const required = variableList.value.filter(v => v.isRequired).length
  const optional = total - required
  const unused = variableList.value.filter(v => !v.isUsed).length
  
  return { total, required, optional, unused }
})

// 筛选后的变量列表
const filteredVariables = computed(() => {
  let result = variableList.value

  if (filterForm.keyword) {
    result = result.filter(v =>
      v.variableName.toLowerCase().includes(filterForm.keyword.toLowerCase()) ||
      v.variableLabel.includes(filterForm.keyword)
    )
  }

  if (filterForm.type) {
    result = result.filter(v => v.variableType === filterForm.type)
  }

  if (filterForm.required) {
    result = result.filter(v => v.isRequired === (filterForm.required === 'true'))
  }

  if (filterForm.usage) {
    result = result.filter(v => v.isUsed === (filterForm.usage === 'used'))
  }

  return result
})

// 测试变量占位符
const testVariablePlaceholder = computed(() => {
  return testVariable.value ? `{{${(testVariable.value as unknown)?.variableName}}}` : '' // 修复属性访问类型
})

// 添加变量
const handleAddVariable = () => {
  currentVariable.value = null
  editorMode.value = 'add'
  editorDialogVisible.value = true
}

// 编辑变量
   
const handleEditVariable = (variable: unknown) => {
  currentVariable.value = variable
  editorMode.value = 'edit'
  editorDialogVisible.value = true
}

// 测试变量
   
const handleTestVariable = (variable: unknown) => {
  testVariable.value = variable
  testValue.value = variable.defaultValue || ''
  testDialogVisible.value = true
}

// 复制变量
   
const handleCopyVariable = (variable: unknown) => {
  const newVariable = {
    ...variable,
    id: Date.now().toString(),
    variableName: `${variable.variableName}_copy`,
    variableLabel: `${variable.variableLabel}(副本)`,
    usageCount: 0,
    isUsed: false
  }
  
  variableList.value.push(newVariable)
  ElMessage.success('变量复制成功')
}

// 删除变量
   
const handleDeleteVariable = async (variable: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除变量 "${variable.variableLabel}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = variableList.value.findIndex(v => v.id === variable.id)
    if (index > -1) {
      variableList.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
  } catch (__error) {
    // 用户取消
  }
}

// 导入变量
const handleImportVariables = () => {
  ElMessage.info('导入变量功能开发中...')
}

// 导出变量
const handleExportVariables = () => {
  ElMessage.info('导出变量功能开发中...')
}

// 筛选
const handleFilter = () => {
  // 筛选逻辑已在computed中实现
}

// 重置筛选
const handleResetFilter = () => {
  Object.assign(filterForm, {
    keyword: '',
    type: '',
    required: '',
    usage: ''
  })
}

// 选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedVariables.value = selection
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedVariables.value.length} 个变量吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
   
    const selectedIds = selectedVariables.value.map((v: unknown) => v.id)
    variableList.value = variableList.value.filter(v => !selectedIds.includes(v.id))
    selectedVariables.value = []
    
    ElMessage.success('批量删除成功')
  } catch (__error) {
    // 用户取消
  }
}

// 批量导出
const handleBatchExport = () => {
  ElMessage.info('批量导出功能开发中...')
}

// 变量编辑成功
   
const handleVariableSuccess = (variable: unknown) => {
  if (editorMode.value === 'add') {
    variableList.value.push({
      ...variable,
      id: Date.now().toString(),
      usageCount: 0,
      isUsed: false
    })
  } else {
   
    const index = variableList.value.findIndex((v: unknown) => (v as unknown)?.id === (currentVariable.value as unknown)?.id) // 修复类型断言
    if (index > -1) {
      variableList.value[index] = { ...variableList.value[index], ...variable }
    }
  }
  ElMessage.success('变量保存成功')
}

// 保存变量
const handleSaveVariables = () => {
  ElMessage.success('变量配置已保存')
  emit('success')
  handleClose()
}

// 获取变量类型标签
const getVariableTypeTag = (type: string) => {
  switch (type) {
    case 'TEXT': return 'primary'
    case 'NUMBER': return 'success'
    case 'DATE': return 'warning'
    case 'SELECT': return 'info'
    case 'BOOLEAN': return 'danger'
    default: return ''
  }
}

// 获取变量类型文本
const getVariableTypeText = (type: string) => {
  switch (type) {
    case 'TEXT': return '文本'
    case 'NUMBER': return '数字'
    case 'DATE': return '日期'
    case 'SELECT': return '选择'
    case 'BOOLEAN': return '布尔'
    default: return type
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.variable-container {
  max-height: 70vh;
  overflow-y: auto;
}

.variable-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.template-info {
  font-weight: 600;
  color: #303133;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.variable-stats {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.required {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-icon.optional {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.unused {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.filter-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.variable-name {
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 12px;
}

.batch-actions {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  color: #1e40af;
  font-size: 14px;
}

.test-container {
  margin-top: 16px;
}

.test-input {
  margin: 20px 0;
}

.test-input h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.test-result {
  margin-top: 20px;
}

.test-result h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.result-box {
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.result-box p {
  margin: 8px 0;
}

.result-box code {
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.dialog-footer {
  text-align: right;
}
</style>
