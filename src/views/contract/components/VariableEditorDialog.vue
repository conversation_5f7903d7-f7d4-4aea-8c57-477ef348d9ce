<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="变量名" prop="variableName">
        <el-input
          v-model="formData.variableName"
          placeholder="请输入变量名，如：employeeName"
          />
        <div class="form-tip">变量名只能包含字母、数字和下划线</div>
      </el-form-item>

      <el-form-item label="显示名称" prop="variableLabel">
        <el-input
          v-model="formData.variableLabel"
          placeholder="请输入显示名称，如：员工姓名"
          />
      </el-form-item>

      <el-form-item label="变量类型" prop="variableType">
        <el-select
          v-model="formData.variableType"
          placeholder="请选择变量类型"
          style="width: 100%"
          @change="handleTypeChange"
        >
          <el-option label="文本" value="TEXT"  />
          <el-option label="数字" value="NUMBER"  />
          <el-option label="日期" value="DATE"  />
          <el-option label="选择" value="SELECT"  />
          <el-option label="布尔" value="BOOLEAN"  />
        </el-select>
      </el-form-item>

      <el-form-item label="是否必填">
        <el-switch
          v-model="formData.isRequired"
          active-text="必填"
          inactive-text="可选"
         />
      </el-form-item>

      <el-form-item label="默认值" prop="defaultValue">
        <el-input
          v-if="formData.variableType === 'TEXT'"
          v-model="formData.defaultValue"
          placeholder="请输入默认值"
          />
        <el-input-number
          v-else-if="formData.variableType === 'NUMBER'"
          v-model="formData.defaultValue"
          placeholder="请输入默认数值"
          style="width: 100%"
          />
        <el-date-picker
          v-else-if="formData.variableType === 'DATE'"
          v-model="formData.defaultValue"
          type="date"
          placeholder="请选择默认日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
         />
        <el-select
          v-else-if="formData.variableType === 'SELECT'"
          v-model="formData.defaultValue"
          placeholder="请选择默认选项"
          style="width: 100%"
        >
          <el-option
            v-for="option in formData.options"
            :key="option"
            :label="option"
            :value="option"
           />
        </el-select>
        <el-select
          v-else-if="formData.variableType === 'BOOLEAN'"
          v-model="formData.defaultValue"
          placeholder="请选择默认值"
          style="width: 100%"
        >
          <el-option label="是" value="true"  />
          <el-option label="否" value="false"  />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="formData.variableType === 'SELECT'"
        label="选项配置"
        prop="options"
      >
        <div class="options-editor">
          <div
            v-for="(option, index) in formData.options"
            :key="index"
            class="option-item"
          >
            <el-input
              v-model="formData.options[index]"
              placeholder="请输入选项值"
              style="flex: 1"
              />
            <el-button
              type="danger"
              size="small"
              @click="removeOption(index)"
            >
              删除
            </el-button>
          </div>
          <el-button
            type="primary"
            size="small"
            @click="addOption"
          >
            <el-icon><Plus /></el-icon>
            添加选项
          </el-button>
        </div>
      </el-form-item>

      <el-form-item
        v-if="formData.variableType === 'TEXT'"
        label="验证规则"
        prop="validation"
      >
        <el-input
          v-model="formData.validation"
          placeholder="请输入正则表达式，如：^[a-zA-Z\u4e00-\u9fa5]+$"
          />
        <div class="form-tip">可选，用于验证输入格式的正则表达式</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'VariableEditorDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
   
  variable?: unknown
  mode: 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  variable: null,
  mode: 'add'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
   
  success: [variable: unknown]
}>()

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.mode === 'add' ? '添加变量' : '编辑变量'
})

// 表单数据
const formData = reactive({
  variableName: '',
  variableLabel: '',
  variableType: 'TEXT',
  isRequired: false,
  defaultValue: '',
  options: [] as string[],
  validation: ''
})

// 表单验证规则
const formRules = {
  variableName: [
    { required: true, message: '请输入变量名', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, 
      message: '变量名只能包含字母、数字和下划线，且不能以数字开头', 
      trigger: 'blur' 
    }
  ],
  variableLabel: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  variableType: [
    { required: true, message: '请选择变量类型', trigger: 'change' }
  ],
  options: [
    {
   
      validator: (rule: unknown, value: string[], callback: Function) => {
        if (formData.variableType === 'SELECT' && (!value || value.length === 0)) {
          callback(new Error('选择类型至少需要一个选项'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 监听变量数据变化
watch(() => props.variable, (newVariable) => {
  if (newVariable) {
    Object.assign(formData, {
      variableName: newVariable.variableName || '',
      variableLabel: newVariable.variableLabel || '',
      variableType: newVariable.variableType || 'TEXT',
      isRequired: newVariable.isRequired || false,
      defaultValue: newVariable.defaultValue || '',
      options: newVariable.options ? [...newVariable.options] : [],
      validation: newVariable.validation || ''
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      variableName: '',
      variableLabel: '',
      variableType: 'TEXT',
      isRequired: false,
      defaultValue: '',
      options: [],
      validation: ''
    })
  }
}, { immediate: true })

// 变量类型变化处理
const handleTypeChange = (type: string) => {
  // 清空默认值和选项
  formData.defaultValue = ''
  formData.options = []
  formData.validation = ''
  
  // 为选择类型添加默认选项
  if (type === 'SELECT') {
    formData.options = ['选项1', '选项2']
  }
}

// 添加选项
const addOption = () => {
  formData.options.push(`选项${formData.options.length + 1}`)
}

// 删除选项
const removeOption = (index: number) => {
  formData.options.splice(index, 1)
  
  // 如果删除的是当前默认值，清空默认值
  if (formData.defaultValue === formData.options[index]) {
    formData.defaultValue = ''
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 构建变量对象
    const variable = {
      variableName: formData.variableName,
      variableLabel: formData.variableLabel,
      variableType: formData.variableType,
      isRequired: formData.isRequired,
      defaultValue: formData.defaultValue,
      options: formData.variableType === 'SELECT' ? [...formData.options] : undefined,
      validation: formData.variableType === 'TEXT' ? formData.validation : undefined
    }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    emit('success', variable)
    handleClose()
  } catch (__error) {
    console.error('保存变量失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.options-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.option-item:last-child {
  margin-bottom: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
