<template>
  <el-dialog
    v-model="dialogVisible"
    title="模板预览"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="preview-container">
      <!-- 预览工具栏 -->
      <div class="preview-toolbar">
        <div class="toolbar-left">
          <span class="template-info">
            {{ template?.templateName }} (v{{ template?.version }})
          </span>
        </div>
        <div class="toolbar-right">
          <el-button size="small" @click="handleFillSampleData">
            <el-icon><Star /></el-icon>
            填充示例数据
          </el-button>
          <el-button size="small" @click="handleClearData">
            <el-icon><Delete /></el-icon>
            清空数据
          </el-button>
          <el-button size="small" type="primary" @click="handleExportPreview">
            <el-icon><Download /></el-icon>
            导出预览
          </el-button>
        </div>
      </div>

      <!-- 变量输入区域 -->
      <el-card v-if="variables.length > 0" class="variables-card" shadow="never">
        <template #header>
          <span>变量值设置</span>
        </template>
        <el-form :model="variableValues" label-width="120px" class="variables-form">
          <el-row :gutter="20">
            <el-col
              v-for="variable in variables"
              :key="variable.variableName"
              :span="12"
            >
              <el-form-item
                :label="variable.variableLabel"
                :required="variable.isRequired"
              >
                <!-- 文本输入 -->
                <el-input
                  v-if="variable.variableType === 'TEXT'"
                  v-model="variableValues[variable.variableName]"
                  :placeholder="`请输入${variable.variableLabel}`"
                  />
                
                <!-- 数字输入 -->
                <el-input-number
                  v-else-if="variable.variableType === 'NUMBER'"
                  v-model="variableValues[variable.variableName]"
                  :placeholder="`请输入${variable.variableLabel}`"
                  style="width: 100%"
                  />
                
                <!-- 日期选择 -->
                <el-date-picker
                  v-else-if="variable.variableType === 'DATE'"
                  v-model="variableValues[variable.variableName]"
                  type="date"
                  :placeholder="`请选择${variable.variableLabel}`"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                 />
                
                <!-- 选择框 -->
                <el-select
                  v-else-if="variable.variableType === 'SELECT'"
                  v-model="variableValues[variable.variableName]"
                  :placeholder="`请选择${variable.variableLabel}`"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in variable.options"
                    :key="option"
                    :label="option"
                    :value="option"
                   />
                </el-select>
                
                <!-- 布尔选择 -->
                <el-select
                  v-else-if="variable.variableType === 'BOOLEAN'"
                  v-model="variableValues[variable.variableName]"
                  :placeholder="`请选择${variable.variableLabel}`"
                  style="width: 100%"
                >
                  <el-option label="是" value="true"  />
                  <el-option label="否" value="false"  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 预览内容区域 -->
      <el-card class="preview-card" shadow="never">
        <template #header>
          <div class="preview-header">
            <span>预览内容</span>
            <div class="preview-actions">
              <el-button size="small" @click="handleRefreshPreview">
                <el-icon><Refresh /></el-icon>
                刷新预览
              </el-button>
              <el-button size="small" @click="togglePreviewMode">
                <el-icon><View /></el-icon>
                {{ previewMode === 'formatted' ? '源码模式' : '格式化模式' }}
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="preview-content">
          <!-- 格式化预览 -->
          <div
            v-if="previewMode === 'formatted'"
            class="formatted-preview"
            v-html="formattedContent"
          ></div>
          
          <!-- 源码预览 -->
          <pre
            v-else
            class="source-preview"
          >{{ sourceContent }}</pre>
        </div>
      </el-card>

      <!-- 预览统计 -->
      <el-card class="stats-card" shadow="never">
        <template #header>
          <span>预览统计</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ contentStats.characters }}</div>
              <div class="stat-label">字符数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ contentStats.words }}</div>
              <div class="stat-label">词数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ contentStats.lines }}</div>
              <div class="stat-label">行数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ contentStats.variables }}</div>
              <div class="stat-label">变量数</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleUseTemplate">
          使用此模板
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TemplatePreviewDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Star, Delete, Download, Refresh, View } from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
   
  template?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  template: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
   
  useTemplate: [template: unknown, variables: unknown]
}>()

// 响应式数据
const previewMode = ref<'formatted' | 'source'>('formatted')
const variableValues = reactive<Record<string, any>>({})

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 变量列表
const variables = computed(() => {
  return props.template?.variables || []
})

// 格式化内容
const formattedContent = computed(() => {
  if (!props.template?.templateContent) return ''
  
  let content = props.template.templateContent
  
  // 替换变量
   
  variables.value.forEach((variable: unknown) => { // 修复参数类型
    const placeholder = `{{${variable.variableName}}}`
    const value = variableValues[variable.variableName] || 
                  variable.defaultValue || 
                  `[${variable.variableLabel}]`
    content = content.replace(new RegExp(placeholder, 'g'), value)
  })
  
  // 转换换行符为HTML
  return content.replace(/\n/g, '<br>')
})

// 源码内容
const sourceContent = computed(() => {
  if (!props.template?.templateContent) return ''
  
  let content = props.template.templateContent
  
  // 替换变量
   
  variables.value.forEach((variable: unknown) => { // 修复参数类型
    const placeholder = `{{${variable.variableName}}}`
    const value = variableValues[variable.variableName] || 
                  variable.defaultValue || 
                  `[${variable.variableLabel}]`
    content = content.replace(new RegExp(placeholder, 'g'), value)
  })
  
  return content
})

// 内容统计
const contentStats = computed(() => {
  const content = sourceContent.value
  return {
    characters: content.length,
    words: content.split(/\s+/).filter((word: string) => word.length > 0).length, // 修复参数类型
    lines: content.split('\n').length,
    variables: variables.value.length
  }
})

// 监听模板变化，初始化变量值
watch(() => props.template, (newTemplate) => {
  if (newTemplate?.variables) {
    // 清空现有值
    Object.keys(variableValues).forEach(key => {
      delete variableValues[key]
    })
    
    // 设置默认值
   
    newTemplate.variables.forEach((variable: unknown) => { // 修复参数类型
      if (variable.defaultValue) {
        variableValues[variable.variableName] = variable.defaultValue
      }
    })
  }
}, { immediate: true })

// 填充示例数据
const handleFillSampleData = () => {
   
  variables.value.forEach((variable: unknown) => { // 修复参数类型
    variableValues[variable.variableName] = getSampleValue(variable)
  })
  ElMessage.success('已填充示例数据')
}

// 获取示例值
   
const getSampleValue = (variable: unknown) => {
  switch (variable.variableType) {
    case 'TEXT':
      if (variable.variableName.includes('name') || variable.variableName.includes('Name')) {
        return '张三'
      } else if (variable.variableName.includes('department') || variable.variableName.includes('Department')) {
        return '计算机学院'
      } else if (variable.variableName.includes('position') || variable.variableName.includes('Position')) {
        return '教授'
      }
      return '示例文本'
    case 'NUMBER':
      return Math.floor(Math.random() * 1000) + 1
    case 'DATE':
      return '2025-06-19'
    case 'SELECT':
      return variable.options?.[0] || '选项1'
    case 'BOOLEAN':
      return 'true'
    default:
      return '示例值'
  }
}

// 清空数据
const handleClearData = () => {
  Object.keys(variableValues).forEach(key => {
    delete variableValues[key]
  })
  ElMessage.success('已清空数据')
}

// 导出预览
const handleExportPreview = () => {
  const content = sourceContent.value
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${props.template?.templateName || '模板预览'}.txt`
  link.click()
  URL.revokeObjectURL(url)
  ElMessage.success('导出成功')
}

// 刷新预览
const handleRefreshPreview = () => {
  ElMessage.success('预览已刷新')
}

// 切换预览模式
const togglePreviewMode = () => {
  previewMode.value = previewMode.value === 'formatted' ? 'source' : 'formatted'
}

// 使用模板
const handleUseTemplate = () => {
  emit('useTemplate', props.template, { ...variableValues })
  ElMessage.success('已选择使用此模板')
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.preview-container {
  max-height: 80vh;
  overflow-y: auto;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.template-info {
  font-weight: 600;
  color: #303133;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.variables-card {
  margin-bottom: 16px;
}

.variables-form {
  max-height: 300px;
  overflow-y: auto;
}

.preview-card {
  margin-bottom: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
}

.formatted-preview {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: white;
  line-height: 1.8;
  font-size: 14px;
}

.source-preview {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #f8f9fa;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.stats-card {
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
