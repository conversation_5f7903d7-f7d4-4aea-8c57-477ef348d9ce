<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item label="步骤名称" prop="stepName">
        <el-input v-model="formData.stepName" placeholder="请输入步骤名称"   />
      </el-form-item>
      
      <el-form-item label="步骤类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择步骤类型" style="width: 100%">
          <el-option label="审批" value="approval"  />
          <el-option label="通知" value="notification"  />
          <el-option label="条件" value="condition"  />
          <el-option label="并行" value="parallel"  />
        </el-select>
      </el-form-item>
      
      <el-form-item label="审批人" prop="approverName">
        <el-input v-model="formData.approverName" placeholder="请输入审批人"   />
      </el-form-item>
      
      <el-form-item label="是否必须">
        <el-switch v-model="formData.isRequired"  />
      </el-form-item>
      
      <el-form-item label="超时时间">
        <el-input-number
          v-model="formData.timeoutHours"
          :min="1"
          :max="168"
          placeholder="小时"
          style="width: 100%"
          />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'StepEditorDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  visible: boolean
   
  step?: unknown
  mode: 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  step: null,
  mode: 'add'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
   
  success: [step: unknown]
}>()

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.mode === 'add' ? '添加步骤' : '编辑步骤'
})

// 表单数据
const formData = reactive({
  stepName: '',
  type: 'approval',
  approverName: '',
  isRequired: true,
  timeoutHours: 24
})

// 表单验证规则
const formRules = {
  stepName: [
    { required: true, message: '请输入步骤名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择步骤类型', trigger: 'change' }
  ],
  approverName: [
    { required: true, message: '请输入审批人', trigger: 'blur' }
  ]
}

// 监听步骤数据变化
watch(() => props.step, (newStep) => {
  if (newStep) {
    Object.assign(formData, {
      stepName: newStep.stepName || '',
      type: newStep.type || 'approval',
      approverName: newStep.approverName || '',
      isRequired: newStep.isRequired !== false,
      timeoutHours: newStep.timeoutHours || 24
    })
  } else {
    Object.assign(formData, {
      stepName: '',
      type: 'approval',
      approverName: '',
      isRequired: true,
      timeoutHours: 24
    })
  }
}, { immediate: true })

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    emit('success', { ...formData })
    handleClose()
  } catch (__error) {
    console.error('保存步骤失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
