<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量审批"
    width="70%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="batch-approval-container">
      <!-- 批量操作概览 -->
      <el-card class="overview-card" shadow="never">
        <template #header>
          <span>批量操作概览</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="overview-item">
              <div class="overview-number">{{ contracts.length }}</div>
              <div class="overview-label">选中合同</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="overview-item">
              <div class="overview-number">{{ urgentCount }}</div>
              <div class="overview-label">紧急合同</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="overview-item">
              <div class="overview-number">{{ overdueCount }}</div>
              <div class="overview-label">超时合同</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="overview-item">
              <div class="overview-number">{{ differentTypesCount }}</div>
              <div class="overview-label">合同类型</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 合同列表 -->
      <el-card class="contracts-card" shadow="never">
        <template #header>
          <div class="contracts-header">
            <span>待审批合同列表</span>
            <div class="header-actions">
              <el-button size="small" @click="handleSelectAll">
                {{ allSelected ? '取消全选' : '全选' }}
              </el-button>
              <el-button size="small" @click="handleSelectUrgent">
                选择紧急
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          ref="contractsTableRef"
          :data="contracts"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          max-height="300"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="contractNumber" label="合同编号" width="120"  />
          <el-table-column prop="employeeName" label="申请人" width="100"  />
          <el-table-column prop="contractType" label="合同类型" width="150" show-overflow-tooltip  />
          <el-table-column prop="department" label="申请部门" width="120"  />
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityTag(scope.row.priority)" size="small">
                {{ getPriorityText(scope.row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submitTime" label="提交时间" width="150"  />
          <el-table-column prop="remainingHours" label="剩余时间" width="100">
            <template #default="scope">
              <el-tag :type="getRemainingHoursTag(scope.row.remainingHours)" size="small">
                {{ scope.row.remainingHours }}小时
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button size="small" type="primary" link @click="handleViewContract(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 批量审批操作 -->
      <el-card class="approval-action-card" shadow="never">
        <template #header>
          <span>批量审批操作</span>
        </template>
        
        <el-form ref="batchFormRef" :model="batchForm" :rules="batchRules" label-width="120px">
          <el-form-item label="审批结果" prop="result">
            <el-radio-group v-model="batchForm.result">
              <el-radio value="APPROVED">
                <el-icon class="approval-icon approved"><Check /></el-icon>
                批量通过
              </el-radio>
              <el-radio value="REJECTED">
                <el-icon class="approval-icon rejected"><Close /></el-icon>
                批量驳回
              </el-radio>
              <el-radio value="RETURNED">
                <el-icon class="approval-icon returned"><Back /></el-icon>
                批量退回
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="审批策略" prop="strategy">
            <el-radio-group v-model="batchForm.strategy">
              <el-radio value="all">全部应用相同结果</el-radio>
              <el-radio value="conditional">按条件分别处理</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="batchForm.strategy === 'conditional'" label="条件设置">
            <div class="conditional-rules">
              <div
                v-for="(rule, index) in conditionalRules"
                :key="index"
                class="rule-item"
              >
                <el-select v-model="rule.condition" placeholder="选择条件" style="width: 150px;">
                  <el-option label="优先级=紧急" value="priority_urgent"  />
                  <el-option label="优先级=高" value="priority_high"  />
                  <el-option label="剩余时间<8小时" value="remaining_lt_8"  />
                  <el-option label="剩余时间<24小时" value="remaining_lt_24"  />
                </el-select>
                <el-select v-model="rule.action" placeholder="选择操作" style="width: 120px;">
                  <el-option label="通过" value="APPROVED"  />
                  <el-option label="驳回" value="REJECTED"  />
                  <el-option label="退回" value="RETURNED"  />
                </el-select>
                <el-button size="small" type="danger" @click="removeRule(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              <el-button size="small" type="primary" @click="addRule">
                <el-icon><Plus /></el-icon>
                添加规则
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="统一审批意见" prop="comment">
            <el-input
              v-model="batchForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入统一的审批意见，将应用到所有选中的合同"
              maxlength="500"
              show-word-limit
              />
          </el-form-item>

          <el-form-item label="个性化意见">
            <el-switch
              v-model="batchForm.allowCustomComment"
              active-text="允许为每个合同设置不同意见"
              inactive-text="使用统一意见"
             />
          </el-form-item>

          <el-form-item v-if="batchForm.allowCustomComment" label="个性化设置">
            <div class="custom-comments">
              <div
                v-for="contract in selectedContracts"
                :key="contract.id"
                class="custom-comment-item"
              >
                <div class="contract-info">
                  <span class="contract-number">{{ contract.contractNumber }}</span>
                  <span class="employee-name">{{ contract.employeeName }}</span>
                </div>
                <el-input
                  v-model="contract.customComment"
                  placeholder="为此合同输入特定审批意见"
                  style="flex: 1"
                  />
              </div>
            </div>
          </el-form-item>

          <el-form-item label="通知设置">
            <el-checkbox-group v-model="batchForm.notifications">
              <el-checkbox value="email">邮件通知</el-checkbox>
              <el-checkbox value="sms">短信通知</el-checkbox>
              <el-checkbox value="system">系统通知</el-checkbox>
              <el-checkbox value="wechat">微信通知</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 预览结果 -->
      <el-card v-if="selectedContracts.length > 0" class="preview-card" shadow="never">
        <template #header>
          <span>操作预览</span>
        </template>
        
        <div class="preview-summary">
          <el-alert
            :title="`将对 ${selectedContracts.length} 个合同执行${getResultText(batchForm.result)}操作`"
            type="info"
            :closable="false"
           />
        </div>

        <el-table :data="previewData" style="width: 100%; margin-top: 16px;" max-height="200">
          <el-table-column prop="contractNumber" label="合同编号" width="120"  />
          <el-table-column prop="employeeName" label="申请人" width="100"  />
          <el-table-column prop="action" label="操作" width="100">
            <template #default="scope">
              <el-tag :type="getActionTag(scope.row.action)" size="small">
                {{ getResultText(scope.row.action) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="审批意见" show-overflow-tooltip  />
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handlePreview">预览结果</el-button>
        <el-button
          type="primary"
          @click="handleSubmitBatchApproval"
          :loading="loading"
          :disabled="selectedContracts.length === 0"
        >
          执行批量审批
        </el-button>
      </div>
    </template>

    <!-- 合同详情对话框 -->
    <ContractDetailDialog
      v-model:visible="detailDialogVisible"
      :contract="currentContract"
    />
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'BatchApprovalDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  Close,
  Back,
  Delete,
  Plus
} from '@element-plus/icons-vue'
import ContractDetailDialog from './ContractDetailDialog.vue'

// Props
interface Props {
  visible: boolean
   
  contracts: unknown[]
}

const props = withDefaults(defineProps<Props>(), {
  contracts: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const contractsTableRef = ref()
const batchFormRef = ref()
const loading = ref(false)
const detailDialogVisible = ref(false)
const currentContract = ref(null)
const selectedContracts = ref<any[]>([]) // 修复类型：never[] → any[]

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 批量审批表单
const batchForm = reactive({
  result: '',
  strategy: 'all',
  comment: '',
  allowCustomComment: false,
  notifications: ['system']
})

// 条件规则
const conditionalRules = ref([
  { condition: 'priority_urgent', action: 'APPROVED' as unknown } // 临时修复枚举类型
])

// 表单验证规则
const batchRules = {
  result: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    { min: 10, message: '审批意见至少10个字符', trigger: 'blur' }
  ]
}

// 计算属性
const urgentCount = computed(() => props.contracts.filter(c => c.priority === 'URGENT').length)
const overdueCount = computed(() => props.contracts.filter(c => c.remainingHours <= 8).length)
const differentTypesCount = computed(() => new Set(props.contracts.map(c => c.contractType)).size)
const allSelected = computed(() => selectedContracts.value.length === props.contracts.length)

// 预览数据
const previewData = computed(() => {
  return selectedContracts.value.map(contract => ({
    contractNumber: contract.contractNumber,
    employeeName: contract.employeeName,
    action: getContractAction(contract),
    comment: getContractComment(contract)
  }))
})

// 获取合同操作
   
const getContractAction = (contract: unknown) => {
  if (batchForm.strategy === 'conditional') {
    for (const rule of conditionalRules.value) {
      if (matchesCondition(contract, rule.condition)) {
        return rule.action
      }
    }
  }
  return batchForm.result
}

// 获取合同意见
   
const getContractComment = (contract: unknown) => {
  if (batchForm.allowCustomComment && contract.customComment) {
    return contract.customComment
  }
  return batchForm.comment
}

// 匹配条件
   
const matchesCondition = (contract: unknown, condition: string) => {
  switch (condition) {
    case 'priority_urgent':
      return contract.priority === 'URGENT'
    case 'priority_high':
      return contract.priority === 'HIGH'
    case 'remaining_lt_8':
      return contract.remainingHours < 8
    case 'remaining_lt_24':
      return contract.remainingHours < 24
    default:
      return false
  }
}

// 选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedContracts.value = selection
  
  // 为新选中的合同初始化自定义意见
  selection.forEach(contract => {
    if (!contract.customComment) {
      contract.customComment = ''
    }
  })
}

// 全选/取消全选
const handleSelectAll = () => {
  if (allSelected.value) {
    contractsTableRef.value.clearSelection()
  } else {
    props.contracts.forEach(contract => {
      contractsTableRef.value.toggleRowSelection(contract, true)
    })
  }
}

// 选择紧急合同
const handleSelectUrgent = () => {
  contractsTableRef.value.clearSelection()
  props.contracts.forEach(contract => {
    if (contract.priority === 'URGENT') {
      contractsTableRef.value.toggleRowSelection(contract, true)
    }
  })
}

// 查看合同
   
const handleViewContract = (contract: unknown) => {
  currentContract.value = contract
  detailDialogVisible.value = true
}

// 添加规则
const addRule = () => {
  conditionalRules.value.push({ condition: '', action: '' })
}

// 删除规则
const removeRule = (index: number) => {
  conditionalRules.value.splice(index, 1)
}

// 预览结果
const handlePreview = () => {
  if (selectedContracts.value.length === 0) {
    ElMessage.warning('请先选择要审批的合同')
    return
  }
  
  if (!batchForm.result) {
    ElMessage.warning('请选择审批结果')
    return
  }
  
  ElMessage.success('预览结果已更新')
}

// 提交批量审批
const handleSubmitBatchApproval = async () => {
  try {
    await batchFormRef.value.validate()
    
    if (selectedContracts.value.length === 0) {
      ElMessage.warning('请选择要审批的合同')
      return
    }
    
    const confirmMessage = `确定要对 ${selectedContracts.value.length} 个合同执行${getResultText(batchForm.result)}操作吗？`
    await ElMessageBox.confirm(confirmMessage, '确认批量审批', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    loading.value = true
    
    // 模拟批量审批
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    ElMessage.success(`批量审批完成，共处理 ${selectedContracts.value.length} 个合同`)
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('批量审批失败:', error)
      ElMessage.error('批量审批失败')
    }
  } finally {
    loading.value = false
  }
}

// 获取优先级标签
const getPriorityTag = (priority: string) => {
  switch (priority) {
    case 'URGENT': return 'danger'
    case 'HIGH': return 'warning'
    case 'MEDIUM': return 'primary'
    case 'LOW': return 'info'
    default: return ''
  }
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'URGENT': return '紧急'
    case 'HIGH': return '高'
    case 'MEDIUM': return '中'
    case 'LOW': return '低'
    default: return priority
  }
}

// 获取剩余时间标签
const getRemainingHoursTag = (hours: number) => {
  if (hours <= 8) return 'danger'
  if (hours <= 24) return 'warning'
  return 'success'
}

// 获取结果文本
const getResultText = (result: string) => {
  switch (result) {
    case 'APPROVED': return '通过'
    case 'REJECTED': return '驳回'
    case 'RETURNED': return '退回'
    default: return result
  }
}

// 获取操作标签
const getActionTag = (action: string) => {
  switch (action) {
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    case 'RETURNED': return 'warning'
    default: return 'info'
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  selectedContracts.value = []
  Object.assign(batchForm, {
    result: '',
    strategy: 'all',
    comment: '',
    allowCustomComment: false,
    notifications: ['system']
  })
  conditionalRules.value = [{ condition: 'priority_urgent', action: 'APPROVED' as unknown }] // 临时修复枚举类型
}
</script>

<style scoped>
.batch-approval-container {
  max-height: 80vh;
  overflow-y: auto;
}

.overview-card,
.contracts-card,
.approval-action-card,
.preview-card {
  margin-bottom: 16px;
}

.overview-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.overview-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #909399;
}

.contracts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.approval-icon {
  margin-right: 4px;
}

.approval-icon.approved {
  color: #67c23a;
}

.approval-icon.rejected {
  color: #f56c6c;
}

.approval-icon.returned {
  color: #e6a23c;
}

.conditional-rules {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.rule-item:last-child {
  margin-bottom: 12px;
}

.custom-comments {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
  max-height: 200px;
  overflow-y: auto;
}

.custom-comment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.contract-info {
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.contract-number {
  font-weight: 600;
  color: #303133;
  font-size: 12px;
}

.employee-name {
  color: #606266;
  font-size: 12px;
}

.preview-summary {
  margin-bottom: 16px;
}

.dialog-footer {
  text-align: right;
}
</style>
