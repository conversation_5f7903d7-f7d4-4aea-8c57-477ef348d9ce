<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="template-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="模板名称" prop="templateName">
            <el-input
              v-model="formData.templateName"
              placeholder="请输入模板名称"
              :disabled="mode === 'view'"
              />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板编码" prop="templateCode">
            <el-input
              v-model="formData.templateCode"
              placeholder="请输入模板编码"
              :disabled="mode === 'view'"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同类型" prop="contractType">
            <el-select
              v-model="formData.contractType"
              placeholder="请选择合同类型"
              style="width: 100%"
              :disabled="mode === 'view'"
            >
              <el-option
                v-for="item in contractTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本号" prop="version">
            <el-input
              v-model="formData.version"
              placeholder="请输入版本号"
              :disabled="mode === 'view'"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="模板描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入模板描述"
          :disabled="mode === 'view'"
          />
      </el-form-item>

      <el-form-item label="模板内容" prop="templateContent">
        <div class="template-editor">
          <el-tabs v-model="activeTab" class="editor-tabs">
            <el-tab-pane label="富文本编辑" name="rich">
              <div class="rich-editor">
                <el-input
                  v-model="formData.templateContent"
                  type="textarea"
                  :rows="15"
                  placeholder="请输入模板内容，可使用变量如：{{employeeName}}、{{startDate}}等"
                  :disabled="mode === 'view'"
                  />
              </div>
            </el-tab-pane>
            <el-tab-pane label="变量管理" name="variables">
              <div class="variables-panel">
                <div class="variables-header">
                  <span>模板变量</span>
                  <el-button
                    v-if="mode !== 'view'"
                    size="small"
                    type="primary"
                    @click="handleAddVariable"
                  >
                    <el-icon><Plus /></el-icon>
                    添加变量
                  </el-button>
                </div>
                <el-table :data="formData.variables" style="width: 100%">
                  <el-table-column prop="variableName" label="变量名" width="150"  />
                  <el-table-column prop="variableLabel" label="显示名称" width="150"  />
                  <el-table-column prop="variableType" label="类型" width="100">
                    <template #default="scope">
                      <el-tag size="small">{{ getVariableTypeText(scope.row.variableType) }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="isRequired" label="必填" width="80">
                    <template #default="scope">
                      <el-tag :type="scope.row.isRequired ? 'danger' : 'info'" size="small">
                        {{ scope.row.isRequired ? '是' : '否' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="defaultValue" label="默认值" show-overflow-tooltip  />
                  <el-table-column v-if="mode !== 'view'" label="操作" width="120">
                    <template #default="scope">
                      <el-button
                        size="small"
                        type="primary"
                        link
                        @click="handleEditVariable(scope.row, scope.$index)"
                      >
                        编辑
                      </el-button>
                      <el-button
                        size="small"
                        type="danger"
                        link
                        @click="handleDeleteVariable(scope.$index)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
            <el-tab-pane label="预览" name="preview">
              <div class="preview-panel">
                <div class="preview-toolbar">
                  <el-button size="small" @click="handlePreview">
                    <el-icon><View /></el-icon>
                    刷新预览
                  </el-button>
                </div>
                <div class="preview-content" v-html="previewContent"></div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form-item>

      <el-form-item label="状态">
        <el-switch
          v-model="formData.isActive"
          active-text="启用"
          inactive-text="禁用"
          :disabled="mode === 'view'"
         />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit" :loading="loading">
          {{ mode === 'add' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>

    <!-- 变量编辑对话框 -->
    <VariableEditorDialog
      v-model:visible="variableDialogVisible"
      :variable="currentVariable"
      :mode="variableMode"
      @success="handleVariableSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TemplateEditorDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, View } from '@element-plus/icons-vue'
import VariableEditorDialog from './VariableEditorDialog.vue'
import { contractTemplateApi } from '@/api/contract'
import type { ContractTemplate, TemplateVariable } from '@/api/contract'

// Props
interface Props {
  visible: boolean
   
  template?: unknown
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  template: null,
  mode: 'add'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const activeTab = ref('rich')
const previewContent = ref('')
const variableDialogVisible = ref(false)
const variableMode = ref<'add' | 'edit'>('add')
const currentVariable = ref(null)
const currentVariableIndex = ref(-1)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add': return '新建模板'
    case 'edit': return '编辑模板'
    case 'view': return '查看模板'
    default: return '模板管理'
  }
})

// 表单数据
const formData = reactive({
  templateName: '',
  templateCode: '',
  contractType: '',
  version: '1.0',
  description: '',
  templateContent: '',
  isActive: true,
  variables: [] as unknown[]
})

// 表单验证规则
const formRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  templateCode: [
    { required: true, message: '请输入模板编码', trigger: 'blur' }
  ],
  contractType: [
    { required: true, message: '请选择合同类型', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  templateContent: [
    { required: true, message: '请输入模板内容', trigger: 'blur' }
  ]
}

// 合同类型选项
const contractTypeOptions = [
  { label: '事业编制聘用合同', value: 'CAREER_ESTABLISHMENT' },
  { label: '人事代理合同', value: 'PERSONNEL_AGENCY' },
  { label: '劳务协议', value: 'LABOR_AGREEMENT' },
  { label: '劳务派遣合同', value: 'LABOR_DISPATCH' },
  { label: '兼职协议', value: 'PART_TIME_AGREEMENT' },
  { label: '项目合作协议', value: 'PROJECT_COOPERATION' }
]

// 监听模板数据变化
watch(() => props.template, (newTemplate) => {
  if (newTemplate) {
    Object.assign(formData, {
      templateName: newTemplate.templateName || '',
      templateCode: newTemplate.templateCode || '',
      contractType: newTemplate.contractType || '',
      version: newTemplate.version || '1.0',
      description: newTemplate.description || '',
      templateContent: newTemplate.templateContent || '',
      isActive: newTemplate.isActive !== false,
      variables: newTemplate.variables || []
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      templateName: '',
      templateCode: '',
      contractType: '',
      version: '1.0',
      description: '',
      templateContent: '',
      isActive: true,
      variables: []
    })
  }
}, { immediate: true })

// 添加变量
const handleAddVariable = () => {
  currentVariable.value = null
  currentVariableIndex.value = -1
  variableMode.value = 'add'
  variableDialogVisible.value = true
}

// 编辑变量
   
const handleEditVariable = (variable: unknown, index: number) => {
  currentVariable.value = { ...variable }
  currentVariableIndex.value = index
  variableMode.value = 'edit'
  variableDialogVisible.value = true
}

// 删除变量
const handleDeleteVariable = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个变量吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    formData.variables.splice(index, 1)
    ElMessage.success('删除成功')
  } catch (__error) {
    // 用户取消删除
  }
}

// 变量编辑成功
   
const handleVariableSuccess = (variable: unknown) => {
  if (variableMode.value === 'add') {
    formData.variables.push(variable)
  } else {
    formData.variables[currentVariableIndex.value] = variable
  }
  ElMessage.success('变量保存成功')
}

// 预览模板
const handlePreview = () => {
  let content = formData.templateContent
  
  // 替换变量为示例值
  formData.variables.forEach(variable => {
    const placeholder = `{{${variable.variableName}}}`
    const sampleValue = getSampleValue(variable)
    content = content.replace(new RegExp(placeholder, 'g'), sampleValue)
  })
  
  previewContent.value = content.replace(/\n/g, '<br>')
}

// 获取变量示例值
   
const getSampleValue = (variable: unknown) => {
  switch (variable.variableType) {
    case 'TEXT': return variable.defaultValue || '示例文本'
    case 'NUMBER': return variable.defaultValue || '123'
    case 'DATE': return variable.defaultValue || '2025-06-19'
    case 'SELECT': return variable.options?.[0] || '选项1'
    case 'BOOLEAN': return variable.defaultValue === 'true' ? '是' : '否'
    default: return variable.defaultValue || '示例值'
  }
}

// 获取变量类型文本
const getVariableTypeText = (type: string) => {
  switch (type) {
    case 'TEXT': return '文本'
    case 'NUMBER': return '数字'
    case 'DATE': return '日期'
    case 'SELECT': return '选择'
    case 'BOOLEAN': return '布尔'
    default: return type
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 构建模板数据
    const templateData: ContractTemplate = {
      templateName: formData.templateName,
      templateCode: formData.templateCode,
      contractType: formData.contractType,
      version: formData.version,
      isActive: formData.isActive,
      templateContent: formData.templateContent,
      variables: formData.variables as TemplateVariable[],
      description: formData.description
    }
    
    // 根据模式调用不同的API
    if (props.mode === 'add') {
      await contractTemplateApi.createTemplate(templateData)
    } else if (props.mode === 'edit' && props.template?.id) {
      await contractTemplateApi.updateTemplate(props.template.id, templateData)
    }
    
    ElMessage.success(props.mode === 'add' ? '创建成功' : '保存成功')
    emit('success')
    handleClose()
  } catch (__error) {
    console.error('保存模板失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}
</script>

<style scoped>
.template-form {
  max-height: 70vh;
  overflow-y: auto;
}

.template-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.editor-tabs {
  min-height: 400px;
}

.rich-editor {
  padding: 16px;
}

.variables-panel {
  padding: 16px;
}

.variables-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
}

.preview-panel {
  padding: 16px;
}

.preview-toolbar {
  margin-bottom: 16px;
}

.preview-content {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  min-height: 200px;
  background: #fafafa;
  line-height: 1.6;
}

.dialog-footer {
  text-align: right;
}
</style>
