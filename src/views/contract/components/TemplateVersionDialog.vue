<template>
  <el-dialog
    v-model="dialogVisible"
    title="版本历史"
    width="70%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="version-container">
      <!-- 版本操作工具栏 -->
      <div class="version-toolbar">
        <div class="toolbar-left">
          <span class="template-info">
            {{ template?.templateName }} 的版本历史
          </span>
        </div>
        <div class="toolbar-right">
          <el-button size="small" type="success" @click="handleCreateVersion">
            <el-icon><Plus /></el-icon>
            创建新版本
          </el-button>
          <el-button size="small" @click="handleRefreshVersions">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 版本列表 -->
      <el-table :data="versionList" style="width: 100%">
        <el-table-column prop="version" label="版本号" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'info'" size="small">
              v{{ scope.row.version }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="版本说明" show-overflow-tooltip  />
        <el-table-column prop="isActive" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'info'" size="small">
              {{ scope.row.isActive ? '当前版本' : '历史版本' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150"  />
        <el-table-column prop="createBy" label="创建人" width="100"  />
        <el-table-column prop="usageCount" label="使用次数" width="100" align="center"  />
        <el-table-column label="操作" width="250">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleViewVersion(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="success" link @click="handleCompareVersion(scope.row)">
              对比
            </el-button>
            <el-button 
              v-if="!scope.row.isActive"
              size="small" 
              type="warning" 
              link 
              @click="handleActivateVersion(scope.row)"
            >
              激活
            </el-button>
            <el-button size="small" type="info" link @click="handleCopyVersion(scope.row)">
              复制
            </el-button>
            <el-button 
              v-if="!scope.row.isActive && scope.row.usageCount === 0"
              size="small" 
              type="danger" 
              link 
              @click="handleDeleteVersion(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 版本详情对话框 -->
    <el-dialog
      v-model="versionDetailVisible"
      :title="`版本 v${currentVersion?.version} 详情`"
      width="60%"
      append-to-body
    >
      <div v-if="currentVersion" class="version-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="版本号">
            v{{ currentVersion.version }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentVersion.isActive ? 'success' : 'info'" size="small">
              {{ currentVersion.isActive ? '当前版本' : '历史版本' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ currentVersion.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ currentVersion.createBy }}
          </el-descriptions-item>
          <el-descriptions-item label="使用次数">
            {{ currentVersion.usageCount }}
          </el-descriptions-item>
          <el-descriptions-item label="变量数量">
            {{ currentVersion.variableCount }}
          </el-descriptions-item>
          <el-descriptions-item label="版本说明" :span="2">
            {{ currentVersion.description }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="content-preview">
          <h4>模板内容预览</h4>
          <div class="content-box">
            {{ currentVersion.templateContent }}
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 版本对比对话框 -->
    <el-dialog
      v-model="compareDialogVisible"
      title="版本对比"
      width="80%"
      append-to-body
    >
      <div class="compare-container">
        <div class="compare-header">
          <div class="compare-selector">
            <span>对比版本：</span>
            <el-select v-model="compareVersion" placeholder="选择对比版本" style="width: 200px;">
              <el-option
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                v-for="version in versionList.filter((v: unknown) => (v as unknown)?.id !== (currentVersion as unknown)?.id)"
                :key="version.id"
                :label="`v${version.version}`"
                :value="version.id"
              />
            </el-select>
          </div>
        </div>

        <div v-if="compareVersion" class="compare-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="compare-panel">
                <h4>v{{ currentVersion?.version }} ({{ currentVersion?.isActive ? '当前版本' : '历史版本' }})</h4>
                <div class="compare-box">
                  {{ currentVersion?.templateContent }}
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="compare-panel">
                <h4>v{{ getVersionById(compareVersion)?.version }} (对比版本)</h4>
                <div class="compare-box">
                  {{ getVersionById(compareVersion)?.templateContent }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>

    <!-- 创建版本对话框 -->
    <el-dialog
      v-model="createVersionVisible"
      title="创建新版本"
      width="500px"
      append-to-body
    >
      <el-form ref="versionFormRef" :model="versionForm" :rules="versionRules" label-width="100px">
        <el-form-item label="版本号" prop="version">
          <el-input v-model="versionForm.version" placeholder="请输入版本号，如：2.0"   />
        </el-form-item>
        <el-form-item label="版本说明" prop="description">
          <el-input
            v-model="versionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入版本说明"
            />
        </el-form-item>
        <el-form-item label="基于版本">
          <el-select v-model="versionForm.baseVersion" placeholder="选择基于的版本" style="width: 100%">
            <el-option
              v-for="version in versionList"
              :key="version.id"
              :label="`v${version.version}`"
              :value="version.id"
             />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createVersionVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitVersion" :loading="createLoading">
            创建
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TemplateVersionDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
   
  template?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  template: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const versionDetailVisible = ref(false)
const compareDialogVisible = ref(false)
const createVersionVisible = ref(false)
const createLoading = ref(false)
const currentVersion = ref<unknown>(null) // 修复类型：null → any
const compareVersion = ref('')
const versionFormRef = ref()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 版本表单
const versionForm = reactive({
  version: '',
  description: '',
  baseVersion: ''
})

// 版本表单验证规则
const versionRules = {
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入版本说明', trigger: 'blur' }
  ]
}

// 模拟版本列表数据
const versionList = ref([
  {
    id: '1',
    version: '2.1',
    description: '修复了变量替换的问题，优化了模板格式',
    isActive: true,
    createTime: '2025-06-19 10:30:00',
    createBy: '张三',
    usageCount: 15,
    variableCount: 12,
    templateContent: '这是最新版本的模板内容...'
  },
  {
    id: '2',
    version: '2.0',
    description: '重大更新：新增了多个变量类型支持',
    isActive: false,
    createTime: '2025-06-15 14:20:00',
    createBy: '李四',
    usageCount: 8,
    variableCount: 10,
    templateContent: '这是2.0版本的模板内容...'
  },
  {
    id: '3',
    version: '1.0',
    description: '初始版本',
    isActive: false,
    createTime: '2025-06-01 09:00:00',
    createBy: '王五',
    usageCount: 0,
    variableCount: 8,
    templateContent: '这是1.0版本的模板内容...'
  }
])

// 根据ID获取版本
const getVersionById = (id: string) => {
  return versionList.value.find(v => v.id === id)
}

// 查看版本详情
   
const handleViewVersion = (version: unknown) => {
  currentVersion.value = version
  versionDetailVisible.value = true
}

// 对比版本
   
const handleCompareVersion = (version: unknown) => {
  currentVersion.value = version
  compareVersion.value = ''
  compareDialogVisible.value = true
}

// 激活版本
   
const handleActivateVersion = async (version: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要激活版本 v${version.version} 吗？这将使其成为当前使用的版本。`,
      '确认激活',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 更新版本状态
    versionList.value.forEach(v => {
      v.isActive = v.id === version.id
    })
    
    ElMessage.success(`版本 v${version.version} 已激活`)
    emit('success')
  } catch (__error) {
    // 用户取消
  }
}

// 复制版本
   
const handleCopyVersion = (version: unknown) => {
  versionForm.version = `${parseFloat(version.version) + 0.1}`
  versionForm.description = `基于 v${version.version} 的副本`
  versionForm.baseVersion = version.id
  createVersionVisible.value = true
}

// 删除版本
   
const handleDeleteVersion = async (version: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除版本 v${version.version} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = versionList.value.findIndex(v => v.id === version.id)
    if (index > -1) {
      versionList.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
    emit('success')
  } catch (__error) {
    // 用户取消
  }
}

// 创建新版本
const handleCreateVersion = () => {
  Object.assign(versionForm, {
    version: '',
    description: '',
    baseVersion: versionList.value.find(v => v.isActive)?.id || ''
  })
  createVersionVisible.value = true
}

// 提交新版本
const handleSubmitVersion = async () => {
  try {
    await versionFormRef.value.validate()
    createLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 添加新版本到列表
    const newVersion = {
      id: Date.now().toString(),
      version: versionForm.version,
      description: versionForm.description,
      isActive: false,
      createTime: new Date().toLocaleString(),
      createBy: '当前用户',
      usageCount: 0,
      variableCount: 10,
      templateContent: '新版本的模板内容...'
    }
    
    versionList.value.unshift(newVersion)
    
    ElMessage.success('新版本创建成功')
    createVersionVisible.value = false
    emit('success')
  } catch (__error) {
    console.error('创建版本失败:', error)
    ElMessage.error('创建失败')
  } finally {
    createLoading.value = false
  }
}

// 刷新版本列表
const handleRefreshVersions = () => {
  ElMessage.success('版本列表已刷新')
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.version-container {
  max-height: 70vh;
  overflow-y: auto;
}

.version-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.template-info {
  font-weight: 600;
  color: #303133;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.version-detail {
  margin-top: 16px;
}

.content-preview {
  margin-top: 20px;
}

.content-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.content-box {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
  max-height: 200px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.compare-container {
  max-height: 60vh;
  overflow-y: auto;
}

.compare-header {
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.compare-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.compare-content {
  margin-top: 16px;
}

.compare-panel h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.compare-box {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
  height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.dialog-footer {
  text-align: right;
}
</style>
