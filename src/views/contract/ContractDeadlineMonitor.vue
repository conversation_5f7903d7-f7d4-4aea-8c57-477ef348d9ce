<template>
  <div class="contract-deadline-monitor">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>签署时限监控</h2>
      <p>监控合同签署时限，智能预警超时风险，确保合同及时签署</p>
    </div>

    <!-- 监控概览 -->
    <el-card class="monitor-overview" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>时限监控概览</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleConfigAlert">
              <el-icon><Setting /></el-icon>
              预警配置
            </el-button>
          </div>
        </div>
      </template>

      <!-- 监控统计 -->
      <div class="monitor-stats">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ monitorStats.total }}</div>
                <div class="stat-label">监控总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card normal">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ monitorStats.normal }}</div>
                <div class="stat-label">正常</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card warning">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ monitorStats.warning }}</div>
                <div class="stat-label">即将超时</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card overdue">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ monitorStats.overdue }}</div>
                <div class="stat-label">已超时</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card urgent">
              <div class="stat-icon">
                <el-icon><Bell /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ monitorStats.urgent }}</div>
                <div class="stat-label">紧急处理</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card completed">
              <div class="stat-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ monitorStats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 时限分布图 -->
      <div class="deadline-chart">
        <h4>签署时限分布</h4>
        <div class="chart-container">
          <el-progress
            :percentage="30"
            :stroke-width="20"
            :color="['#67c23a', '#e6a23c', '#f56c6c']"
            :show-text="false"
           />
          <div class="chart-legend">
            <div class="legend-item">
              <div class="legend-color normal"></div>
              <span>正常 (30%)</span>
            </div>
            <div class="legend-item">
              <div class="legend-color warning"></div>
              <span>即将超时 (45%)</span>
            </div>
            <div class="legend-item">
              <div class="legend-color overdue"></div>
              <span>已超时 (25%)</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 监控列表 -->
    <el-card class="monitor-list" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>时限监控列表</h3>
          <div class="header-actions">
            <el-button @click="handleBatchRemind">
              <el-icon><Message /></el-icon>
              批量提醒
            </el-button>
            <el-button type="primary" @click="handleExportReport">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="合同状态">
            <el-select v-model="filterForm.contractStatus" placeholder="请选择状态" clearable>
              <el-option label="待签署" value="pending"  />
              <el-option label="部分签署" value="partial"  />
              <el-option label="已签署" value="signed"  />
              <el-option label="已过期" value="expired"  />
            </el-select>
          </el-form-item>
          <el-form-item label="时限状态">
            <el-select v-model="filterForm.deadlineStatus" placeholder="请选择时限状态" clearable>
              <el-option label="正常" value="normal"  />
              <el-option label="即将超时" value="warning"  />
              <el-option label="已超时" value="overdue"  />
              <el-option label="紧急" value="urgent"  />
            </el-select>
          </el-form-item>
          <el-form-item label="签署人">
            <el-input 
              v-model="filterForm.signerName" 
              placeholder="请输入签署人姓名"
              clearable
              />
          </el-form-item>
          <el-form-item label="截止日期">
            <el-date-picker
              v-model="filterForm.deadlineRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 监控表格 -->
      <el-table :data="monitorList" style="width: 100%" :row-class-name="getRowClassName">
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="contractNumber" label="合同编号" width="150"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="contractStatus" label="合同状态" width="100">
          <template #default="scope">
            <el-tag :type="getContractStatusType(scope.row.contractStatus)" size="small">
              {{ getContractStatusText(scope.row.contractStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="signerName" label="签署人" width="100"  />
        <el-table-column prop="signerRole" label="签署角色" width="100"  />
        <el-table-column prop="deadline" label="签署截止时间" width="150"  />
        <el-table-column prop="remainingTime" label="剩余时间" width="120">
          <template #default="scope">
            <span :class="getRemainingTimeClass(scope.row.remainingTime)">
              {{ formatRemainingTime(scope.row.remainingTime) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="deadlineStatus" label="时限状态" width="100">
          <template #default="scope">
            <el-tag :type="getDeadlineStatusType(scope.row.deadlineStatus)" size="small">
              {{ getDeadlineStatusText(scope.row.deadlineStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remindCount" label="提醒次数" width="100"  />
        <el-table-column prop="lastRemindTime" label="最后提醒时间" width="150"  />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleViewDetail(scope.row)">
              查看详情
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleSendReminder(scope.row)"
            >
              发送提醒
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleExtendDeadline(scope.row)"
              v-if="scope.row.deadlineStatus === 'overdue'"
            >
              延期
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="签署详情" width="800px">
      <div class="signing-detail" v-if="currentContract">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="合同编号">{{ currentContract.contractNumber }}</el-descriptions-item>
          <el-descriptions-item label="员工姓名">{{ currentContract.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="合同状态">
            <el-tag :type="getContractStatusType(currentContract.contractStatus)" size="small">
              {{ getContractStatusText(currentContract.contractStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="签署人">{{ currentContract.signerName }}</el-descriptions-item>
          <el-descriptions-item label="签署角色">{{ currentContract.signerRole }}</el-descriptions-item>
          <el-descriptions-item label="签署截止时间">{{ currentContract.deadline }}</el-descriptions-item>
          <el-descriptions-item label="剩余时间">
            <span :class="getRemainingTimeClass(currentContract.remainingTime)">
              {{ formatRemainingTime(currentContract.remainingTime) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="提醒次数">{{ currentContract.remindCount }}</el-descriptions-item>
        </el-descriptions>

        <div class="signing-timeline">
          <h4>签署进度</h4>
          <el-timeline>
            <el-timeline-item
              v-for="milestone in signingMilestones"
              :key="milestone.id"
              :timestamp="milestone.timestamp"
              :type="milestone.type"
            >
              <div class="milestone-content">
                <div class="milestone-title">{{ milestone.title }}</div>
                <div class="milestone-description">{{ milestone.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>

        <div class="remind-history">
          <h4>提醒历史</h4>
          <el-table :data="remindHistory" style="width: 100%">
            <el-table-column prop="remindTime" label="提醒时间" width="150"  />
            <el-table-column prop="remindType" label="提醒方式" width="100"  />
            <el-table-column prop="recipient" label="接收人" width="100"  />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'sent' ? 'success' : 'warning'" size="small">
                  {{ scope.row.status === 'sent' ? '已发送' : '发送中' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="提醒内容" show-overflow-tooltip  />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 预警配置对话框 -->
    <el-dialog v-model="alertConfigDialogVisible" title="预警配置" width="700px">
      <div class="alert-config">
        <el-form ref="alertConfigFormRef" :model="alertConfig" :rules="alertConfigRules" label-width="120px">
          <el-form-item label="预警级别设置">
            <el-table :data="alertLevels" style="width: 100%">
              <el-table-column prop="level" label="级别" width="100"  />
              <el-table-column prop="threshold" label="阈值（小时）" width="120">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.threshold"
                    :min="0"
                    :max="168"
                    controls-position="right"
                    size="small"
                    />
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述"  />
              <el-table-column prop="enabled" label="启用" width="80">
                <template #default="scope">
                  <el-switch v-model="scope.row.enabled"  />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>

          <el-form-item label="提醒方式" prop="remindMethods">
            <el-checkbox-group v-model="alertConfig.remindMethods">
              <el-checkbox value="email">邮件提醒</el-checkbox>
              <el-checkbox value="sms">短信提醒</el-checkbox>
              <el-checkbox value="system">系统消息</el-checkbox>
              <el-checkbox value="wechat">企业微信</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="提醒频率" prop="remindFrequency">
            <el-radio-group v-model="alertConfig.remindFrequency">
              <el-radio value="once">仅一次</el-radio>
              <el-radio value="daily">每天</el-radio>
              <el-radio value="hourly">每小时</el-radio>
              <el-radio value="custom">自定义</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="工作时间提醒" prop="workingHoursOnly">
            <el-switch v-model="alertConfig.workingHoursOnly"  />
            <span style="margin-left: 10px; color: #909399;">仅在工作时间发送提醒</span>
          </el-form-item>

          <el-form-item label="提醒模板" prop="reminderTemplate">
            <el-input
              v-model="alertConfig.reminderTemplate"
              type="textarea"
              :rows="4"
              placeholder="请输入提醒模板内容，支持变量：{contractNumber}, {employeeName}, {deadline}"
              />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="alertConfigDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveAlertConfig">保存配置</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 延期申请对话框 -->
    <el-dialog v-model="extendDialogVisible" title="延期申请" width="500px">
      <div class="extend-form">
        <el-form ref="extendFormRef" :model="extendForm" :rules="extendRules" label-width="100px">
          <el-form-item label="延期天数" prop="extendDays">
            <el-input-number
              v-model="extendForm.extendDays"
              :min="1"
              :max="30"
              controls-position="right"
              />
            <span style="margin-left: 10px;">天</span>
          </el-form-item>
          <el-form-item label="延期原因" prop="extendReason">
            <el-input
              v-model="extendForm.extendReason"
              type="textarea"
              :rows="3"
              placeholder="请输入延期原因"
              />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="extendDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmExtend">确认延期</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractDeadlineMonitor'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Setting,
  Document,
  CircleCheck,
  Warning,
  Clock,
  Bell,
  Check,
  Message,
  Download,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const detailDialogVisible = ref(false)
const alertConfigDialogVisible = ref(false)
const extendDialogVisible = ref(false)
const currentContract = ref(null)
const alertConfigFormRef = ref()
const extendFormRef = ref()

// 监控统计
const monitorStats = reactive({
  total: 89,
  normal: 45,
  warning: 28,
  overdue: 12,
  urgent: 4,
  completed: 156
})

// 筛选表单
const filterForm = reactive({
  contractStatus: '',
  deadlineStatus: '',
  signerName: '',
  deadlineRange: []
})

// 预警配置
const alertConfig = reactive({
  remindMethods: ['email', 'system'],
  remindFrequency: 'daily',
  workingHoursOnly: true,
  reminderTemplate: '您好，合同{contractNumber}（员工：{employeeName}）即将于{deadline}到期，请及时处理。'
})

// 预警配置验证规则
const alertConfigRules = {
  remindMethods: [
    { required: true, message: '请选择提醒方式', trigger: 'change' }
  ],
  reminderTemplate: [
    { required: true, message: '请输入提醒模板', trigger: 'blur' }
  ]
}

// 预警级别配置
const alertLevels = ref([
  {
    level: '正常',
    threshold: 72,
    description: '距离截止时间超过72小时',
    enabled: true
  },
  {
    level: '预警',
    threshold: 24,
    description: '距离截止时间24-72小时',
    enabled: true
  },
  {
    level: '紧急',
    threshold: 6,
    description: '距离截止时间6-24小时',
    enabled: true
  },
  {
    level: '严重',
    threshold: 0,
    description: '已超过截止时间',
    enabled: true
  }
])

// 延期表单
const extendForm = reactive({
  extendDays: 7,
  extendReason: ''
})

// 延期验证规则
const extendRules = {
  extendDays: [
    { required: true, message: '请输入延期天数', trigger: 'blur' }
  ],
  extendReason: [
    { required: true, message: '请输入延期原因', trigger: 'blur' }
  ]
}

// 监控列表数据
const monitorList = ref([
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '张三',
    contractStatus: 'pending',
    signerName: '李经理',
    signerRole: '部门经理',
    deadline: '2025-01-25 18:00:00',
    remainingTime: 18,
    deadlineStatus: 'warning',
    remindCount: 2,
    lastRemindTime: '2025-01-23 10:00:00'
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '李四',
    contractStatus: 'partial',
    signerName: '王总',
    signerRole: '总经理',
    deadline: '2025-01-24 16:00:00',
    remainingTime: -2,
    deadlineStatus: 'overdue',
    remindCount: 5,
    lastRemindTime: '2025-01-23 14:00:00'
  },
  {
    id: '3',
    contractNumber: 'HKY2025003',
    employeeName: '王五',
    contractStatus: 'pending',
    signerName: '赵主管',
    signerRole: '人事主管',
    deadline: '2025-01-28 17:00:00',
    remainingTime: 92,
    deadlineStatus: 'normal',
    remindCount: 0,
    lastRemindTime: '-'
  }
])

// 签署进度里程碑
const signingMilestones = ref([
  {
    id: '1',
    timestamp: '2025-01-20 09:00:00',
    type: 'primary',
    title: '合同创建',
    description: '合同已创建，等待签署'
  },
  {
    id: '2',
    timestamp: '2025-01-21 10:00:00',
    type: 'success',
    title: '员工签署',
    description: '员工已完成签署'
  },
  {
    id: '3',
    timestamp: '2025-01-22 11:00:00',
    type: 'warning',
    title: '部门审批',
    description: '等待部门经理审批签署'
  },
  {
    id: '4',
    timestamp: '2025-01-23 14:00:00',
    type: 'danger',
    title: '签署提醒',
    description: '已发送签署提醒'
  }
])

// 提醒历史数据
const remindHistory = ref([
  {
    remindTime: '2025-01-23 10:00:00',
    remindType: '邮件',
    recipient: '李经理',
    status: 'sent',
    content: '合同HKY2025001即将到期，请及时签署'
  },
  {
    remindTime: '2025-01-23 14:00:00',
    remindType: '系统消息',
    recipient: '李经理',
    status: 'sent',
    content: '合同HKY2025001签署截止时间临近'
  },
  {
    remindTime: '2025-01-23 16:00:00',
    remindType: '短信',
    recipient: '李经理',
    status: 'sent',
    content: '紧急：合同HKY2025001即将超时'
  }
])

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleConfigAlert = () => {
  alertConfigDialogVisible.value = true
}

const handleBatchRemind = () => {
  ElMessage.success('批量提醒已发送')
}

const handleExportReport = () => {
  ElMessage.success('导出监控报告')
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    contractStatus: '',
    deadlineStatus: '',
    signerName: '',
    deadlineRange: []
  })
  ElMessage.success('筛选条件已重置')
}

   
const handleViewDetail = (row: unknown) => {
  currentContract.value = row
  detailDialogVisible.value = true
}

   
const handleSendReminder = (row: unknown) => {
  ElMessage.success(`已向${row.signerName}发送提醒`)
  row.remindCount++
  row.lastRemindTime = new Date().toLocaleString()
}

   
const handleExtendDeadline = (row: unknown) => {
  currentContract.value = row
  Object.assign(extendForm, {
    extendDays: 7,
    extendReason: ''
  })
  extendDialogVisible.value = true
}

const handleConfirmExtend = () => {
  extendFormRef.value.validate((valid: boolean) => {
    if (valid) {
      extendDialogVisible.value = false
      ElMessage.success(`合同截止时间已延期${extendForm.extendDays}天`)
      // 更新截止时间
      if (currentContract.value) {
        const newDeadline = new Date(currentContract.value.deadline)
        newDeadline.setDate(newDeadline.getDate() + extendForm.extendDays)
        currentContract.value.deadline = newDeadline.toLocaleString()
        currentContract.value.deadlineStatus = 'normal'
      }
    }
  })
}

const handleSaveAlertConfig = () => {
  alertConfigFormRef.value.validate((valid: boolean) => {
    if (valid) {
      alertConfigDialogVisible.value = false
      ElMessage.success('预警配置已保存')
    }
  })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

   
const getRowClassName = ({ row }: { row: unknown }) => {
  if (row.deadlineStatus === 'overdue') {
    return 'row-overdue'
  }
  if (row.deadlineStatus === 'urgent') {
    return 'row-urgent'
  }
  if (row.deadlineStatus === 'warning') {
    return 'row-warning'
  }
  return ''
}

const getContractStatusType = (status: string) => {
  const statusMap = {
    'pending': 'warning',
    'partial': 'primary',
    'signed': 'success',
    'expired': 'danger'
  }
  return statusMap[status] || 'info'
}

const getContractStatusText = (status: string) => {
  const statusMap = {
    'pending': '待签署',
    'partial': '部分签署',
    'signed': '已签署',
    'expired': '已过期'
  }
  return statusMap[status] || '未知'
}

const getDeadlineStatusType = (status: string) => {
  const statusMap = {
    'normal': 'success',
    'warning': 'warning',
    'overdue': 'danger',
    'urgent': 'danger'
  }
  return statusMap[status] || 'info'
}

const getDeadlineStatusText = (status: string) => {
  const statusMap = {
    'normal': '正常',
    'warning': '即将超时',
    'overdue': '已超时',
    'urgent': '紧急'
  }
  return statusMap[status] || '未知'
}

const getRemainingTimeClass = (remainingTime: number) => {
  if (remainingTime < 0) {
    return 'time-overdue'
  }
  if (remainingTime < 6) {
    return 'time-urgent'
  }
  if (remainingTime < 24) {
    return 'time-warning'
  }
  return 'time-normal'
}

const formatRemainingTime = (remainingTime: number) => {
  if (remainingTime < 0) {
    return `已超时${Math.abs(remainingTime)}小时`
  }
  if (remainingTime < 24) {
    return `${remainingTime}小时`
  }
  const days = Math.floor(remainingTime / 24)
  const hours = remainingTime % 24
  return `${days}天${hours}小时`
}

// 生命周期
onMounted(() => {
  total.value = monitorList.value.length
})
</script>

<style scoped>
.contract-deadline-monitor {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.monitor-overview,
.monitor-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.monitor-stats {
  margin-bottom: 30px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.normal .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.overdue .stat-icon {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stat-card.urgent .stat-icon {
  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
}

.stat-card.completed .stat-icon {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.deadline-chart {
  margin-top: 20px;
}

.deadline-chart h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.chart-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.legend-color.normal {
  background: #67c23a;
}

.legend-color.warning {
  background: #e6a23c;
}

.legend-color.overdue {
  background: #f56c6c;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.time-normal {
  color: #67c23a;
}

.time-warning {
  color: #e6a23c;
}

.time-urgent {
  color: #f56c6c;
}

.time-overdue {
  color: #f56c6c;
  font-weight: 600;
}

.signing-detail {
  margin-top: 20px;
}

.signing-timeline {
  margin-top: 30px;
}

.signing-timeline h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.milestone-content {
  padding: 8px 0;
}

.milestone-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.milestone-description {
  color: #606266;
  font-size: 14px;
}

.remind-history {
  margin-top: 30px;
}

.remind-history h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.alert-config,
.extend-form {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}

:deep(.row-overdue) {
  background-color: #fef0f0;
}

:deep(.row-urgent) {
  background-color: #fdf6ec;
}

:deep(.row-warning) {
  background-color: #fdfaec;
}
</style>