<template>
  <div class="contract-archive-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同归档管理</h2>
      <p>管理合同归档全流程，包括归档申请、审批、存储和查询</p>
    </div>

    <!-- 归档控制台 -->
    <el-card class="archive-console" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>归档控制台</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleBatchArchive">
              <el-icon><FolderAdd /></el-icon>
              批量归档
            </el-button>
          </div>
        </div>
      </template>

      <!-- 归档配置 -->
      <div class="archive-config">
        <el-form :inline="true" :model="archiveConfig" size="small">
          <el-form-item label="归档类型">
            <el-select v-model="archiveConfig.archiveType" placeholder="请选择归档类型">
              <el-option label="完整归档" value="complete"  />
              <el-option label="电子归档" value="digital"  />
              <el-option label="纸质归档" value="physical"  />
              <el-option label="混合归档" value="hybrid"  />
            </el-select>
          </el-form-item>
          <el-form-item label="归档范围">
            <el-select v-model="archiveConfig.archiveScope" placeholder="请选择归档范围">
              <el-option label="已完成合同" value="completed"  />
              <el-option label="已终止合同" value="terminated"  />
              <el-option label="已到期合同" value="expired"  />
              <el-option label="所有合同" value="all"  />
            </el-select>
          </el-form-item>
          <el-form-item label="存储位置">
            <el-select v-model="archiveConfig.storageLocation" placeholder="请选择存储位置">
              <el-option label="本地存储" value="local"  />
              <el-option label="云端存储" value="cloud"  />
              <el-option label="混合存储" value="hybrid"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleStartArchive">
              <el-icon><Operation /></el-icon>
              开始归档
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 归档统计 -->
      <div class="archive-stats">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ archiveStats.total }}</div>
                <div class="stat-label">归档总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card pending">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ archiveStats.pending }}</div>
                <div class="stat-label">待归档</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card archived">
              <div class="stat-icon">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ archiveStats.archived }}</div>
                <div class="stat-label">已归档</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card digital">
              <div class="stat-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ archiveStats.digital }}</div>
                <div class="stat-label">电子归档</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card physical">
              <div class="stat-icon">
                <el-icon><Box /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ archiveStats.physical }}</div>
                <div class="stat-label">纸质归档</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card storage">
              <div class="stat-icon">
                <el-icon><Coin /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ archiveStats.storageSize }}GB</div>
                <div class="stat-label">存储占用</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 归档进度 -->
      <div class="archive-progress">
        <h4>归档进度</h4>
        <div class="progress-container">
          <el-progress
            :percentage="archiveProgress.percentage"
            :status="archiveProgress.status"
            :stroke-width="20"
            :show-text="true"
           />
          <div class="progress-info">
            <span>{{ archiveProgress.current }} / {{ archiveProgress.total }}</span>
            <span class="progress-status">{{ archiveProgress.statusText }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 归档列表 -->
    <el-card class="archive-list" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>归档列表</h3>
          <div class="header-actions">
            <el-button @click="handleExportArchive">
              <el-icon><Download /></el-icon>
              导出归档
            </el-button>
            <el-button type="primary" @click="handleImportArchive">
              <el-icon><Upload /></el-icon>
              导入归档
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="归档状态">
            <el-select v-model="filterForm.archiveStatus" placeholder="请选择状态" clearable>
              <el-option label="待归档" value="pending"  />
              <el-option label="归档中" value="processing"  />
              <el-option label="已归档" value="archived"  />
              <el-option label="归档失败" value="failed"  />
            </el-select>
          </el-form-item>
          <el-form-item label="归档类型">
            <el-select v-model="filterForm.archiveType" placeholder="请选择类型" clearable>
              <el-option label="电子归档" value="digital"  />
              <el-option label="纸质归档" value="physical"  />
              <el-option label="混合归档" value="hybrid"  />
            </el-select>
          </el-form-item>
          <el-form-item label="员工姓名">
            <el-input 
              v-model="filterForm.employeeName" 
              placeholder="请输入员工姓名"
              clearable
              />
          </el-form-item>
          <el-form-item label="归档时间">
            <el-date-picker
              v-model="filterForm.archiveDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 归档表格 -->
      <el-table :data="archiveList" style="width: 100%">
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="contractNumber" label="合同编号" width="150"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="archiveNumber" label="归档编号" width="150"  />
        <el-table-column prop="archiveType" label="归档类型" width="100">
          <template #default="scope">
            <el-tag :type="getArchiveTypeType(scope.row.archiveType)" size="small">
              {{ getArchiveTypeText(scope.row.archiveType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="archiveStatus" label="归档状态" width="100">
          <template #default="scope">
            <el-tag :type="getArchiveStatusType(scope.row.archiveStatus)" size="small">
              {{ getArchiveStatusText(scope.row.archiveStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="storageLocation" label="存储位置" width="120"  />
        <el-table-column prop="archiveTime" label="归档时间" width="150"  />
        <el-table-column prop="archiveSize" label="存储大小" width="100"  />
        <el-table-column prop="operator" label="操作人" width="100"  />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleViewArchive(scope.row)">
              查看详情
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleDownloadArchive(scope.row)"
            >
              下载
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleUpdateArchive(scope.row)"
            >
              更新
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 归档详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="归档详情" width="900px">
      <div class="archive-detail" v-if="currentArchive">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="合同编号">{{ currentArchive.contractNumber }}</el-descriptions-item>
          <el-descriptions-item label="员工姓名">{{ currentArchive.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="归档编号">{{ currentArchive.archiveNumber }}</el-descriptions-item>
          <el-descriptions-item label="归档类型">
            <el-tag :type="getArchiveTypeType(currentArchive.archiveType)" size="small">
              {{ getArchiveTypeText(currentArchive.archiveType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="归档状态">
            <el-tag :type="getArchiveStatusType(currentArchive.archiveStatus)" size="small">
              {{ getArchiveStatusText(currentArchive.archiveStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="存储位置">{{ currentArchive.storageLocation }}</el-descriptions-item>
          <el-descriptions-item label="归档时间">{{ currentArchive.archiveTime }}</el-descriptions-item>
          <el-descriptions-item label="存储大小">{{ currentArchive.archiveSize }}</el-descriptions-item>
          <el-descriptions-item label="操作人">{{ currentArchive.operator }}</el-descriptions-item>
        </el-descriptions>

        <div class="archive-files">
          <h4>归档文件</h4>
          <el-table :data="archiveFiles" style="width: 100%">
            <el-table-column prop="fileName" label="文件名"  />
            <el-table-column prop="fileType" label="文件类型" width="100"  />
            <el-table-column prop="fileSize" label="文件大小" width="100"  />
            <el-table-column prop="uploadTime" label="上传时间" width="150"  />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button size="small" @click="handlePreviewFile(scope.row)">
                  预览
                </el-button>
                <el-button size="small" type="primary" @click="handleDownloadFile(scope.row)">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="archive-history">
          <h4>归档历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="history in archiveHistory"
              :key="history.id"
              :timestamp="history.timestamp"
              :type="history.type"
            >
              <div class="history-content">
                <div class="history-title">{{ history.title }}</div>
                <div class="history-description">{{ history.description }}</div>
                <div class="history-operator">操作人：{{ history.operator }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>

    <!-- 批量归档对话框 -->
    <el-dialog v-model="batchArchiveDialogVisible" title="批量归档" width="800px">
      <div class="batch-archive">
        <el-form ref="batchArchiveFormRef" :model="batchArchiveForm" :rules="batchArchiveRules" label-width="120px">
          <el-form-item label="归档类型" prop="archiveType">
            <el-radio-group v-model="batchArchiveForm.archiveType">
              <el-radio value="digital">电子归档</el-radio>
              <el-radio value="physical">纸质归档</el-radio>
              <el-radio value="hybrid">混合归档</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="存储位置" prop="storageLocation">
            <el-select v-model="batchArchiveForm.storageLocation" placeholder="请选择存储位置">
              <el-option label="本地存储" value="local"  />
              <el-option label="云端存储" value="cloud"  />
              <el-option label="混合存储" value="hybrid"  />
            </el-select>
          </el-form-item>
          <el-form-item label="归档备注" prop="archiveNote">
            <el-input
              v-model="batchArchiveForm.archiveNote"
              type="textarea"
              :rows="3"
              placeholder="请输入归档备注"
              />
          </el-form-item>
        </el-form>

        <div class="contract-selection">
          <h4>选择合同</h4>
          <el-table :data="contractsToArchive" style="width: 100%">
            <el-table-column type="selection" width="55"  />
            <el-table-column prop="contractNumber" label="合同编号" width="150"  />
            <el-table-column prop="employeeName" label="员工姓名" width="100"  />
            <el-table-column prop="contractStatus" label="合同状态" width="100">
              <template #default="scope">
                <el-tag :type="getContractStatusType(scope.row.contractStatus)" size="small">
                  {{ getContractStatusText(scope.row.contractStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="endDate" label="结束日期" width="120"  />
            <el-table-column prop="terminationDate" label="终止日期" width="120"  />
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchArchiveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmBatchArchive">确认归档</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractArchiveManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  FolderAdd,
  Operation,
  Document,
  Clock,
  FolderOpened,
  Monitor,
  Box,
  Coin,
  Download,
  Upload,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const detailDialogVisible = ref(false)
const batchArchiveDialogVisible = ref(false)
const currentArchive = ref(null)
const batchArchiveFormRef = ref()

// 归档配置
const archiveConfig = reactive({
  archiveType: 'complete',
  archiveScope: 'completed',
  storageLocation: 'local'
})

// 归档统计
const archiveStats = reactive({
  total: 234,
  pending: 45,
  archived: 189,
  digital: 156,
  physical: 78,
  storageSize: 12.8
})

// 归档进度
const archiveProgress = reactive({
  percentage: 75,
  status: 'active',
  current: 180,
  total: 240,
  statusText: '归档中...'
})

// 筛选表单
const filterForm = reactive({
  archiveStatus: '',
  archiveType: '',
  employeeName: '',
  archiveDateRange: []
})

// 批量归档表单
const batchArchiveForm = reactive({
  archiveType: 'digital',
  storageLocation: 'local',
  archiveNote: ''
})

// 批量归档验证规则
const batchArchiveRules = {
  archiveType: [
    { required: true, message: '请选择归档类型', trigger: 'change' }
  ],
  storageLocation: [
    { required: true, message: '请选择存储位置', trigger: 'change' }
  ]
}

// 归档列表数据
const archiveList = ref([
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '张三',
    archiveNumber: 'ARC2025001',
    archiveType: 'digital',
    archiveStatus: 'archived',
    storageLocation: '本地存储/contracts/2025',
    archiveTime: '2025-01-20 14:30:00',
    archiveSize: '2.5MB',
    operator: '系统管理员'
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '李四',
    archiveNumber: 'ARC2025002',
    archiveType: 'physical',
    archiveStatus: 'archived',
    storageLocation: '档案室/B区/2025',
    archiveTime: '2025-01-21 09:15:00',
    archiveSize: '1.8MB',
    operator: '档案管理员'
  },
  {
    id: '3',
    contractNumber: 'HKY2025003',
    employeeName: '王五',
    archiveNumber: 'ARC2025003',
    archiveType: 'hybrid',
    archiveStatus: 'processing',
    storageLocation: '云端存储/contracts',
    archiveTime: '2025-01-22 16:45:00',
    archiveSize: '3.2MB',
    operator: '系统管理员'
  }
])

// 归档文件数据
const archiveFiles = ref([
  {
    fileName: '劳动合同.pdf',
    fileType: 'PDF',
    fileSize: '1.2MB',
    uploadTime: '2025-01-20 14:30:00'
  },
  {
    fileName: '身份证复印件.jpg',
    fileType: 'JPG',
    fileSize: '0.8MB',
    uploadTime: '2025-01-20 14:31:00'
  },
  {
    fileName: '学历证明.pdf',
    fileType: 'PDF',
    fileSize: '0.5MB',
    uploadTime: '2025-01-20 14:32:00'
  }
])

// 归档历史数据
const archiveHistory = ref([
  {
    id: '1',
    timestamp: '2025-01-20 14:30:00',
    type: 'success',
    title: '归档完成',
    description: '合同已成功归档到本地存储',
    operator: '系统管理员'
  },
  {
    id: '2',
    timestamp: '2025-01-20 14:25:00',
    type: 'primary',
    title: '开始归档',
    description: '系统开始执行归档操作',
    operator: '系统管理员'
  },
  {
    id: '3',
    timestamp: '2025-01-20 14:20:00',
    type: 'warning',
    title: '归档申请',
    description: '提交合同归档申请',
    operator: '人事专员'
  }
])

// 待归档合同数据
const contractsToArchive = ref([
  {
    id: '1',
    contractNumber: 'HKY2025010',
    employeeName: '赵六',
    contractStatus: 'completed',
    endDate: '2025-01-15',
    terminationDate: null
  },
  {
    id: '2',
    contractNumber: 'HKY2025011',
    employeeName: '孙七',
    contractStatus: 'terminated',
    endDate: '2025-01-18',
    terminationDate: '2025-01-18'
  }
])

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleBatchArchive = () => {
  batchArchiveDialogVisible.value = true
}

const handleStartArchive = () => {
  ElMessage.success('归档任务已启动')
  archiveProgress.percentage = 0
  archiveProgress.status = 'active'
  archiveProgress.statusText = '开始归档...'
  
  // 模拟归档进度
  const timer = setInterval(() => {
    archiveProgress.percentage += 10
    if (archiveProgress.percentage >= 100) {
      archiveProgress.percentage = 100
      archiveProgress.status = 'success'
      archiveProgress.statusText = '归档完成'
      clearInterval(timer)
      ElMessage.success('归档任务完成')
    }
  }, 500)
}

const handleExportArchive = () => {
  ElMessage.success('导出归档数据')
}

const handleImportArchive = () => {
  ElMessage.success('导入归档数据')
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    archiveStatus: '',
    archiveType: '',
    employeeName: '',
    archiveDateRange: []
  })
  ElMessage.success('筛选条件已重置')
}

   
const handleViewArchive = (row: unknown) => {
  currentArchive.value = row
  detailDialogVisible.value = true
}

   
const handleDownloadArchive = (row: unknown) => {
  ElMessage.success(`下载归档: ${row.archiveNumber}`)
}

   
const handleUpdateArchive = (row: unknown) => {
  ElMessage.success(`更新归档: ${row.archiveNumber}`)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

   
const handlePreviewFile = (row: unknown) => {
  ElMessage.success(`预览文件: ${row.fileName}`)
}

   
const handleDownloadFile = (row: unknown) => {
  ElMessage.success(`下载文件: ${row.fileName}`)
}

const handleConfirmBatchArchive = () => {
  batchArchiveFormRef.value.validate((valid: boolean) => {
    if (valid) {
      batchArchiveDialogVisible.value = false
      ElMessage.success('批量归档任务已提交')
    }
  })
}

const getArchiveTypeType = (type: string) => {
  const typeMap = {
    'digital': 'primary',
    'physical': 'warning',
    'hybrid': 'success'
  }
  return typeMap[type] || 'info'
}

const getArchiveTypeText = (type: string) => {
  const typeMap = {
    'digital': '电子归档',
    'physical': '纸质归档',
    'hybrid': '混合归档'
  }
  return typeMap[type] || '未知'
}

const getArchiveStatusType = (status: string) => {
  const statusMap = {
    'pending': 'warning',
    'processing': 'primary',
    'archived': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getArchiveStatusText = (status: string) => {
  const statusMap = {
    'pending': '待归档',
    'processing': '归档中',
    'archived': '已归档',
    'failed': '归档失败'
  }
  return statusMap[status] || '未知'
}

const getContractStatusType = (status: string) => {
  const statusMap = {
    'completed': 'success',
    'terminated': 'danger',
    'expired': 'warning'
  }
  return statusMap[status] || 'info'
}

const getContractStatusText = (status: string) => {
  const statusMap = {
    'completed': '已完成',
    'terminated': '已终止',
    'expired': '已到期'
  }
  return statusMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  total.value = archiveList.value.length
})
</script>

<style scoped>
.contract-archive-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.archive-console,
.archive-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.archive-config {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.archive-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.pending .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.archived .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.digital .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.physical .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.storage .stat-icon {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.archive-progress {
  margin-top: 20px;
}

.archive-progress h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.progress-status {
  color: #909399;
  font-size: 12px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.archive-detail {
  margin-top: 20px;
}

.archive-files {
  margin-top: 30px;
}

.archive-files h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.archive-history {
  margin-top: 30px;
}

.archive-history h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.history-content {
  padding: 8px 0;
}

.history-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.history-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.history-operator {
  color: #909399;
  font-size: 12px;
}

.batch-archive {
  margin-top: 20px;
}

.contract-selection {
  margin-top: 30px;
}

.contract-selection h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>