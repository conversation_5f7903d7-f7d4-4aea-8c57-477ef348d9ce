<template>
  <div class="contract-renewal-reminder-report">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>续签提醒报表</h2>
      <p>生成续签提醒报表，支持分类汇总和批量通知</p>
    </div>

    <!-- 报表控制台 -->
    <el-card class="report-console" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>续签提醒控制台</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleGenerateReport">
              <el-icon><Bell /></el-icon>
              生成报表
            </el-button>
          </div>
        </div>
      </template>

      <!-- 报表配置 -->
      <div class="report-config">
        <el-form :model="reportConfig" :rules="configRules" ref="reportConfigRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="提醒时间范围" prop="timeRange">
                <el-select v-model="reportConfig.timeRange" placeholder="请选择时间范围">
                  <el-option label="未来30天" value="30days"  />
                  <el-option label="未来60天" value="60days"  />
                  <el-option label="未来90天" value="90days"  />
                  <el-option label="未来180天" value="180days"  />
                  <el-option label="自定义" value="custom"  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="提醒级别" prop="reminderLevel">
                <el-select v-model="reportConfig.reminderLevel" placeholder="请选择提醒级别">
                  <el-option label="全部级别" value="all"  />
                  <el-option label="紧急" value="urgent"  />
                  <el-option label="重要" value="important"  />
                  <el-option label="一般" value="normal"  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="部门筛选" prop="department">
                <el-select v-model="reportConfig.department" placeholder="请选择部门">
                  <el-option label="全部部门" value="all"  />
                  <el-option label="技术部" value="tech"  />
                  <el-option label="人事部" value="hr"  />
                  <el-option label="财务部" value="finance"  />
                  <el-option label="市场部" value="marketing"  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="合同类型" prop="contractType">
                <el-select v-model="reportConfig.contractType" placeholder="请选择合同类型">
                  <el-option label="全部类型" value="all"  />
                  <el-option label="劳动合同" value="labor"  />
                  <el-option label="聘用合同" value="employment"  />
                  <el-option label="临时合同" value="temporary"  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="通知方式" prop="notificationMethod">
                <el-checkbox-group v-model="reportConfig.notificationMethod">
                  <el-checkbox value="email">邮件通知</el-checkbox>
                  <el-checkbox value="sms">短信通知</el-checkbox>
                  <el-checkbox value="system">系统通知</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分组方式" prop="groupBy">
                <el-select v-model="reportConfig.groupBy" placeholder="请选择分组方式">
                  <el-option label="按部门分组" value="department"  />
                  <el-option label="按合同类型分组" value="contractType"  />
                  <el-option label="按到期时间分组" value="expirationTime"  />
                  <el-option label="按提醒级别分组" value="reminderLevel"  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 统计概览 -->
      <div class="reminder-stats">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ reminderStats.total }}</div>
                <div class="stat-label">需要续签</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card urgent">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ reminderStats.urgent }}</div>
                <div class="stat-label">紧急</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card important">
              <div class="stat-icon">
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ reminderStats.important }}</div>
                <div class="stat-label">重要</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card normal">
              <div class="stat-icon">
                <el-icon><Bell /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ reminderStats.normal }}</div>
                <div class="stat-label">一般</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card sent">
              <div class="stat-icon">
                <el-icon><Message /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ reminderStats.sent }}</div>
                <div class="stat-label">已发送</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card rate">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ reminderStats.successRate }}%</div>
                <div class="stat-label">成功率</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 分类汇总 -->
    <el-card class="category-summary" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>分类汇总</h3>
          <div class="header-actions">
            <el-button @click="handleExportSummary">
              <el-icon><Download /></el-icon>
              导出汇总
            </el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeSummaryTab" type="card">
        <el-tab-pane label="按部门汇总" name="department">
          <div class="summary-table">
            <el-table :data="departmentSummary" style="width: 100%">
              <el-table-column prop="department" label="部门" width="120"  />
              <el-table-column prop="total" label="总数" width="80"  />
              <el-table-column prop="urgent" label="紧急" width="80">
                <template #default="scope">
                  <span class="urgent-count">{{ scope.row.urgent }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="important" label="重要" width="80">
                <template #default="scope">
                  <span class="important-count">{{ scope.row.important }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="normal" label="一般" width="80">
                <template #default="scope">
                  <span class="normal-count">{{ scope.row.normal }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="sent" label="已发送" width="80"  />
              <el-table-column prop="response" label="回复数" width="80"  />
              <el-table-column prop="responseRate" label="回复率" width="100">
                <template #default="scope">
                  <el-progress
                    :percentage="scope.row.responseRate"
                    :color="getProgressColor(scope.row.responseRate)"
                    :show-text="false"
                    :stroke-width="6"
                   />
                  <span class="rate-text">{{ scope.row.responseRate }}%</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button size="small" @click="handleViewDepartmentDetail(scope.row)">
                    查看详情
                  </el-button>
                  <el-button size="small" type="primary" @click="handleSendDepartmentReminder(scope.row)">
                    发送提醒
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="按合同类型汇总" name="contractType">
          <div class="summary-chart">
            <div class="chart-container">
              <h4>合同类型分布</h4>
              <div class="type-distribution">
                <div class="type-item" v-for="(item, index) in contractTypeSummary" :key="index">
                  <div class="type-chart">
                    <div class="chart-circle" :style="{ '--progress': item.percentage }">
                      <span class="percentage">{{ item.percentage }}%</span>
                    </div>
                  </div>
                  <div class="type-info">
                    <div class="type-name">{{ item.name }}</div>
                    <div class="type-count">{{ item.count }}个</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="按到期时间汇总" name="expirationTime">
          <div class="time-summary">
            <div class="time-chart">
              <h4>到期时间分布</h4>
              <div class="time-bars">
                <div class="time-bar" v-for="(item, index) in timeSummary" :key="index">
                  <div class="bar-label">{{ item.period }}</div>
                  <div class="bar-container">
                    <div class="bar-fill" :style="{ width: (item.count / 50) * 100 + '%' }"></div>
                  </div>
                  <div class="bar-count">{{ item.count }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 续签提醒列表 -->
    <el-card class="reminder-list" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>续签提醒列表</h3>
          <div class="header-actions">
            <el-button @click="handleBatchReminder">
              <el-icon><Message /></el-icon>
              批量提醒
            </el-button>
            <el-button type="primary" @click="handleExportList">
              <el-icon><Download /></el-icon>
              导出列表
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="提醒级别">
            <el-select v-model="filterForm.reminderLevel" placeholder="请选择提醒级别" clearable>
              <el-option label="紧急" value="urgent"  />
              <el-option label="重要" value="important"  />
              <el-option label="一般" value="normal"  />
            </el-select>
          </el-form-item>
          <el-form-item label="发送状态">
            <el-select v-model="filterForm.sendStatus" placeholder="请选择发送状态" clearable>
              <el-option label="未发送" value="unsent"  />
              <el-option label="已发送" value="sent"  />
              <el-option label="发送失败" value="failed"  />
            </el-select>
          </el-form-item>
          <el-form-item label="员工姓名">
            <el-input 
              v-model="filterForm.employeeName" 
              placeholder="请输入员工姓名"
              clearable
              />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 提醒表格 -->
      <el-table :data="reminderList" style="width: 100%">
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="contractNumber" label="合同编号" width="150"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="department" label="部门" width="100"  />
        <el-table-column prop="contractType" label="合同类型" width="100">
          <template #default="scope">
            <el-tag :type="getContractTypeColor(scope.row.contractType)" size="small">
              {{ getContractTypeText(scope.row.contractType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="到期日期" width="120"  />
        <el-table-column prop="remainingDays" label="剩余天数" width="100">
          <template #default="scope">
            <span :class="getRemainingDaysClass(scope.row.remainingDays)">
              {{ scope.row.remainingDays }}天
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="reminderLevel" label="提醒级别" width="100">
          <template #default="scope">
            <el-tag :type="getReminderLevelColor(scope.row.reminderLevel)" size="small">
              {{ getReminderLevelText(scope.row.reminderLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendStatus" label="发送状态" width="100">
          <template #default="scope">
            <el-tag :type="getSendStatusColor(scope.row.sendStatus)" size="small">
              {{ getSendStatusText(scope.row.sendStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" width="150"  />
        <el-table-column prop="responseStatus" label="回复状态" width="100">
          <template #default="scope">
            <el-tag :type="getResponseStatusColor(scope.row.responseStatus)" size="small">
              {{ getResponseStatusText(scope.row.responseStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleViewDetail(scope.row)">
              查看详情
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleSendReminder(scope.row)"
              :disabled="scope.row.sendStatus === 'sent'"
            >
              发送提醒
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleResendReminder(scope.row)"
              v-if="scope.row.sendStatus === 'failed'"
            >
              重新发送
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Bell,
  Document,
  Warning,
  InfoFilled,
  Message,
  TrendCharts,
  Download,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const activeSummaryTab = ref('department')
const reportConfigRef = ref()

// 报表配置
const reportConfig = reactive({
  timeRange: '90days',
  reminderLevel: 'all',
  department: 'all',
  contractType: 'all',
  notificationMethod: ['email', 'system'],
  groupBy: 'department'
})

// 配置验证规则
const configRules = {
  timeRange: [
    { required: true, message: '请选择时间范围', trigger: 'change' }
  ],
  notificationMethod: [
    { required: true, message: '请选择通知方式', trigger: 'change' }
  ]
}

// 统计数据
const reminderStats = reactive({
  total: 156,
  urgent: 23,
  important: 45,
  normal: 88,
  sent: 98,
  successRate: 76
})

// 筛选表单
const filterForm = reactive({
  reminderLevel: '',
  sendStatus: '',
  employeeName: ''
})

// 部门汇总数据
const departmentSummary = ref([
  {
    department: '技术部',
    total: 45,
    urgent: 8,
    important: 15,
    normal: 22,
    sent: 32,
    response: 24,
    responseRate: 75
  },
  {
    department: '人事部',
    total: 28,
    urgent: 5,
    important: 12,
    normal: 11,
    sent: 20,
    response: 16,
    responseRate: 80
  },
  {
    department: '财务部',
    total: 32,
    urgent: 6,
    important: 9,
    normal: 17,
    sent: 25,
    response: 18,
    responseRate: 72
  },
  {
    department: '市场部',
    total: 38,
    urgent: 4,
    important: 13,
    normal: 21,
    sent: 28,
    response: 21,
    responseRate: 75
  }
])

// 合同类型汇总数据
const contractTypeSummary = ref([
  { name: 'HrHr劳动合同', count: 89, percentage: 57 },
  { name: '聘用合同', count: 45, percentage: 29 },
  { name: '临时合同', count: 22, percentage: 14 }
])

// 时间汇总数据
const timeSummary = ref([
  { period: '0-30天', count: 23 },
  { period: '31-60天', count: 45 },
  { period: '61-90天', count: 38 },
  { period: '91-120天', count: 32 },
  { period: '121-180天', count: 18 }
])

// 提醒列表数据
const reminderList = ref([
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '张三',
    department: '技术部',
    contractType: 'labor',
    endDate: '2025-01-30',
    remainingDays: 7,
    reminderLevel: 'urgent',
    sendStatus: 'sent',
    sendTime: '2025-01-23 10:30:00',
    responseStatus: 'replied'
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '李四',
    department: '人事部',
    contractType: 'employment',
    endDate: '2025-02-15',
    remainingDays: 23,
    reminderLevel: 'important',
    sendStatus: 'unsent',
    sendTime: '',
    responseStatus: 'pending'
  },
  {
    id: '3',
    contractNumber: 'HKY2025003',
    employeeName: '王五',
    department: '财务部',
    contractType: 'labor',
    endDate: '2025-03-10',
    remainingDays: 46,
    reminderLevel: 'normal',
    sendStatus: 'failed',
    sendTime: '2025-01-22 14:20:00',
    responseStatus: 'no_response'
  }
])

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleGenerateReport = () => {
  reportConfigRef.value.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('生成续签提醒报表')
    }
  })
}

const handleExportSummary = () => {
  ElMessage.success('导出汇总数据')
}

const handleBatchReminder = () => {
  ElMessage.success('批量发送提醒')
}

const handleExportList = () => {
  ElMessage.success('导出提醒列表')
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    reminderLevel: '',
    sendStatus: '',
    employeeName: ''
  })
  ElMessage.success('筛选条件已重置')
}

   
const handleViewDepartmentDetail = (row: unknown) => {
  ElMessage.success(`查看部门详情: ${row.department}`)
}

   
const handleSendDepartmentReminder = (row: unknown) => {
  ElMessage.success(`发送部门提醒: ${row.department}`)
}

   
const handleViewDetail = (row: unknown) => {
  ElMessage.success(`查看详情: ${row.contractNumber}`)
}

   
const handleSendReminder = (row: unknown) => {
  ElMessage.success(`发送提醒: ${row.employeeName}`)
  row.sendStatus = 'sent'
  row.sendTime = new Date().toLocaleString()
}

   
const handleResendReminder = (row: unknown) => {
  ElMessage.success(`重新发送提醒: ${row.employeeName}`)
  row.sendStatus = 'sent'
  row.sendTime = new Date().toLocaleString()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getRemainingDaysClass = (days: number) => {
  if (days <= 7) return 'days-urgent'
  if (days <= 30) return 'days-important'
  return 'days-normal'
}

const getContractTypeColor = (type: string) => {
  const colorMap = {
    'labor': 'primary',
    'employment': 'success',
    'temporary': 'warning'
  }
  return colorMap[type] || 'info'
}

const getContractTypeText = (type: string) => {
  const textMap = {
    'labor': '劳动合同',
    'employment': '聘用合同',
    'temporary': '临时合同'
  }
  return textMap[type] || '未知'
}

const getReminderLevelColor = (level: string) => {
  const colorMap = {
    'urgent': 'danger',
    'important': 'warning',
    'normal': 'primary'
  }
  return colorMap[level] || 'info'
}

const getReminderLevelText = (level: string) => {
  const textMap = {
    'urgent': '紧急',
    'important': '重要',
    'normal': '一般'
  }
  return textMap[level] || '未知'
}

const getSendStatusColor = (status: string) => {
  const colorMap = {
    'sent': 'success',
    'unsent': 'info',
    'failed': 'danger'
  }
  return colorMap[status] || 'info'
}

const getSendStatusText = (status: string) => {
  const textMap = {
    'sent': '已发送',
    'unsent': '未发送',
    'failed': '发送失败'
  }
  return textMap[status] || '未知'
}

const getResponseStatusColor = (status: string) => {
  const colorMap = {
    'replied': 'success',
    'pending': 'warning',
    'no_response': 'danger'
  }
  return colorMap[status] || 'info'
}

const getResponseStatusText = (status: string) => {
  const textMap = {
    'replied': '已回复',
    'pending': '待回复',
    'no_response': '未回复'
  }
  return textMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  total.value = reminderList.value.length
})
</script>

<style scoped>
.contract-renewal-reminder-report {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.report-console,
.category-summary,
.reminder-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.report-config {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.reminder-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.urgent .stat-icon {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stat-card.important .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.normal .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.sent .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.rate .stat-icon {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.summary-table {
  padding: 20px;
}

.urgent-count {
  color: #f56c6c;
  font-weight: 600;
}

.important-count {
  color: #e6a23c;
  font-weight: 600;
}

.normal-count {
  color: #67c23a;
  font-weight: 600;
}

.rate-text {
  margin-left: 8px;
  font-size: 12px;
  color: #606266;
}

.summary-chart {
  padding: 20px;
}

.chart-container h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.type-distribution {
  display: flex;
  gap: 40px;
  justify-content: center;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.type-chart {
  position: relative;
}

.chart-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: conic-gradient(#4299e1 0% calc(var(--progress) * 1%), #e2e8f0 calc(var(--progress) * 1%) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.chart-circle::before {
  content: '';
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: white;
  position: absolute;
}

.percentage {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  position: relative;
  z-index: 1;
}

.type-info {
  text-align: center;
}

.type-name {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
  margin-bottom: 4px;
}

.type-count {
  font-size: 12px;
  color: #606266;
}

.time-summary {
  padding: 20px;
}

.time-chart h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.time-bars {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.time-bar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.bar-label {
  width: 100px;
  font-size: 14px;
  color: #303133;
}

.bar-container {
  flex: 1;
  max-width: 300px;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #4299e1, #667eea);
  transition: width 0.3s ease;
}

.bar-count {
  width: 40px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  text-align: right;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.days-urgent {
  color: #f56c6c;
  font-weight: 600;
}

.days-important {
  color: #e6a23c;
  font-weight: 600;
}

.days-normal {
  color: #67c23a;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>