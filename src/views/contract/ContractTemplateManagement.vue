<template>
  <div class="contract-template-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>合同模板管理</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleCreateTemplate">
              <el-icon><Plus /></el-icon>
              新建模板
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="模板名称">
            <el-input 
              v-model="searchForm.name" 
              placeholder="请输入模板名称"
              clearable
              />
          </el-form-item>
          <el-form-item label="模板类型">
            <el-select v-model="searchForm.type" placeholder="请选择模板类型" clearable>
              <el-option label="事业编制合同" value="career"  />
              <el-option label="劳务协议" value="service"  />
              <el-option label="劳务派遣" value="dispatch"  />
              <el-option label="兼职协议" value="parttime"  />
              <el-option label="实习协议" value="internship"  />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="enabled"  />
              <el-option label="禁用" value="disabled"  />
              <el-option label="草稿" value="draft"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleResetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 模板列表 -->
      <div class="template-list">
        <el-table
          :data="templateList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="name" label="模板名称" min-width="200"  />
          <el-table-column prop="type" label="合同类型" width="120">
            <template #default="scope">
              <el-tag :type="getTypeColor(scope.row.type)">
                {{ getTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="version" label="版本" width="80" align="center"  />
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="'enabled'"
                :inactive-value="'disabled'"
                @change="handleStatusChange(scope.row)"
               />
            </template>
          </el-table-column>
          <el-table-column prop="creator" label="创建人" width="100"  />
          <el-table-column prop="createTime" label="创建时间" width="160" align="center">
            <template #default="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="160" align="center">
            <template #default="scope">
              {{ formatDate(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="handleViewTemplate(scope.row)">
                查看
              </el-button>
              <el-button type="text" @click="handleEditTemplate(scope.row)">
                编辑
              </el-button>
              <el-button type="text" @click="handleCopyTemplate(scope.row)">
                复制
              </el-button>
              <el-button type="text" @click="handleVersionHistory(scope.row)">
                版本
              </el-button>
              <el-button type="text" danger @click="handleDeleteTemplate(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
           />
        </div>
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedTemplates.length > 0">
        <el-button @click="handleBatchEnable">批量启用</el-button>
        <el-button @click="handleBatchDisable">批量禁用</el-button>
        <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
      </div>
    </el-card>

    <!-- 模板编辑对话框 -->
    <el-dialog
      v-model="templateDialog.visible"
      :title="templateDialog.title"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="template-editor">
        <el-form 
          :model="templateForm" 
          :rules="templateRules"
          ref="templateFormRef"
          label-width="100px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模板名称" prop="name">
                <el-input v-model="templateForm.name" placeholder="请输入模板名称"   />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同类型" prop="type">
                <el-select v-model="templateForm.type" placeholder="请选择合同类型">
                  <el-option label="事业编制合同" value="career"  />
                  <el-option label="劳务协议" value="service"  />
                  <el-option label="劳务派遣" value="dispatch"  />
                  <el-option label="兼职协议" value="parttime"  />
                  <el-option label="实习协议" value="internship"  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="版本号" prop="version">
                <el-input v-model="templateForm.version" placeholder="如：V1.0"   />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="templateForm.status">
                  <el-radio label="draft">草稿</el-radio>
                  <el-radio label="enabled">启用</el-radio>
                  <el-radio label="disabled">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="描述">
            <el-input 
              v-model="templateForm.description" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入模板描述"
              />
          </el-form-item>
        </el-form>

        <!-- 模板变量设置 -->
        <div class="template-variables">
          <h4>模板变量</h4>
          <div class="variables-list">
            <div 
              v-for="variable in templateForm.variables" 
              :key="variable.id"
              class="variable-item"
            >
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-input 
                    v-model="variable.name" 
                    placeholder="变量名称"
                    size="small"
                    />
                </el-col>
                <el-col :span="6">
                  <el-input 
                    v-model="variable.key" 
                    placeholder="变量键名"
                    size="small"
                    />
                </el-col>
                <el-col :span="4">
                  <el-select v-model="variable.type" placeholder="类型" size="small">
                    <el-option label="文本" value="text"  />
                    <el-option label="数字" value="number"  />
                    <el-option label="日期" value="date"  />
                    <el-option label="选择" value="select"  />
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-input 
                    v-model="variable.defaultValue" 
                    placeholder="默认值"
                    size="small"
                    />
                </el-col>
                <el-col :span="3">
                  <el-checkbox v-model="variable.required" size="small">必填</el-checkbox>
                </el-col>
                <el-col :span="1">
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="removeVariable(variable.id)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </div>
          <el-button @click="addVariable" size="small">
            <el-icon><Plus /></el-icon>
            添加变量
          </el-button>
        </div>

        <!-- 模板内容编辑 -->
        <div class="template-content">
          <h4>模板内容</h4>
          <div class="content-editor">
            <el-input
              v-model="templateForm.content"
              type="textarea"
              :rows="15"
              placeholder="请输入合同模板内容，使用 {{变量键名}} 来标记变量位置"
              />
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="templateDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveTemplate">
            保存模板
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 版本历史对话框 -->
    <el-dialog
      v-model="versionDialog.visible"
      title="版本历史"
      width="70%"
    >
      <el-table :data="versionHistory" v-loading="versionLoading">
        <el-table-column prop="version" label="版本号" width="100"  />
        <el-table-column prop="description" label="版本说明" min-width="200"  />
        <el-table-column prop="creator" label="创建人" width="100"  />
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'current' ? 'success' : 'info'">
              {{ scope.row.status === 'current' ? '当前版本' : '历史版本' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="text" @click="handleViewVersion(scope.row)">
              查看
            </el-button>
            <el-button 
              type="text" 
              @click="handleRestoreVersion(scope.row)"
              :disabled="scope.row.status === 'current'"
            >
              恢复
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractTemplateManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Files,
  Check,
  Collection,
  Document,
  Upload,
  Download,
  ArrowDown
} from '@element-plus/icons-vue'
import { contractTemplateApi } from '@/api/contract'
import type { ContractTemplate } from '@/api/contract'

// 组件引入
import TemplateEditorDialog from './components/TemplateEditorDialog.vue'
import TemplatePreviewDialog from './components/TemplatePreviewDialog.vue'
import TemplateVersionDialog from './components/TemplateVersionDialog.vue'
import TemplateVariableDialog from './components/TemplateVariableDialog.vue'
import TemplateUsageDialog from './components/TemplateUsageDialog.vue'
import TemplateImportDialog from './components/TemplateImportDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref<any[]>([]) // 修复类型：never[] → any[]

// 搜索表单
const searchForm = reactive({
  keyword: '',
  contractType: '',
  status: '',
  version: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  active: 0,
  versions: 0,
  used: 0
})

// 对话框相关
const editorDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const versionDialogVisible = ref(false)
const variableDialogVisible = ref(false)
const usageDialogVisible = ref(false)
const importDialogVisible = ref(false)
const editorMode = ref<'view' | 'add' | 'edit'>('view')
const currentTemplate = ref(null)

// 合同类型选项
const contractTypeOptions = [
  { label: '事业编制聘用合同', value: 'CAREER_ESTABLISHMENT' },
  { label: '人事代理合同', value: 'PERSONNEL_AGENCY' },
  { label: '劳务协议', value: 'LABOR_AGREEMENT' },
  { label: '劳务派遣合同', value: 'LABOR_DISPATCH' },
  { label: '兼职协议', value: 'PART_TIME_AGREEMENT' },
  { label: '项目合作协议', value: 'PROJECT_COOPERATION' }
]

// 模拟数据
const mockData = [
  {
    id: '1',
    templateName: '事业编制聘用合同模板',
    templateCode: 'CAREER_EST_V1',
    contractType: 'CAREER_ESTABLISHMENT',
    version: '1.0',
    isActive: true,
    variableCount: 15,
    usageCount: 45,
    createTime: '2025-01-15 10:30:00',
    createBy: '系统管理员'
  },
  {
    id: '2',
    templateName: '劳务协议模板',
    templateCode: 'LABOR_AGR_V2',
    contractType: 'LABOR_AGREEMENT',
    version: '2.1',
    isActive: true,
    variableCount: 12,
    usageCount: 28,
    createTime: '2025-02-20 14:15:00',
    createBy: '人事专员'
  }
]

// 获取模板列表
const fetchTemplates = async () => {
  try {
    loading.value = true
    const params = {
      keyword: searchForm.keyword,
      contractType: searchForm.contractType,
      status: searchForm.status,
      version: searchForm.version,
      page: pagination.page - 1,
      size: pagination.size
    }
    
    const response = await contractTemplateApi.queryTemplates(params)
    tableData.value = response as unknown[]
    // 如果API返回的是分页结果，需要调整处理方式
    pagination.total = Array.isArray(response) ? response.length : response.totalElements || 0
  } catch (__error) {
    console.error('获取模板列表失败:', error)
    ElMessage.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    // 由于contractTemplateApi中没有提供统计API，我们通过查询列表计算统计信息
    const allTemplates = await contractTemplateApi.queryTemplates({})
    
    if (Array.isArray(allTemplates)) {
      stats.total = allTemplates.length
      stats.active = allTemplates.filter(t => t.isActive).length
      // 计算所有版本数（假设每个模板可能有多个版本）
      const versions = new Set(allTemplates.map(t => `${t.templateCode}-${t.version}`))
      stats.versions = versions.size
      // 使用次数需要从其他API获取，暂时使用模拟数据
      stats.used = stats.total * 6 // 临时计算
    }
  } catch (__error) {
    console.error('获取统计信息失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchTemplates()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    contractType: '',
    status: '',
    version: ''
  })
  pagination.page = 1
  fetchTemplates()
}

// 新建模板
const handleAdd = () => {
  currentTemplate.value = null
  editorMode.value = 'add'
  editorDialogVisible.value = true
}

// 查看模板
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleView = (template: unknown) => {
  currentTemplate.value = template
  editorMode.value = 'view'
  editorDialogVisible.value = true
}

// 编辑模板
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleEdit = (template: unknown) => {
  currentTemplate.value = template
  editorMode.value = 'edit'
  editorDialogVisible.value = true
}

// 预览模板
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handlePreview = (template: unknown) => {
  currentTemplate.value = template
  previewDialogVisible.value = true
}

// 复制模板
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleCopy = async (template: unknown) => {
  try {
    const {value: _value} =  await ElMessageBox.prompt(
      '请输入新模板名称',
      '复制模板',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '模板名称不能为空',
        inputValue: `${template.templateName}_副本`
      }
    )
    
    // 调用复制模板API
    const newVersion 
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.versions {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.used {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
