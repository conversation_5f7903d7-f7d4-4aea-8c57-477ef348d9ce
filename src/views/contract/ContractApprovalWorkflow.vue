<template>
  <div class="contract-approval-workflow">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同审批工作流</h2>
      <p>管理合同审批流程，实现智能审批路由和审批状态跟踪</p>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="function-tabs">
      <!-- 待审批合同 -->
      <el-tab-pane label="待审批合同" name="pending">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">待我审批的合同</span>
            <div class="tab-actions">
              <el-button type="primary" @click="handleBatchApprove" :disabled="selectedPendingContracts.length === 0">
                <el-icon><Check /></el-icon>
                批量审批
              </el-button>
              <el-button @click="handleRefreshPending">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>

          <!-- 审批统计 -->
          <el-row :gutter="20" class="approval-stats">
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon urgent">
                    <el-icon><Warning /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ approvalStats.urgent }}</div>
                    <div class="stats-label">紧急审批</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon pending">
                    <el-icon><Clock /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ approvalStats.pending }}</div>
                    <div class="stats-label">待审批</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon overdue">
                    <el-icon><Timer /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ approvalStats.overdue }}</div>
                    <div class="stats-label">超时审批</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon today">
                    <el-icon><Star /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ approvalStats.today }}</div>
                    <div class="stats-label">今日新增</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 筛选条件 -->
          <div class="filter-form">
            <el-row :gutter="20">
              <el-col :span="4">
                <el-select v-model="pendingFilter.priority" placeholder="优先级" clearable>
                  <el-option label="紧急" value="URGENT"  />
                  <el-option label="高" value="HIGH"  />
                  <el-option label="中" value="MEDIUM"  />
                  <el-option label="低" value="LOW"  />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="pendingFilter.contractType" placeholder="合同类型" clearable>
                  <el-option label="事业编制聘用合同" value="CAREER_ESTABLISHMENT"  />
                  <el-option label="人事代理合同" value="PERSONNEL_AGENCY"  />
                  <el-option label="劳务协议" value="LABOR_AGREEMENT"  />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="pendingFilter.department" placeholder="申请部门" clearable>
                  <el-option label="全部部门" value=""  />
                  <el-option
                    v-for="dept in departmentOptions"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.id"
                   />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="pendingFilter.timeRange" placeholder="时间范围" clearable>
                  <el-option label="今天" value="today"  />
                  <el-option label="本周" value="week"  />
                  <el-option label="本月" value="month"  />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-button type="primary" @click="handleFilterPending">
                  <el-icon><Search /></el-icon>
                  筛选
                </el-button>
                <el-button @click="handleResetPendingFilter">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 待审批列表 -->
          <el-table :data="pendingContractsList" style="width: 100%" @selection-change="handlePendingSelectionChange">
            <el-table-column type="selection" width="55"  />
            <el-table-column prop="contractNumber" label="合同编号" width="120"  />
            <el-table-column prop="employeeName" label="申请人" width="100"  />
            <el-table-column prop="contractType" label="合同类型" width="150" show-overflow-tooltip  />
            <el-table-column prop="department" label="申请部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="currentStep" label="当前步骤" width="120"  />
            <el-table-column prop="priority" label="优先级" width="100">
              <template #default="scope">
                <el-tag :type="getPriorityTag(scope.row.priority)" size="small">
                  {{ getPriorityText(scope.row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="submitTime" label="提交时间" width="150"  />
            <el-table-column prop="remainingHours" label="剩余时间" width="100">
              <template #default="scope">
                <el-tag :type="getRemainingHoursTag(scope.row.remainingHours)" size="small">
                  {{ scope.row.remainingHours }}小时
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewContract(scope.row)">
                  查看合同
                </el-button>
                <el-button size="small" type="success" link @click="handleApprove(scope.row)">
                  审批
                </el-button>
                <el-button size="small" type="info" link @click="handleViewWorkflow(scope.row)">
                  流程图
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 审批历史 -->
      <el-tab-pane label="审批历史" name="history">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">我的审批历史</span>
            <div class="tab-actions">
              <el-button @click="handleExportHistory">
                <el-icon><Download /></el-icon>
                导出记录
              </el-button>
            </div>
          </div>

          <!-- 历史筛选 -->
          <div class="filter-form">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model="historyFilter.keyword"
                  placeholder="搜索合同编号、申请人"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="historyFilter.result" placeholder="审批结果" clearable>
                  <el-option label="通过" value="APPROVED"  />
                  <el-option label="驳回" value="REJECTED"  />
                  <el-option label="退回" value="RETURNED"  />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-date-picker
                  v-model="historyFilter.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                 />
              </el-col>
              <el-col :span="9">
                <el-button type="primary" @click="handleFilterHistory">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleResetHistoryFilter">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 历史记录列表 -->
          <el-table :data="historyList" style="width: 100%">
            <el-table-column prop="contractNumber" label="合同编号" width="120"  />
            <el-table-column prop="employeeName" label="申请人" width="100"  />
            <el-table-column prop="contractType" label="合同类型" width="150" show-overflow-tooltip  />
            <el-table-column prop="approvalStep" label="审批步骤" width="120"  />
            <el-table-column prop="approvalResult" label="审批结果" width="100">
              <template #default="scope">
                <el-tag :type="getApprovalResultTag(scope.row.approvalResult)" size="small">
                  {{ getApprovalResultText(scope.row.approvalResult) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="approvalTime" label="审批时间" width="150"  />
            <el-table-column prop="approvalComment" label="审批意见" show-overflow-tooltip  />
            <el-table-column prop="processingTime" label="处理时长" width="100"  />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewHistoryDetail(scope.row)">
                  查看详情
                </el-button>
                <el-button size="small" type="info" link @click="handleViewWorkflowHistory(scope.row)">
                  流程详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 工作流配置 -->
      <el-tab-pane label="工作流配置" name="workflow-config">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">审批工作流配置</span>
            <el-button type="success" @click="handleAddWorkflow">
              <el-icon><Plus /></el-icon>
              新建工作流
            </el-button>
          </div>

          <!-- 工作流列表 -->
          <el-table :data="workflowList" style="width: 100%">
            <el-table-column prop="workflowName" label="工作流名称" width="200"  />
            <el-table-column prop="contractType" label="适用合同类型" width="180">
              <template #default="scope">
                <el-tag size="small">{{ getContractTypeText(scope.row.contractType) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="stepCount" label="审批步骤数" width="120" align="center"  />
            <el-table-column prop="avgProcessingTime" label="平均处理时长" width="140"  />
            <el-table-column prop="isActive" label="状态" width="100">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.isActive"
                  @change="handleToggleWorkflow(scope.row)"
                 />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="150"  />
            <el-table-column prop="createBy" label="创建人" width="100"  />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewWorkflowConfig(scope.row)">
                  查看
                </el-button>
                <el-button size="small" type="success" link @click="handleEditWorkflow(scope.row)">
                  编辑
                </el-button>
                <el-button size="small" type="warning" link @click="handleDesignWorkflow(scope.row)">
                  设计器
                </el-button>
                <el-button size="small" type="danger" link @click="handleDeleteWorkflow(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 审批统计 -->
      <el-tab-pane label="审批统计" name="statistics">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">审批效率统计</span>
            <div class="tab-actions">
              <el-button @click="handleRefreshStats">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
              <el-button @click="handleExportStats">
                <el-icon><Download /></el-icon>
                导出报告
              </el-button>
            </div>
          </div>

          <!-- 统计图表区域 -->
          <el-row :gutter="20" class="stats-charts">
            <el-col :span="12">
              <el-card class="chart-card" shadow="never">
                <template #header>
                  <span>审批效率趋势</span>
                </template>
                <div class="chart-container" id="efficiencyChart">
                  <div class="chart-placeholder">审批效率趋势图</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="chart-card" shadow="never">
                <template #header>
                  <span>审批结果分布</span>
                </template>
                <div class="chart-container" id="resultChart">
                  <div class="chart-placeholder">审批结果分布图</div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 详细统计表格 -->
          <el-table :data="statisticsData" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="approver" label="审批人" width="120"  />
            <el-table-column prop="totalCount" label="审批总数" width="100" align="center"  />
            <el-table-column prop="approvedCount" label="通过数" width="100" align="center"  />
            <el-table-column prop="rejectedCount" label="驳回数" width="100" align="center"  />
            <el-table-column prop="approvalRate" label="通过率" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getApprovalRateTag(scope.row.approvalRate)" size="small">
                  {{ scope.row.approvalRate }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="avgProcessingTime" label="平均处理时长" width="140"  />
            <el-table-column prop="overdueCount" label="超时次数" width="100" align="center"  />
            <el-table-column prop="efficiency" label="效率评级" width="100">
              <template #default="scope">
                <el-tag :type="getEfficiencyTag(scope.row.efficiency)" size="small">
                  {{ scope.row.efficiency }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewApproverDetail(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 合同审批对话框 -->
    <ContractApprovalDialog
      v-model:visible="approvalDialogVisible"
      :contract="currentContract"
      @success="handleApprovalSuccess"
    />

    <!-- 工作流设计器对话框 -->
    <WorkflowDesignerDialog
      v-model:visible="designerDialogVisible"
      :workflow="currentWorkflow"
      :mode="designerMode"
      @success="handleDesignerSuccess"
    />

    <!-- 工作流查看器对话框 -->
    <WorkflowViewerDialog
      v-model:visible="viewerDialogVisible"
      :contract="currentContract"
    />

    <!-- 批量审批对话框 -->
    <BatchApprovalDialog
      v-model:visible="batchApprovalDialogVisible"
      :contracts="selectedPendingContracts"
      @success="handleBatchApprovalSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  Refresh,
  Warning,
  Clock,
  Timer,
  Star,
  Search,
  Download,
  Plus
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'

// 组件引入
import ContractApprovalDialog from './components/ContractApprovalDialog.vue'
import WorkflowDesignerDialog from './components/WorkflowDesignerDialog.vue'
import WorkflowViewerDialog from './components/WorkflowViewerDialog.vue'
import BatchApprovalDialog from './components/BatchApprovalDialog.vue'

// 响应式数据
const activeTab = ref('pending')
const selectedPendingContracts = ref([])

// 审批统计数据
const approvalStats = reactive({
  urgent: 5,
  pending: 18,
  overdue: 3,
  today: 8
})

// 筛选条件
const pendingFilter = reactive({
  priority: '',
  contractType: '',
  department: '',
  timeRange: ''
})

const historyFilter = reactive({
  keyword: '',
  result: '',
  dateRange: []
})

// 待审批合同列表
const pendingContractsList = ref([
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '张三',
    contractType: '事业编制聘用合同',
    department: '计算机学院',
    currentStep: '人事处审批',
    priority: 'HIGH',
    submitTime: '2025-06-19 09:00:00',
    remainingHours: 24
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '李四',
    contractType: '劳务协议',
    department: '后勤服务中心',
    currentStep: '部门审批',
    priority: 'URGENT',
    submitTime: '2025-06-19 08:30:00',
    remainingHours: 6
  }
])

// 审批历史列表
const historyList = ref([
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '王五',
    contractType: '人事代理合同',
    approvalStep: '人事处审批',
    approvalResult: 'APPROVED',
    approvalTime: '2025-06-18 15:30:00',
    approvalComment: '符合条件，同意签署',
    processingTime: '2小时30分钟'
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '赵六',
    contractType: '劳务协议',
    approvalStep: '部门审批',
    approvalResult: 'REJECTED',
    approvalTime: '2025-06-18 14:15:00',
    approvalComment: '材料不完整，需要补充相关证明',
    processingTime: '1小时15分钟'
  }
])

// 工作流配置列表
const workflowList = ref([
  {
    id: '1',
    workflowName: '事业编制聘用合同审批流程',
    contractType: 'CAREER_ESTABLISHMENT',
    stepCount: 4,
    avgProcessingTime: '3.5天',
    isActive: true,
    createTime: '2025-01-15 10:00:00',
    createBy: '系统管理员'
  },
  {
    id: '2',
    workflowName: '劳务协议审批流程',
    contractType: 'LABOR_AGREEMENT',
    stepCount: 2,
    avgProcessingTime: '1.2天',
    isActive: true,
    createTime: '2025-01-20 14:30:00',
    createBy: '人事专员'
  }
])

// 统计数据
const statisticsData = ref([
  {
    approver: '张主任',
    totalCount: 45,
    approvedCount: 42,
    rejectedCount: 3,
    approvalRate: 93.3,
    avgProcessingTime: '2.5小时',
    overdueCount: 1,
    efficiency: '优秀'
  },
  {
    approver: '李处长',
    totalCount: 38,
    approvedCount: 35,
    rejectedCount: 3,
    approvalRate: 92.1,
    avgProcessingTime: '3.2小时',
    overdueCount: 2,
    efficiency: '良好'
  }
])

// 对话框相关
const approvalDialogVisible = ref(false)
const designerDialogVisible = ref(false)
const viewerDialogVisible = ref(false)
const batchApprovalDialogVisible = ref(false)
const designerMode = ref<'view' | 'add' | 'edit'>('add')
const currentContract = ref(null)
const currentWorkflow = ref(null)

// 部门列表
const departmentOptions = ref<any[]>([])

// 批量审批
const handleBatchApprove = () => {
  if (selectedPendingContracts.value.length === 0) {
    ElMessage.warning('请先选择要审批的合同')
    return
  }
  batchApprovalDialogVisible.value = true
}

// 刷新待审批
const handleRefreshPending = () => {
  ElMessage.success('待审批列表已刷新')
}

// 筛选待审批
const handleFilterPending = () => {
  ElMessage.info('筛选待审批合同功能开发中...')
}

// 重置待审批筛选
const handleResetPendingFilter = () => {
  Object.assign(pendingFilter, {
    priority: '',
    contractType: '',
    department: '',
    timeRange: ''
  })
}

// 待审批选择变化
   
const handlePendingSelectionChange = (selection: unknown[]) => {
  selectedPendingContracts.value = selection as unknown // 修复类型：never[] → any[]
}

// 查看合同
   
const handleViewContract = (contract: unknown) => {
  ElMessage.info(`查看合同 ${contract.contractNumber} 的详细信息`)
}

// 审批合同
   
const handleApprove = (contract: unknown) => {
  currentContract.value = contract
  approvalDialogVisible.value = true
}

// 查看工作流
   
const handleViewWorkflow = (contract: unknown) => {
  currentContract.value = contract
  viewerDialogVisible.value = true
}

// 导出历史记录
const handleExportHistory = () => {
  ElMessage.info('导出审批历史功能开发中...')
}

// 筛选历史记录
const handleFilterHistory = () => {
  ElMessage.info('筛选历史记录功能开发中...')
}

// 重置历史筛选
const handleResetHistoryFilter = () => {
  Object.assign(historyFilter, {
    keyword: '',
    result: '',
    dateRange: []
  })
}

// 查看历史详情
   
const handleViewHistoryDetail = (history: unknown) => {
  ElMessage.info(`查看审批记录 ${history.contractNumber} 的详细信息`)
}

// 查看工作流历史
   
const handleViewWorkflowHistory = (history: unknown) => {
  ElMessage.info(`查看合同 ${history.contractNumber} 的完整审批流程`)
}

// 新建工作流
const handleAddWorkflow = () => {
  currentWorkflow.value = null
  designerMode.value = 'add'
  designerDialogVisible.value = true
}

// 查看工作流配置
   
const handleViewWorkflowConfig = (workflow: unknown) => {
  currentWorkflow.value = workflow
  designerMode.value = 'view'
  designerDialogVisible.value = true
}

// 编辑工作流
   
const handleEditWorkflow = (workflow: unknown) => {
  currentWorkflow.value = workflow
  designerMode.value = 'edit'
  designerDialogVisible.value = true
}

// 设计工作流
   
const handleDesignWorkflow = (workflow: unknown) => {
  currentWorkflow.value = workflow
  designerMode.value = 'edit'
  designerDialogVisible.value = true
}

// 删除工作流
   
const handleDeleteWorkflow = async (workflow: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工作流 "${workflow.workflowName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('删除成功')
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 切换工作流状态
   
const handleToggleWorkflow = (workflow: unknown) => {
  const status = workflow.isActive ? '启用' : '禁用'
  ElMessage.success(`工作流 "${workflow.workflowName}" 已${status}`)
}

// 刷新统计数据
const handleRefreshStats = () => {
  ElMessage.success('统计数据已刷新')
}

// 导出统计报告
const handleExportStats = () => {
  ElMessage.info('导出统计报告功能开发中...')
}

// 查看审批人详情
   
const handleViewApproverDetail = (approver: unknown) => {
  ElMessage.info(`查看 ${approver.approver} 的详细审批统计`)
}

// 对话框成功回调
const handleApprovalSuccess = () => {
  ElMessage.success('审批成功')
  // 刷新待审批列表
}

const handleDesignerSuccess = () => {
  ElMessage.success('工作流配置成功')
  // 刷新工作流列表
}

const handleBatchApprovalSuccess = () => {
  ElMessage.success('批量审批成功')
  // 刷新待审批列表
}

// 获取优先级标签
const getPriorityTag = (priority: string) => {
  switch (priority) {
    case 'URGENT': return 'danger'
    case 'HIGH': return 'warning'
    case 'MEDIUM': return 'primary'
    case 'LOW': return 'info'
    default: return ''
  }
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'URGENT': return '紧急'
    case 'HIGH': return '高'
    case 'MEDIUM': return '中'
    case 'LOW': return '低'
    default: return priority
  }
}

// 获取剩余时间标签
const getRemainingHoursTag = (hours: number) => {
  if (hours <= 8) return 'danger'
  if (hours <= 24) return 'warning'
  return 'success'
}

// 获取审批结果标签
const getApprovalResultTag = (result: string) => {
  switch (result) {
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    case 'RETURNED': return 'warning'
    default: return ''
  }
}

// 获取审批结果文本
const getApprovalResultText = (result: string) => {
  switch (result) {
    case 'APPROVED': return '通过'
    case 'REJECTED': return '驳回'
    case 'RETURNED': return '退回'
    default: return result
  }
}

// 获取合同类型文本
const getContractTypeText = (type: string) => {
  switch (type) {
    case 'CAREER_ESTABLISHMENT': return '事业编制聘用合同'
    case 'PERSONNEL_AGENCY': return '人事代理合同'
    case 'LABOR_AGREEMENT': return '劳务协议'
    default: return type
  }
}

// 获取通过率标签
const getApprovalRateTag = (rate: number) => {
  if (rate >= 95) return 'success'
  if (rate >= 85) return 'warning'
  return 'danger'
}

// 获取效率标签
const getEfficiencyTag = (efficiency: string) => {
  switch (efficiency) {
    case '优秀': return 'success'
    case '良好': return 'primary'
    case '一般': return 'warning'
    case '较差': return 'danger'
    default: return ''
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  // 初始化数据
  fetchDepartments()
})
</script>

<style scoped>
.contract-approval-workflow {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.function-tabs {
  margin-top: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tab-actions {
  display: flex;
  gap: 8px;
}

.approval-stats {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.urgent {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-icon.overdue {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.today {
  background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.filter-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stats-charts {
  margin-bottom: 20px;
}

.chart-card {
  height: 300px;
}

.chart-container {
  height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 14px;
}
</style>