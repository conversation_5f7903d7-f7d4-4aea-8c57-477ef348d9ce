<template>
  <div class="contract-expiration-analysis">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同到期分析</h2>
      <p>分析合同到期时间分布，提供智能预警和续签建议</p>
    </div>

    <!-- 分析控制台 -->
    <el-card class="analysis-console" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>到期分析控制台</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleAnalyze">
              <el-icon><TrendCharts /></el-icon>
              开始分析
            </el-button>
          </div>
        </div>
      </template>

      <!-- 分析配置 -->
      <div class="analysis-config">
        <el-form :inline="true" :model="analysisConfig" size="small">
          <el-form-item label="分析范围">
            <el-select v-model="analysisConfig.timeRange" placeholder="请选择时间范围">
              <el-option label="未来1个月" value="1month"  />
              <el-option label="未来3个月" value="3months"  />
              <el-option label="未来6个月" value="6months"  />
              <el-option label="未来1年" value="1year"  />
              <el-option label="自定义" value="custom"  />
            </el-select>
          </el-form-item>
          <el-form-item label="合同类型">
            <el-select v-model="analysisConfig.contractType" placeholder="请选择合同类型">
              <el-option label="全部类型" value="all"  />
              <el-option label="劳动合同" value="labor"  />
              <el-option label="聘用合同" value="employment"  />
              <el-option label="临时合同" value="temporary"  />
            </el-select>
          </el-form-item>
          <el-form-item label="部门筛选">
            <el-select v-model="analysisConfig.department" placeholder="请选择部门">
              <el-option label="全部部门" value="all"  />
              <el-option label="技术部" value="tech"  />
              <el-option label="人事部" value="hr"  />
              <el-option label="财务部" value="finance"  />
              <el-option label="市场部" value="marketing"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleRunAnalysis">
              <el-icon><Operation /></el-icon>
              执行分析
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 到期统计 -->
      <div class="expiration-stats">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ expirationStats.total }}</div>
                <div class="stat-label">合同总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card expired">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ expirationStats.expired }}</div>
                <div class="stat-label">已到期</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card expiring">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ expirationStats.expiring }}</div>
                <div class="stat-label">即将到期</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card normal">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ expirationStats.normal }}</div>
                <div class="stat-label">正常</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card renewed">
              <div class="stat-icon">
                <el-icon><RefreshRight /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ expirationStats.renewed }}</div>
                <div class="stat-label">已续签</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card rate">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ expirationStats.renewalRate }}%</div>
                <div class="stat-label">续签率</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 时间分布图 -->
    <el-card class="time-distribution" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>到期时间分布</h3>
          <div class="header-actions">
            <el-button @click="handleExportChart">
              <el-icon><Download /></el-icon>
              导出图表
            </el-button>
          </div>
        </div>
      </template>

      <div class="chart-container">
        <div class="chart-tabs">
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="月度分布" name="monthly">
              <div class="chart-placeholder">
                <div class="chart-info">
                  <h4>未来12个月合同到期分布</h4>
                  <div class="chart-data">
                    <div class="data-item" v-for="(item, index) in monthlyData" :key="index">
                      <div class="data-bar">
                        <div class="bar-fill" :style="{ width: (item.count / 50) * 100 + '%' }"></div>
                      </div>
                      <div class="data-label">{{ item.month }}</div>
                      <div class="data-count">{{ item.count }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="部门分布" name="department">
              <div class="chart-placeholder">
                <div class="chart-info">
                  <h4>各部门合同到期情况</h4>
                  <div class="dept-chart">
                    <div class="dept-item" v-for="(dept, index) in departmentData" :key="index">
                      <div class="dept-name">{{ dept.name }}</div>
                      <div class="dept-progress">
                        <el-progress
                          :percentage="dept.expirationRate"
                          :color="getProgressColor(dept.expirationRate)"
                          :show-text="false"
                         />
                      </div>
                      <div class="dept-stats">
                        <span class="expired">{{ dept.expired }}</span>
                        <span class="total">/ {{ dept.total }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="类型分布" name="type">
              <div class="chart-placeholder">
                <div class="chart-info">
                  <h4>不同合同类型到期分布</h4>
                  <div class="type-chart">
                    <div class="type-item" v-for="(type, index) in typeData" :key="index">
                      <div class="type-icon" :class="type.class">
                        <el-icon><Document /></el-icon>
                      </div>
                      <div class="type-content">
                        <div class="type-name">{{ type.name }}</div>
                        <div class="type-count">{{ type.count }}个</div>
                        <div class="type-percentage">{{ type.percentage }}%</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-card>

    <!-- 预警列表 -->
    <el-card class="warning-list" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>到期预警</h3>
          <div class="header-actions">
            <el-button @click="handleBatchRenewal">
              <el-icon><RefreshRight /></el-icon>
              批量续签
            </el-button>
            <el-button type="primary" @click="handleSendNotification">
              <el-icon><Message /></el-icon>
              发送通知
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="预警级别">
            <el-select v-model="filterForm.warningLevel" placeholder="请选择预警级别" clearable>
              <el-option label="紧急" value="urgent"  />
              <el-option label="重要" value="important"  />
              <el-option label="一般" value="normal"  />
            </el-select>
          </el-form-item>
          <el-form-item label="合同状态">
            <el-select v-model="filterForm.contractStatus" placeholder="请选择合同状态" clearable>
              <el-option label="已到期" value="expired"  />
              <el-option label="即将到期" value="expiring"  />
              <el-option label="正常" value="normal"  />
            </el-select>
          </el-form-item>
          <el-form-item label="员工姓名">
            <el-input 
              v-model="filterForm.employeeName" 
              placeholder="请输入员工姓名"
              clearable
              />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 预警表格 -->
      <el-table :data="warningList" style="width: 100%">
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="contractNumber" label="合同编号" width="150"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="department" label="部门" width="100"  />
        <el-table-column prop="contractType" label="合同类型" width="100"  />
        <el-table-column prop="startDate" label="起始日期" width="120"  />
        <el-table-column prop="endDate" label="结束日期" width="120"  />
        <el-table-column prop="remainingDays" label="剩余天数" width="100">
          <template #default="scope">
            <span :class="getRemainingDaysClass(scope.row.remainingDays)">
              {{ formatRemainingDays(scope.row.remainingDays) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="warningLevel" label="预警级别" width="100">
          <template #default="scope">
            <el-tag :type="getWarningLevelType(scope.row.warningLevel)" size="small">
              {{ getWarningLevelText(scope.row.warningLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="renewalSuggestion" label="续签建议" width="120">
          <template #default="scope">
            <el-tag :type="getRenewalSuggestionType(scope.row.renewalSuggestion)" size="small">
              {{ getRenewalSuggestionText(scope.row.renewalSuggestion) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleViewDetail(scope.row)">
              查看详情
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleInitiateRenewal(scope.row)"
            >
              启动续签
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleSendReminder(scope.row)"
            >
              发送提醒
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="合同详情" width="800px">
      <div class="contract-detail" v-if="currentContract">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="合同编号">{{ currentContract.contractNumber }}</el-descriptions-item>
          <el-descriptions-item label="员工姓名">{{ currentContract.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ currentContract.department }}</el-descriptions-item>
          <el-descriptions-item label="合同类型">{{ currentContract.contractType }}</el-descriptions-item>
          <el-descriptions-item label="起始日期">{{ currentContract.startDate }}</el-descriptions-item>
          <el-descriptions-item label="结束日期">{{ currentContract.endDate }}</el-descriptions-item>
          <el-descriptions-item label="剩余天数">
            <span :class="getRemainingDaysClass(currentContract.remainingDays)">
              {{ formatRemainingDays(currentContract.remainingDays) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="预警级别">
            <el-tag :type="getWarningLevelType(currentContract.warningLevel)" size="small">
              {{ getWarningLevelText(currentContract.warningLevel) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="renewal-analysis">
          <h4>续签分析</h4>
          <div class="analysis-content">
            <div class="analysis-item">
              <div class="analysis-label">续签建议</div>
              <div class="analysis-value">
                <el-tag :type="getRenewalSuggestionType(currentContract.renewalSuggestion)" size="small">
                  {{ getRenewalSuggestionText(currentContract.renewalSuggestion) }}
                </el-tag>
              </div>
            </div>
            <div class="analysis-item">
              <div class="analysis-label">风险评估</div>
              <div class="analysis-value">{{ currentContract.riskAssessment }}</div>
            </div>
            <div class="analysis-item">
              <div class="analysis-label">续签建议</div>
              <div class="analysis-value">{{ currentContract.renewalRecommendation }}</div>
            </div>
          </div>
        </div>

        <div class="renewal-history">
          <h4>续签历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="history in renewalHistory"
              :key="history.id"
              :timestamp="history.timestamp"
              :type="history.type"
            >
              <div class="history-content">
                <div class="history-title">{{ history.title }}</div>
                <div class="history-description">{{ history.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  TrendCharts,
  Operation,
  Document,
  Clock,
  Warning,
  CircleCheck,
  RefreshRight,
  Download,
  Message,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const activeTab = ref('monthly')
const detailDialogVisible = ref(false)
const currentContract = ref(null)

// 分析配置
const analysisConfig = reactive({
  timeRange: '6months',
  contractType: 'all',
  department: 'all'
})

// 到期统计
const expirationStats = reactive({
  total: 456,
  expired: 23,
  expiring: 67,
  normal: 298,
  renewed: 68,
  renewalRate: 75
})

// 筛选表单
const filterForm = reactive({
  warningLevel: '',
  contractStatus: '',
  employeeName: ''
})

// 月度数据
const monthlyData = ref([
  { month: '2025-01', count: 12 },
  { month: '2025-02', count: 18 },
  { month: '2025-03', count: 25 },
  { month: '2025-04', count: 34 },
  { month: '2025-05', count: 28 },
  { month: '2025-06', count: 41 },
  { month: '2025-07', count: 37 },
  { month: '2025-08', count: 29 },
  { month: '2025-09', count: 22 },
  { month: '2025-10', count: 31 },
  { month: '2025-11', count: 26 },
  { month: '2025-12', count: 19 }
])

// 部门数据
const departmentData = ref([
  { name: 'HrHr技术部', total: 120, expired: 15, expirationRate: 12.5 },
  { name: '人事部', total: 45, expired: 8, expirationRate: 17.8 },
  { name: '财务部', total: 38, expired: 5, expirationRate: 13.2 },
  { name: '市场部', total: 67, expired: 12, expirationRate: 17.9 },
  { name: '运营部', total: 52, expired: 7, expirationRate: 13.5 }
])

// 类型数据
const typeData = ref([
  { name: '劳动合同', count: 298, percentage: 65.4, class: 'labor' },
  { name: '聘用合同', count: 124, percentage: 27.2, class: 'employment' },
  { name: '临时合同', count: 34, percentage: 7.5, class: 'temporary' }
])

// 预警列表数据
const warningList = ref([
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '张三',
    department: '技术部',
    contractType: '劳动合同',
    startDate: '2024-01-25',
    endDate: '2025-01-25',
    remainingDays: 2,
    warningLevel: 'urgent',
    renewalSuggestion: 'recommended',
    riskAssessment: '低风险',
    renewalRecommendation: '建议续签3年，薪资调整15%'
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '李四',
    department: '人事部',
    contractType: '聘用合同',
    startDate: '2024-01-30',
    endDate: '2025-01-30',
    remainingDays: 7,
    warningLevel: 'important',
    renewalSuggestion: 'consider',
    riskAssessment: '中风险',
    renewalRecommendation: '建议续签2年，保持现有薪资'
  },
  {
    id: '3',
    contractNumber: 'HKY2025003',
    employeeName: '王五',
    department: '财务部',
    contractType: '劳动合同',
    startDate: '2024-02-15',
    endDate: '2025-02-15',
    remainingDays: 23,
    warningLevel: 'normal',
    renewalSuggestion: 'evaluate',
    riskAssessment: '低风险',
    renewalRecommendation: '建议续签5年，薪资调整10%'
  }
])

// 续签历史数据
const renewalHistory = ref([
  {
    id: '1',
    timestamp: '2024-01-25',
    type: 'success',
    title: '合同签署',
    description: '初次签署劳动合同'
  },
  {
    id: '2',
    timestamp: '2024-07-15',
    type: 'primary',
    title: '薪资调整',
    description: '年中薪资调整，涨幅8%'
  },
  {
    id: '3',
    timestamp: '2024-12-01',
    type: 'warning',
    title: '续签提醒',
    description: '系统自动发送续签提醒'
  }
])

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleAnalyze = () => {
  ElMessage.success('开始分析')
}

const handleRunAnalysis = () => {
  ElMessage.success('执行分析')
}

const handleExportChart = () => {
  ElMessage.success('导出图表')
}

const handleBatchRenewal = () => {
  ElMessage.success('批量续签')
}

const handleSendNotification = () => {
  ElMessage.success('发送通知')
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    warningLevel: '',
    contractStatus: '',
    employeeName: ''
  })
  ElMessage.success('筛选条件已重置')
}

   
const handleViewDetail = (row: unknown) => {
  currentContract.value = row
  detailDialogVisible.value = true
}

   
const handleInitiateRenewal = (row: unknown) => {
  ElMessage.success(`启动续签: ${row.contractNumber}`)
}

   
const handleSendReminder = (row: unknown) => {
  ElMessage.success(`发送提醒: ${row.employeeName}`)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

const getProgressColor = (percentage: number) => {
  if (percentage > 15) return '#f56c6c'
  if (percentage > 10) return '#e6a23c'
  return '#67c23a'
}

const getRemainingDaysClass = (days: number) => {
  if (days <= 0) return 'days-expired'
  if (days <= 7) return 'days-urgent'
  if (days <= 30) return 'days-warning'
  return 'days-normal'
}

const formatRemainingDays = (days: number) => {
  if (days <= 0) return `已过期${Math.abs(days)}天`
  return `${days}天`
}

const getWarningLevelType = (level: string) => {
  const typeMap = {
    'urgent': 'danger',
    'important': 'warning',
    'normal': 'primary'
  }
  return typeMap[level] || 'info'
}

const getWarningLevelText = (level: string) => {
  const textMap = {
    'urgent': '紧急',
    'important': '重要',
    'normal': '一般'
  }
  return textMap[level] || '未知'
}

const getRenewalSuggestionType = (suggestion: string) => {
  const typeMap = {
    'recommended': 'success',
    'consider': 'warning',
    'evaluate': 'primary',
    'not_recommended': 'danger'
  }
  return typeMap[suggestion] || 'info'
}

const getRenewalSuggestionText = (suggestion: string) => {
  const textMap = {
    'recommended': '建议续签',
    'consider': '考虑续签',
    'evaluate': '评估续签',
    'not_recommended': '不建议续签'
  }
  return textMap[suggestion] || '未知'
}

// 生命周期
onMounted(() => {
  total.value = warningList.value.length
})
</script>

<style scoped>
.contract-expiration-analysis {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.analysis-console,
.time-distribution,
.warning-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.analysis-config {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.expiration-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.expired .stat-icon {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stat-card.expiring .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.normal .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.renewed .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.rate .stat-icon {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.chart-container {
  height: 400px;
}

.chart-placeholder {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.chart-info h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.chart-data {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.data-bar {
  width: 200px;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #4299e1, #667eea);
  transition: width 0.3s ease;
}

.data-label {
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.data-count {
  width: 40px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.dept-chart {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dept-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dept-name {
  width: 100px;
  font-size: 14px;
  color: #303133;
}

.dept-progress {
  flex: 1;
  max-width: 300px;
}

.dept-stats {
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.dept-stats .expired {
  color: #f56c6c;
  font-weight: 600;
}

.type-chart {
  display: flex;
  gap: 20px;
}

.type-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.type-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.type-icon.labor {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.type-icon.employment {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.type-icon.temporary {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.type-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.type-name {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.type-count {
  font-size: 12px;
  color: #606266;
}

.type-percentage {
  font-size: 12px;
  color: #909399;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.days-expired {
  color: #f56c6c;
  font-weight: 600;
}

.days-urgent {
  color: #e6a23c;
  font-weight: 600;
}

.days-warning {
  color: #909399;
}

.days-normal {
  color: #67c23a;
}

.contract-detail {
  margin-top: 20px;
}

.renewal-analysis {
  margin-top: 30px;
}

.renewal-analysis h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.analysis-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.analysis-label {
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.analysis-value {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.renewal-history {
  margin-top: 30px;
}

.renewal-history h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.history-content {
  padding: 8px 0;
}

.history-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.history-description {
  color: #606266;
  font-size: 14px;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>