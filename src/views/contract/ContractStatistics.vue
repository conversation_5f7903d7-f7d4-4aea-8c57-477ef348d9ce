<template>
  <div class="contract-statistics">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同统计分析</h2>
      <p>查看合同数量、类型、金额统计和履行情况分析</p>
    </div>

    <!-- 总体统计概览 -->
    <el-row :gutter="20" class="overview-stats">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.total }}</div>
              <div class="stats-label">合同总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon amount">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ formatAmount(overviewStats.totalAmount) }}</div>
              <div class="stats-label">合同总金额</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon active">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.active }}</div>
              <div class="stats-label">有效合同</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon thisMonth">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.thisMonth }}</div>
              <div class="stats-label">本月新签</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="chart-header">
              <span>合同类型分布</span>
              <el-select v-model="typeChartYear" size="small" style="width: 100px;">
                <el-option label="2025年" value="2025"  />
                <el-option label="2024年" value="2024"  />
              </el-select>
            </div>
          </template>
          <div class="chart-container" id="typeChart">
            <div class="chart-placeholder">合同类型分布图</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>合同状态分布</span>
          </template>
          <div class="chart-container" id="statusChart">
            <div class="chart-placeholder">合同状态分布图</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>月度签约趋势</span>
          </template>
          <div class="chart-container" id="trendChart">
            <div class="chart-placeholder">月度签约趋势图</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>部门合同分布</span>
          </template>
          <div class="chart-container" id="departmentChart">
            <div class="chart-placeholder">部门合同分布图</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 合同金额统计 -->
    <el-card class="amount-stats-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>合同金额统计</span>
          <el-button size="small" @click="handleRefreshAmountStats">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </template>

      <el-row :gutter="20" class="amount-stats">
        <el-col :span="8">
          <div class="amount-item">
            <div class="amount-title">事业编制合同</div>
            <div class="amount-content">
              <div class="amount-number">{{ formatAmount(amountStats.careerEstablishment) }}</div>
              <div class="amount-change">
                <el-tag :type="amountStats.careerEstablishmentChange > 0 ? 'success' : 'danger'" size="small">
                  {{ amountStats.careerEstablishmentChange > 0 ? '+' : '' }}{{ amountStats.careerEstablishmentChange }}%
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="amount-item">
            <div class="amount-title">编外人员合同</div>
            <div class="amount-content">
              <div class="amount-number">{{ formatAmount(amountStats.externalStaff) }}</div>
              <div class="amount-change">
                <el-tag :type="amountStats.externalStaffChange > 0 ? 'success' : 'danger'" size="small">
                  {{ amountStats.externalStaffChange > 0 ? '+' : '' }}{{ amountStats.externalStaffChange }}%
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="amount-item">
            <div class="amount-title">项目合作合同</div>
            <div class="amount-content">
              <div class="amount-number">{{ formatAmount(amountStats.projectCooperation) }}</div>
              <div class="amount-change">
                <el-tag :type="amountStats.projectCooperationChange > 0 ? 'success' : 'danger'" size="small">
                  {{ amountStats.projectCooperationChange > 0 ? '+' : '' }}{{ amountStats.projectCooperationChange }}%
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 履行情况分析 -->
    <el-card class="performance-analysis-card" shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>合同履行情况分析</span>
          <div class="header-actions">
            <el-button size="small" @click="handleExportPerformanceReport">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </div>
      </template>

      <!-- 履行统计 -->
      <el-row :gutter="20" class="performance-stats">
        <el-col :span="6">
          <el-card class="performance-card">
            <div class="performance-content">
              <div class="performance-icon normal">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="performance-info">
                <div class="performance-number">{{ performanceStats.normal }}</div>
                <div class="performance-label">正常履行</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="performance-card">
            <div class="performance-content">
              <div class="performance-icon warning">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="performance-info">
                <div class="performance-number">{{ performanceStats.warning }}</div>
                <div class="performance-label">履行异常</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="performance-card">
            <div class="performance-content">
              <div class="performance-icon breach">
                <el-icon><Close /></el-icon>
              </div>
              <div class="performance-info">
                <div class="performance-number">{{ performanceStats.breach }}</div>
                <div class="performance-label">违约终止</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="performance-card">
            <div class="performance-content">
              <div class="performance-icon completed">
                <el-icon><Select /></el-icon>
              </div>
              <div class="performance-info">
                <div class="performance-number">{{ performanceStats.completed }}</div>
                <div class="performance-label">正常完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 履行详情表格 -->
      <el-table :data="performanceDetailList" style="width: 100%; margin-top: 20px;">
        <el-table-column prop="department" label="部门" width="150"  />
        <el-table-column prop="totalContracts" label="合同总数" width="100" align="center"  />
        <el-table-column prop="normalCount" label="正常履行" width="100" align="center"  />
        <el-table-column prop="warningCount" label="履行异常" width="100" align="center"  />
        <el-table-column prop="breachCount" label="违约终止" width="100" align="center"  />
        <el-table-column prop="performanceRate" label="履行率" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getPerformanceRateTag(scope.row.performanceRate)" size="small">
              {{ scope.row.performanceRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="avgDuration" label="平均履行期" width="120"  />
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskLevelTag(scope.row.riskLevel)" size="small">
              {{ scope.row.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleViewDepartmentDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 详细统计表格 -->
    <el-card class="detailed-stats-card" shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>详细统计数据</span>
          <div class="header-actions">
            <el-button size="small" @click="handleExportDetailedStats">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button size="small" @click="handleRefreshDetailedStats">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-form">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-select v-model="detailedFilter.contractType" placeholder="合同类型" clearable>
              <el-option label="事业编制聘用合同" value="CAREER_ESTABLISHMENT"  />
              <el-option label="人事代理合同" value="PERSONNEL_AGENCY"  />
              <el-option label="劳务协议" value="LABOR_AGREEMENT"  />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="detailedFilter.status" placeholder="合同状态" clearable>
              <el-option label="有效" value="ACTIVE"  />
              <el-option label="即将到期" value="EXPIRING"  />
              <el-option label="已到期" value="EXPIRED"  />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="detailedFilter.department" placeholder="部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="detailedFilter.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleFilterDetailedStats">
              <el-icon><Search /></el-icon>
              筛选
            </el-button>
            <el-button @click="handleResetDetailedFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 详细统计表格 -->
      <el-table :data="detailedStatsList" style="width: 100%">
        <el-table-column prop="contractType" label="合同类型" width="150"  />
        <el-table-column prop="totalCount" label="总数" width="80" align="center"  />
        <el-table-column prop="activeCount" label="有效" width="80" align="center"  />
        <el-table-column prop="expiringCount" label="即将到期" width="100" align="center"  />
        <el-table-column prop="expiredCount" label="已到期" width="80" align="center"  />
        <el-table-column prop="totalAmount" label="总金额(万元)" width="120" align="center"  />
        <el-table-column prop="avgAmount" label="平均金额(万元)" width="130" align="center"  />
        <el-table-column prop="renewalRate" label="续签率" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getRenewalRateTag(scope.row.renewalRate)" size="small">
              {{ scope.row.renewalRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleViewTypeDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  Money,
  Check,
  Star,
  Refresh,
  Download,
  Search,
  CircleCheck,
  Warning,
  Close,
  Select
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'

// 响应式数据
const typeChartYear = ref('2025')
const departmentOptions = ref<any[]>([])

// 总体统计数据
const overviewStats = reactive({
  total: 1256,
  totalAmount: 85600000, // 8560万元
  active: 1089,
  thisMonth: 23
})

// 金额统计数据
const amountStats = reactive({
  careerEstablishment: 65200000, // 6520万元
  careerEstablishmentChange: 8.5,
  externalStaff: 15800000, // 1580万元
  externalStaffChange: -2.3,
  projectCooperation: 4600000, // 460万元
  projectCooperationChange: 15.2
})

// 履行情况统计
const performanceStats = reactive({
  normal: 1089,
  warning: 45,
  breach: 12,
  completed: 856
})

// 筛选条件
const detailedFilter = reactive({
  contractType: '',
  status: '',
  department: '',
  dateRange: []
})

// 履行详情列表
const performanceDetailList = ref([
  {
    department: '计算机学院',
    totalContracts: 156,
    normalCount: 148,
    warningCount: 6,
    breachCount: 2,
    performanceRate: 94.9,
    avgDuration: '2.8年',
    riskLevel: '低'
  },
  {
    department: '机械工程学院',
    totalContracts: 128,
    normalCount: 118,
    warningCount: 8,
    breachCount: 2,
    performanceRate: 92.2,
    avgDuration: '2.5年',
    riskLevel: '中'
  },
  {
    department: '管理学院',
    totalContracts: 98,
    normalCount: 92,
    warningCount: 4,
    breachCount: 2,
    performanceRate: 93.9,
    avgDuration: '3.1年',
    riskLevel: '低'
  }
])

// 详细统计列表
const detailedStatsList = ref([
  {
    contractType: '事业编制聘用合同',
    totalCount: 856,
    activeCount: 745,
    expiringCount: 68,
    expiredCount: 43,
    totalAmount: 652.0,
    avgAmount: 76.2,
    renewalRate: 89.5
  },
  {
    contractType: '人事代理合同',
    totalCount: 234,
    activeCount: 198,
    expiringCount: 25,
    expiredCount: 11,
    totalAmount: 158.0,
    avgAmount: 67.5,
    renewalRate: 85.2
  },
  {
    contractType: '劳务协议',
    totalCount: 166,
    activeCount: 146,
    expiringCount: 15,
    expiredCount: 5,
    totalAmount: 46.0,
    avgAmount: 27.7,
    renewalRate: 78.3
  }
])

// 格式化金额
const formatAmount = (amount: number) => {
  if (amount >= 100000000) {
    return (amount / 100000000).toFixed(1) + '亿'
  } else if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万'
  }
  return amount.toString()
}

// 刷新金额统计
const handleRefreshAmountStats = () => {
  ElMessage.success('金额统计数据已刷新')
}

// 导出履行报告
const handleExportPerformanceReport = () => {
  ElMessage.info('导出履行情况报告功能开发中...')
}

// 查看部门详情
   
const handleViewDepartmentDetail = (department: unknown) => {
  ElMessage.info(`查看 ${department.department} 的详细履行情况`)
}

// 导出详细统计
const handleExportDetailedStats = () => {
  ElMessage.info('导出详细统计数据功能开发中...')
}

// 刷新详细统计
const handleRefreshDetailedStats = () => {
  ElMessage.success('详细统计数据已刷新')
}

// 筛选详细统计
const handleFilterDetailedStats = () => {
  ElMessage.info('筛选详细统计功能开发中...')
}

// 重置详细筛选
const handleResetDetailedFilter = () => {
  Object.assign(detailedFilter, {
    contractType: '',
    status: '',
    department: '',
    dateRange: []
  })
}

// 查看类型详情
   
const handleViewTypeDetail = (type: unknown) => {
  ElMessage.info(`查看 ${type.contractType} 的详细统计信息`)
}

// 获取履行率标签
const getPerformanceRateTag = (rate: number) => {
  if (rate >= 95) return 'success'
  if (rate >= 85) return 'warning'
  return 'danger'
}

// 获取风险等级标签
const getRiskLevelTag = (level: string) => {
  switch (level) {
    case '低': return 'success'
    case '中': return 'warning'
    case '高': return 'danger'
    default: return ''
  }
}

// 获取续签率标签
const getRenewalRateTag = (rate: number) => {
  if (rate >= 90) return 'success'
  if (rate >= 80) return 'warning'
  return 'danger'
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  fetchDepartments()
  // 初始化图表和数据
})
</script>

<style scoped>
.contract-statistics {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.overview-stats {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.amount {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.thisMonth {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 350px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.amount-stats {
  margin-bottom: 20px;
}

.amount-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.amount-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 12px;
}

.amount-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.amount-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.performance-stats {
  margin-bottom: 20px;
}

.performance-card {
  cursor: pointer;
  transition: all 0.3s;
}

.performance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.performance-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.performance-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.performance-icon.normal {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.performance-icon.warning {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.performance-icon.breach {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.performance-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.performance-info {
  flex: 1;
}

.performance-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.performance-label {
  font-size: 12px;
  color: #909399;
}

.filter-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}
</style>
