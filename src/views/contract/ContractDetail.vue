<template>
  <div class="contract-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleBack" circle>
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <div class="header-info">
          <h2>合同详情</h2>
          <p>查看和管理教职工合同的详细信息</p>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="handlePrint">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
    </div>

    <!-- 合同基本信息 -->
    <el-card class="basic-info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>基本信息</h3>
          <el-tag 
            :type="getStatusTag(contractDetail.status)" 
            size="large"
          >
            {{ contractDetail.statusName }}
          </el-tag>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>合同编号</label>
            <span>{{ contractDetail.contractNumber }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>合同类型</label>
            <span>{{ contractDetail.contractType }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>签订日期</label>
            <span>{{ contractDetail.signDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>生效日期</label>
            <span>{{ contractDetail.effectiveDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>到期日期</label>
            <span>{{ contractDetail.expiryDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>合同期限</label>
            <span>{{ contractDetail.contractDuration }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 员工信息 -->
    <el-card class="employee-info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>员工信息</h3>
          <el-button size="small" @click="handleViewEmployeeDetail">
            <el-icon><User /></el-icon>
            查看档案
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="4">
          <div class="employee-avatar">
            <el-avatar :size="80" :src="contractDetail.employee.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </div>
        </el-col>
        <el-col :span="20">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>姓名</label>
                <span>{{ contractDetail.employee.name }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>工号</label>
                <span>{{ contractDetail.employee.employeeNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>身份证号</label>
                <span>{{ contractDetail.employee.idNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>联系电话</label>
                <span>{{ contractDetail.employee.phone }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>邮箱</label>
                <span>{{ contractDetail.employee.email }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>所属部门</label>
                <span>{{ contractDetail.employee.department }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>职务</label>
                <span>{{ contractDetail.employee.position }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>职级</label>
                <span>{{ contractDetail.employee.level }}</span>
              </div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-card>

    <!-- 薪资信息 -->
    <el-card class="salary-info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>薪资信息</h3>
          <el-button size="small" @click="handleViewSalaryDetail">
            <el-icon><Money /></el-icon>
            查看详情
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="salary-item">
            <label>基本工资</label>
            <span class="salary-amount">¥{{ contractDetail.salary.basicSalary }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="salary-item">
            <label>岗位津贴</label>
            <span class="salary-amount">¥{{ contractDetail.salary.positionAllowance }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="salary-item">
            <label>绩效工资</label>
            <span class="salary-amount">¥{{ contractDetail.salary.performanceSalary }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="salary-item">
            <label>税前总计</label>
            <span class="salary-amount total">¥{{ contractDetail.salary.totalSalary }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 合同条款 -->
    <el-card class="contract-terms-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>合同条款</h3>
          <el-button size="small" @click="handleViewFullTerms">
            <el-icon><Document /></el-icon>
            查看全文
          </el-button>
        </div>
      </template>
      
      <el-tabs type="border-card">
        <el-tab-pane label="工作内容" name="work">
          <div class="terms-content">
            <p>{{ contractDetail.terms.workContent }}</p>
          </div>
        </el-tab-pane>
        <el-tab-pane label="工作时间" name="time">
          <div class="terms-content">
            <p>{{ contractDetail.terms.workTime }}</p>
          </div>
        </el-tab-pane>
        <el-tab-pane label="薪酬待遇" name="salary">
          <div class="terms-content">
            <p>{{ contractDetail.terms.salaryTerms }}</p>
          </div>
        </el-tab-pane>
        <el-tab-pane label="权利义务" name="rights">
          <div class="terms-content">
            <p>{{ contractDetail.terms.rightsObligations }}</p>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 合同历史 -->
    <el-card class="history-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>合同历史</h3>
          <el-button size="small" @click="handleViewAllHistory">
            <el-icon><Clock /></el-icon>
            查看全部
          </el-button>
        </div>
      </template>
      
      <el-timeline>
        <el-timeline-item
          v-for="history in contractDetail.history"
          :key="history.id"
          :timestamp="history.timestamp"
          :type="getHistoryType(history.type)"
        >
          <div class="history-item">
            <div class="history-title">{{ history.title }}</div>
            <div class="history-description">{{ history.description }}</div>
            <div class="history-operator">操作人：{{ history.operator }}</div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 相关文件 -->
    <el-card class="files-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>相关文件</h3>
          <el-button size="small" @click="handleUploadFile">
            <el-icon><Upload /></el-icon>
            上传文件
          </el-button>
        </div>
      </template>
      
      <el-table :data="contractDetail.files" style="width: 100%">
        <el-table-column prop="fileName" label="文件名" width="200"  />
        <el-table-column prop="fileType" label="文件类型" width="120">
          <template #default="scope">
            <el-tag size="small">{{ scope.row.fileType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小" width="120"  />
        <el-table-column prop="uploadTime" label="上传时间" width="150"  />
        <el-table-column prop="uploader" label="上传人" width="100"  />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handlePreviewFile(scope.row)">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button size="small" @click="handleDownloadFile(scope.row)">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button size="small" type="danger" @click="handleDeleteFile(scope.row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 操作记录 -->
    <el-card class="operation-log-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>操作记录</h3>
          <el-button size="small" @click="handleExportLog">
            <el-icon><Download /></el-icon>
            导出日志
          </el-button>
        </div>
      </template>
      
      <el-table :data="contractDetail.operationLogs" style="width: 100%">
        <el-table-column prop="operation" label="操作" width="120"  />
        <el-table-column prop="operator" label="操作人" width="100"  />
        <el-table-column prop="operationTime" label="操作时间" width="150"  />
        <el-table-column prop="operationResult" label="操作结果" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.operationResult === 'success' ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.operationResult === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" show-overflow-tooltip  />
        <el-table-column prop="ip" label="IP地址" width="120"  />
      </el-table>
    </el-card>

    <!-- 合同操作 -->
    <el-card class="contract-actions-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>合同操作</h3>
        </div>
      </template>
      
      <div class="action-buttons">
        <el-button type="primary" @click="handleRenewal">
          <el-icon><Refresh /></el-icon>
          续签合同
        </el-button>
        <el-button type="warning" @click="handleModification">
          <el-icon><Edit /></el-icon>
          变更合同
        </el-button>
        <el-button type="info" @click="handleSuspension">
          <el-icon><VideoPlay /></el-icon>
          暂停合同
        </el-button>
        <el-button type="danger" @click="handleTermination">
          <el-icon><Close /></el-icon>
          终止合同
        </el-button>
      </div>
    </el-card>

    <!-- 文件上传对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传文件" width="500px">
      <el-upload
        ref="uploadRef"
        :multiple="true"
        :auto-upload="false"
        :on-change="handleFileChange"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处或<em>点击选择文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持jpg/png/pdf/doc/docx文件，单个文件大小不超过10MB
          </div>
        </template>
      </el-upload>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmUpload">确定上传</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="文件预览" width="80%">
      <div class="file-preview">
        <iframe
          v-if="previewFile && previewFile.fileType === 'PDF'"
          :src="previewFile.url"
          width="100%"
          height="600px"
          frameborder="0"
        ></iframe>
        <img
          v-else-if="previewFile && ['JPG', 'PNG', 'JPEG'].includes(previewFile.fileType)"
          :src="previewFile.url"
          style="max-width: 100%; max-height: 600px;"
        />
        <div v-else class="no-preview">
          <el-icon size="60"><Document /></el-icon>
          <p>该文件类型不支持预览</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Printer,
  Download,
  Edit,
  User,
  Money,
  Document,
  Clock,
  Upload,
  View,
  Delete,
  Refresh,
  Close,
  VideoPlay,
  UploadFilled
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const uploadDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const uploadRef = ref()
const previewFile = ref(null)

// 合同详情数据
const contractDetail = reactive({
  id: route.params.id || '1',
  contractNumber: 'HKY2025001',
  contractType: '事业编制聘用合同',
  signDate: '2025-01-01',
  effectiveDate: '2025-01-01',
  expiryDate: '2028-12-31',
  contractDuration: '3年',
  status: 'ACTIVE',
  statusName: '有效',
  employee: {
    name: 'HrHr张三',
    employeeNumber: 'EMP001',
    idNumber: '33010219850315****',
    phone: '138****8888',
    email: '<EMAIL>',
    department: '计算机学院',
    position: '讲师',
    level: '中级',
    avatar: ''
  },
  salary: {
    basicSalary: 8000,
    positionAllowance: 2000,
    performanceSalary: 3000,
    totalSalary: 13000
  },
  terms: {
    workContent: '负责计算机科学与技术专业的教学工作，包括但不限于课程教学、实验指导、学生指导等工作。',
    workTime: '按照学校规定的工作时间执行，每周工作时间不少于40小时。',
    salaryTerms: '基本工资8000元/月，岗位津贴2000元/月，绩效工资根据考核结果发放。',
    rightsObligations: '享有教师的各项权利，承担相应的教学、科研和社会服务义务。'
  },
  history: [
    {
      id: '1',
      type: 'CREATE',
      title: '合同创建',
      description: '新建教职工聘用合同',
      operator: '人事处',
      timestamp: '2025-01-01 10:00:00'
    },
    {
      id: '2',
      type: 'SIGN',
      title: '合同签署',
      description: '双方签署合同，合同正式生效',
      operator: '张三',
      timestamp: '2025-01-01 14:00:00'
    },
    {
      id: '3',
      type: 'ARCHIVE',
      title: '合同归档',
      description: '合同文件已归档至人事档案',
      operator: '档案室',
      timestamp: '2025-01-02 09:00:00'
    }
  ],
  files: [
    {
      id: '1',
      fileName: '聘用合同.pdf',
      fileType: 'PDF',
      fileSize: '2.5MB',
      uploadTime: '2025-01-01 10:00:00',
      uploader: '人事处',
      url: '/files/contract_001.pdf'
    },
    {
      id: '2',
      fileName: '身份证复印件.jpg',
      fileType: 'JPG',
      fileSize: '1.2MB',
      uploadTime: '2025-01-01 10:30:00',
      uploader: '张三',
      url: '/files/id_card_001.jpg'
    },
    {
      id: '3',
      fileName: '学历证明.pdf',
      fileType: 'PDF',
      fileSize: '3.1MB',
      uploadTime: '2025-01-01 11:00:00',
      uploader: '张三',
      url: '/files/education_001.pdf'
    }
  ],
  operationLogs: [
    {
      id: '1',
      operation: '查看合同',
      operator: '李四',
      operationTime: '2025-01-20 09:30:00',
      operationResult: 'success',
      description: '查看合同详情',
      ip: '*************'
    },
    {
      id: '2',
      operation: '编辑合同',
      operator: '王五',
      operationTime: '2025-01-19 15:20:00',
      operationResult: 'success',
      description: '修改合同基本信息',
      ip: '*************'
    },
    {
      id: '3',
      operation: '上传文件',
      operator: '张三',
      operationTime: '2025-01-18 10:15:00',
      operationResult: 'success',
      description: '上传学历证明文件',
      ip: '*************'
    }
  ]
})

// 方法
const handleBack = () => {
  router.go(-1)
}

const handlePrint = () => {
  window.print()
}

const handleExport = () => {
  ElMessage.success('导出合同详情成功')
}

const handleEdit = () => {
  router.push({
    name: 'contract-information-edit',
    params: { id: contractDetail.id }
  })
}

const handleViewEmployeeDetail = () => {
  router.push({
    name: 'employee-detail',
    params: { id: contractDetail.employee.employeeNumber }
  })
}

const handleViewSalaryDetail = () => {
  router.push({
    name: 'salary-detail',
    params: { id: contractDetail.employee.employeeNumber }
  })
}

const handleViewFullTerms = () => {
  ElMessage.info('查看合同全文功能开发中...')
}

const handleViewAllHistory = () => {
  ElMessage.info('查看全部历史记录功能开发中...')
}

const handleUploadFile = () => {
  uploadDialogVisible.value = true
}

   
const handleFileChange = (file: unknown) => {
  console.log('文件变化:', file)
}

const handleConfirmUpload = () => {
  ElMessage.success('文件上传成功')
  uploadDialogVisible.value = false
}

   
const handlePreviewFile = (file: unknown) => {
  previewFile.value = file
  previewDialogVisible.value = true
}

   
const handleDownloadFile = (file: unknown) => {
  ElMessage.success(`下载文件: ${file.fileName}`)
}

   
const handleDeleteFile = async (file: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.fileName}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    const index = contractDetail.files.findIndex(f => f.id === file.id)
    if (index > -1) {
      contractDetail.files.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消操作
  }
}

const handleExportLog = () => {
  ElMessage.success('导出操作日志成功')
}

const handleRenewal = () => {
  router.push({
    name: 'contract-renewal',
    params: { id: contractDetail.id }
  })
}

const handleModification = () => {
  router.push({
    name: 'contract-modification',
    params: { id: contractDetail.id }
  })
}

const handleSuspension = () => {
  ElMessage.info('暂停合同功能开发中...')
}

const handleTermination = () => {
  ElMessage.info('终止合同功能开发中...')
}

// 辅助方法
const getStatusTag = (status: string) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'EXPIRED': return 'danger'
    case 'SUSPENDED': return 'warning'
    case 'TERMINATED': return 'info'
    default: return ''
  }
}

const getHistoryType = (type: string) => {
  switch (type) {
    case 'CREATE': return 'primary'
    case 'SIGN': return 'success'
    case 'MODIFY': return 'warning'
    case 'ARCHIVE': return 'info'
    default: return ''
  }
}

// 生命周期
onMounted(() => {
  // 根据路由参数加载合同详情
  console.log('加载合同详情，ID:', route.params.id)
})
</script>

<style scoped>
.contract-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-info {
  margin-left: 16px;
}

.header-info h2 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.basic-info-card,
.employee-info-card,
.salary-info-card,
.contract-terms-card,
.history-card,
.files-card,
.operation-log-card,
.contract-actions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.info-item {
  margin-bottom: 16px;
}

.info-item label {
  display: block;
  margin-bottom: 4px;
  color: #909399;
  font-size: 12px;
}

.info-item span {
  color: #303133;
  font-size: 14px;
}

.employee-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.salary-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.salary-item label {
  display: block;
  margin-bottom: 8px;
  color: #909399;
  font-size: 12px;
}

.salary-amount {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.salary-amount.total {
  color: #409eff;
  font-size: 20px;
}

.terms-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  line-height: 1.6;
}

.history-item {
  padding: 8px 0;
}

.history-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.history-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.history-operator {
  color: #909399;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.file-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.no-preview {
  text-align: center;
  color: #909399;
}

.no-preview p {
  margin-top: 16px;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

/* 打印样式 */
@media print {
  .header-actions,
  .action-buttons {
    display: none !important;
  }
  
  .contract-detail {
    padding: 0;
  }
  
  .el-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}
</style>