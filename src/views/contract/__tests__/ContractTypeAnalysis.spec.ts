 
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * ContractTypeAnalysis 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import ContractTypeAnalysis from '../ContractTypeAnalysis.vue'
describe('ContractTypeAnalysis', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(ContractTypeAnalysis)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-contract-type-analysis').exists()).toBe(true)
  })

  it('应该匹配快照', () => {
    const wrapper = mount(ContractTypeAnalysis)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
