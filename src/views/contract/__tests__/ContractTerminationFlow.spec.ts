 
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * ContractTerminationFlow 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import ContractTerminationFlow from '../ContractTerminationFlow.vue'
describe('ContractTerminationFlow', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(ContractTerminationFlow)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-contract-termination-flow').exists()).toBe(true)
  })

  it('应该匹配快照', () => {
    const wrapper = mount(ContractTerminationFlow)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
