<template>
  <div class="contract-ledger-report">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同台账报表</h2>
      <p>生成详细的合同台账报表，支持多种格式导出和打印</p>
    </div>

    <!-- 报表配置 -->
    <el-card class="report-config" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>报表配置</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleGenerateReport">
              <el-icon><Document /></el-icon>
              生成报表
            </el-button>
          </div>
        </div>
      </template>

      <div class="config-form">
        <el-form :model="reportConfig" :rules="configRules" ref="reportConfigRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报表类型" prop="reportType">
                <el-select v-model="reportConfig.reportType" placeholder="请选择报表类型">
                  <el-option label="全量台账" value="full"  />
                  <el-option label="增量台账" value="incremental"  />
                  <el-option label="到期台账" value="expiring"  />
                  <el-option label="异常台账" value="abnormal"  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据范围" prop="dataRange">
                <el-select v-model="reportConfig.dataRange" placeholder="请选择数据范围">
                  <el-option label="全部合同" value="all"  />
                  <el-option label="生效合同" value="active"  />
                  <el-option label="已终止合同" value="terminated"  />
                  <el-option label="草稿合同" value="draft"  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="时间范围" prop="timeRange">
                <el-date-picker
                  v-model="reportConfig.timeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                 />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门筛选" prop="departments">
                <el-select v-model="reportConfig.departments" multiple placeholder="请选择部门">
                  <el-option label="全部部门" value="all"  />
                  <el-option label="技术部" value="tech"  />
                  <el-option label="人事部" value="hr"  />
                  <el-option label="财务部" value="finance"  />
                  <el-option label="市场部" value="marketing"  />
                  <el-option label="运营部" value="operations"  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="导出格式" prop="exportFormat">
                <el-checkbox-group v-model="reportConfig.exportFormat">
                  <el-checkbox value="excel">Excel</el-checkbox>
                  <el-checkbox value="pdf">PDF</el-checkbox>
                  <el-checkbox value="word">Word</el-checkbox>
                  <el-checkbox value="csv">CSV</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报表模板" prop="template">
                <el-select v-model="reportConfig.template" placeholder="请选择报表模板">
                  <el-option label="标准模板" value="standard"  />
                  <el-option label="详细模板" value="detailed"  />
                  <el-option label="简化模板" value="simplified"  />
                  <el-option label="自定义模板" value="custom"  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="包含字段">
            <el-checkbox-group v-model="reportConfig.includeFields">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-checkbox value="basic">基本信息</el-checkbox>
                </el-col>
                <el-col :span="6">
                  <el-checkbox value="salary">薪资信息</el-checkbox>
                </el-col>
                <el-col :span="6">
                  <el-checkbox value="benefits">福利信息</el-checkbox>
                </el-col>
                <el-col :span="6">
                  <el-checkbox value="performance">绩效信息</el-checkbox>
                </el-col>
                <el-col :span="6">
                  <el-checkbox value="attendance">考勤信息</el-checkbox>
                </el-col>
                <el-col :span="6">
                  <el-checkbox value="training">培训信息</el-checkbox>
                </el-col>
                <el-col :span="6">
                  <el-checkbox value="compliance">合规信息</el-checkbox>
                </el-col>
                <el-col :span="6">
                  <el-checkbox value="history">历史记录</el-checkbox>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 报表统计 -->
    <el-card class="report-statistics" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>数据统计</h3>
        </div>
      </template>

      <div class="statistics-grid">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.total }}</div>
                <div class="stat-label">合同总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card active">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.active }}</div>
                <div class="stat-label">生效合同</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card terminated">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.terminated }}</div>
                <div class="stat-label">已终止</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card expiring">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.expiring }}</div>
                <div class="stat-label">即将到期</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card renewed">
              <div class="stat-icon">
                <el-icon><RefreshRight /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.renewed }}</div>
                <div class="stat-label">已续签</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card value">
              <div class="stat-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.totalValue }}万</div>
                <div class="stat-label">合同总价值</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 报表预览 -->
    <el-card class="report-preview" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>报表预览</h3>
          <div class="header-actions">
            <el-button @click="handlePreview">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button type="primary" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button @click="handlePrint">
              <el-icon><Printer /></el-icon>
              打印
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="合同状态">
            <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
              <el-option label="生效" value="active"  />
              <el-option label="草稿" value="draft"  />
              <el-option label="已终止" value="terminated"  />
              <el-option label="已到期" value="expired"  />
            </el-select>
          </el-form-item>
          <el-form-item label="合同类型">
            <el-select v-model="filterForm.type" placeholder="请选择类型" clearable>
              <el-option label="劳动合同" value="labor"  />
              <el-option label="聘用合同" value="employment"  />
              <el-option label="临时合同" value="temporary"  />
            </el-select>
          </el-form-item>
          <el-form-item label="员工姓名">
            <el-input 
              v-model="filterForm.employeeName" 
              placeholder="请输入员工姓名"
              clearable
              />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 报表表格 -->
      <el-table :data="ledgerData" style="width: 100%" stripe>
        <el-table-column type="index" label="序号" width="60"  />
        <el-table-column prop="contractNumber" label="合同编号" width="150"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="department" label="部门" width="100"  />
        <el-table-column prop="position" label="岗位" width="120"  />
        <el-table-column prop="contractType" label="合同类型" width="100">
          <template #default="scope">
            <el-tag :type="getContractTypeColor(scope.row.contractType)" size="small">
              {{ getContractTypeText(scope.row.contractType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="起始日期" width="120"  />
        <el-table-column prop="endDate" label="结束日期" width="120"  />
        <el-table-column prop="duration" label="合同期限" width="100"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="salary" label="薪资" width="100"  />
        <el-table-column prop="signDate" label="签署日期" width="120"  />
        <el-table-column prop="renewalCount" label="续签次数" width="100"  />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="handleViewDetail(scope.row)">
              查看详情
            </el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 导出进度对话框 -->
    <el-dialog v-model="exportDialogVisible" title="导出进度" width="500px">
      <div class="export-progress">
        <div class="progress-info">
          <div class="progress-label">正在导出报表...</div>
          <div class="progress-detail">{{ exportProgress.current }} / {{ exportProgress.total }}</div>
        </div>
        <el-progress
          :percentage="exportProgress.percentage"
          :status="exportProgress.status"
          :stroke-width="20"
         />
        <div class="progress-actions">
          <el-button @click="handleCancelExport">取消</el-button>
          <el-button type="primary" @click="handleDownload" :disabled="exportProgress.percentage < 100">
            下载
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractLedgerReport'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Document,
  CircleCheck,
  CircleClose,
  Warning,
  RefreshRight,
  Money,
  View,
  Download,
  Printer,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const exportDialogVisible = ref(false)
const reportConfigRef = ref()

// 报表配置
const reportConfig = reactive({
  reportType: 'full',
  dataRange: 'all',
  timeRange: [],
  departments: [],
  exportFormat: ['excel'],
  template: 'standard',
  includeFields: ['basic', 'salary', 'benefits']
})

// 配置验证规则
const configRules = {
  reportType: [
    { required: true, message: '请选择报表类型', trigger: 'change' }
  ],
  dataRange: [
    { required: true, message: '请选择数据范围', trigger: 'change' }
  ],
  exportFormat: [
    { required: true, message: '请选择导出格式', trigger: 'change' }
  ]
}

// 统计数据
const statistics = reactive({
  total: 1246,
  active: 892,
  terminated: 254,
  expiring: 67,
  renewed: 158,
  totalValue: 2846.5
})

// 筛选表单
const filterForm = reactive({
  status: '',
  type: '',
  employeeName: ''
})

// 导出进度
const exportProgress = reactive({
  current: 0,
  total: 100,
  percentage: 0,
  status: 'active'
})

// 台账数据
const ledgerData = ref([
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '张三',
    department: '技术部',
    position: '高级工程师',
    contractType: 'labor',
    startDate: '2024-01-01',
    endDate: '2026-12-31',
    duration: '3年',
    status: 'active',
    salary: '15,000',
    signDate: '2023-12-25',
    renewalCount: 1
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '李四',
    department: '人事部',
    position: '人事专员',
    contractType: 'employment',
    startDate: '2024-02-01',
    endDate: '2025-01-31',
    duration: '1年',
    status: 'expiring',
    salary: '8,000',
    signDate: '2024-01-28',
    renewalCount: 0
  },
  {
    id: '3',
    contractNumber: 'HKY2025003',
    employeeName: '王五',
    department: '财务部',
    position: '财务经理',
    contractType: 'labor',
    startDate: '2023-06-01',
    endDate: '2025-05-31',
    duration: '2年',
    status: 'active',
    salary: '12,000',
    signDate: '2023-05-28',
    renewalCount: 2
  },
  {
    id: '4',
    contractNumber: 'HKY2025004',
    employeeName: '赵六',
    department: '市场部',
    position: '市场专员',
    contractType: 'temporary',
    startDate: '2024-03-01',
    endDate: '2024-08-31',
    duration: '6个月',
    status: 'terminated',
    salary: '6,000',
    signDate: '2024-02-26',
    renewalCount: 0
  }
])

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleGenerateReport = () => {
  reportConfigRef.value.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('开始生成报表')
    }
  })
}

const handlePreview = () => {
  ElMessage.success('预览报表')
}

const handleExport = () => {
  exportDialogVisible.value = true
  exportProgress.current = 0
  exportProgress.percentage = 0
  exportProgress.status = 'active'
  
  // 模拟导出进度
  const timer = setInterval(() => {
    exportProgress.current += 10
    exportProgress.percentage = exportProgress.current
    if (exportProgress.current >= 100) {
      exportProgress.status = 'success'
      clearInterval(timer)
      ElMessage.success('导出完成')
    }
  }, 300)
}

const handlePrint = () => {
  ElMessage.success('打印报表')
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    status: '',
    type: '',
    employeeName: ''
  })
  ElMessage.success('筛选条件已重置')
}

   
const handleViewDetail = (row: unknown) => {
  ElMessage.success(`查看详情: ${row.contractNumber}`)
}

   
const handleEdit = (row: unknown) => {
  ElMessage.success(`编辑合同: ${row.contractNumber}`)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

const handleCancelExport = () => {
  exportDialogVisible.value = false
  ElMessage.info('已取消导出')
}

const handleDownload = () => {
  exportDialogVisible.value = false
  ElMessage.success('开始下载')
}

const getContractTypeColor = (type: string) => {
  const colorMap = {
    'labor': 'primary',
    'employment': 'success',
    'temporary': 'warning'
  }
  return colorMap[type] || 'info'
}

const getContractTypeText = (type: string) => {
  const textMap = {
    'labor': '劳动合同',
    'employment': '聘用合同',
    'temporary': '临时合同'
  }
  return textMap[type] || '未知'
}

const getStatusColor = (status: string) => {
  const colorMap = {
    'active': 'success',
    'draft': 'info',
    'terminated': 'danger',
    'expired': 'warning',
    'expiring': 'warning'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    'active': '生效',
    'draft': '草稿',
    'terminated': '已终止',
    'expired': '已到期',
    'expiring': '即将到期'
  }
  return textMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  total.value = ledgerData.value.length
})
</script>

<style scoped>
.contract-ledger-report {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.report-config,
.report-statistics,
.report-preview {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.config-form {
  padding: 20px;
}

.statistics-grid {
  padding: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.active .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.terminated .stat-icon {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stat-card.expiring .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.renewed .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.value .stat-icon {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.export-progress {
  padding: 20px;
  text-align: center;
}

.progress-info {
  margin-bottom: 20px;
}

.progress-label {
  font-size: 16px;
  color: #303133;
  margin-bottom: 8px;
}

.progress-detail {
  font-size: 14px;
  color: #606266;
}

.progress-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>