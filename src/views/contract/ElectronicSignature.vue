<template>
  <div class="electronic-signature">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>电子签章</h2>
      <p>管理合同电子签章，支持多种签章类型和防伪验证</p>
    </div>

    <!-- 签章操作卡片 -->
    <el-card class="signature-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>签章操作</h3>
          <div class="header-actions">
            <el-button @click="handlePreview">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button type="primary" @click="handleStartSign">
              <el-icon><EditPen /></el-icon>
              开始签章
            </el-button>
          </div>
        </div>
      </template>

      <!-- 文档预览区域 -->
      <div class="document-preview">
        <div class="preview-toolbar">
          <el-button-group>
            <el-button @click="handleZoomIn">
              <el-icon><ZoomIn /></el-icon>
              放大
            </el-button>
            <el-button @click="handleZoomOut">
              <el-icon><ZoomOut /></el-icon>
              缩小
            </el-button>
            <el-button @click="handleResetZoom">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-button-group>
          <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
        </div>
        
        <div class="preview-container" ref="previewContainer">
          <div 
            class="document-page" 
            :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }"
            @click="handlePageClick"
          >
            <!-- 文档内容 -->
            <div class="document-content">
              <div class="document-header">
                <h1>{{ contractData.title }}</h1>
                <div class="contract-info">
                  <p>合同编号: {{ contractData.contractNumber }}</p>
                  <p>签署日期: {{ contractData.signDate }}</p>
                </div>
              </div>
              
              <div class="document-body">
                <div class="contract-parties">
                  <div class="party">
                    <h3>甲方（用人单位）</h3>
                    <p>单位名称: {{ contractData.partyA.name }}</p>
                    <p>法定代表人: {{ contractData.partyA.representative }}</p>
                    <p>联系地址: {{ contractData.partyA.address }}</p>
                  </div>
                  
                  <div class="party">
                    <h3>乙方（受聘人员）</h3>
                    <p>姓名: {{ contractData.partyB.name }}</p>
                    <p>身份证号: {{ contractData.partyB.idNumber }}</p>
                    <p>联系地址: {{ contractData.partyB.address }}</p>
                  </div>
                </div>
                
                <div class="contract-terms">
                  <h3>合同条款</h3>
                  <div class="term-item">
                    <h4>一、工作内容和要求</h4>
                    <p>{{ contractData.terms.workContent }}</p>
                  </div>
                  
                  <div class="term-item">
                    <h4>二、工作时间和地点</h4>
                    <p>{{ contractData.terms.workTime }}</p>
                  </div>
                  
                  <div class="term-item">
                    <h4>三、劳动报酬</h4>
                    <p>{{ contractData.terms.salary }}</p>
                  </div>
                  
                  <div class="term-item">
                    <h4>四、社会保险和福利</h4>
                    <p>{{ contractData.terms.benefits }}</p>
                  </div>
                </div>
                
                <!-- 签章位置 -->
                <div class="signature-areas">
                  <div class="signature-area party-a">
                    <h4>甲方签章</h4>
                    <div 
                      class="signature-box"
                      :class="{ 'has-signature': signatures.partyA }"
                      @click="handleSignatureClick('partyA')"
                    >
                      <div v-if="signatures.partyA" class="signature-content">
                        <img :src="signatures.partyA.image" alt="签章" />
                        <div class="signature-info">
                          <p>{{ signatures.partyA.signerName }}</p>
                          <p>{{ signatures.partyA.signTime }}</p>
                        </div>
                      </div>
                      <div v-else class="signature-placeholder">
                        <el-icon><Edit /></el-icon>
                        <p>点击此处签章</p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="signature-area party-b">
                    <h4>乙方签章</h4>
                    <div 
                      class="signature-box"
                      :class="{ 'has-signature': signatures.partyB }"
                      @click="handleSignatureClick('partyB')"
                    >
                      <div v-if="signatures.partyB" class="signature-content">
                        <img :src="signatures.partyB.image" alt="签章" />
                        <div class="signature-info">
                          <p>{{ signatures.partyB.signerName }}</p>
                          <p>{{ signatures.partyB.signTime }}</p>
                        </div>
                      </div>
                      <div v-else class="signature-placeholder">
                        <el-icon><Edit /></el-icon>
                        <p>点击此处签章</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 签章工具栏 -->
    <el-card class="signature-tools" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>签章工具</h3>
          <el-button type="success" @click="handleCompleteSign" :disabled="!canComplete">
            <el-icon><Check /></el-icon>
            完成签章
          </el-button>
        </div>
      </template>
      
      <div class="tools-content">
        <el-tabs v-model="activeToolTab" type="card">
          <!-- 签章选择 -->
          <el-tab-pane label="签章选择" name="stamps">
            <div class="stamp-selection">
              <div class="stamp-types">
                <h4>签章类型</h4>
                <el-radio-group v-model="selectedStampType">
                  <el-radio value="official">公章</el-radio>
                  <el-radio value="personal">个人签名</el-radio>
                  <el-radio value="contract">合同专用章</el-radio>
                  <el-radio value="financial">财务专用章</el-radio>
                </el-radio-group>
              </div>
              
              <div class="stamp-library">
                <h4>签章库</h4>
                <div class="stamp-grid">
                  <div 
                    v-for="stamp in availableStamps"
                    :key="stamp.id"
                    class="stamp-item"
                    :class="{ 'selected': selectedStamp?.id === stamp.id }"
                    @click="selectStamp(stamp)"
                  >
                    <img :src="stamp.image" :alt="stamp.name" />
                    <p>{{ stamp.name }}</p>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 手写签名 -->
          <el-tab-pane label="手写签名" name="handwriting">
            <div class="handwriting-pad">
              <div class="pad-header">
                <h4>手写签名</h4>
                <div class="pad-controls">
                  <el-button @click="clearCanvas">
                    <el-icon><Delete /></el-icon>
                    清除
                  </el-button>
                  <el-button type="primary" @click="saveSignature">
                    <el-icon><Check /></el-icon>
                    保存
                  </el-button>
                </div>
              </div>
              
              <canvas 
                ref="signatureCanvas"
                class="signature-canvas"
                width="400"
                height="200"
                @mousedown="startDrawing"
                @mousemove="draw"
                @mouseup="stopDrawing"
                @mouseleave="stopDrawing"
              ></canvas>
              
              <div class="pen-settings">
                <el-form :inline="true" size="small">
                  <el-form-item label="笔刷颜色">
                    <el-color-picker v-model="penColor"  />
                  </el-form-item>
                  <el-form-item label="笔刷大小">
                    <el-slider v-model="penSize" :min="1" :max="10" style="width: 100px;"  />
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 签章验证 -->
          <el-tab-pane label="签章验证" name="verification">
            <div class="verification-panel">
              <div class="verification-info">
                <h4>签章验证信息</h4>
                <el-table :data="signatureVerifications" style="width: 100%">
                  <el-table-column prop="party" label="签章方" width="100"  />
                  <el-table-column prop="signerName" label="签章人" width="120"  />
                  <el-table-column prop="signTime" label="签章时间" width="150"  />
                  <el-table-column prop="status" label="验证状态" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === 'verified' ? 'success' : 'warning'">
                        {{ scope.row.status === 'verified' ? '已验证' : '待验证' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="certificate" label="证书信息" show-overflow-tooltip  />
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button size="small" @click="handleVerifySignature(scope.row)">
                        验证
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 签章历史记录 -->
    <el-card class="signature-history" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>签章历史</h3>
          <el-button @click="handleExportHistory">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>
        </div>
      </template>
      
      <el-table :data="signatureHistory" style="width: 100%">
        <el-table-column prop="version" label="版本" width="80"  />
        <el-table-column prop="operation" label="操作" width="120"  />
        <el-table-column prop="operator" label="操作人" width="100"  />
        <el-table-column prop="operationTime" label="操作时间" width="150"  />
        <el-table-column prop="ipAddress" label="IP地址" width="120"  />
        <el-table-column prop="description" label="描述" show-overflow-tooltip  />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" @click="handleViewVersion(scope.row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 签章对话框 -->
    <el-dialog v-model="signatureDialogVisible" title="签章确认" width="500px">
      <div class="signature-confirm">
        <div class="signature-preview">
          <h4>签章预览</h4>
          <div class="preview-box">
            <img v-if="selectedStamp" :src="selectedStamp.image" :alt="selectedStamp.name" />
            <div v-else class="no-signature">请选择签章</div>
          </div>
        </div>
        
        <el-form :model="signatureForm" label-width="100px">
          <el-form-item label="签章方">
            <span>{{ getPartyName(currentSignatureParty) }}</span>
          </el-form-item>
          <el-form-item label="签章人">
            <el-input v-model="signatureForm.signerName" placeholder="请输入签章人姓名"   />
          </el-form-item>
          <el-form-item label="签章密码">
            <el-input 
              v-model="signatureForm.password" 
              type="password" 
              placeholder="请输入签章密码"
              show-password
              />
          </el-form-item>
          <el-form-item label="签章说明">
            <el-input 
              v-model="signatureForm.description" 
              type="textarea" 
              :rows="3"
              placeholder="请输入签章说明（可选）"
              />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="signatureDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmSignature">确认签章</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  View,
  EditPen,
  ZoomIn,
  ZoomOut,
  Refresh,
  Edit,
  Check,
  Delete,
  Download
} from '@element-plus/icons-vue'

// 响应式数据
const zoomLevel = ref(1)
const previewContainer = ref()
const signatureCanvas = ref()
const activeToolTab = ref('stamps')
const selectedStampType = ref('official')
const selectedStamp = ref(null)
const signatureDialogVisible = ref(false)
const currentSignatureParty = ref('')
const penColor = ref('#000000')
const penSize = ref(3)
const isDrawing = ref(false)
const lastPoint = ref(null)

// 合同数据
const contractData = reactive({
  title: '杭州科技学院教职工聘用合同',
  contractNumber: 'HKY2025001',
  signDate: '2025-01-22',
  partyA: {
    name: 'HrHr杭州科技学院',
    representative: '张校长',
    address: '浙江省杭州市西湖区学院路123号'
  },
  partyB: {
    name: '张三',
    idNumber: '33010219850315****',
    address: '浙江省杭州市西湖区居住地址'
  },
  terms: {
    workContent: '负责计算机科学与技术专业的教学工作，包括课程教学、实验指导、学生管理等。',
    workTime: '每周工作时间不少于40小时，按照学校规定的工作时间执行。',
    salary: '基本工资8000元/月，岗位津贴2000元/月，绩效工资根据考核结果确定。',
    benefits: '享受国家规定的各项社会保险和住房公积金，享受学校规定的各项福利待遇。'
  }
})

// 签章数据
const signatures = reactive({
  partyA: null,
  partyB: null
})

// 签章表单
const signatureForm = reactive({
  signerName: '',
  password: '',
  description: ''
})

// 可用签章
const availableStamps = ref([
  {
    id: '1',
    name: '杭州科技学院公章',
    type: 'official',
    image: '/images/stamps/official_stamp.png'
  },
  {
    id: '2',
    name: '合同专用章',
    type: 'contract',
    image: '/images/stamps/contract_stamp.png'
  },
  {
    id: '3',
    name: '张三个人签名',
    type: 'personal',
    image: '/images/stamps/personal_sign.png'
  },
  {
    id: '4',
    name: '财务专用章',
    type: 'financial',
    image: '/images/stamps/financial_stamp.png'
  }
])

// 签章验证数据
const signatureVerifications = ref([
  {
    party: '甲方',
    signerName: '张校长',
    signTime: '2025-01-22 10:30:00',
    status: 'verified',
    certificate: 'RSA-2048 证书，有效期至2026-01-22'
  },
  {
    party: '乙方',
    signerName: '张三',
    signTime: '2025-01-22 11:00:00',
    status: 'pending',
    certificate: '待验证'
  }
])

// 签章历史记录
const signatureHistory = ref([
  {
    version: 'v1.0',
    operation: '创建合同',
    operator: '系统管理员',
    operationTime: '2025-01-22 09:00:00',
    ipAddress: '*************',
    description: '初始创建合同文档'
  },
  {
    version: 'v1.1',
    operation: '甲方签章',
    operator: '张校长',
    operationTime: '2025-01-22 10:30:00',
    ipAddress: '*************',
    description: '甲方完成签章'
  },
  {
    version: 'v1.2',
    operation: '乙方签章',
    operator: '张三',
    operationTime: '2025-01-22 11:00:00',
    ipAddress: '*************',
    description: '乙方完成签章'
  }
])

// 计算属性
const canComplete = computed(() => {
  return signatures.partyA && signatures.partyB
})

// 方法
const handlePreview = () => {
  ElMessage.success('预览功能')
}

const handleStartSign = () => {
  ElMessage.info('开始签章流程')
}

const handleZoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.2, 3)
}

const handleZoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.2, 0.2)
}

const handleResetZoom = () => {
  zoomLevel.value = 1
}

const handlePageClick = (event: MouseEvent) => {
  console.log('页面点击位置:', event.clientX, event.clientY)
}

const handleSignatureClick = (party: string) => {
  currentSignatureParty.value = party
  signatureDialogVisible.value = true
}

   
const selectStamp = (stamp: unknown) => {
  selectedStamp.value = stamp
}

const getPartyName = (party: string) => {
  return party === 'partyA' ? '甲方（用人单位）' : '乙方（受聘人员）'
}

const handleConfirmSignature = () => {
  if (!selectedStamp.value) {
    ElMessage.warning('请选择签章')
    return
  }
  if (!signatureForm.signerName) {
    ElMessage.warning('请输入签章人姓名')
    return
  }
  if (!signatureForm.password) {
    ElMessage.warning('请输入签章密码')
    return
  }
  
  const signatureData = {
    image: selectedStamp.value.image,
    signerName: signatureForm.signerName,
    signTime: new Date().toLocaleString(),
    stampId: selectedStamp.value.id,
    stampName: selectedStamp.value.name,
    description: signatureForm.description
  }
  
  signatures[currentSignatureParty.value] = signatureData
  signatureDialogVisible.value = false
  
  // 重置表单
  Object.assign(signatureForm, {
    signerName: '',
    password: '',
    description: ''
  })
  
  ElMessage.success('签章成功')
}

const handleCompleteSign = () => {
  ElMessage.success('签章完成，合同已生效')
}

   
const handleVerifySignature = (signature: unknown) => {
  ElMessage.success(`验证签章: ${signature.signerName}`)
}

const handleExportHistory = () => {
  ElMessage.success('导出签章历史记录')
}

   
const handleViewVersion = (version: unknown) => {
  ElMessage.info(`查看版本: ${version.version}`)
}

// 手写签名相关方法
const startDrawing = (event: MouseEvent) => {
  isDrawing.value = true
  const canvas = signatureCanvas.value
  const rect = canvas.getBoundingClientRect()
  lastPoint.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
}

const draw = (event: MouseEvent) => {
  if (!isDrawing.value) return
  
  const canvas = signatureCanvas.value
  const ctx = canvas.getContext('2d')
  const rect = canvas.getBoundingClientRect()
  
  const currentPoint = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
  
  ctx.beginPath()
  ctx.moveTo(lastPoint.value.x, lastPoint.value.y)
  ctx.lineTo(currentPoint.x, currentPoint.y)
  ctx.strokeStyle = penColor.value
  ctx.lineWidth = penSize.value
  ctx.lineCap = 'round'
  ctx.stroke()
  
  lastPoint.value = currentPoint
}

const stopDrawing = () => {
  isDrawing.value = false
}

const clearCanvas = () => {
  const canvas = signatureCanvas.value
  const ctx = canvas.getContext('2d')
  ctx.clearRect(0, 0, canvas.width, canvas.height)
}

const saveSignature = () => {
  const canvas = signatureCanvas.value
  const dataURL = canvas.toDataURL()
  
  // 创建手写签名作为可用签章
  const handwritingStamp = {
    id: `handwriting_${Date.now()}`,
    name: '手写签名',
    type: 'personal',
    image: dataURL
  }
  
  availableStamps.value.push(handwritingStamp)
  selectedStamp.value = handwritingStamp
  
  ElMessage.success('手写签名已保存')
}

// 生命周期
onMounted(() => {
  // 初始化签章数据
})
</script>

<style scoped>
.electronic-signature {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.signature-card,
.signature-tools,
.signature-history {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.document-preview {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.zoom-level {
  color: #606266;
  font-size: 14px;
}

.preview-container {
  height: 600px;
  overflow: auto;
  background: #f0f2f5;
  padding: 20px;
}

.document-page {
  background: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  width: 600px;
  min-height: 800px;
}

.document-content {
  padding: 40px;
}

.document-header {
  text-align: center;
  margin-bottom: 30px;
}

.document-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
}

.contract-info {
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 14px;
}

.contract-parties {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.party {
  width: 48%;
}

.party h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.party p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.contract-terms {
  margin-bottom: 40px;
}

.contract-terms h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
}

.term-item {
  margin-bottom: 20px;
}

.term-item h4 {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.term-item p {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.signature-areas {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
}

.signature-area {
  width: 48%;
}

.signature-area h4 {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
}

.signature-box {
  width: 200px;
  height: 120px;
  border: 2px dashed #d4d7de;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.signature-box:hover {
  border-color: #409eff;
}

.signature-box.has-signature {
  border-color: #67c23a;
  border-style: solid;
}

.signature-content {
  text-align: center;
}

.signature-content img {
  max-width: 150px;
  max-height: 80px;
  margin-bottom: 8px;
}

.signature-info p {
  margin: 2px 0;
  color: #606266;
  font-size: 12px;
}

.signature-placeholder {
  text-align: center;
  color: #909399;
}

.signature-placeholder .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.signature-placeholder p {
  margin: 0;
  font-size: 14px;
}

.tools-content {
  margin-top: 20px;
}

.stamp-selection {
  display: flex;
  gap: 40px;
}

.stamp-types {
  flex: 1;
}

.stamp-library {
  flex: 2;
}

.stamp-types h4,
.stamp-library h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.stamp-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.stamp-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.stamp-item:hover {
  border-color: #409eff;
}

.stamp-item.selected {
  border-color: #409eff;
  background: #f0f7ff;
}

.stamp-item img {
  width: 60px;
  height: 60px;
  object-fit: contain;
  margin-bottom: 8px;
}

.stamp-item p {
  margin: 0;
  color: #606266;
  font-size: 12px;
}

.handwriting-pad {
  text-align: center;
}

.pad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pad-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.pad-controls {
  display: flex;
  gap: 8px;
}

.signature-canvas {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: crosshair;
}

.pen-settings {
  margin-top: 20px;
}

.verification-panel {
  margin-top: 20px;
}

.verification-info h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.signature-confirm {
  text-align: center;
}

.signature-preview {
  margin-bottom: 20px;
}

.signature-preview h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.preview-box {
  width: 150px;
  height: 100px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px auto;
}

.preview-box img {
  max-width: 120px;
  max-height: 80px;
}

.no-signature {
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}
</style>