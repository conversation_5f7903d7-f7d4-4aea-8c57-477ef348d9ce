<template>
  <div class="document-loan-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Document /></el-icon>
        证照借还管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增借用
        </el-button>
        <el-button @click="handleBatchReturn">
          <el-icon><RefreshLeft /></el-icon>
          批量归还
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出记录
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="借用人">
          <el-input
            v-model="searchForm.borrowerName"
            placeholder="请输入借用人姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="证照类型">
          <el-select
            v-model="searchForm.documentType"
            placeholder="请选择证照类型"
            clearable
            style="width: 150px"
          >
            <el-option label="护照" value="passport"  />
            <el-option label="身份证" value="id_card"  />
            <el-option label="学历证书" value="diploma"  />
            <el-option label="职业资格证" value="qualification"  />
            <el-option label="其他证件" value="other"  />
          </el-select>
        </el-form-item>
        <el-form-item label="借用状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="借用中" value="borrowed"  />
            <el-option label="已归还" value="returned"  />
            <el-option label="逾期未还" value="overdue"  />
            <el-option label="已取消" value="cancelled"  />
          </el-select>
        </el-form-item>
        <el-form-item label="借用时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总借用数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon borrowed">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.borrowed }}</div>
              <div class="stat-label">借用中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon overdue">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.overdue }}</div>
              <div class="stat-label">逾期未还</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.returnRate }}%</div>
              <div class="stat-label">归还率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 借用记录列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="loanId" label="借用编号" width="120"  />
        <el-table-column prop="borrowerName" label="借用人" width="100"  />
        <el-table-column prop="employeeId" label="员工工号" width="120"  />
        <el-table-column prop="department" label="所属部门" width="120"  />
        <el-table-column prop="documentType" label="证照类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.documentType)">
              {{ getTypeLabel(row.documentType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="documentName" label="证照名称" min-width="150"  />
        <el-table-column prop="borrowDate" label="借用日期" width="120"  />
        <el-table-column prop="expectedReturnDate" label="预计归还" width="120"  />
        <el-table-column prop="actualReturnDate" label="实际归还" width="120">
          <template #default="{ row }">
            {{ row.actualReturnDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="borrowDays" label="借用天数" width="100">
          <template #default="{ row }">
            {{ row.borrowDays }}天
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)" v-if="row.status === 'borrowed'">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleReturn(row)" v-if="row.status === 'borrowed'">
              归还
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="extend" v-if="row.status === 'borrowed'">
                    延期归还
                  </el-dropdown-item>
                  <el-dropdown-item command="remind" v-if="row.status === 'borrowed'">
                    归还提醒
                  </el-dropdown-item>
                  <el-dropdown-item command="history">借用历史</el-dropdown-item>
                  <el-dropdown-item command="print">打印凭证</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 借用详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="借用编号" prop="loanId">
              <el-input v-model="formData.loanId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="借用人" prop="borrowerName">
              <el-select v-model="formData.borrowerName" :disabled="isView" style="width: 100%">
                <el-option label="张三" value="张三"  />
                <el-option label="李四" value="李四"  />
                <el-option label="王五" value="王五"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="证照类型" prop="documentType">
              <el-select v-model="formData.documentType" :disabled="isView" style="width: 100%">
                <el-option label="护照" value="passport"  />
                <el-option label="身份证" value="id_card"  />
                <el-option label="学历证书" value="diploma"  />
                <el-option label="职业资格证" value="qualification"  />
                <el-option label="其他证件" value="other"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证照名称" prop="documentName">
              <el-input v-model="formData.documentName" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="证照编号" prop="documentNumber">
              <el-input v-model="formData.documentNumber" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发证机关" prop="issuingAuthority">
              <el-input v-model="formData.issuingAuthority" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="借用日期" prop="borrowDate">
              <el-date-picker
                v-model="formData.borrowDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计归还日期" prop="expectedReturnDate">
              <el-date-picker
                v-model="formData.expectedReturnDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="借用天数" prop="borrowDays">
              <el-input-number
                v-model="formData.borrowDays"
                :min="1"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">天</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="formData.contactPhone" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="借用目的" prop="purpose">
          <el-input
            v-model="formData.purpose"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入借用目的"
            />
        </el-form-item>
        <el-form-item label="使用地点" prop="usageLocation">
          <el-input
            v-model="formData.usageLocation"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入使用地点"
            />
        </el-form-item>
        <el-form-item label="保管承诺" prop="custodyCommitment">
          <el-input
            v-model="formData.custodyCommitment"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入保管承诺"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'DocumentLoanManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Plus,
  RefreshLeft,
  Download,
  Search,
  Refresh,
  Clock,
  Warning,
  TrendCharts,
  ArrowDown
} from '@element-plus/icons-vue'
import { documentLoanApi } from '@/api/comprehensiveService'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)
const selectedLoans = ref([])

const searchForm = reactive({
  borrowerName: '',
  documentType: '',
  status: '',
  dateRange: []
})

const stats = reactive({
  total: 156,
  borrowed: 45,
  overdue: 8,
  returnRate: 92.3
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    loanId: '*********',
    borrowerName: '张三',
    employeeId: 'EMP001',
    department: '计算机学院',
    documentType: 'passport',
    documentName: '中华人民共和国护照',
    documentNumber: '*********',
    issuingAuthority: '杭州市公安局',
    borrowDate: '2024-06-01',
    expectedReturnDate: '2024-06-15',
    actualReturnDate: '',
    borrowDays: 14,
    status: 'borrowed',
    contactPhone: '13800138001',
    purpose: '出国参加学术会议',
    usageLocation: '美国斯坦福大学',
    custodyCommitment: '妥善保管，按时归还，如有遗失承担相应责任',
    notes: '重要证件，请妥善保管'
  }
])

const formData = reactive({
  loanId: '',
  borrowerName: '',
  documentType: '',
  documentName: '',
  documentNumber: '',
  issuingAuthority: '',
  borrowDate: '',
  expectedReturnDate: '',
  borrowDays: 1,
  contactPhone: '',
  purpose: '',
  usageLocation: '',
  custodyCommitment: '',
  notes: ''
})

const formRules = {
  loanId: [{ required: true, message: '请输入借用编号', trigger: 'blur' }],
  borrowerName: [{ required: true, message: '请选择借用人', trigger: 'change' }],
  documentType: [{ required: true, message: '请选择证照类型', trigger: 'change' }],
  documentName: [{ required: true, message: '请输入证照名称', trigger: 'blur' }],
  borrowDate: [{ required: true, message: '请选择借用日期', trigger: 'change' }],
  expectedReturnDate: [{ required: true, message: '请选择预计归还日期', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    borrowerName: '',
    documentType: '',
    status: '',
    dateRange: []
  })
  loadData()
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedLoans.value = selection as unknown // 临时修复类型不匹配
}

const handleAdd = () => {
  dialogTitle.value = '新增证照借用'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看证照借用'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑证照借用'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleReturn = (row: unknown) => {
  ElMessageBox.confirm(`确定要归还 ${row.borrowerName} 借用的 ${row.documentName} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('归还成功')
  })
}

const handleBatchReturn = () => {
  if (selectedLoans.value.length === 0) {
    ElMessage.warning('请先选择要批量归还的记录')
    return
  }
  ElMessage.info('批量归还功能开发中')
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'extend':
      ElMessage.info(`延期归还 ${row.borrowerName} 的 ${row.documentName}`)
      break
    case 'remind':
      ElMessage.success(`已发送归还提醒给 ${row.borrowerName}`)
      break
    case 'history':
      ElMessage.info(`查看 ${row.borrowerName} 的借用历史`)
      break
    case 'print':
      ElMessage.info(`打印 ${row.borrowerName} 的借用凭证`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除 ${row.borrowerName} 的借用记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleExport = () => {
  ElMessage.info('导出记录功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    loanId: '',
    borrowerName: '',
    documentType: '',
    documentName: '',
    documentNumber: '',
    issuingAuthority: '',
    borrowDate: '',
    expectedReturnDate: '',
    borrowDays: 1,
    contactPhone: '',
    purpose: '',
    usageLocation: '',
    custodyCommitment: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      status: searchForm.status,
      documentType: searchForm.documentType,
      borrowerName: searchForm.borrowerName,
      isOverdue: searchForm.status === 'overdue' ? true : undefined
    }
    
    const response = await documentLoanApi.getPage(params)
    if (response.data.success) {
      tableData.value = response.data.data.content.map(item => ({
        id: item.id,
        loanId: item.loanCode,
        borrowerName: item.borrowerName,
        department: item.departmentName,
        documentType: item.documentType,
        documentNumber: item.documentNumber,
        purpose: item.purpose,
        borrowDate: item.borrowDate,
        expectedReturnDate: item.expectedReturnDate,
        actualReturnDate: item.actualReturnDate,
        status: item.status.toLowerCase(),
        overdueReason: item.overdueReason || '',
        notes: '',
        approvalStatus: item.approvalStatus,
        isOverdue: item.isOverdue
      }))
      pagination.total = response.data.data.totalElements
      
      // 更新统计数据
      stats.total = response.data.data.totalElements
   
      stats.borrowed = response.data.data.content.filter((item: unknown) => !item.actualReturnDate).length
   
      stats.returned = response.data.data.content.filter((item: unknown) => item.actualReturnDate).length
   
      stats.overdue = response.data.data.content.filter((item: unknown) => item.isOverdue).length
    } else {
      throw new Error(response.data.message)
    }
  } catch (__error) {
    console.error('加载证照借还数据失败:', error)
    // 使用模拟数据
    tableData.value = [
      {
        id: '1',
        loanId: '*********',
        borrowerName: '张三',
        department: '人事处',
        documentType: 'passport',
        documentNumber: '*********',
        purpose: '出国办理公务',
        borrowDate: '2024-06-01',
        expectedReturnDate: '2024-06-15',
        actualReturnDate: '',
        status: 'borrowed',
        overdueReason: '',
        notes: '',
        approvalStatus: 'APPROVED',
        isOverdue: false
      }
    ]
    pagination.total = 156
    ElMessage.warning('数据加载失败，显示默认数据')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    passport: 'primary',
    id_card: 'success',
    diploma: 'warning',
    qualification: 'info',
    other: 'danger'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    passport: '护照',
    id_card: '身份证',
    diploma: '学历证书',
    qualification: '职业资格证',
    other: '其他证件'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    borrowed: 'warning',
    returned: 'success',
    overdue: 'danger',
    cancelled: 'info'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    borrowed: '借用中',
    returned: '已归还',
    overdue: '逾期未还',
    cancelled: '已取消'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.document-loan-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.borrowed {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.overdue {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
