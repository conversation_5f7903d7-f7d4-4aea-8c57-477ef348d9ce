<template>
  <div class="employee-add">
    <!-- 页面头部 -->
    <el-page-header @back="handleBack">
      <template #content>
        <span class="text-lg font-600">新增员工</span>
      </template>
    </el-page-header>

    <!-- 步骤条 -->
    <el-card class="steps-card">
      <el-steps :active="activeStep" align-center finish-status="success">
        <el-step title="基本信息" description="填写员工基本信息"  />
        <el-step title="教育经历" description="填写学习经历"  />
        <el-step title="工作经历" description="填写工作经历"  />
        <el-step title="家庭信息" description="填写家庭成员信息"  />
        <el-step title="其他信息" description="补充其他必要信息"  />
        <el-step title="确认提交" description="检查并提交信息"  />
      </el-steps>
    </el-card>

    <!-- 表单内容区 -->
    <el-card class="form-card" v-loading="loading">
      <div class="form-container">
        <!-- 步骤1：基本信息 -->
        <BasicInfoForm
          v-if="activeStep === 0"
          v-model="formData"
          ref="basicInfoRef"
          @validate="handleStepValidate"
        />

        <!-- 步骤2：教育经历 -->
        <EducationForm
          v-if="activeStep === 1"
          v-model:education-list="formData.educationHistory"
          ref="educationRef"
          @validate="handleStepValidate"
        />

        <!-- 步骤3：工作经历 -->
        <WorkForm
          v-if="activeStep === 2"
          v-model:work-list="formData.workExperience"
          ref="workRef"
          @validate="handleStepValidate"
        />

        <!-- 步骤4：家庭信息 -->
        <FamilyForm
          v-if="activeStep === 3"
          v-model:family-list="formData.familyMembers"
          ref="familyRef"
          @validate="handleStepValidate"
        />

        <!-- 步骤5：其他信息 -->
        <OtherInfoForm
          v-if="activeStep === 4"
          v-model="formData"
          ref="otherInfoRef"
          @validate="handleStepValidate"
        />

        <!-- 步骤6：确认提交 -->
        <ConfirmSubmit
          v-if="activeStep === 5"
          :form-data="formData"
          ref="confirmRef"
          @edit="handleEditStep"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleSaveDraft" :loading="savingDraft">
          <el-icon><Document /></el-icon>
          保存草稿
        </el-button>
        <div class="step-actions">
          <el-button v-if="activeStep > 0" @click="handlePrev">
            上一步
          </el-button>
          <el-button
            v-if="activeStep < 5"
            type="primary"
            @click="handleNext"
            :loading="validating"
          >
            下一步
          </el-button>
          <el-button
            v-if="activeStep === 5"
            type="primary"
            @click="handleSubmit"
            :loading="submitting"
          >
            提交
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
defineOptions({
  name: 'AddPage'
})

import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import BasicInfoForm from './components/BasicInfoForm.vue'
import EducationForm from './components/EducationForm.vue'
import WorkForm from './components/WorkForm.vue'
import FamilyForm from './components/FamilyForm.vue'
import OtherInfoForm from './components/OtherInfoForm.vue'
import ConfirmSubmit from './components/ConfirmSubmit.vue'
import employeeApi from '@/api/modules/employee'
import type { EmployeeDetail } from '@/types/employee'

// 路由实例
const router = useRouter()

// 当前步骤
const activeStep = ref(0)

// 加载状态
const loading = ref(false)
const validating = ref(false)
const submitting = ref(false)
const savingDraft = ref(false)

// 表单引用
const basicInfoRef = ref<unknown>()
const educationRef = ref<unknown>()
const workRef = ref<unknown>()
const familyRef = ref<unknown>()
const otherInfoRef = ref<unknown>()
const confirmRef = ref<unknown>()

// 表单数据
const formData = reactive<Partial<EmployeeDetail>>({
  // 基本信息
  fullName: '',
  gender: undefined,
  dateOfBirth: '',
  ethnicity: '',
  politicalStatus: '',
  nativePlace: '',
  placeOfBirth: '',
  idType: 'idCard',
  idNumber: '',
  workStartDate: '',
  hireDate: '',
  phoneNumber: '',
  email: '',
  institutionId: '',
  institutionName: '',
  positionId: '',
  positionName: '',
  highLevelTalentCategory: '',
  
  // 信息子集
  educationHistory: [],
  workExperience: [],
  familyMembers: [],
  
  // 其他信息
  emergencyContact: '',
  emergencyContactPhone: '',
  currentAddress: '',
  postalCode: '',
  remarks: ''
})

// 步骤表单引用映射
const stepFormRefs = computed(() => [
  basicInfoRef.value,
  educationRef.value,
  workRef.value,
  familyRef.value,
  otherInfoRef.value,
  confirmRef.value
])

// 自动保存定时器
let autoSaveTimer: NodeJS.Timeout | null = null

// 组件挂载时
onMounted(() => {
  // 加载草稿数据
  loadDraft()
  
  // 启动自动保存
  startAutoSave()
})

// 组件卸载时
onBeforeUnmount(() => {
  // 清除自动保存定时器
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer)
  }
})

// 加载草稿数据
const loadDraft = async () => {
  try {
    const draft = localStorage.getItem('employee_add_draft')
    if (draft) {
      const draftData = JSON.parse(draft)
      Object.assign(formData, draftData.data)
      activeStep.value = draftData.step || 0
      
      ElMessage.info('已恢复上次填写的内容')
    }
  } catch (__error) {
    console.error('加载草稿失败:', error)
  }
}

// 启动自动保存
const startAutoSave = () => {
  // 每30秒自动保存一次
  autoSaveTimer = setInterval(() => {
    saveDraftToLocal()
  }, 30000)
}

// 保存草稿到本地
const saveDraftToLocal = () => {
  try {
    const draftData = {
      data: formData,
      step: activeStep.value,
      timestamp: new Date().toISOString()
    }
    localStorage.setItem('employee_add_draft', JSON.stringify(draftData))
  } catch (__error) {
    console.error('保存草稿失败:', error)
  }
}

// 清除草稿
const clearDraft = () => {
  localStorage.removeItem('employee_add_draft')
}

// 返回上一页
const handleBack = async () => {
  try {
    await ElMessageBox.confirm(
      '返回将丢失当前填写的内容，是否确认？',
      '提示',
      {
        confirmButtonText: '确认返回',
        cancelButtonText: '继续填写',
        type: 'warning'
      }
    )
    
    clearDraft()
    router.back()
  } catch {
    // 用户取消
  }
}

// 处理步骤验证
const handleStepValidate = (valid: boolean) => {
  validating.value = false
  if (valid && activeStep.value < 5) {
    activeStep.value++
    saveDraftToLocal()
  }
}

// 上一步
const handlePrev = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}

// 下一步
const handleNext = async () => {
  const currentForm = stepFormRefs.value[activeStep.value]
  if (currentForm && currentForm.validate) {
    validating.value = true
    await currentForm.validate()
  }
}

// 保存草稿
const handleSaveDraft = async () => {
  try {
    savingDraft.value = true
    
    // 保存到本地
    saveDraftToLocal()
    
    // 如果需要，也可以保存到服务器
    // await employeeApi.saveDraft(formData)
    
    ElMessage.success('草稿保存成功')
  } catch (__error) {
    ElMessage.error('草稿保存失败')
  } finally {
    savingDraft.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 验证确认状态
    const confirmForm = confirmRef.value
    if (confirmForm && confirmForm.validate) {
      const confirmed = await confirmForm.validate()
      if (!confirmed) {
        ElMessage.warning('请勾选确认信息无误')
        return
      }
    }
    
    submitting.value = true
    
    // 生成工号
    const employeeNumber = await generateEmployeeNumber()
    formData.employeeNumber = employeeNumber
    
    // 提交数据
    await employeeApi.create(formData as EmployeeDetail)
    
    // 清除草稿
    clearDraft()
    
    ElMessage.success('员工信息创建成功')
    
    // 跳转到员工列表
    router.push('/employee/list')
  } catch (__error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 生成工号
const generateEmployeeNumber = async (): Promise<string> => {
  try {
    // 调用后端接口生成工号
    const {data: _data} =  await employeeApi.generateEmployeeNumber({
      year: new Date().getFullYear(),
      type: formData.personnelType || 'regular'
    })
    return data.employeeNumber
  } catch (__error) {
    // 如果后端生成失败，使用前端临时方案
    const year 
  
  .steps-card {
    margin-top: 20px;
    
    :deep(.el-card__body) {
      padding: 30px 20px;
    }
  }
  
  .form-card {
    margin-top: 20px;
    min-height: 500px;
    
    .form-container {
      min-height: 400px;
      padding: 20px;
    }
    
    .form-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
      
      .step-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .employee-add {
    padding: 10px;
    
    .steps-card {
      :deep(.el-step__title) {
        font-size: 12px;
      }
      
      :deep(.el-step__description) {
        display: none;
      }
    }
    
    .form-card {
      .form-actions {
        flex-direction: column;
        gap: 10px;
        
        .step-actions {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }
  }
}
</style>