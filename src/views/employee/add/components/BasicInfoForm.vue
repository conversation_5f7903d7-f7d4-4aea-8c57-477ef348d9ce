<template>
  <el-form
    ref="formRef"
    :model="localData"
    :rules="rules"
    label-width="120px"
    class="basic-info-form"
  >
    <el-row :gutter="20">
      <!-- 个人照片上传 -->
      <el-col :span="24" :md="6">
        <div class="photo-upload">
          <el-upload
            class="avatar-uploader"
            :action="uploadConfig.action"
            :headers="uploadConfig.headers"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
            accept="image/jpeg,image/png"
          >
            <el-avatar v-if="localData.photoUrl" v-lazy="localData.photoUrl" :size="120"   />
            <div v-else class="avatar-placeholder">
              <el-icon :size="40"><User /></el-icon>
              <div class="upload-text">点击上传照片</div>
            </div>
          </el-upload>
          <div class="photo-tips">
            支持JPG/PNG格式<br>
            建议尺寸：400x600像素<br>
            文件大小不超过2MB
          </div>
        </div>
      </el-col>

      <!-- 基本信息表单 -->
      <el-col :span="24" :md="18">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="fullName">
              <el-input
                v-model="localData.fullName"
                placeholder="请输入姓名"
                maxlength="50"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="localData.gender">
                <el-radio value="male">男</el-radio>
                <el-radio value="female">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="出生年月" prop="dateOfBirth">
              <el-date-picker
                v-model="localData.dateOfBirth"
                type="date"
                placeholder="选择出生日期"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledBirthDate"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="民族" prop="ethnicity">
              <el-select v-model="localData.ethnicity" placeholder="请选择民族">
                <el-option
                  v-for="item in ethnicityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="政治面貌" prop="politicalStatus">
              <el-select v-model="localData.politicalStatus" placeholder="请选择政治面貌">
                <el-option
                  v-for="item in politicalStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="籍贯" prop="nativePlace">
              <el-cascader
                v-model="localData.nativePlace"
                :options="regionOptions"
                :props="{ value: 'label' }"
                placeholder="请选择籍贯"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="出生地" prop="placeOfBirth">
              <el-cascader
                v-model="localData.placeOfBirth"
                :options="regionOptions"
                :props="{ value: 'label' }"
                placeholder="请选择出生地"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="证件类型" prop="idType">
              <el-select v-model="localData.idType" placeholder="请选择证件类型">
                <el-option label="身份证" value="idCard"  />
                <el-option label="港澳通行证" value="hkMacaoPass"  />
                <el-option label="台胞证" value="taiwanPass"  />
                <el-option label="护照" value="passport"  />
                <el-option label="其他" value="other"  />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="证件号码" prop="idNumber">
              <el-input
                v-model="localData.idNumber"
                :placeholder="idNumberPlaceholder"
                maxlength="30"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="参加工作时间" prop="workStartDate">
              <el-date-picker
                v-model="localData.workStartDate"
                type="date"
                placeholder="选择参加工作时间"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledWorkDate"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="入职日期" prop="hireDate">
              <el-date-picker
                v-model="localData.hireDate"
                type="date"
                placeholder="选择入职日期"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledHireDate"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phoneNumber">
              <el-input
                v-model="localData.phoneNumber"
                placeholder="请输入手机号码"
                maxlength="11"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="localData.email"
                placeholder="请输入邮箱地址"
                maxlength="100"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="所属机构" prop="institutionId">
              <el-tree-select
                v-model="localData.institutionId"
                :data="organizationTree"
                :props="{
                  label: 'name',
                  value: 'id'
                }"
                placeholder="请选择所属机构"
                @change="handleInstitutionChange"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="当前岗位" prop="positionId">
              <el-select
                v-model="localData.positionId"
                placeholder="请选择岗位"
                :disabled="!localData.institutionId"
                @change="handlePositionChange"
              >
                <el-option
                  v-for="item in positionOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="人员类型" prop="personnelType">
              <el-select v-model="localData.personnelType" placeholder="请选择人员类型">
                <el-option label="在编人员" value="regular"  />
                <el-option label="编外人员" value="contract"  />
                <el-option label="兼职教师" value="partTime"  />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="高层次人才" prop="highLevelTalentCategory">
              <el-select
                v-model="localData.highLevelTalentCategory"
                placeholder="请选择高层次人才类别"
                clearable
              >
                <el-option label="A类（国家级领军人才）" value="A"  />
                <el-option label="B类（省级领军人才）" value="B"  />
                <el-option label="C类（市级领军人才）" value="C"  />
                <el-option label="D类（高级人才）" value="D"  />
                <el-option label="E类（基础人才）" value="E"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">

defineOptions({
  name: 'BasicInfoForm'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  ethnicityOptions,
  politicalStatusOptions
} from '@/composables/useEmployee'
import organizationApi from '@/api/modules/organization'
import positionApi from '@/api/modules/position'
import type { EmployeeDetail } from '@/types/employee'

interface Props {
  modelValue: Partial<EmployeeDetail>
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: Partial<EmployeeDetail>]
  'validate': [valid: boolean]
}>()

// 定义区域选项
const regionOptions = ref([
  {
    label: '北京',
    children: [
      { label: '东城区' },
      { label: '西城区' },
      { label: '朝阳区' },
      { label: '海淀区' }
    ]
  },
  {
    label: '上海',
    children: [
      { label: '黄浦区' },
      { label: '徐汇区' },
      { label: '长宁区' },
      { label: '静安区' }
    ]
  },
  {
    label: '广东',
    children: [
      { label: '广州市' },
      { label: '深圳市' },
      { label: '珠海市' },
      { label: '佛山市' }
    ]
  },
  {
    label: '浙江',
    children: [
      { label: '杭州市' },
      { label: '宁波市' },
      { label: '温州市' },
      { label: '嘉兴市' }
    ]
  }
])

// 表单实例
const formRef = ref<FormInstance>()

// 本地数据
const localData = reactive({ ...props.modelValue })

// 组织机构树
const organizationTree = ref<any[]>([])

// 岗位选项
const positionOptions = ref<any[]>([])

// 上传配置
const uploadConfig = {
  action: '/api/upload',
  headers: {
    Authorization: 'Bearer ' + localStorage.getItem('token')
  }
}

// 证件号码占位符
const idNumberPlaceholder = computed(() => {
  const placeholders: Record<string, string> = {
    idCard: '请输入18位身份证号',
    hkMacaoPass: '请输入港澳通行证号',
    taiwanPass: '请输入台胞证号',
    passport: '请输入护照号',
    other: '请输入证件号码'
  }
  return placeholders[localData.idType] || '请输入证件号码'
})

// 表单验证规则
const rules: FormRules = {
  fullName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在2-50个字符', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  dateOfBirth: [
    { required: true, message: '请选择出生年月', trigger: 'change' }
  ],
  ethnicity: [
    { required: true, message: '请选择民族', trigger: 'change' }
  ],
  politicalStatus: [
    { required: true, message: '请选择政治面貌', trigger: 'change' }
  ],
  nativePlace: [
    { required: true, message: '请选择籍贯', trigger: 'change' }
  ],
  placeOfBirth: [
    { required: true, message: '请选择出生地', trigger: 'change' }
  ],
  idType: [
    { required: true, message: '请选择证件类型', trigger: 'change' }
  ],
  idNumber: [
    { required: true, message: '请输入证件号码', trigger: 'blur' },
    { validator: validateIdNumber, trigger: 'blur' }
  ],
  workStartDate: [
    { required: true, message: '请选择参加工作时间', trigger: 'change' }
  ],
  hireDate: [
    { required: true, message: '请选择入职日期', trigger: 'change' }
  ],
  phoneNumber: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  institutionId: [
    { required: true, message: '请选择所属机构', trigger: 'change' }
  ],
  positionId: [
    { required: true, message: '请选择当前岗位', trigger: 'change' }
  ],
  personnelType: [
    { required: true, message: '请选择人员类型', trigger: 'change' }
  ]
}

// 身份证号验证
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function validateIdNumber(rule: unknown, value: string, callback: unknown) {
  if (localData.idType === 'idCard') {
    // 简单的18位身份证号验证
    const idCardReg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/
    if (!idCardReg.test(value)) {
      callback(new Error('请输入正确的身份证号'))
    } else {
      callback()
    }
  } else {
    // 其他证件类型暂时只验证长度
    if (value.length < 6 || value.length > 30) {
      callback(new Error('证件号码长度应在6-30位之间'))
    } else {
      callback()
    }
  }
}

// 出生日期禁用
const disabledBirthDate = (date: Date) => {
  // 不能选择未来日期
  // 年龄不能小于16岁
  const now = new Date()
  const minDate = new Date(now.getFullYear() - 16, now.getMonth(), now.getDate())
  return date > minDate
}

// 工作日期禁用
const disabledWorkDate = (date: Date) => {
  // 不能选择未来日期
  // 不能早于出生日期16年
  const now = new Date()
  if (date > now) return true
  
  if (localData.dateOfBirth) {
    const birthDate = new Date(localData.dateOfBirth)
    const minWorkDate = new Date(birthDate.getFullYear() + 16, birthDate.getMonth(), birthDate.getDate())
    return date < minWorkDate
  }
  
  return false
}

// 入职日期禁用
const disabledHireDate = (date: Date) => {
  // 不能选择未来日期
  // 不能早于参加工作时间
  const now = new Date()
  if (date > now) return true
  
  if (localData.workStartDate) {
    const workStartDate = new Date(localData.workStartDate)
    return date < workStartDate
  }
  
  return false
}

// 监听数据变化，同步到父组件
watch(localData, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 监听父组件数据变化
watch(() => props.modelValue, (newVal) => {
  Object.assign(localData, newVal)
}, { deep: true })

// 初始化数据
const initData = async () => {
  try {
    // 加载组织机构树
    const {data: _data} =  await organizationApi.getTree()
    organizationTree.value 
    padding: 20px;
    
    .avatar-uploader {
      display: inline-block;
      cursor: pointer;
      
      .avatar-placeholder {
        width: 120px;
        height: 120px;
        border: 2px dashed var(--el-border-color);
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        
        &:hover {
          border-color: var(--el-color-primary);
          color: var(--el-color-primary);
        }
        
        .upload-text {
          margin-top: 8px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
    
    .photo-tips {
      margin-top: 10px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      line-height: 1.5;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .basic-info-form {
    :deep(.el-form-item) {
      margin-bottom: 12px;
    }
    
    :deep(.el-col) {
      margin-bottom: 0 !important;
    }
  }
}
</style>