<template>
  <div class="confirm-submit">
    <!-- 提示信息 -->
    <el-alert
      title="请仔细核对以下信息，确认无误后提交"
      type="info"
      show-icon
      :closable="false"
     />

    <!-- 信息预览 -->
    <div class="info-preview">
      <!-- 基本信息 -->
      <el-card class="preview-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button link type="primary" @click="handleEdit(0)">
              <el-icon><Edit /></el-icon>
              修改
            </el-button>
          </div>
        </template>
        
        <el-descriptions :column="3" border>
          <el-descriptions-item label="姓名">
            {{ formData.fullName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ formData.gender === 'male' ? '男' : formData.gender === 'female' ? '女' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="出生年月">
            {{ formData.dateOfBirth || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="民族">
            {{ formData.ethnicity || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="政治面貌">
            {{ formData.politicalStatus || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="籍贯">
            {{ formData.nativePlace || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="证件类型">
            {{ getIdTypeName(formData.idType) }}
          </el-descriptions-item>
          <el-descriptions-item label="证件号码" :span="2">
            {{ maskIdNumber(formData.idNumber) }}
          </el-descriptions-item>
          <el-descriptions-item label="参加工作时间">
            {{ formData.workStartDate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="入职日期">
            {{ formData.hireDate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="人员类型">
            {{ getPersonnelTypeName(formData.personnelType) }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ formData.phoneNumber || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱" :span="2">
            {{ formData.email || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="所属机构">
            {{ formData.institutionName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="当前岗位">
            {{ formData.positionName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="高层次人才">
            <el-tag v-if="formData.highLevelTalentCategory" type="danger">
              {{ formData.highLevelTalentCategory }}类
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 教育经历 -->
      <el-card class="preview-card">
        <template #header>
          <div class="card-header">
            <span>教育经历（{{ formData.educationHistory?.length || 0 }}条）</span>
            <el-button link type="primary" @click="handleEdit(1)">
              <el-icon><Edit /></el-icon>
              修改
            </el-button>
          </div>
        </template>
        
        <el-table :data="formData.educationHistory || []" style="width: 100%">
          <el-table-column prop="startDate" label="开始时间" width="100"  />
          <el-table-column prop="endDate" label="结束时间" width="100"  />
          <el-table-column prop="graduationSchool" label="毕业院校" min-width="150" show-overflow-tooltip  />
          <el-table-column prop="degree" label="学历" width="100"  />
          <el-table-column prop="major" label="专业" min-width="120" show-overflow-tooltip  />
          <el-table-column label="最高学历" width="90" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.isHighestDegree" type="success" size="small">是</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 工作经历 -->
      <el-card class="preview-card">
        <template #header>
          <div class="card-header">
            <span>工作经历（{{ formData.workExperience?.length || 0 }}条）</span>
            <el-button link type="primary" @click="handleEdit(2)">
              <el-icon><Edit /></el-icon>
              修改
            </el-button>
          </div>
        </template>
        
        <el-table :data="formData.workExperience || []" style="width: 100%">
          <el-table-column prop="startDate" label="开始时间" width="100"  />
          <el-table-column label="结束时间" width="100">
            <template #default="{ row }">
              {{ row.endDate || '至今' }}
            </template>
          </el-table-column>
          <el-table-column prop="companyName" label="工作单位" min-width="180" show-overflow-tooltip  />
          <el-table-column prop="positionHeld" label="职位" width="120" show-overflow-tooltip  />
          <el-table-column prop="salaryGrade" label="薪资等级" width="90" align="center"  />
        </el-table>
      </el-card>

      <!-- 家庭成员 -->
      <el-card class="preview-card">
        <template #header>
          <div class="card-header">
            <span>家庭成员（{{ formData.familyMembers?.length || 0 }}人）</span>
            <el-button link type="primary" @click="handleEdit(3)">
              <el-icon><Edit /></el-icon>
              修改
            </el-button>
          </div>
        </template>
        
        <el-table :data="formData.familyMembers || []" style="width: 100%">
          <el-table-column prop="fullName" label="姓名" width="100"  />
          <el-table-column prop="relationship" label="关系" width="80"  />
          <el-table-column prop="dateOfBirth" label="出生日期" width="110"  />
          <el-table-column prop="politicalStatus" label="政治面貌" width="100"  />
          <el-table-column prop="workUnitAndPosition" label="工作单位及职务" min-width="200" show-overflow-tooltip  />
        </el-table>
      </el-card>

      <!-- 其他信息 -->
      <el-card class="preview-card">
        <template #header>
          <div class="card-header">
            <span>其他信息</span>
            <el-button link type="primary" @click="handleEdit(4)">
              <el-icon><Edit /></el-icon>
              修改
            </el-button>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="紧急联系人">
            {{ formData.emergencyContact || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ formData.emergencyContactPhone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="现住址" :span="2">
            {{ formData.currentAddress || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="邮政编码">
            {{ formData.postalCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="入职材料">
            {{ formData.entryMaterialUrls?.length || 0 }}个文件
          </el-descriptions-item>
          <el-descriptions-item label="合同类型">
            {{ getContractTypeName(formData.contractType) }}
          </el-descriptions-item>
          <el-descriptions-item label="合同期限">
            {{ formData.contractPeriod ? `${formData.contractPeriod}年` : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ formData.remarks || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 确认提交 -->
    <div class="submit-confirm">
      <el-checkbox v-model="confirmed">
        我已仔细核对以上信息，确认无误
      </el-checkbox>
    </div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ConfirmSubmit'
})
 
import { ref, computed } from 'vue'
import { Edit } from '@element-plus/icons-vue'
import type { EmployeeDetail } from '@/types/employee'

interface Props {
  formData: Partial<EmployeeDetail>
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'edit': [step: number]
}>()

// 确认状态
const confirmed = ref(false)

// 获取证件类型名称
const getIdTypeName = (type?: string) => {
  const typeMap: Record<string, string> = {
    idCard: '身份证',
    hkMacaoPass: '港澳通行证',
    taiwanPass: '台胞证',
    passport: '护照',
    other: '其他'
  }
  return typeMap[type || ''] || '-'
}

// 获取人员类型名称
const getPersonnelTypeName = (type?: string) => {
  const typeMap: Record<string, string> = {
    regular: '在编人员',
    contract: '编外人员',
    partTime: '兼职教师'
  }
  return typeMap[type || ''] || '-'
}

// 获取合同类型名称
const getContractTypeName = (type?: string) => {
  const typeMap: Record<string, string> = {
    fixed: '固定期限劳动合同',
    permanent: '无固定期限劳动合同',
    project: '以完成一定工作任务为期限',
    service: '劳务合同',
    internship: '实习协议'
  }
  return typeMap[type || ''] || '-'
}

// 身份证号脱敏
const maskIdNumber = (idNumber?: string) => {
  if (!idNumber) return '-'
  if (idNumber.length < 8) return idNumber
  
  const start = idNumber.slice(0, 4)
  const end = idNumber.slice(-4)
  const middle = '*'.repeat(idNumber.length - 8)
  
  return `${start}${middle}${end}`
}

// 编辑
const handleEdit = (step: number) => {
  emit('edit', step)
}

// 验证
const validate = async () => {
  return confirmed.value
}

// 暴露方法
defineExpose({
  validate
})
</script>

<style lang="scss" scoped>
.confirm-submit {
  .info-preview {
    margin-top: 20px;
    
    .preview-card {
      margin-bottom: 20px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      :deep(.el-descriptions) {
        .el-descriptions__label {
          font-weight: normal;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  
  .submit-confirm {
    margin-top: 30px;
    padding: 20px;
    text-align: center;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
    
    :deep(.el-checkbox) {
      .el-checkbox__label {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .confirm-submit {
    .info-preview {
      .preview-card {
        :deep(.el-descriptions) {
          .el-descriptions-item__label {
            width: 80px !important;
          }
        }
      }
    }
  }
}
</style>