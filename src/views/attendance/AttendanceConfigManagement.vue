<template>
  <div class="attendance-config-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>考勤配置管理</h2>
      <p>管理考勤规则、设备和系统配置</p>
    </div>

    <!-- 配置导航 -->
    <el-card class="config-nav" shadow="never">
      <el-tabs v-model="activeConfigTab" @tab-change="handleTabChange">
        <el-tab-pane label="考勤规则" name="rules">
          <!-- 考勤规则配置 -->
          <div class="config-section">
            <div class="section-header">
              <h3>考勤规则配置</h3>
              <el-button type="primary" @click="handleAddRule">
                <el-icon><Plus /></el-icon>
                新增规则
              </el-button>
            </div>
            
            <el-table :data="attendanceRules" stripe style="width: 100%">
              <el-table-column prop="ruleName" label="规则名称" width="200"  />
              <el-table-column prop="ruleType" label="规则类型" width="120">
                <template #default="scope">
                  <el-tag :type="getRuleTypeTagType(scope.row.ruleType)">
                    {{ scope.row.ruleTypeName }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="workStartTime" label="上班时间" width="100"  />
              <el-table-column prop="workEndTime" label="下班时间" width="100"  />
              <el-table-column prop="lateThreshold" label="迟到阈值" width="100">
                <template #default="scope">
                  {{ scope.row.lateThreshold }}分钟
                </template>
              </el-table-column>
              <el-table-column prop="earlyLeaveThreshold" label="早退阈值" width="100">
                <template #default="scope">
                  {{ scope.row.earlyLeaveThreshold }}分钟
                </template>
              </el-table-column>
              <el-table-column prop="isActive" label="状态" width="80">
                <template #default="scope">
                  <el-switch
                    v-model="scope.row.isActive"
                    @change="handleRuleStatusChange(scope.row)"
                   />
                </template>
              </el-table-column>
              <el-table-column prop="effectiveDate" label="生效日期" width="120"  />
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button size="small" type="primary" link @click="handleEditRule(scope.row)">
                    编辑
                  </el-button>
                  <el-button size="small" type="primary" link @click="handleCopyRule(scope.row)">
                    复制
                  </el-button>
                  <el-button size="small" type="danger" link @click="handleDeleteRule(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="设备管理" name="devices">
          <!-- 考勤设备管理 -->
          <div class="config-section">
            <div class="section-header">
              <h3>考勤设备管理</h3>
              <el-button type="primary" @click="handleAddDevice">
                <el-icon><Plus /></el-icon>
                添加设备
              </el-button>
            </div>

            <el-row :gutter="20" class="device-cards">
              <el-col :span="8" v-for="device in attendanceDevices" :key="device.id">
                <el-card class="device-card" shadow="hover">
                  <div class="device-header">
                    <div class="device-icon" :class="getDeviceIconClass(device.deviceType)">
                      <el-icon><Monitor /></el-icon>
                    </div>
                    <div class="device-info">
                      <div class="device-name">{{ device.deviceName }}</div>
                      <div class="device-type">{{ device.deviceTypeName }}</div>
                    </div>
                    <div class="device-status">
                      <el-tag :type="getDeviceStatusTagType(device.status)">
                        {{ device.statusName }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="device-details">
                    <el-descriptions :column="1" size="small">
                      <el-descriptions-item label="设备IP">
                        {{ device.deviceIp }}
                      </el-descriptions-item>
                      <el-descriptions-item label="安装位置">
                        {{ device.location }}
                      </el-descriptions-item>
                      <el-descriptions-item label="最后同步">
                        {{ formatDateTime(device.lastSyncTime) }}
                      </el-descriptions-item>
                      <el-descriptions-item label="今日数据">
                        {{ device.todayRecords }}条
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>

                  <div class="device-actions">
                    <el-button size="small" @click="handleSyncDevice(device)">
                      <el-icon><Refresh /></el-icon>
                      同步数据
                    </el-button>
                    <el-button size="small" @click="handleTestDevice(device)">
                      <el-icon><Connection /></el-icon>
                      测试连接
                    </el-button>
                    <el-button size="small" type="primary" @click="handleEditDevice(device)">
                      编辑
                    </el-button>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <el-tab-pane label="校历配置" name="calendar">
          <!-- 校历配置 -->
          <div class="config-section">
            <div class="section-header">
              <h3>校历配置</h3>
              <el-button type="primary" @click="handleImportCalendar">
                <el-icon><Upload /></el-icon>
                导入校历
              </el-button>
            </div>

            <el-row :gutter="20">
              <el-col :span="16">
                <el-card class="calendar-config" shadow="never">
                  <template #header>
                    <span>{{ currentYear }}年校历</span>
                  </template>
                  <el-calendar v-model="calendarValue">
                    <template #date-cell="{ data }">
                      <div class="calendar-day" :class="getCalendarDayClass(data.date)">
                        <div class="day-number">{{ data.day.split('-').pop() }}</div>
                        <div v-if="getHolidayInfo(data.date)" class="holiday-info">
                          {{ getHolidayInfo(data.date)?.name || '' }}
                        </div>
                      </div>
                    </template>
                  </el-calendar>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="holiday-list" shadow="never">
                  <template #header>
                    <span>节假日列表</span>
                  </template>
                  <div class="holiday-items">
                    <div
                      v-for="holiday in holidays"
                      :key="holiday.id"
                      class="holiday-item"
                    >
                      <div class="holiday-name">{{ holiday.name }}</div>
                      <div class="holiday-date">{{ holiday.startDate }} - {{ holiday.endDate }}</div>
                      <div class="holiday-type">
                        <el-tag :type="getHolidayTypeTagType(holiday.type)" size="small">
                          {{ holiday.typeName }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <el-tab-pane label="系统配置" name="system">
          <!-- 系统配置 -->
          <div class="config-section">
            <div class="section-header">
              <h3>系统配置</h3>
              <el-button type="primary" @click="handleSaveSystemConfig">
                <el-icon><Check /></el-icon>
                保存配置
              </el-button>
            </div>

            <el-form ref="systemConfigFormRef" :model="systemConfig" label-width="150px">
              <el-card class="config-group" shadow="never">
                <template #header>
                  <span>数据同步配置</span>
                </template>
                <el-form-item label="自动同步间隔">
                  <el-select v-model="systemConfig.syncInterval" style="width: 200px">
                    <el-option label="5分钟" value="5"  />
                    <el-option label="10分钟" value="10"  />
                    <el-option label="15分钟" value="15"  />
                    <el-option label="30分钟" value="30"  />
                    <el-option label="1小时" value="60"  />
                  </el-select>
                </el-form-item>
                <el-form-item label="数据保留天数">
                  <el-input-number
                    v-model="systemConfig.dataRetentionDays"
                    :min="30"
                    :max="365"
                    style="width: 200px"
                    />
                </el-form-item>
                <el-form-item label="启用实时同步">
                  <el-switch v-model="systemConfig.enableRealTimeSync"  />
                </el-form-item>
              </el-card>

              <el-card class="config-group" shadow="never">
                <template #header>
                  <span>通知配置</span>
                </template>
                <el-form-item label="异常考勤通知">
                  <el-switch v-model="systemConfig.enableAbnormalNotification"  />
                </el-form-item>
                <el-form-item label="请假审批通知">
                  <el-switch v-model="systemConfig.enableLeaveApprovalNotification"  />
                </el-form-item>
                <el-form-item label="月度报表通知">
                  <el-switch v-model="systemConfig.enableMonthlyReportNotification"  />
                </el-form-item>
              </el-card>

              <el-card class="config-group" shadow="never">
                <template #header>
                  <span>安全配置</span>
                </template>
                <el-form-item label="数据访问日志">
                  <el-switch v-model="systemConfig.enableAccessLog"  />
                </el-form-item>
                <el-form-item label="操作审计">
                  <el-switch v-model="systemConfig.enableOperationAudit"  />
                </el-form-item>
                <el-form-item label="数据加密">
                  <el-switch v-model="systemConfig.enableDataEncryption"  />
                </el-form-item>
              </el-card>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 规则配置对话框 -->
    <AttendanceRuleDialog
      v-model:visible="ruleDialogVisible"
      :rule="currentRule"
      :mode="ruleDialogMode"
      @success="handleRuleDialogSuccess"
    />

    <!-- 设备配置对话框 -->
    <AttendanceDeviceDialog
      v-model:visible="deviceDialogVisible"
      :device="currentDevice"
      :mode="deviceDialogMode"
      @success="handleDeviceDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Monitor,
  Refresh,
  Connection,
  Upload,
  Check
} from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
// import AttendanceRuleDialog from './components/AttendanceRuleDialog.vue'
// import AttendanceDeviceDialog from './components/AttendanceDeviceDialog.vue'

// 响应式数据
const activeConfigTab = ref('rules')
const systemConfigFormRef = ref()
const calendarValue = ref(new Date())
const currentYear = computed(() => new Date().getFullYear())

// 对话框相关
const ruleDialogVisible = ref(false)
const deviceDialogVisible = ref(false)
const ruleDialogMode = ref<'add' | 'edit' | 'copy'>('add')
const deviceDialogMode = ref<'add' | 'edit'>('add')
const currentRule = ref<unknown>(null)
const currentDevice = ref<unknown>(null)

// 考勤规则数据
const attendanceRules = ref([
  {
    id: 1,
    ruleName: '标准工作制',
    ruleType: 'STANDARD',
    ruleTypeName: '标准工作制',
    workStartTime: '08:30',
    workEndTime: '17:30',
    lateThreshold: 10,
    earlyLeaveThreshold: 10,
    isActive: true,
    effectiveDate: '2025-01-01'
  },
  {
    id: 2,
    ruleName: '弹性工作制',
    ruleType: 'FLEXIBLE',
    ruleTypeName: '弹性工作制',
    workStartTime: '09:00',
    workEndTime: '18:00',
    lateThreshold: 30,
    earlyLeaveThreshold: 30,
    isActive: false,
    effectiveDate: '2025-01-01'
  }
])

// 考勤设备数据
const attendanceDevices = ref([
  {
    id: 1,
    deviceName: '主楼入口考勤机',
    deviceType: 'FINGERPRINT',
    deviceTypeName: '指纹识别',
    deviceIp: '*************',
    location: '主楼一楼大厅',
    status: 'ONLINE',
    statusName: '在线',
    lastSyncTime: '2025-06-19 14:30:00',
    todayRecords: 156
  },
  {
    id: 2,
    deviceName: '实验楼考勤机',
    deviceType: 'FACE',
    deviceTypeName: '人脸识别',
    deviceIp: '*************',
    location: '实验楼二楼',
    status: 'OFFLINE',
    statusName: '离线',
    lastSyncTime: '2025-06-19 12:00:00',
    todayRecords: 89
  }
])

// 节假日数据
const holidays = ref([
  {
    id: 1,
    name: 'HrHr元旦',
    startDate: '2025-01-01',
    endDate: '2025-01-01',
    type: 'NATIONAL',
    typeName: '国家法定节假日'
  },
  {
    id: 2,
    name: '春节',
    startDate: '2025-01-28',
    endDate: '2025-02-03',
    type: 'NATIONAL',
    typeName: '国家法定节假日'
  },
  {
    id: 3,
    name: '寒假',
    startDate: '2025-01-20',
    endDate: '2025-02-20',
    type: 'SCHOOL',
    typeName: '学校假期'
  }
])

// 系统配置
const systemConfig = reactive({
  syncInterval: '15',
  dataRetentionDays: 90,
  enableRealTimeSync: true,
  enableAbnormalNotification: true,
  enableLeaveApprovalNotification: true,
  enableMonthlyReportNotification: false,
  enableAccessLog: true,
  enableOperationAudit: true,
  enableDataEncryption: true
})

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeConfigTab.value = tabName
}

// 规则管理
const handleAddRule = () => {
  currentRule.value = null
  ruleDialogMode.value = 'add'
  ruleDialogVisible.value = true
}

   
const handleEditRule = (rule: unknown) => {
  currentRule.value = rule
  ruleDialogMode.value = 'edit'
  ruleDialogVisible.value = true
}

   
const handleCopyRule = (rule: unknown) => {
  currentRule.value = { ...rule, id: null, ruleName: rule.ruleName + '_副本' }
  ruleDialogMode.value = 'copy'
  ruleDialogVisible.value = true
}

   
const handleDeleteRule = async (rule: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则 "${rule.ruleName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
    // 这里应该调用删除API
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

   
const handleRuleStatusChange = (rule: unknown) => {
  ElMessage.success(`规则 "${rule.ruleName}" 状态已更新`)
  // 这里应该调用更新API
}

// 设备管理
const handleAddDevice = () => {
  currentDevice.value = null
  deviceDialogMode.value = 'add'
  deviceDialogVisible.value = true
}

   
const handleEditDevice = (device: unknown) => {
  currentDevice.value = device
  deviceDialogMode.value = 'edit'
  deviceDialogVisible.value = true
}

   
const handleSyncDevice = async (device: unknown) => {
  try {
    ElMessage.info(`正在同步设备 "${device.deviceName}" 的数据...`)
    // 模拟同步过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('数据同步成功')
  } catch (__error) {
    ElMessage.error('数据同步失败')
  }
}

   
const handleTestDevice = async (device: unknown) => {
  try {
    ElMessage.info(`正在测试设备 "${device.deviceName}" 的连接...`)
    // 模拟测试过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('设备连接正常')
  } catch (__error) {
    ElMessage.error('设备连接失败')
  }
}

// 校历管理
const handleImportCalendar = () => {
  ElMessage.info('导入校历功能开发中...')
}

const getCalendarDayClass = (date: string) => {
  const holiday = getHolidayInfo(date)
  if (holiday) {
    return holiday.type === 'NATIONAL' ? 'national-holiday' : 'school-holiday'
  }
  const dayOfWeek = new Date(date).getDay()
  return dayOfWeek === 0 || dayOfWeek === 6 ? 'weekend' : 'workday'
}

const getHolidayInfo = (date: string) => {
  return holidays.value.find(holiday => {
    const targetDate = new Date(date)
    const startDate = new Date(holiday.startDate)
    const endDate = new Date(holiday.endDate)
    return targetDate >= startDate && targetDate <= endDate
  })
}

// 系统配置
const handleSaveSystemConfig = () => {
  ElMessage.success('系统配置保存成功')
  // 这里应该调用保存API
}

// 对话框回调
const handleRuleDialogSuccess = () => {
  // 刷新规则列表
  ElMessage.success('操作成功')
}

const handleDeviceDialogSuccess = () => {
  // 刷新设备列表
  ElMessage.success('操作成功')
}

// 获取标签类型
const getRuleTypeTagType = (type: string) => {
  switch (type) {
    case 'STANDARD':
      return 'primary'
    case 'FLEXIBLE':
      return 'success'
    default:
      return ''
  }
}

const getDeviceStatusTagType = (status: string) => {
  switch (status) {
    case 'ONLINE':
      return 'success'
    case 'OFFLINE':
      return 'danger'
    case 'MAINTENANCE':
      return 'warning'
    default:
      return ''
  }
}

const getDeviceIconClass = (type: string) => {
  switch (type) {
    case 'FINGERPRINT':
      return 'device-fingerprint'
    case 'FACE':
      return 'device-face'
    case 'CARD':
      return 'device-card'
    default:
      return 'device-default'
  }
}

const getHolidayTypeTagType = (type: string) => {
  switch (type) {
    case 'NATIONAL':
      return 'danger'
    case 'SCHOOL':
      return 'warning'
    default:
      return ''
  }
}

// 初始化
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.attendance-config-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.config-nav {
  margin-bottom: 20px;
}

.config-section {
  padding: 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.device-cards {
  margin-top: 20px;
}

.device-card {
  margin-bottom: 20px;
}

.device-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.device-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.device-icon.device-fingerprint {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.device-icon.device-face {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.device-icon.device-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.device-icon.device-default {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.device-type {
  font-size: 12px;
  color: #909399;
}

.device-status {
  margin-left: 12px;
}

.device-details {
  margin-bottom: 16px;
}

.device-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.calendar-config {
  height: 600px;
}

.calendar-day {
  height: 100%;
  padding: 4px;
  position: relative;
}

.calendar-day.national-holiday {
  background: #fef0f0;
  color: #f56c6c;
}

.calendar-day.school-holiday {
  background: #fdf6ec;
  color: #e6a23c;
}

.calendar-day.weekend {
  background: #f5f7fa;
  color: #909399;
}

.day-number {
  font-size: 14px;
  font-weight: 600;
}

.holiday-info {
  font-size: 10px;
  margin-top: 2px;
  line-height: 1;
}

.holiday-list {
  height: 600px;
  overflow-y: auto;
}

.holiday-items {
  max-height: 500px;
  overflow-y: auto;
}

.holiday-item {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
}

.holiday-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.holiday-date {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.holiday-type {
  text-align: right;
}

.config-group {
  margin-bottom: 20px;
}

:deep(.el-calendar__body) {
  padding: 12px;
}

:deep(.el-calendar-day) {
  height: 60px;
  padding: 0;
}
</style>
