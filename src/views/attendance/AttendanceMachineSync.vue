<template>
  <div class="attendance-machine-sync">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>考勤机数据同步</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleManualSync">
          <el-icon><Refresh /></el-icon>
          手动同步
        </el-button>
        <el-button @click="showSyncConfig = true">
          <el-icon><Setting /></el-icon>
          同步配置
        </el-button>
      </div>
    </div>

    <!-- 设备列表 -->
    <el-card class="device-list-card">
      <template #header>
        <div class="card-header">
          <h3>考勤设备列表</h3>
          <el-button type="primary" size="small" @click="showAddDevice = true">
            <el-icon><Plus /></el-icon>
            添加设备
          </el-button>
        </div>
      </template>

      <el-table :data="deviceList" v-loading="loading">
        <el-table-column prop="deviceName" label="设备名称"  />
        <el-table-column prop="deviceType" label="设备类型">
          <template #default="{ row }">
            <el-tag>{{ deviceTypeMap[row.deviceType] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ipAddress" label="IP地址"  />
        <el-table-column prop="location" label="安装位置"  />
        <el-table-column prop="status" label="连接状态" width="100">
          <template #default="{ row }">
            <el-badge :is-dot="true" :type="row.status === 'online' ? 'success' : 'danger'">
              <span>{{ row.status === 'online' ? '在线' : '离线' }}</span>
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column prop="lastSyncTime" label="最后同步时间" width="160"  />
        <el-table-column label="同步记录" width="100">
          <template #default="{ row }">
            <el-text type="primary">
              {{ row.syncCount || 0 }} 条
            </el-text>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleSyncDevice(row)">同步</el-button>
            <el-button link type="primary" @click="handleTestConnection(row)">测试</el-button>
            <el-button link type="primary" @click="handleEditDevice(row)">编辑</el-button>
            <el-button link type="danger" @click="handleDeleteDevice(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 同步任务列表 -->
    <el-card class="sync-task-card">
      <template #header>
        <div class="card-header">
          <h3>同步任务记录</h3>
          <el-space>
            <el-select v-model="taskFilter.status" placeholder="任务状态" clearable size="small">
              <el-option label="全部" value=""  />
              <el-option label="同步中" value="running"  />
              <el-option label="成功" value="success"  />
              <el-option label="失败" value="failed"  />
            </el-select>
            <el-date-picker
              v-model="taskFilter.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
             />
          </el-space>
        </div>
      </template>

      <el-table :data="syncTaskList">
        <el-table-column prop="taskId" label="任务ID" width="180"  />
        <el-table-column prop="deviceName" label="设备名称"  />
        <el-table-column prop="syncType" label="同步类型" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ syncTypeMap[row.syncType] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="160"  />
        <el-table-column prop="endTime" label="结束时间" width="160"  />
        <el-table-column prop="duration" label="耗时" width="80">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalCount" label="总记录数" width="90"  />
        <el-table-column prop="successCount" label="成功" width="70">
          <template #default="{ row }">
            <el-text type="success">{{ row.successCount }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="failedCount" label="失败" width="70">
          <template #default="{ row }">
            <el-text type="danger">{{ row.failedCount }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getTaskStatusType(row.status)" size="small">
              {{ taskStatusMap[row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleViewTaskDetail(row)">详情</el-button>
            <el-button link type="primary" @click="handleRetryTask(row)" v-if="row.status === 'failed'">
              重试
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchSyncTasks"
        @current-change="fetchSyncTasks"
       />
    </el-card>

    <!-- 同步统计 -->
    <el-card class="sync-stats-card">
      <template #header>
        <h3>同步统计分析</h3>
      </template>

      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ syncStats.todayCount }}</div>
            <div class="stat-label">今日同步次数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ syncStats.todayRecords }}</div>
            <div class="stat-label">今日同步记录</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ syncStats.successRate }}%</div>
            <div class="stat-label">同步成功率</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ syncStats.avgDuration }}s</div>
            <div class="stat-label">平均同步耗时</div>
          </div>
        </el-col>
      </el-row>

      <!-- 同步趋势图 -->
      <div class="sync-trend-chart" ref="trendChartRef"></div>
    </el-card>

    <!-- 添加/编辑设备对话框 -->
    <el-dialog
      v-model="showAddDevice"
      :title="editingDevice ? '编辑考勤设备' : '添加考勤设备'"
      width="600px"
    >
      <el-form :model="deviceForm" :rules="deviceRules" ref="deviceFormRef" label-width="100px">
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="deviceForm.deviceName" placeholder="请输入设备名称"   />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="deviceForm.deviceType" placeholder="请选择设备类型">
            <el-option label="中控考勤机" value="zktech"  />
            <el-option label="汉王考勤机" value="hanvon"  />
            <el-option label="海康威视" value="hikvision"  />
            <el-option label="大华考勤机" value="dahua"  />
            <el-option label="通用考勤机" value="generic"  />
          </el-select>
        </el-form-item>
        <el-form-item label="IP地址" prop="ipAddress">
          <el-input v-model="deviceForm.ipAddress" placeholder="例如：*************"   />
        </el-form-item>
        <el-form-item label="端口号" prop="port">
          <el-input-number v-model="deviceForm.port" :min="1" :max="65535"   />
        </el-form-item>
        <el-form-item label="设备序列号" prop="serialNumber">
          <el-input v-model="deviceForm.serialNumber" placeholder="请输入设备序列号"   />
        </el-form-item>
        <el-form-item label="安装位置" prop="location">
          <el-input v-model="deviceForm.location" placeholder="例如：一楼大厅"   />
        </el-form-item>
        <el-form-item label="同步间隔" prop="syncInterval">
          <el-select v-model="deviceForm.syncInterval">
            <el-option label="实时同步" :value="0"  />
            <el-option label="每5分钟" :value="5"  />
            <el-option label="每10分钟" :value="10"  />
            <el-option label="每30分钟" :value="30"  />
            <el-option label="每小时" :value="60"  />
            <el-option label="每天" :value="1440"  />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDevice = false">取消</el-button>
        <el-button type="primary" @click="handleSaveDevice">确定</el-button>
      </template>
    </el-dialog>

    <!-- 同步配置对话框 -->
    <el-dialog v-model="showSyncConfig" title="同步配置" width="600px">
      <el-form :model="syncConfig" label-width="120px">
        <el-form-item label="自动同步">
          <el-switch v-model="syncConfig.autoSync"  />
        </el-form-item>
        <el-form-item label="同步时间范围">
          <el-time-picker
            v-model="syncConfig.syncTimeRange"
            is-range
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH:mm"
           />
        </el-form-item>
        <el-form-item label="失败重试次数">
          <el-input-number v-model="syncConfig.retryCount" :min="0" :max="5"   />
        </el-form-item>
        <el-form-item label="重试间隔">
          <el-input-number v-model="syncConfig.retryInterval" :min="1" :max="60"   />
          <span class="ml-2">秒</span>
        </el-form-item>
        <el-form-item label="数据去重">
          <el-switch v-model="syncConfig.deduplication"  />
        </el-form-item>
        <el-form-item label="冲突处理">
          <el-radio-group v-model="syncConfig.conflictStrategy">
            <el-radio label="skip">跳过</el-radio>
            <el-radio label="override">覆盖</el-radio>
            <el-radio label="merge">合并</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="通知设置">
          <el-checkbox-group v-model="syncConfig.notifications">
            <el-checkbox label="syncSuccess">同步成功</el-checkbox>
            <el-checkbox label="syncFailed">同步失败</el-checkbox>
            <el-checkbox label="deviceOffline">设备离线</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSyncConfig = false">取消</el-button>
        <el-button type="primary" @click="handleSaveSyncConfig">保存配置</el-button>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog v-model="showTaskDetail" title="同步任务详情" width="800px">
      <el-descriptions :column="2" border v-if="currentTask">
        <el-descriptions-item label="任务ID">{{ currentTask.taskId }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ currentTask.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="同步类型">{{ syncTypeMap[currentTask.syncType] }}</el-descriptions-item>
        <el-descriptions-item label="任务状态">
          <el-tag :type="getTaskStatusType(currentTask.status)">
            {{ taskStatusMap[currentTask.status] }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ currentTask.startTime }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ currentTask.endTime }}</el-descriptions-item>
        <el-descriptions-item label="总记录数">{{ currentTask.totalCount }}</el-descriptions-item>
        <el-descriptions-item label="成功/失败">
          <el-text type="success">{{ currentTask.successCount }}</el-text> / 
          <el-text type="danger">{{ currentTask.failedCount }}</el-text>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 错误日志 -->
      <div class="error-logs" v-if="currentTask?.errorLogs?.length">
        <h4>错误日志</h4>
        <el-table :data="currentTask.errorLogs" max-height="300">
          <el-table-column prop="recordId" label="记录ID" width="180"  />
          <el-table-column prop="employeeNo" label="员工工号" width="120"  />
          <el-table-column prop="errorType" label="错误类型" width="120"  />
          <el-table-column prop="errorMessage" label="错误信息"  />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { 
  Refresh, 
  Setting, 
  Plus 
} from '@element-plus/icons-vue'

// 数据定义
const loading = ref(false)
const showAddDevice = ref(false)
const showSyncConfig = ref(false)
const showTaskDetail = ref(false)
const editingDevice = ref(null)
const currentTask = ref(null)
const deviceFormRef = ref()
const trendChartRef = ref()

// 设备列表
const deviceList = ref([
  {
    id: 1,
    deviceName: '一楼大厅考勤机',
    deviceType: 'zktech',
    ipAddress: '*************',
    port: 4370,
    location: '一楼大厅',
    status: 'online',
    lastSyncTime: '2025-01-21 08:30:00',
    syncCount: 1520
  },
  {
    id: 2,
    deviceName: '二楼考勤机',
    deviceType: 'hanvon',
    ipAddress: '*************',
    port: 4370,
    location: '二楼电梯口',
    status: 'online',
    lastSyncTime: '2025-01-21 08:25:00',
    syncCount: 980
  },
  {
    id: 3,
    deviceName: '三楼考勤机',
    deviceType: 'hikvision',
    ipAddress: '*************',
    port: 8000,
    location: '三楼走廊',
    status: 'offline',
    lastSyncTime: '2025-01-21 06:00:00',
    syncCount: 650
  }
])

// 同步任务列表
const syncTaskList = ref([
  {
    taskId: 'SYNC20250121083000',
    deviceName: '一楼大厅考勤机',
    syncType: 'auto',
    startTime: '2025-01-21 08:30:00',
    endTime: '2025-01-21 08:30:45',
    duration: 45,
    totalCount: 120,
    successCount: 120,
    failedCount: 0,
    status: 'success'
  },
  {
    taskId: 'SYNC20250121082500',
    deviceName: '二楼考勤机',
    syncType: 'auto',
    startTime: '2025-01-21 08:25:00',
    endTime: '2025-01-21 08:25:30',
    duration: 30,
    totalCount: 85,
    successCount: 83,
    failedCount: 2,
    status: 'success'
  }
])

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 任务筛选
const taskFilter = reactive({
  status: '',
  dateRange: []
})

// 同步统计
const syncStats = reactive({
  todayCount: 48,
  todayRecords: 3560,
  successRate: 98.5,
  avgDuration: 35
})

// 设备表单
const deviceForm = reactive({
  deviceName: '',
  deviceType: '',
  ipAddress: '',
  port: 4370,
  serialNumber: '',
  location: '',
  syncInterval: 10
})

// 同步配置
const syncConfig = reactive({
  autoSync: true,
  syncTimeRange: ['00:00', '23:59'],
  retryCount: 3,
  retryInterval: 5,
  deduplication: true,
  conflictStrategy: 'merge',
  notifications: ['syncFailed', 'deviceOffline']
})

// 表单验证规则
const deviceRules = {
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  deviceType: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  ipAddress: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'IP地址格式不正确', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请输入安装位置', trigger: 'blur' }
  ]
}

// 枚举映射
const deviceTypeMap = {
  zktech: '中控考勤机',
  hanvon: '汉王考勤机',
  hikvision: '海康威视',
  dahua: '大华考勤机',
  generic: '通用考勤机'
}

const syncTypeMap = {
  auto: '自动同步',
  manual: '手动同步',
  schedule: '定时同步'
}

const taskStatusMap = {
  running: '同步中',
  success: '成功',
  failed: '失败',
  cancelled: '已取消'
}

// 方法定义
const handleManualSync = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要立即同步所有在线设备的考勤数据吗？',
      '提示',
      { type: 'warning' }
    )
    
    loading.value = true
    // 模拟同步过程
    setTimeout(() => {
      loading.value = false
      ElMessage.success('同步任务已启动，请查看任务列表')
      fetchSyncTasks()
    }, 2000)
  } catch {
    // 用户取消
  }
}

   
const handleSyncDevice = (device: unknown) => {
  ElMessage.info(`正在同步设备：${device.deviceName}`)
  // 实际同步逻辑
}

   
const handleTestConnection = (device: unknown) => {
  ElMessage.info(`正在测试设备连接：${device.deviceName}`)
  // 测试连接逻辑
}

   
const handleEditDevice = (device: unknown) => {
  editingDevice.value = device
  Object.assign(deviceForm, device)
  showAddDevice.value = true
}

   
const handleDeleteDevice = async (device: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备"${device.deviceName}"吗？`,
      '提示',
      { type: 'warning' }
    )
    
    ElMessage.success('删除成功')
    // 刷新列表
  } catch {
    // 用户取消
  }
}

const handleSaveDevice = async () => {
  const valid = await deviceFormRef.value.validate()
  if (!valid) return
  
  // 保存设备信息
  showAddDevice.value = false
  ElMessage.success(editingDevice.value ? '编辑成功' : '添加成功')
}

const handleSaveSyncConfig = () => {
  showSyncConfig.value = false
  ElMessage.success('同步配置已保存')
}

   
const handleViewTaskDetail = (task: unknown) => {
  currentTask.value = {
    ...task,
    errorLogs: task.failedCount > 0 ? [
      {
        recordId: 'REC001',
        employeeNo: 'EMP001',
        errorType: 'DUPLICATE',
        errorMessage: '该时间段已存在打卡记录'
      },
      {
        recordId: 'REC002',
        employeeNo: 'EMP002',
        errorType: 'INVALID_DATA',
        errorMessage: '员工工号不存在'
      }
    ] : []
  }
  showTaskDetail.value = true
}

   
const handleRetryTask = (task: unknown) => {
  ElMessage.info(`正在重试任务：${task.taskId}`)
}

const fetchSyncTasks = () => {
  // 获取同步任务列表
}

const getTaskStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    running: 'primary',
    success: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) return `${seconds}s`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}m${remainingSeconds}s`
}

// 初始化同步趋势图
const initTrendChart = () => {
  const chart = echarts.init(trendChartRef.value)
  
  const option = {
    title: {
      text: '最近7天同步趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['同步次数', '同步记录数', '失败次数'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['01-15', '01-16', '01-17', '01-18', '01-19', '01-20', '01-21']
    },
    yAxis: [
      {
        type: 'value',
        name: 'HrHr次数',
        position: 'left'
      },
      {
        type: 'value',
        name: '记录数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '同步次数',
        type: 'line',
        data: [45, 48, 46, 50, 52, 49, 48]
      },
      {
        name: '同步记录数',
        type: 'bar',
        yAxisIndex: 1,
        data: [3200, 3560, 3380, 3700, 3850, 3600, 3560]
      },
      {
        name: '失败次数',
        type: 'line',
        itemStyle: { color: '#F56C6C' },
        data: [2, 1, 3, 0, 2, 1, 0]
      }
    ]
  }
  
  chart.setOption(option)
}

// 生命周期
onMounted(() => {
  fetchSyncTasks()
  initTrendChart()
})
</script>

<style scoped>
.attendance-machine-sync {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
}

.device-list-card,
.sync-task-card,
.sync-stats-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.sync-trend-chart {
  height: 300px;
  margin-top: 20px;
}

.error-logs {
  margin-top: 20px;
}

.error-logs h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #666;
}

.ml-2 {
  margin-left: 8px;
}

:deep(.el-badge__content) {
  border: none;
}
</style>