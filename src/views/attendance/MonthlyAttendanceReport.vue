<template>
  <div class="monthly-attendance-report">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>月度考勤报表</h2>
      <div class="header-actions">
        <el-button @click="handlePrint">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button @click="exportPDF">
          <el-icon><Document /></el-icon>
          导出PDF
        </el-button>
        <el-button type="primary" @click="exportExcel">
          <el-icon><Download /></el-icon>
          导出Excel
        </el-button>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" size="default">
        <el-form-item label="报表月份">
          <el-date-picker
            v-model="searchForm.month"
            type="month"
            placeholder="选择月份"
            format="YYYY年MM月"
            value-format="YYYY-MM"
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择部门"
            clearable
            filterable
            check-strictly
           />
        </el-form-item>
        <el-form-item label="报表类型">
          <el-select v-model="searchForm.reportType" placeholder="请选择">
            <el-option value="summary" label="汇总报表"  />
            <el-option value="detail" label="明细报表"  />
            <el-option value="department" label="部门报表"  />
            <el-option value="personal" label="个人报表"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="generateReport">生成报表</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 报表信息 -->
    <el-card class="report-info" v-if="reportData">
      <div class="info-header">
        <h3>{{ reportData.title }}</h3>
        <div class="info-meta">
          <span>生成时间：{{ reportData.generateTime }}</span>
          <span>生成人：{{ reportData.generator }}</span>
        </div>
      </div>
      <el-descriptions :column="4" border size="small">
        <el-descriptions-item label="报表月份">{{ reportData.month }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ reportData.department || '全部' }}</el-descriptions-item>
        <el-descriptions-item label="统计人数">{{ reportData.totalPeople }}</el-descriptions-item>
        <el-descriptions-item label="工作日天数">{{ reportData.workDays }}</el-descriptions-item>
        <el-descriptions-item label="应出勤工时">{{ reportData.shouldWorkHours }}h</el-descriptions-item>
        <el-descriptions-item label="实际出勤工时">{{ reportData.actualWorkHours }}h</el-descriptions-item>
        <el-descriptions-item label="平均出勤率">{{ reportData.avgAttendanceRate }}%</el-descriptions-item>
        <el-descriptions-item label="加班总时长">{{ reportData.totalOvertimeHours }}h</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 报表内容 -->
    <div class="report-content" v-if="reportData">
      <!-- 汇总报表 -->
      <el-card v-if="searchForm.reportType === 'summary'" class="report-section">
        <template #header>
          <span>考勤汇总统计</span>
        </template>
        
        <!-- 汇总数据表格 -->
        <el-table
          :data="summaryData"
          border
          stripe
          show-summary
          :summary-method="getSummaryMethod"
          style="width: 100%"
        >
          <el-table-column prop="department" label="部门" width="150" fixed  />
          <el-table-column prop="totalPeople" label="人数" width="80" align="center"  />
          <el-table-column label="出勤统计" align="center">
            <el-table-column prop="shouldDays" label="应出勤" width="80" align="center">
              <template #default="{ row }">
                {{ row.shouldDays }}天
              </template>
            </el-table-column>
            <el-table-column prop="actualDays" label="实出勤" width="80" align="center">
              <template #default="{ row }">
                {{ row.actualDays }}天
              </template>
            </el-table-column>
            <el-table-column prop="attendanceRate" label="出勤率" width="90" align="center">
              <template #default="{ row }">
                <span :class="{ 'text-danger': row.attendanceRate < 90 }">
                  {{ row.attendanceRate }}%
                </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="异常统计" align="center">
            <el-table-column prop="lateTimes" label="迟到" width="70" align="center"  />
            <el-table-column prop="earlyTimes" label="早退" width="70" align="center"  />
            <el-table-column prop="absentDays" label="旷工" width="70" align="center"  />
            <el-table-column prop="leaveDays" label="请假" width="70" align="center"  />
          </el-table-column>
          <el-table-column label="加班统计" align="center">
            <el-table-column prop="overtimeHours" label="时长(h)" width="80" align="center"  />
            <el-table-column prop="overtimeDays" label="天数" width="70" align="center"  />
          </el-table-column>
        </el-table>

        <!-- 图表展示 -->
        <el-row :gutter="20" class="chart-row">
          <el-col :span="12">
            <div ref="attendanceChart" class="chart-container"></div>
          </el-col>
          <el-col :span="12">
            <div ref="abnormalChart" class="chart-container"></div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 明细报表 -->
      <el-card v-if="searchForm.reportType === 'detail'" class="report-section">
        <template #header>
          <div class="section-header">
            <span>考勤明细记录</span>
            <el-input
              v-model="detailSearch"
              placeholder="搜索员工姓名或工号"
              style="width: 200px"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </template>
        
        <el-table
          :data="filteredDetailData"
          border
          stripe
          max-height="600"
          v-loading="loading"
        >
          <el-table-column type="index" label="序号" width="60" fixed  />
          <el-table-column prop="employeeName" label="姓名" width="100" fixed  />
          <el-table-column prop="employeeNo" label="工号" width="100"  />
          <el-table-column prop="department" label="部门" width="120"  />
          <el-table-column prop="position" label="职位" width="120"  />
          
          <!-- 动态日期列 -->
          <el-table-column
            v-for="day in monthDays"
            :key="day"
            :label="day"
            width="50"
            align="center"
          >
            <template #header>
              <div class="date-header">
                <div>{{ day }}</div>
                <div class="weekday">{{ getWeekday(day) }}</div>
              </div>
            </template>
            <template #default="{ row }">
              <el-tooltip
                :content="getAttendanceTooltip(row.attendance[day])"
                placement="top"
                v-if="row.attendance[day]"
              >
                <span :class="getAttendanceClass(row.attendance[day])">
                  {{ getAttendanceSymbol(row.attendance[day]) }}
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
          
          <el-table-column label="统计" align="center" fixed="right">
            <el-table-column prop="totalDays" label="出勤" width="60" align="center"  />
            <el-table-column prop="lateDays" label="迟到" width="60" align="center"  />
            <el-table-column prop="leaveDays" label="请假" width="60" align="center"  />
            <el-table-column prop="overtimeHours" label="加班(h)" width="80" align="center"  />
          </el-table-column>
        </el-table>

        <!-- 图例说明 -->
        <div class="legend-box">
          <span class="legend-item">
            <span class="symbol normal">√</span>正常
          </span>
          <span class="legend-item">
            <span class="symbol late">迟</span>迟到
          </span>
          <span class="legend-item">
            <span class="symbol early">早</span>早退
          </span>
          <span class="legend-item">
            <span class="symbol absent">旷</span>旷工
          </span>
          <span class="legend-item">
            <span class="symbol leave">假</span>请假
          </span>
          <span class="legend-item">
            <span class="symbol overtime">加</span>加班
          </span>
          <span class="legend-item">
            <span class="symbol holiday">休</span>休息
          </span>
        </div>
      </el-card>

      <!-- 部门报表 -->
      <el-card v-if="searchForm.reportType === 'department'" class="report-section">
        <template #header>
          <span>部门考勤对比</span>
        </template>
        
        <!-- 部门对比图表 -->
        <div ref="departmentCompareChart" class="large-chart"></div>
        
        <!-- 部门排名表格 -->
        <el-table
          :data="departmentRankData"
          border
          stripe
          style="margin-top: 20px"
        >
          <el-table-column type="index" label="排名" width="60" align="center"  />
          <el-table-column prop="department" label="部门"  />
          <el-table-column prop="attendanceRate" label="出勤率" width="100" align="center">
            <template #default="{ row }">
              <el-progress
                :percentage="row.attendanceRate"
                :color="getProgressColor(row.attendanceRate)"
                :stroke-width="6"
               />
            </template>
          </el-table-column>
          <el-table-column prop="avgWorkHours" label="人均工时" width="100" align="center"  />
          <el-table-column prop="overtimeRate" label="加班率" width="100" align="center">
            <template #default="{ row }">
              {{ row.overtimeRate }}%
            </template>
          </el-table-column>
          <el-table-column prop="abnormalRate" label="异常率" width="100" align="center">
            <template #default="{ row }">
              <span :class="{ 'text-danger': row.abnormalRate > 10 }">
                {{ row.abnormalRate }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="综合得分" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getScoreType(row.score)">{{ row.score }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 个人报表 -->
      <el-card v-if="searchForm.reportType === 'personal'" class="report-section">
        <template #header>
          <div class="section-header">
            <span>个人考勤报表</span>
            <el-select
              v-model="selectedEmployee"
              placeholder="选择员工"
              filterable
              style="width: 200px"
            >
              <el-option
                v-for="emp in employeeList"
                :key="emp.id"
                :label="`${emp.name} (${emp.no})`"
                :value="emp.id"
               />
            </el-select>
          </div>
        </template>
        
        <div v-if="personalData" class="personal-report">
          <!-- 个人信息卡片 -->
          <div class="personal-info">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="姓名">{{ personalData.name }}</el-descriptions-item>
              <el-descriptions-item label="工号">{{ personalData.no }}</el-descriptions-item>
              <el-descriptions-item label="部门">{{ personalData.department }}</el-descriptions-item>
              <el-descriptions-item label="职位">{{ personalData.position }}</el-descriptions-item>
              <el-descriptions-item label="入职日期">{{ personalData.joinDate }}</el-descriptions-item>
              <el-descriptions-item label="考勤组">{{ personalData.attendanceGroup }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 考勤日历 -->
          <div class="attendance-calendar">
            <el-calendar v-model="calendarDate">
              <template #dateCell="{ data }">
                <div class="calendar-day" :class="getCalendarClass(data)">
                  <div class="day-number">{{ data.day.split('-').pop() }}</div>
                  <div class="day-status" v-if="personalData.calendar[data.day]">
                    {{ getAttendanceSymbol(personalData.calendar[data.day]) }}
                  </div>
                </div>
              </template>
            </el-calendar>
          </div>

          <!-- 个人统计 -->
          <el-row :gutter="20" class="personal-stats">
            <el-col :span="6">
              <el-statistic title="出勤天数" :value="personalData.stats.attendanceDays" suffix="天"  />
            </el-col>
            <el-col :span="6">
              <el-statistic title="出勤率" :value="personalData.stats.attendanceRate" suffix="%"  />
            </el-col>
            <el-col :span="6">
              <el-statistic title="加班时长" :value="personalData.stats.overtimeHours" suffix="小时"  />
            </el-col>
            <el-col :span="6">
              <el-statistic title="请假天数" :value="personalData.stats.leaveDays" suffix="天"  />
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 打印预览对话框 -->
    <el-dialog
      v-model="printPreviewVisible"
      title="打印预览"
      width="80%"
      fullscreen
    >
      <div class="print-preview" ref="printContent">
        <!-- 打印内容 -->
      </div>
      <template #footer>
        <el-button @click="printPreviewVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPrint">确认打印</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import {
  Printer,
  Document,
  Download,
  Search
} from '@element-plus/icons-vue'
import echarts, { createChart } from '@/utils/echarts'
import dayjs from 'dayjs'
import { attendanceApi } from '@/api/attendance'
// 搜索表单
const searchForm = reactive({
  month: dayjs().subtract(1, 'month').format('YYYY-MM'),
  departmentId: '',
  reportType: 'summary'
})

// 响应式数据
const loading = ref(false)
const departmentTree = ref([])
const reportData = ref<unknown>(null)
const summaryData = ref([])
const detailData = ref([])
const departmentRankData = ref([])
const personalData = ref<unknown>(null)
const employeeList = ref([])
const selectedEmployee = ref('')
const detailSearch = ref('')
const printPreviewVisible = ref(false)
const calendarDate = ref(new Date())

// 图表实例
const attendanceChart = ref()
const abnormalChart = ref()
const departmentCompareChart = ref()

// 计算属性
const monthDays = computed(() => {
  if (!searchForm.month) return []
  const year = parseInt(searchForm.month.split('-')[0])
  const month = parseInt(searchForm.month.split('-')[1])
  const daysInMonth = new Date(year, month, 0).getDate()
  return Array.from({ length: daysInMonth }, (_, i) => i + 1)
})

const filteredDetailData = computed(() => {
  if (!detailSearch.value) return detailData.value
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return detailData.value.filter((item: unknown) => 
    item.employeeName.includes(detailSearch.value) ||
    item.employeeNo.includes(detailSearch.value)
  )
})

// 获取星期
function getWeekday(day: number) {
  const date = new Date(`${searchForm.month}-${day}`)
  const weekdays = ['日', '一', '二', '三', '四', '五', '六']
  return weekdays[date.getDay()]
}

// 获取考勤符号
function getAttendanceSymbol(status: string) {
  const symbols: Record<string, string> = {
    normal: '√',
    late: '迟',
    early: '早',
    absent: '旷',
    leave: '假',
    overtime: '加',
    holiday: '休',
    missing: '缺'
  }
  return symbols[status] || ''
}

// 获取考勤样式
function getAttendanceClass(status: string) {
  return `attendance-${status}`
}

// 获取考勤提示
function getAttendanceTooltip(status: string) {
  const tips: Record<string, string> = {
    normal: '正常出勤',
    late: '迟到',
    early: '早退',
    absent: '旷工',
    leave: '请假',
    overtime: '加班',
    holiday: '休息日',
    missing: '缺卡'
  }
  return tips[status] || ''
}

// 获取进度条颜色
function getProgressColor(percentage: number) {
  if (percentage >= 95) return '#67c23a'
  if (percentage >= 90) return '#e6a23c'
  return '#f56c6c'
}

// 获取分数类型
function getScoreType(score: number) {
  if (score >= 90) return 'success'
  if (score >= 80) return 'warning'
  return 'danger'
}

// 获取日历样式
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function getCalendarClass(data: unknown) {
  const status = personalData.value?.calendar[data.day]
  if (!status) return ''
  return `calendar-${status}`
}

// 生成报表
async function generateReport() {
  if (!searchForm.month) {
    ElMessage.warning('请选择报表月份')
    return
  }

  loading.value = true
  const loadingInstance = ElLoading.service({
    text: '正在生成报表...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const params = {
      ...searchForm,
      year: parseInt(searchForm.month.split('-')[0]),
      month: parseInt(searchForm.month.split('-')[1])
    }

    // 根据报表类型调用不同接口
    switch (searchForm.reportType) {
      case 'summary':
        await generateSummaryReport(params)
        break
      case 'detail':
        await generateDetailReport(params)
        break
      case 'department':
        await generateDepartmentReport(params)
        break
      case 'personal':
        await loadEmployeeList()
        break
    }

    // 设置报表基本信息
    reportData.value = {
      title: `${params.year}年${params.month}月考勤报表`,
      month: searchForm.month,
      department: getDepartmentName(searchForm.departmentId),
      generateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      generator: '当前用户',
      ...reportData.value
    }

  } catch (__error) {
    ElMessage.error('生成报表失败')
  } finally {
    loading.value = false
    loadingInstance.close()
  }
}

// 生成汇总报表
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
async function generateSummaryReport(params: unknown) {
  const data = await attendanceApi.getMonthlyReport(params)
  
  summaryData.value = data.summary
  reportData.value = data.overview

  // 初始化图表
  setTimeout(() => {
    initSummaryCharts(data.charts)
  }, 100)
}

// 生成明细报表
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
async function generateDetailReport(params: unknown) {
  const data = await attendanceApi.getMonthlyDetail(params)
  
  detailData.value = data.list
  reportData.value = data.overview
}

// 生成部门报表
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
async function generateDepartmentReport(params: unknown) {
  const data = await attendanceApi.getDepartmentReport(params)
  
  departmentRankData.value = data.ranking
  reportData.value = data.overview

  // 初始化部门对比图表
  setTimeout(() => {
    initDepartmentChart(data.comparison)
  }, 100)
}

// 加载员工列表
async function loadEmployeeList() {
  const list = await attendanceApi.getEmployeeList(searchForm.departmentId)
  employeeList.value = list
}

// 加载个人数据
async function loadPersonalData() {
  if (!selectedEmployee.value) return

  const data = await attendanceApi.getPersonalReport({
    employeeId: selectedEmployee.value,
    month: searchForm.month
  })

  personalData.value = data
  calendarDate.value = new Date(searchForm.month)
}

// 初始化汇总图表
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function initSummaryCharts(data: unknown) {
  // 出勤率图表
  if (attendanceChart.value) {
    const chart = createChart(attendanceChart.value)
    chart.setOption({
      title: { text: '部门出勤率对比', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: data.attendance.departments,
        axisLabel: { rotate: 45 }
      },
      yAxis: {
        type: 'value',
        name: 'HrHr出勤率(%)'
      },
      series: [{
        type: 'bar',
        data: data.attendance.rates,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }]
    })
  }

  // 异常分布图表
  if (abnormalChart.value) {
    const chart = createChart(abnormalChart.value)
    chart.setOption({
      title: { text: '异常类型分布', left: 'center' },
      tooltip: { trigger: 'item' },
      legend: { bottom: 0 },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: data.abnormal
      }]
    })
  }
}

// 初始化部门对比图表
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function initDepartmentChart(data: unknown) {
  if (!departmentCompareChart.value) return

  const chart = createChart(departmentCompareChart.value)
  chart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['出勤率', '加班率', '异常率', '综合得分']
    },
    radar: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
      indicator: data.indicators.map((item: unknown) => ({
        name: item.name,
        max: 100
      }))
    },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    series: data.departments.map((dept: unknown) => ({
      name: dept.name,
      type: 'radar',
      data: [{
        value: dept.values,
        name: dept.name
      }]
    }))
  })
}

// 获取部门名称
function getDepartmentName(id: string) {
  if (!id) return '全部'
  // 递归查找部门名称
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const findDept = (list: unknown[], targetId: string): string => {
    for (const item of list) {
      if (item.id === targetId) return item.name
      if (item.children) {
        const found = findDept(item.children, targetId)
        if (found) return found
      }
    }
    return ''
  }
  return findDept(departmentTree.value, id)
}

// 汇总方法
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function getSummaryMethod(param: unknown) {
  const {columns: _columns, data: _data} =  param
  const sums: string[] 
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .report-info {
    margin-bottom: 20px;
    
    .info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        font-size: 18px;
      }
      
      .info-meta {
        color: #909399;
        font-size: 14px;
        
        span {
          margin-left: 20px;
        }
      }
    }
  }
  
  .report-section {
    margin-bottom: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .chart-row {
      margin-top: 20px;
    }
    
    .chart-container {
      height: 300px;
    }
    
    .large-chart {
      height: 400px;
    }
  }
  
  // 考勤状态样式
  .attendance-normal { color: #67c23a; }
  .attendance-late { color: #e6a23c; }
  .attendance-early { color: #e6a23c; }
  .attendance-absent { color: #f56c6c; }
  .attendance-leave { color: #409eff; }
  .attendance-overtime { color: #722ed1; }
  .attendance-holiday { color: #909399; }
  .attendance-missing { color: #f56c6c; }
  
  .date-header {
    text-align: center;
    line-height: 1.2;
    
    .weekday {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .legend-box {
    margin-top: 16px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 4px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .symbol {
        display: inline-block;
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        
        &.normal { background: #f0f9ff; color: #67c23a; }
        &.late { background: #fef0f0; color: #e6a23c; }
        &.early { background: #fef0f0; color: #e6a23c; }
        &.absent { background: #fef0f0; color: #f56c6c; }
        &.leave { background: #f0f9ff; color: #409eff; }
        &.overtime { background: #f9f0ff; color: #722ed1; }
        &.holiday { background: #f4f4f5; color: #909399; }
      }
    }
  }
  
  .personal-report {
    .personal-info {
      margin-bottom: 20px;
    }
    
    .attendance-calendar {
      margin-bottom: 20px;
      
      .calendar-day {
        height: 100%;
        padding: 8px;
        
        .day-number {
          font-size: 14px;
          margin-bottom: 4px;
        }
        
        .day-status {
          font-size: 16px;
          font-weight: bold;
        }
        
        &.calendar-normal { background: #f0f9ff; }
        &.calendar-late { background: #fef0f0; }
        &.calendar-absent { background: #fef0f0; }
        &.calendar-leave { background: #ecf5ff; }
        &.calendar-overtime { background: #f9f0ff; }
        &.calendar-holiday { background: #f4f4f5; }
      }
    }
    
    .personal-stats {
      padding: 20px;
      background: #f5f7fa;
      border-radius: 4px;
    }
  }
  
  .text-danger {
    color: #f56c6c;
  }
}

@media print {
  .page-header,
  .search-card,
  .el-dialog__header,
  .el-dialog__footer {
    display: none !important;
  }
  
  .report-content {
    margin: 0;
    padding: 0;
  }
}
</style>