<template>
  <div class="abnormal-attendance-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>异常考勤分析</h2>
      <div class="header-actions">
        <el-button @click="exportData">导出报告</el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" size="default">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择部门"
            clearable
            filterable
           />
        </el-form-item>
        <el-form-item label="异常类型">
          <el-select
            v-model="searchForm.abnormalType"
            placeholder="全部类型"
            clearable
            multiple
          >
            <el-option value="late" label="迟到"  />
            <el-option value="early" label="早退"  />
            <el-option value="absent" label="旷工"  />
            <el-option value="missing" label="缺卡"  />
            <el-option value="outOfRange" label="打卡异常"  />
          </el-select>
        </el-form-item>
        <el-form-item label="严重程度">
          <el-select
            v-model="searchForm.severity"
            placeholder="全部程度"
            clearable
          >
            <el-option value="low" label="轻微"  />
            <el-option value="medium" label="中等"  />
            <el-option value="high" label="严重"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 异常概览 -->
    <div class="abnormal-overview">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in overviewData" :key="item.key">
          <div class="overview-card" :class="`card-${item.severity}`">
            <div class="card-header">
              <span class="card-title">{{ item.title }}</span>
              <el-icon :size="20" :class="`icon-${item.severity}`">
                <component :is="item.icon" />
              </el-icon>
            </div>
            <div class="card-value">{{ item.value }}</div>
            <div class="card-footer">
              <span class="card-label">{{ item.label }}</span>
              <span class="card-percent" :class="{ 'up': item.trend > 0, 'down': item.trend < 0 }">
                {{ item.trend > 0 ? '+' : '' }}{{ item.trend }}%
              </span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 趋势分析图表 -->
    <el-row :gutter="20" class="charts-container">
      <!-- 异常趋势图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>异常趋势分析</span>
              <el-radio-group v-model="trendPeriod" size="small">
                <el-radio-button value="week">周</el-radio-button>
                <el-radio-button value="month">月</el-radio-button>
                <el-radio-button value="quarter">季</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 部门分布图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>部门异常分布</span>
          </template>
          <div ref="departmentChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-container">
      <!-- 时段分布热力图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>异常时段分布</span>
          </template>
          <div ref="timeHeatmap" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 人员TOP榜 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>异常人员TOP10</span>
              <el-select v-model="topType" size="small" style="width: 100px;">
                <el-option value="count" label="次数"  />
                <el-option value="duration" label="时长"  />
              </el-select>
            </div>
          </template>
          <div ref="topChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 异常明细表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>异常明细</span>
          <div class="header-tools">
            <el-checkbox v-model="showProcessed">显示已处理</el-checkbox>
            <el-button type="text" @click="batchProcess">批量处理</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        ref="tableRef"
        :data="tableData"
        stripe
        border
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="date" label="日期" width="100" sortable  />
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="abnormalType" label="异常类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getAbnormalTypeTag(row.abnormalType)" size="small">
              {{ getAbnormalTypeText(row.abnormalType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="异常描述" min-width="200"  />
        <el-table-column prop="severity" label="严重程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getSeverityTag(row.severity)">
              {{ getSeverityText(row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="异常时长" width="100">
          <template #default="{ row }">
            <span v-if="row.duration">{{ row.duration }}分钟</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="处理状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'processed' ? 'success' : 'info'">
              {{ row.status === 'processed' ? '已处理' : '待处理' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewDetail(row)">详情</el-button>
            <el-button type="text" @click="processRecord(row)" v-if="row.status !== 'processed'">
              处理
            </el-button>
            <el-button type="text" @click="notifyEmployee(row)">通知</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 处理对话框 -->
    <el-dialog
      v-model="processDialogVisible"
      title="异常处理"
      width="600px"
    >
      <el-form :model="processForm" label-width="100px">
        <el-form-item label="处理类型">
          <el-select v-model="processForm.type" placeholder="请选择">
            <el-option value="approved" label="已批准"  />
            <el-option value="deduct" label="扣款处理"  />
            <el-option value="warning" label="警告处理"  />
            <el-option value="ignore" label="忽略"  />
          </el-select>
        </el-form-item>
        <el-form-item label="处理说明">
          <el-input
            v-model="processForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入处理说明"
            />
        </el-form-item>
        <el-form-item label="扣款金额" v-if="processForm.type === 'deduct'">
          <el-input-number
            v-model="processForm.deductAmount"
            :min="0"
            :precision="2"
            placeholder="请输入扣款金额"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmProcess">确认处理</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="异常详情"
      width="700px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ currentRecord.employeeName }}</el-descriptions-item>
        <el-descriptions-item label="工号">{{ currentRecord.employeeNo }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ currentRecord.department }}</el-descriptions-item>
        <el-descriptions-item label="职位">{{ currentRecord.position }}</el-descriptions-item>
        <el-descriptions-item label="异常日期">{{ currentRecord.date }}</el-descriptions-item>
        <el-descriptions-item label="异常类型">
          <el-tag :type="getAbnormalTypeTag(currentRecord.abnormalType)" size="small">
            {{ getAbnormalTypeText(currentRecord.abnormalType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="异常描述" :span="2">{{ currentRecord.description }}</el-descriptions-item>
        <el-descriptions-item label="应打卡时间">{{ currentRecord.shouldTime }}</el-descriptions-item>
        <el-descriptions-item label="实际打卡时间">{{ currentRecord.actualTime || '未打卡' }}</el-descriptions-item>
        <el-descriptions-item label="异常时长">{{ currentRecord.duration }}分钟</el-descriptions-item>
        <el-descriptions-item label="严重程度">
          <el-tag :type="getSeverityTag(currentRecord.severity)">
            {{ getSeverityText(currentRecord.severity) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="打卡地点" :span="2">{{ currentRecord.location || '-' }}</el-descriptions-item>
        <el-descriptions-item label="历史记录" :span="2">
          最近30天内异常{{ currentRecord.recentCount }}次
        </el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag :type="currentRecord.status === 'processed' ? 'success' : 'info'">
            {{ currentRecord.status === 'processed' ? '已处理' : '待处理' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="处理人" v-if="currentRecord.status === 'processed'">
          {{ currentRecord.processor }}
        </el-descriptions-item>
        <el-descriptions-item label="处理时间" v-if="currentRecord.status === 'processed'">
          {{ currentRecord.processTime }}
        </el-descriptions-item>
        <el-descriptions-item label="处理说明" v-if="currentRecord.status === 'processed'">
          {{ currentRecord.processRemark }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh,
  Warning,
  Clock,
  CircleClose,
  SemiSelect
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { attendanceApi } from '@/api/attendance'
import { exportToExcel } from '@/utils/export'

// 搜索表单
const searchForm = reactive({
  dateRange: [
    dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ],
  departmentId: '',
  abnormalType: [],
  severity: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 响应式数据
const loading = ref(false)
const departmentTree = ref([])
const tableData = ref([])
const selectedRows = ref([])
const showProcessed = ref(false)
const trendPeriod = ref('week')
const topType = ref('count')
const processDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentRecord = ref<unknown>({})
const tableRef = ref()

// 处理表单
const processForm = reactive({
  type: '',
  remark: '',
  deductAmount: 0
})

// 图表实例
const trendChart = ref()
const departmentChart = ref()
const timeHeatmap = ref()
const topChart = ref()

// 概览数据
const overviewData = ref([
  {
    key: 'total',
    title: '异常总数',
    value: 0,
    label: '本期总计',
    trend: 0,
    severity: 'danger',
    icon: Warning
  },
  {
    key: 'late',
    title: '迟到次数',
    value: 0,
    label: '占比最高',
    trend: 0,
    severity: 'warning',
    icon: Clock
  },
  {
    key: 'absent',
    title: '旷工人次',
    value: 0,
    label: '需重点关注',
    trend: 0,
    severity: 'danger',
    icon: CircleClose
  },
  {
    key: 'unprocessed',
    title: '待处理',
    value: 0,
    label: '需要处理',
    trend: 0,
    severity: 'info',
    icon: SemiSelect
  }
])

// 初始化图表
function initCharts() {
  // 异常趋势图
  const trend = echarts.init(trendChart.value)
  trend.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['迟到', '早退', '旷工', '缺卡', '异常打卡']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: {
      type: 'value',
      name: 'HrHr次数'
    },
    series: [
      {
        name: '迟到',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '早退',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: { color: '#f56c6c' }
      },
      {
        name: '旷工',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: { color: '#f56c6c' }
      },
      {
        name: '缺卡',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: { color: '#909399' }
      },
      {
        name: '异常打卡',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: { color: '#409eff' }
      }
    ]
  })

  // 部门分布图
  const department = echarts.init(departmentChart.value)
  department.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '16',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }]
  })

  // 时段热力图
  const heatmap = echarts.init(timeHeatmap.value)
  heatmap.setOption({
    tooltip: {
      position: 'top',
   
      formatter: function(params: unknown) {
        return `${params.data[1]} ${params.data[0]}点<br/>异常次数: ${params.data[2]}`
      }
    },
    grid: {
      height: '80%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`),
      splitArea: { show: true }
    },
    yAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      splitArea: { show: true }
    },
    visualMap: {
      min: 0,
      max: 50,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%',
      inRange: {
        color: ['#f0f0f0', '#ffeda0', '#feb24c', '#fd8d3c', '#f03b20', '#bd0026']
      }
    },
    series: [{
      name: '异常分布',
      type: 'heatmap',
      data: [],
      label: { show: true },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  })

  // TOP榜图表
  const top = echarts.init(topChart.value)
  top.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '次数'
    },
    yAxis: {
      type: 'category',
      data: []
    },
    series: [{
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#feb24c' },
          { offset: 1, color: '#f03b20' }
        ])
      },
      label: {
        show: true,
        position: 'right',
        formatter: '{c}'
      }
    }]
  })

  // 响应式调整
  window.addEventListener('resize', () => {
    trend.resize()
    department.resize()
    heatmap.resize()
    top.resize()
  })
}

// 获取异常类型标签
function getAbnormalTypeTag(type: string) {
  const map: Record<string, string> = {
    late: 'warning',
    early: 'danger',
    absent: 'danger',
    missing: 'info',
    outOfRange: ''
  }
  return map[type] || ''
}

// 获取异常类型文本
function getAbnormalTypeText(type: string) {
  const map: Record<string, string> = {
    late: '迟到',
    early: '早退',
    absent: '旷工',
    missing: '缺卡',
    outOfRange: '打卡异常'
  }
  return map[type] || type
}

// 获取严重程度标签
function getSeverityTag(severity: string) {
  const map: Record<string, string> = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return map[severity] || ''
}

// 获取严重程度文本
function getSeverityText(severity: string) {
  const map: Record<string, string> = {
    low: '轻微',
    medium: '中等',
    high: '严重'
  }
  return map[severity] || severity
}

// 查询数据
async function handleSearch() {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      showProcessed: showProcessed.value,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    }

    const data = await attendanceApi.getAbnormalAnalysis(params)

    // 更新表格数据
    tableData.value = data.list
    pagination.total = data.total

    // 更新概览数据
    updateOverviewData(data.overview)

    // 更新图表数据
    updateChartsData(data.charts)

  } catch (__error) {
    ElMessage.error('获取异常数据失败')
  } finally {
    loading.value = false
  }
}

// 更新概览数据
   
function updateOverviewData(data: unknown) {
  overviewData.value[0].value = data.totalCount
  overviewData.value[0].trend = data.totalTrend
  
  overviewData.value[1].value = data.lateCount
  overviewData.value[1].trend = data.lateTrend
  
  overviewData.value[2].value = data.absentCount
  overviewData.value[2].trend = data.absentTrend
  
  overviewData.value[3].value = data.unprocessedCount
  overviewData.value[3].trend = data.unprocessedTrend
}

// 更新图表数据
   
function updateChartsData(data: unknown) {
  // 更新趋势图
  const trendInstance = echarts.getInstanceByDom(trendChart.value)
  trendInstance.setOption({
    xAxis: { data: data.trend.xAxis },
    series: [
      { data: data.trend.late },
      { data: data.trend.early },
      { data: data.trend.absent },
      { data: data.trend.missing },
      { data: data.trend.outOfRange }
    ]
  })

  // 更新部门分布图
  const departmentInstance = echarts.getInstanceByDom(departmentChart.value)
  departmentInstance.setOption({
    series: [{ data: data.department }]
  })

  // 更新热力图
  const heatmapInstance = echarts.getInstanceByDom(timeHeatmap.value)
  heatmapInstance.setOption({
    series: [{ data: data.heatmap }]
  })

  // 更新TOP榜
  const topInstance = echarts.getInstanceByDom(topChart.value)
  topInstance.setOption({
    yAxis: { data: data.top.names },
    series: [{ data: data.top.values }]
  })
}

// 重置搜索
function resetSearch() {
  searchForm.dateRange = [
    dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ]
  searchForm.departmentId = ''
  searchForm.abnormalType = []
  searchForm.severity = ''
  handleSearch()
}

// 刷新数据
function refreshData() {
  handleSearch()
}

// 导出数据
async function exportData() {
  try {
    loading.value = true
    const data = await attendanceApi.exportAbnormalAnalysis(searchForm)
    
    exportToExcel({
      data: data.list,
      columns: [
        { key: 'date', title: '日期' },
        { key: 'employeeName', title: '姓名' },
        { key: 'employeeNo', title: '工号' },
        { key: 'department', title: '部门' },
        { key: 'abnormalType', title: '异常类型' },
        { key: 'description', title: '异常描述' },
        { key: 'severity', title: '严重程度' },
        { key: 'duration', title: '异常时长(分钟)' },
        { key: 'status', title: '处理状态' },
        { key: 'processRemark', title: '处理说明' }
      ],
      filename: `异常考勤分析_${dayjs().format('YYYYMMDD')}`
    })
    
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}

// 选择变化
   
function handleSelectionChange(val: unknown[]) {
  selectedRows.value = val
}

// 批量处理
function batchProcess() {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要处理的记录')
    return
  }
  processForm.type = ''
  processForm.remark = ''
  processForm.deductAmount = 0
  processDialogVisible.value = true
}

// 处理单条记录
   
function processRecord(row: unknown) {
  currentRecord.value = row
  processForm.type = ''
  processForm.remark = ''
  processForm.deductAmount = 0
  processDialogVisible.value = true
}

// 确认处理
async function confirmProcess() {
  if (!processForm.type) {
    ElMessage.warning('请选择处理类型')
    return
  }

  try {
    const ids = selectedRows.value.length > 0 
      ? selectedRows.value.map(row => row.id)
      : [currentRecord.value.id]

    await attendanceApi.processAbnormal({
      ids,
      ...processForm
    })

    ElMessage.success('处理成功')
    processDialogVisible.value = false
    handleSearch()
  } catch (__error) {
    ElMessage.error('处理失败')
  }
}

// 查看详情
   
function viewDetail(row: unknown) {
  currentRecord.value = row
  detailDialogVisible.value = true
}

// 通知员工
   
async function notifyEmployee(row: unknown) {
  try {
    await ElMessageBox.confirm(
      `确定要通知员工 ${row.employeeName} 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await attendanceApi.notifyEmployee({
      employeeId: row.employeeId,
      abnormalId: row.id,
      type: row.abnormalType
    })

    ElMessage.success('通知已发送')
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('通知发送失败')
    }
  }
}

// 分页处理
function handleSizeChange(val: number) {
  pagination.pageSize = val
  handleSearch()
}

function handleCurrentChange(val: number) {
  pagination.currentPage = val
  handleSearch()
}

// 监听查询条件变化
watch([trendPeriod, topType, showProcessed], () => {
  handleSearch()
})

// 初始化
onMounted(async () => {
  // 加载部门树
  try {
    departmentTree.value = await attendanceApi.getDepartmentTree()
  } catch (__error) {
    console.error('获取部门树失败:', error)
  }

  // 初始化图表
  initCharts()

  // 加载数据
  handleSearch()
})
</script>

<style lang="scss" scoped>
.abnormal-attendance-analysis {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .abnormal-overview {
    margin-bottom: 20px;
    
    .overview-card {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .card-title {
          font-size: 14px;
          color: #606266;
        }
      }
      
      .card-value {
        font-size: 32px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .card-label {
          font-size: 12px;
          color: #909399;
        }
        
        .card-percent {
          font-size: 12px;
          
          &.up {
            color: #f56c6c;
          }
          
          &.down {
            color: #67c23a;
          }
        }
      }
      
      &.card-danger {
        border-left: 4px solid #f56c6c;
        
        .icon-danger {
          color: #f56c6c;
        }
      }
      
      &.card-warning {
        border-left: 4px solid #e6a23c;
        
        .icon-warning {
          color: #e6a23c;
        }
      }
      
      &.card-info {
        border-left: 4px solid #409eff;
        
        .icon-info {
          color: #409eff;
        }
      }
    }
  }
  
  .charts-container {
    margin-bottom: 20px;
    
    .chart-card {
      height: 400px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 320px;
      }
    }
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-tools {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
    
    .el-pagination {
      margin-top: 20px;
      justify-content: flex-end;
    }
  }
}
</style>