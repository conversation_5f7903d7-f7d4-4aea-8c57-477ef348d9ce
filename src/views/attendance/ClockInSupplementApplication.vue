<template>
  <div class="clock-in-supplement">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>补卡申请</h2>
      <div class="header-actions">
        <el-button @click="viewHistory">申请记录</el-button>
        <el-button @click="viewMonthlyQuota">本月额度</el-button>
      </div>
    </div>

    <!-- 申请表单 -->
    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="补卡日期" prop="supplementDate">
              <el-date-picker
                v-model="form.supplementDate"
                type="date"
                placeholder="选择补卡日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                @change="onDateChange"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补卡类型" prop="supplementType">
              <el-select v-model="form.supplementType" placeholder="请选择补卡类型">
                <el-option value="morning" label="上班补卡"  />
                <el-option value="evening" label="下班补卡"  />
                <el-option value="both" label="全天补卡"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 原始打卡记录 -->
        <el-form-item label="打卡记录" v-if="originalRecord">
          <div class="original-record">
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="应到时间">
                {{ originalRecord.scheduledStart }} - {{ originalRecord.scheduledEnd }}
              </el-descriptions-item>
              <el-descriptions-item label="实际打卡">
                <span :class="{ 'text-danger': !originalRecord.clockIn }">
                  上班：{{ originalRecord.clockIn || '未打卡' }}
                </span>
                <br>
                <span :class="{ 'text-danger': !originalRecord.clockOut }">
                  下班：{{ originalRecord.clockOut || '未打卡' }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="考勤状态">
                <el-tag :type="getStatusType(originalRecord.status)" size="small">
                  {{ originalRecord.statusName }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="异常说明">
                {{ originalRecord.abnormalDesc || '无' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-form-item>

        <!-- 补卡时间 -->
        <template v-if="form.supplementType !== 'both'">
          <el-form-item 
            :label="form.supplementType === 'morning' ? '上班时间' : '下班时间'" 
            prop="supplementTime"
          >
            <el-time-picker
              v-model="form.supplementTime"
              placeholder="选择补卡时间"
              format="HH:mm"
              value-format="HH:mm:ss"
             />
            <span class="time-tip">
              标准时间：{{ form.supplementType === 'morning' ? standardTime.start : standardTime.end }}
            </span>
          </el-form-item>
        </template>
        <template v-else>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="上班时间" prop="morningTime">
                <el-time-picker
                  v-model="form.morningTime"
                  placeholder="选择上班时间"
                  format="HH:mm"
                  value-format="HH:mm:ss"
                 />
                <span class="time-tip">标准时间：{{ standardTime.start }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="下班时间" prop="eveningTime">
                <el-time-picker
                  v-model="form.eveningTime"
                  placeholder="选择下班时间"
                  format="HH:mm"
                  value-format="HH:mm:ss"
                 />
                <span class="time-tip">标准时间：{{ standardTime.end }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <el-form-item label="补卡原因" prop="reason">
          <el-select v-model="form.reasonType" placeholder="请选择原因类型" style="width: 200px; margin-right: 10px;">
            <el-option value="forget" label="忘记打卡"  />
            <el-option value="system" label="系统故障"  />
            <el-option value="business" label="公务外出"  />
            <el-option value="device" label="设备问题"  />
            <el-option value="other" label="其他原因"  />
          </el-select>
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="3"
            placeholder="请详细说明补卡原因（不少于10个字）"
            maxlength="200"
            show-word-limit
            style="margin-top: 10px;"
            />
        </el-form-item>

        <!-- 证明材料 -->
        <el-form-item label="证明材料" :required="materialRequired">
          <el-upload
            v-model:file-list="form.attachments"
            class="attachment-upload"
            action="#"
            :auto-upload="false"
            :limit="3"
            accept=".jpg,.jpeg,.png,.pdf"
            :on-exceed="handleExceed"
          >
            <el-button type="primary" plain>
              <el-icon><Upload /></el-icon>
              上传证明
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持jpg/png/pdf格式，单个文件不超过5MB
                <span v-if="materialTip" class="material-tip">
                  （{{ materialTip }}）
                </span>
              </div>
            </template>
          </el-upload>
          
          <!-- 证明材料示例 -->
          <div class="material-examples" v-if="form.reasonType">
            <div class="example-title">证明材料示例：</div>
            <el-space>
              <el-tag 
                v-for="example in materialExamples" 
                :key="example"
                type="info"
                size="small"
              >
                {{ example }}
              </el-tag>
            </el-space>
          </div>
        </el-form-item>

        <!-- 额度提醒 -->
        <el-form-item label="补卡额度">
          <div class="quota-info">
            <el-progress 
              :percentage="quotaPercentage" 
              :color="quotaColor"
              :stroke-width="10"
              style="width: 300px;"
             />
            <span class="quota-text">
              本月已使用 {{ monthlyUsed }}/{{ monthlyQuota }} 次
            </span>
          </div>
          <el-alert
            v-if="quotaWarning"
            :title="quotaWarning"
            type="warning"
            show-icon
            :closable="false"
            style="margin-top: 10px;"
           />
        </el-form-item>

        <!-- 审批流程 -->
        <el-form-item label="审批流程">
          <div class="approval-flow-simple">
            <div class="flow-step" :class="{ active: true }">
              <div class="step-icon">1</div>
              <div class="step-name">直接主管</div>
            </div>
            <div class="flow-line"></div>
            <div class="flow-step">
              <div class="step-icon">2</div>
              <div class="step-name">人事备案</div>
            </div>
          </div>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">
            提交申请
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 本月额度对话框 -->
    <el-dialog
      v-model="quotaDialogVisible"
      title="本月补卡额度"
      width="600px"
    >
      <div class="quota-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="月度额度">
            {{ monthlyQuota }} 次
          </el-descriptions-item>
          <el-descriptions-item label="已使用">
            <span :class="{ 'text-danger': monthlyUsed >= monthlyQuota }">
              {{ monthlyUsed }} 次
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="剩余额度">
            <span :class="{ 'text-warning': monthlyQuota - monthlyUsed <= 1 }">
              {{ monthlyQuota - monthlyUsed }} 次
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="额度周期">
            自然月
          </el-descriptions-item>
        </el-descriptions>

        <el-divider   />

        <div class="quota-rules">
          <h4>补卡规则说明：</h4>
          <ul>
            <li>每月补卡额度为{{ monthlyQuota }}次，不累计、不结转</li>
            <li>忘记打卡需在3个工作日内申请补卡</li>
            <li>系统故障、公务外出等需提供相关证明</li>
            <li>恶意使用补卡功能将受到处罚</li>
            <li>特殊情况可申请额外补卡次数</li>
          </ul>
        </div>

        <el-table 
          :data="supplementHistory" 
          style="margin-top: 20px;"
          max-height="300"
        >
          <el-table-column prop="date" label="补卡日期" width="100"  />
          <el-table-column prop="type" label="类型" width="80">
            <template #default="{ row }">
              {{ getSupplementTypeName(row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="原因"  />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)" size="small">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/stores'
import { workflowApi } from '@/api/workflow'
import { attendanceApi } from '@/api/attendance'

// 表单数据
const form = reactive({
  supplementDate: '',
  supplementType: '',
  supplementTime: '',
  morningTime: '',
  eveningTime: '',
  reasonType: '',
  reason: '',
  attachments: []
})

// 表单验证规则
const rules = {
  supplementDate: [
    { required: true, message: '请选择补卡日期', trigger: 'change' }
  ],
  supplementType: [
    { required: true, message: '请选择补卡类型', trigger: 'change' }
  ],
  supplementTime: [
    { required: true, message: '请选择补卡时间', trigger: 'change' }
  ],
  morningTime: [
    { required: true, message: '请选择上班时间', trigger: 'change' }
  ],
  eveningTime: [
    { required: true, message: '请选择下班时间', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请填写补卡原因', trigger: 'blur' },
    { min: 10, message: '补卡原因不少于10个字', trigger: 'blur' }
  ]
}

// 响应式变量
const formRef = ref()
const loading = ref(false)
const userStore = useUserStore()
const quotaDialogVisible = ref(false)
const originalRecord = ref(null)
const standardTime = ref({ start: '09:00', end: '18:00' })
const monthlyQuota = ref(3)
const monthlyUsed = ref(0)
const supplementHistory = ref([])

// 计算属性
const quotaPercentage = computed(() => {
  return Math.min(100, (monthlyUsed.value / monthlyQuota.value) * 100)
})

const quotaColor = computed(() => {
  const percentage = quotaPercentage.value
  if (percentage < 60) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
})

const quotaWarning = computed(() => {
  const remaining = monthlyQuota.value - monthlyUsed.value
  if (remaining === 0) return '本月补卡额度已用完，无法申请'
  if (remaining === 1) return '本月仅剩1次补卡机会，请谨慎使用'
  return ''
})

const materialRequired = computed(() => {
  return ['system', 'business', 'device'].includes(form.reasonType)
})

const materialTip = computed(() => {
  const tips: Record<string, string> = {
    system: '需提供系统故障截图或IT部门证明',
    business: '需提供外出审批单或会议通知',
    device: '需提供设备故障照片或维修单'
  }
  return tips[form.reasonType] || ''
})

const materialExamples = computed(() => {
  const examples: Record<string, string[]> = {
    forget: ['工作证照片', '同事证明'],
    system: ['错误截图', 'IT工单'],
    business: ['会议通知', '外出审批单', '出差证明'],
    device: ['故障照片', '维修记录'],
    other: ['相关证明材料']
  }
  return examples[form.reasonType] || []
})

// 方法
function disabledDate(date: Date) {
  // 只能补最近7天内的卡
  const today = new Date()
  const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
  return date > today || date < sevenDaysAgo
}

async function onDateChange(date: string) {
  if (!date) return
  
  try {
    // 获取该日期的打卡记录
    const record = await attendanceApi.getClockRecord({
      employeeId: userStore.userInfo.employeeId,
      date: date
    })
    
    originalRecord.value = record
    
    // 根据打卡记录自动选择补卡类型
    if (!record.clockIn && !record.clockOut) {
      form.supplementType = 'both'
    } else if (!record.clockIn) {
      form.supplementType = 'morning'
    } else if (!record.clockOut) {
      form.supplementType = 'evening'
    } else {
      ElMessage.info('该日期打卡记录完整，无需补卡')
      form.supplementDate = ''
    }
    
    // 获取标准上下班时间
    const rule = await attendanceApi.getAttendanceRule({
      employeeId: userStore.userInfo.employeeId,
      date: date
    })
    standardTime.value = {
      start: rule.workTime.start,
      end: rule.workTime.end
    }
    
  } catch (__error) {
    }
}

function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    normal: 'success',
    late: 'warning',
    absent: 'danger',
    leave: 'info'
  }
  return typeMap[status] || 'info'
}

function getSupplementTypeName(type: string) {
  const nameMap: Record<string, string> = {
    morning: '上班',
    evening: '下班',
    both: '全天'
  }
  return nameMap[type] || type
}

function getStatusTag(status: string) {
  const tagMap: Record<string, string> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return tagMap[status] || ''
}

function getStatusName(status: string) {
  const nameMap: Record<string, string> = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return nameMap[status] || status
}

function handleExceed() {
  ElMessage.warning('最多只能上传3个文件')
}

async function viewHistory() {
  // 跳转到补卡申请记录
  window.location.href = '/attendance/supplement-history'
}

async function viewMonthlyQuota() {
  try {
    loading.value = true
    
    // 获取本月补卡统计
    const data = await attendanceApi.getSupplementQuota({
      employeeId: userStore.userInfo.employeeId,
      month: dayjs().format('YYYY-MM')
    })
    
    monthlyUsed.value = data.used
    supplementHistory.value = data.history
    
    quotaDialogVisible.value = true
    
  } catch (__error) {
    ElMessage.error('获取补卡额度失败')
  } finally {
    loading.value = false
  }
}

async function submitForm() {
  const valid = await formRef.value.validate()
  if (!valid) return
  
  // 检查额度
  if (monthlyUsed.value >= monthlyQuota.value) {
    ElMessage.error('本月补卡额度已用完')
    return
  }
  
  // 检查证明材料
  if (materialRequired.value && form.attachments.length === 0) {
    ElMessage.error('请上传必要的证明材料')
    return
  }
  
  // 确认提交
  try {
    await ElMessageBox.confirm(
      `确定要申请${form.supplementDate}的补卡吗？本月剩余额度：${monthlyQuota.value - monthlyUsed.value}次`,
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
  } catch {
    return
  }
  
  try {
    loading.value = true
    
    // 准备提交数据
    const submitData = {
      ...form,
      employeeId: userStore.userInfo.employeeId,
      employeeName: userStore.userInfo.name,
      department: userStore.userInfo.department,
      applyTime: new Date().toISOString(),
      originalRecord: originalRecord.value,
      // 构建补卡时间数据
      clockTimes: getClockTimes()
    }
    
    // 上传附件
    if (form.attachments.length > 0) {
      const fileIds = await uploadAttachments()
      submitData.attachmentIds = fileIds
    }
    
    // 启动流程
    const result = await workflowApi.startProcess({
      processKey: 'clock_supplement',
      businessKey: `SUPPLEMENT_${Date.now()}`,
      variables: submitData
    })
    
    ElMessage.success('补卡申请已提交')
    
    // 跳转到申请详情页
    setTimeout(() => {
      window.location.href = `/workflow/instance/${result.instanceId}`
    }, 1500)
    
  } catch (__error) {
    ElMessage.error('提交申请失败')
  } finally {
    loading.value = false
  }
}

function getClockTimes() {
   
  const times: unknown = {}
  
  if (form.supplementType === 'morning' || form.supplementType === 'both') {
    times.clockIn = `${form.supplementDate} ${form.morningTime || form.supplementTime}`
  }
  
  if (form.supplementType === 'evening' || form.supplementType === 'both') {
    times.clockOut = `${form.supplementDate} ${form.eveningTime || form.supplementTime}`
  }
  
  return times
}

async function uploadAttachments() {
  // 实际项目中应该实现文件上传逻辑
  return form.attachments.map((file, index) => `file_${index}`)
}

function resetForm() {
  formRef.value.resetFields()
  form.attachments = []
  originalRecord.value = null
}

// 监听原因类型变化
watch(() => form.reasonType, (newVal) => {
  if (newVal === 'forget') {
    form.reason = '忘记打卡，'
  } else if (newVal === 'system') {
    form.reason = '考勤系统故障，无法正常打卡，'
  } else if (newVal === 'business') {
    form.reason = '因公务外出，'
  } else if (newVal === 'device') {
    form.reason = '考勤设备故障，'
  }
})

// 初始化
async function init() {
  try {
    // 获取本月已使用次数
    const quota = await attendanceApi.getSupplementQuota({
      employeeId: userStore.userInfo.employeeId,
      month: dayjs().format('YYYY-MM')
    })
    monthlyUsed.value = quota.used
  } catch (__error) {
    }
}

init()
</script>

<style lang="scss" scoped>
.clock-in-supplement {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
  }
  
  .form-card {
    .el-form {
      max-width: 1000px;
      margin: 0 auto;
    }
    
    .original-record {
      background: #f5f7fa;
      padding: 10px;
      border-radius: 4px;
      
      .text-danger {
        color: #f56c6c;
      }
    }
    
    .time-tip {
      margin-left: 10px;
      color: #909399;
      font-size: 12px;
    }
    
    .attachment-upload {
      width: 100%;
      
      .el-upload__tip {
        margin-top: 7px;
        
        .material-tip {
          color: #e6a23c;
        }
      }
    }
    
    .material-examples {
      margin-top: 10px;
      padding: 10px;
      background: #f5f7fa;
      border-radius: 4px;
      
      .example-title {
        font-size: 12px;
        color: #606266;
        margin-bottom: 8px;
      }
    }
    
    .quota-info {
      .quota-text {
        margin-left: 15px;
        color: #606266;
      }
    }
    
    .approval-flow-simple {
      display: flex;
      align-items: center;
      
      .flow-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        &.active {
          .step-icon {
            background: #409eff;
            color: #fff;
          }
        }
        
        .step-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #e4e7ed;
          color: #909399;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 500;
          margin-bottom: 8px;
        }
        
        .step-name {
          font-size: 14px;
          color: #606266;
        }
      }
      
      .flow-line {
        width: 80px;
        height: 2px;
        background: #e4e7ed;
        margin: 0 20px;
        margin-bottom: 24px;
      }
    }
  }
}

.quota-details {
  .text-danger {
    color: #f56c6c;
  }
  
  .text-warning {
    color: #e6a23c;
  }
  
  .quota-rules {
    h4 {
      margin-bottom: 10px;
      color: #303133;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
        line-height: 1.6;
      }
    }
  }
}
</style>