<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        :disabled="mode === 'view'"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="员工" prop="employeeId">
              <el-select
                v-model="formData.employeeId"
                placeholder="请选择员工"
                filterable
                remote
                :remote-method="searchEmployees"
                :loading="employeeLoading"
                style="width: 100%"
                @change="handleEmployeeChange"
              >
                <el-option
                  v-for="employee in employeeOptions"
                  :key="employee.id"
                  :label="`${employee.name} (${employee.employeeCode})`"
                  :value="employee.id"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请假类型" prop="leaveType">
              <el-select v-model="formData.leaveType" placeholder="请选择请假类型" style="width: 100%">
                <el-option
                  v-for="item in attendanceOptions.leaveType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                placeholder="请选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                @change="calculateLeaveDays"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                placeholder="请选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                @change="calculateLeaveDays"
               />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                v-model="formData.startTime"
                placeholder="请选择开始时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
                @change="calculateLeaveDays"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                v-model="formData.endTime"
                placeholder="请选择结束时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
                @change="calculateLeaveDays"
               />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="请假天数">
              <el-input
                v-model="leaveDaysDisplay"
                readonly
                style="width: 200px"
              >
                <template #suffix>天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="请假事由" prop="leaveReason">
              <el-input
                v-model="formData.leaveReason"
                type="textarea"
                :rows="3"
                placeholder="请详细说明请假事由"
                maxlength="500"
                show-word-limit
                />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="showCourseArrangement">
          <el-col :span="24">
            <el-form-item label="调课情况" prop="courseArrangement">
              <el-input
                v-model="formData.courseArrangement"
                type="textarea"
                :rows="2"
                placeholder="请说明调课安排（适用于任课教师）"
                maxlength="300"
                show-word-limit
                />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="附件上传">
              <el-upload
                ref="uploadRef"
                :action="uploadAction"
                :headers="uploadHeaders"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :before-upload="beforeUpload"
                :file-list="fileList"
                :disabled="mode === 'view'"
              >
                <el-button type="primary" :disabled="mode === 'view'">
                  <el-icon><Upload /></el-icon>
                  上传附件
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg/png/pdf文件，且不超过10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 审批信息（查看模式） -->
        <div v-if="mode === 'view' && leaveApplication">
          <el-divider content-position="left">审批信息</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="审批状态">
                <el-tag :type="getStatusTagType(leaveApplication.approvalStatus)">
                  {{ leaveApplication.approvalStatusName }}
                </el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="提交时间">
                <span>{{ formatDateTime(leaveApplication.submissionTime || '') }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="leaveApplication.approvalComments">
            <el-col :span="24">
              <el-form-item label="审批意见">
                <el-input
                  :value="leaveApplication.approvalComments"
                  type="textarea"
                  :rows="2"
                  readonly
                  />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="leaveApplication.approvalTime">
            <el-col :span="12">
              <el-form-item label="审批时间">
                <span>{{ formatDateTime(leaveApplication.approvalTime) }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="leaveApplication.isCancelled">
            <el-col :span="12">
              <el-form-item label="销假时间">
                <span>{{ formatDateTime(leaveApplication.cancellationTime || '') }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ mode === 'view' ? '关闭' : '取消' }}
        </el-button>
        <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ mode === 'add' ? '提交申请' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { attendanceApi, attendanceOptions, type LeaveApplication, type LeaveApplicationCreateRequest } from '@/api/attendance'
import { employeeApi, type Employee } from '@/api/employee'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  visible: boolean
  leaveApplication?: LeaveApplication | null
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  leaveApplication: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const submitLoading = ref(false)
const employeeLoading = ref(false)
const employeeOptions = ref<Employee[]>([])
const fileList = ref<any[]>([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add':
      return '申请请假'
    case 'edit':
      return '编辑请假申请'
    case 'view':
      return '查看请假申请'
    default:
      return ''
  }
})

// 表单数据
const formData = reactive<LeaveApplicationCreateRequest>({
  employeeId: 0,
  leaveType: 'PERSONAL_LEAVE',
  startDate: '',
  endDate: '',
  startTime: '',
  endTime: '',
  leaveReason: '',
  courseArrangement: '',
  attachmentUrl: ''
})

// 表单验证规则
const formRules = {
  employeeId: [
    { required: true, message: '请选择员工', trigger: 'change' }
  ],
  leaveType: [
    { required: true, message: '请选择请假类型', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' }
  ],
  leaveReason: [
    { required: true, message: '请填写请假事由', trigger: 'blur' },
    { min: 5, max: 500, message: '请假事由长度在 5 到 500 个字符', trigger: 'blur' }
  ]
}

// 是否显示调课情况
const showCourseArrangement = computed(() => {
  // 这里可以根据员工类型判断是否为任课教师
  return true
})

// 请假天数显示
const leaveDaysDisplay = computed(() => {
  const days = calculateLeaveDaysValue()
  return days > 0 ? days.toFixed(1) : '0'
})

// 上传配置
const uploadAction = '/api/v1/attendance/upload/leave-attachment'
const uploadHeaders = {
  // 这里可以添加认证头
}

// 计算请假天数
const calculateLeaveDays = () => {
  // 这里实现请假天数计算逻辑
  // 考虑工作日、节假日、时间等因素
}

const calculateLeaveDaysValue = (): number => {
  if (!formData.startDate || !formData.endDate) {
    return 0
  }
  
  const start = new Date(formData.startDate)
  const end = new Date(formData.endDate)
  
  if (end < start) {
    return 0
  }
  
  // 简单计算天数差，实际应该考虑工作日、节假日等
  const diffTime = end.getTime() - start.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  
  // 如果有具体时间，需要更精确的计算
  if (formData.startTime && formData.endTime) {
    // 这里可以实现更精确的时间计算
  }
  
  return diffDays
}

// 搜索员工
const searchEmployees = async (query: string) => {
  if (!query) {
    employeeOptions.value = []
    return
  }
  
  try {
    employeeLoading.value = true
    const result = await employeeApi.queryEmployees({
      keyword: query,
      size: 20
    })
    employeeOptions.value = result.content
  } catch (__error) {
    console.error('搜索员工失败:', error)
  } finally {
    employeeLoading.value = false
  }
}

// 员工选择变化
const handleEmployeeChange = (employeeId: number) => {
  const employee = employeeOptions.value.find(emp => emp.id === employeeId)
  if (employee) {
    // 可以根据员工信息设置一些默认值
  }
}

// 文件上传相关
const beforeUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 JPG/PNG/PDF 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

   
const handleUploadSuccess = (response: unknown) => {
  if (response.url) {
    formData.attachmentUrl = response.url
    ElMessage.success('文件上传成功')
  }
}

const handleUploadError = () => {
  ElMessage.error('文件上传失败')
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING_DEPARTMENT':
    case 'PENDING_HR':
    case 'PENDING_LEADERSHIP':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 设置请假天数
    const leaveDays = calculateLeaveDaysValue()
    const submitData = {
      ...formData,
      leaveDays
    }
    
    if (props.mode === 'add') {
      await attendanceApi.createLeaveApplication(submitData)
      ElMessage.success('请假申请提交成功')
    } else {
      await attendanceApi.updateLeaveApplication(props.leaveApplication!.id!, submitData)
      ElMessage.success('请假申请修改成功')
    }
    
    emit('success')
    handleClose()
  } catch (__error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    employeeId: 0,
    leaveType: 'PERSONAL_LEAVE',
    startDate: '',
    endDate: '',
    startTime: '',
    endTime: '',
    leaveReason: '',
    courseArrangement: '',
    attachmentUrl: ''
  })
  fileList.value = []
  employeeOptions.value = []
  formRef.value?.clearValidate()
}

// 监听请假申请变化，初始化表单
watch(() => props.leaveApplication, (newVal) => {
  if (newVal && props.mode !== 'add') {
    Object.assign(formData, {
      employeeId: newVal.employeeId,
      leaveType: newVal.leaveType,
      startDate: newVal.startDate,
      endDate: newVal.endDate,
      startTime: newVal.startTime || '',
      endTime: newVal.endTime || '',
      leaveReason: newVal.leaveReason,
      courseArrangement: newVal.courseArrangement || '',
      attachmentUrl: newVal.attachmentUrl || ''
    })
    
    // 设置员工选项
    if (newVal.employeeName) {
      employeeOptions.value = [{
        id: newVal.employeeId,
        name: newVal.employeeName,
        employeeCode: newVal.employeeCode || ''
      } as Employee]
    }
    
    // 设置文件列表
    if (newVal.attachmentUrl) {
      fileList.value = [{
        name: 'HrHr附件',
        url: newVal.attachmentUrl
      }]
    }
  }
}, { immediate: true })
</script>

<style scoped>
.dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}
</style>
