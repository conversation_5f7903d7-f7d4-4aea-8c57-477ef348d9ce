<template>
  <el-dialog
    v-model="dialogVisible"
    title="审批请假申请"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <!-- 请假申请信息 -->
      <el-card class="application-info" shadow="never">
        <template #header>
          <span>请假申请信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请人">
            {{ leaveApplication?.employeeName }} ({{ leaveApplication?.employeeCode }})
          </el-descriptions-item>
          <el-descriptions-item label="所属组织">
            {{ leaveApplication?.organizationName }}
          </el-descriptions-item>
          <el-descriptions-item label="请假类型">
            <el-tag :type="getLeaveTypeTagType(leaveApplication?.leaveType)">
              {{ leaveApplication?.leaveTypeName }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="请假天数">
            {{ leaveApplication?.leaveDays }}天
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ leaveApplication?.startDate }} {{ leaveApplication?.startTime || '' }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ leaveApplication?.endDate }} {{ leaveApplication?.endTime || '' }}
          </el-descriptions-item>
          <el-descriptions-item label="请假事由" :span="2">
            {{ leaveApplication?.leaveReason }}
          </el-descriptions-item>
          <el-descriptions-item v-if="leaveApplication?.courseArrangement" label="调课情况" :span="2">
            {{ leaveApplication?.courseArrangement }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ formatDateTime(leaveApplication?.submissionTime || '') }}
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusTagType(leaveApplication?.approvalStatus)">
              {{ leaveApplication?.approvalStatusName }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 附件预览 -->
        <div v-if="leaveApplication?.attachmentUrl" class="attachment-section">
          <el-divider content-position="left">附件</el-divider>
          <el-button type="primary" link @click="previewAttachment">
            <el-icon><Paperclip /></el-icon>
            查看附件
          </el-button>
        </div>
      </el-card>

      <!-- 审批历史 -->
      <el-card v-if="approvalHistory.length > 0" class="approval-history" shadow="never">
        <template #header>
          <span>审批历史</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in approvalHistory"
            :key="index"
            :timestamp="formatDateTime(item.approvalTime)"
            :type="getTimelineType(item.result)"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="approver">{{ item.approverName }}</span>
                <el-tag :type="getApprovalResultTagType(item.result)" size="small">
                  {{ item.resultName }}
                </el-tag>
              </div>
              <div v-if="item.comments" class="timeline-comments">
                {{ item.comments }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 审批操作 -->
      <el-card class="approval-action" shadow="never">
        <template #header>
          <span>审批操作</span>
        </template>
        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
          <el-form-item label="审批结果" prop="result">
            <el-radio-group v-model="formData.result">
              <el-radio value="APPROVE">
                <el-icon><Check /></el-icon>
                批准
              </el-radio>
              <el-radio value="REJECT">
                <el-icon><Close /></el-icon>
                驳回
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批意见" prop="comments">
            <el-input
              v-model="formData.comments"
              type="textarea"
              :rows="4"
              placeholder="请填写审批意见（驳回时必填）"
              maxlength="500"
              show-word-limit
              />
          </el-form-item>
          <el-form-item label="审批时间">
            <el-date-picker
              v-model="formData.approvalTime"
              type="datetime"
              placeholder="选择审批时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-value="new Date()"
              style="width: 100%"
             />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 审批提醒 -->
      <el-alert
        v-if="approvalReminder"
        :title="approvalReminder.title"
        :description="approvalReminder.description"
        :type="approvalReminder.type"
        show-icon
        :closable="false"
        class="approval-reminder"
       />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          提交审批
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ApprovalDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Close, Paperclip } from '@element-plus/icons-vue'
import { attendanceApi, type LeaveApplication } from '@/api/attendance'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  visible: boolean
  leaveApplication?: LeaveApplication | null
}

const props = withDefaults(defineProps<Props>(), {
  leaveApplication: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)
const approvalHistory = ref<any[]>([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const formData = reactive({
  result: 'APPROVE',
  comments: '',
  approvalTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
})

// 表单验证规则
const formRules = {
  result: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  comments: [
    {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
      validator: (rule: unknown, value: string, callback: Function) => {
        if (formData.result === 'REJECT' && !value.trim()) {
          callback(new Error('驳回时必须填写审批意见'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 审批提醒信息
const approvalReminder = computed(() => {
  if (!props.leaveApplication) return null

  const {leaveType: _leaveType, leaveDays: _leaveDays, approvalStatus: _approvalStatus} =  props.leaveApplication

  // 根据请假类型和天数给出审批提醒
  if (leaveType 
  overflow-y: auto;
}

.application-info,
.approval-history,
.approval-action {
  margin-bottom: 16px;
}

.attachment-section {
  margin-top: 16px;
}

.timeline-content {
  padding: 8px 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.approver {
  font-weight: 600;
  color: #303133;
}

.timeline-comments {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.approval-reminder {
  margin-top: 16px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
