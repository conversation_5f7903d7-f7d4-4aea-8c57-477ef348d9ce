<template>
  <el-dialog
    v-model="dialogVisible"
    title="销假登记"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <!-- 请假申请信息 -->
      <el-card class="application-info" shadow="never">
        <template #header>
          <span>请假申请信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请人">
            {{ leaveApplication?.employeeName }} ({{ leaveApplication?.employeeCode }})
          </el-descriptions-item>
          <el-descriptions-item label="请假类型">
            <el-tag :type="getLeaveTypeTagType(leaveApplication?.leaveType)">
              {{ leaveApplication?.leaveTypeName }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="批准假期">
            {{ leaveApplication?.startDate }} 至 {{ leaveApplication?.endDate }}
          </el-descriptions-item>
          <el-descriptions-item label="批准天数">
            {{ leaveApplication?.leaveDays }}天
          </el-descriptions-item>
          <el-descriptions-item label="请假事由" :span="2">
            {{ leaveApplication?.leaveReason }}
          </el-descriptions-item>
          <el-descriptions-item label="审批时间">
            {{ formatDateTime(leaveApplication?.approvalTime || '') }}
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag type="success">已批准</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 销假信息 -->
      <el-card class="cancellation-info" shadow="never">
        <template #header>
          <span>销假信息</span>
        </template>
        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
          <el-form-item label="销假类型" prop="cancellationType">
            <el-radio-group v-model="formData.cancellationType">
              <el-radio value="NORMAL">
                <el-icon><Check /></el-icon>
                正常销假
              </el-radio>
              <el-radio value="EARLY">
                <el-icon><Clock /></el-icon>
                提前销假
              </el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="实际销假时间" prop="cancellationTime">
            <el-date-picker
              v-model="formData.cancellationTime"
              type="datetime"
              placeholder="选择销假时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              :disabled-date="disabledDate"
             />
          </el-form-item>

          <el-form-item label="实际请假天数">
            <el-input
              :value="actualLeaveDaysDisplay"
              readonly
              style="width: 200px"
            >
              <template #suffix>天</template>
            </el-input>
            <span v-if="daysDifference !== 0" class="days-difference">
              （{{ daysDifference > 0 ? '超出' : '节省' }}{{ Math.abs(daysDifference) }}天）
            </span>
          </el-form-item>

          <el-form-item label="销假说明" prop="cancellationReason">
            <el-input
              v-model="formData.cancellationReason"
              type="textarea"
              :rows="3"
              placeholder="请说明销假情况（提前销假时必填）"
              maxlength="300"
              show-word-limit
              />
          </el-form-item>

          <el-form-item v-if="formData.cancellationType === 'EARLY'" label="工作安排">
            <el-input
              v-model="formData.workArrangement"
              type="textarea"
              :rows="2"
              placeholder="请说明提前销假后的工作安排"
              maxlength="200"
              show-word-limit
              />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 销假提醒 -->
      <el-alert
        v-if="cancellationReminder"
        :title="cancellationReminder.title"
        :description="cancellationReminder.description"
        :type="cancellationReminder.type"
        show-icon
        :closable="false"
        class="cancellation-reminder"
       />

      <!-- 销假统计 -->
      <el-card v-if="showStatistics" class="statistics-info" shadow="never">
        <template #header>
          <span>销假统计</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ leaveApplication?.leaveDays || 0 }}</div>
              <div class="stat-label">批准天数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ actualLeaveDays }}</div>
              <div class="stat-label">实际天数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number" :class="daysDifferenceClass">
                {{ daysDifference > 0 ? '+' : '' }}{{ daysDifference }}
              </div>
              <div class="stat-label">差异天数</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确认销假
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'CancellationDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Clock } from '@element-plus/icons-vue'
import { attendanceApi, type LeaveApplication } from '@/api/attendance'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  visible: boolean
  leaveApplication?: LeaveApplication | null
}

const props = withDefaults(defineProps<Props>(), {
  leaveApplication: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const formData = reactive({
  cancellationType: 'NORMAL',
  cancellationTime: '',
  cancellationReason: '',
  workArrangement: ''
})

// 表单验证规则
const formRules = {
  cancellationType: [
    { required: true, message: '请选择销假类型', trigger: 'change' }
  ],
  cancellationTime: [
    { required: true, message: '请选择销假时间', trigger: 'change' }
  ],
  cancellationReason: [
    {
   
      validator: (rule: unknown, value: string, callback: Function) => {
        if (formData.cancellationType === 'EARLY' && !value.trim()) {
          callback(new Error('提前销假时必须填写销假说明'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算实际请假天数
const actualLeaveDays = computed(() => {
  if (!props.leaveApplication?.startDate || !formData.cancellationTime) {
    return 0
  }
  
  const startDate = new Date(props.leaveApplication.startDate)
  const cancellationDate = new Date(formData.cancellationTime)
  
  if (cancellationDate < startDate) {
    return 0
  }
  
  // 简单计算天数差，实际应该考虑工作日、节假日等
  const diffTime = cancellationDate.getTime() - startDate.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  
  return Math.max(0, diffDays)
})

// 实际请假天数显示
const actualLeaveDaysDisplay = computed(() => {
  return actualLeaveDays.value.toFixed(1)
})

// 天数差异
const daysDifference = computed(() => {
  const approved = props.leaveApplication?.leaveDays || 0
  return actualLeaveDays.value - approved
})

// 天数差异样式类
const daysDifferenceClass = computed(() => {
  if (daysDifference.value > 0) return 'text-danger'
  if (daysDifference.value < 0) return 'text-success'
  return 'text-normal'
})

// 是否显示统计信息
const showStatistics = computed(() => {
  return formData.cancellationTime && props.leaveApplication?.startDate
})

// 销假提醒信息
const cancellationReminder = computed(() => {
  if (!formData.cancellationTime || !props.leaveApplication) return null

  if (formData.cancellationType === 'EARLY') {
    return {
      title: '提前销假提醒',
      description: '提前销假可能影响工作安排，请确认已做好相应的工作交接和安排。',
      type: 'warning'
    }
  }

  if (daysDifference.value > 0) {
    return {
      title: '超期销假提醒',
      description: `实际请假天数超出批准天数${Math.abs(daysDifference.value)}天，请说明具体情况。`,
      type: 'error'
    }
  }

  return {
    title: '销假确认',
    description: '请确认销假信息无误后提交。',
    type: 'success'
  }
})

// 获取请假类型标签类型
const getLeaveTypeTagType = (leaveType?: string) => {
  switch (leaveType) {
    case 'SICK_LEAVE':
      return 'danger'
    case 'PERSONAL_LEAVE':
      return 'warning'
    case 'MATERNITY_LEAVE':
      return 'success'
    case 'MARRIAGE_LEAVE':
      return 'primary'
    default:
      return ''
  }
}

// 禁用日期
const disabledDate = (time: Date) => {
  if (!props.leaveApplication?.startDate) return false
  
  const startDate = new Date(props.leaveApplication.startDate)
  // 不能早于请假开始日期
  return time < startDate
}

// 提交销假
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const confirmMessage = formData.cancellationType === 'EARLY' 
      ? '确定要提前销假吗？' 
      : '确定要销假吗？'
    
    await ElMessageBox.confirm(
      confirmMessage,
      '确认销假',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    submitLoading.value = true
    
    await attendanceApi.cancelLeave(
      props.leaveApplication!.id!,
      formData.cancellationTime
    )
    
    ElMessage.success('销假成功')
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('销假失败:', error)
      ElMessage.error('销假失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    cancellationType: 'NORMAL',
    cancellationTime: '',
    cancellationReason: '',
    workArrangement: ''
  })
  formRef.value?.clearValidate()
}

// 监听对话框显示状态，设置默认销假时间
watch(() => props.visible, (visible) => {
  if (visible && props.leaveApplication) {
    // 设置默认销假时间为当前时间
    formData.cancellationTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
  }
})
</script>

<style scoped>
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.application-info,
.cancellation-info,
.statistics-info {
  margin-bottom: 16px;
}

.days-difference {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.cancellation-reminder {
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-number.text-danger {
  color: #f56c6c;
}

.stat-number.text-success {
  color: #67c23a;
}

.stat-number.text-normal {
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
