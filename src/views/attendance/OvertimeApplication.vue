<template>
  <div class="overtime-application">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>加班申请</h2>
      <div class="header-actions">
        <el-button @click="viewHistory">申请记录</el-button>
        <el-button @click="viewOvertimeStats">加班统计</el-button>
      </div>
    </div>

    <!-- 申请表单 -->
    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="加班类型" prop="overtimeType">
              <el-select v-model="form.overtimeType" placeholder="请选择加班类型">
                <el-option value="weekday" label="工作日加班"  />
                <el-option value="weekend" label="周末加班"  />
                <el-option value="holiday" label="节假日加班"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补偿方式" prop="compensationType">
              <el-radio-group v-model="form.compensationType">
                <el-radio value="pay">加班费</el-radio>
                <el-radio value="leave">调休</el-radio>
                <el-radio value="mixed">混合</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="calculateHours"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledEndDate"
                @change="calculateHours"
               />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="加班时长">
              <el-input v-model="form.overtimeHours" readonly>
                <template #append>小时</template>
              </el-input>
              <div class="form-tip" v-if="overtimeLimit">
                本月已加班：{{ monthlyOvertimeHours }}小时，
                剩余额度：{{ overtimeLimit - monthlyOvertimeHours }}小时
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用餐安排" prop="mealArrangement">
              <el-checkbox-group v-model="form.mealArrangement">
                <el-checkbox label="dinner" :disabled="!needDinner">晚餐</el-checkbox>
                <el-checkbox label="midnight" :disabled="!needMidnight">夜宵</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 混合补偿方式详情 -->
        <template v-if="form.compensationType === 'mixed'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="调休时长" prop="leaveHours">
                <el-input-number
                  v-model="form.leaveHours"
                  :min="0"
                  :max="form.overtimeHours"
                  :step="0.5"
                  controls-position="right"
                  />
                <span class="input-suffix">小时</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="加班费时长">
                <el-input 
                  :value="form.overtimeHours - form.leaveHours" 
                  readonly
                >
                  <template #append>小时</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <el-form-item label="加班事由" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明加班原因和工作内容（不少于20个字）"
            maxlength="500"
            show-word-limit
            />
        </el-form-item>

        <el-form-item label="工作任务" prop="tasks">
          <div class="task-list">
            <div 
              v-for="(task, index) in form.tasks" 
              :key="index"
              class="task-item"
            >
              <el-input 
                v-model="task.content" 
                placeholder="请输入具体工作任务"
                />
              <el-button 
                type="danger" 
                :icon="Delete" 
                circle 
                size="small"
                @click="removeTask(index)"
                v-if="form.tasks.length > 1"
              />
            </div>
            <el-button 
              type="primary" 
              plain 
              size="small"
              @click="addTask"
            >
              <el-icon><Plus /></el-icon>
              添加任务
            </el-button>
          </div>
        </el-form-item>

        <!-- 项目关联 -->
        <el-form-item label="关联项目">
          <el-select 
            v-model="form.projectId" 
            placeholder="请选择关联项目（可选）"
            clearable
          >
            <el-option
              v-for="project in projectList"
              :key="project.id"
              :label="project.name"
              :value="project.id"
             />
          </el-select>
        </el-form-item>

        <!-- 审批人预览 -->
        <el-form-item label="审批流程">
          <div class="approval-flow">
            <div class="flow-node" v-for="(node, index) in approvalNodes" :key="index">
              <div class="node-content">
                <el-avatar :size="32">{{ node.name[0] }}</el-avatar>
                <div class="node-info">
                  <div class="node-name">{{ node.name }}</div>
                  <div class="node-role">{{ node.role }}</div>
                </div>
              </div>
              <el-icon class="flow-arrow" v-if="index < approvalNodes.length - 1">
                <ArrowRight />
              </el-icon>
            </div>
          </div>
        </el-form-item>

        <!-- 提醒事项 -->
        <el-alert 
          v-if="overtimeWarning"
          :title="overtimeWarning"
          type="warning"
          show-icon
          :closable="false"
         />

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">
            提交申请
          </el-button>
          <el-button @click="saveAsDraft">保存草稿</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 加班统计对话框 -->
    <el-dialog
      v-model="statsDialogVisible"
      title="加班统计"
      width="900px"
    >
      <div class="overtime-stats">
        <el-row :gutter="20" class="stats-cards">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ overtimeStats.totalHours }}</div>
              <div class="stat-label">本月加班时长（小时）</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ overtimeStats.convertedLeave }}</div>
              <div class="stat-label">已转调休（小时）</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ overtimeStats.paidHours }}</div>
              <div class="stat-label">已付费（小时）</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ overtimeStats.pendingHours }}</div>
              <div class="stat-label">待处理（小时）</div>
            </div>
          </el-col>
        </el-row>

        <el-divider   />

        <div class="chart-container">
          <div ref="overtimeChart" style="height: 300px;"></div>
        </div>

        <el-table :data="overtimeRecords" style="margin-top: 20px;">
          <el-table-column prop="date" label="日期" width="120"  />
          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTypeTag(row.type)" size="small">
                {{ getTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="hours" label="时长" width="80">
            <template #default="{ row }">
              {{ row.hours }}h
            </template>
          </el-table-column>
          <el-table-column prop="compensation" label="补偿方式" width="100"  />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)" size="small">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="事由"  />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Plus, ArrowRight } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import echarts, { createChart } from '@/utils/echarts'
import { useUserStore } from '@/stores'
import { workflowApi } from '@/api/workflow'
import { attendanceApi } from '@/api/attendance'

// 表单数据
const form = reactive({
  overtimeType: '',
  compensationType: 'leave',
  startTime: '',
  endTime: '',
  overtimeHours: 0,
  mealArrangement: [],
  reason: '',
  tasks: [{ content: '' }],
  projectId: '',
  leaveHours: 0
})

// 表单验证规则
const rules = {
  overtimeType: [
    { required: true, message: '请选择加班类型', trigger: 'change' }
  ],
  compensationType: [
    { required: true, message: '请选择补偿方式', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请填写加班事由', trigger: 'blur' },
    { min: 20, message: '加班事由不少于20个字', trigger: 'blur' }
  ],
  leaveHours: [
    { validator: validateLeaveHours, trigger: 'change' }
  ]
}

// 响应式变量
const formRef = ref()
const loading = ref(false)
const userStore = useUserStore()
const statsDialogVisible = ref(false)
const overtimeChart = ref()
const projectList = ref([])
const overtimeStats = ref({
  totalHours: 0,
  convertedLeave: 0,
  paidHours: 0,
  pendingHours: 0
})
const overtimeRecords = ref([])
const monthlyOvertimeHours = ref(0)
const overtimeLimit = ref(36) // 月度加班上限

// 计算属性
const approvalNodes = computed(() => {
  const nodes = [
    { name: 'HrHr直接主管', role: '部门负责人' }
  ]
  
  // 加班超过8小时需要分管领导审批
  if (form.overtimeHours > 8) {
    nodes.push({ name: '分管领导', role: '分管副校长' })
  }
  
  // 人事备案
  nodes.push({ name: '人事处', role: '备案' })
  
  return nodes
})

const needDinner = computed(() => {
  if (!form.startTime || !form.endTime) return false
  const end = dayjs(form.endTime)
  return end.hour() >= 19 || (end.hour() === 18 && end.minute() >= 30)
})

const needMidnight = computed(() => {
  if (!form.startTime || !form.endTime) return false
  const end = dayjs(form.endTime)
  return end.hour() >= 22
})

const overtimeWarning = computed(() => {
  if (form.overtimeHours > 8) {
    return '单次加班超过8小时需要分管领导审批'
  }
  if (monthlyOvertimeHours.value + form.overtimeHours > overtimeLimit.value) {
    return `注意：本月加班时长将超过${overtimeLimit.value}小时上限`
  }
  if (form.overtimeType === 'weekend' && form.overtimeHours < 4) {
    return '周末加班建议不少于4小时'
  }
  return ''
})

// 方法
   
function validateEndTime(_rule: unknown, value: unknown, callback: unknown) {
  if (value && form.startTime) {
    const start = dayjs(form.startTime)
    const end = dayjs(value)
    
    if (end.isBefore(start)) {
      callback(new Error('结束时间不能早于开始时间'))
    } else if (end.diff(start, 'hour') > 12) {
      callback(new Error('单次加班不能超过12小时'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

   
function validateLeaveHours(_rule: unknown, value: unknown, callback: unknown) {
  if (form.compensationType === 'mixed') {
    if (!value && value !== 0) {
      callback(new Error('请输入调休时长'))
    } else if (value > form.overtimeHours) {
      callback(new Error('调休时长不能超过加班时长'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

function disabledEndDate(date: Date) {
  if (!form.startTime) return false
  const start = dayjs(form.startTime)
  // 只能选择同一天或次日
  return date < start.toDate() || date > start.add(1, 'day').toDate()
}

function calculateHours() {
  if (form.startTime && form.endTime) {
    const start = dayjs(form.startTime)
    const end = dayjs(form.endTime)
    const hours = end.diff(start, 'hour', true)
    
    // 扣除用餐时间
    let mealTime = 0
    if (form.mealArrangement.includes('dinner')) mealTime += 0.5
    if (form.mealArrangement.includes('midnight')) mealTime += 0.5
    
    form.overtimeHours = Math.max(0, Number((hours - mealTime).toFixed(1)))
    
    // 自动勾选用餐
    const mealArr = []
    if (needDinner.value) mealArr.push('dinner')
    if (needMidnight.value) mealArr.push('midnight')
    form.mealArrangement = mealArr
  }
}

function addTask() {
  form.tasks.push({ content: '' })
}

function removeTask(index: number) {
  form.tasks.splice(index, 1)
}

async function viewHistory() {
  // 跳转到加班申请记录
  window.location.href = '/attendance/overtime-history'
}

async function viewOvertimeStats() {
  try {
    loading.value = true
    
    // 获取加班统计数据
    const stats = await attendanceApi.getOvertimeStats({
      employeeId: userStore.userInfo.employeeId,
      month: dayjs().format('YYYY-MM')
    })
    
    overtimeStats.value = stats.summary
    overtimeRecords.value = stats.records
    monthlyOvertimeHours.value = stats.summary.totalHours
    
    statsDialogVisible.value = true
    
    // 绘制图表
    setTimeout(() => {
      drawOvertimeChart(stats.chartData)
    }, 100)
    
  } catch (__error) {
    ElMessage.error('获取加班统计失败')
  } finally {
    loading.value = false
  }
}

   
function drawOvertimeChart(data: unknown) {
  const chart = createChart(overtimeChart.value)
  
  const option = {
    title: {
      text: '近6个月加班趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['工作日', '周末', '节假日'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: data.months
    },
    yAxis: {
      type: 'value',
      name: '小时'
    },
    series: [
      {
        name: '工作日',
        type: 'bar',
        stack: 'total',
        data: data.weekday
      },
      {
        name: '周末',
        type: 'bar',
        stack: 'total',
        data: data.weekend
      },
      {
        name: '节假日',
        type: 'bar',
        stack: 'total',
        data: data.holiday
      }
    ]
  }
  
  chart.setOption(option)
}

function getTypeTag(type: string) {
  const map: Record<string, string> = {
    weekday: '',
    weekend: 'warning',
    holiday: 'danger'
  }
  return map[type] || ''
}

function getTypeName(type: string) {
  const map: Record<string, string> = {
    weekday: '工作日',
    weekend: '周末',
    holiday: '节假日'
  }
  return map[type] || type
}

function getStatusTag(status: string) {
  const map: Record<string, string> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info'
  }
  return map[status] || ''
}

function getStatusName(status: string) {
  const map: Record<string, string> = {
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return map[status] || status
}

async function submitForm() {
  const valid = await formRef.value.validate()
  if (!valid) return
  
  // 检查任务列表
  const validTasks = form.tasks.filter(t => t.content.trim())
  if (validTasks.length === 0) {
    ElMessage.error('请至少填写一项工作任务')
    return
  }
  
  // 检查加班时长
  if (form.overtimeHours < 0.5) {
    ElMessage.error('加班时长不能少于0.5小时')
    return
  }
  
  try {
    loading.value = true
    
    // 准备提交数据
    const submitData = {
      ...form,
      tasks: validTasks,
      employeeId: userStore.userInfo.employeeId,
      employeeName: userStore.userInfo.name,
      department: userStore.userInfo.department,
      applyTime: new Date().toISOString(),
      // 计算补偿
      compensationDetails: getCompensationDetails()
    }
    
    // 启动流程
    const result = await workflowApi.startProcess({
      processKey: 'overtime_approval',
      businessKey: `OVERTIME_${Date.now()}`,
      variables: submitData
    })
    
    ElMessage.success('加班申请已提交')
    
    // 跳转到申请详情页
    setTimeout(() => {
      window.location.href = `/workflow/instance/${result.instanceId}`
    }, 1500)
    
  } catch (__error) {
    ElMessage.error('提交申请失败')
  } finally {
    loading.value = false
  }
}

function getCompensationDetails() {
  if (form.compensationType === 'leave') {
    return {
      type: 'leave',
      leaveHours: form.overtimeHours,
      payHours: 0
    }
  } else if (form.compensationType === 'pay') {
    return {
      type: 'pay',
      leaveHours: 0,
      payHours: form.overtimeHours,
      // 根据加班类型计算倍率
      payRate: getOvertimeRate()
    }
  } else {
    return {
      type: 'mixed',
      leaveHours: form.leaveHours,
      payHours: form.overtimeHours - form.leaveHours,
      payRate: getOvertimeRate()
    }
  }
}

function getOvertimeRate() {
  const rates: Record<string, number> = {
    weekday: 1.5,
    weekend: 2.0,
    holiday: 3.0
  }
  return rates[form.overtimeType] || 1.5
}

function saveAsDraft() {
  const draft = {
    ...form,
    savedAt: new Date().toISOString()
  }
  localStorage.setItem('overtime_application_draft', JSON.stringify(draft))
  ElMessage.success('草稿已保存')
}

function resetForm() {
  formRef.value.resetFields()
  form.tasks = [{ content: '' }]
  form.mealArrangement = []
}

// 生命周期
onMounted(async () => {
  // 加载草稿
  const draft = localStorage.getItem('overtime_application_draft')
  if (draft) {
    const draftData = JSON.parse(draft)
    Object.assign(form, draftData)
    ElMessage.info('已加载上次保存的草稿')
  }
  
  // 加载项目列表
  try {
    projectList.value = await attendanceApi.getActiveProjects()
  } catch (__error) {
    }
  
  // 获取本月加班时长
  try {
    const stats = await attendanceApi.getMonthlyOvertimeHours(
      userStore.userInfo.employeeId
    )
    monthlyOvertimeHours.value = stats.totalHours
  } catch (__error) {
    }
})
</script>

<style lang="scss" scoped>
.overtime-application {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
  }
  
  .form-card {
    .el-form {
      max-width: 1000px;
      margin: 0 auto;
    }
    
    .form-tip {
      margin-top: 5px;
      color: #909399;
      font-size: 12px;
    }
    
    .input-suffix {
      margin-left: 10px;
      color: #606266;
    }
    
    .task-list {
      .task-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        
        .el-input {
          flex: 1;
          margin-right: 10px;
        }
      }
      
      .el-button {
        margin-top: 5px;
      }
    }
    
    .approval-flow {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .flow-node {
        display: flex;
        align-items: center;
        
        .node-content {
          display: flex;
          align-items: center;
          gap: 10px;
          
          .node-info {
            .node-name {
              font-weight: 500;
            }
            
            .node-role {
              font-size: 12px;
              color: #909399;
            }
          }
        }
        
        .flow-arrow {
          margin-left: 10px;
          color: #c0c4cc;
        }
      }
    }
  }
}

.overtime-stats {
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      background: #f5f7fa;
      padding: 20px;
      border-radius: 4px;
      text-align: center;
      
      .stat-value {
        font-size: 32px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }
  
  .chart-container {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
  }
}
</style>