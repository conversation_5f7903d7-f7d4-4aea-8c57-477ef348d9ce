<template>
  <div class="clock-in-record-management">
    <!-- 搜索条件 -->
    <el-card shadow="never" class="mb-4">
      <el-form :model="searchForm" inline>
        <el-form-item label="员工姓名">
          <el-input
            v-model="searchForm.employeeName"
            placeholder="请输入员工姓名"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="选择部门"
            clearable
            style="width: 200px"
           />
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item label="打卡状态">
          <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 120px">
            <el-option label="正常" value="normal"  />
            <el-option label="迟到" value="late"  />
            <el-option label="早退" value="early"  />
            <el-option label="缺卡" value="missing"  />
            <el-option label="异常" value="abnormal"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 视图切换 -->
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>打卡记录</span>
          <el-radio-group v-model="viewType" size="small">
            <el-radio-button label="timeline">时间轴视图</el-radio-button>
            <el-radio-button label="table">表格视图</el-radio-button>
            <el-radio-button label="calendar">日历视图</el-radio-button>
          </el-radio-group>
        </div>
      </template>

      <!-- 时间轴视图 -->
      <div v-if="viewType === 'timeline'" class="timeline-view" v-loading="loading">
        <div v-for="employee in timelineData" :key="employee.id" class="employee-timeline">
          <div class="employee-header">
            <el-avatar  :size="40">
            <img v-lazy="employee.avatar" style="width: 40px; height: 40px; object-fit: cover;" />
          </el-avatar>
              {{ employee.name.charAt(0) }}
            </el-avatar>
            <div class="employee-info">
              <div class="employee-name">{{ employee.name }}</div>
              <div class="employee-dept">{{ employee.department }} | {{ employee.position }}</div>
            </div>
            <div class="employee-stats">
              <el-tag type="success">正常 {{ employee.stats.normal }}</el-tag>
              <el-tag type="warning">迟到 {{ employee.stats.late }}</el-tag>
              <el-tag type="danger">早退 {{ employee.stats.early }}</el-tag>
              <el-tag type="info">缺卡 {{ employee.stats.missing }}</el-tag>
            </div>
          </div>
          
          <el-timeline class="record-timeline">
            <el-timeline-item
              v-for="record in employee.records"
              :key="record.id"
              :timestamp="record.date"
              :type="getTimelineType(record.status)"
              :hollow="record.isToday"
              placement="top"
            >
              <div class="timeline-content">
                <div class="record-main">
                  <div class="clock-times">
                    <div class="clock-item" v-if="record.clockIn">
                      <el-icon><Sunrise /></el-icon>
                      <span class="clock-label">上班打卡</span>
                      <span class="clock-time" :class="{ 'text-warning': record.clockIn.isLate }">
                        {{ record.clockIn.time }}
                      </span>
                      <el-tag v-if="record.clockIn.isLate" type="warning" size="small">
                        迟到{{ record.clockIn.lateMinutes }}分钟
                      </el-tag>
                    </div>
                    <div class="clock-item" v-if="record.clockOut">
                      <el-icon><Sunset /></el-icon>
                      <span class="clock-label">下班打卡</span>
                      <span class="clock-time" :class="{ 'text-danger': record.clockOut.isEarly }">
                        {{ record.clockOut.time }}
                      </span>
                      <el-tag v-if="record.clockOut.isEarly" type="danger" size="small">
                        早退{{ record.clockOut.earlyMinutes }}分钟
                      </el-tag>
                    </div>
                    <div v-if="!record.clockIn && !record.clockOut" class="no-record">
                      <el-tag type="info">无打卡记录</el-tag>
                    </div>
                  </div>
                  <div class="record-extra">
                    <el-tag v-if="record.isWeekend" size="small">周末</el-tag>
                    <el-tag v-if="record.isHoliday" type="success" size="small">节假日</el-tag>
                    <el-tag v-if="record.hasLeave" type="info" size="small">{{ record.leaveType }}</el-tag>
                    <el-tag v-if="record.hasOvertime" type="warning" size="small">
                      加班{{ record.overtimeHours }}小时
                    </el-tag>
                  </div>
                </div>
                <div class="record-actions">
                  <el-button link type="primary" size="small" @click="handleViewDetail(record)">
                    详情
                  </el-button>
                  <el-button link type="primary" size="small" @click="handleEditRecord(record)">
                    补卡
                  </el-button>
                  <el-button link type="danger" size="small" @click="handleMarkAbnormal(record)">
                    标记异常
                  </el-button>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-else-if="viewType === 'table'" class="table-view">
        <el-table :data="tableData" stripe v-loading="loading">
          <el-table-column prop="employeeName" label="员工姓名" width="100" fixed  />
          <el-table-column prop="department" label="部门" width="120"  />
          <el-table-column prop="date" label="日期" width="100"  />
          <el-table-column prop="weekDay" label="星期" width="80">
            <template #default="{ row }">
              <el-tag v-if="row.isWeekend" type="warning" size="small">{{ row.weekDay }}</el-tag>
              <span v-else>{{ row.weekDay }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="scheduleTime" label="排班时间" width="150"  />
          <el-table-column prop="clockInTime" label="上班打卡" width="100">
            <template #default="{ row }">
              <span :class="{ 'text-warning': row.isLate }">
                {{ row.clockInTime || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="clockOutTime" label="下班打卡" width="100">
            <template #default="{ row }">
              <span :class="{ 'text-danger': row.isEarly }">
                {{ row.clockOutTime || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="workHours" label="工作时长" width="100"  />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="abnormal" label="异常标记" width="150">
            <template #default="{ row }">
              <el-tag v-if="row.abnormalType" type="danger" size="small">
                {{ row.abnormalType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" size="small" @click="handleViewDetail(row)">
                详情
              </el-button>
              <el-button link type="primary" size="small" @click="handleEditRecord(row)">
                补卡
              </el-button>
              <el-dropdown style="margin-left: 10px">
                <el-button link type="primary" size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleMarkAbnormal(row)">标记异常</el-dropdown-item>
                    <el-dropdown-item @click="handleApplyLeave(row)">申请请假</el-dropdown-item>
                    <el-dropdown-item @click="handleApplyOvertime(row)">申请加班</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="pageInfo.currentPage"
          v-model:page-size="pageInfo.pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="pageInfo.total"
          layout="total, sizes, prev, pager, next, jumper"
          style="margin-top: 20px"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>

      <!-- 日历视图 -->
      <div v-else-if="viewType === 'calendar'" class="calendar-view">
        <div class="calendar-header">
          <el-select v-model="selectedEmployee" placeholder="选择员工" style="width: 200px">
            <el-option
              v-for="emp in employeeList"
              :key="emp.id"
              :label="emp.name"
              :value="emp.id"
             />
          </el-select>
          <el-date-picker
            v-model="selectedMonth"
            type="month"
            placeholder="选择月份"
            @change="handleMonthChange"
           />
        </div>
        <el-calendar v-model="selectedDate">
          <template #date-cell="{ data }">
            <div class="calendar-cell" @click="handleCalendarClick(data)">
              <div class="calendar-date">{{ data.day.split('-')[2] }}</div>
              <div v-if="getCalendarRecord(data.day)" class="calendar-record">
                <div class="clock-status">
                  <el-icon v-if="getCalendarRecord(data.day).clockIn" color="#67C23A">
                    <CircleCheck />
                  </el-icon>
                  <el-icon v-else color="#F56C6C"><CircleClose /></el-icon>
                  <el-icon v-if="getCalendarRecord(data.day).clockOut" color="#67C23A">
                    <CircleCheck />
                  </el-icon>
                  <el-icon v-else color="#F56C6C"><CircleClose /></el-icon>
                </div>
                <el-tag
                  v-if="getCalendarRecord(data.day).status !== 'normal'"
                  :type="getStatusType(getCalendarRecord(data.day).status)"
                  size="small"
                >
                  {{ getStatusText(getCalendarRecord(data.day).status) }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialog"
      title="打卡详情"
      width="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ currentRecord.employeeName }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ currentRecord.department }}</el-descriptions-item>
        <el-descriptions-item label="日期">{{ currentRecord.date }}</el-descriptions-item>
        <el-descriptions-item label="排班">{{ currentRecord.scheduleTime }}</el-descriptions-item>
        <el-descriptions-item label="上班打卡">
          {{ currentRecord.clockInTime || '未打卡' }}
          <el-tag v-if="currentRecord.isLate" type="warning" size="small" style="margin-left: 10px">
            迟到{{ currentRecord.lateMinutes }}分钟
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="下班打卡">
          {{ currentRecord.clockOutTime || '未打卡' }}
          <el-tag v-if="currentRecord.isEarly" type="danger" size="small" style="margin-left: 10px">
            早退{{ currentRecord.earlyMinutes }}分钟
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="工作时长">{{ currentRecord.workHours }}</el-descriptions-item>
        <el-descriptions-item label="打卡地点">{{ currentRecord.location }}</el-descriptions-item>
        <el-descriptions-item label="打卡设备">{{ currentRecord.device }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ currentRecord.ipAddress }}</el-descriptions-item>
      </el-descriptions>
      
      <div class="clock-photos" v-if="currentRecord.photos && currentRecord.photos.length > 0">
        <h4>打卡照片</h4>
        <el-image v-for="(photo, index) in currentRecord.photos"
          :key="index"
          :lazy="true" :src="photo"
          :preview-src-list="currentRecord.photos"
          fit="cover"
          style="width: 100px; height: 100px; margin-right: 10px"
         />
      </div>
    </el-dialog>

    <!-- 补卡对话框 -->
    <el-dialog
      v-model="supplementDialog"
      title="补卡申请"
      width="500px"
    >
      <el-form ref="supplementFormRef" :model="supplementForm" :rules="supplementRules" label-width="100px">
        <el-form-item label="补卡类型" prop="type">
          <el-radio-group v-model="supplementForm.type">
            <el-radio label="clockIn">上班补卡</el-radio>
            <el-radio label="clockOut">下班补卡</el-radio>
            <el-radio label="both">全天补卡</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="补卡时间" prop="time" v-if="supplementForm.type !== 'both'">
          <el-time-picker
            v-model="supplementForm.time"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
           />
        </el-form-item>
        <el-form-item label="上班时间" prop="clockInTime" v-if="supplementForm.type === 'both'">
          <el-time-picker
            v-model="supplementForm.clockInTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择上班时间"
           />
        </el-form-item>
        <el-form-item label="下班时间" prop="clockOutTime" v-if="supplementForm.type === 'both'">
          <el-time-picker
            v-model="supplementForm.clockOutTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择下班时间"
           />
        </el-form-item>
        <el-form-item label="补卡原因" prop="reason">
          <el-input
            v-model="supplementForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入补卡原因"
            />
        </el-form-item>
        <el-form-item label="附件">
          <el-upload
            class="upload-demo"
            :limit="3"
            :on-exceed="handleExceed"
            :file-list="supplementForm.fileList"
          >
            <el-button size="small" type="primary">上传证明</el-button>
            <template #tip>
              <div class="el-upload__tip">支持jpg/png文件，不超过2MB</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="supplementDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSupplementSubmit">提交申请</el-button>
      </template>
    </el-dialog>

    <!-- 异常标记对话框 -->
    <el-dialog
      v-model="abnormalDialog"
      title="标记异常"
      width="500px"
    >
      <el-form ref="abnormalFormRef" :model="abnormalForm" label-width="100px">
        <el-form-item label="异常类型">
          <el-select v-model="abnormalForm.type" placeholder="请选择">
            <el-option label="设备故障" value="device"  />
            <el-option label="系统异常" value="system"  />
            <el-option label="位置异常" value="location"  />
            <el-option label="时间异常" value="time"  />
            <el-option label="其他异常" value="other"  />
          </el-select>
        </el-form-item>
        <el-form-item label="异常描述">
          <el-input
            v-model="abnormalForm.description"
            type="textarea"
            :rows="4"
            placeholder="请描述异常情况"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="abnormalDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAbnormalSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Sunrise,
  Sunset,
  CircleCheck,
  CircleClose,
  ArrowDown
} from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  employeeName: '',
  departmentId: null,
  dateRange: [],
  status: ''
})

// 视图类型
const viewType = ref('timeline')

// 分页信息
const pageInfo = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 加载状态
const loading = ref(false)

// 部门树
const departmentTree = ref([
  {
    id: '1',
    name: 'HrHr教学部门',
    children: [
      { id: '1-1', name: '计算机系' },
      { id: '1-2', name: '数学系' }
    ]
  },
  {
    id: '2',
    name: '行政部门',
    children: [
      { id: '2-1', name: '人事处' },
      { id: '2-2', name: '财务处' }
    ]
  }
])

// 时间轴数据
const timelineData = ref([
  {
    id: 1,
    name: '张三',
    avatar: '',
    department: '计算机系',
    position: '教师',
    stats: {
      normal: 18,
      late: 2,
      early: 1,
      missing: 1
    },
    records: [
      {
        id: 1,
        date: '2025-01-20 周一',
        isToday: true,
        status: 'normal',
        clockIn: { time: '08:25', isLate: false },
        clockOut: { time: '17:35', isEarly: false },
        isWeekend: false,
        isHoliday: false,
        hasLeave: false,
        hasOvertime: false
      },
      {
        id: 2,
        date: '2025-01-19 周日',
        isToday: false,
        status: 'weekend',
        clockIn: null,
        clockOut: null,
        isWeekend: true,
        isHoliday: false,
        hasLeave: false,
        hasOvertime: false
      },
      {
        id: 3,
        date: '2025-01-18 周六',
        isToday: false,
        status: 'overtime',
        clockIn: { time: '09:00', isLate: false },
        clockOut: { time: '18:00', isEarly: false },
        isWeekend: true,
        isHoliday: false,
        hasLeave: false,
        hasOvertime: true,
        overtimeHours: 8
      },
      {
        id: 4,
        date: '2025-01-17 周五',
        isToday: false,
        status: 'late',
        clockIn: { time: '08:45', isLate: true, lateMinutes: 15 },
        clockOut: { time: '17:30', isEarly: false },
        isWeekend: false,
        isHoliday: false,
        hasLeave: false,
        hasOvertime: false
      }
    ]
  }
])

// 表格数据
const tableData = ref([
  {
    id: 1,
    employeeName: '张三',
    department: '计算机系',
    date: '2025-01-20',
    weekDay: '周一',
    isWeekend: false,
    scheduleTime: '08:30-17:30',
    clockInTime: '08:25',
    clockOutTime: '17:35',
    workHours: '9小时10分',
    status: 'normal',
    isLate: false,
    isEarly: false,
    abnormalType: ''
  },
  {
    id: 2,
    employeeName: '李四',
    department: '数学系',
    date: '2025-01-20',
    weekDay: '周一',
    isWeekend: false,
    scheduleTime: '08:30-17:30',
    clockInTime: '08:45',
    clockOutTime: '17:20',
    workHours: '8小时35分',
    status: 'abnormal',
    isLate: true,
    isEarly: true,
    lateMinutes: 15,
    earlyMinutes: 10,
    abnormalType: '迟到早退'
  }
])

// 员工列表
const employeeList = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

// 日历相关
const selectedEmployee = ref(1)
const selectedMonth = ref(new Date())
const selectedDate = ref(new Date())

// 对话框
const detailDialog = ref(false)
const supplementDialog = ref(false)
const abnormalDialog = ref(false)

// 当前记录
const currentRecord = ref<unknown>({})

// 补卡表单
const supplementFormRef = ref()
const supplementForm = reactive({
  type: 'clockIn',
  time: '',
  clockInTime: '',
  clockOutTime: '',
  reason: '',
  fileList: []
})

const supplementRules = reactive({
  type: [{ required: true, message: '请选择补卡类型', trigger: 'change' }],
  time: [{ required: true, message: '请选择补卡时间', trigger: 'change' }],
  clockInTime: [{ required: true, message: '请选择上班时间', trigger: 'change' }],
  clockOutTime: [{ required: true, message: '请选择下班时间', trigger: 'change' }],
  reason: [{ required: true, message: '请输入补卡原因', trigger: 'blur' }]
})

// 异常表单
const abnormalFormRef = ref()
const abnormalForm = reactive({
  type: '',
  description: ''
})

// 获取时间轴类型
const getTimelineType = (status: string) => {
  const map: Record<string, string> = {
    normal: 'success',
    late: 'warning',
    early: 'danger',
    missing: 'info',
    abnormal: 'danger'
  }
  return map[status] || 'primary'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    normal: 'success',
    late: 'warning',
    early: 'danger',
    missing: 'info',
    abnormal: 'danger'
  }
  return map[status] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    normal: '正常',
    late: '迟到',
    early: '早退',
    missing: '缺卡',
    abnormal: '异常',
    weekend: '周末',
    holiday: '节假日',
    leave: '请假',
    overtime: '加班'
  }
  return map[status] || status
}

// 获取日历记录
const getCalendarRecord = (date: string) => {
  // 模拟数据
  const records: Record<string, unknown> = {
    '2025-01-20': { clockIn: true, clockOut: true, status: 'normal' },
    '2025-01-17': { clockIn: true, clockOut: true, status: 'late' },
    '2025-01-16': { clockIn: false, clockOut: false, status: 'missing' }
  }
  return records[date]
}

// 查询
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询成功')
  }, 500)
}

// 重置
const handleReset = () => {
  searchForm.employeeName = ''
  searchForm.departmentId = null
  searchForm.dateRange = []
  searchForm.status = ''
  handleSearch()
}

// 导出
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 查看详情
   
const handleViewDetail = (record: unknown) => {
  currentRecord.value = {
    ...record,
    employeeName: record.employeeName || '张三',
    department: record.department || '计算机系',
    date: record.date || '2025-01-20',
    scheduleTime: '08:30-17:30',
    location: '主校区行政楼',
    device: '考勤机-001',
    ipAddress: '*************',
    photos: []
  }
  detailDialog.value = true
}

// 补卡
   
const handleEditRecord = (record: unknown) => {
  currentRecord.value = record
  supplementDialog.value = true
}

// 标记异常
   
const handleMarkAbnormal = (record: unknown) => {
  currentRecord.value = record
  abnormalDialog.value = true
}

// 申请请假
   
const handleApplyLeave = (record: unknown) => {
  ElMessage.info('跳转到请假申请页面')
}

// 申请加班
   
const handleApplyOvertime = (record: unknown) => {
  ElMessage.info('跳转到加班申请页面')
}

// 日历点击
   
const handleCalendarClick = (data: unknown) => {
  }

// 月份变化
const handleMonthChange = () => {
  // 重新加载数据
}

// 文件超出限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传3个文件')
}

// 提交补卡
const handleSupplementSubmit = async () => {
  const valid = await supplementFormRef.value.validate()
  if (!valid) return
  
  ElMessage.success('补卡申请已提交')
  supplementDialog.value = false
}

// 提交异常标记
const handleAbnormalSubmit = () => {
  if (!abnormalForm.type) {
    ElMessage.warning('请选择异常类型')
    return
  }
  
  ElMessage.success('异常标记成功')
  abnormalDialog.value = false
}

// 分页
const handleSizeChange = (val: number) => {
  pageInfo.pageSize = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  pageInfo.currentPage = val
  handleSearch()
}
</script>

<style lang="scss" scoped>
.clock-in-record-management {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  // 时间轴视图
  .timeline-view {
    .employee-timeline {
      margin-bottom: 40px;
      padding: 20px;
      background-color: #f5f7fa;
      border-radius: 8px;
    }

    .employee-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .employee-info {
      flex: 1;
      margin-left: 15px;

      .employee-name {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 4px;
      }

      .employee-dept {
        font-size: 14px;
        color: #666;
      }
    }

    .employee-stats {
      display: flex;
      gap: 10px;
    }

    .record-timeline {
      margin-left: 30px;
    }

    .timeline-content {
      padding: 15px;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .record-main {
      margin-bottom: 10px;
    }

    .clock-times {
      display: flex;
      gap: 20px;
      margin-bottom: 10px;
    }

    .clock-item {
      display: flex;
      align-items: center;
      gap: 5px;

      .el-icon {
        font-size: 16px;
        color: #409EFF;
      }

      .clock-label {
        color: #666;
        font-size: 14px;
      }

      .clock-time {
        font-weight: bold;
        font-size: 16px;

        &.text-warning {
          color: #E6A23C;
        }

        &.text-danger {
          color: #F56C6C;
        }
      }
    }

    .no-record {
      padding: 10px;
      text-align: center;
    }

    .record-extra {
      display: flex;
      gap: 10px;
    }

    .record-actions {
      display: flex;
      gap: 10px;
      border-top: 1px solid #ebeef5;
      padding-top: 10px;
    }
  }

  // 日历视图
  .calendar-view {
    .calendar-header {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }

    :deep(.el-calendar-table) {
      .el-calendar-day {
        height: 80px;
      }
    }

    .calendar-cell {
      height: 100%;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }
    }

    .calendar-date {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .calendar-record {
      .clock-status {
        display: flex;
        gap: 5px;
        margin-bottom: 5px;
        justify-content: center;
      }
    }
  }

  // 详情对话框
  .clock-photos {
    margin-top: 20px;

    h4 {
      margin-bottom: 10px;
    }
  }
}
</style>