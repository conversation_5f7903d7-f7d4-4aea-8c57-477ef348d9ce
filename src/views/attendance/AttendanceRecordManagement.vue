<template>
  <div class="attendance-record-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>考勤记录管理</h2>
      <p>查询、补录和管理教职工考勤记录</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、员工编号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.attendanceType" placeholder="考勤类型" clearable>
              <el-option
                v-for="item in attendanceOptions.attendanceType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.dataSource" placeholder="数据来源" clearable>
              <el-option
                v-for="item in attendanceOptions.dataSource"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              补录考勤
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">总记录数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon present">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.present }}</div>
              <div class="stats-label">出勤记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon leave">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.leave }}</div>
              <div class="stats-label">请假记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon abnormal">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.abnormal }}</div>
              <div class="stats-label">异常记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能切换标签 -->
    <el-card class="tab-card" shadow="never">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="考勤记录列表" name="list">
          <!-- 考勤记录表格 -->
          <div class="table-header">
            <span class="table-title">考勤记录列表</span>
            <div class="table-actions">
              <el-button size="small" @click="handleExport">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
              <el-button size="small" @click="handleBatchDelete" :disabled="selectedRows.length === 0">
                <el-icon><Delete /></el-icon>
                批量删除
              </el-button>
            </div>
          </div>

          <el-table
            v-loading="loading"
            :data="tableData"
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"  />
            <el-table-column prop="employeeCode" label="员工编号" width="120"  />
            <el-table-column prop="employeeName" label="姓名" width="100"  />
            <el-table-column prop="organizationName" label="所属组织" width="150" show-overflow-tooltip  />
            <el-table-column prop="attendanceDate" label="考勤日期" width="120"  />
            <el-table-column prop="attendanceTypeName" label="考勤类型" width="100">
              <template #default="scope">
                <el-tag :type="getAttendanceTypeTagType(scope.row.attendanceType)">
                  {{ scope.row.attendanceTypeName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="checkInTime" label="签到时间" width="100"  />
            <el-table-column prop="checkOutTime" label="签退时间" width="100"  />
            <el-table-column prop="workHours" label="工作时长" width="100">
              <template #default="scope">
                {{ scope.row.workHours ? scope.row.workHours + 'h' : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="dataSourceName" label="数据来源" width="100">
              <template #default="scope">
                <el-tag size="small" :type="getDataSourceTagType(scope.row.dataSource)">
                  {{ scope.row.dataSourceName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注" width="150" show-overflow-tooltip  />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleView(scope.row)">
                  查看
                </el-button>
                <el-button 
                  v-if="canEdit(scope.row)" 
                  size="small" 
                  type="primary" 
                  link 
                  @click="handleEdit(scope.row)"
                >
                  编辑
                </el-button>
                <el-button 
                  v-if="canDelete(scope.row)" 
                  size="small" 
                  type="danger" 
                  link 
                  @click="handleDelete(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
             />
          </div>
        </el-tab-pane>

        <el-tab-pane label="考勤日历" name="calendar">
          <!-- 考勤日历组件 -->
          <AttendanceCalendar
            :employee-id="calendarEmployeeId"
            :month="calendarMonth"
            @date-click="handleDateClick"
            @employee-change="handleEmployeeChange"
            @month-change="handleMonthChange"
          />
        </el-tab-pane>

        <el-tab-pane label="考勤统计" name="statistics">
          <!-- 考勤统计图表 -->
          <AttendanceStatistics
            :organization-id="statisticsOrganizationId"
            :date-range="statisticsDateRange"
            @organization-change="handleOrganizationChange"
            @date-range-change="handleDateRangeChange"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 考勤记录详情/编辑对话框 -->
    <AttendanceRecordDialog
      v-model:visible="dialogVisible"
      :attendance-record="currentAttendanceRecord"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Document,
  Check,
  Calendar,
  Warning,
  Download,
  Delete
} from '@element-plus/icons-vue'
import { 
  attendanceApi, 
  attendanceOptions, 
  type AttendanceRecord, 
  type AttendanceRecordQueryRequest 
} from '@/api/attendance'

// 响应式数据
const loading = ref(false)
const tableData = ref<AttendanceRecord[]>([])
const selectedRows = ref<AttendanceRecord[]>([])
const activeTab = ref('list')

// 搜索表单
const searchForm = reactive<AttendanceRecordQueryRequest & { dateRange?: string[] }>({
  page: 0,
  size: 20,
  keyword: '',
  attendanceType: undefined,
  dataSource: undefined,
  dateRange: undefined
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  present: 0,
  leave: 0,
  abnormal: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentAttendanceRecord = ref<AttendanceRecord | null>(null)

// 日历相关
const calendarEmployeeId = ref<number>()
const calendarMonth = ref(new Date().toISOString().slice(0, 7))

// 统计相关
const statisticsOrganizationId = ref<number>()
const statisticsDateRange = ref<string[]>([])

// 获取考勤记录列表
const fetchAttendanceRecords = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page - 1,
      size: pagination.size
    }
    
    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.attendanceDateStart = searchForm.dateRange[0]
      params.attendanceDateEnd = searchForm.dateRange[1]
    }
    delete params.dateRange
    
    const result = await attendanceApi.queryAttendanceRecords(params)
    tableData.value = result.content
    pagination.total = result.totalElements
    
    // 更新统计信息
    updateStats()
  } catch (__error) {
    ElMessage.error('获取考勤记录列表失败')
  } finally {
    loading.value = false
  }
}

// 更新统计信息
const updateStats = () => {
  stats.total = tableData.value.length
  stats.present = tableData.value.filter(item => item.attendanceType === 'PRESENT').length
  stats.leave = tableData.value.filter(item => item.attendanceType === 'LEAVE').length
  stats.abnormal = tableData.value.filter(item => 
    ['ABSENT', 'LATE', 'EARLY_LEAVE'].includes(item.attendanceType)
  ).length
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchAttendanceRecords()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    attendanceType: undefined,
    dataSource: undefined,
    dateRange: undefined
  })
  pagination.page = 1
  fetchAttendanceRecords()
}

// 新增考勤记录
const handleAdd = () => {
  currentAttendanceRecord.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看考勤记录
const handleView = (record: AttendanceRecord) => {
  currentAttendanceRecord.value = record
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑考勤记录
const handleEdit = (record: AttendanceRecord) => {
  currentAttendanceRecord.value = record
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 删除考勤记录
const handleDelete = async (record: AttendanceRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${record.employeeName}" 在 "${record.attendanceDate}" 的考勤记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await attendanceApi.deleteAttendanceRecord(record.id!)
    ElMessage.success('删除成功')
    fetchAttendanceRecords()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 权限判断
const canEdit = (record: AttendanceRecord) => {
  return record.dataSource === 'MANUAL_ENTRY'
}

const canDelete = (record: AttendanceRecord) => {
  return record.dataSource === 'MANUAL_ENTRY'
}

// 表格选择变化
const handleSelectionChange = (selection: AttendanceRecord[]) => {
  selectedRows.value = selection
}

// 批量删除
const handleBatchDelete = () => {
  ElMessage.info('批量删除功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
}

// 日历相关事件
const handleDateClick = (date: string) => {
  // 点击日期时的处理逻辑
  }

const handleEmployeeChange = (employeeId: number) => {
  calendarEmployeeId.value = employeeId
}

const handleMonthChange = (month: string) => {
  calendarMonth.value = month
}

// 统计相关事件
const handleOrganizationChange = (organizationId: number) => {
  statisticsOrganizationId.value = organizationId
}

const handleDateRangeChange = (dateRange: string[]) => {
  statisticsDateRange.value = dateRange
}

// 分页相关
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchAttendanceRecords()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchAttendanceRecords()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchAttendanceRecords()
}

// 获取考勤类型标签类型
const getAttendanceTypeTagType = (type: string) => {
  switch (type) {
    case 'PRESENT':
      return 'success'
    case 'LEAVE':
      return 'warning'
    case 'ABSENT':
      return 'danger'
    case 'LATE':
    case 'EARLY_LEAVE':
      return 'warning'
    default:
      return ''
  }
}

// 获取数据来源标签类型
const getDataSourceTagType = (source: string) => {
  switch (source) {
    case 'ATTENDANCE_MACHINE':
      return 'primary'
    case 'LEAVE_SYSTEM':
      return 'success'
    case 'MANUAL_ENTRY':
      return 'warning'
    case 'MOBILE_APP':
      return 'info'
    default:
      return ''
  }
}

// 初始化
onMounted(() => {
  fetchAttendanceRecords()
})
</script>

<style scoped>
.attendance-record-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.present {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.leave {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-icon.abnormal {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.tab-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
