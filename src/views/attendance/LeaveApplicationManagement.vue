<template>
  <div class="leave-application-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>请假管理</h2>
      <p>管理教职工请假申请、审批和销假</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、员工编号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.leaveType" placeholder="请假类型" clearable>
              <el-option
                v-for="item in attendanceOptions.leaveType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.approvalStatus" placeholder="审批状态" clearable>
              <el-option
                v-for="item in attendanceOptions.approvalStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              申请请假
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">总申请数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待审批</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon approved">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.approved }}</div>
              <div class="stats-label">已批准</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon rejected">
              <el-icon><Close /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.rejected }}</div>
              <div class="stats-label">已驳回</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 请假申请列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">请假申请列表</span>
        <div class="table-actions">
          <el-button size="small" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button size="small" @click="handleBatchApprove" :disabled="selectedRows.length === 0">
            <el-icon><Check /></el-icon>
            批量审批
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeCode" label="员工编号" width="120"  />
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="organizationName" label="所属组织" width="150" show-overflow-tooltip  />
        <el-table-column prop="leaveTypeName" label="请假类型" width="100"  />
        <el-table-column prop="startDate" label="开始日期" width="100"  />
        <el-table-column prop="endDate" label="结束日期" width="100"  />
        <el-table-column prop="leaveDays" label="请假天数" width="80">
          <template #default="scope">
            {{ scope.row.leaveDays }}天
          </template>
        </el-table-column>
        <el-table-column prop="leaveReason" label="请假事由" width="200" show-overflow-tooltip  />
        <el-table-column prop="approvalStatusName" label="审批状态" width="120">
          <template #default="scope">
            <el-tag
              :type="getStatusTagType(scope.row.approvalStatus)"
              size="small"
            >
              {{ scope.row.approvalStatusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="submissionTime" label="提交时间" width="150">
          <template #default="scope">
            {{ formatDateTime(scope.row.submissionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button 
              v-if="canEdit(scope.row)" 
              size="small" 
              type="primary" 
              link 
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="canApprove(scope.row)" 
              size="small" 
              type="success" 
              link 
              @click="handleApprove(scope.row)"
            >
              审批
            </el-button>
            <el-button 
              v-if="canCancel(scope.row)" 
              size="small" 
              type="warning" 
              link 
              @click="handleCancel(scope.row)"
            >
              销假
            </el-button>
            <el-button 
              v-if="canDelete(scope.row)" 
              size="small" 
              type="danger" 
              link 
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 请假申请详情/编辑对话框 -->
    <LeaveApplicationDialog
      v-model:visible="dialogVisible"
      :leave-application="currentLeaveApplication"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 审批对话框 -->
    <ApprovalDialog
      v-model:visible="approvalDialogVisible"
      :leave-application="currentLeaveApplication"
      @success="handleApprovalSuccess"
    />

    <!-- 销假对话框 -->
    <CancellationDialog
      v-model:visible="cancellationDialogVisible"
      :leave-application="currentLeaveApplication"
      @success="handleCancellationSuccess"
    />
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Document,
  Clock,
  Check,
  Close,
  Download
} from '@element-plus/icons-vue'
import { 
  attendanceApi, 
  attendanceOptions, 
  type LeaveApplication, 
  type LeaveApplicationQueryRequest 
} from '@/api/attendance'
import { formatDateTime } from '@/utils/date'
import LeaveApplicationDialog from './components/LeaveApplicationDialog.vue'
import HrApprovalDialog from './components/HrApprovalDialog.vue'
import CancellationDialog from './components/CancellationDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<LeaveApplication[]>([])
const selectedRows = ref<LeaveApplication[]>([])

// 搜索表单
const searchForm = reactive<LeaveApplicationQueryRequest & { dateRange?: string[] }>({
  page: 0,
  size: 20,
  keyword: '',
  leaveType: undefined,
  approvalStatus: undefined,
  dateRange: undefined
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0
})

// 对话框相关
const dialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const cancellationDialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentLeaveApplication = ref<LeaveApplication | null>(null)

// 获取请假申请列表
const fetchLeaveApplications = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page - 1,
      size: pagination.size
    }
    
    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDateStart = searchForm.dateRange[0]
      params.startDateEnd = searchForm.dateRange[1]
    }
    delete params.dateRange
    
    const result = await attendanceApi.queryLeaveApplications(params)
    tableData.value = result.content
    pagination.total = result.totalElements
  } catch (__error) {
    ElMessage.error('获取请假申请列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    // 这里应该调用统计API，暂时模拟数据
    stats.total = tableData.value.length
    stats.pending = tableData.value.filter(item => 
      ['PENDING_DEPARTMENT', 'PENDING_HR', 'PENDING_LEADERSHIP'].includes(item.approvalStatus)
    ).length
    stats.approved = tableData.value.filter(item => item.approvalStatus === 'APPROVED').length
    stats.rejected = tableData.value.filter(item => item.approvalStatus === 'REJECTED').length
  } catch (__error) {
    }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchLeaveApplications()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    leaveType: undefined,
    approvalStatus: undefined,
    dateRange: undefined
  })
  pagination.page = 1
  fetchLeaveApplications()
}

// 新增请假申请
const handleAdd = () => {
  currentLeaveApplication.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看请假申请
const handleView = (leaveApplication: LeaveApplication) => {
  currentLeaveApplication.value = leaveApplication
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑请假申请
const handleEdit = (leaveApplication: LeaveApplication) => {
  currentLeaveApplication.value = leaveApplication
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 审批请假申请
const handleApprove = (leaveApplication: LeaveApplication) => {
  currentLeaveApplication.value = leaveApplication
  approvalDialogVisible.value = true
}

// 销假
const handleCancel = (leaveApplication: LeaveApplication) => {
  currentLeaveApplication.value = leaveApplication
  cancellationDialogVisible.value = true
}

// 删除请假申请
const handleDelete = async (leaveApplication: LeaveApplication) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${leaveApplication.employeeName}" 的请假申请吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await attendanceApi.deleteLeaveApplication(leaveApplication.id!)
    ElMessage.success('删除成功')
    fetchLeaveApplications()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 权限判断
const canEdit = (leaveApplication: LeaveApplication) => {
  return ['PENDING_DEPARTMENT'].includes(leaveApplication.approvalStatus)
}

const canApprove = (leaveApplication: LeaveApplication) => {
  return ['PENDING_DEPARTMENT', 'PENDING_HR', 'PENDING_LEADERSHIP'].includes(leaveApplication.approvalStatus)
}

const canCancel = (leaveApplication: LeaveApplication) => {
  return leaveApplication.approvalStatus === 'APPROVED' && !leaveApplication.isCancelled
}

const canDelete = (leaveApplication: LeaveApplication) => {
  return ['PENDING_DEPARTMENT', 'REJECTED'].includes(leaveApplication.approvalStatus)
}

// 表格选择变化
const handleSelectionChange = (selection: LeaveApplication[]) => {
  selectedRows.value = selection
}

// 批量审批
const handleBatchApprove = () => {
  ElMessage.info('批量审批功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchLeaveApplications()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchLeaveApplications()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchLeaveApplications()
  fetchStats()
}

const handleApprovalSuccess = () => {
  fetchLeaveApplications()
  fetchStats()
}

const handleCancellationSuccess = () => {
  fetchLeaveApplications()
  fetchStats()
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING_DEPARTMENT':
    case 'PENDING_HR':
    case 'PENDING_LEADERSHIP':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}

// 初始化
onMounted(() => {
  fetchLeaveApplications()
})
</script>

<style scoped>
.leave-application-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.rejected {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
