<template>
  <div class="attendance-dashboard">
    <!-- 头部统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :xs="24" :sm="12" :md="6" v-for="stat in statCards" :key="stat.key">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon :size="30">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">
                <hr-animated-number :value="stat.value" />
                <span class="stat-unit">{{ stat.unit }}</span>
              </div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-trend" :class="stat.trend > 0 ? 'up' : 'down'">
                <el-icon><TrendCharts /></el-icon>
                {{ Math.abs(stat.trend) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时监控区 -->
    <el-row :gutter="20" class="monitor-section">
      <!-- 实时打卡动态 -->
      <el-col :xs="24" :md="12" :lg="8">
        <el-card shadow="never" class="realtime-card">
          <template #header>
            <div class="card-header">
              <span>实时打卡动态</span>
              <el-tag type="success" effect="plain">
                <el-icon class="is-loading"><Loading /></el-icon>
                实时更新
              </el-tag>
            </div>
          </template>
          <div class="realtime-list" ref="realtimeListRef">
            <TransitionGroup name="list" tag="div">
              <div
                v-for="record in realtimeRecords"
                :key="record.id"
                class="realtime-item"
              >
                <div class="item-time">{{ record.time }}</div>
                <div class="item-content">
                  <el-avatar :src="record.avatar" :size="32">
                    {{ record.name.charAt(0) }}
                  </el-avatar>
                  <div class="item-info">
                    <div class="item-name">{{ record.name }}</div>
                    <div class="item-action">
                      <el-tag :type="getActionType(record.action)" size="small">
                        {{ record.action }}
                      </el-tag>
                      <span class="item-location">{{ record.location }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </TransitionGroup>
          </div>
        </el-card>
      </el-col>

      <!-- 部门出勤率 -->
      <el-col :xs="24" :md="12" :lg="8">
        <el-card shadow="never" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>部门出勤率</span>
              <el-button size="small" @click="refreshDeptChart">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </template>
          <div ref="deptChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 异常情况分布 -->
      <el-col :xs="24" :md="12" :lg="8">
        <el-card shadow="never" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>今日异常分布</span>
              <el-select v-model="abnormalType" <!-- eslint-disable-line vue/no-mutating-props --> size="small" style="width: 100px">
                <el-option label="全部" value="all"  />
                <el-option label="迟到" value="late"  />
                <el-option label="早退" value="early"  />
                <el-option label="缺卡" value="missing"  />
              </el-select>
            </div>
          </template>
          <div ref="abnormalChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据区 -->
    <el-row :gutter="20" class="detail-section">
      <!-- 考勤趋势图 -->
      <el-col :xs="24" :lg="16">
        <el-card shadow="never" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>考勤趋势分析</span>
              <el-radio-group v-model="trendPeriod" <!-- eslint-disable-line vue/no-mutating-props --> size="small">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container-large"></div>
        </el-card>
      </el-col>

      <!-- 排行榜 -->
      <el-col :xs="24" :lg="8">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>{{ rankTitle }}</span>
              <el-dropdown @command="handleRankChange">
                <el-button size="small" text>
                  {{ rankType }} <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="punctual">准时榜</el-dropdown-item>
                    <el-dropdown-item command="early">早到榜</el-dropdown-item>
                    <el-dropdown-item command="overtime">加班榜</el-dropdown-item>
                    <el-dropdown-item command="late">迟到榜</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <div class="rank-list">
            <div
              v-for="(item, index) in rankList"
              :key="item.id"
              class="rank-item"
            >
              <div class="rank-index" :class="{ top: index < 3 }">
                {{ index + 1 }}
              </div>
              <el-avatar :src="item.avatar" :size="32">
                {{ item.name.charAt(0) }}
              </el-avatar>
              <div class="rank-info">
                <div class="rank-name">{{ item.name }}</div>
                <div class="rank-dept">{{ item.department }}</div>
              </div>
              <div class="rank-value">{{ item.value }}{{ item.unit }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 预警信息 -->
    <el-card shadow="never" class="alert-card" v-if="alertList.length > 0">
      <template #header>
        <div class="card-header">
          <span>
            <el-icon><Warning /></el-icon>
            考勤预警
          </span>
          <el-button size="small" @click="handleAllAlerts">
            处理全部
          </el-button>
        </div>
      </template>
      <el-alert
        v-for="alert in alertList"
        :key="alert.id"
        :title="alert.title"
        :type="alert.type"
        :description="alert.description"
        show-icon
        closable
        @close="handleAlertClose(alert)"
        style="margin-bottom: 10px"
      >
        <template #default>
          <div class="alert-actions">
            <el-button size="small" @click="handleAlertDetail(alert)">
              查看详情
            </el-button>
            <el-button size="small" type="primary" @click="handleAlertProcess(alert)">
              立即处理
            </el-button>
          </div>
        </template>
      </el-alert>
    </el-card>

    <!-- WebSocket连接状态 -->
    <div class="ws-status" :class="wsStatus">
      <el-icon v-if="wsStatus === 'connected'"><Connection /></el-icon>
      <el-icon v-else-if="wsStatus === 'connecting'" class="is-loading"><Loading /></el-icon>
      <el-icon v-else><Close /></el-icon>
      <span>{{ wsStatusText }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  User,
  Clock,
  Warning,
  TrendCharts,
  Loading,
  Refresh,
  ArrowDown,
  Connection,
  Close
} from '@element-plus/icons-vue'
import { attendanceApi } from '@/api/attendance'
import dayjs from 'dayjs'

// 图表实例
let deptChart: echarts.ECharts | null = null
let abnormalChart: echarts.ECharts | null = null
let trendChart: echarts.ECharts | null = null

// DOM引用
const deptChartRef = ref<HTMLElement>()
const abnormalChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()
const realtimeListRef = ref<HTMLElement>()

// WebSocket相关
const ws: WebSocket | null = null
const wsStatus = ref('disconnected')
const wsStatusText = computed(() => {
  const map: Record<string, string> = {
    connected: '已连接',
    connecting: '连接中',
    disconnected: '已断开'
  }
  return map[wsStatus.value]
})

// 统计卡片数据
const statCards = ref([
  {
    key: 'total',
    label: '应到人数',
    value: 856,
    unit: '人',
    icon: 'User',
    color: '#409EFF',
    trend: 2.5
  },
  {
    key: 'present',
    label: '实到人数',
    value: 823,
    unit: '人',
    icon: 'CircleCheck',
    color: '#67C23A',
    trend: 1.2
  },
  {
    key: 'late',
    label: '迟到人数',
    value: 12,
    unit: '人',
    icon: 'Clock',
    color: '#E6A23C',
    trend: -15.3
  },
  {
    key: 'absence',
    label: '缺勤人数',
    value: 5,
    unit: '人',
    icon: 'Warning',
    color: '#F56C6C',
    trend: -25.0
  }
])

// 实时打卡记录
const realtimeRecords = ref([
  {
    id: 1,
    time: '08:58:32',
    name: 'HrHr张三',
    avatar: '',
    action: '上班打卡',
    location: '行政楼1F'
  },
  {
    id: 2,
    time: '08:55:12',
    name: '李四',
    avatar: '',
    action: '上班打卡',
    location: '教学楼3F'
  }
])

// 异常类型
const abnormalType = ref('all')

// 趋势周期
const trendPeriod = ref('day')

// 排行榜
const rankType = ref('准时榜')
const rankTitle = computed(() => `本月${rankType.value}`)
const rankList = ref([
  {
    id: 1,
    name: '王小明',
    avatar: '',
    department: '计算机系',
    value: 22,
    unit: '天'
  },
  {
    id: 2,
    name: '李晓红',
    avatar: '',
    department: '数学系',
    value: 21,
    unit: '天'
  },
  {
    id: 3,
    name: '张建国',
    avatar: '',
    department: '物理系',
    value: 20,
    unit: '天'
  }
])

// 预警列表
const alertList = ref([
  {
    id: 1,
    type: 'warning',
    title: '连续迟到预警',
    description: '张三本周已连续迟到3天，请关注'
  },
  {
    id: 2,
    type: 'error',
    title: '异常缺勤预警',
    description: '李四今日未打卡且未请假，请及时联系确认'
  }
])

// 动画数字组件
const AnimatedNumber = {
  props: {
    value: {
      type: Number,
      default: 0
    }
  },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setup(props: unknown) {
    const displayValue = ref(0)
    let animationId: number

    const animate = () => {
      const step = (props.value - displayValue.value) / 10
      if (Math.abs(step) > 0.1) {
        displayValue.value += step
        animationId = requestAnimationFrame(animate)
      } else {
        displayValue.value = /* eslint-disable-line vue/no-mutating-props */ props.value
      }
    }

    watch(() => props.value, () => {
      cancelAnimationFrame(animationId)
      animate()
    }, { immediate: true })

    onUnmounted(() => {
      cancelAnimationFrame(animationId)
    })

    return () => Math.round(displayValue.value)
  }
}

// 初始化WebSocket
const initWebSocket = () => {
  wsStatus.value = /* eslint-disable-line vue/no-mutating-props */ 'connecting'
  
  // 模拟WebSocket连接
  setTimeout(() => {
    wsStatus.value = /* eslint-disable-line vue/no-mutating-props */ 'connected'
    ElMessage.success('实时数据连接成功')
    
    // 模拟实时数据推送
    setInterval(() => {
      addRealtimeRecord()
      updateStatCards()
    }, 5000)
  }, 1000)
}

// 添加实时记录
const addRealtimeRecord = () => {
  const names = ['王五', '赵六', '钱七', '孙八']
  const actions = ['上班打卡', '下班打卡', '外出', '返回']
  const locations = ['行政楼1F', '教学楼2F', '实验楼3F', '图书馆']
  
  const now = new Date()
  const record = {
    id: Date.now(),
    time: now.toLocaleTimeString('zh-CN'),
    name: names[Math.floor(Math.random() * names.length)],
    avatar: '',
    action: actions[Math.floor(Math.random() * actions.length)],
    location: locations[Math.floor(Math.random() * locations.length)]
  }
  
  realtimeRecords.value.unshift(record)
  if (realtimeRecords.value.length > 10) {
    realtimeRecords.value.pop()
  }
  
  // 滚动到顶部
  nextTick(() => {
    if (realtimeListRef.value) {
      realtimeListRef.value.scrollTop = 0
    }
  })
}

// 更新统计卡片
const updateStatCards = () => {
  statCards.value.forEach(card => {
    const change = (Math.random() - 0.5) * 5
    card.value = /* eslint-disable-line vue/no-mutating-props */ Math.max(0, Math.round(card.value + change))
    card.trend = parseFloat((card.trend + change).toFixed(1))
  })
}

// 初始化部门出勤率图表
const initDeptChart = () => {
  if (!deptChartRef.value) return
  
  deptChart = echarts.init(deptChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    yAxis: {
      type: 'category',
      data: ['计算机系', '数学系', '物理系', '化学系', '生物系']
    },
    series: [
      {
        name: '出勤率',
        type: 'bar',
        data: [95.5, 92.3, 96.8, 94.2, 91.5],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%'
        }
      }
    ]
  }
  deptChart.setOption(option)
}

// 初始化异常分布图表
const initAbnormalChart = () => {
  if (!abnormalChartRef.value) return
  
  abnormalChart = echarts.init(abnormalChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      bottom: '5%',
      left: 'center'
    },
    series: [
      {
        name: '异常类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 12, name: '迟到', itemStyle: { color: '#E6A23C' } },
          { value: 8, name: '早退', itemStyle: { color: '#F56C6C' } },
          { value: 5, name: '缺卡', itemStyle: { color: '#909399' } },
          { value: 3, name: '旷工', itemStyle: { color: '#303133' } }
        ]
      }
    ]
  }
  abnormalChart.setOption(option)
}

// 初始化趋势图表
const initTrendChart = async () => {
  if (!trendChartRef.value) return
  
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value)
  }
  
  try {
    // 显示加载动画
    trendChart.showLoading()
    
    // 调用API获取趋势数据
    const params = {
      period: trendPeriod.value as 'day' | 'week' | 'month',
      startDate: dayjs().subtract(trendPeriod.value = /* eslint-disable-line vue/no-mutating-props */== 'day' ? 7 : trendPeriod.value = /* eslint-disable-line vue/no-mutating-props */== 'week' ? 4 : 3, trendPeriod.value).format('YYYY-MM-DD'),
      endDate: dayjs().format('YYYY-MM-DD')
    }
    
    const {data: _data} =  await attendanceApi.getAttendanceTrends(params)
    
    // 更新图表配置
    const option 

  // 统计卡片
  .stat-cards {
    margin-bottom: 20px;

    .stat-card {
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
      }

      .stat-content {
        display: flex;
        align-items: center;
        padding: 10px 0;

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          margin-right: 15px;
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;

            .stat-unit {
              font-size: 14px;
              color: #909399;
              margin-left: 5px;
            }
          }

          .stat-label {
            font-size: 14px;
            color: #606266;
          }

          .stat-trend {
            display: inline-flex;
            align-items: center;
            font-size: 12px;
            margin-top: 5px;

            &.up {
              color: #67C23A;
            }

            &.down {
              color: #F56C6C;
            }

            .el-icon {
              margin-right: 2px;
            }
          }
        }
      }
    }
  }

  // 监控区
  .monitor-section {
    margin-bottom: 20px;
  }

  // 详细数据区
  .detail-section {
    margin-bottom: 20px;
  }

  // 卡片头部
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  // 实时打卡列表
  .realtime-list {
    height: 400px;
    overflow-y: auto;

    .realtime-item {
      padding: 15px 0;
      border-bottom: 1px solid #EBEEF5;
      
      &:last-child {
        border-bottom: none;
      }

      .item-time {
        font-size: 12px;
        color: #909399;
        margin-bottom: 5px;
      }

      .item-content {
        display: flex;
        align-items: center;

        .el-avatar {
          margin-right: 10px;
        }

        .item-info {
          flex: 1;

          .item-name {
            font-weight: bold;
            margin-bottom: 5px;
          }

          .item-action {
            display: flex;
            align-items: center;
            gap: 10px;

            .item-location {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }
  }

  // 图表容器
  .chart-container {
    height: 300px;
  }

  .chart-container-large {
    height: 400px;
  }

  // 排行榜
  .rank-list {
    .rank-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #EBEEF5;

      &:last-child {
        border-bottom: none;
      }

      .rank-index {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
        border-radius: 50%;
        background-color: #F2F6FC;

        &.top {
          background-color: #FDF6EC;
          color: #E6A23C;
        }
      }

      .el-avatar {
        margin-right: 10px;
      }

      .rank-info {
        flex: 1;

        .rank-name {
          font-weight: bold;
          margin-bottom: 2px;
        }

        .rank-dept {
          font-size: 12px;
          color: #909399;
        }
      }

      .rank-value {
        font-weight: bold;
        color: #409EFF;
      }
    }
  }

  // 预警卡片
  .alert-card {
    margin-bottom: 20px;

    .alert-actions {
      margin-top: 10px;
      display: flex;
      gap: 10px;
    }
  }

  // WebSocket状态
  .ws-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 8px 16px;
    background-color: white;
    border-radius: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;

    &.connected {
      color: #67C23A;
    }

    &.connecting {
      color: #E6A23C;
    }

    &.disconnected {
      color: #F56C6C;
    }
  }
}

// 列表动画
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// 响应式
@media (max-width: 768px) {
  .attendance-dashboard {
    padding: 10px;

    .stat-card {
      margin-bottom: 10px;
    }
  }
}
</style>