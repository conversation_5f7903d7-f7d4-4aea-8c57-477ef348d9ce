<template>
  <div class="attendance-report-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>考勤报表管理</h2>
      <p>生成、查看和导出各类考勤报表</p>
    </div>

    <!-- 报表生成区域 -->
    <el-card class="report-generator" shadow="never">
      <template #header>
        <span>报表生成</span>
      </template>
      <el-form ref="reportFormRef" :model="reportForm" :rules="reportRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="报表类型" prop="reportType">
              <el-select v-model="reportForm.reportType" placeholder="请选择报表类型" style="width: 100%">
                <el-option
                  v-for="item in attendanceOptions.reportType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="统计范围" prop="organizationId">
              <el-tree-select
                v-model="reportForm.organizationId"
                :data="organizationTree"
                :props="{ label: 'name', value: 'id' }"
                placeholder="请选择组织范围"
                check-strictly
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="统计周期" prop="dateRange">
              <el-date-picker
                v-model="reportForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="handleGenerateReport" :loading="generateLoading">
                <el-icon><Document /></el-icon>
                生成报表
              </el-button>
              <el-button @click="handlePreviewReport" :disabled="!reportData">
                <el-icon><View /></el-icon>
                预览报表
              </el-button>
              <el-button @click="handleExportExcel" :disabled="!reportData">
                <el-icon><Download /></el-icon>
                导出Excel
              </el-button>
              <el-button @click="handleExportPDF" :disabled="!reportData">
                <el-icon><Download /></el-icon>
                导出PDF
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 报表预览区域 -->
    <el-card v-if="reportData" class="report-preview" shadow="never">
      <template #header>
        <div class="report-header">
          <span>{{ reportTitle }}</span>
          <div class="report-actions">
            <el-button size="small" @click="handleRefreshReport">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" @click="handleFullscreen">
              <el-icon><FullScreen /></el-icon>
              全屏
            </el-button>
          </div>
        </div>
      </template>

      <!-- 报表统计概览 -->
      <div class="report-summary">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-number">{{ reportSummary.totalEmployees }}</div>
              <div class="summary-label">统计人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-number">{{ reportSummary.totalWorkDays }}</div>
              <div class="summary-label">应出勤天数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-number">{{ reportSummary.totalPresentDays }}</div>
              <div class="summary-label">实际出勤天数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-number">{{ reportSummary.averageAttendanceRate }}%</div>
              <div class="summary-label">平均出勤率</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 报表内容切换 -->
      <el-tabs v-model="activeReportTab" class="report-tabs">
        <el-tab-pane label="数据表格" name="table">
          <el-table
            :data="reportData"
            stripe
            border
            style="width: 100%"
            max-height="500"
          >
            <el-table-column prop="employeeCode" label="员工编号" width="120" fixed="left"  />
            <el-table-column prop="employeeName" label="姓名" width="100" fixed="left"  />
            <el-table-column prop="organizationName" label="所属组织" width="150"  />
            <el-table-column prop="workDays" label="应出勤天数" width="100"  />
            <el-table-column prop="presentDays" label="实际出勤天数" width="120"  />
            <el-table-column prop="leaveDays" label="请假天数" width="100"  />
            <el-table-column prop="absentDays" label="旷工天数" width="100"  />
            <el-table-column prop="lateTimes" label="迟到次数" width="100"  />
            <el-table-column prop="earlyLeaveTimes" label="早退次数" width="100"  />
            <el-table-column prop="attendanceRate" label="出勤率" width="100">
              <template #default="scope">
                <el-tag :type="getAttendanceRateTagType(scope.row.attendanceRate)">
                  {{ scope.row.attendanceRate }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注" width="150" show-overflow-tooltip  />
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="图表分析" name="chart">
          <div class="chart-container">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="chart-item">
                  <h4>出勤率分布</h4>
                  <div ref="attendanceRateChartRef" class="chart" style="height: 300px;"></div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="chart-item">
                  <h4>考勤类型统计</h4>
                  <div ref="attendanceTypeChartRef" class="chart" style="height: 300px;"></div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top: 20px;">
              <el-col :span="24">
                <div class="chart-item">
                  <h4>部门考勤对比</h4>
                  <div ref="departmentComparisonChartRef" class="chart" style="height: 400px;"></div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <el-tab-pane label="趋势分析" name="trend">
          <div class="trend-container">
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="chart-item">
                  <h4>出勤率趋势</h4>
                  <div ref="trendChartRef" class="chart" style="height: 400px;"></div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 历史报表列表 -->
    <el-card class="history-reports" shadow="never">
      <template #header>
        <span>历史报表</span>
      </template>
      <el-table
        :data="historyReports"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="reportName" label="报表名称" width="200"  />
        <el-table-column prop="reportType" label="报表类型" width="120"  />
        <el-table-column prop="organizationName" label="统计范围" width="150"  />
        <el-table-column prop="dateRange" label="统计周期" width="200"  />
        <el-table-column prop="generateTime" label="生成时间" width="180"  />
        <el-table-column prop="generateBy" label="生成人" width="100"  />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleViewHistoryReport(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="primary" link @click="handleDownloadReport(scope.row)">
              下载
            </el-button>
            <el-button size="small" type="danger" link @click="handleDeleteReport(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  View,
  Download,
  Refresh,
  FullScreen
} from '@element-plus/icons-vue'
import echarts, { createChart } from '@/utils/echarts'
import { attendanceApi, attendanceOptions } from '@/api/attendance'
import { organizationApi } from '@/api/organization'

// 响应式数据
const reportFormRef = ref()
const generateLoading = ref(false)
const reportData = ref<unknown[]>([])
const organizationTree = ref<unknown[]>([])
const activeReportTab = ref('table')
const historyReports = ref<unknown[]>([])

// 图表引用
const attendanceRateChartRef = ref()
const attendanceTypeChartRef = ref()
const departmentComparisonChartRef = ref()
const trendChartRef = ref()

// 报表表单
const reportForm = reactive({
  reportType: 'MONTHLY',
  organizationId: undefined,
  dateRange: []
})

// 表单验证规则
const reportRules = {
  reportType: [
    { required: true, message: '请选择报表类型', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '请选择统计周期', trigger: 'change' }
  ]
}

// 报表标题
const reportTitle = computed(() => {
  const typeMap = {
    'MONTHLY': '月度出勤报表',
    'LEAVE_STATISTICS': '请假统计表',
    'ANALYSIS': '考勤分析报告'
  }
  return (typeMap as unknown)[reportForm.reportType] || '考勤报表'
})

// 报表统计概览
const reportSummary = reactive({
  totalEmployees: 0,
  totalWorkDays: 0,
  totalPresentDays: 0,
  averageAttendanceRate: 0
})

// 生成报表
const handleGenerateReport = async () => {
  try {
    await reportFormRef.value.validate()
    
    generateLoading.value = true
    
    // 模拟生成报表数据
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟报表数据
    reportData.value = [
      {
        employeeCode: 'E001',
        employeeName: '张三',
        organizationName: '计算机学院',
        workDays: 22,
        presentDays: 20,
        leaveDays: 2,
        absentDays: 0,
        lateTimes: 1,
        earlyLeaveTimes: 0,
        attendanceRate: 90.9,
        remarks: ''
      },
      {
        employeeCode: 'E002',
        employeeName: '李四',
        organizationName: '计算机学院',
        workDays: 22,
        presentDays: 22,
        leaveDays: 0,
        absentDays: 0,
        lateTimes: 0,
        earlyLeaveTimes: 0,
        attendanceRate: 100,
        remarks: ''
      }
    ]
    
    // 更新统计概览
    updateReportSummary()
    
    // 渲染图表
    nextTick(() => {
      renderCharts()
    })
    
    ElMessage.success('报表生成成功')
  } catch (__error) {
    ElMessage.error('生成报表失败')
  } finally {
    generateLoading.value = false
  }
}

// 更新报表统计概览
const updateReportSummary = () => {
  const data = reportData.value
  reportSummary.totalEmployees = data.length
  reportSummary.totalWorkDays = data.reduce((sum, item) => sum + item.workDays, 0)
  reportSummary.totalPresentDays = data.reduce((sum, item) => sum + item.presentDays, 0)
  reportSummary.averageAttendanceRate = data.length > 0 
    ? Math.round(data.reduce((sum, item) => sum + item.attendanceRate, 0) / data.length)
    : 0
}

// 渲染图表
const renderCharts = () => {
  renderAttendanceRateChart()
  renderAttendanceTypeChart()
  renderDepartmentComparisonChart()
  renderTrendChart()
}

// 渲染出勤率分布图
const renderAttendanceRateChart = () => {
  const chart = createChart(attendanceRateChartRef.value)
  const option = {
    title: {
      text: '出勤率分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: '60%',
        data: [
          { value: 15, name: 'HrHr90%以上' },
          { value: 8, name: '80%-90%' },
          { value: 3, name: '70%-80%' },
          { value: 1, name: '70%以下' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 渲染考勤类型统计图
const renderAttendanceTypeChart = () => {
  const chart = createChart(attendanceTypeChartRef.value)
  const option = {
    title: {
      text: '考勤类型统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['出勤', '请假', '旷工', '迟到', '早退']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'bar',
        data: [450, 32, 5, 12, 8],
        itemStyle: {
   
          color: function(params: unknown) {
            const colors = ['#5cb87a', '#e6a23c', '#f56c6c', '#f56c6c', '#f56c6c']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 渲染部门考勤对比图
const renderDepartmentComparisonChart = () => {
  const chart = createChart(departmentComparisonChartRef.value)
  const option = {
    title: {
      text: '部门考勤对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['出勤率', '请假率', '旷工率'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: ['计算机学院', '机械学院', '电子学院', '管理学院', '外语学院']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '出勤率',
        type: 'bar',
        data: [95, 92, 88, 90, 94],
        itemStyle: { color: '#5cb87a' }
      },
      {
        name: '请假率',
        type: 'bar',
        data: [4, 6, 8, 7, 5],
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '旷工率',
        type: 'bar',
        data: [1, 2, 4, 3, 1],
        itemStyle: { color: '#f56c6c' }
      }
    ]
  }
  chart.setOption(option)
}

// 渲染趋势图
const renderTrendChart = () => {
  const chart = createChart(trendChartRef.value)
  const option = {
    title: {
      text: '出勤率趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        type: 'line',
        data: [92, 88, 94, 91, 95, 93],
        smooth: true,
        itemStyle: { color: '#409eff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 预览报表
const handlePreviewReport = () => {
  ElMessage.info('预览功能开发中...')
}

// 导出Excel
const handleExportExcel = async () => {
  try {
    const params = {
      reportType: reportForm.reportType as 'MONTHLY' | 'LEAVE_STATISTICS' | 'ANALYSIS',
      organizationId: reportForm.organizationId,
      startDate: reportForm.dateRange[0],
      endDate: reportForm.dateRange[1],
      format: 'EXCEL' as 'EXCEL' | 'PDF'
    }
    
    const blob = await attendanceApi.generateAttendanceReport(params)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${reportTitle.value}_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 导出PDF
const handleExportPDF = async () => {
  try {
    const params = {
      reportType: reportForm.reportType as 'MONTHLY' | 'LEAVE_STATISTICS' | 'ANALYSIS',
      organizationId: reportForm.organizationId,
      startDate: reportForm.dateRange[0],
      endDate: reportForm.dateRange[1],
      format: 'PDF' as 'EXCEL' | 'PDF'
    }
    
    const blob = await attendanceApi.generateAttendanceReport(params)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${reportTitle.value}_${new Date().toISOString().slice(0, 10)}.pdf`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 刷新报表
const handleRefreshReport = () => {
  handleGenerateReport()
}

// 全屏显示
const handleFullscreen = () => {
  ElMessage.info('全屏功能开发中...')
}

// 查看历史报表
   
const handleViewHistoryReport = (report: unknown) => {
  ElMessage.info('查看历史报表功能开发中...')
}

// 下载报表
   
const handleDownloadReport = (report: unknown) => {
  ElMessage.info('下载报表功能开发中...')
}

// 删除报表
   
const handleDeleteReport = (report: unknown) => {
  ElMessage.info('删除报表功能开发中...')
}

// 获取出勤率标签类型
const getAttendanceRateTagType = (rate: number) => {
  if (rate >= 95) return 'success'
  if (rate >= 85) return 'warning'
  return 'danger'
}

// 获取组织树
const fetchOrganizationTree = async () => {
  try {
    const result = await organizationApi.getOrganizationChart()
    // 将组织架构图转换为树形选择器需要的格式
    organizationTree.value = result.nodes ? result.nodes.map(node => ({
      id: node.id,
      name: node.name,
      children: node.children?.map(child => ({
        id: child.id,
        name: child.name
      }))
    })) : []
  } catch (__error) {
    }
}

// 获取历史报表
const fetchHistoryReports = async () => {
  try {
    // 模拟历史报表数据
    historyReports.value = [
      {
        id: 1,
        reportName: '2025年5月考勤报表',
        reportType: '月度报表',
        organizationName: '全校',
        dateRange: '2025-05-01 至 2025-05-31',
        generateTime: '2025-06-01 09:00:00',
        generateBy: '张三'
      }
    ]
  } catch (__error) {
    }
}

// 初始化
onMounted(() => {
  fetchOrganizationTree()
  fetchHistoryReports()
})
</script>

<style scoped>
.attendance-report-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.report-generator {
  margin-bottom: 20px;
}

.report-preview {
  margin-bottom: 20px;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-actions {
  display: flex;
  gap: 8px;
}

.report-summary {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-item {
  text-align: center;
}

.summary-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 14px;
  color: #606266;
}

.report-tabs {
  margin-top: 20px;
}

.chart-container,
.trend-container {
  padding: 20px;
}

.chart-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-item h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.chart {
  width: 100%;
}

.history-reports {
  margin-bottom: 20px;
}
</style>
