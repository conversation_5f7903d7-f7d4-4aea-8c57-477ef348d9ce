<template>
  <div class="attendance-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>考勤管理</h2>
      <p>管理教职工考勤、请假申请和考勤统计</p>
    </div>

    <!-- 功能导航卡片 -->
    <el-row :gutter="20" class="function-cards">
      <el-col :span="6">
        <el-card class="function-card" shadow="hover" @click="navigateToLeaveApplication">
          <div class="card-content">
            <div class="card-icon leave">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">请假管理</div>
              <div class="card-desc">申请、审批、销假</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" shadow="hover" @click="navigateToAttendanceRecord">
          <div class="card-content">
            <div class="card-icon record">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">考勤记录</div>
              <div class="card-desc">查询、补录、统计</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" shadow="hover" @click="navigateToAttendanceReport">
          <div class="card-content">
            <div class="card-icon report">
              <el-icon><Document /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">考勤报表</div>
              <div class="card-desc">生成、导出、分析</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" shadow="hover" @click="navigateToAttendanceConfig">
          <div class="card-content">
            <div class="card-icon config">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">考勤配置</div>
              <div class="card-desc">规则、设备、校历</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalEmployees }}</div>
              <div class="stats-label">总人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon present">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.presentCount }}</div>
              <div class="stats-label">今日出勤</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon leave">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.leaveCount }}</div>
              <div class="stats-label">今日请假</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.attendanceRate }}%</div>
              <div class="stats-label">出勤率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions-card" shadow="never">
      <template #header>
        <span>快速操作</span>
      </template>
      <div class="quick-actions">
        <el-button type="primary" @click="handleQuickLeaveApplication">
          <el-icon><Plus /></el-icon>
          申请请假
        </el-button>
        <el-button type="success" @click="handleQuickAttendanceQuery">
          <el-icon><Search /></el-icon>
          查询考勤
        </el-button>
        <el-button type="warning" @click="handleQuickReportGeneration">
          <el-icon><Document /></el-icon>
          生成报表
        </el-button>
        <el-button type="info" @click="handleQuickDataEntry">
          <el-icon><Edit /></el-icon>
          补录数据
        </el-button>
      </div>
    </el-card>

    <!-- 待处理事项 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="pending-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>待审批请假</span>
              <el-button size="small" type="primary" link @click="viewAllPendingLeave">
                查看全部
              </el-button>
            </div>
          </template>
          <div class="pending-list">
            <div
              v-for="item in pendingLeaveApplications"
              :key="item.id"
              class="pending-item"
              @click="handleViewLeaveApplication(item)"
            >
              <div class="item-info">
                <div class="item-title">{{ item.employeeName }} - {{ item.leaveTypeName }}</div>
                <div class="item-desc">{{ item.startDate }} 至 {{ item.endDate }} ({{ item.leaveDays }}天)</div>
              </div>
              <div class="item-actions">
                <el-tag :type="getLeaveStatusTagType(item.approvalStatus)" size="small">
                  {{ item.approvalStatusName }}
                </el-tag>
              </div>
            </div>
            <div v-if="pendingLeaveApplications.length === 0" class="empty-state">
              暂无待审批请假申请
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="pending-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>待处理考勤</span>
              <el-button size="small" type="primary" link @click="viewAllPendingAttendance">
                查看全部
              </el-button>
            </div>
          </template>
          <div class="pending-list">
            <div
              v-for="item in pendingAttendanceReports"
              :key="item.id"
              class="pending-item"
              @click="handleViewAttendanceReport(item)"
            >
              <div class="item-info">
                <div class="item-title">{{ item.organizationName }} - {{ item.reportMonth }}</div>
                <div class="item-desc">提交时间：{{ formatDateTime(item.submissionTime || '') }}</div>
              </div>
              <div class="item-actions">
                <el-tag :type="getReportStatusTagType(item.reportStatus)" size="small">
                  {{ item.reportStatusName }}
                </el-tag>
              </div>
            </div>
            <div v-if="pendingAttendanceReports.length === 0" class="empty-state">
              暂无待处理考勤上报
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Calendar,
  Clock,
  Document,
  Setting,
  User,
  Check,
  TrendCharts,
  Plus,
  Search,
  Edit
} from '@element-plus/icons-vue'
import { attendanceApi, type LeaveApplication, type AttendanceReportBatch, type AttendanceStatistics } from '@/api/attendance'
import { formatDateTime } from '@/utils/date'

const router = useRouter()

// 响应式数据
const stats = reactive<AttendanceStatistics>({
  totalEmployees: 0,
  presentCount: 0,
  leaveCount: 0,
  absentCount: 0,
  lateCount: 0,
  earlyLeaveCount: 0,
  attendanceRate: 0,
  leaveRate: 0,
  absentRate: 0
})

const pendingLeaveApplications = ref<LeaveApplication[]>([])
const pendingAttendanceReports = ref<AttendanceReportBatch[]>([])

// 导航到各个功能模块
const navigateToLeaveApplication = () => {
  router.push('/attendance/leave-application')
}

const navigateToAttendanceRecord = () => {
  router.push('/attendance/record')
}

const navigateToAttendanceReport = () => {
  router.push('/attendance/report')
}

const navigateToAttendanceConfig = () => {
  router.push('/attendance/config')
}

// 快速操作
const handleQuickLeaveApplication = () => {
  router.push('/attendance/leave-application?action=create')
}

const handleQuickAttendanceQuery = () => {
  router.push('/attendance/record?action=query')
}

const handleQuickReportGeneration = () => {
  router.push('/attendance/report?action=generate')
}

const handleQuickDataEntry = () => {
  router.push('/attendance/record?action=entry')
}

// 查看待处理事项
const viewAllPendingLeave = () => {
  router.push('/attendance/leave-application?status=pending')
}

const viewAllPendingAttendance = () => {
  router.push('/attendance/report?status=pending')
}

const handleViewLeaveApplication = (item: LeaveApplication) => {
  router.push(`/attendance/leave-application/${item.id}`)
}

const handleViewAttendanceReport = (item: AttendanceReportBatch) => {
  router.push(`/attendance/report/${item.id}`)
}

// 获取状态标签类型
const getLeaveStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING_DEPARTMENT':
    case 'PENDING_HR':
    case 'PENDING_LEADERSHIP':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}

const getReportStatusTagType = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return 'info'
    case 'SUBMITTED':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const today = new Date().toISOString().split('T')[0]
    const result = await attendanceApi.getAttendanceStatistics(undefined, today, today)
    Object.assign(stats, result)
  } catch (__error) {
    }
}

// 获取待审批请假申请
const fetchPendingLeaveApplications = async () => {
  try {
    const result = await attendanceApi.queryLeaveApplications({
      page: 0,
      size: 5,
      approvalStatuses: ['PENDING_DEPARTMENT', 'PENDING_HR', 'PENDING_LEADERSHIP']
    })
    pendingLeaveApplications.value = result.content
  } catch (__error) {
    }
}

// 获取待处理考勤上报
const fetchPendingAttendanceReports = async () => {
  try {
    const result = await attendanceApi.queryAttendanceReportBatches({
      page: 0,
      size: 5,
      reportStatus: 'SUBMITTED'
    })
    pendingAttendanceReports.value = result.content
  } catch (__error) {
    }
}

// 初始化
onMounted(() => {
  fetchStatistics()
  fetchPendingLeaveApplications()
  fetchPendingAttendanceReports()
})
</script>

<style scoped>
.attendance-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.function-cards {
  margin-bottom: 20px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-icon.leave {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.record {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.report {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.config {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #909399;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.present {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.leave {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-icon.rate {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.quick-actions-card {
  margin-bottom: 20px;
}

.quick-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.pending-card {
  height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pending-list {
  max-height: 220px;
  overflow-y: auto;
}

.pending-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.pending-item:hover {
  background: #f5f7fa;
  border-color: #409eff;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.item-desc {
  font-size: 12px;
  color: #909399;
}

.item-actions {
  margin-left: 12px;
}

.empty-state {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}
</style>
