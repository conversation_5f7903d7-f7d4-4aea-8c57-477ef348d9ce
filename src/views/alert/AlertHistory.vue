<template>
  <div class="alert-history">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>预警历史记录</h2>
      <div class="header-actions">
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出记录
        </el-button>
        <el-button type="primary" @click="showStatistics = true">
          <el-icon><DataAnalysis /></el-icon>
          统计分析
        </el-button>
      </div>
    </div>

    <!-- 搜索过滤 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="预警类型">
          <el-select
            v-model="searchForm.alertType"
            placeholder="全部类型"
            clearable
            style="width: 150px"
          >
            <el-option-group
              v-for="group in alertTypeGroups"
              :key="group.label"
              :label="group.label"
            >
              <el-option
                v-for="item in group.options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-option-group>
          </el-select>
        </el-form-item>
        
        <el-form-item label="预警级别">
          <el-select
            v-model="searchForm.level"
            placeholder="全部级别"
            clearable
            style="width: 120px"
          >
            <el-option label="低级" value="low"  />
            <el-option label="中级" value="medium"  />
            <el-option label="高级" value="high"  />
            <el-option label="严重" value="critical"  />
          </el-select>
        </el-form-item>
        
        <el-form-item label="处理状态">
          <el-select
            v-model="searchForm.status"
            placeholder="全部状态"
            clearable
            style="width: 120px"
          >
            <el-option label="活跃" value="active"  />
            <el-option label="已解决" value="resolved"  />
            <el-option label="已忽略" value="ignored"  />
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
           />
        </el-form-item>
        
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="标题/内容/责任人"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshRight /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <div class="statistics-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic
              title="总预警数"
              :value="statistics.total"
              suffix="条"
            >
              <template #prefix>
                <el-icon style="color: #409EFF">
                  <WarningFilled />
                </el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic
              title="待处理"
              :value="statistics.active"
              suffix="条"
            >
              <template #prefix>
                <el-icon style="color: #E6A23C">
                  <Clock />
                </el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic
              title="处理率"
              :value="statistics.resolveRate"
              suffix="%"
            >
              <template #prefix>
                <el-icon style="color: #67C23A">
                  <CircleCheck />
                </el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic
              title="平均响应时间"
              :value="statistics.avgResponseTime"
              suffix="分钟"
            >
              <template #prefix>
                <el-icon style="color: #F56C6C">
                  <Timer />
                </el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 历史记录列表 -->
    <el-card>
      <el-table
        v-loading="loading"
        :data="alertList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="id" label="预警ID" width="120"  />
        <el-table-column prop="alertType" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.alertType)">
              {{ getAlertTypeName(row.alertType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="级别" width="80">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.level)">
              {{ getLevelName(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="预警标题" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="assignee" label="责任人" width="100"  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="createTime" label="发生时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="responseTime" label="响应时长" width="100">
          <template #default="{ row }">
            <span v-if="row.responseTime">
              {{ formatDuration(row.responseTime) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewDetail(row)">
              详情
            </el-button>
            <el-button
              v-if="row.status === 'active'"
              link
              type="primary"
              @click="handleProcess(row)"
            >
              处理
            </el-button>
            <el-button
              v-if="row.processInstanceId"
              link
              type="primary"
              @click="viewProcess(row)"
            >
              流程
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchData"
        @current-change="fetchData"
        class="mt-20"
       />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetail"
      title="预警详情"
      width="800px"
    >
      <div v-if="currentAlert" class="alert-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="预警ID">
            {{ currentAlert.id }}
          </el-descriptions-item>
          <el-descriptions-item label="预警类型">
            {{ getAlertTypeName(currentAlert.alertType) }}
          </el-descriptions-item>
          <el-descriptions-item label="预警级别">
            <el-tag :type="getLevelTagType(currentAlert.level)">
              {{ getLevelName(currentAlert.level) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentAlert.status)">
              {{ getStatusName(currentAlert.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发生时间">
            {{ formatTime(currentAlert.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="解决时间">
            {{ currentAlert.resolveTime ? formatTime(currentAlert.resolveTime) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="责任人">
            {{ currentAlert.assignee || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ currentAlert.department || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="业务影响" :span="2">
            {{ currentAlert.businessImpact }}
          </el-descriptions-item>
          <el-descriptions-item label="触发规则" :span="2">
            {{ currentAlert.triggerRule }}
          </el-descriptions-item>
          <el-descriptions-item label="预警标题" :span="2">
            {{ currentAlert.title }}
          </el-descriptions-item>
          <el-descriptions-item label="预警内容" :span="2">
            <div style="white-space: pre-wrap">{{ currentAlert.message }}</div>
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 处理历史 -->
        <div class="process-history" v-if="currentAlert.processHistory">
          <h4>处理历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in currentAlert.processHistory"
              :key="index"
              :timestamp="formatTime(item.time)"
              placement="top"
            >
              <div>
                <strong>{{ item.operator }}</strong>
                {{ item.action }}
              </div>
              <div v-if="item.comment" class="comment">
                备注：{{ item.comment }}
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>

    <!-- 统计分析对话框 -->
    <el-dialog
      v-model="showStatistics"
      title="预警统计分析"
      width="90%"
      :close-on-click-modal="false"
    >
      <AlertStatistics
        :date-range="searchForm.dateRange"
        @close="showStatistics = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AlertHistory'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download,
  DataAnalysis,
  Search,
  RefreshRight,
  WarningFilled,
  Clock,
  CircleCheck,
  Timer
} from '@element-plus/icons-vue'
import AlertStatistics from './components/AlertStatistics.vue'
import { exportToExcel } from '@/utils/export'

// 类型定义
interface AlertRecord {
  id: string
  alertType: string
  level: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  processInstanceId?: string
  processDefinitionKey?: string
  taskId?: string
  assignee?: string
  createTime: Date
  resolveTime?: Date
  status: 'active' | 'resolved' | 'ignored'
  responseTime?: number
  businessImpact: string
  department?: string
  triggerRule: string
  notificationSent: boolean
  processHistory?: Array<{
    time: Date
    operator: string
    action: string
    comment?: string
  }>
}

// 响应式数据
const loading = ref(false)
const alertList = ref<AlertRecord[]>([])
const selectedAlerts = ref<AlertRecord[]>([])
const showDetail = ref(false)
const showStatistics = ref(false)
const currentAlert = ref<AlertRecord | null>(null)

// 搜索表单
const searchForm = reactive({
  alertType: '',
  level: '',
  status: '',
  dateRange: null as unknown,
  keyword: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 统计数据
const statistics = reactive({
  total: 0,
  active: 0,
  resolveRate: 0,
  avgResponseTime: 0
})

// 预警类型分组
const alertTypeGroups = [
  {
    label: '超时预警',
    options: [
      { label: '任务超时', value: 'task_timeout' },
      { label: '流程超时', value: 'process_timeout' },
      { label: '审批超时', value: 'approval_timeout' },
      { label: '响应超时', value: 'response_timeout' }
    ]
  },
  {
    label: '异常预警',
    options: [
      { label: '系统异常', value: 'system_error' },
      { label: '业务异常', value: 'business_error' },
      { label: '数据异常', value: 'data_error' },
      { label: '网络异常', value: 'network_error' }
    ]
  },
  {
    label: '积压预警',
    options: [
      { label: '任务积压', value: 'task_backlog' },
      { label: '审批积压', value: 'approval_backlog' },
      { label: '处理积压', value: 'process_backlog' },
      { label: '队列积压', value: 'queue_backlog' }
    ]
  },
  {
    label: '业务预警',
    options: [
      { label: '证照到期', value: 'license_expire' },
      { label: '合同到期', value: 'contract_expire' },
      { label: '考勤异常', value: 'attendance_abnormal' },
      { label: '薪酬异常', value: 'salary_abnormal' }
    ]
  }
]

// 方法
const getAlertTypeName = (type: string) => {
  for (const group of alertTypeGroups) {
    const found = group.options.find(opt => opt.value === type)
    if (found) return found.label
  }
  return type
}

const getTypeTagType = (type: string) => {
  if (type.includes('timeout')) return 'warning'
  if (type.includes('error')) return 'danger'
  if (type.includes('backlog')) return 'info'
  return ''
}

const getLevelName = (level: string) => {
  const levelMap: Record<string, string> = {
    low: '低级',
    medium: '中级',
    high: '高级',
    critical: '严重'
  }
  return levelMap[level] || level
}

const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return typeMap[level] || 'info'
}

const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    resolved: '已解决',
    ignored: '已忽略'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'danger',
    resolved: 'success',
    ignored: 'info'
  }
  return typeMap[status] || 'info'
}

const formatTime = (time: Date | string) => {
  if (!time) return '-'
  const date = new Date(time)
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

const formatDuration = (minutes: number) => {
  if (minutes < 60) return `${minutes}分钟`
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}小时${mins}分钟`
}

const fetchData = async () => {
  loading.value = true
  try {
    // 模拟数据获取
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 生成模拟数据
    const mockData: AlertRecord[] = []
    for (let i = 0; i < 20; i++) {
      const isActive = Math.random() > 0.7
      const createTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
      const resolveTime = isActive ? undefined : new Date(createTime.getTime() + Math.random() * 4 * 60 * 60 * 1000)
      
      mockData.push({
        id: `ALERT-${Date.now()}-${i}`,
        alertType: alertTypeGroups[Math.floor(Math.random() * 4)].options[Math.floor(Math.random() * 4)].value,
        level: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as unknown,
        title: `预警标题示例 ${i + 1}`,
        message: `这是预警的详细描述信息，包含具体的异常数据和触发条件...`,
        assignee: ['张三', '李四', '王五'][Math.floor(Math.random() * 3)],
        department: ['技术部', '运营部', '人事部'][Math.floor(Math.random() * 3)],
        createTime,
        resolveTime,
        status: isActive ? 'active' : (['resolved', 'ignored'][Math.floor(Math.random() * 2)] as unknown),
        responseTime: resolveTime ? Math.floor((resolveTime.getTime() - createTime.getTime()) / 60000) : undefined,
        businessImpact: '高',
        triggerRule: '连续3次失败触发',
        notificationSent: true,
        processHistory: !isActive ? [
          {
            time: createTime,
            operator: '系统',
            action: '创建预警'
          },
          {
            time: new Date(createTime.getTime() + 10 * 60000),
            operator: '张三',
            action: '开始处理'
          },
          {
            time: resolveTime!,
            operator: '张三',
            action: '问题已解决',
            comment: '已修复相关配置'
          }
        ] : undefined
      })
    }
    
    alertList.value = mockData
    pagination.total = 100
    
    // 更新统计
    statistics.total = 100
    statistics.active = 30
    statistics.resolveRate = 70
    statistics.avgResponseTime = 45
    
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    alertType: '',
    level: '',
    status: '',
    dateRange: null,
    keyword: ''
  })
  handleSearch()
}

const handleSelectionChange = (selection: AlertRecord[]) => {
  selectedAlerts.value = selection
}

const viewDetail = (row: AlertRecord) => {
  currentAlert.value = row
  showDetail.value = true
}

const handleProcess = (row: AlertRecord) => {
  ElMessage.info('跳转到预警处理页面')
}

const viewProcess = (row: AlertRecord) => {
  ElMessage.info(`查看流程实例: ${row.processInstanceId}`)
}

const handleExport = async () => {
  if (selectedAlerts.value.length === 0) {
    ElMessage.warning('请选择要导出的记录')
    return
  }
  
  const data = selectedAlerts.value.map(item => ({
    预警ID: item.id,
    预警类型: getAlertTypeName(item.alertType),
    预警级别: getLevelName(item.level),
    预警标题: item.title,
    责任人: item.assignee || '-',
    部门: item.department || '-',
    发生时间: formatTime(item.createTime),
    状态: getStatusName(item.status),
    响应时长: item.responseTime ? formatDuration(item.responseTime) : '-'
  }))
  
  exportToExcel(data, '预警历史记录')
  ElMessage.success('导出成功')
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.alert-history {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .statistics-overview {
    margin-bottom: 20px;
    
    .stat-card {
      text-align: center;
      
      .el-statistic {
        :deep(.el-statistic__head) {
          color: var(--el-text-color-secondary);
          font-size: 14px;
        }
        
        :deep(.el-statistic__content) {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
        }
      }
    }
  }
  
  .alert-detail {
    .process-history {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color);
      
      h4 {
        margin-bottom: 16px;
        color: var(--el-text-color-primary);
      }
      
      .comment {
        margin-top: 4px;
        color: var(--el-text-color-secondary);
        font-size: 13px;
      }
    }
  }
  
  .mt-20 {
    margin-top: 20px;
  }
}
</style>