<template>
  <el-card class="system-status-panel" shadow="hover">
    <template #header>
      <div class="panel-header">
        <h3 class="panel-title">
          <el-icon><Monitor /></el-icon>
          系统状态
        </h3>
        <el-tag 
          :type="statusTagType"
          size="small"
        >
          {{ statusText }}
        </el-tag>
      </div>
    </template>

    <div class="status-content">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="`status-${data.status}`">
        <div class="status-icon">
          <el-icon :size="48">
            <CircleCheck v-if="data.status === 'healthy'" />
            <Warning v-else-if="data.status === 'warning'" />
            <CircleClose v-else />
          </el-icon>
        </div>
        <div class="status-text">
          <h4>{{ statusTitle }}</h4>
          <p>{{ statusDescription }}</p>
        </div>
      </div>

      <!-- 关键指标 -->
      <div class="metrics-grid">
        <div class="metric-item">
          <div class="metric-label">运行时间</div>
          <div class="metric-value">{{ data.uptime }}</div>
        </div>
        <div class="metric-item">
          <div class="metric-label">最后同步</div>
          <div class="metric-value">{{ formatLastSync(data.lastSync) }}</div>
        </div>
        <div class="metric-item">
          <div class="metric-label">在线用户</div>
          <div class="metric-value">
            <AnimatedNumber :value="data.activeUsers" format="0,0" />
          </div>
        </div>
        <div class="metric-item">
          <div class="metric-label">系统负载</div>
          <div class="metric-value">
            <el-progress 
              :percentage="systemLoad" 
              :color="loadColors"
              :stroke-width="6"
             />
          </div>
        </div>
      </div>

      <!-- 最近告警 -->
      <div class="alerts-section" v-if="recentAlerts.length > 0">
        <h5 class="section-title">最近告警</h5>
        <div class="alert-list">
          <div 
            v-for="alert in recentAlerts"
            :key="alert.id"
            class="alert-item"
            :class="`alert-${alert.type}`"
          >
            <el-icon class="alert-icon">
              <InfoFilled v-if="alert.type === 'info'" />
              <Warning v-else-if="alert.type === 'warning'" />
              <CircleClose v-else />
            </el-icon>
            <div class="alert-content">
              <div class="alert-message">{{ alert.message }}</div>
              <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="actions">
        <el-button 
          size="small"
          @click="$emit('view-details')"
        >
          查看详情
        </el-button>
        <el-button 
          size="small"
          @click="$emit('run-diagnostics')"
          :loading="diagnosticsRunning"
        >
          运行诊断
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">

defineOptions({
  name: 'SystemStatusPanel'
})
 
import { 
  Monitor, 
  CircleCheck, 
  Warning, 
  CircleClose,
  InfoFilled
} from '@element-plus/icons-vue'
import HrAnimatedNumber from '@/components/common/HrAnimatedNumber.vue'
import type { Alert } from '../types'

interface Props {
  data: {
    status: 'healthy' | 'warning' | 'error'
    uptime: string
    lastSync: string
    activeUsers: number
  }
  alerts?: Alert[]
  diagnosticsRunning?: boolean
}

interface Emits {
  (e: 'view-details'): void
  (e: 'run-diagnostics'): void
}

const props = withDefaults(defineProps<Props>(), {
  alerts: () => [],
  diagnosticsRunning: false
})

const emit = defineEmits<Emits>()

// 状态相关计算
const statusTagType = computed(() => {
  const typeMap = {
    healthy: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return typeMap[props.data.status] || 'info'
})

const statusText = computed(() => {
  const textMap = {
    healthy: '运行正常',
    warning: '轻微异常',
    error: '严重故障'
  }
  return textMap[props.data.status] || '未知'
})

const statusTitle = computed(() => {
  const titleMap = {
    healthy: '系统运行正常',
    warning: '检测到潜在问题',
    error: '系统出现故障'
  }
  return titleMap[props.data.status]
})

const statusDescription = computed(() => {
  const descMap = {
    healthy: '所有服务正常运行，无异常情况',
    warning: '部分功能可能受影响，请关注告警信息',
    error: '核心功能受到影响，请立即处理'
  }
  return descMap[props.data.status]
})

// 系统负载计算（模拟）
const systemLoad = computed(() => {
  // 基于活跃用户数计算负载
  const maxUsers = 1000
  return Math.min(Math.round((props.data.activeUsers / maxUsers) * 100), 100)
})

// 负载颜色配置
const loadColors = [
  { color: '#67c23a', percentage: 20 },
  { color: '#409eff', percentage: 40 },
  { color: '#e6a23c', percentage: 60 },
  { color: '#f56c6c', percentage: 80 },
  { color: '#f56c6c', percentage: 100 }
]

// 最近告警（最多显示3条）
const recentAlerts = computed(() => {
  return props.alerts
    .filter(alert => !alert.acknowledged)
    .slice(0, 3)
})

// 格式化最后同步时间
const formatLastSync = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)} 分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)} 小时前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.system-status-panel {
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  background: #f5f7fa;
}

.status-indicator.status-healthy {
  background: #f0f9ff;
}

.status-indicator.status-warning {
  background: #fdf6ec;
}

.status-indicator.status-error {
  background: #fef0f0;
}

.status-icon {
  flex-shrink: 0;
}

.status-healthy .status-icon {
  color: #67c23a;
}

.status-warning .status-icon {
  color: #e6a23c;
}

.status-error .status-icon {
  color: #f56c6c;
}

.status-text h4 {
  margin: 0 0 4px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.status-text p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

/* 指标网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

.metric-value {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

/* 告警部分 */
.alerts-section {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.section-title {
  margin: 0 0 12px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  background: #f5f7fa;
}

.alert-item.alert-info {
  background: #ecf5ff;
}

.alert-item.alert-warning {
  background: #fdf6ec;
}

.alert-item.alert-error {
  background: #fef0f0;
}

.alert-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-info .alert-icon {
  color: #909399;
}

.alert-warning .alert-icon {
  color: #e6a23c;
}

.alert-error .alert-icon {
  color: #f56c6c;
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-message {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.alert-time {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

/* 响应式 */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .status-indicator {
    flex-direction: column;
    text-align: center;
  }
}
</style>