<template>
  <div class="overview-cards">
    <el-row :gutter="16">
      <el-col
        v-for="card in cards"
        :key="card.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
      >
        <div 
          class="stat-card"
          :class="[`stat-card--${card.color}`, { 'is-clickable': card.clickable }]"
          @click="card.clickable && $emit('card-click', card.id)"
        >
          <div class="stat-card__header">
            <div class="stat-card__icon">
              <el-icon :size="24">
                <component :is="card.icon" />
              </el-icon>
            </div>
            <div class="stat-card__trend" v-if="card.trend">
              <span 
                class="trend-value"
                :class="{ 'trend-up': card.trend > 0, 'trend-down': card.trend < 0 }"
              >
                <el-icon>
                  <CaretTop v-if="card.trend > 0" />
                  <CaretBottom v-else />
                </el-icon>
                {{ Math.abs(card.trend) }}%
              </span>
              <span class="trend-label" v-if="card.trendLabel">
                {{ card.trendLabel }}
              </span>
            </div>
          </div>
          <div class="stat-card__content">
            <h3 class="stat-card__title">{{ card.title }}</h3>
            <div class="stat-card__value">
              <hr-animated-number 
                :value="card.value" 
                :format="card.unit === '个' ? '0,0' : '0,0.00'"
              />
              <span class="stat-card__unit" v-if="card.unit">{{ card.unit }}</span>
            </div>
            <p class="stat-card__description" v-if="card.description">
              {{ card.description }}
            </p>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'OverviewCards'
})
 
import { 
  User, 
  Calendar, 
  CircleCheck, 
  Timer,
  CaretTop,
  CaretBottom
} from '@element-plus/icons-vue'
import HrAnimatedNumber from '@/components/common/HrAnimatedNumber.vue'
import type { StatisticsCardType } from '../types'

interface Props {
  data: {
    totalInstitutions: number
    activeChanges: number
    pendingApprovals: number
    completedChanges: number
  }
}

interface Emits {
  (e: 'card-click', cardId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算统计卡片数据
const cards = computed<StatisticsCardType[]>(() => [
  {
    id: 'total-institutions',
    title: '机构总数',
    value: props.data.totalInstitutions,
    icon: 'User',
    color: 'primary',
    unit: '个',
    clickable: true,
    description: '当前系统中的机构总数'
  },
  {
    id: 'active-changes',
    title: '进行中变更',
    value: props.data.activeChanges,
    icon: 'Calendar',
    color: 'warning',
    trend: 12.5,
    trendLabel: '较上月',
    unit: '项',
    clickable: true,
    description: '正在处理的机构变更申请'
  },
  {
    id: 'pending-approvals',
    title: '待审批',
    value: props.data.pendingApprovals,
    icon: 'Timer',
    color: 'danger',
    trend: -8.3,
    trendLabel: '较昨日',
    unit: '项',
    clickable: true,
    description: '等待审批的申请'
  },
  {
    id: 'completed-changes',
    title: '已完成变更',
    value: props.data.completedChanges,
    icon: 'CircleCheck',
    color: 'success',
    trend: 23.1,
    trendLabel: '较上月',
    unit: '项',
    description: '本月已完成的变更数'
  }
])
</script>

<style scoped>
.overview-cards {
  margin-bottom: 24px;
}

.stat-card {
  height: 100%;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: default;
}

.stat-card.is-clickable {
  cursor: pointer;
}

.stat-card.is-clickable:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.12);
}

.stat-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.stat-card__icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--icon-bg-color);
  color: var(--icon-color);
}

/* 颜色主题 */
.stat-card--primary {
  --icon-bg-color: #ecf5ff;
  --icon-color: #409eff;
}

.stat-card--success {
  --icon-bg-color: #f0f9ff;
  --icon-color: #67c23a;
}

.stat-card--warning {
  --icon-bg-color: #fdf6ec;
  --icon-color: #e6a23c;
}

.stat-card--danger {
  --icon-bg-color: #fef0f0;
  --icon-color: #f56c6c;
}

.stat-card__trend {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.trend-value {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.trend-value.trend-up {
  color: #67c23a;
}

.trend-value.trend-down {
  color: #f56c6c;
}

.trend-label {
  font-size: 12px;
  color: #909399;
}

.stat-card__content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-card__title {
  margin: 0;
  font-size: 14px;
  font-weight: 400;
  color: #909399;
}

.stat-card__value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.stat-card__unit {
  font-size: 14px;
  font-weight: 400;
  color: #606266;
}

.stat-card__description {
  margin: 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-card {
    padding: 16px;
  }

  .stat-card__value {
    font-size: 24px;
  }

  .stat-card__icon {
    width: 40px;
    height: 40px;
  }
}
</style>