<template>
  <div class="stat-card" :style="cardStyle">
    <div class="stat-header">
      <h4 class="stat-title">{{ title }}</h4>
      <el-icon v-if="icon" class="stat-icon" :size="24">
        <component :is="icon" />
      </el-icon>
    </div>
    <div class="stat-content">
      <div class="stat-value">{{ formattedValue }}</div>
      <div v-if="subtitle" class="stat-subtitle">{{ subtitle }}</div>
    </div>
    <div v-if="trend !== undefined" class="stat-trend" :class="trendClass">
      <el-icon :size="16">
        <ArrowUp v-if="trend > 0" />
        <ArrowDown v-else />
      </el-icon>
      <span>{{ Math.abs(trend) }}%</span>
    </div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'StatCard'
})
 
import { computed } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

interface Props {
  title: string
  value: number | string
  subtitle?: string
   
  icon?: unknown
  trend?: number
  color?: string
  format?: 'number' | 'currency' | 'percent'
}

const props = withDefaults(defineProps<Props>(), {
  format: 'number'
})

const formattedValue = computed(() => {
  if (typeof props.value === 'string') return props.value
  
  switch (props.format) {
    case 'currency':
      return `¥${props.value.toLocaleString()}`
    case 'percent':
      return `${props.value}%`
    default:
      return props.value.toLocaleString()
  }
})

const cardStyle = computed(() => ({
  '--card-color': props.color || '#409EFF'
}))

const trendClass = computed(() => {
  if (props.trend === undefined) return ''
  return props.trend > 0 ? 'trend-up' : 'trend-down'
})
</script>

<style scoped>
.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--card-color);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-title {
  margin: 0;
  font-size: 14px;
  color: #606266;
  font-weight: normal;
}

.stat-icon {
  color: var(--card-color);
}

.stat-content {
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.stat-subtitle {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.trend-up {
  color: #67C23A;
}

.trend-down {
  color: #F56C6C;
}
</style>