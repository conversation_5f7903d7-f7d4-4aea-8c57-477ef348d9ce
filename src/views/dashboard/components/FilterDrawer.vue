<template>
  <el-drawer
    v-model="visible"
    :title="isMobile ? '筛选条件' : '高级筛选'"
    :size="isMobile ? '80%' : '400px'"
    :direction="isMobile ? 'btr' : 'rtl'"
    @close="handleClose"
  >
    <div class="filter-content">
      <!-- 时间范围 -->
      <div class="filter-section">
        <h4 class="section-title">时间范围</h4>
        <el-radio-group 
          v-model="localFilters.timeRange" 
          class="radio-group-vertical"
        >
          <el-radio 
            v-for="option in timeRangeOptions"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
        
        <!-- 自定义日期范围 -->
        <el-date-picker
          v-if="localFilters.timeRange === 'custom'"
          v-model="localFilters.customDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :shortcuts="dateShortcuts"
          style="width: 100%; margin-top: 12px"
         />
      </div>

      <!-- 数据类型 -->
      <div class="filter-section">
        <h4 class="section-title">数据类型</h4>
        <el-checkbox-group v-model="localFilters.dataTypes">
          <el-checkbox 
            v-for="type in dataTypeOptions"
            :key="type.value"
            :label="type.value"
          >
            {{ type.label }}
          </el-checkbox>
        </el-checkbox-group>
      </div>

      <!-- 部门选择 -->
      <div class="filter-section">
        <h4 class="section-title">
          部门筛选
          <el-button 
            text 
            size="small"
            @click="toggleAllDepartments"
          >
            {{ allDepartmentsSelected ? '取消全选' : '全选' }}
          </el-button>
        </h4>
        
        <el-input
          v-model="departmentSearch"
          placeholder="搜索部门"
          :prefix-icon="Search"
          size="small"
          clearable
          style="margin-bottom: 12px"
          />
        
        <div class="department-tree">
          <el-tree
            ref="treeRef"
            :data="filteredDepartmentTree"
            :props="treeProps"
            :default-expand-all="false"
            :expand-on-click-node="false"
            show-checkbox
            node-key="id"
            :default-checked-keys="localFilters.departments"
            @check="handleDepartmentCheck"
            :filter-node-method="filterNode"
           />
        </div>
      </div>

      <!-- 其他选项 -->
      <div class="filter-section">
        <h4 class="section-title">其他选项</h4>
        <el-form label-position="left" label-width="100px">
          <el-form-item label="包含已完成">
            <el-switch v-model="localFilters.includeCompleted"  />
          </el-form-item>
          <el-form-item label="仅显示异常">
            <el-switch v-model="localFilters.onlyAbnormal"  />
          </el-form-item>
          <el-form-item label="数据粒度">
            <el-select v-model="localFilters.granularity" size="small">
              <el-option label="按日" value="day"  />
              <el-option label="按周" value="week"  />
              <el-option label="按月" value="month"  />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">

defineOptions({
  name: 'FilterDrawer'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Search } from '@element-plus/icons-vue'
import type { DashboardQueryParams, TimeRange, DataType } from '../types'
import type { TreeInstance } from 'element-plus'

interface Props {
  modelValue: boolean
  filters: DashboardQueryParams
  isMobile?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'update:filters', value: DashboardQueryParams): void
  (e: 'apply'): void
}

const props = withDefaults(defineProps<Props>(), {
  isMobile: false
})

const emit = defineEmits<Emits>()

// 本地筛选条件
const localFilters = ref<DashboardQueryParams & {
  includeCompleted?: boolean
  onlyAbnormal?: boolean
  granularity?: string
}>({
  ...props.filters,
  includeCompleted: false,
  onlyAbnormal: false,
  granularity: 'day'
})

// 控制抽屉显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 时间范围选项
const timeRangeOptions = [
  { label: '今日', value: 'today' as TimeRange },
  { label: '本周', value: 'week' as TimeRange },
  { label: '本月', value: 'month' as TimeRange },
  { label: '本季度', value: 'quarter' as TimeRange },
  { label: '本年度', value: 'year' as TimeRange },
  { label: '自定义', value: 'custom' as TimeRange }
]

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

// 数据类型选项
const dataTypeOptions = [
  { label: '机构变更', value: DataType.INSTITUTION_CHANGE },
  { label: '临时机构', value: DataType.AD_HOC_INSTITUTION }
]

// 部门树数据（模拟）
const departmentTree = ref([
  {
    id: '1',
    label: '集团总部',
    children: [
      { id: '1-1', label: '行政部' },
      { id: '1-2', label: '人力资源部' },
      { id: '1-3', label: '财务部' },
      { id: '1-4', label: '技术部' }
    ]
  },
  {
    id: '2',
    label: '华东区域',
    children: [
      { id: '2-1', label: '上海分公司' },
      { id: '2-2', label: '杭州分公司' },
      { id: '2-3', label: '南京分公司' }
    ]
  },
  {
    id: '3',
    label: '华北区域',
    children: [
      { id: '3-1', label: '北京分公司' },
      { id: '3-2', label: '天津分公司' }
    ]
  }
])

// 部门搜索
const departmentSearch = ref('')
const treeRef = ref<TreeInstance>()

// 树形控件配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 过滤后的部门树
const filteredDepartmentTree = computed(() => {
  if (!departmentSearch.value) return departmentTree.value
  
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const filterTree = (nodes: unknown[]): unknown[] => {
    return nodes.reduce((acc, node) => {
      if (node.label.includes(departmentSearch.value)) {
        acc.push(node)
      } else if (node.children) {
        const filteredChildren = filterTree(node.children)
        if (filteredChildren.length) {
          acc.push({ ...node, children: filteredChildren })
        }
      }
      return acc
    }, [])
  }
  
  return filterTree(departmentTree.value)
})

// 树节点过滤方法
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const filterNode = (value: string, data: unknown) => {
  if (!value) return true
  return data.label.includes(value)
}

// 监听搜索变化
watch(departmentSearch, (val) => {
  treeRef.value?.filter(val)
})

// 全选状态
const allDepartmentsSelected = computed(() => {
  const allIds = getAllDepartmentIds(departmentTree.value)
  return allIds.every(id => localFilters.value.departments.includes(id))
})

// 获取所有部门ID
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getAllDepartmentIds = (nodes: unknown[]): string[] => {
  return nodes.reduce((acc, node) => {
    acc.push(node.id)
    if (node.children) {
      acc.push(...getAllDepartmentIds(node.children))
    }
    return acc
  }, [])
}

// 切换全选
const toggleAllDepartments = () => {
  if (allDepartmentsSelected.value) {
    localFilters.value.departments = []
  } else {
    localFilters.value.departments = getAllDepartmentIds(departmentTree.value)
  }
  treeRef.value?.setCheckedKeys(localFilters.value.departments)
}

// 处理部门选择
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleDepartmentCheck = (data: unknown, checked: unknown) => {
  localFilters.value.departments = checked.checkedKeys
}

// 重置筛选条件
const handleReset = () => {
  localFilters.value = {
    timeRange: 'month' as TimeRange,
    dataTypes: [DataType.INSTITUTION_CHANGE, DataType.AD_HOC_INSTITUTION],
    departments: [],
    includeCompleted: false,
    onlyAbnormal: false,
    granularity: 'day'
  }
  treeRef.value?.setCheckedKeys([])
}

// 确认筛选
const handleConfirm = () => {
  const {includeCompleted: _includeCompleted, onlyAbnormal: _onlyAbnormal, granularity: _granularity, ...filters: _...filters} =  localFilters.value
  emit('update:filters', filters)
  emit('apply')
  visible.value 
}

.filter-section {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 16px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.radio-group-vertical {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.department-tree {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 8px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 0 20px;
}

/* 移动端样式 */
:deep(.el-drawer__header) {
  margin-bottom: 20px;
}

:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 响应式 */
@media (max-width: 768px) {
  .filter-content {
    padding: 0 16px;
  }
  
  .department-tree {
    max-height: 200px;
  }
}
</style>