<template>
  <div class="modern-dashboard" :class="{ 'mobile-layout': isMobile }">
    <!-- 现代化顶部控制栏 -->
    <div class="dashboard-header" role="banner">
      <div class="header-content">
        <div class="header-title">
          <h1>数据仪表板</h1>
          <p class="subtitle">实时监控系统运行状态和关键指标</p>
        </div>

        <!-- 简化的快速筛选 -->
        <div class="quick-controls" v-if="!isMobile">
          <el-button-group class="time-range-group">
            <el-button
              v-for="range in quickTimeRanges"
              :key="range.value"
              :type="queryParams.timeRange === range.value ? 'primary' : 'default'"
              size="small"
              @click="setQuickTimeRange(range.value)"
              :aria-pressed="queryParams.timeRange === range.value"
            >
              {{ range.label }}
            </el-button>
          </el-button-group>

          <div class="header-actions">
            <el-tooltip content="刷新数据" placement="bottom">
              <el-button
                :icon="Refresh"
                circle
                @click="refreshData"
                :loading="loading"
                aria-label="刷新数据"
                />
            </el-tooltip>

            <el-tooltip content="导出报告" placement="bottom">
              <el-button
                :icon="Download"
                circle
                @click="exportDashboard"
                aria-label="导出报告"
                />
            </el-tooltip>

            <el-tooltip content="设置" placement="bottom">
              <el-button
                :icon="Setting"
                circle
                @click="showSettings"
                aria-label="打开设置"
                />
            </el-tooltip>

            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              @change="handleAutoRefreshChange"
              aria-label="切换自动刷新"
             />
          </div>
        </div>

        <!-- 移动端控制按钮 -->
        <div class="mobile-controls" v-if="isMobile">
          <el-button
            :icon="Filter"
            circle
            @click="showMobileFilters = true"
            aria-label="打开筛选"
            />
          <el-button
            :icon="Refresh"
            circle
            @click="refreshData"
            :loading="loading"
            aria-label="刷新数据"
            />
        </div>
      </div>
    </div>

    <!-- 高级筛选抽屉（移动端和桌面端共用） -->
    <el-drawer
      v-model="showAdvancedFilters"
      :title="isMobile ? '筛选条件' : '高级筛选'"
      :size="isMobile ? '100%' : '400px'"
      direction="rtl"
    >
      <div class="advanced-filters">
        <el-form :model="queryParams" label-position="top" size="default">
          <el-form-item label="时间范围">
            <el-select
              v-model="queryParams.timeRange"
              @change="handleTimeRangeChange"
              style="width: 100%"
            >
              <el-option
                v-for="option in timeRangeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
               />
            </el-select>
          </el-form-item>

          <el-form-item label="自定义时间" v-if="queryParams.timeRange === 'CUSTOM'">
            <el-date-picker
              v-model="customDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleCustomDateChange"
              style="width: 100%"
             />
          </el-form-item>

          <el-form-item label="数据类型">
            <el-select
              v-model="selectedDataTypes"
              multiple
              placeholder="选择数据类型"
              style="width: 100%"
              @change="handleDataTypeChange"
            >
              <el-option
                v-for="option in dataTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
               />
            </el-select>
          </el-form-item>

          <el-form-item label="部门筛选">
            <el-select
              v-model="queryParams.departments"
              multiple
              placeholder="选择部门"
              style="width: 100%"
              @change="refreshData"
            >
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.value"
                :label="dept.label"
                :value="dept.value"
               />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="filter-actions">
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="primary" @click="applyFilters">应用筛选</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 移动端快速筛选抽屉 -->
    <el-drawer
      v-model="showMobileFilters"
      title="快速筛选"
      size="100%"
      direction="btt"
      v-if="isMobile"
    >
      <div class="mobile-quick-filters">
        <div class="time-range-section">
          <h3>时间范围</h3>
          <div class="time-range-grid">
            <el-button
              v-for="range in quickTimeRanges"
              :key="range.value"
              :type="queryParams.timeRange === range.value ? 'primary' : 'default'"
              @click="setQuickTimeRange(range.value); showMobileFilters = false"
              class="time-range-btn"
            >
              {{ range.label }}
            </el-button>
          </div>
        </div>

        <div class="actions-section">
          <el-button @click="showAdvancedFilters = true; showMobileFilters = false">
            高级筛选
          </el-button>
          <el-button type="primary" @click="showMobileFilters = false">
            完成
          </el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 现代化概览卡片区域 -->
    <div class="overview-section" role="region" aria-label="关键指标概览">
      <div class="section-header">
        <h2>关键指标</h2>
        <div class="last-update">
          <el-icon><Clock /></el-icon>
          <span>最后更新：{{ formatTime(dashboardData?.lastUpdateTime || new Date().toISOString()) }}</span>
        </div>
      </div>

      <div class="overview-grid" :class="{ 'mobile-grid': isMobile }">
        <hr-modern-statistics-card
          v-for="card in overviewCards"
          :key="card.id"
          :card="card"
          @click="handleCardClick"
          :class="{ 'mobile-card': isMobile }"
        />
      </div>
    </div>

    <!-- 现代化预警信息区域 -->
    <div v-if="alerts.length > 0" class="alerts-section" role="region" aria-label="系统预警信息">
      <div class="alerts-header">
        <div class="alerts-title">
          <el-icon class="warning-icon"><Warning /></el-icon>
          <h3>系统预警</h3>
          <el-badge :value="alerts.length" :type="getAlertsBadgeType()" class="alerts-badge"  />
        </div>
        <el-button
          size="small"
          text
          type="primary"
          @click="showAllAlerts"
          aria-label="查看所有预警信息"
        >
          查看全部 <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>

      <div class="alerts-container">
        <TransitionGroup name="alert-list" tag="div" class="alerts-list">
          <div
            v-for="alert in alerts.slice(0, isMobile ? 2 : 3)"
            :key="alert.id"
            class="modern-alert-item"
            :class="[`alert-${alert.type}`, `severity-${alert.severity}`]"
            role="alert"
            :aria-live="alert.severity === 'critical' ? 'assertive' : 'polite'"
          >
            <div class="alert-icon">
              <el-icon>
                <Warning v-if="alert.type === 'warning'" />
                <CircleClose v-else-if="alert.type === 'error'" />
                <InfoFilled v-else />
              </el-icon>
            </div>

            <div class="alert-content">
              <div class="alert-main">
                <h4 class="alert-title">{{ alert.title }}</h4>
                <p class="alert-message">{{ alert.message }}</p>
              </div>

              <div class="alert-meta">
                <span class="alert-source">{{ alert.source }}</span>
                <span class="alert-time">{{ formatTime(alert.createTime) }}</span>
              </div>
            </div>

            <div class="alert-actions">
              <el-button
                v-if="!alert.acknowledged"
                size="small"
                type="primary"
                text
                @click="acknowledgeAlert(alert.id)"
                :aria-label="`确认预警：${alert.title}`"
              >
                确认
              </el-button>

              <el-button
                size="small"
                text
                @click="dismissAlert(alert.id)"
                :aria-label="`忽略预警：${alert.title}`"
              >
                忽略
              </el-button>
            </div>
          </div>
        </TransitionGroup>
      </div>
    </div>

    <!-- 现代化图表展示区域 -->
    <div class="charts-section" role="region" aria-label="数据图表分析">
      <div class="section-header">
        <h2>数据分析</h2>
        <div class="chart-view-toggle" v-if="!isMobile">
          <el-radio-group v-model="chartViewMode" size="small">
            <el-radio-button value="grid">网格视图</el-radio-button>
            <el-radio-button value="carousel">轮播视图</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 桌面端网格布局 -->
      <div v-if="!isMobile && chartViewMode === 'grid'" class="charts-grid">
        <div class="chart-row">
          <!-- 机构变更趋势图 -->
          <hr-modern-chart-card
            title="机构变更趋势"
            subtitle="近期机构变更数据趋势分析"
            chart-id="changes-trend"
            :loading="chartsLoading.changesTrend"
            @drill-down="drillDownChart"
            @export="exportChart"
            class="chart-card-large"
          >
            <div
              ref="changesTrendChartRef"
              class="modern-chart-container"
              role="img"
              :aria-label="`机构变更趋势图，显示${queryParams.timeRange}的数据`"
            ></div>
          </hr-modern-chart-card>

          <!-- 临时机构状态分布 -->
          <hr-modern-chart-card
            title="临时机构状态分布"
            subtitle="各状态临时机构数量分布"
            chart-id="institution-status"
            :loading="chartsLoading.institutionStatus"
            @drill-down="drillDownChart"
            @export="exportChart"
            class="chart-card-large"
          >
            <div
              ref="institutionStatusChartRef"
              class="modern-chart-container"
              role="img"
              aria-label="临时机构状态分布饼图"
            ></div>
          </hr-modern-chart-card>
        </div>
      </div>

      <!-- 移动端轮播布局 -->
      <div v-if="isMobile || chartViewMode === 'carousel'" class="charts-carousel">
        <el-carousel
          :interval="0"
          arrow="always"
          indicator-position="outside"
          height="400px"
          :autoplay="false"
        >
          <el-carousel-item>
            <hr-modern-chart-card
              title="机构变更趋势"
              subtitle="近期机构变更数据趋势分析"
              chart-id="changes-trend"
              :loading="chartsLoading.changesTrend"
              @drill-down="drillDownChart"
              @export="exportChart"
              class="carousel-chart-card"
            >
              <div
                ref="changesTrendChartRef"
                class="modern-chart-container mobile-chart"
                role="img"
                :aria-label="`机构变更趋势图，显示${queryParams.timeRange}的数据`"
              ></div>
            </hr-modern-chart-card>
          </el-carousel-item>

          <el-carousel-item>
            <hr-modern-chart-card
              title="临时机构状态分布"
              subtitle="各状态临时机构数量分布"
              chart-id="institution-status"
              :loading="chartsLoading.institutionStatus"
              @drill-down="drillDownChart"
              @export="exportChart"
              class="carousel-chart-card"
            >
              <div
                ref="institutionStatusChartRef"
                class="modern-chart-container mobile-chart"
                role="img"
                aria-label="临时机构状态分布饼图"
              ></div>
            </hr-modern-chart-card>
          </el-carousel-item>

          <!-- 移动端额外图表轮播项 -->
          <el-carousel-item v-if="isMobile || chartViewMode === 'carousel'">
            <hr-modern-chart-card
              title="变更类型对比"
              subtitle="各类型变更数量统计"
              chart-id="change-type-comparison"
              :loading="chartsLoading.changeType"
              @drill-down="drillDownChart"
              @export="exportChart"
              class="carousel-chart-card"
            >
              <div
                ref="changeTypeChartRef"
                class="modern-chart-container mobile-chart"
                role="img"
                aria-label="变更类型对比柱状图"
              ></div>
            </hr-modern-chart-card>
          </el-carousel-item>

          <el-carousel-item v-if="isMobile || chartViewMode === 'carousel'">
            <hr-modern-chart-card
              title="部门活跃度分析"
              subtitle="各部门活跃度热力图分析"
              chart-id="department-heatmap"
              :loading="chartsLoading.departmentHeatmap"
              @drill-down="drillDownChart"
              @export="exportChart"
              class="carousel-chart-card"
            >
              <template #header-actions>
                <el-select
                  v-model="heatmapMetric"
                  size="small"
                  @change="updateHeatmapChart"
                  style="width: 120px;"
                  aria-label="选择热力图指标"
                >
                  <el-option label="变更数量" value="changes"  />
                  <el-option label="临时机构" value="institutions"  />
                  <el-option label="活跃度" value="activity"  />
                </el-select>
              </template>

              <div
                ref="departmentHeatmapChartRef"
                class="modern-chart-container mobile-chart heatmap-chart"
                role="img"
                :aria-label="`部门${heatmapMetric}热力图`"
              ></div>
            </ModernChartCard>
          </el-carousel-item>
        </el-carousel>
      </div>

      <!-- 第二行图表 -->
      <div v-if="!isMobile && chartViewMode === 'grid'" class="charts-grid secondary-charts">
        <div class="chart-row">
          <!-- 变更类型对比 -->
          <ModernChartCard
            title="变更类型对比"
            subtitle="各类型变更数量统计"
            chart-id="change-type-comparison"
            :loading="chartsLoading.changeType"
            @drill-down="drillDownChart"
            @export="exportChart"
            class="chart-card-medium"
          >
            <div
              ref="changeTypeChartRef"
              class="modern-chart-container"
              role="img"
              aria-label="变更类型对比柱状图"
            ></div>
          </ModernChartCard>

          <!-- 部门活跃度热力图 -->
          <ModernChartCard
            title="部门活跃度分析"
            subtitle="各部门活跃度热力图分析"
            chart-id="department-heatmap"
            :loading="chartsLoading.departmentHeatmap"
            @drill-down="drillDownChart"
            @export="exportChart"
            class="chart-card-large"
          >
            <template #header-actions>
              <el-select
                v-model="heatmapMetric"
                size="small"
                @change="updateHeatmapChart"
                style="width: 120px;"
                aria-label="选择热力图指标"
              >
                <el-option label="变更数量" value="changes"  />
                <el-option label="临时机构" value="institutions"  />
                <el-option label="活跃度" value="activity"  />
              </el-select>
            </template>

            <div
              ref="departmentHeatmapChartRef"
              class="modern-chart-container heatmap-chart"
              role="img"
              :aria-label="`部门${heatmapMetric}热力图`"
            ></div>
          </ModernChartCard>
        </div>
      </div>
    </div>

    <!-- 现代化底部信息区域 -->
    <div class="bottom-section" role="region" aria-label="实时监控和快速操作">
      <div class="section-header">
        <h2>实时监控</h2>
        <div class="system-status">
          <div class="status-indicator" :class="systemStatus.type">
            <div class="status-dot"></div>
            <span>{{ systemStatus.text }}</span>
          </div>
        </div>
      </div>

      <div class="bottom-grid" :class="{ 'mobile-bottom': isMobile }">
        <!-- 实时指标卡片 -->
        <div class="metrics-section">
          <div class="metrics-grid">
            <div
              v-for="metric in realTimeMetrics"
              :key="metric.key"
              class="modern-metric-card"
              :class="[`status-${metric.status}`]"
              role="status"
              :aria-label="`${metric.label}: ${metric.value}${metric.unit}`"
            >
              <div class="metric-icon">
                <el-icon>
                  <Timer v-if="metric.key === 'response-time'" />
                  <User v-else-if="metric.key === 'active-users'" />
                  <DataAnalysis v-else-if="metric.key === 'data-accuracy'" />
                  <Setting v-else />
                </el-icon>
              </div>

              <div class="metric-content">
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-value">
                  {{ metric.value }}
                  <span class="metric-unit">{{ metric.unit }}</span>
                </div>

                <div class="metric-status-bar">
                  <div
                    class="status-fill"
                    :style="{ width: getMetricPercentage(metric) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作区域 -->
        <div class="actions-section" v-if="!isMobile">
          <h3>快速操作</h3>
          <div class="quick-actions-grid">
            <button
              v-for="action in quickActions"
              :key="action.key"
              class="modern-action-button"
              :class="[`action-${action.type}`]"
              @click="handleQuickAction(action.key)"
              :aria-label="action.label"
            >
              <div class="action-icon">
                <el-icon>
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <span class="action-label">{{ action.label }}</span>
            </button>
          </div>
        </div>

        <!-- 最近活动时间线 -->
        <div class="activities-section" v-if="!isMobile">
          <h3>最近活动</h3>
          <div class="modern-timeline">
            <div
              v-for="(activity, index) in recentActivities.slice(0, 5)"
              :key="activity.id"
              class="timeline-item"
              :class="{ 'latest': index === 0 }"
            >
              <div class="timeline-dot" :class="`type-${activity.type}`"></div>
              <div class="timeline-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 移动端快速操作抽屉 -->
      <div v-if="isMobile" class="mobile-quick-actions">
        <el-button
          type="primary"
          size="large"
          @click="showMobileActions = true"
          class="mobile-actions-trigger"
        >
          <el-icon><Operation /></el-icon>
          快速操作
        </el-button>
      </div>
    </div>

    <!-- 移动端快速操作抽屉 -->
    <el-drawer
      v-model="showMobileActions"
      title="快速操作"
      size="100%"
      direction="btt"
      v-if="isMobile"
    >
      <div class="mobile-actions-content">
        <div class="actions-grid">
          <button
            v-for="action in quickActions"
            :key="action.key"
            class="mobile-action-button"
            @click="handleQuickAction(action.key); showMobileActions = false"
          >
            <div class="action-icon">
              <el-icon>
                <component :is="action.icon" />
              </el-icon>
            </div>
            <span class="action-label">{{ action.label }}</span>
          </button>
        </div>

        <div class="recent-activities-mobile">
          <h3>最近活动</h3>
          <div class="activities-list">
            <div
              v-for="activity in recentActivities.slice(0, 8)"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-main">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
              </div>
              <div class="activity-time">{{ formatTime(activity.time) }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 对话框组件 -->
    <DashboardSettingsDialog
      v-model="settingsDialogVisible"
      @save="handleSettingsSave"
    />

    <AlertsDialog
      v-model="alertsDialogVisible"
      :alerts="alerts"
      @acknowledge="acknowledgeAlert"
    />

    <ChartDrillDownDialog
      v-model="drillDownDialogVisible"
      :chart-id="currentDrillDownChart"
      :params="queryParams"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'StatisticsDashboard'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, onUnmounted, onBeforeUnmount, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Download,
  Setting,
  ZoomIn,
  Filter,
  Clock,
  Warning,
  ArrowRight,
  CircleClose,
  InfoFilled,
  Timer,
  User,
  DataAnalysis,
  Operation,
  FullScreen,
  MoreFilled,
  Share,
  Plus,
  Check,
  Minus,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { dashboardApi, integratedStatsApi } from '@/api/dashboard'
import type {
  DashboardData,
  DashboardQueryParams,
  StatisticsCard as StatisticsCardType,
  AlertData,
  RealTimeData,
  TimeRange
} from '@/types/dashboard'
import {
  DataType,
  TimeRange as TimeRangeEnum
} from '@/types/dashboard'
import { useMobile } from '@/composables/useMobile'
import HrModernStatisticsCard from '@/components/dashboard/HrModernStatisticsCard.vue'
import HrModernChartCard from '@/components/dashboard/HrModernChartCard.vue'
import HrDashboardSettingsDialog from '@/components/dashboard/HrDashboardSettingsDialog.vue'
import HrAlertsDialog from '@/components/dashboard/HrAlertsDialog.vue'
import HrChartDrillDownDialog from '@/components/dashboard/HrChartDrillDownDialog.vue'

// 移动端适配
const {isMobile: _isMobile} =  useMobile()

// 响应式数据
const loading 
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

.mobile-layout {
  background: #f8f9fa;
}

/* 顶部控制栏样式 */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 20px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.mobile-layout .header-content {
  padding: 0 16px;
  gap: 16px;
}

.header-title h1 {
  font-size: 28px;
  font-weight: 700;
  color: #262626;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.mobile-layout .header-title h1 {
  font-size: 22px;
}

.subtitle {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
  line-height: 1.4;
}

.mobile-layout .subtitle {
  font-size: 13px;
}

.quick-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.time-range-group {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 高级筛选样式 */
.advanced-filters {
  padding: 24px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.filter-actions .el-button {
  flex: 1;
}

/* 移动端快速筛选样式 */
.mobile-quick-filters {
  padding: 24px;
}

.time-range-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 16px 0;
}

.time-range-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.time-range-btn {
  height: 48px;
  border-radius: 8px;
  font-weight: 500;
}

.actions-section {
  display: flex;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.actions-section .el-button {
  flex: 1;
  height: 48px;
}

/* 概览区域样式 */
.overview-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: 32px 24px;
}

.mobile-layout .overview-section {
  padding: 24px 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.mobile-layout .section-header h2 {
  font-size: 18px;
}

.last-update {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #8c8c8c;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.mobile-grid {
  grid-template-columns: 1fr;
  gap: 16px;
}

/* 预警区域样式 */
.alerts-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px 32px;
}

.mobile-layout .alerts-section {
  padding: 0 16px 24px;
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.alerts-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.alerts-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.warning-icon {
  color: #faad14;
  font-size: 20px;
}

.alerts-badge {
  margin-left: 8px;
}

.alerts-container {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modern-alert-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  background: #fafafa;
  transition: all 0.3s ease;
}

.modern-alert-item:hover {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.alert-warning {
  border-left: 4px solid #faad14;
}

.alert-error {
  border-left: 4px solid #ff4d4f;
}

.alert-info {
  border-left: 4px solid #1890ff;
}

.severity-critical {
  background: #fff2f0;
}

.severity-high {
  background: #fffbe6;
}

.alert-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #ffffff;
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-main {
  margin-bottom: 8px;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 4px 0;
}

.alert-message {
  font-size: 13px;
  color: #595959;
  margin: 0;
  line-height: 1.4;
}

.alert-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #8c8c8c;
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

/* 动画效果 */
.alert-list-enter-active,
.alert-list-leave-active {
  transition: all 0.3s ease;
}

.alert-list-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.alert-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* 图表区域样式 */
.charts-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px 32px;
}

.mobile-layout .charts-section {
  padding: 0 16px 24px;
}

.chart-view-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.charts-grid {
  margin-top: 24px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.secondary-charts .chart-row {
  grid-template-columns: 1fr 2fr;
}

.chart-card-large {
  grid-column: span 1;
}

.chart-card-medium {
  grid-column: span 1;
}

.charts-carousel {
  margin-top: 24px;
}

.carousel-chart-card {
  height: 100%;
  margin: 0 8px;
}

/* 底部区域样式 */
.bottom-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px 40px;
}

.mobile-layout .bottom-section {
  padding: 0 16px 24px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
}

.status-indicator.success {
  background: #f6ffed;
  color: #52c41a;
}

.status-indicator.warning {
  background: #fffbe6;
  color: #faad14;
}

.status-indicator.danger {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.bottom-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 24px;
  margin-top: 24px;
}

.mobile-bottom {
  grid-template-columns: 1fr;
  gap: 20px;
}

/* 指标卡片样式 */
.metrics-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 16px 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.modern-metric-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;
}

.modern-metric-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #e6f7ff;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f9ff;
  color: #1890ff;
  flex-shrink: 0;
}

.status-warning .metric-icon {
  background: #fffbe6;
  color: #faad14;
}

.status-normal .metric-icon {
  background: #f6ffed;
  color: #52c41a;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-label {
  font-size: 13px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 20px;
  font-weight: 700;
  color: #262626;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.metric-unit {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
}

.metric-status-bar {
  width: 100%;
  height: 4px;
  background: #f5f5f5;
  border-radius: 2px;
  margin-top: 8px;
  overflow: hidden;
}

.status-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.status-warning .status-fill {
  background: linear-gradient(90deg, #faad14 0%, #ffc53d 100%);
}

/* 快速操作样式 */
.actions-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 16px 0;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.modern-action-button {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.modern-action-button:hover {
  background: #f8f9fa;
  border-color: #e6f7ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f9ff;
  color: #1890ff;
}

.action-primary .action-icon {
  background: #e6f7ff;
  color: #1890ff;
}

.action-success .action-icon {
  background: #f6ffed;
  color: #52c41a;
}

.action-warning .action-icon {
  background: #fffbe6;
  color: #faad14;
}

.action-label {
  font-size: 12px;
  font-weight: 500;
  color: #595959;
  text-align: center;
  line-height: 1.3;
}

/* 活动时间线样式 */
.activities-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 16px 0;
}

.modern-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 24px;
  bottom: -8px;
  width: 1px;
  background: #f0f0f0;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #d9d9d9;
  flex-shrink: 0;
  margin-top: 4px;
  position: relative;
  z-index: 1;
}

.timeline-item.latest .timeline-dot {
  background: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
}

.type-success .timeline-dot {
  background: #52c41a;
}

.type-warning .timeline-dot {
  background: #faad14;
}

.type-error .timeline-dot {
  background: #ff4d4f;
}

.timeline-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
  line-height: 1.4;
}

.activity-description {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
  line-height: 1.4;
}

.activity-time {
  font-size: 11px;
  color: #bfbfbf;
}

/* 移动端快速操作样式 */
.mobile-quick-actions {
  margin-top: 24px;
  text-align: center;
}

.mobile-actions-trigger {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
}

.mobile-actions-content {
  padding: 24px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 32px;
}

.mobile-action-button {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-action-button:hover {
  background: #f8f9fa;
  border-color: #e6f7ff;
}

.mobile-action-button .action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
}

.mobile-action-button .action-label {
  font-size: 13px;
}

.recent-activities-mobile h3 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 16px 0;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.activity-main {
  flex: 1;
  min-width: 0;
}

.activity-item .activity-title {
  font-size: 14px;
  margin-bottom: 4px;
}

.activity-item .activity-description {
  font-size: 13px;
}

.activity-item .activity-time {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .secondary-charts .chart-row {
    grid-template-columns: 1fr;
  }

  .bottom-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 16px 0;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .bottom-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .header-title h1 {
    font-size: 20px;
  }

  .overview-section,
  .alerts-section,
  .charts-section,
  .bottom-section {
    padding-left: 12px;
    padding-right: 12px;
  }

  .modern-alert-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .alert-actions {
    flex-direction: row;
    justify-content: flex-end;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-dashboard {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .dashboard-header {
    background: rgba(31, 31, 31, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.06);
  }

  .header-title h1 {
    color: #ffffff;
  }

  .subtitle {
    color: #a6a6a6;
  }

  .alerts-container,
  .modern-metric-card,
  .modern-action-button,
  .activity-item {
    background: #1f1f1f;
    border-color: #303030;
  }

  .modern-alert-item {
    background: #2a2a2a;
    border-color: #404040;
  }

  .modern-alert-item:hover {
    background: #1f1f1f;
  }

  .section-header h2,
  .alerts-title h3,
  .metrics-section h3,
  .actions-section h3,
  .activities-section h3 {
    color: #ffffff;
  }

  .alert-title,
  .activity-title {
    color: #ffffff;
  }

  .alert-message,
  .activity-description {
    color: #d9d9d9;
  }

  .metric-value {
    color: #ffffff;
  }

  .action-label {
    color: #d9d9d9;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .modern-dashboard {
    background: #ffffff;
  }

  .dashboard-header {
    background: #ffffff;
    border-bottom: 2px solid #000000;
  }

  .modern-statistics-card,
  .modern-chart-card,
  .modern-metric-card,
  .modern-action-button {
    border-width: 2px;
    border-color: #000000;
  }

  .header-title h1,
  .section-header h2,
  .alert-title,
  .metric-value,
  .activity-title {
    color: #000000;
    font-weight: 700;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .modern-statistics-card,
  .modern-chart-card,
  .modern-metric-card,
  .modern-action-button,
  .modern-alert-item,
  .alert-list-enter-active,
  .alert-list-leave-active {
    transition: none;
  }

  .modern-statistics-card.clickable:hover,
  .modern-action-button:hover {
    transform: none;
  }

  .status-dot {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .modern-dashboard {
    background: #ffffff;
  }

  .dashboard-header {
    background: #ffffff;
    border-bottom: 1px solid #000000;
    position: static;
  }

  .header-actions,
  .action-buttons,
  .mobile-controls,
  .mobile-quick-actions {
    display: none;
  }

  .modern-statistics-card,
  .modern-chart-card,
  .alerts-container {
    box-shadow: none;
    border: 1px solid #000000;
  }

  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-row {
    grid-template-columns: 1fr;
  }

  .bottom-grid {
    grid-template-columns: 1fr;
  }
}

/* 焦点样式 */
.modern-statistics-card:focus,
.modern-action-button:focus,
button:focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

/* 键盘导航支持 */
.modern-statistics-card[tabindex="0"],
.modern-action-button {
  cursor: pointer;
}

.modern-statistics-card[tabindex="-1"] {
  cursor: default;
}

/* 屏幕阅读器支持 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
