<template>
  <div class="dashboard-designer">
    <!-- 顶部工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="saveDashboard">
          <el-icon><DocumentAdd /></el-icon>
          保存仪表盘
        </el-button>
        <el-button @click="previewDashboard">
          <el-icon><View /></el-icon>
          预览
        </el-button>
        <el-button @click="clearCanvas">
          <el-icon><Delete /></el-icon>
          清空画布
        </el-button>
      </div>
      <div class="toolbar-center">
        <el-input
          v-model="dashboardName"
          placeholder="仪表盘名称"
          style="width: 300px"
          />
      </div>
      <div class="toolbar-right">
        <el-button @click="showSettings = true">
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
        <el-button @click="exportDashboard">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 主设计区域 -->
    <div class="designer-container">
      <!-- 左侧组件库 -->
      <div class="component-panel">
        <h3>组件库</h3>
        <div class="component-list">
          <div
            v-for="component in componentLibrary"
            :key="component.type"
            class="component-item"
            draggable="true"
            @dragstart="handleDragStart($event, component)"
          >
            <el-icon :size="24">
              <component :is="component.icon" />
            </el-icon>
            <span>{{ component.name }}</span>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <div
          ref="canvasRef"
          class="design-canvas"
          @drop="handleDrop"
          @dragover="handleDragOver"
        >
          <!-- 网格背景 -->
          <div class="grid-background" v-if="showGrid"></div>
          
          <!-- 已添加的组件 -->
          <div
            v-for="widget in widgets"
            :key="widget.id"
            class="widget-wrapper"
            :class="{ active: activeWidgetId === widget.id }"
            :style="{
              left: widget.x + 'px',
              top: widget.y + 'px',
              width: widget.width + 'px',
              height: widget.height + 'px'
            }"
            @click="selectWidget(widget)"
          >
            <!-- 缩放手柄 -->
            <div
              v-if="activeWidgetId === widget.id"
              class="resize-handle resize-handle-nw"
              @mousedown="startResize($event, widget, 'nw')"
            ></div>
            <div
              v-if="activeWidgetId === widget.id"
              class="resize-handle resize-handle-ne"
              @mousedown="startResize($event, widget, 'ne')"
            ></div>
            <div
              v-if="activeWidgetId === widget.id"
              class="resize-handle resize-handle-sw"
              @mousedown="startResize($event, widget, 'sw')"
            ></div>
            <div
              v-if="activeWidgetId === widget.id"
              class="resize-handle resize-handle-se"
              @mousedown="startResize($event, widget, 'se')"
            ></div>
            
            <!-- 移动手柄 -->
            <div
              class="move-handle"
              @mousedown="startMove($event, widget)"
            >
              <el-icon><Rank /></el-icon>
            </div>
            
            <!-- 删除按钮 -->
            <el-button
              v-if="activeWidgetId === widget.id"
              class="delete-button"
              type="danger"
              size="small"
              circle
              @click.stop="removeWidget(widget.id)"
            >
              <el-icon><Close /></el-icon>
            </el-button>
            
            <!-- 组件内容 -->
            <component
              :is="getComponentByType(widget.type)"
              v-bind="widget.config"
              :data="widget.data"
              class="widget-content"
            />
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="property-panel" v-if="activeWidget">
        <h3>属性配置</h3>
        <el-form label-width="80px" size="small">
          <el-form-item label="组件ID">
            <el-input v-model="activeWidget.id" disabled   />
          </el-form-item>
          <el-form-item label="组件名称">
            <el-input v-model="activeWidget.name"   />
          </el-form-item>
          <el-form-item label="X坐标">
            <el-input-number v-model="activeWidget.x" :min="0"   />
          </el-form-item>
          <el-form-item label="Y坐标">
            <el-input-number v-model="activeWidget.y" :min="0"   />
          </el-form-item>
          <el-form-item label="宽度">
            <el-input-number v-model="activeWidget.width" :min="100"   />
          </el-form-item>
          <el-form-item label="高度">
            <el-input-number v-model="activeWidget.height" :min="100"   />
          </el-form-item>
          
          <!-- 图表配置 -->
          <el-divider>图表配置</el-divider>
          <el-button type="primary" size="small" @click="configureChart">
            配置图表
          </el-button>
          
          <!-- 数据配置 -->
          <el-divider>数据配置</el-divider>
          <el-form-item label="数据源">
            <el-select v-model="activeWidget.dataSource">
              <el-option label="静态数据" value="static"  />
              <el-option label="API接口" value="api"  />
              <el-option label="实时数据" value="realtime"  />
            </el-select>
          </el-form-item>
          <el-form-item label="刷新间隔" v-if="activeWidget.dataSource !== 'static'">
            <el-input-number
              v-model="activeWidget.refreshInterval"
              :min="0"
              :step="1000"
              />
            <span class="form-tip">毫秒</span>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 仪表盘设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="仪表盘设置"
      width="600px"
    >
      <el-form label-width="120px">
        <el-form-item label="仪表盘名称">
          <el-input v-model="dashboardConfig.name"   />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="dashboardConfig.description"
            type="textarea"
            :rows="3"
            />
        </el-form-item>
        <el-form-item label="布局模式">
          <el-radio-group v-model="dashboardConfig.layoutMode">
            <el-radio label="free">自由布局</el-radio>
            <el-radio label="grid">栅格布局</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="背景颜色">
          <el-color-picker v-model="dashboardConfig.backgroundColor"  />
        </el-form-item>
        <el-form-item label="显示网格">
          <el-switch v-model="showGrid"  />
        </el-form-item>
        <el-form-item label="网格大小" v-if="showGrid">
          <el-input-number
            v-model="gridSize"
            :min="10"
            :max="50"
            :step="5"
            />
        </el-form-item>
        <el-form-item label="自动保存">
          <el-switch v-model="dashboardConfig.autoSave"  />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="applySettings">应用</el-button>
      </template>
    </el-dialog>

    <!-- 图表配置对话框 -->
    <el-dialog
      v-model="showChartConfig"
      title="图表配置"
      width="1200px"
      :close-on-click-modal="false"
    >
      <ChartConfigPanel
        v-if="showChartConfig && activeWidget"
        :chart-type="activeWidget.type"
        v-model="activeWidget.config"
        @preview="handleChartPreview"
        @save="handleChartSave"
        @cancel="showChartConfig = false"
      />
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreview"
      title="仪表盘预览"
      fullscreen
      :show-close="true"
    >
      <DashboardPreview
        v-if="showPreview"
        :widgets="widgets"
        :config="dashboardConfig"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { dashboardApi, type DashboardConfig } from '@/api/dashboard'
import {
  DocumentAdd,
  View,
  Delete,
  Setting,
  Download,
  Rank,
  Close,
  TrendCharts,
  Histogram,
  HrPieChart as PieChartIcon,
  DataAnalysis,
  HrScatterChart as ScatterChartIcon,
  DataBoard,
  Timer,
  List
} from '@element-plus/icons-vue'
import HrChartConfigPanel from '@/components/visualization/HrChartConfigPanel.vue'
import HrLineChart from '@/components/visualization/HrLineChart.vue'
import HrBarChart from '@/components/visualization/HrBarChart.vue'
import HrPieChart from '@/components/visualization/HrPieChart.vue'
import HrRadarChart from '@/components/visualization/HrRadarChart.vue'
import HrScatterChart from '@/components/visualization/HrScatterChart.vue'
import DashboardPreview from './components/DashboardPreview.vue'
import StatCard from './components/StatCard.vue'
import GaugeChart from './components/GaugeChart.vue'
import DataTable from './components/DataTable.vue'

// 组件库定义
const componentLibrary = [
  { type: 'line', name: 'HrHr折线图', icon: TrendCharts },
  { type: 'bar', name: '柱状图', icon: Histogram },
  { type: 'pie', name: '饼图', icon: PieChartIcon },
  { type: 'radar', name: '雷达图', icon: DataAnalysis },
  { type: 'scatter', name: '散点图', icon: ScatterChartIcon },
  { type: 'card', name: '数据卡片', icon: DataBoard },
  { type: 'gauge', name: '仪表盘', icon: Timer },
  { type: 'table', name: '数据表格', icon: List }
]

// 组件映射
const componentMap: Record<string, unknown> = {
  line: LineChart,
  bar: BarChart,
  pie: PieChart,
  radar: RadarChart,
  scatter: ScatterChart,
  card: StatCard,
  gauge: GaugeChart,
  table: DataTable
}

// 响应式数据
const canvasRef = ref<HTMLElement>()
const dashboardName = ref('新建仪表盘')
const showSettings = ref(false)
const showChartConfig = ref(false)
const showPreview = ref(false)
const showGrid = ref(true)
const gridSize = ref(20)
const widgets = ref<any[]>([])
const activeWidgetId = ref<string | null>(null)
const draggedComponent = ref<unknown>(null)

// 仪表盘配置
const dashboardConfig = reactive({
  name: '新建仪表盘',
  description: '',
  layoutMode: 'free',
  backgroundColor: '#f5f7fa',
  autoSave: true
})

// 计算属性
const activeWidget = computed(() => {
  return widgets.value.find(w => w.id === activeWidgetId.value)
})

// 拖拽处理
   
const handleDragStart = (event: DragEvent, component: unknown) => {
  draggedComponent.value = component
  event.dataTransfer!.effectAllowed = 'copy'
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  
  if (!draggedComponent.value || !canvasRef.value) return
  
  const rect = canvasRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // 创建新组件
  const widget = {
    id: `widget-${Date.now()}`,
    type: draggedComponent.value.type,
    name: draggedComponent.value.name,
    x: Math.round(x / gridSize.value) * gridSize.value,
    y: Math.round(y / gridSize.value) * gridSize.value,
    width: 400,
    height: 300,
    config: getDefaultConfig(draggedComponent.value.type),
    data: null,
    dataSource: 'static',
    refreshInterval: 0
  }
  
  widgets.value.push(widget)
  activeWidgetId.value = widget.id
  draggedComponent.value = null
}

// 获取组件默认配置
const getDefaultConfig = (type: string) => {
  switch (type) {
    case 'line':
      return {
        title: '折线图',
        smooth: true,
        showArea: false
      }
    case 'bar':
      return {
        title: '柱状图',
        horizontal: false,
        showLabel: false
      }
    case 'pie':
      return {
        title: '饼图',
        donut: false,
        rose: false
      }
    case 'radar':
      return {
        title: '雷达图',
        shape: 'polygon'
      }
    case 'scatter':
      return {
        title: '散点图',
        symbolSize: 10
      }
    default:
      return { title: '未配置' }
  }
}

// 获取组件
const getComponentByType = (type: string) => {
  return componentMap[type] || 'div'
}

// 选中组件
   
const selectWidget = (widget: unknown) => {
  activeWidgetId.value = widget.id
}

// 删除组件
const removeWidget = (id: string) => {
  ElMessageBox.confirm('确定要删除该组件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    widgets.value = widgets.value.filter(w => w.id !== id)
    if (activeWidgetId.value === id) {
      activeWidgetId.value = null
    }
    ElMessage.success('删除成功')
  })
}

// 移动相关
   
let moveData: unknown = null

   
const startMove = (event: MouseEvent, widget: unknown) => {
  event.preventDefault()
  event.stopPropagation()
  
  moveData = {
    widget,
    startX: event.clientX,
    startY: event.clientY,
    originalX: widget.x,
    originalY: widget.y
  }
  
  document.addEventListener('mousemove', handleMove)
  document.addEventListener('mouseup', endMove)
}

const handleMove = (event: MouseEvent) => {
  if (!moveData) return
  
  const deltaX = event.clientX - moveData.startX
  const deltaY = event.clientY - moveData.startY
  
  let newX = moveData.originalX + deltaX
  let newY = moveData.originalY + deltaY
  
  // 网格对齐
  if (showGrid.value) {
    newX = Math.round(newX / gridSize.value) * gridSize.value
    newY = Math.round(newY / gridSize.value) * gridSize.value
  }
  
  // 边界限制
  newX = Math.max(0, newX)
  newY = Math.max(0, newY)
  
  moveData.widget.x = newX
  moveData.widget.y = newY
}

const endMove = () => {
  moveData = null
  document.removeEventListener('mousemove', handleMove)
  document.removeEventListener('mouseup', endMove)
}

// 缩放相关
   
let resizeData: unknown = null

   
const startResize = (event: MouseEvent, widget: unknown, direction: string) => {
  event.preventDefault()
  event.stopPropagation()
  
  resizeData = {
    widget,
    direction,
    startX: event.clientX,
    startY: event.clientY,
    originalWidth: widget.width,
    originalHeight: widget.height,
    originalX: widget.x,
    originalY: widget.y
  }
  
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', endResize)
}

const handleResize = (event: MouseEvent) => {
  if (!resizeData) return
  
  const deltaX = event.clientX - resizeData.startX
  const deltaY = event.clientY - resizeData.startY
  
  let newWidth = resizeData.originalWidth
  let newHeight = resizeData.originalHeight
  let newX = resizeData.originalX
  let newY = resizeData.originalY
  
  // 根据方向调整
  switch (resizeData.direction) {
    case 'se':
      newWidth = resizeData.originalWidth + deltaX
      newHeight = resizeData.originalHeight + deltaY
      break
    case 'sw':
      newWidth = resizeData.originalWidth - deltaX
      newHeight = resizeData.originalHeight + deltaY
      newX = resizeData.originalX + deltaX
      break
    case 'ne':
      newWidth = resizeData.originalWidth + deltaX
      newHeight = resizeData.originalHeight - deltaY
      newY = resizeData.originalY + deltaY
      break
    case 'nw':
      newWidth = resizeData.originalWidth - deltaX
      newHeight = resizeData.originalHeight - deltaY
      newX = resizeData.originalX + deltaX
      newY = resizeData.originalY + deltaY
      break
  }
  
  // 最小尺寸限制
  newWidth = Math.max(100, newWidth)
  newHeight = Math.max(100, newHeight)
  
  // 更新组件
  resizeData.widget.width = newWidth
  resizeData.widget.height = newHeight
  resizeData.widget.x = newX
  resizeData.widget.y = newY
}

const endResize = () => {
  resizeData = null
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', endResize)
}

// 配置图表
const configureChart = () => {
  if (!activeWidget.value) return
  showChartConfig.value = true
}

// 图表配置处理
   
const handleChartPreview = (config: unknown) => {
  if (!activeWidget.value) return
  activeWidget.value.config = config
}

   
const handleChartSave = (config: unknown) => {
  if (!activeWidget.value) return
  activeWidget.value.config = config
  showChartConfig.value = false
  ElMessage.success('图表配置已保存')
}

// 应用设置
const applySettings = () => {
  dashboardName.value = dashboardConfig.name
  canvasRef.value!.style.backgroundColor = dashboardConfig.backgroundColor
  showSettings.value = false
  ElMessage.success('设置已应用')
}

// 清空画布
const clearCanvas = () => {
  ElMessageBox.confirm('确定要清空画布吗？所有组件将被删除。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    widgets.value = []
    activeWidgetId.value = null
    ElMessage.success('画布已清空')
  })
}

// 保存仪表盘
const saveDashboard = async () => {
  try {
    const dashboardData: DashboardConfig = {
      name: dashboardName.value,
      description: '自定义仪表盘',
      layout: dashboardConfig.layout,
      theme: dashboardConfig.theme,
      refreshInterval: dashboardConfig.autoRefresh ? dashboardConfig.refreshInterval : undefined,
      widgets: widgets.value.map(w => ({
        id: w.id,
        type: w.type,
        name: w.name,
        dataSource: w.config?.dataSource || 'static',
        endpoint: w.config?.endpoint,
        params: w.config,
        refreshInterval: w.config?.refreshInterval
      })),
      isPublic: false
    }
    
    // 调用API保存
    const savedDashboard = await dashboardApi.saveDashboard(dashboardData)
    console.log('保存仪表盘成功:', savedDashboard)
    
    ElMessage.success('仪表盘保存成功')
  } catch (__error) {
    ElMessage.error('保存失败，请重试')
  }
}

// 预览仪表盘
const previewDashboard = () => {
  showPreview.value = true
}

// 导出仪表盘
const exportDashboard = () => {
  const dashboardData = {
    name: dashboardName.value,
    config: dashboardConfig,
    widgets: widgets.value,
    exportTime: new Date()
  }
  
  const dataStr = JSON.stringify(dashboardData, null, 2)
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
  
  const linkElement = document.createElement('a')
  linkElement.setAttribute('href', dataUri)
  linkElement.setAttribute('download', `${dashboardName.value}.json`)
  linkElement.click()
  
  ElMessage.success('导出成功')
}

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  // Delete键删除选中组件
  if (event.key === 'Delete' && activeWidgetId.value) {
    removeWidget(activeWidgetId.value)
  }
  
  // Ctrl+S 保存
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    saveDashboard()
  }
  
  // Escape 取消选中
  if (event.key === 'Escape') {
    activeWidgetId.value = null
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  
  // 设置画布背景色
  if (canvasRef.value) {
    canvasRef.value.style.backgroundColor = dashboardConfig.backgroundColor
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.dashboard-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

/* 顶部工具栏 */
.designer-toolbar {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 10px;
}

/* 主设计区域 */
.designer-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧组件库 */
.component-panel {
  width: 200px;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  padding: 20px;
  overflow-y: auto;
}

.component-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #303133;
}

.component-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: move;
  transition: all 0.3s;
}

.component-item:hover {
  background: #e9f7ff;
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.component-item span {
  font-size: 12px;
  color: #606266;
}

/* 中间画布区域 */
.canvas-container {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

.design-canvas {
  position: relative;
  width: 100%;
  min-height: 800px;
  background: #fff;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 网格背景 */
.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

/* 组件容器 */
.widget-wrapper {
  position: absolute;
  border: 2px solid transparent;
  transition: border-color 0.3s;
  cursor: pointer;
}

.widget-wrapper:hover {
  border-color: #409eff;
}

.widget-wrapper.active {
  border-color: #409eff;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.3);
}

.widget-content {
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 缩放手柄 */
.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #409eff;
  border: 2px solid #fff;
  border-radius: 50%;
  z-index: 10;
}

.resize-handle-nw {
  top: -5px;
  left: -5px;
  cursor: nw-resize;
}

.resize-handle-ne {
  top: -5px;
  right: -5px;
  cursor: ne-resize;
}

.resize-handle-sw {
  bottom: -5px;
  left: -5px;
  cursor: sw-resize;
}

.resize-handle-se {
  bottom: -5px;
  right: -5px;
  cursor: se-resize;
}

/* 移动手柄 */
.move-handle {
  position: absolute;
  top: 5px;
  left: 5px;
  padding: 5px;
  background: rgba(64, 158, 255, 0.9);
  color: #fff;
  border-radius: 4px;
  cursor: move;
  z-index: 9;
}

/* 删除按钮 */
.delete-button {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 9;
}

/* 右侧属性面板 */
.property-panel {
  width: 300px;
  background: #fff;
  border-left: 1px solid #e8e8e8;
  padding: 20px;
  overflow-y: auto;
}

.property-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #303133;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c0c0c0;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #909399;
}
</style>