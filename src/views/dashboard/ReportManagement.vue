<template>
  <div class="report-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Document /></el-icon>
        报表管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreateReport">
          <el-icon><Plus /></el-icon>
          创建报表
        </el-button>
        <el-button @click="handleScheduleReport">
          <el-icon><Timer /></el-icon>
          定时报表
        </el-button>
        <el-button @click="handleExportAll">
          <el-icon><Download /></el-icon>
          批量导出
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="报表名称">
          <el-input
            v-model="searchForm.reportName"
            placeholder="请输入报表名称"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="报表类型">
          <el-select
            v-model="searchForm.reportType"
            placeholder="请选择报表类型"
            clearable
            style="width: 150px"
          >
            <el-option label="人员统计" value="personnel"  />
            <el-option label="薪酬分析" value="salary"  />
            <el-option label="考勤报表" value="attendance"  />
            <el-option label="绩效报表" value="performance"  />
            <el-option label="招聘报表" value="recruitment"  />
            <el-option label="培训报表" value="training"  />
          </el-select>
        </el-form-item>
        <el-form-item label="报表状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="草稿" value="draft"  />
            <el-option label="已发布" value="published"  />
            <el-option label="已归档" value="archived"  />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">报表总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon published">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.published }}</div>
              <div class="stat-label">已发布</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon scheduled">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.scheduled }}</div>
              <div class="stat-label">定时报表</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon downloads">
              <el-icon><Download /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.downloads }}</div>
              <div class="stat-label">本月下载</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 报表列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="reportId" label="报表编号" width="120"  />
        <el-table-column prop="reportName" label="报表名称" min-width="200"  />
        <el-table-column prop="reportType" label="报表类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.reportType)">
              {{ getTypeLabel(row.reportType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dataSource" label="数据源" width="120"  />
        <el-table-column prop="frequency" label="生成频率" width="120">
          <template #default="{ row }">
            {{ getFrequencyLabel(row.frequency) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastGenerated" label="最后生成" width="150"  />
        <el-table-column prop="downloadCount" label="下载次数" width="100"  />
        <el-table-column prop="creator" label="创建人" width="100"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleGenerate(row)">
              生成
            </el-button>
            <el-button type="text" size="small" @click="handleDownload(row)">
              下载
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)"> <!-- 修复command参数类型 -->
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="preview">预览</el-dropdown-item>
                  <el-dropdown-item command="schedule">设置定时</el-dropdown-item>
                  <el-dropdown-item command="share">分享</el-dropdown-item>
                  <el-dropdown-item command="history">历史版本</el-dropdown-item>
                  <el-dropdown-item command="duplicate">复制</el-dropdown-item>
                  <el-dropdown-item command="archive" divided>归档</el-dropdown-item>
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 报表详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报表编号" prop="reportId">
              <el-input v-model="formData.reportId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报表名称" prop="reportName">
              <el-input v-model="formData.reportName" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报表类型" prop="reportType">
              <el-select v-model="formData.reportType" :disabled="isView" style="width: 100%">
                <el-option label="人员统计" value="personnel"  />
                <el-option label="薪酬分析" value="salary"  />
                <el-option label="考勤报表" value="attendance"  />
                <el-option label="绩效报表" value="performance"  />
                <el-option label="招聘报表" value="recruitment"  />
                <el-option label="培训报表" value="training"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据源" prop="dataSource">
              <el-select v-model="formData.dataSource" :disabled="isView" style="width: 100%">
                <el-option label="员工数据库" value="employee_db"  />
                <el-option label="薪酬系统" value="salary_system"  />
                <el-option label="考勤系统" value="attendance_system"  />
                <el-option label="绩效系统" value="performance_system"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生成频率" prop="frequency">
              <el-select v-model="formData.frequency" :disabled="isView" style="width: 100%">
                <el-option label="手动生成" value="manual"  />
                <el-option label="每日" value="daily"  />
                <el-option label="每周" value="weekly"  />
                <el-option label="每月" value="monthly"  />
                <el-option label="每季度" value="quarterly"  />
                <el-option label="每年" value="yearly"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="输出格式" prop="outputFormat">
              <el-select v-model="formData.outputFormat" :disabled="isView" style="width: 100%" multiple>
                <el-option label="Excel" value="excel"  />
                <el-option label="PDF" value="pdf"  />
                <el-option label="CSV" value="csv"  />
                <el-option label="HTML" value="html"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="报表描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入报表描述"
            />
        </el-form-item>
        <el-form-item label="数据筛选条件" prop="filterConditions">
          <el-input
            v-model="formData.filterConditions"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入数据筛选条件"
            />
        </el-form-item>
        <el-form-item label="报表模板" prop="template">
          <el-input
            v-model="formData.template"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入报表模板配置"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reportTemplateApi } from '@/api/reportTemplate'
import {
  Document,
  Plus,
  Timer,
  Download,
  Search,
  Refresh,
  CircleCheck,
  ArrowDown
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)
const selectedReports = ref([])

const searchForm = reactive({
  reportName: '',
  reportType: '',
  status: '',
  dateRange: []
})

const stats = reactive({
  total: 45,
  published: 32,
  scheduled: 12,
  downloads: 156
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const mockReports = [
  {
    id: '1',
    name: 'HrHr月度人员统计报表',
    category: '人事报表',
    createTime: '2024-06-01',
    createBy: '管理员',
    lastGenerateTime: '2024-06-20 10:30:00',
    generateCount: 25,
    status: 'active',
    description: '统计各部门人员数量、结构分布等信息',
    type: 'scheduled'
  },
  {
    id: '2',
    name: '薪资发放汇总表',
    category: '薪资报表',
    createTime: '2024-05-15',
    createBy: '财务主管',
    lastGenerateTime: '2024-06-15 09:00:00',
    generateCount: 12,
    status: 'active',
    description: '月度薪资发放汇总统计',
    type: 'manual'
  }
]

const tableData = ref([])

const formData = reactive({
  reportId: '',
  reportName: '',
  reportType: '',
  dataSource: '',
  frequency: '',
  outputFormat: [],
  description: '',
  filterConditions: '',
  template: '',
  notes: ''
})

const formRules = {
  reportId: [{ required: true, message: '请输入报表编号', trigger: 'blur' }],
  reportName: [{ required: true, message: '请输入报表名称', trigger: 'blur' }],
  reportType: [{ required: true, message: '请选择报表类型', trigger: 'change' }],
  dataSource: [{ required: true, message: '请选择数据源', trigger: 'change' }],
  frequency: [{ required: true, message: '请选择生成频率', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    reportName: '',
    reportType: '',
    status: '',
    dateRange: []
  })
  loadData()
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedReports.value = selection as unknown // 修复类型：never[] → any[]
}

const handleCreateReport = () => {
  dialogTitle.value = '创建报表'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看报表'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑报表'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleGenerate = (row: unknown) => {
  ElMessage.info(`生成报表 ${row.reportName}`)
}

   
const handleDownload = (row: unknown) => {
  ElMessage.info(`下载报表 ${row.reportName}`)
}

const handleScheduleReport = () => {
  ElMessage.info('定时报表功能开发中')
}

const handleExportAll = () => {
  if (selectedReports.value.length === 0) {
    ElMessage.warning('请先选择要导出的报表')
    return
  }
  ElMessage.info('批量导出功能开发中')
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'preview':
      ElMessage.info(`预览报表 ${row.reportName}`)
      break
    case 'schedule':
      ElMessage.info(`设置定时 ${row.reportName}`)
      break
    case 'share':
      ElMessage.info(`分享报表 ${row.reportName}`)
      break
    case 'history':
      ElMessage.info(`查看 ${row.reportName} 的历史版本`)
      break
    case 'duplicate':
      ElMessage.success(`已复制报表 ${row.reportName}`)
      break
    case 'archive':
      ElMessage.success(`报表 ${row.reportName} 已归档`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除报表 ${row.reportName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    reportId: '',
    reportName: '',
    reportType: '',
    dataSource: '',
    frequency: '',
    outputFormat: [],
    description: '',
    filterConditions: '',
    template: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    // 调用API加载报表数据
    const response = await reportTemplateApi.getTemplates({
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      category: selectedCategory.value === '全部' ? undefined : selectedCategory.value,
      keyword: searchKeyword.value
    })
    
    // 更新表格数据
    tableData.value = response.content.map(template => ({
      id: template.id,
      name: template.name,
      category: template.category.name,
      createTime: template.createdAt,
      createBy: template.createdBy || '系统',
      lastGenerateTime: template.lastGeneratedAt || '-',
      generateCount: template.generatedCount || 0,
      status: template.status || 'active',
      description: template.description || '',
      type: template.dataType || 'manual'
    }))
    
    // 更新分页信息
    pagination.total = response.totalElements
  } catch (__error) {
    console.error('加载报表数据失败:', error)
    // 使用模拟数据作为后备
    tableData.value = mockReports
    pagination.total = mockReports.length
    ElMessage.warning('数据加载失败，显示默认数据')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    personnel: 'primary',
    salary: 'success',
    attendance: 'warning',
    performance: 'info',
    recruitment: 'danger',
    training: ''
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    personnel: '人员统计',
    salary: '薪酬分析',
    attendance: '考勤报表',
    performance: '绩效报表',
    recruitment: '招聘报表',
    training: '培训报表'
  }
  return labels[type] || type
}

const getFrequencyLabel = (frequency: string) => {
  const labels: Record<string, string> = {
    manual: '手动生成',
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    quarterly: '每季度',
    yearly: '每年'
  }
  return labels[frequency] || frequency
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    published: 'success',
    archived: 'warning'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.report-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.published {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.scheduled {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.downloads {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
