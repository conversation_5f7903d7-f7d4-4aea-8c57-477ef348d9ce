<template>
  <div class="real-time-monitoring">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Monitor /></el-icon>
        实时监控
      </h2>
      <div class="header-actions">
        <div class="status-indicator">
          <el-badge :is-dot="true" :type="connectionStatus === 'connected' ? 'success' : 'danger'">
            <span class="status-text">{{ getConnectionStatusText() }}</span>
          </el-badge>
        </div>
        <el-button @click="handleToggleAutoRefresh">
          <el-icon><VideoPlay v-if="!autoRefresh" /><VideoPause v-else /></el-icon>
          {{ autoRefresh ? '暂停' : '开始' }}监控
        </el-button>
        <el-button @click="handleFullscreen">
          <el-icon><FullScreen /></el-icon>
          全屏显示
        </el-button>
        <el-button @click="handleExportSnapshot">
          <el-icon><Camera /></el-icon>
          导出快照
        </el-button>
      </div>
    </div>

    <!-- 实时指标面板 -->
    <el-row :gutter="20" class="real-time-metrics">
      <el-col :span="6">
        <el-card class="metric-card online-users">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ realTimeData.onlineUsers }}</div>
              <div class="metric-label">在线用户</div>
              <div class="metric-trend">
                <span class="trend-indicator" :class="realTimeData.onlineUsersChange >= 0 ? 'up' : 'down'">
                  {{ realTimeData.onlineUsersChange >= 0 ? '↗' : '↘' }}
                </span>
                <span class="trend-value">{{ Math.abs(realTimeData.onlineUsersChange) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card active-sessions">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ realTimeData.activeSessions }}</div>
              <div class="metric-label">活跃会话</div>
              <div class="metric-trend">
                <span class="trend-indicator" :class="realTimeData.activeSessionsChange >= 0 ? 'up' : 'down'">
                  {{ realTimeData.activeSessionsChange >= 0 ? '↗' : '↘' }}
                </span>
                <span class="trend-value">{{ Math.abs(realTimeData.activeSessionsChange) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card system-load">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ realTimeData.systemLoad }}%</div>
              <div class="metric-label">系统负载</div>
              <div class="metric-trend">
                <el-progress
                  :percentage="realTimeData.systemLoad"
                  :color="getLoadColor(realTimeData.systemLoad)"
                  :show-text="false"
                  :stroke-width="4"
                 />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card response-time">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><Stopwatch /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ realTimeData.responseTime }}ms</div>
              <div class="metric-label">响应时间</div>
              <div class="metric-trend">
                <span class="trend-indicator" :class="realTimeData.responseTimeChange <= 0 ? 'up' : 'down'">
                  {{ realTimeData.responseTimeChange <= 0 ? '↗' : '↘' }}
                </span>
                <span class="trend-value">{{ Math.abs(realTimeData.responseTimeChange) }}ms</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时图表 -->
    <el-row :gutter="20" class="real-time-charts">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>系统性能监控</span>
              <div class="chart-controls">
                <el-radio-group v-model="performanceMetric" size="small">
                  <el-radio-button label="cpu">CPU</el-radio-button>
                  <el-radio-button label="memory">内存</el-radio-button>
                  <el-radio-button label="disk">磁盘</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="performanceChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>用户活动监控</span>
              <div class="chart-controls">
                <el-radio-group v-model="activityMetric" size="small">
                  <el-radio-button label="login">登录</el-radio-button>
                  <el-radio-button label="operation">操作</el-radio-button>
                  <el-radio-button label="api">API调用</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="activityChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时事件流 -->
    <el-row :gutter="20" class="monitoring-panels">
      <el-col :span="12">
        <el-card class="event-stream">
          <template #header>
            <div class="panel-header">
              <span>实时事件流</span>
              <div class="panel-controls">
                <el-select v-model="eventFilter" size="small" style="width: 120px">
                  <el-option label="全部事件" value="all"  />
                  <el-option label="登录事件" value="login"  />
                  <el-option label="操作事件" value="operation"  />
                  <el-option label="错误事件" value="error"  />
                  <el-option label="警告事件" value="warning"  />
                </el-select>
                <el-button size="small" @click="handleClearEvents">
                  <el-icon><Delete /></el-icon>
                  清空
                </el-button>
              </div>
            </div>
          </template>
          <div class="event-list" ref="eventListRef">
            <div
              v-for="event in filteredEvents"
              :key="event.id"
              class="event-item"
              :class="getEventClass(event.type)"
            >
              <div class="event-time">{{ formatTime(event.timestamp) }}</div>
              <div class="event-content">
                <div class="event-type">
                  <el-tag :type="getEventTagType(event.type)" size="small">
                    {{ getEventTypeLabel(event.type) }}
                  </el-tag>
                </div>
                <div class="event-message">{{ event.message }}</div>
                <div class="event-details" v-if="event.details">{{ event.details }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="alert-panel">
          <template #header>
            <div class="panel-header">
              <span>系统告警</span>
              <div class="panel-controls">
                <el-badge :value="activeAlerts.length" :type="activeAlerts.length > 0 ? 'danger' : 'success'">
                  <el-button size="small" @click="handleViewAllAlerts">
                    查看全部
                  </el-button>
                </el-badge>
              </div>
            </div>
          </template>
          <div class="alert-list">
            <div
              v-for="alert in activeAlerts"
              :key="alert.id"
              class="alert-item"
              :class="getAlertClass(alert.level)"
            >
              <div class="alert-icon">
                <el-icon><Warning v-if="alert.level === 'warning'" /><CircleCloseFilled v-else /></el-icon>
              </div>
              <div class="alert-content">
                <div class="alert-title">{{ alert.title }}</div>
                <div class="alert-message">{{ alert.message }}</div>
                <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
              </div>
              <div class="alert-actions">
                <el-button type="text" size="small" @click="handleAcknowledgeAlert(alert)">
                  确认
                </el-button>
                <el-button type="text" size="small" @click="handleResolveAlert(alert)">
                  解决
                </el-button>
              </div>
            </div>
            <div v-if="activeAlerts.length === 0" class="no-alerts">
              <el-icon><CircleCheckFilled /></el-icon>
              <span>暂无告警</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 在线用户列表 -->
    <el-card class="online-users-panel">
      <template #header>
        <div class="panel-header">
          <span>在线用户 ({{ onlineUsers.length }})</span>
          <div class="panel-controls">
            <el-input
              v-model="userSearchKeyword"
              placeholder="搜索用户"
              size="small"
              style="width: 200px"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button size="small" @click="handleRefreshUsers">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      <el-table :data="filteredOnlineUsers" style="width: 100%" max-height="400">
        <el-table-column prop="username" label="用户名" width="120"  />
        <el-table-column prop="realName" label="真实姓名" width="120"  />
        <el-table-column prop="department" label="部门" width="150"  />
        <el-table-column prop="ipAddress" label="IP地址" width="140"  />
        <el-table-column prop="loginTime" label="登录时间" width="160"  />
        <el-table-column prop="lastActivity" label="最后活动" width="160"  />
        <el-table-column prop="sessionDuration" label="在线时长" width="120"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getUserStatusType(row.status)" size="small">
              {{ getUserStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleViewUserActivity(row)">
              查看活动
            </el-button>
            <el-button type="text" size="small" @click="handleForceLogout(row)" v-if="row.status === 'active'">
              强制下线
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { monitoringApi } from '@/api/monitoring'
import type { SystemEvent, SystemAlert, OnlineUser } from '@/api/monitoring'
import {
  Monitor,
  VideoPlay,
  VideoPause,
  FullScreen,
  Camera,
  User,
  Connection,
  Cpu,
  Stopwatch,
  Delete,
  Warning,
  CircleCloseFilled,
  CircleCheckFilled,
  Search,
  Refresh
} from '@element-plus/icons-vue'

// Router实例
const router = useRouter()

// 响应式数据
const autoRefresh = ref(true)
const connectionStatus = ref('connected')
const performanceMetric = ref('cpu')
const activityMetric = ref('login')
const eventFilter = ref('all')
const userSearchKeyword = ref('')

// 图表引用
const performanceChartRef = ref<HTMLDivElement>()
const activityChartRef = ref<HTMLDivElement>()
const eventListRef = ref<HTMLDivElement>()

// 图表实例
let performanceChart: echarts.ECharts | null = null
let activityChart: echarts.ECharts | null = null

// 定时器
   
let refreshTimer: unknown | null = null // 修复NodeJS.Timeout类型问题
let wsConnection: (() => void) | null = null // WebSocket断开连接函数

// 实时数据
const realTimeData = reactive({
  onlineUsers: 156,
  onlineUsersChange: 3,
  activeSessions: 142,
  activeSessionsChange: -2,
  systemLoad: 68,
  responseTime: 85,
  responseTimeChange: -5
})

// 实时事件
const events = ref<SystemEvent[]>([])

// 系统告警
const activeAlerts = ref<SystemAlert[]>([])

// 在线用户
const onlineUsers = ref<OnlineUser[]>([])

// 计算属性
const filteredEvents = computed(() => {
  if (eventFilter.value === 'all') {
    return events.value
  }
  return events.value.filter(event => event.type === eventFilter.value)
})

const filteredOnlineUsers = computed(() => {
  if (!userSearchKeyword.value) {
    return onlineUsers.value
  }
  const keyword = userSearchKeyword.value.toLowerCase()
  return onlineUsers.value.filter(user =>
    user.username.toLowerCase().includes(keyword) ||
    user.realName.toLowerCase().includes(keyword) ||
    user.department.toLowerCase().includes(keyword)
  )
})

// 方法
const getConnectionStatusText = () => {
  return connectionStatus.value === 'connected' ? '已连接' : '连接断开'
}

const handleToggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const handleFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    document.documentElement.requestFullscreen()
  }
}

const handleExportSnapshot = () => {
  ElMessage.info('导出快照功能开发中')
}

const getLoadColor = (load: number) => {
  if (load < 50) return '#67c23a'
  if (load < 80) return '#e6a23c'
  return '#f56c6c'
}

const handleClearEvents = () => {
  ElMessageBox.confirm('确定要清空所有事件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    events.value = []
    ElMessage.success('事件已清空')
  })
}

const formatTime = (timestamp: string | Date) => {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp
  return date.toLocaleTimeString()
}

const getEventClass = (type: string) => {
  return `event-${type}`
}

const getEventTagType = (type: string) => {
  const types: Record<string, string> = {
    login: 'success',
    operation: 'primary',
    warning: 'warning',
    error: 'danger'
  }
  return types[type] || 'info'
}

const getEventTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    login: '登录',
    operation: '操作',
    warning: '警告',
    error: '错误'
  }
  return labels[type] || type
}

const getAlertClass = (level: string) => {
  return `alert-${level}`
}

const handleViewAllAlerts = () => {
  // 跳转到系统管理的告警管理页面
  router.push({
    path: '/system/alert-management',
    query: {
      // 可以传递一些筛选参数
      status: 'active',
      from: 'monitoring'
    }
  })
}

const handleAcknowledgeAlert = async (alert: SystemAlert) => {
  try {
    await monitoringApi.acknowledgeAlert(alert.id)
    ElMessage.success(`已确认告警：${alert.title}`)
    // 更新本地状态
    const index = activeAlerts.value.findIndex(a => a.id === alert.id)
    if (index > -1) {
      activeAlerts.value[index].acknowledged = true
    }
  } catch (__error) {
    console.error('确认告警失败:', error)
    ElMessage.error('确认告警失败，请稍后重试')
  }
}

const handleResolveAlert = async (alert: SystemAlert) => {
  try {
    await monitoringApi.resolveAlert(alert.id)
    ElMessage.success(`已解决告警：${alert.title}`)
    // 从列表中移除已解决的告警
    const index = activeAlerts.value.findIndex(a => a.id === alert.id)
    if (index > -1) {
      activeAlerts.value.splice(index, 1)
    }
  } catch (__error) {
    console.error('解决告警失败:', error)
    ElMessage.error('解决告警失败，请稍后重试')
  }
}

const handleRefreshUsers = async () => {
  try {
    const users = await monitoringApi.getOnlineUsers()
    onlineUsers.value = users
    ElMessage.success('用户列表已刷新')
  } catch (__error) {
    console.error('刷新用户列表失败:', error)
    ElMessage.error('刷新失败，请稍后重试')
  }
}

const getUserStatusType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    idle: 'warning',
    offline: 'info'
  }
  return types[status] || 'info'
}

const getUserStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    active: '活跃',
    idle: '空闲',
    offline: '离线'
  }
  return labels[status] || status
}

const handleViewUserActivity = (user: OnlineUser) => {
  // 跳转到用户活动记录页面
  router.push({
    path: '/system/user-activity',
    query: {
      userId: user.userId,
      username: user.username,
      realName: user.realName,
      // 默认查看最近7天的活动记录
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    }
  })
}

const handleForceLogout = async (user: OnlineUser) => {
  try {
    await ElMessageBox.confirm(`确定要强制用户 ${user.realName} 下线吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await monitoringApi.forceLogout(user.id)
    ElMessage.success(`用户 ${user.realName} 已强制下线`)
    
    // 更新本地状态
    const index = onlineUsers.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      onlineUsers.value[index].status = 'offline'
    }
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('强制下线失败:', error)
      ElMessage.error('强制下线失败，请稍后重试')
    }
  }
}

const startAutoRefresh = () => {
  stopAutoRefresh()
  refreshTimer = setInterval(async () => {
    // 加载实时数据
    await loadRealTimeData()
    
    // 更新图表
    await updatePerformanceChart()
    await updateActivityChart()
  }, 3000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 初始化图表
const initCharts = () => {
  // 初始化性能监控图表
  if (performanceChartRef.value) {
    performanceChart = echarts.init(performanceChartRef.value)
    window.addEventListener('resize', () => performanceChart?.resize())
  }
  
  // 初始化用户活动图表
  if (activityChartRef.value) {
    activityChart = echarts.init(activityChartRef.value)
    window.addEventListener('resize', () => activityChart?.resize())
  }
  
  // 设置初始图表选项
  updatePerformanceChart()
  updateActivityChart()
}

// 更新性能图表
const updatePerformanceChart = async () => {
  if (!performanceChart) return
  
  try {
    const data = await monitoringApi.getPerformanceHistory({
      metric: performanceMetric.value as 'cpu' | 'memory' | 'disk',
      duration: 60
    })
    
    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.map(d => new Date(d.timestamp).toLocaleTimeString()),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: 'HrHr使用率(%)',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: performanceMetric.value.toUpperCase(),
          type: 'line',
          smooth: true,
          symbol: 'none',
          sampling: 'lttb',
          data: data.map(d => d[performanceMetric.value as keyof typeof d]),
          itemStyle: {
            color: '#409eff'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.0)' }
            ])
          }
        }
      ]
    }
    
    performanceChart.setOption(option)
  } catch (__error) {
    console.error('更新性能图表失败:', error)
  }
}

// 更新活动图表
const updateActivityChart = async () => {
  if (!activityChart) return
  
  try {
    const data = await monitoringApi.getUserActivityHistory({
      metric: activityMetric.value as 'login' | 'operation' | 'api',
      duration: 60
    })
    
    const metricData = data.map(d => {
      switch (activityMetric.value) {
        case 'login':
          return d.loginCount
        case 'operation':
          return d.operationCount
        case 'api':
          return d.apiCallCount
        default:
          return 0
      }
    })
    
    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(d => new Date(d.timestamp).toLocaleTimeString()),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '次数'
      },
      series: [
        {
          name: activityMetric.value === 'login' ? '登录次数' : 
                activityMetric.value === 'operation' ? '操作次数' : 'API调用次数',
          type: 'bar',
          data: metricData,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#67c23a' },
              { offset: 1, color: '#409eff' }
            ])
          }
        }
      ]
    }
    
    activityChart.setOption(option)
  } catch (__error) {
    console.error('更新活动图表失败:', error)
  }
}

// 加载实时数据
const loadRealTimeData = async () => {
  try {
    // 获取实时监控数据
    const data = await monitoringApi.getRealTimeData()
    Object.assign(realTimeData, data)
    
    // 获取系统事件
    const eventsData = await monitoringApi.getSystemEvents({ limit: 50 })
    events.value = eventsData
    
    // 获取系统告警
    const alertsData = await monitoringApi.getSystemAlerts({ active: true })
    activeAlerts.value = alertsData
    
    // 获取在线用户
    const usersData = await monitoringApi.getOnlineUsers()
    onlineUsers.value = usersData
  } catch (__error) {
    console.error('加载实时数据失败:', error)
  }
}

// 连接WebSocket
const connectWebSocket = () => {
  wsConnection = monitoringApi.connectRealTimeStream({
    onData: (data) => {
      Object.assign(realTimeData, data)
    },
    onEvent: (_event) => {
      events.value.unshift(event)
      if (events.value.length > 50) {
        events.value = events.value.slice(0, 50)
      }
    },
    onAlert: (alert) => {
      if (!activeAlerts.value.find(a => a.id === alert.id)) {
        activeAlerts.value.unshift(alert)
      }
    },
    onError: (_error) => {
      console.error('WebSocket错误:', error)
      connectionStatus.value = 'disconnected'
    }
  })
  
  connectionStatus.value = 'connected'
}

// 监听指标变化
watch(performanceMetric, () => {
  updatePerformanceChart()
})

watch(activityMetric, () => {
  updateActivityChart()
})

onMounted(() => {
  // 初始化图表
  initCharts()
  
  // 加载初始数据
  loadRealTimeData()
  
  // 连接WebSocket
  connectWebSocket()
  
  // 启动自动刷新
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  // 停止自动刷新
  stopAutoRefresh()
  
  // 断开WebSocket连接
  if (wsConnection) {
    wsConnection()
  }
  
  // 销毁图表实例
  performanceChart?.dispose()
  activityChart?.dispose()
})
</script>

<style scoped>
.real-time-monitoring {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  font-size: 14px;
  color: #606266;
}

.real-time-metrics {
  margin-bottom: 20px;
}

.metric-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.online-users .metric-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.active-sessions .metric-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.system-load .metric-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.response-time .metric-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-indicator {
  font-size: 16px;
  font-weight: 600;
}

.trend-indicator.up {
  color: #67c23a;
}

.trend-indicator.down {
  color: #f56c6c;
}

.trend-value {
  font-size: 12px;
  color: #909399;
}

.real-time-charts {
  margin-bottom: 20px;
}

.chart-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.chart-container {
  height: 300px;
}

.monitoring-panels {
  margin-bottom: 20px;
}

.event-stream,
.alert-panel {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-controls {
  display: flex;
  gap: 8px;
}

.event-list {
  max-height: 400px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.event-item:hover {
  background-color: #f8f9fa;
}

.event-item:last-child {
  border-bottom: none;
}

.event-time {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  min-width: 80px;
}

.event-content {
  flex: 1;
}

.event-type {
  margin-bottom: 4px;
}

.event-message {
  font-size: 14px;
  color: #303133;
  margin-bottom: 2px;
}

.event-details {
  font-size: 12px;
  color: #909399;
}

.alert-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.alert-item.alert-warning {
  background: #fdf6ec;
  border-left: 4px solid #e6a23c;
}

.alert-item.alert-error {
  background: #fef0f0;
  border-left: 4px solid #f56c6c;
}

.alert-icon {
  font-size: 20px;
  margin-top: 2px;
}

.alert-item.alert-warning .alert-icon {
  color: #e6a23c;
}

.alert-item.alert-error .alert-icon {
  color: #f56c6c;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.alert-message {
  font-size: 13px;
  color: #606266;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #909399;
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.no-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
}

.no-alerts .el-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 8px;
}

.online-users-panel {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>