/**
 * 仪表板类型定义
 */

// 时间范围类型
export type TimeRange = 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom'

// 数据类型
export enum DataType {
  INSTITUTION_CHANGE = 'INSTITUTION_CHANGE',
  AD_HOC_INSTITUTION = 'AD_HOC_INSTITUTION'
}

// 查询参数
export interface DashboardQueryParams {
  timeRange: TimeRange
  customDateRange?: [string, string]
  dataTypes: DataType[]
  departments: string[]
}

// 统计卡片类型
export interface StatisticsCardType {
  id: string
  title: string
  value: number | string
  icon: string
  color: string
  trend?: number
  trendLabel?: string
  clickable?: boolean
  unit?: string
  description?: string
}

// 仪表板数据类型
export interface DashboardData {
  overview: {
    totalInstitutions: number
    activeChanges: number
    pendingApprovals: number
    completedChanges: number
  }
  systemStatus: {
    status: 'healthy' | 'warning' | 'error'
    uptime: string
    lastSync: string
    activeUsers: number
  }
  realTimeMetrics: {
    requestsPerMinute: number
    avgResponseTime: number
    activeConnections: number
    errorRate: number
  }
  charts: {
    changesTrend: ChartData
    institutionStatus: ChartData
    changeTypes: ChartData
    departmentHeatmap: HeatmapData
  }
}

// 图表数据类型
export interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string
    borderWidth?: number
    type?: string
  }>
}

// 热力图数据类型
export interface HeatmapData {
  categories: {
    x: string[]
    y: string[]
  }
  data: Array<[number, number, number]> // [x, y, value]
}

// 警报类型
export interface Alert {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  timestamp: string
  acknowledged: boolean
  source?: string
  priority?: 'low' | 'medium' | 'high'
}

// 设置类型
export interface DashboardSettings {
  autoRefreshInterval: number // 秒
  defaultTimeRange: TimeRange
  enableNotifications: boolean
  chartAnimations: boolean
  compactMode: boolean
}

// 钻取数据类型
export interface DrillDownData {
  chartId: string
  title: string

  data: unknown
  type: 'detail' | 'list' | 'chart'
}
