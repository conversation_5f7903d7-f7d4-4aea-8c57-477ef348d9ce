<template>
  <div class="dashboard-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><DataAnalysis /></el-icon>
        领导驾驶舱
      </h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 时间范围选择 -->
    <el-card class="time-range-card">
      <el-row :gutter="20" align="middle">
        <el-col :span="12">
          <el-date-picker
            v-model="timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleTimeRangeChange"
           />
        </el-col>
        <el-col :span="12">
          <div class="quick-time-buttons">
            <el-button size="small" @click="setQuickTime('today')">今日</el-button>
            <el-button size="small" @click="setQuickTime('week')">本周</el-button>
            <el-button size="small" @click="setQuickTime('month')">本月</el-button>
            <el-button size="small" @click="setQuickTime('quarter')">本季度</el-button>
            <el-button size="small" @click="setQuickTime('year')">本年度</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 核心指标卡片 -->
    <el-row :gutter="20" class="metrics-cards">
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon personnel">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.totalPersonnel }}</div>
              <div class="metric-label">在职人员</div>
              <div class="metric-trend" :class="getTrendClass(metrics.personnelTrend)">
                <el-icon><TrendCharts /></el-icon>
                {{ formatTrend(metrics.personnelTrend) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon recruitment">
              <el-icon><User /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.newRecruits }}</div>
              <div class="metric-label">新增招聘</div>
              <div class="metric-trend" :class="getTrendClass(metrics.recruitmentTrend)">
                <el-icon><TrendCharts /></el-icon>
                {{ formatTrend(metrics.recruitmentTrend) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon training">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.trainingHours }}</div>
              <div class="metric-label">培训学时</div>
              <div class="metric-trend" :class="getTrendClass(metrics.trainingTrend)">
                <el-icon><TrendCharts /></el-icon>
                {{ formatTrend(metrics.trainingTrend) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon satisfaction">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.satisfaction }}%</div>
              <div class="metric-label">员工满意度</div>
              <div class="metric-trend" :class="getTrendClass(metrics.satisfactionTrend)">
                <el-icon><TrendCharts /></el-icon>
                {{ formatTrend(metrics.satisfactionTrend) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>人员结构分析</span>
              <el-button type="text" @click="drillDown('personnel')">
                <el-icon><ZoomIn /></el-icon>
                钻取
              </el-button>
            </div>
          </template>
          <div class="chart-container">
            <PersonnelStructureChart 
              :data="personnelStructureData"
              :type="personnelStructureType"
              height="300px"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>招聘趋势分析</span>
              <el-button type="text" @click="drillDown('recruitment')">
                <el-icon><ZoomIn /></el-icon>
                钻取
              </el-button>
            </div>
          </template>
          <div class="chart-container">
            <RecruitmentTrendChart 
              :data="recruitmentTrendData"
              height="300px"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>培训完成率</span>
            </div>
          </template>
          <div class="chart-container">
            <TrainingCompletionChart 
              :data="trainingCompletionData"
              height="300px"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>考核等级分布</span>
            </div>
          </template>
          <div class="chart-container">
            <AppraisalDistributionChart 
              :data="appraisalDistributionData"
              height="300px"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>薪酬水平分析</span>
            </div>
          </template>
          <div class="chart-container">
            <SalaryAnalysisChart 
              :data="salaryAnalysisData"
              :type="salaryAnalysisType"
              height="300px"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- AI分析洞察 -->
    <el-card class="ai-insights">
      <template #header>
        <div class="card-header">
          <span>AI分析洞察</span>
          <el-button type="text" @click="generateAIInsights">
            <el-icon><MagicStick /></el-icon>
            生成洞察
          </el-button>
        </div>
      </template>
      <div v-if="aiInsights.length > 0" class="insights-list">
        <div
          v-for="insight in aiInsights"
          :key="insight.id"
          class="insight-item"
        >
          <div class="insight-header">
            <el-icon class="insight-icon"><Sunny /></el-icon>
            <h4>{{ insight.title }}</h4>
            <el-tag :type="getInsightType(insight.confidence)" size="small">
              置信度: {{ insight.confidence }}%
            </el-tag>
          </div>
          <p class="insight-content">{{ insight.content }}</p>
          <div v-if="insight.recommendations.length > 0" class="recommendations">
            <h5>建议措施：</h5>
            <ul>
              <li v-for="rec in insight.recommendations" :key="rec">{{ rec }}</li>
            </ul>
          </div>
        </div>
      </div>
      <div v-else class="no-insights">
        <el-empty description="暂无AI分析洞察，点击生成洞察按钮获取智能分析"  />
      </div>
    </el-card>

    <!-- 实时预警 -->
    <el-card class="alerts-section">
      <template #header>
        <div class="card-header">
          <span>实时预警</span>
          <el-badge :value="unreadAlerts" class="alert-badge">
            <el-button type="text" @click="viewAllAlerts">
              查看全部
            </el-button>
          </el-badge>
        </div>
      </template>
      <div class="alerts-list">
        <div
          v-for="alert in recentAlerts"
          :key="alert.id"
          class="alert-item"
          :class="getAlertClass(alert.severity)"
        >
          <div class="alert-icon">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="alert-content">
            <h4>{{ alert.title }}</h4>
            <p>{{ alert.message }}</p>
            <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
          </div>
          <div class="alert-actions">
            <el-button type="text" size="small" @click="acknowledgeAlert(alert.id)">
              确认
            </el-button>
            <el-button type="text" size="small" @click="viewAlertDetail(alert)">
              详情
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DataAnalysis,
  Refresh,
  Download,
  UserFilled,
  User,
  Reading,
  Trophy,
  TrendCharts,
  ZoomIn,
  MagicStick,
  Sunny,
  Warning
} from '@element-plus/icons-vue'
import { dashboardApi } from '@/api/dashboard'
import HrPersonnelStructureChart from '@/components/charts/HrPersonnelStructureChart.vue'
import HrRecruitmentTrendChart from '@/components/charts/HrRecruitmentTrendChart.vue'
import HrTrainingCompletionChart from '@/components/charts/HrTrainingCompletionChart.vue'
import HrAppraisalDistributionChart from '@/components/charts/HrAppraisalDistributionChart.vue'
import HrSalaryAnalysisChart from '@/components/charts/HrSalaryAnalysisChart.vue'

// 响应式数据
const timeRange = ref<[string, string]>(['', ''])
const metrics = ref({
  totalPersonnel: 1256,
  personnelTrend: 2.5,
  newRecruits: 45,
  recruitmentTrend: 15.2,
  trainingHours: 8640,
  trainingTrend: -3.1,
  satisfaction: 87.5,
  satisfactionTrend: 1.8
})

const aiInsights = ref([
  {
    id: '1',
    title: '人员流失率预警',
    content: '根据历史数据分析，预计下季度人员流失率可能上升至8.5%，建议加强员工关怀和薪酬调整。',
    confidence: 85,
    recommendations: [
      '开展员工满意度调研',
      '优化薪酬福利体系',
      '加强职业发展规划'
    ]
  },
  {
    id: '2',
    title: '培训效果分析',
    content: '新员工培训完成率达95%，但培训效果评估显示实际技能提升有限，建议优化培训内容。',
    confidence: 92,
    recommendations: [
      '增加实践操作环节',
      '建立导师制度',
      '定期跟踪培训效果'
    ]
  }
])

const recentAlerts = ref([
  {
    id: '1',
    title: '合同到期提醒',
    message: '15名员工合同将在30天内到期，请及时处理续签事宜',
    severity: 'warning',
    timestamp: '2024-06-19 14:30:00'
  },
  {
    id: '2',
    title: '培训计划延期',
    message: '新员工入职培训计划因讲师调整延期至下周',
    severity: 'info',
    timestamp: '2024-06-19 11:20:00'
  }
])

const unreadAlerts = ref(5)

// 图表数据
const personnelStructureType = ref<'department' | 'position' | 'education' | 'age'>('department')
const personnelStructureData = ref({
  byDepartment: [
    { name: 'HrHr技术部', value: 320 },
    { name: '市场部', value: 180 },
    { name: '人力资源部', value: 120 },
    { name: '财务部', value: 98 },
    { name: '行政部', value: 76 },
    { name: '销售部', value: 256 },
    { name: '研发部', value: 206 }
  ],
  byPosition: [
    { name: '普通员工', value: 680 },
    { name: '主管', value: 320 },
    { name: '经理', value: 180 },
    { name: '总监', value: 76 }
  ],
  byEducation: [
    { name: '博士', value: 45 },
    { name: '硕士', value: 280 },
    { name: '本科', value: 720 },
    { name: '专科', value: 211 }
  ],
  byAge: [
    { name: '20-25岁', range: '20-25', value: 180 },
    { name: '26-30岁', range: '26-30', value: 420 },
    { name: '31-35岁', range: '31-35', value: 356 },
    { name: '36-40岁', range: '36-40', value: 200 },
    { name: '40岁以上', range: '40+', value: 100 }
  ],
  total: 1256
})

const recruitmentTrendData = ref({
  months: ['1月', '2月', '3月', '4月', '5月', '6月'],
  planned: [30, 25, 35, 28, 32, 40],
  actual: [35, 28, 40, 30, 38, 45],
  onboarded: [25, 22, 30, 25, 28, 35],
  turnover: [8, 5, 12, 6, 9, 7]
})

const trainingCompletionData = ref({
  overall: 78.5,
  departments: [
    { name: '技术部', completion: 85, target: 100 },
    { name: '市场部', completion: 72, target: 100 },
    { name: '人力资源部', completion: 90, target: 100 },
    { name: '财务部', completion: 68, target: 100 },
    { name: '销售部', completion: 75, target: 100 }
  ]
})

const appraisalDistributionData = ref({
  levels: ['优秀', '良好', '合格', '待改进'],
  counts: [156, 520, 480, 100],
  percentages: [12.4, 41.4, 38.2, 8.0]
})

const salaryAnalysisType = ref<'comparison' | 'distribution'>('comparison')
const salaryAnalysisData = ref({
  departments: ['技术部', '市场部', '人力资源部', '财务部', '销售部'],
  average: [15000, 12000, 10000, 11000, 13000],
  min: [8000, 6000, 5000, 6000, 5000],
  max: [35000, 25000, 20000, 22000, 30000],
  median: [14000, 11000, 9500, 10500, 12000],
  ranges: [
    { range: '5k以下', count: 50 },
    { range: '5k-10k', count: 380 },
    { range: '10k-15k', count: 450 },
    { range: '15k-20k', count: 256 },
    { range: '20k-30k', count: 100 },
    { range: '30k以上', count: 20 }
  ]
})

// 初始化时间范围
const initTimeRange = () => {
  const today = new Date()
  const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())
  timeRange.value = [
    lastMonth.toISOString().split('T')[0],
    today.toISOString().split('T')[0]
  ]
}

// 处理时间范围变化
const handleTimeRangeChange = (range: [string, string]) => {
  if (range && range.length === 2) {
    loadDashboardData()
  }
}

// 设置快捷时间
const setQuickTime = (type: string) => {
  const today = new Date()
  let startDate: Date
  const endDate = today

  switch (type) {
    case 'today':
      startDate = today
      break
    case 'week':
      startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 7)
      break
    case 'month':
      startDate = new Date(today.getFullYear(), today.getMonth(), 1)
      break
    case 'quarter':
      const quarter = Math.floor(today.getMonth() / 3)
      startDate = new Date(today.getFullYear(), quarter * 3, 1)
      break
    case 'year':
      startDate = new Date(today.getFullYear(), 0, 1)
      break
    default:
      return
  }

  timeRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]
  loadDashboardData()
}

// 加载仪表板数据
const loadDashboardData = async () => {
  try {
    // 调用API加载各类统计数据
    const [startDate, endDate] = timeRange.value
    
    // 获取仪表板概览数据
    const overviewData = await dashboardApi.getOverview({
      startDate,
      endDate
    })
    
    // 更新指标卡数据
    if (overviewData.metrics) {
      metrics.value = {
        totalPersonnel: overviewData.metrics.totalPersonnel || 1256,
        personnelTrend: overviewData.metrics.personnelTrend || 2.5,
        newRecruits: overviewData.metrics.newRecruits || 45,
        recruitmentTrend: overviewData.metrics.recruitmentTrend || 15.2,
        trainingHours: overviewData.metrics.trainingHours || 8640,
        trainingTrend: overviewData.metrics.trainingTrend || -3.1,
        satisfaction: overviewData.metrics.satisfaction || 87.5,
        satisfactionTrend: overviewData.metrics.satisfactionTrend || 1.8
      }
    }
    
    // 更新人员结构数据
    if (overviewData.personnelStructure) {
      personnelStructureData.value = overviewData.personnelStructure
    }
    
    // 更新招聘趋势数据
    if (overviewData.recruitmentTrend) {
      recruitmentTrendData.value = overviewData.recruitmentTrend
    }
    
    // 更新培训完成率数据
    if (overviewData.trainingCompletion) {
      trainingCompletionData.value = overviewData.trainingCompletion
    }
    
    // 更新考核分布数据
    if (overviewData.appraisalDistribution) {
      appraisalDistributionData.value = overviewData.appraisalDistribution
    }
    
    // 更新薪酬分析数据
    if (overviewData.salaryAnalysis) {
      salaryAnalysisData.value = overviewData.salaryAnalysis
    }
    
    console.log('仪表板数据加载成功')
  } catch (__error) {
    console.error('加载仪表板数据失败:', error)
    ElMessage.error('加载数据失败，使用默认数据')
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    await dashboardApi.refreshCache()
    await loadDashboardData()
    ElMessage.success('数据刷新成功')
  } catch (__error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  }
}

// 导出报告
const exportReport = async () => {
  try {
    ElMessage.info('正在生成报告，请稍候...')
    
    // 准备导出数据
    const exportData = {
      timeRange: timeRange.value,
      metrics: metrics.value,
      personnelStructure: personnelStructureData.value,
      recruitmentTrend: recruitmentTrendData.value,
      trainingCompletion: trainingCompletionData.value,
      appraisalDistribution: appraisalDistributionData.value,
      salaryAnalysis: salaryAnalysisData.value,
      aiInsights: aiInsights.value
    }
    
    // 调用导出API
    const blob = await dashboardApi.exportReport(exportData)
    
    // 下载文件
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `HR仪表板报告_${new Date().toISOString().split('T')[0]}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('报告导出成功')
  } catch (__error) {
    console.error('导出报告失败:', error)
    ElMessage.error('导出报告失败')
  }
}

// 钻取分析
const drillDown = (type: string) => {
  // 根据不同类型切换视图或跳转页面
  switch (type) {
    case 'personnel':
      // 切换人员结构图表类型
      const types: Array<'department' | 'position' | 'education' | 'age'> = ['department', 'position', 'education', 'age']
      const currentIndex = types.indexOf(personnelStructureType.value)
      personnelStructureType.value = types[(currentIndex + 1) % types.length]
      ElMessage.success(`切换到${personnelStructureType.value === 'department' ? '部门' : 
                          personnelStructureType.value === 'position' ? '岗位' :
                          personnelStructureType.value === 'education' ? '学历' : '年龄'}分布`)
      break
    case 'recruitment':
      // 跳转到招聘管理详情页
      window.open('/recruitment/overview', '_blank')
      break
    default:
      ElMessage.info(`正在开发${type}钻取分析功能`)
  }
}

// 生成AI洞察
const generateAIInsights = async () => {
  try {
    ElMessage.info('正在生成AI洞察，请稍候...')
    
    // 准备分析数据
    const analysisData = {
      timeRange: timeRange.value,
      metrics: metrics.value,
      personnelStructure: personnelStructureData.value,
      recruitmentTrend: recruitmentTrendData.value,
      trainingCompletion: trainingCompletionData.value,
      appraisalDistribution: appraisalDistributionData.value,
      salaryAnalysis: salaryAnalysisData.value
    }
    
    // 调用AI分析API
    const insights = await dashboardApi.generateAIInsights(analysisData)
    
    if (insights && insights.length > 0) {
   
      aiInsights.value = insights.map((insight: unknown, index: number) => ({
        id: String(index + 1),
        title: insight.title,
        content: insight.content,
        confidence: insight.confidence || 85,
        recommendations: insight.recommendations || []
      }))
      
      ElMessage.success('AI洞察生成成功')
    } else {
      ElMessage.warning('暂无可用的AI洞察')
    }
  } catch (__error) {
    console.error('生成AI洞察失败:', error)
    ElMessage.error('AI分析服务暂时不可用')
    
    // 使用默认洞察
    aiInsights.value = [
      {
        id: '1',
        title: '人员流失率预警',
        content: '根据历史数据分析，预计下季度人员流失率可能上升至8.5%，建议加强员工关怀和薪酬调整。',
        confidence: 85,
        recommendations: [
          '开展员工满意度调研',
          '优化薪酬福利体系',
          '加强职业发展规划'
        ]
      },
      {
        id: '2',
        title: '培训效果分析',
        content: '新员工培训完成率达95%，但培训效果评估显示实际技能提升有限，建议优化培训内容。',
        confidence: 92,
        recommendations: [
          '增加实践操作环节',
          '建立导师制度',
          '定期跟踪培训效果'
        ]
      }
    ]
  }
}

// 确认预警
const acknowledgeAlert = async (alertId: string) => {
  try {
    // 调用确认预警API
    await dashboardApi.acknowledgeAlert(alertId)
    
    // 从列表中移除
    const index = recentAlerts.value.findIndex(alert => alert.id === alertId)
    if (index > -1) {
      recentAlerts.value.splice(index, 1)
      unreadAlerts.value = Math.max(0, unreadAlerts.value - 1)
      ElMessage.success('已确认预警')
    }
  } catch (__error) {
    console.error('确认预警失败:', error)
    ElMessage.error('确认预警失败')
  }
}

// 查看预警详情
   
const viewAlertDetail = (alert: unknown) => {
  // 根据预警类型展示不同的详情
  switch (alert.severity) {
    case 'warning':
      if (alert.title.includes('合同')) {
        // 跳转到合同管理页面
        window.open('/contract/expiring', '_blank')
      } else {
        ElMessage.info(`预警详情：${alert.message}`)
      }
      break
    case 'info':
      if (alert.title.includes('培训')) {
        // 跳转到培训管理页面
        window.open('/faculty-development/training-plans', '_blank')
      } else {
        ElMessage.info(`通知详情：${alert.message}`)
      }
      break
    default:
      ElMessage.info(`详情：${alert.message}`)
  }
}

// 查看所有预警
const viewAllAlerts = () => {
  // 跳转到预警管理页面
  window.open('/dashboard/alerts', '_blank')
}

// 获取趋势样式类
const getTrendClass = (trend: number) => {
  return trend > 0 ? 'trend-up' : trend < 0 ? 'trend-down' : 'trend-stable'
}

// 格式化趋势
const formatTrend = (trend: number) => {
  const abs = Math.abs(trend)
  const symbol = trend > 0 ? '+' : trend < 0 ? '-' : ''
  return `${symbol}${abs}%`
}

// 获取洞察类型
const getInsightType = (confidence: number) => {
  if (confidence >= 90) return 'success'
  if (confidence >= 70) return 'warning'
  return 'info'
}

// 获取预警样式类
const getAlertClass = (severity: string) => {
  return `alert-${severity}`
}

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

onMounted(() => {
  initTimeRange()
  loadDashboardData()
})
</script>

<style scoped>
.dashboard-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.time-range-card {
  margin-bottom: 20px;
}

.quick-time-buttons {
  display: flex;
  gap: 8px;
}

.metrics-cards {
  margin-bottom: 20px;
}

.metric-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.metric-icon.personnel {
  background: linear-gradient(135deg, #409eff, #67c23a);
}

.metric-icon.recruitment {
  background: linear-gradient(135deg, #e6a23c, #f56c6c);
}

.metric-icon.training {
  background: linear-gradient(135deg, #67c23a, #409eff);
}

.metric-icon.satisfaction {
  background: linear-gradient(135deg, #f56c6c, #e6a23c);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin: 4px 0;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.trend-stable {
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
}

.chart-placeholder {
  height: 100%;
  background: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}

.ai-insights,
.alerts-section {
  margin-bottom: 20px;
}

.insights-list {
  max-height: 400px;
  overflow-y: auto;
}

.insight-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.insight-icon {
  color: #e6a23c;
}

.insight-header h4 {
  margin: 0;
  flex: 1;
  font-size: 16px;
  font-weight: 600;
}

.insight-content {
  margin: 8px 0;
  color: #606266;
  line-height: 1.5;
}

.recommendations h5 {
  margin: 12px 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.recommendations li {
  margin-bottom: 4px;
  color: #606266;
  font-size: 14px;
}

.alerts-list {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  border-left: 4px solid;
}

.alert-warning {
  background: #fdf6ec;
  border-left-color: #e6a23c;
}

.alert-info {
  background: #f0f9ff;
  border-left-color: #409eff;
}

.alert-error {
  background: #fef0f0;
  border-left-color: #f56c6c;
}

.alert-icon {
  color: inherit;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.alert-content p {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: #606266;
}

.alert-time {
  font-size: 12px;
  color: #909399;
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.no-insights {
  text-align: center;
  padding: 40px 0;
}
</style>
