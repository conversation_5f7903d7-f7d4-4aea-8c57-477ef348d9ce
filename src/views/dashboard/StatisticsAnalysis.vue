<template>
  <div class="statistics-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><TrendCharts /></el-icon>
        统计分析
      </h2>
      <div class="header-actions">
        <el-button @click="handleRefreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="handleCustomAnalysis">
          <el-icon><Setting /></el-icon>
          自定义分析
        </el-button>
        <el-button @click="handleExportAnalysis">
          <el-icon><Download /></el-icon>
          导出分析
        </el-button>
      </div>
    </div>

    <!-- 分析维度选择 -->
    <el-card class="dimension-card">
      <template #header>
        <span>分析维度</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="时间范围">
            <el-select v-model="analysisParams.timeRange" @change="handleTimeRangeChange">
              <el-option label="近7天" value="7days"  />
              <el-option label="近30天" value="30days"  />
              <el-option label="近3个月" value="3months"  />
              <el-option label="近6个月" value="6months"  />
              <el-option label="近1年" value="1year"  />
              <el-option label="自定义" value="custom"  />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="分析类型">
            <el-select v-model="analysisParams.analysisType" @change="handleAnalysisTypeChange">
              <el-option label="人员分析" value="personnel"  />
              <el-option label="薪酬分析" value="salary"  />
              <el-option label="绩效分析" value="performance"  />
              <el-option label="考勤分析" value="attendance"  />
              <el-option label="招聘分析" value="recruitment"  />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="部门筛选">
            <el-select v-model="analysisParams.department" multiple collapse-tags>
              <el-option label="全部部门" value=""  />
              <el-option label="计算机学院" value="cs"  />
              <el-option label="机械工程学院" value="me"  />
              <el-option label="经济管理学院" value="em"  />
              <el-option label="外国语学院" value="fl"  />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="对比分析">
            <el-switch
              v-model="analysisParams.compareMode"
              active-text="开启"
              inactive-text="关闭"
              @change="handleCompareModeChange"
             />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 关键指标概览 -->
    <el-row :gutter="20" class="overview-metrics">
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon primary">
              <el-icon><User /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ overview.totalEmployees }}</div>
              <div class="metric-label">总员工数</div>
              <div class="metric-change">
                <span :class="overview.employeeChange >= 0 ? 'positive' : 'negative'">
                  {{ overview.employeeChange >= 0 ? '+' : '' }}{{ overview.employeeChange }}%
                </span>
                <span class="change-label">环比上期</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon success">
              <el-icon><Money /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ overview.avgSalary }}</div>
              <div class="metric-label">平均薪酬</div>
              <div class="metric-change">
                <span :class="overview.salaryChange >= 0 ? 'positive' : 'negative'">
                  {{ overview.salaryChange >= 0 ? '+' : '' }}{{ overview.salaryChange }}%
                </span>
                <span class="change-label">环比上期</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon warning">
              <el-icon><Star /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ overview.avgPerformance }}</div>
              <div class="metric-label">平均绩效</div>
              <div class="metric-change">
                <span :class="overview.performanceChange >= 0 ? 'positive' : 'negative'">
                  {{ overview.performanceChange >= 0 ? '+' : '' }}{{ overview.performanceChange }}%
                </span>
                <span class="change-label">环比上期</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon info">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ overview.attendanceRate }}%</div>
              <div class="metric-label">出勤率</div>
              <div class="metric-change">
                <span :class="overview.attendanceChange >= 0 ? 'positive' : 'negative'">
                  {{ overview.attendanceChange >= 0 ? '+' : '' }}{{ overview.attendanceChange }}%
                </span>
                <span class="change-label">环比上期</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表分析区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>趋势分析</span>
              <el-radio-group v-model="trendChartType" size="small">
                <el-radio-button label="line">折线图</el-radio-button>
                <el-radio-button label="bar">柱状图</el-radio-button>
                <el-radio-button label="area">面积图</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>分布分析</span>
              <el-radio-group v-model="distributionChartType" size="small">
                <el-radio-button label="pie">饼图</el-radio-button>
                <el-radio-button label="doughnut">环形图</el-radio-button>
                <el-radio-button label="radar">雷达图</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="distributionChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>部门对比</span>
          </template>
          <div ref="departmentChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>年龄结构</span>
          </template>
          <div ref="ageChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>学历分布</span>
          </template>
          <div ref="educationChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card class="data-table">
      <template #header>
        <div class="table-header">
          <span>详细数据</span>
          <div class="table-actions">
            <el-button size="small" @click="handleExportTable">
              <el-icon><Download /></el-icon>
              导出表格
            </el-button>
            <el-button size="small" @click="handleTableSettings">
              <el-icon><Setting /></el-icon>
              表格设置
            </el-button>
          </div>
        </div>
      </template>
      <el-table :data="tableData" v-loading="tableLoading" stripe style="width: 100%">
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="employeeCount" label="员工数量" width="100"  />
        <el-table-column prop="avgSalary" label="平均薪酬" width="120">
          <template #default="{ row }">
            ¥{{ row.avgSalary?.toLocaleString() || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="avgPerformance" label="平均绩效" width="100"  />
        <el-table-column prop="attendanceRate" label="出勤率" width="100">
          <template #default="{ row }">
            {{ row.attendanceRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="turnoverRate" label="离职率" width="100">
          <template #default="{ row }">
            {{ row.turnoverRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="recruitmentCount" label="招聘人数" width="100"  />
        <el-table-column prop="trainingHours" label="培训时长" width="100">
          <template #default="{ row }">
            {{ row.trainingHours }}小时
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleViewDetail(row)">
              查看详情
            </el-button>
            <el-button type="text" size="small" @click="handleDrillDown(row)">
              下钻分析
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分析洞察 -->
    <el-card class="insights-card">
      <template #header>
        <span>分析洞察</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="8" v-for="insight in insights" :key="insight.id">
          <div class="insight-item">
            <div class="insight-icon" :class="insight.type">
              <el-icon>
                <TrendCharts v-if="insight.type === 'positive'" />
                <Warning v-else-if="insight.type === 'warning'" />
                <InfoFilled v-else />
              </el-icon>
            </div>
            <div class="insight-content">
              <div class="insight-title">{{ insight.title }}</div>
              <div class="insight-desc">{{ insight.description }}</div>
              <div class="insight-recommendations" v-if="insight.recommendations && insight.recommendations.length">
                <el-tag 
                  v-for="(rec, index) in insight.recommendations.slice(0, 2)" 
                  :key="index"
                  size="small"
                  type="info"
                  style="margin-right: 4px; margin-top: 4px;"
                >
                  {{ rec }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  Refresh,
  Setting,
  Download,
  User,
  Money,
  Star,
  Clock,
  Warning,
  InfoFilled
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { statisticsApi } from '@/api/statistics'
import type { AnalysisParams, StatisticsAnalysisResult } from '@/api/statistics'

// 响应式数据
const tableLoading = ref(false)
const trendChartType = ref('line')
const distributionChartType = ref('pie')

// 图表引用
const trendChartRef = ref<HTMLDivElement>()
const distributionChartRef = ref<HTMLDivElement>()
const departmentChartRef = ref<HTMLDivElement>()
const ageChartRef = ref<HTMLDivElement>()
const educationChartRef = ref<HTMLDivElement>()

// 分析参数
const analysisParams = reactive({
  timeRange: '30days',
  analysisType: 'personnel',
  department: [],
  compareMode: false
})

// 概览数据
const overview = reactive({
  totalEmployees: 1256,
  employeeChange: 3.2,
  avgSalary: 8500,
  salaryChange: 5.8,
  avgPerformance: 4.2,
  performanceChange: 2.1,
  attendanceRate: 96.8,
  attendanceChange: 1.2
})

// 表格数据
const tableData = ref<any[]>([])

// 图表实例
let trendChart: echarts.ECharts | null = null
let distributionChart: echarts.ECharts | null = null
let departmentChart: echarts.ECharts | null = null
let ageChart: echarts.ECharts | null = null
let educationChart: echarts.ECharts | null = null

// 分析洞察
const insights = ref<any[]>([])

// 方法
const handleRefreshData = () => {
  ElMessage.success('数据已刷新')
  loadAnalysisData()
}

const handleCustomAnalysis = () => {
  ElMessage.info('自定义分析功能开发中')
}

const handleExportAnalysis = () => {
  ElMessage.info('导出分析功能开发中')
}

const handleTimeRangeChange = () => {
  loadAnalysisData()
}

const handleAnalysisTypeChange = () => {
  loadAnalysisData()
}

const handleCompareModeChange = () => {
  loadAnalysisData()
}

const handleExportTable = () => {
  ElMessage.info('导出表格功能开发中')
}

const handleTableSettings = () => {
  ElMessage.info('表格设置功能开发中')
}

   
const handleViewDetail = (row: unknown) => {
  ElMessage.info(`查看 ${row.department} 的详细信息`)
}

   
const handleDrillDown = (row: unknown) => {
  ElMessage.info(`对 ${row.department} 进行下钻分析`)
}

const loadAnalysisData = async () => {
  tableLoading.value = true
  try {
    // 构建分析参数
    const params: AnalysisParams = {
      timeRange: analysisParams.timeRange,
      analysisType: analysisParams.analysisType,
      department: analysisParams.department,
      compareMode: analysisParams.compareMode
    }
    
    // 调用API获取数据
    const result: StatisticsAnalysisResult = await statisticsApi.getAnalysisData(params)
    
    // 更新概览数据
    if (result.overview) {
      Object.assign(overview, result.overview)
    }
    
    // 更新表格数据
    if (result.departmentStats) {
      tableData.value = result.departmentStats
    }
    
    // 更新洞察数据
    if (result.insights) {
      insights.value = result.insights
    }
    
    // 更新图表数据
    updateCharts(result)
    
    ElMessage.success('数据加载成功')
  } catch (__error) {
    console.error('加载分析数据失败:', error)
    ElMessage.error('数据加载失败，请稍后重试')
  } finally {
    tableLoading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  // 初始化趋势图
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
    window.addEventListener('resize', () => trendChart?.resize())
  }
  
  // 初始化分布图
  if (distributionChartRef.value) {
    distributionChart = echarts.init(distributionChartRef.value)
    window.addEventListener('resize', () => distributionChart?.resize())
  }
  
  // 初始化部门对比图
  if (departmentChartRef.value) {
    departmentChart = echarts.init(departmentChartRef.value)
    window.addEventListener('resize', () => departmentChart?.resize())
  }
  
  // 初始化年龄结构图
  if (ageChartRef.value) {
    ageChart = echarts.init(ageChartRef.value)
    window.addEventListener('resize', () => ageChart?.resize())
  }
  
  // 初始化学历分布图
  if (educationChartRef.value) {
    educationChart = echarts.init(educationChartRef.value)
    window.addEventListener('resize', () => educationChart?.resize())
  }
}

// 更新图表数据
const updateCharts = (data: StatisticsAnalysisResult) => {
  // 更新趋势图
  if (trendChart && data.trendData) {
    const trendOption: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: data.trendData.datasets.map(d => d.label),
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.trendData.labels
      },
      yAxis: {
        type: 'value'
      },
      series: data.trendData.datasets.map(dataset => ({
        name: dataset.label,
        type: dataset.type || 'line',
        data: dataset.data,
        smooth: true
      }))
    }
    trendChart.setOption(trendOption)
  }
  
  // 更新分布图
  if (distributionChart && data.distributionData?.department) {
    const distributionOption: echarts.EChartsOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: 'HrHr部门分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data.distributionData.department.labels.map((label, index) => ({
            name: label,
            value: data.distributionData.department.data[index]
          }))
        }
      ]
    }
    distributionChart.setOption(distributionOption)
  }
  
  // 更新部门对比图
  if (departmentChart && data.departmentStats) {
    const departmentOption: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['员工数量', '平均薪资', '平均绩效'],
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.departmentStats.map(d => d.department),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '人数',
          position: 'left'
        },
        {
          type: 'value',
          name: '薪资(元)',
          position: 'right'
        }
      ],
      series: [
        {
          name: '员工数量',
          type: 'bar',
          data: data.departmentStats.map(d => d.employeeCount)
        },
        {
          name: '平均薪资',
          type: 'line',
          yAxisIndex: 1,
          data: data.departmentStats.map(d => d.avgSalary)
        }
      ]
    }
    departmentChart.setOption(departmentOption)
  }
  
  // 更新年龄结构图
  if (ageChart && data.distributionData?.age) {
    const ageOption: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.distributionData.age.labels
      },
      yAxis: {
        type: 'value',
        name: '人数'
      },
      series: [
        {
          name: '人数',
          type: 'bar',
          data: data.distributionData.age.data,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }
      ]
    }
    ageChart.setOption(ageOption)
  }
  
  // 更新学历分布图
  if (educationChart && data.distributionData?.education) {
    const educationOption: echarts.EChartsOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: '学历分布',
          type: 'pie',
          radius: '60%',
          data: data.distributionData.education.labels.map((label, index) => ({
            name: label,
            value: data.distributionData.education.data[index]
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    educationChart.setOption(educationOption)
  }
}

onMounted(() => {
  initCharts()
  loadAnalysisData()
})

onUnmounted(() => {
  // 销毁图表实例
  trendChart?.dispose()
  distributionChart?.dispose()
  departmentChart?.dispose()
  ageChart?.dispose()
  educationChart?.dispose()
})
</script>

<style scoped>
.statistics-analysis {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.dimension-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.overview-metrics {
  margin-bottom: 20px;
}

.metric-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.metric-icon.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-icon.success {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-icon.warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-icon.info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 6px;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
}

.positive {
  color: #67c23a;
  font-weight: 600;
}

.negative {
  color: #f56c6c;
  font-weight: 600;
}

.change-label {
  font-size: 12px;
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.data-table {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.insights-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.insight-item:hover {
  background: #e9ecef;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.insight-icon.positive {
  background: #67c23a;
}

.insight-icon.warning {
  background: #e6a23c;
}

.insight-icon.info {
  background: #409eff;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.insight-desc {
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
}
</style>
