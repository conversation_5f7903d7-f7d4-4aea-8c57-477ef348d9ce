<template>
  <div class="appraisal-360-feedback">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>360度评价管理</h2>
      <p>全方位收集上级、同级、下级及自我评价，为员工发展提供多维度反馈</p>
    </div>

    <!-- 评价活动管理 -->
    <el-tabs v-model="activeTab" class="main-tabs">
      <el-tab-pane label="评价活动" name="activities">
        <!-- 筛选条件 -->
        <el-card class="filter-card" shadow="never">
          <el-form :model="filterForm" inline>
            <el-form-item label="活动状态">
              <el-select v-model="filterForm.status" clearable>
                <el-option label="全部状态" value=""  />
                <el-option label="筹备中" value="PREPARING"  />
                <el-option label="进行中" value="IN_PROGRESS"  />
                <el-option label="已结束" value="COMPLETED"  />
                <el-option label="已归档" value="ARCHIVED"  />
              </el-select>
            </el-form-item>
            <el-form-item label="评价周期">
              <el-select v-model="filterForm.periodId" clearable>
                <el-option label="全部周期" value=""  />
                <el-option
                  v-for="period in periodOptions"
                  :key="period.id"
                  :label="period.name"
                  :value="period.id"
                 />
              </el-select>
            </el-form-item>
            <el-form-item label="部门">
              <el-tree-select
                v-model="filterForm.departmentId"
                :data="departmentTree"
                :props="{ label: 'name', value: 'id' }"
                clearable
                placeholder="请选择部门"
               />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="handleReset">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button type="success" @click="handleCreateActivity">
                <el-icon><Plus /></el-icon>
                创建活动
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 活动列表 -->
        <el-card class="table-card" shadow="never">
          <el-table
            v-loading="loading"
            :data="activityList"
            stripe
          >
            <el-table-column prop="name" label="活动名称" min-width="200">
              <template #default="scope">
                <el-link type="primary" @click="handleViewActivity(scope.row)">
                  {{ scope.row.name }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="period" label="评价周期" width="150"  />
            <el-table-column prop="targetCount" label="参评人数" width="100" align="center"  />
            <el-table-column label="进度" width="200">
              <template #default="scope">
                <div class="progress-wrapper">
                  <el-progress
                    :percentage="scope.row.progress"
                    :stroke-width="6"
                    :color="getProgressColor(scope.row.progress)"
                   />
                  <span class="progress-text">{{ scope.row.completedCount }}/{{ scope.row.totalCount }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="startTime" label="开始时间" width="160"  />
            <el-table-column prop="endTime" label="结束时间" width="160"  />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getActivityStatusType(scope.row.status)">
                  {{ getActivityStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <el-button
                  v-if="scope.row.status === 'PREPARING'"
                  size="small"
                  type="primary"
                  @click="handleStartActivity(scope.row)"
                >
                  启动
                </el-button>
                <el-button
                  size="small"
                  @click="handleViewActivity(scope.row)"
                >
                  详情
                </el-button>
                <el-button
                  v-if="scope.row.status === 'IN_PROGRESS'"
                  size="small"
                  type="warning"
                  @click="handleMonitor(scope.row)"
                >
                  监控
                </el-button>
                <el-dropdown v-if="scope.row.status !== 'ARCHIVED'">
                  <el-button size="small" link>
                    更多<el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleEditActivity(scope.row)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item 
                        v-if="scope.row.status === 'COMPLETED'"
                        @click="handleExportReport(scope.row)"
                      >
                        <el-icon><Download /></el-icon>
                        导出报告
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="scope.row.status === 'COMPLETED'"
                        @click="handleArchive(scope.row)"
                      >
                        <el-icon><FolderAdd /></el-icon>
                        归档
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
             />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 我的评价 -->
      <el-tab-pane label="我的评价" name="myEvaluations">
        <el-card class="evaluation-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">待完成的评价任务</span>
              <el-tag type="warning">{{ pendingEvaluations.length }} 项待完成</el-tag>
            </div>
          </template>

          <el-table
            :data="pendingEvaluations"
            stripe
          >
            <el-table-column prop="targetName" label="被评价人" width="120"  />
            <el-table-column prop="targetDept" label="部门" width="150"  />
            <el-table-column prop="targetPosition" label="岗位" width="120"  />
            <el-table-column prop="relation" label="关系" width="100">
              <template #default="scope">
                <el-tag :type="getRelationTagType(scope.row.relation)" size="small">
                  {{ getRelationText(scope.row.relation) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="activityName" label="评价活动" min-width="200"  />
            <el-table-column prop="deadline" label="截止时间" width="160">
              <template #default="scope">
                <span :class="{ 'deadline-warning': isDeadlineSoon(scope.row.deadline) }">
                  {{ scope.row.deadline }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleEvaluate(scope.row)"
                >
                  去评价
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 已完成的评价 -->
        <el-card class="evaluation-card" shadow="never">
          <template #header>
            <span class="card-title">已完成的评价</span>
          </template>

          <el-table
            :data="completedEvaluations"
            stripe
          >
            <el-table-column prop="targetName" label="被评价人" width="120"  />
            <el-table-column prop="relation" label="关系" width="100">
              <template #default="scope">
                <el-tag :type="getRelationTagType(scope.row.relation)" size="small">
                  {{ getRelationText(scope.row.relation) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="综合评分" width="100" align="center">
              <template #default="scope">
                <el-tag type="primary">{{ scope.row.score }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="completedTime" label="评价时间" width="160"  />
            <el-table-column prop="comments" label="评价要点" min-width="300" show-overflow-tooltip  />
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  size="small"
                  @click="handleViewEvaluation(scope.row)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 我的反馈 -->
      <el-tab-pane label="我的反馈" name="myFeedback">
        <el-card class="feedback-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">360度评价反馈报告</span>
              <el-button type="primary" @click="handleDownloadReport">
                <el-icon><Download /></el-icon>
                下载报告
              </el-button>
            </div>
          </template>

          <!-- 总体评价 -->
          <div class="overall-feedback">
            <h4>总体评价</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="score-card">
                  <div class="score-label">综合得分</div>
                  <div class="score-value">{{ feedbackData.overallScore }}</div>
                  <div class="score-rank">排名: {{ feedbackData.rank }}/{{ feedbackData.total }}</div>
                </div>
              </el-col>
              <el-col :span="18">
                <div class="radar-chart" id="radarChart"></div>
              </el-col>
            </el-row>
          </div>

          <!-- 分维度反馈 -->
          <div class="dimension-feedback">
            <h4>分维度反馈</h4>
            <el-table :data="feedbackData.dimensions" stripe>
              <el-table-column prop="dimension" label="评价维度" width="150"  />
              <el-table-column label="各方评分" min-width="400">
                <template #default="scope">
                  <div class="score-breakdown">
                    <div
                      v-for="(score, index) in scope.row.scores"
                      :key="index"
                      class="score-item"
                    >
                      <span class="score-source">{{ score.source }}:</span>
                      <el-progress
                        :percentage="score.value"
                        :stroke-width="10"
                        :color="getScoreColor(score.value)"
                       />
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="average" label="平均分" width="100" align="center">
                <template #default="scope">
                  <el-tag type="primary">{{ scope.row.average }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 文字反馈 -->
          <div class="text-feedback">
            <h4>文字反馈汇总</h4>
            <el-collapse>
              <el-collapse-item
                v-for="(feedback, index) in feedbackData.textFeedback"
                :key="index"
                :title="`${feedback.source}的反馈`"
                :name="index"
              >
                <div class="feedback-content">
                  <div class="feedback-section">
                    <h5>优点与长处：</h5>
                    <p>{{ feedback.strengths }}</p>
                  </div>
                  <div class="feedback-section">
                    <h5>改进建议：</h5>
                    <p>{{ feedback.improvements }}</p>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑活动对话框 -->
    <el-dialog
      v-model="activityDialogVisible"
      :title="activityDialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="activityFormRef"
        :model="activityForm"
        :rules="activityRules"
        label-width="120px"
      >
        <el-form-item label="活动名称" prop="name">
          <el-input v-model="activityForm.name" placeholder="请输入活动名称"   />
        </el-form-item>
        
        <el-form-item label="评价周期" prop="periodId">
          <el-select v-model="activityForm.periodId" placeholder="请选择评价周期">
            <el-option
              v-for="period in periodOptions"
              :key="period.id"
              :label="period.name"
              :value="period.id"
             />
          </el-select>
        </el-form-item>

        <el-form-item label="参评范围" prop="scope">
          <el-radio-group v-model="activityForm.scope">
            <el-radio value="ALL">全员参与</el-radio>
            <el-radio value="DEPARTMENT">指定部门</el-radio>
            <el-radio value="CUSTOM">自定义</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="activityForm.scope === 'DEPARTMENT'"
          label="选择部门"
          prop="departments"
        >
          <el-tree-select
            v-model="activityForm.departments"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            multiple
            show-checkbox
            placeholder="请选择部门"
           />
        </el-form-item>

        <el-form-item label="评价关系" prop="relations">
          <el-checkbox-group v-model="activityForm.relations">
            <el-checkbox label="SELF">自评</el-checkbox>
            <el-checkbox label="SUPERIOR">上级评价</el-checkbox>
            <el-checkbox label="PEER">同级评价</el-checkbox>
            <el-checkbox label="SUBORDINATE">下级评价</el-checkbox>
            <el-checkbox label="CUSTOMER">客户评价</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="活动时间" prop="timeRange">
          <el-date-picker
            v-model="activityForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
           />
        </el-form-item>

        <el-form-item label="评价模板" prop="templateId">
          <el-select v-model="activityForm.templateId" placeholder="请选择评价模板">
            <el-option
              v-for="template in templateOptions"
              :key="template.id"
              :label="template.name"
              :value="template.id"
             />
          </el-select>
        </el-form-item>

        <el-form-item label="匿名设置" prop="anonymous">
          <el-switch
            v-model="activityForm.anonymous"
            active-text="匿名评价"
            inactive-text="实名评价"
           />
        </el-form-item>

        <el-form-item label="活动说明" prop="description">
          <el-input
            v-model="activityForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入活动说明"
            />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="activityDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveActivity">保存</el-button>
      </template>
    </el-dialog>

    <!-- 评价对话框 -->
    <el-dialog
      v-model="evaluationDialogVisible"
      title="360度评价"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="evaluation-dialog-content">
        <!-- 被评价人信息 -->
        <el-card class="target-info-card" shadow="never">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="姓名">{{ currentEvaluation?.targetName }}</el-descriptions-item>
            <el-descriptions-item label="部门">{{ currentEvaluation?.targetDept }}</el-descriptions-item>
            <el-descriptions-item label="岗位">{{ currentEvaluation?.targetPosition }}</el-descriptions-item>
            <el-descriptions-item label="评价关系">
              <el-tag :type="getRelationTagType(currentEvaluation?.relation)">
                {{ getRelationText(currentEvaluation?.relation) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="活动名称" :span="2">{{ currentEvaluation?.activityName }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 评价表单 -->
        <el-form
          ref="evaluationFormRef"
          :model="evaluationForm"
          label-width="120px"
          class="evaluation-form"
        >
          <!-- 维度评分 -->
          <div class="dimension-scoring">
            <h4>维度评分</h4>
            <el-table :data="evaluationForm.dimensions" stripe>
              <el-table-column prop="name" label="评价维度" width="150"  />
              <el-table-column prop="description" label="维度说明" min-width="300" show-overflow-tooltip  />
              <el-table-column label="评分" width="200">
                <template #default="scope">
                  <el-rate
                    v-model="scope.row.score"
                    :max="10"
                    :colors="rateColors"
                    :texts="rateTexts"
                    show-text
                    text-color="#ff9900"
                   />
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 文字评价 -->
          <el-form-item label="优点与长处" prop="strengths" class="text-evaluation">
            <el-input
              v-model="evaluationForm.strengths"
              type="textarea"
              :rows="4"
              placeholder="请描述被评价人的优点与长处..."
              maxlength="500"
              show-word-limit
              />
          </el-form-item>

          <el-form-item label="改进建议" prop="improvements">
            <el-input
              v-model="evaluationForm.improvements"
              type="textarea"
              :rows="4"
              placeholder="请提供改进建议..."
              maxlength="500"
              show-word-limit
              />
          </el-form-item>

          <el-form-item label="综合评价" prop="overall">
            <el-input
              v-model="evaluationForm.overall"
              type="textarea"
              :rows="3"
              placeholder="请输入综合评价..."
              maxlength="300"
              show-word-limit
              />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="evaluationDialogVisible = false">取消</el-button>
        <el-button type="warning" @click="handleSaveDraft">暂存</el-button>
        <el-button type="primary" @click="handleSubmitEvaluation">提交评价</el-button>
      </template>
    </el-dialog>

    <!-- 活动监控对话框 -->
    <el-dialog
      v-model="monitorDialogVisible"
      title="活动进度监控"
      width="1000px"
    >
      <div class="monitor-content">
        <!-- 总体进度 -->
        <el-card class="monitor-card" shadow="never">
          <template #header>
            <span class="card-title">总体进度</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="总参评人数" :value="monitorData.totalCount"  />
            </el-col>
            <el-col :span="8">
              <el-statistic title="已完成人数" :value="monitorData.completedCount"  />
            </el-col>
            <el-col :span="8">
              <el-statistic title="完成率" :value="monitorData.completionRate" suffix="%"  />
            </el-col>
          </el-row>
        </el-card>

        <!-- 部门进度 -->
        <el-card class="monitor-card" shadow="never">
          <template #header>
            <span class="card-title">部门进度明细</span>
          </template>
          <el-table :data="monitorData.departmentProgress" stripe>
            <el-table-column prop="department" label="部门" width="200"  />
            <el-table-column prop="totalCount" label="应评人数" width="100" align="center"  />
            <el-table-column prop="completedCount" label="已完成" width="100" align="center"  />
            <el-table-column label="完成率" width="150">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.completionRate"
                  :stroke-width="10"
                  :color="getProgressColor(scope.row.completionRate)"
                 />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button
                  size="small"
                  @click="handleSendReminder(scope.row)"
                >
                  发送提醒
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="monitorDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */

import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  ArrowDown,
  Edit,
  Download,
  FolderAdd
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const activeTab = ref('activities')
const activityList = ref<any[]>([])
const pendingEvaluations = ref<any[]>([])
const completedEvaluations = ref<any[]>([])
const periodOptions = ref<any[]>([])
const departmentTree = ref<any[]>([])
const templateOptions = ref<any[]>([])

// 筛选表单
const filterForm = reactive({
  status: '',
  periodId: '',
  departmentId: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框控制
const activityDialogVisible = ref(false)
const activityDialogTitle = ref('')
const evaluationDialogVisible = ref(false)
const monitorDialogVisible = ref(false)
const currentEvaluation = ref<unknown>(null)

// 活动表单
const activityFormRef = ref()
const activityForm = reactive({
  id: '',
  name: '',
  periodId: '',
  scope: 'ALL',
  departments: [] as string[],
  relations: ['SELF', 'SUPERIOR', 'PEER'],
  timeRange: [],
  templateId: '',
  anonymous: true,
  description: ''
})

// 评价表单
const evaluationFormRef = ref()
const evaluationForm = reactive({
  dimensions: [] as unknown[],
  strengths: '',
  improvements: '',
  overall: ''
})

// 反馈数据
const feedbackData = reactive({
  overallScore: 88.5,
  rank: 12,
  total: 156,
  dimensions: [] as unknown[],
  textFeedback: [] as unknown[]
})

// 监控数据
const monitorData = reactive({
  totalCount: 0,
  completedCount: 0,
  completionRate: 0,
  departmentProgress: [] as unknown[]
})

// 评分配置
const rateColors = ['#99A9BF', '#F7BA2A', '#FF9900']
const rateTexts = ['极差', '差', '一般', '良好', '优秀', '非常优秀', '卓越', '杰出', '完美', '无可挑剔']

// 表单验证规则
const activityRules = {
  name: [
    { required: true, message: '请输入活动名称', trigger: 'blur' }
  ],
  periodId: [
    { required: true, message: '请选择评价周期', trigger: 'change' }
  ],
  relations: [
    { required: true, message: '请选择评价关系', trigger: 'change' }
  ],
  timeRange: [
    { required: true, message: '请选择活动时间', trigger: 'change' }
  ],
  templateId: [
    { required: true, message: '请选择评价模板', trigger: 'change' }
  ]
}

// 初始化数据
const initData = async () => {
  // 加载期间选项
  periodOptions.value = [
    { id: '1', name: 'HrHr2025年度360评价' },
    { id: '2', name: '2025年中期360评价' },
    { id: '3', name: '2024年度360评价' }
  ]
  
  // 加载部门树
  departmentTree.value = [
    {
      id: '1',
      name: '杭科院',
      children: [
        { id: '11', name: '计算机学院' },
        { id: '12', name: '机械工程学院' },
        { id: '13', name: '电气工程学院' }
      ]
    }
  ]
  
  // 加载模板选项
  templateOptions.value = [
    { id: '1', name: '通用360度评价模板' },
    { id: '2', name: '管理人员360度评价模板' },
    { id: '3', name: '技术人员360度评价模板' }
  ]
  
  fetchActivityList()
  fetchMyEvaluations()
  fetchMyFeedback()
}

// 获取活动列表
const fetchActivityList = async () => {
  try {
    loading.value = true
    
    // 模拟数据
    activityList.value = [
      {
        id: '1',
        name: '2025年度360度评价活动',
        period: '2025年度',
        targetCount: 156,
        completedCount: 102,
        totalCount: 468,
        progress: 65,
        startTime: '2025-06-01 00:00:00',
        endTime: '2025-06-30 23:59:59',
        status: 'IN_PROGRESS'
      },
      {
        id: '2',
        name: '2025年新员工360度评价',
        period: '2025年第一季度',
        targetCount: 45,
        completedCount: 0,
        totalCount: 135,
        progress: 0,
        startTime: '2025-07-01 00:00:00',
        endTime: '2025-07-15 23:59:59',
        status: 'PREPARING'
      },
      {
        id: '3',
        name: '2024年度360度评价活动',
        period: '2024年度',
        targetCount: 148,
        completedCount: 444,
        totalCount: 444,
        progress: 100,
        startTime: '2024-12-01 00:00:00',
        endTime: '2024-12-31 23:59:59',
        status: 'COMPLETED'
      }
    ]
    
    pagination.total = activityList.value.length
  } catch(_error: unknown) {
    console.error('获取活动列表失败:', error)
    ElMessage.error('获取活动列表失败')
  } finally {
    loading.value = false
  }
}

// 获取我的评价任务
const fetchMyEvaluations = async () => {
  // 模拟待完成的评价
  pendingEvaluations.value = [
    {
      id: '1',
      targetName: '张三',
      targetDept: '计算机学院',
      targetPosition: '副教授',
      relation: 'PEER',
      activityName: '2025年度360度评价活动',
      deadline: '2025-06-30 23:59:59'
    },
    {
      id: '2',
      targetName: '李四',
      targetDept: '计算机学院',
      targetPosition: '讲师',
      relation: 'SUBORDINATE',
      activityName: '2025年度360度评价活动',
      deadline: '2025-06-30 23:59:59'
    }
  ]
  
  // 模拟已完成的评价
  completedEvaluations.value = [
    {
      id: '3',
      targetName: '王五',
      relation: 'SUPERIOR',
      score: 92,
      completedTime: '2025-06-15 14:30:00',
      comments: '工作认真负责，领导能力强，团队管理有方...'
    }
  ]
}

// 获取我的反馈
const fetchMyFeedback = async () => {
  // 模拟反馈数据
  feedbackData.dimensions = [
    {
      dimension: '专业能力',
      scores: [
        { source: '自评', value: 90 },
        { source: '上级', value: 88 },
        { source: '同级', value: 85 },
        { source: '下级', value: 92 }
      ],
      average: 88.8
    },
    {
      dimension: '沟通协作',
      scores: [
        { source: '自评', value: 85 },
        { source: '上级', value: 87 },
        { source: '同级', value: 90 },
        { source: '下级', value: 88 }
      ],
      average: 87.5
    },
    {
      dimension: '创新能力',
      scores: [
        { source: '自评', value: 88 },
        { source: '上级', value: 85 },
        { source: '同级', value: 83 },
        { source: '下级', value: 86 }
      ],
      average: 85.5
    }
  ]
  
  feedbackData.textFeedback = [
    {
      source: '上级',
      strengths: '专业能力强，工作认真负责，能够很好地完成各项任务。团队协作意识强，乐于帮助同事。',
      improvements: '建议加强创新思维，多提出新的想法和解决方案。在项目管理方面可以更加主动。'
    },
    {
      source: '同级',
      strengths: '沟通能力出色，团队合作精神强。技术功底扎实，解决问题能力强。',
      improvements: '有时过于追求完美，建议在时间管理上更加灵活。可以多参与跨部门合作项目。'
    }
  ]
  
  // 绘制雷达图
  nextTick(() => {
    initRadarChart()
  })
}

// 初始化雷达图
const initRadarChart = () => {
  const chartDom = document.getElementById('radarChart')
  if(chartDom: unknown) {
    const radarChart = echarts.init(chartDom)
    const option = {
      radar: {
        indicator: [
          { name: '专业能力', max: 100 },
          { name: '沟通协作', max: 100 },
          { name: '创新能力', max: 100 },
          { name: '领导力', max: 100 },
          { name: '执行力', max: 100 },
          { name: '学习能力', max: 100 }
        ]
      },
      series: [{
        type: 'radar',
        data: [
          {
            value: [88, 87, 85, 82, 90, 86],
            name: '综合评价',
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.2)'
            }
          }
        ]
      }]
    }
    radarChart.setOption(option)
    
    // 响应式
    window.addEventListener('resize', () => {
      radarChart.resize()
    })
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchActivityList()
}

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    status: '',
    periodId: '',
    departmentId: ''
  })
  fetchActivityList()
}

// 创建活动
const handleCreateActivity = () => {
  Object.assign(activityForm, {
    id: '',
    name: '',
    periodId: '',
    scope: 'ALL',
    departments: [],
    relations: ['SELF', 'SUPERIOR', 'PEER'],
    timeRange: [],
    templateId: '',
    anonymous: true,
    description: ''
  })
  activityDialogTitle.value = '创建360度评价活动'
  activityDialogVisible.value = true
}

// 编辑活动
   
const handleEditActivity = (row: unknown) => {
  Object.assign(activityForm, row)
  activityDialogTitle.value = '编辑360度评价活动'
  activityDialogVisible.value = true
}

// 查看活动
   
const handleViewActivity = (row: unknown) => {
  ElMessage.info('查看活动详情功能开发中...')
}

// 启动活动
   
const handleStartActivity = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要启动该评价活动吗？启动后将通知所有参评人员。',
      '启动确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用启动API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('活动启动成功')
    fetchActivityList()
  } catch(_error: unknown) {
    if(error !== 'cancel': unknown) {
      console.error('启动失败:', error)
      ElMessage.error('启动失败')
    }
  }
}

// 监控活动
   
const handleMonitor = (row: unknown) => {
  // 加载监控数据
  monitorData.totalCount = row.targetCount * 3
  monitorData.completedCount = row.completedCount
  monitorData.completionRate = Math.round((row.completedCount / (row.targetCount * 3)) * 100)
  
  monitorData.departmentProgress = [
    {
      department: '计算机学院',
      totalCount: 60,
      completedCount: 45,
      completionRate: 75
    },
    {
      department: '机械工程学院',
      totalCount: 48,
      completedCount: 30,
      completionRate: 62.5
    },
    {
      department: '电气工程学院',
      totalCount: 36,
      completedCount: 27,
      completionRate: 75
    }
  ]
  
  monitorDialogVisible.value = true
}

// 导出报告
   
const handleExportReport = (row: unknown) => {
  ElMessage.success('正在导出360度评价报告...')
}

// 归档活动
   
const handleArchive = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要归档该活动吗？归档后将无法修改。',
      '归档确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用归档API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('归档成功')
    fetchActivityList()
  } catch(_error: unknown) {
    if(error !== 'cancel': unknown) {
      console.error('归档失败:', error)
      ElMessage.error('归档失败')
    }
  }
}

// 保存活动
const handleSaveActivity = async () => {
  try {
    await activityFormRef.value.validate()
    
    // 调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('保存成功')
    activityDialogVisible.value = false
    fetchActivityList()
  } catch(_error: unknown) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 开始评价
   
const handleEvaluate = (row: unknown) => {
  currentEvaluation.value = row
  
  // 加载评价维度
  evaluationForm.dimensions = [
    {
      name: '专业能力',
      description: '专业知识掌握程度、技术能力、问题解决能力等',
      score: 0
    },
    {
      name: '沟通协作',
      description: '团队合作精神、沟通表达能力、人际关系处理等',
      score: 0
    },
    {
      name: '创新能力',
      description: '创新思维、改进意识、学习能力等',
      score: 0
    },
    {
      name: '工作态度',
      description: '责任心、积极性、敬业精神等',
      score: 0
    },
    {
      name: '执行力',
      description: '任务完成质量、工作效率、目标达成等',
      score: 0
    }
  ]
  
  evaluationForm.strengths = ''
  evaluationForm.improvements = ''
  evaluationForm.overall = ''
  
  evaluationDialogVisible.value = true
}

// 查看评价
   
const handleViewEvaluation = (row: unknown) => {
  ElMessage.info('查看评价详情功能开发中...')
}

// 暂存评价
const handleSaveDraft = async () => {
  try {
    // 调用暂存API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('评价已暂存')
    evaluationDialogVisible.value = false
  } catch(_error: unknown) {
    console.error('暂存失败:', error)
    ElMessage.error('暂存失败')
  }
}

// 提交评价
const handleSubmitEvaluation = async () => {
  try {
    // 验证是否所有维度都已评分
    const allScored = evaluationForm.dimensions.every(d => d.score > 0)
    if(!allScored: unknown) {
      ElMessage.warning('请完成所有维度的评分')
      return
    }
    
    await ElMessageBox.confirm(
      '确定要提交评价吗？提交后将无法修改。',
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用提交API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('评价提交成功')
    evaluationDialogVisible.value = false
    fetchMyEvaluations()
  } catch(_error: unknown) {
    if(error !== 'cancel': unknown) {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  }
}

// 下载反馈报告
const handleDownloadReport = () => {
  ElMessage.success('正在生成个人360度反馈报告...')
}

// 发送提醒
   
const handleSendReminder = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要向${row.department}发送评价提醒吗？`,
      '发送提醒',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用发送提醒API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('提醒已发送')
  } catch(_error: unknown) {
    if(error !== 'cancel': unknown) {
      console.error('发送失败:', error)
      ElMessage.error('发送失败')
    }
  }
}

// 获取进度颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 获取活动状态类型
const getActivityStatusType = (status: string) => {
  switch(status: unknown) {
    case 'PREPARING': return 'info'
    case 'IN_PROGRESS': return 'primary'
    case 'COMPLETED': return 'success'
    case 'ARCHIVED': return 'warning'
    default: return ''
  }
}

// 获取活动状态文本
const getActivityStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PREPARING': '筹备中',
    'IN_PROGRESS': '进行中',
    'COMPLETED': '已结束',
    'ARCHIVED': '已归档'
  }
  return statusMap[status] || status
}

// 获取关系标签类型
const getRelationTagType = (relation: string) => {
  switch(relation: unknown) {
    case 'SELF': return 'primary'
    case 'SUPERIOR': return 'success'
    case 'PEER': return 'warning'
    case 'SUBORDINATE': return 'info'
    case 'CUSTOMER': return 'danger'
    default: return ''
  }
}

// 获取关系文本
const getRelationText = (relation: string) => {
  const relationMap: Record<string, string> = {
    'SELF': '自评',
    'SUPERIOR': '上级',
    'PEER': '同级',
    'SUBORDINATE': '下级',
    'CUSTOMER': '客户'
  }
  return relationMap[relation] || relation
}

// 判断是否临近截止
const isDeadlineSoon = (deadline: string) => {
  const deadlineTime = new Date(deadline).getTime()
  const currentTime = new Date().getTime()
  const diffDays = (deadlineTime - currentTime) / (1000 * 60 * 60 * 24)
  return diffDays <= 3
}

// 获取分数颜色
const getScoreColor = (score: number) => {
  if (score >= 90) return '#67c23a'
  if (score >= 70) return '#e6a23c'
  return '#f56c6c'
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchActivityList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchActivityList()
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<style scoped>
.appraisal-360-feedback {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.main-tabs {
  margin-top: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card,
.evaluation-card,
.feedback-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.progress-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.deadline-warning {
  color: #f56c6c;
  font-weight: 600;
}

.overall-feedback {
  margin-bottom: 30px;
}

.overall-feedback h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.score-card {
  text-align: center;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.score-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.score-value {
  font-size: 36px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 10px;
}

.score-rank {
  font-size: 13px;
  color: #606266;
}

.radar-chart {
  height: 300px;
}

.dimension-feedback {
  margin-bottom: 30px;
}

.dimension-feedback h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.score-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.score-source {
  width: 60px;
  font-size: 13px;
  color: #606266;
}

.text-feedback {
  margin-bottom: 30px;
}

.text-feedback h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.feedback-content {
  padding: 10px 0;
}

.feedback-section {
  margin-bottom: 20px;
}

.feedback-section h5 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.feedback-section p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.evaluation-dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.target-info-card {
  margin-bottom: 20px;
}

.evaluation-form {
  margin-top: 20px;
}

.dimension-scoring {
  margin-bottom: 20px;
}

.dimension-scoring h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.text-evaluation {
  margin-top: 20px;
}

.monitor-content {
  max-height: 500px;
  overflow-y: auto;
}

.monitor-card {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  width: 100px;
}

:deep(.el-rate__text) {
  font-size: 12px;
}
</style>