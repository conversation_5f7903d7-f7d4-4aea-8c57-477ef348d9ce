<template>
  <div class="appraisal-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Trophy /></el-icon>
        考核管理
      </h1>
      <p class="page-description">管理绩效考核方案、考核记录、评分管理和结果分析</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon active">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.activeSchemes }}</div>
              <div class="stats-label">进行中考核</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon participants">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalParticipants }}</div>
              <div class="stats-label">参与人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.completedRecords }}</div>
              <div class="stats-label">已完成考核</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon average">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.averageScore }}</div>
              <div class="stats-label">平均分</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航 -->
    <el-row :gutter="20" class="function-nav">
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/appraisal/schemes')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="function-info">
              <h3>考核方案管理</h3>
              <p>制定和管理考核方案</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/appraisal/templates')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Files /></el-icon>
            </div>
            <div class="function-info">
              <h3>考核模板管理</h3>
              <p>管理考核表模板</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/appraisal/records')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><EditPen /></el-icon>
            </div>
            <div class="function-info">
              <h3>考核记录管理</h3>
              <p>管理考核记录和评分</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/appraisal/statistics')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="function-info">
              <h3>统计分析</h3>
              <p>查看考核统计和分析</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions">
      <template #header>
        <div class="card-header">
          <span>快速操作</span>
        </div>
      </template>
      <el-row :gutter="16">
        <el-col :span="6">
          <el-button type="primary" size="large" @click="createScheme" style="width: 100%">
            <el-icon><Plus /></el-icon>
            创建考核方案
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" size="large" @click="createTemplate" style="width: 100%">
            <el-icon><Files /></el-icon>
            创建考核模板
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" size="large" @click="batchEvaluation" style="width: 100%">
            <el-icon><EditPen /></el-icon>
            批量评分
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" size="large" @click="exportReport" style="width: 100%">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 当前考核 -->
    <el-card class="current-appraisals">
      <template #header>
        <div class="card-header">
          <span>当前考核</span>
          <el-button type="text" @click="viewAllSchemes">查看全部</el-button>
        </div>
      </template>
      <el-table :data="currentAppraisals" style="width: 100%">
        <el-table-column prop="schemeName" label="考核方案" min-width="200"  />
        <el-table-column prop="appraisalPeriod" label="考核周期" width="120"  />
        <el-table-column prop="startDate" label="开始时间" width="120"  />
        <el-table-column prop="endDate" label="结束时间" width="120"  />
        <el-table-column prop="participantCount" label="参与人数" width="100" align="center"  />
        <el-table-column prop="completedCount" label="已完成" width="100" align="center"  />
        <el-table-column prop="completionRate" label="完成率" width="100" align="center">
          <template #default="{ row }">
            <el-progress
              :percentage="row.completionRate"
              :color="getProgressColor(row.completionRate)"
              :stroke-width="6"
              text-inside
             />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewScheme(row)">
              查看
            </el-button>
            <el-button
              v-if="canManage(row)"
              type="text"
              size="small"
              @click="manageRecords(row)"
            >
              管理
            </el-button>
            <el-button
              v-if="canEvaluate(row)"
              type="text"
              size="small"
              @click="startEvaluation(row)"
            >
              评分
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 我的考核 -->
    <el-card class="my-appraisals">
      <template #header>
        <div class="card-header">
          <span>我的考核</span>
          <el-button type="text" @click="viewMyRecords">查看全部</el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="my-appraisal-item">
            <div class="item-header">
              <h4>待我评分</h4>
              <span class="count">{{ myAppraisals.pendingEvaluation }}</span>
            </div>
            <p>需要我进行评分的考核记录</p>
            <el-button type="primary" size="small" @click="viewPendingEvaluations">
              立即处理
            </el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="my-appraisal-item">
            <div class="item-header">
              <h4>我的自评</h4>
              <span class="count">{{ myAppraisals.selfEvaluation }}</span>
            </div>
            <p>需要我完成自评的考核</p>
            <el-button type="success" size="small" @click="viewSelfEvaluations">
              完成自评
            </el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="my-appraisal-item">
            <div class="item-header">
              <h4>考核结果</h4>
              <span class="count">{{ myAppraisals.completed }}</span>
            </div>
            <p>我的考核结果和历史记录</p>
            <el-button type="info" size="small" @click="viewMyResults">
              查看结果
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 考核等级分布 -->
    <el-card class="grade-distribution">
      <template #header>
        <div class="card-header">
          <span>考核等级分布</span>
          <el-select v-model="selectedYear" size="small" style="width: 120px" @change="loadGradeDistribution">
            <el-option
              v-for="year in availableYears"
              :key="year"
              :label="year + '年'"
              :value="year"
             />
          </el-select>
        </div>
      </template>
      <div class="grade-chart" ref="gradeChartRef"></div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  Trophy,
  Document,
  UserFilled,
  CircleCheck,
  DataAnalysis,
  Files,
  EditPen,
  Plus,
  Download
} from '@element-plus/icons-vue'
import { AppraisalStatus, appraisalStatisticsApi } from '@/api/appraisal'

const router = useRouter()

// 统计数据
const statistics = ref({
  activeSchemes: 0,
  totalParticipants: 0,
  completedRecords: 0,
  averageScore: 0
})

// 当前考核
const currentAppraisals = ref([
  {
    id: '1',
    schemeName: '2024年度绩效考核',
    appraisalPeriod: '年度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    participantCount: 150,
    completedCount: 120,
    completionRate: 80,
    status: AppraisalStatus.IN_PROGRESS
  },
  {
    id: '2',
    schemeName: '2024年第二季度考核',
    appraisalPeriod: '季度',
    startDate: '2024-04-01',
    endDate: '2024-06-30',
    participantCount: 80,
    completedCount: 75,
    completionRate: 94,
    status: AppraisalStatus.IN_PROGRESS
  }
])

// 我的考核
const myAppraisals = ref({
  pendingEvaluation: 5,
  selfEvaluation: 2,
  completed: 8
})

// 年份选择
const selectedYear = ref(new Date().getFullYear())
const availableYears = ref([2024, 2023, 2022, 2021])

// 图表引用
const gradeChartRef = ref<HTMLDivElement>()
let gradeChart: echarts.ECharts | null = null

// 页面导航
const navigateTo = (path: string) => {
  router.push(path)
}

// 创建考核方案
const createScheme = () => {
  router.push('/appraisal/schemes/create')
}

// 创建考核模板
const createTemplate = () => {
  router.push('/appraisal/templates/create')
}

// 批量评分
const batchEvaluation = () => {
  router.push('/appraisal/batch-evaluation')
}

// 导出报告
const exportReport = () => {
  router.push('/appraisal/export')
}

// 查看所有方案
const viewAllSchemes = () => {
  router.push('/appraisal/schemes')
}

// 查看方案详情
   
const viewScheme = (scheme: unknown) => {
  router.push(`/appraisal/schemes/${scheme.id}`)
}

// 管理考核记录
   
const manageRecords = (scheme: unknown) => {
  router.push(`/appraisal/records?schemeId=${scheme.id}`)
}

// 开始评分
   
const startEvaluation = (scheme: unknown) => {
  router.push(`/appraisal/evaluation?schemeId=${scheme.id}`)
}

// 查看我的记录
const viewMyRecords = () => {
  router.push('/appraisal/my-records')
}

// 查看待评分
const viewPendingEvaluations = () => {
  router.push('/appraisal/pending-evaluations')
}

// 查看自评
const viewSelfEvaluations = () => {
  router.push('/appraisal/self-evaluations')
}

// 查看我的结果
const viewMyResults = () => {
  router.push('/appraisal/my-results')
}

// 判断是否可以管理
   
const canManage = (scheme: unknown) => {
  return scheme.status === AppraisalStatus.IN_PROGRESS
}

// 判断是否可以评分
   
const canEvaluate = (scheme: unknown) => {
  return scheme.status === AppraisalStatus.IN_PROGRESS
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    DRAFT: 'info',
    PUBLISHED: 'warning',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    DRAFT: '草稿',
    PUBLISHED: '已发布',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return textMap[status] || status
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await appraisalStatisticsApi.getDashboardStats()
    if (response.data.success) {
      statistics.value = response.data.data
    } else {
      throw new Error(response.data.message)
    }
  } catch (__error) {
    console.error('加载统计数据失败:', error)
    // 使用备用数据
    statistics.value = {
      activeSchemes: 3,
      totalParticipants: 230,
      completedRecords: 195,
      averageScore: 85.6
    }
    ElMessage.warning('统计数据加载失败，显示默认数据')
  }
}

// 加载等级分布
const loadGradeDistribution = async () => {
  try {
    const response = await appraisalStatisticsApi.getGradeDistribution(selectedYear.value)
    if (response.data.success) {
      // 更新图表数据
      updateGradeChart(response.data.data)
    } else {
      throw new Error(response.data.message)
    }
  } catch (__error) {
    console.error('加载等级分布失败:', error)
    // 使用模拟数据
    const mockData = {
      year: selectedYear.value,
      data: [
        { grade: '优秀', count: 35, percentage: 15.2 },
        { grade: '良好', count: 112, percentage: 48.5 },
        { grade: '合格', count: 74, percentage: 32.1 },
        { grade: '不合格', count: 9, percentage: 4.2 }
      ],
      total: 230
    }
    updateGradeChart(mockData)
    ElMessage.warning('等级分布数据加载失败，显示默认数据')
  }
}

// 初始化图表
const initGradeChart = () => {
  if (gradeChartRef.value) {
    gradeChart = echarts.init(gradeChartRef.value)
    window.addEventListener('resize', () => gradeChart?.resize())
  }
}

// 更新等级分布图表
const updateGradeChart = (data: {
  year: number
  data: Array<{
    grade: string
    count: number
    percentage: number
  }>
  total: number
}) => {
  if (!gradeChart) return

  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      bottom: '5%',
      left: 'center'
    },
    series: [
      {
        name: 'HrHr考核等级',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data.data.map(item => ({
          value: item.count,
          name: item.grade,
          itemStyle: {
            color: getGradeColor(item.grade)
          }
        }))
      }
    ]
  }

  gradeChart.setOption(option)
}

// 获取等级颜色
const getGradeColor = (grade: string) => {
  const colorMap: Record<string, string> = {
    '优秀': '#67c23a',
    '良好': '#409eff',
    '合格': '#e6a23c',
    '不合格': '#f56c6c'
  }
  return colorMap[grade] || '#909399'
}

onMounted(() => {
  initGradeChart()
  loadStatistics()
  loadGradeDistribution()
})

onUnmounted(() => {
  // 销毁图表实例
  gradeChart?.dispose()
})
</script>

<style scoped>
.appraisal-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.active {
  background: linear-gradient(135deg, #409eff, #67c23a);
}

.stats-icon.participants {
  background: linear-gradient(135deg, #e6a23c, #f56c6c);
}

.stats-icon.completed {
  background: linear-gradient(135deg, #67c23a, #409eff);
}

.stats-icon.average {
  background: linear-gradient(135deg, #909399, #606266);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.function-nav {
  margin-bottom: 24px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 120px;
}

.function-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.function-content {
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100%;
}

.function-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.function-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.function-info p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.quick-actions,
.current-appraisals,
.my-appraisals,
.grade-distribution {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.my-appraisal-item {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.my-appraisal-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.count {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.my-appraisal-item p {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
}

.grade-chart {
  height: 300px;
}

.chart-placeholder {
  height: 100%;
  background: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}
</style>
