<template>
  <div class="appraisal-appeal-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>考核申诉管理</h2>
      <p>处理员工对考核结果的申诉请求，确保考核的公平性和透明度</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filterForm" inline>
        <el-form-item label="考核期间">
          <el-select v-model="filterForm.periodId" placeholder="请选择考核期间">
            <el-option
              v-for="period in periodOptions"
              :key="period.id"
              :label="period.name"
              :value="period.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="申诉状态">
          <el-select v-model="filterForm.status" clearable>
            <el-option label="全部状态" value=""  />
            <el-option label="待受理" value="PENDING"  />
            <el-option label="处理中" value="PROCESSING"  />
            <el-option label="已处理" value="PROCESSED"  />
            <el-option label="已驳回" value="REJECTED"  />
            <el-option label="已撤回" value="WITHDRAWN"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申诉类型">
          <el-select v-model="filterForm.type" clearable>
            <el-option label="全部类型" value=""  />
            <el-option label="评分异议" value="SCORE_DISPUTE"  />
            <el-option label="流程问题" value="PROCESS_ISSUE"  />
            <el-option label="标准质疑" value="STANDARD_QUERY"  />
            <el-option label="其他" value="OTHER"  />
          </el-select>
        </el-form-item>
        <el-form-item label="紧急程度">
          <el-select v-model="filterForm.priority" clearable>
            <el-option label="全部" value=""  />
            <el-option label="紧急" value="HIGH"  />
            <el-option label="普通" value="NORMAL"  />
            <el-option label="低" value="LOW"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申诉时间">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待受理</div>
              <div class="stats-trend">
                <span>较上月</span>
                <span class="trend-value up">+12%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon processing">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.processing }}</div>
              <div class="stats-label">处理中</div>
              <div class="stats-avg">平均处理时长: 3.5天</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon resolved">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.resolved }}</div>
              <div class="stats-label">已解决</div>
              <div class="stats-rate">解决率: {{ stats.resolveRate }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon satisfaction">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.satisfaction }}</div>
              <div class="stats-label">满意度</div>
              <div class="stats-score">评分: 4.2/5</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 申诉列表 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">申诉记录列表</span>
          <div class="card-actions">
            <el-button type="primary" @click="handleBatchAssign" :disabled="selectedRows.length === 0">
              <el-icon><User /></el-icon>
              批量分配
            </el-button>
            <el-button @click="handleExport">
              <el-icon><Download /></el-icon>
              导出记录
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        :row-class-name="getRowClassName"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="appealNo" label="申诉编号" width="140">
          <template #default="scope">
            <el-link type="primary" @click.stop="handleView(scope.row)">
              {{ scope.row.appealNo }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申诉人" width="100"  />
        <el-table-column prop="department" label="部门" width="150"  />
        <el-table-column prop="type" label="申诉类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)" size="small">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="申诉原因" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="submitTime" label="提交时间" width="160"  />
        <el-table-column prop="priority" label="紧急程度" width="90">
          <template #default="scope">
            <el-tag :type="getPriorityType(scope.row.priority)" size="small">
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="90">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="handler" label="处理人" width="100"  />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button
              v-if="canAccept(scope.row)"
              size="small"
              type="primary"
              @click.stop="handleAccept(scope.row)"
            >
              受理
            </el-button>
            <el-button
              v-if="canProcess(scope.row)"
              size="small"
              type="success"
              @click.stop="handleProcess(scope.row)"
            >
              处理
            </el-button>
            <el-button
              size="small"
              @click.stop="handleView(scope.row)"
            >
              详情
            </el-button>
            <el-dropdown v-if="scope.row.status === 'PENDING' || scope.row.status === 'PROCESSING'">
              <el-button size="small" link>
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleTransfer(scope.row)">
                    <el-icon><Switch /></el-icon>
                    转办
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleUrge(scope.row)">
                    <el-icon><Bell /></el-icon>
                    催办
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 申诉详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="`申诉详情 - ${currentAppeal?.appealNo}`"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="detail-dialog-content">
        <!-- 申诉信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <span class="detail-card-title">申诉信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="申诉人">{{ currentAppeal?.applicant }}</el-descriptions-item>
            <el-descriptions-item label="工号">{{ currentAppeal?.employeeNo }}</el-descriptions-item>
            <el-descriptions-item label="部门">{{ currentAppeal?.department }}</el-descriptions-item>
            <el-descriptions-item label="岗位">{{ currentAppeal?.position }}</el-descriptions-item>
            <el-descriptions-item label="考核期间">{{ currentAppeal?.period }}</el-descriptions-item>
            <el-descriptions-item label="原始得分">
              <el-tag type="primary">{{ currentAppeal?.originalScore }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申诉类型">
              <el-tag :type="getTypeTagType(currentAppeal?.type)" size="small">
                {{ getTypeText(currentAppeal?.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="紧急程度">
              <el-tag :type="getPriorityType(currentAppeal?.priority)" size="small">
                {{ getPriorityText(currentAppeal?.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申诉原因" :span="2">
              {{ currentAppeal?.reason }}
            </el-descriptions-item>
            <el-descriptions-item label="详细说明" :span="2">
              <div class="appeal-detail">{{ currentAppeal?.detail }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 附件信息 -->
        <el-card v-if="currentAppeal?.attachments?.length > 0" class="detail-card" shadow="never">
          <template #header>
            <span class="detail-card-title">相关附件</span>
          </template>
          <div class="attachment-list">
            <div
              v-for="(file, index) in currentAppeal.attachments"
              :key="index"
              class="attachment-item"
            >
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ file.name }}</span>
              <el-button type="primary" link size="small" @click="handleDownload(file)">
                下载
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 处理进度 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <span class="detail-card-title">处理进度</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in currentAppeal?.processRecords"
              :key="index"
              :timestamp="record.time"
              :type="record.type"
              :hollow="record.status === 'CURRENT'"
            >
              <div class="timeline-content">
                <div class="timeline-title">{{ record.action }}</div>
                <div class="timeline-operator">操作人：{{ record.operator }}</div>
                <div v-if="record.comment" class="timeline-comment">
                  {{ record.comment }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 处理申诉对话框 -->
    <el-dialog
      v-model="processDialogVisible"
      title="处理申诉"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="processFormRef"
        :model="processForm"
        :rules="processRules"
        label-width="120px"
      >
        <el-form-item label="处理结果" prop="result">
          <el-radio-group v-model="processForm.result">
            <el-radio value="ACCEPTED">接受申诉</el-radio>
            <el-radio value="REJECTED">驳回申诉</el-radio>
            <el-radio value="PARTIAL">部分接受</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="processForm.result === 'ACCEPTED' || processForm.result === 'PARTIAL'"
          label="调整后得分"
          prop="adjustedScore"
        >
          <el-input-number
            v-model="processForm.adjustedScore"
            :min="0"
            :max="100"
            :step="0.5"
            :precision="1"
            />
          <el-tag type="info" class="score-tip">
            原始得分：{{ currentAppeal?.originalScore }}
          </el-tag>
        </el-form-item>

        <el-form-item label="处理意见" prop="opinion">
          <el-input
            v-model="processForm.opinion"
            type="textarea"
            :rows="4"
            placeholder="请输入处理意见..."
            maxlength="500"
            show-word-limit
            />
        </el-form-item>

        <el-form-item label="改进措施" prop="improvement">
          <el-input
            v-model="processForm.improvement"
            type="textarea"
            :rows="3"
            placeholder="请输入后续改进措施..."
            maxlength="300"
            show-word-limit
            />
        </el-form-item>

        <el-form-item label="通知方式">
          <el-checkbox-group v-model="processForm.notifyMethods">
            <el-checkbox label="SYSTEM">系统通知</el-checkbox>
            <el-checkbox label="EMAIL">邮件通知</el-checkbox>
            <el-checkbox label="SMS">短信通知</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="相关附件">
          <el-upload
            ref="uploadRef"
            action="#"
            :auto-upload="false"
            :limit="3"
            :on-change="handleFileChange"
            accept=".pdf,.doc,.docx"
          >
            <el-button type="primary" plain>
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、Word 格式，单个文件不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitProcess">提交处理</el-button>
      </template>
    </el-dialog>

    <!-- 批量分配对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="批量分配处理人"
      width="500px"
    >
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="处理人" prop="handlerId">
          <el-select
            v-model="assignForm.handlerId"
            filterable
            placeholder="请选择处理人"
          >
            <el-option
              v-for="user in handlerOptions"
              :key="user.id"
              :label="`${user.name} (当前任务: ${user.taskCount})`"
              :value="user.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="分配说明">
          <el-input
            v-model="assignForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入分配说明..."
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmAssign">确定分配</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Clock,
  Loading,
  CircleCheck,
  Star,
  User,
  Download,
  ArrowDown,
  Switch as SwitchIcon,
  Bell,
  Document,
  Upload
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const periodOptions = ref<any[]>([])
const handlerOptions = ref<any[]>([])

// 筛选表单
const filterForm = reactive({
  periodId: '',
  status: '',
  type: '',
  priority: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计数据
const stats = reactive({
  pending: 12,
  processing: 8,
  resolved: 45,
  resolveRate: 85.2,
  satisfaction: 92
})

// 对话框控制
const detailDialogVisible = ref(false)
const processDialogVisible = ref(false)
const assignDialogVisible = ref(false)
const currentAppeal = ref<unknown>(null)

// 处理表单
const processFormRef = ref()
const processForm = reactive({
  result: 'ACCEPTED',
  adjustedScore: 0,
  opinion: '',
  improvement: '',
  notifyMethods: ['SYSTEM', 'EMAIL'],
  attachments: [] as unknown[]
})

// 分配表单
const assignForm = reactive({
  handlerId: '',
  remark: ''
})

// 表单验证规则
const processRules = {
  result: [
    { required: true, message: '请选择处理结果', trigger: 'change' }
  ],
  adjustedScore: [
    { required: true, message: '请输入调整后的分数', trigger: 'blur' }
  ],
  opinion: [
    { required: true, message: '请输入处理意见', trigger: 'blur' },
    { min: 10, message: '处理意见至少10个字符', trigger: 'blur' }
  ]
}

// 初始化数据
const initData = async () => {
  // 加载期间选项
  periodOptions.value = [
    { id: '1', name: 'HrHr2025年度考核' },
    { id: '2', name: '2025年第一季度考核' },
    { id: '3', name: '2024年度考核' }
  ]
  
  // 加载处理人选项
  handlerOptions.value = [
    { id: '1', name: '张主任', taskCount: 5 },
    { id: '2', name: '李处长', taskCount: 3 },
    { id: '3', name: '王经理', taskCount: 7 }
  ]
  
  // 默认选中第一个期间
  if (periodOptions.value.length > 0) {
    filterForm.periodId = periodOptions.value[0].id
  }
  
  fetchList()
}

// 获取列表数据
const fetchList = async () => {
  try {
    loading.value = true
    
    // 模拟数据
    tableData.value = [
      {
        id: '1',
        appealNo: 'APL202506001',
        applicant: '张三',
        employeeNo: 'EMP001',
        department: '计算机学院',
        position: '讲师',
        period: '2025年度考核',
        originalScore: 82,
        type: 'SCORE_DISPUTE',
        reason: '对工作业绩评分存在异议',
        detail: '本年度完成科研项目3项，发表论文5篇，但评分未能充分体现工作成果...',
        submitTime: '2025-06-20 14:30:00',
        priority: 'HIGH',
        status: 'PENDING',
        handler: null,
        attachments: [
          { name: '科研成果证明.pdf', url: '#' },
          { name: '论文发表清单.docx', url: '#' }
        ],
        processRecords: [
          {
            time: '2025-06-20 14:30:00',
            action: '提交申诉',
            operator: '张三',
            comment: '申请复核考核结果',
            type: 'primary'
          }
        ]
      },
      {
        id: '2',
        appealNo: 'APL202506002',
        applicant: '李四',
        employeeNo: 'EMP002',
        department: '机械工程学院',
        position: '副教授',
        period: '2025年度考核',
        originalScore: 88,
        type: 'PROCESS_ISSUE',
        reason: '考核流程未按规定执行',
        detail: '部门评分环节未通知本人参与，违反考核流程规定...',
        submitTime: '2025-06-19 10:15:00',
        priority: 'NORMAL',
        status: 'PROCESSING',
        handler: '王经理',
        processRecords: [
          {
            time: '2025-06-19 10:15:00',
            action: '提交申诉',
            operator: '李四',
            comment: '流程存在问题',
            type: 'primary'
          },
          {
            time: '2025-06-19 15:30:00',
            action: '受理申诉',
            operator: '王经理',
            comment: '已受理，正在调查核实',
            type: 'success',
            status: 'CURRENT'
          }
        ]
      },
      {
        id: '3',
        appealNo: 'APL202506003',
        applicant: '王五',
        employeeNo: 'EMP003',
        department: '电气工程学院',
        position: '教授',
        period: '2025年度考核',
        originalScore: 90,
        type: 'STANDARD_QUERY',
        reason: '考核标准设置不合理',
        detail: '新的考核标准中科研权重过低，不符合教授岗位特点...',
        submitTime: '2025-06-18 16:20:00',
        priority: 'LOW',
        status: 'PROCESSED',
        handler: '李处长',
        processRecords: [
          {
            time: '2025-06-18 16:20:00',
            action: '提交申诉',
            operator: '王五',
            comment: '标准不合理',
            type: 'primary'
          },
          {
            time: '2025-06-19 09:00:00',
            action: '受理申诉',
            operator: '李处长',
            comment: '已受理',
            type: 'success'
          },
          {
            time: '2025-06-20 11:30:00',
            action: '处理完成',
            operator: '李处长',
            comment: '经核实，考核标准符合规定，维持原评分',
            type: 'success'
          }
        ]
      }
    ]
    
    pagination.total = tableData.value.length
  } catch (__error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    periodId: periodOptions.value[0]?.id || '',
    status: '',
    type: '',
    priority: '',
    dateRange: []
  })
  fetchList()
}

// 批量分配
const handleBatchAssign = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要分配的申诉记录')
    return
  }
  
  // 检查是否都是待受理状态
  const canAssign = selectedRows.value.every(row => row.status === 'PENDING')
  if (!canAssign) {
    ElMessage.warning('只能分配待受理的申诉')
    return
  }
  
  assignDialogVisible.value = true
}

// 导出记录
const handleExport = () => {
  ElMessage.success('正在导出申诉记录...')
}

// 表格选择
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 行点击
   
const handleRowClick = (row: unknown) => {
  handleView(row)
}

// 获取行样式
   
const getRowClassName = ({ row }: unknown) => {
  if (row.priority === 'HIGH' && row.status === 'PENDING') {
    return 'high-priority-row'
  }
  return ''
}

// 判断是否可以受理
   
const canAccept = (row: unknown) => {
  return row.status === 'PENDING'
}

// 判断是否可以处理
   
const canProcess = (row: unknown) => {
  return row.status === 'PROCESSING' && row.handler === '当前用户'
}

// 受理申诉
   
const handleAccept = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要受理该申诉吗？',
      '受理确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用受理API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('申诉已受理')
    fetchList()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('受理失败:', error)
      ElMessage.error('受理失败')
    }
  }
}

// 处理申诉
   
const handleProcess = (row: unknown) => {
  currentAppeal.value = row
  processForm.adjustedScore = row.originalScore
  processDialogVisible.value = true
}

// 查看详情
   
const handleView = (row: unknown) => {
  currentAppeal.value = row
  detailDialogVisible.value = true
}

// 转办
   
const handleTransfer = (row: unknown) => {
  ElMessage.info('转办功能开发中...')
}

// 催办
   
const handleUrge = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要发送催办通知吗？',
      '催办确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('催办通知已发送')
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('催办失败:', error)
    }
  }
}

// 下载附件
   
const handleDownload = (file: unknown) => {
  ElMessage.success(`正在下载：${file.name}`)
}

// 文件变化
   
const handleFileChange = (file: unknown) => {
  processForm.attachments.push(file)
}

// 提交处理
const handleSubmitProcess = async () => {
  try {
    await processFormRef.value.validate()
    
    await ElMessageBox.confirm(
      '确定要提交处理结果吗？',
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用处理API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('处理结果已提交')
    processDialogVisible.value = false
    fetchList()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  }
}

// 确认分配
const handleConfirmAssign = async () => {
  try {
    if (!assignForm.handlerId) {
      ElMessage.warning('请选择处理人')
      return
    }
    
    // 调用分配API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success(`成功分配 ${selectedRows.value.length} 条申诉记录`)
    assignDialogVisible.value = false
    selectedRows.value = []
    fetchList()
  } catch (__error) {
    console.error('分配失败:', error)
    ElMessage.error('分配失败')
  }
}

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  switch (type) {
    case 'SCORE_DISPUTE': return 'primary'
    case 'PROCESS_ISSUE': return 'warning'
    case 'STANDARD_QUERY': return 'info'
    case 'OTHER': return ''
    default: return ''
  }
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'SCORE_DISPUTE': '评分异议',
    'PROCESS_ISSUE': '流程问题',
    'STANDARD_QUERY': '标准质疑',
    'OTHER': '其他'
  }
  return typeMap[type] || type
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'HIGH': return 'danger'
    case 'NORMAL': return 'warning'
    case 'LOW': return 'info'
    default: return ''
  }
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'HIGH': '紧急',
    'NORMAL': '普通',
    'LOW': '低'
  }
  return priorityMap[priority] || priority
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING': return 'warning'
    case 'PROCESSING': return 'primary'
    case 'PROCESSED': return 'success'
    case 'REJECTED': return 'danger'
    case 'WITHDRAWN': return 'info'
    default: return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': '待受理',
    'PROCESSING': '处理中',
    'PROCESSED': '已处理',
    'REJECTED': '已驳回',
    'WITHDRAWN': '已撤回'
  }
  return statusMap[status] || status
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<style scoped>
.appraisal-appeal-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.pending {
  background: linear-gradient(135deg, #ffd93d 0%, #ffb347 100%);
}

.stats-icon.processing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.resolved {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.satisfaction {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.stats-trend,
.stats-avg,
.stats-rate,
.stats-score {
  font-size: 12px;
  color: #606266;
}

.trend-value {
  font-weight: 600;
  margin-left: 4px;
}

.trend-value.up {
  color: #67c23a;
}

.trend-value.down {
  color: #f56c6c;
}

.table-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.detail-dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
}

.detail-card-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.appeal-detail {
  line-height: 1.6;
  color: #606266;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.file-name {
  flex: 1;
  color: #606266;
}

.timeline-content {
  padding: 8px 0;
}

.timeline-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.timeline-operator {
  font-size: 13px;
  color: #909399;
  margin-bottom: 4px;
}

.timeline-comment {
  font-size: 13px;
  color: #606266;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-top: 4px;
}

.score-tip {
  margin-left: 10px;
}

:deep(.el-descriptions__label) {
  width: 100px;
}

:deep(.high-priority-row) {
  background-color: #fef0f0;
}
</style>