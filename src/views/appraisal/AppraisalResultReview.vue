<template>
  <div class="appraisal-result-review">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>考核结果审核</h2>
      <p>对考核评分结果进行复核确认，确保考核的公平公正</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filterForm" inline>
        <el-form-item label="考核期间">
          <el-select v-model="filterForm.periodId" placeholder="请选择考核期间">
            <el-option
              v-for="period in periodOptions"
              :key="period.id"
              :label="period.name"
              :value="period.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="filterForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            clearable
            placeholder="请选择部门"
           />
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="filterForm.reviewStatus" clearable>
            <el-option label="全部状态" value=""  />
            <el-option label="待审核" value="PENDING"  />
            <el-option label="审核中" value="REVIEWING"  />
            <el-option label="已通过" value="APPROVED"  />
            <el-option label="已驳回" value="REJECTED"  />
            <el-option label="已发布" value="PUBLISHED"  />
          </el-select>
        </el-form-item>
        <el-form-item label="成绩等级">
          <el-select v-model="filterForm.grade" clearable>
            <el-option label="全部等级" value=""  />
            <el-option label="优秀" value="A"  />
            <el-option label="良好" value="B"  />
            <el-option label="合格" value="C"  />
            <el-option label="不合格" value="D"  />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="filterForm.keyword"
            placeholder="姓名/工号"
            clearable
            @keyup.enter="handleSearch"
            />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="4">
        <el-card class="stats-card">
          <el-statistic title="待审核" :value="stats.pending">
            <template #prefix>
              <el-icon><Clock /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <el-statistic title="审核中" :value="stats.reviewing">
            <template #prefix>
              <el-icon><Loading /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <el-statistic title="已通过" :value="stats.approved">
            <template #prefix>
              <el-icon><CircleCheck /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <el-statistic title="已驳回" :value="stats.rejected">
            <template #prefix>
              <el-icon><CircleClose /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <el-statistic title="优秀率" :value="stats.excellentRate" suffix="%">
            <template #prefix>
              <el-icon><Trophy /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stats-card">
          <el-statistic title="平均分" :value="stats.avgScore" :precision="2">
            <template #prefix>
              <el-icon><DataAnalysis /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
    </el-row>

    <!-- 结果列表 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">考核结果列表</span>
          <div class="card-actions">
            <el-button type="primary" @click="handleBatchReview" :disabled="selectedRows.length === 0">
              <el-icon><Stamp /></el-icon>
              批量审核
            </el-button>
            <el-button type="success" @click="handleBatchPublish" :disabled="!canBatchPublish">
              <el-icon><Promotion /></el-icon>
              批量发布
            </el-button>
            <el-button @click="handleExport">
              <el-icon><Download /></el-icon>
              导出结果
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="department" label="部门" width="150"  />
        <el-table-column prop="position" label="岗位" width="120"  />
        <el-table-column label="各项得分" width="240">
          <template #default="scope">
            <div class="score-breakdown">
              <el-tooltip
                v-for="item in scope.row.scoreBreakdown"
                :key="item.name"
                :content="`${item.name}: ${item.score}分`"
                placement="top"
              >
                <el-tag size="small" class="score-tag">
                  {{ item.abbr }}: {{ item.score }}
                </el-tag>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="totalScore" label="总分" width="80" align="center">
          <template #default="scope">
            <el-tag type="primary" effect="dark">
              {{ scope.row.totalScore }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="grade" label="等级" width="80" align="center">
          <template #default="scope">
            <el-tag :type="getGradeTagType(scope.row.grade)">
              {{ getGradeText(scope.row.grade) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="rank" label="排名" width="80" align="center">
          <template #default="scope">
            <span class="rank-number">{{ scope.row.rank }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reviewStatus" label="审核状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.reviewStatus)" size="small">
              {{ getStatusText(scope.row.reviewStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleReview(scope.row)"
            >
              审核
            </el-button>
            <el-button
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.reviewStatus === 'APPROVED'"
              size="small"
              type="success"
              @click="handlePublish(scope.row)"
            >
              发布
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="reviewDialogVisible"
      :title="`考核结果审核 - ${currentResult?.employeeName}`"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="review-dialog-content">
        <!-- 基本信息 -->
        <el-descriptions :column="2" border class="result-info">
          <el-descriptions-item label="考核期间">{{ currentResult?.period }}</el-descriptions-item>
          <el-descriptions-item label="考核方案">{{ currentResult?.scheme }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ currentResult?.department }}</el-descriptions-item>
          <el-descriptions-item label="岗位">{{ currentResult?.position }}</el-descriptions-item>
        </el-descriptions>

        <!-- 得分详情 -->
        <div class="score-detail">
          <h4>得分详情</h4>
          <el-table :data="currentResult?.scoreDetails" show-summary :summary-method="getScoreSummaries">
            <el-table-column prop="category" label="考核维度" width="120"  />
            <el-table-column prop="weight" label="权重(%)" width="80" align="center"  />
            <el-table-column prop="selfScore" label="自评分" width="80" align="center"  />
            <el-table-column prop="superiorScore" label="上级评分" width="90" align="center"  />
            <el-table-column prop="deptScore" label="部门评分" width="90" align="center"  />
            <el-table-column prop="hrScore" label="HR评分" width="80" align="center"  />
            <el-table-column prop="finalScore" label="最终得分" width="90" align="center">
              <template #default="scope">
                <el-tag type="warning">{{ scope.row.finalScore }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="weightedScore" label="加权得分" width="90" align="center">
              <template #default="scope">
                <strong>{{ scope.row.weightedScore }}</strong>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 审核意见 -->
        <el-form
          ref="reviewFormRef"
          :model="reviewForm"
          :rules="reviewRules"
          label-width="100px"
          class="review-form"
        >
          <el-form-item label="审核结果" prop="result">
            <el-radio-group v-model="reviewForm.result">
              <el-radio value="APPROVE">通过</el-radio>
              <el-radio value="REJECT">驳回</el-radio>
              <el-radio value="ADJUST">调整分数</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="reviewForm.result === 'ADJUST'" label="调整后总分" prop="adjustedScore">
            <el-input-number
              v-model="reviewForm.adjustedScore"
              :min="0"
              :max="100"
              :step="0.5"
              :precision="1"
              />
            <el-tag type="info" class="adjust-tip">
              原始总分：{{ currentResult?.totalScore }}
            </el-tag>
          </el-form-item>

          <el-form-item label="审核意见" prop="comments">
            <el-input
              v-model="reviewForm.comments"
              type="textarea"
              :rows="4"
              placeholder="请输入审核意见..."
              maxlength="500"
              show-word-limit
              />
          </el-form-item>

          <el-form-item label="特别说明">
            <el-checkbox-group v-model="reviewForm.flags">
              <el-checkbox label="EXCELLENT">推荐为优秀</el-checkbox>
              <el-checkbox label="IMPROVEMENT">需要改进</el-checkbox>
              <el-checkbox label="SPECIAL">特殊情况</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>

        <!-- 历史审核记录 -->
        <div v-if="reviewHistory.length > 0" class="review-history">
          <h4>审核历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(history, index) in reviewHistory"
              :key="index"
              :timestamp="history.time"
              :type="history.type"
            >
              <div class="history-content">
                <div class="history-title">
                  {{ history.reviewer }} - {{ history.action }}
                </div>
                <div class="history-comment">{{ history.comment }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <template #footer>
        <el-button @click="reviewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitReview">提交审核</el-button>
      </template>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <el-dialog
      v-model="batchReviewDialogVisible"
      title="批量审核"
      width="600px"
    >
      <el-alert
        title="批量审核说明"
        type="info"
        show-icon
        :closable="false"
        class="batch-info"
      >
        您已选择 <strong>{{ selectedRows.length }}</strong> 条考核结果进行批量审核
      </el-alert>

      <el-form
        ref="batchReviewFormRef"
        :model="batchReviewForm"
        :rules="batchReviewRules"
        label-width="100px"
        class="batch-review-form"
      >
        <el-form-item label="审核结果" prop="result">
          <el-radio-group v-model="batchReviewForm.result">
            <el-radio value="APPROVE">全部通过</el-radio>
            <el-radio value="REJECT">全部驳回</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="审核意见" prop="comments">
          <el-input
            v-model="batchReviewForm.comments"
            type="textarea"
            :rows="3"
            placeholder="请输入统一的审核意见..."
            />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="batchReviewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitBatchReview">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Clock,
  Loading,
  CircleCheck,
  CircleClose,
  Trophy,
  DataAnalysis,
  Stamp,
  Promotion,
  Download
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const periodOptions = ref<any[]>([])
const departmentTree = ref<any[]>([])

// 筛选表单
const filterForm = reactive({
  periodId: '',
  departmentId: '',
  reviewStatus: '',
  grade: '',
  keyword: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计数据
const stats = reactive({
  pending: 45,
  reviewing: 23,
  approved: 156,
  rejected: 8,
  excellentRate: 25.5,
  avgScore: 86.3
})

// 对话框控制
const reviewDialogVisible = ref(false)
const batchReviewDialogVisible = ref(false)
const currentResult = ref<unknown>(null)
const reviewHistory = ref<any[]>([])

// 审核表单
const reviewFormRef = ref()
const reviewForm = reactive({
  result: 'APPROVE',
  adjustedScore: 0,
  comments: '',
  flags: [] as string[]
})

// 批量审核表单
const batchReviewFormRef = ref()
const batchReviewForm = reactive({
  result: 'APPROVE',
  comments: ''
})

// 表单验证规则
const reviewRules = {
  result: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  adjustedScore: [
    { required: true, message: '请输入调整后的分数', trigger: 'blur' }
  ],
  comments: [
    { required: true, message: '请输入审核意见', trigger: 'blur' }
  ]
}

const batchReviewRules = {
  result: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  comments: [
    { required: true, message: '请输入审核意见', trigger: 'blur' }
  ]
}

// 计算属性
const canBatchPublish = computed(() => {
  return selectedRows.value.length > 0 &&
    selectedRows.value.every(row => row.reviewStatus === 'APPROVED')
})

// 初始化数据
const initData = async () => {
  // 加载期间选项
  periodOptions.value = [
    { id: '1', name: 'HrHr2025年度考核' },
    { id: '2', name: '2025年第一季度考核' },
    { id: '3', name: '2024年度考核' }
  ]
  
  // 加载部门树
  departmentTree.value = [
    {
      id: '1',
      name: '杭科院',
      children: [
        { id: '11', name: '计算机学院' },
        { id: '12', name: '机械工程学院' },
        { id: '13', name: '电气工程学院' }
      ]
    }
  ]
  
  // 默认选中第一个期间
  if (periodOptions.value.length > 0) {
    filterForm.periodId = periodOptions.value[0].id
  }
  
  fetchList()
}

// 获取列表数据
const fetchList = async () => {
  try {
    loading.value = true
    
    // 模拟数据
    tableData.value = [
      {
        id: '1',
        employeeNo: 'EMP001',
        employeeName: '张三',
        department: '计算机学院',
        position: '讲师',
        period: '2025年度考核',
        scheme: '教学人员考核方案',
        scoreBreakdown: [
          { name: '工作业绩', abbr: '业绩', score: 85 },
          { name: '工作能力', abbr: '能力', score: 88 },
          { name: '工作态度', abbr: '态度', score: 92 }
        ],
        totalScore: 87.5,
        grade: 'B',
        rank: 15,
        reviewStatus: 'PENDING',
        scoreDetails: [
          {
            category: '工作业绩',
            weight: 60,
            selfScore: 88,
            superiorScore: 85,
            deptScore: 84,
            hrScore: 85,
            finalScore: 85,
            weightedScore: 51
          },
          {
            category: '工作能力',
            weight: 25,
            selfScore: 90,
            superiorScore: 88,
            deptScore: 87,
            hrScore: 88,
            finalScore: 88,
            weightedScore: 22
          },
          {
            category: '工作态度',
            weight: 15,
            selfScore: 95,
            superiorScore: 92,
            deptScore: 91,
            hrScore: 92,
            finalScore: 92,
            weightedScore: 13.8
          }
        ]
      },
      {
        id: '2',
        employeeNo: 'EMP002',
        employeeName: '李四',
        department: '机械工程学院',
        position: '副教授',
        period: '2025年度考核',
        scheme: '教学人员考核方案',
        scoreBreakdown: [
          { name: '工作业绩', abbr: '业绩', score: 92 },
          { name: '工作能力', abbr: '能力', score: 90 },
          { name: '工作态度', abbr: '态度', score: 95 }
        ],
        totalScore: 91.8,
        grade: 'A',
        rank: 5,
        reviewStatus: 'REVIEWING'
      },
      {
        id: '3',
        employeeNo: 'EMP003',
        employeeName: '王五',
        department: '电气工程学院',
        position: '教授',
        period: '2025年度考核',
        scheme: '管理人员考核方案',
        scoreBreakdown: [
          { name: '工作业绩', abbr: '业绩', score: 88 },
          { name: '工作能力', abbr: '能力', score: 86 },
          { name: '工作态度', abbr: '态度', score: 90 }
        ],
        totalScore: 87.6,
        grade: 'B',
        rank: 18,
        reviewStatus: 'APPROVED'
      }
    ]
    
    pagination.total = tableData.value.length
  } catch (__error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    periodId: periodOptions.value[0]?.id || '',
    departmentId: '',
    reviewStatus: '',
    grade: '',
    keyword: ''
  })
  fetchList()
}

// 批量审核
const handleBatchReview = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要审核的记录')
    return
  }
  
  // 检查是否都是待审核状态
  const canReview = selectedRows.value.every(row => 
    row.reviewStatus === 'PENDING' || row.reviewStatus === 'REVIEWING'
  )
  
  if (!canReview) {
    ElMessage.warning('只能审核待审核或审核中的记录')
    return
  }
  
  batchReviewDialogVisible.value = true
}

// 批量发布
const handleBatchPublish = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要发布选中的 ${selectedRows.value.length} 条考核结果吗？`,
      '批量发布确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用批量发布API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('批量发布成功')
    selectedRows.value = []
    fetchList()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('批量发布失败:', error)
      ElMessage.error('批量发布失败')
    }
  }
}

// 导出结果
const handleExport = () => {
  ElMessage.success('正在导出考核结果...')
}

// 表格选择
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 审核
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleReview = (row: unknown) => {
  currentResult.value = row
  reviewForm.adjustedScore = row.totalScore
  loadReviewHistory(row.id)
  reviewDialogVisible.value = true
}

// 查看详情
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleViewDetail = (row: unknown) => {
  currentResult.value = row
  loadReviewHistory(row.id)
  reviewDialogVisible.value = true
}

// 发布
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handlePublish = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要发布该考核结果吗？发布后员工可以查看。',
      '发布确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用发布API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('发布成功')
    fetchList()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('发布失败:', error)
      ElMessage.error('发布失败')
    }
  }
}

// 加载审核历史
const loadReviewHistory = async (resultId: string) => {
  // 模拟审核历史
  reviewHistory.value = [
    {
      time: '2025-06-20 14:30:00',
      reviewer: 'HR专员',
      action: '提交审核',
      comment: '考核评分已完成，提交审核',
      type: 'primary'
    },
    {
      time: '2025-06-21 10:15:00',
      reviewer: '部门经理',
      action: '初审通过',
      comment: '评分合理，建议通过',
      type: 'success'
    }
  ]
}

// 提交审核
const handleSubmitReview = async () => {
  try {
    await reviewFormRef.value.validate()
    
    // 调用审核API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('审核提交成功')
    reviewDialogVisible.value = false
    fetchList()
  } catch (__error) {
    console.error('审核提交失败:', error)
    ElMessage.error('审核提交失败')
  }
}

// 提交批量审核
const handleSubmitBatchReview = async () => {
  try {
    await batchReviewFormRef.value.validate()
    
    // 调用批量审核API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(`成功审核 ${selectedRows.value.length} 条记录`)
    batchReviewDialogVisible.value = false
    selectedRows.value = []
    fetchList()
  } catch (__error) {
    console.error('批量审核失败:', error)
    ElMessage.error('批量审核失败')
  }
}

// 获取分数汇总
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getScoreSummaries = (param: unknown) => {
  const {columns: _columns, data: _data} =  param
  const sums: string[] 
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-statistic__head) {
  color: #909399;
  font-size: 14px;
}

:deep(.el-statistic__content) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-statistic__number) {
  font-size: 24px;
  font-weight: 600;
}

.table-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.score-breakdown {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.score-tag {
  font-size: 12px;
  padding: 0 6px;
}

.rank-number {
  font-weight: 600;
  color: #606266;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.review-dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.result-info {
  margin-bottom: 20px;
}

.score-detail {
  margin-bottom: 20px;
}

.score-detail h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.review-form {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.adjust-tip {
  margin-left: 10px;
}

.review-history {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.review-history h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.history-content {
  padding: 8px 0;
}

.history-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.history-comment {
  color: #606266;
  font-size: 14px;
}

.batch-info {
  margin-bottom: 20px;
}

.batch-review-form {
  margin-top: 20px;
}

:deep(.el-descriptions__label) {
  width: 100px;
}
</style>