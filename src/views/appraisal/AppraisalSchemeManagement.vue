<template>
  <div class="appraisal-scheme-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Document /></el-icon>
        考核方案管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增方案
        </el-button>
        <el-button @click="handleImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出方案
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="方案名称">
          <el-input
            v-model="searchForm.schemeName"
            placeholder="请输入方案名称"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="考核类型">
          <el-select
            v-model="searchForm.appraisalType"
            placeholder="请选择考核类型"
            clearable
            style="width: 150px"
          >
            <el-option label="年度考核" value="annual"  />
            <el-option label="季度考核" value="quarterly"  />
            <el-option label="月度考核" value="monthly"  />
            <el-option label="专项考核" value="special"  />
          </el-select>
        </el-form-item>
        <el-form-item label="适用对象">
          <el-select
            v-model="searchForm.targetGroup"
            placeholder="请选择适用对象"
            clearable
            style="width: 150px"
          >
            <el-option label="教学人员" value="teaching"  />
            <el-option label="管理人员" value="management"  />
            <el-option label="技术人员" value="technical"  />
            <el-option label="服务人员" value="service"  />
          </el-select>
        </el-form-item>
        <el-form-item label="方案状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="草稿" value="draft"  />
            <el-option label="启用" value="active"  />
            <el-option label="停用" value="inactive"  />
            <el-option label="归档" value="archived"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总方案数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.active }}</div>
              <div class="stat-label">启用中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon participants">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.participants }}</div>
              <div class="stat-label">参与人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon indicators">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.indicators }}</div>
              <div class="stat-label">考核指标</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 考核方案列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="schemeCode" label="方案编号" width="120"  />
        <el-table-column prop="schemeName" label="方案名称" min-width="200"  />
        <el-table-column prop="appraisalType" label="考核类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.appraisalType)">
              {{ getTypeLabel(row.appraisalType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="targetGroup" label="适用对象" width="120">
          <template #default="{ row }">
            <el-tag :type="getGroupColor(row.targetGroup)">
              {{ getGroupLabel(row.targetGroup) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="cycle" label="考核周期" width="120"  />
        <el-table-column prop="indicatorCount" label="指标数量" width="100"  />
        <el-table-column prop="participantCount" label="参与人数" width="100"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120"  />
        <el-table-column prop="updateTime" label="更新时间" width="120"  />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleIndicators(row)">
              指标管理
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="activate" v-if="row.status === 'draft'">
                    启用方案
                  </el-dropdown-item>
                  <el-dropdown-item command="deactivate" v-if="row.status === 'active'">
                    停用方案
                  </el-dropdown-item>
                  <el-dropdown-item command="copy">复制方案</el-dropdown-item>
                  <el-dropdown-item command="archive" v-if="row.status === 'inactive'">
                    归档方案
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 考核方案详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="方案编号" prop="schemeCode">
              <el-input v-model="formData.schemeCode" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方案名称" prop="schemeName">
              <el-input v-model="formData.schemeName" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="考核类型" prop="appraisalType">
              <el-select v-model="formData.appraisalType" :disabled="isView" style="width: 100%">
                <el-option label="年度考核" value="annual"  />
                <el-option label="季度考核" value="quarterly"  />
                <el-option label="月度考核" value="monthly"  />
                <el-option label="专项考核" value="special"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适用对象" prop="targetGroup">
              <el-select v-model="formData.targetGroup" :disabled="isView" style="width: 100%">
                <el-option label="教学人员" value="teaching"  />
                <el-option label="管理人员" value="management"  />
                <el-option label="技术人员" value="technical"  />
                <el-option label="服务人员" value="service"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="考核周期" prop="cycle">
              <el-input v-model="formData.cycle" :disabled="isView" placeholder="如：2024年度"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总分" prop="totalScore">
              <el-input-number
                v-model="formData.totalScore"
                :min="1"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">分</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="方案描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入方案描述"
            />
        </el-form-item>
        <el-form-item label="考核目标" prop="objectives">
          <el-input
            v-model="formData.objectives"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入考核目标"
            />
        </el-form-item>
        <el-form-item label="评分标准" prop="scoringCriteria">
          <el-input
            v-model="formData.scoringCriteria"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入评分标准"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AppraisalSchemeManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  CircleCheck,
  User,
  DataAnalysis,
  ArrowDown
} from '@element-plus/icons-vue'
import { appraisalSchemeApi } from '@/api/appraisal'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

const searchForm = reactive({
  schemeName: '',
  appraisalType: '',
  targetGroup: '',
  status: ''
})

const stats = reactive({
  total: 28,
  active: 8,
  participants: 456,
  indicators: 156
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    schemeCode: '*********',
    schemeName: '2024年度教学人员考核方案',
    appraisalType: 'annual',
    targetGroup: 'teaching',
    cycle: '2024年度',
    indicatorCount: 12,
    participantCount: 156,
    totalScore: 100,
    status: 'active',
    createTime: '2024-01-15',
    updateTime: '2024-06-10',
    description: '针对教学人员的年度综合考核方案',
    objectives: '全面评估教学人员的教学质量、科研能力和服务水平',
    scoringCriteria: '教学质量40%、科研成果30%、服务贡献20%、师德师风10%',
    notes: '重点关注教学质量和学生满意度'
  }
])

const formData = reactive({
  id: '',
  schemeCode: '',
  schemeName: '',
  appraisalType: '',
  targetGroup: '',
  cycle: '',
  totalScore: 100,
  description: '',
  objectives: '',
  scoringCriteria: '',
  notes: ''
})

const formRules = {
  schemeCode: [{ required: true, message: '请输入方案编号', trigger: 'blur' }],
  schemeName: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
  appraisalType: [{ required: true, message: '请选择考核类型', trigger: 'change' }],
  targetGroup: [{ required: true, message: '请选择适用对象', trigger: 'change' }],
  cycle: [{ required: true, message: '请输入考核周期', trigger: 'blur' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    schemeName: '',
    appraisalType: '',
    targetGroup: '',
    status: ''
  })
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增考核方案'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看考核方案'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑考核方案'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleIndicators = (row: unknown) => {
  ElMessage.info(`管理方案 ${row.schemeName} 的考核指标`)
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'activate':
      ElMessage.success(`考核方案 ${row.schemeName} 已启用`)
      break
    case 'deactivate':
      ElMessage.success(`考核方案 ${row.schemeName} 已停用`)
      break
    case 'copy':
      ElMessage.success(`已复制考核方案 ${row.schemeName}`)
      break
    case 'archive':
      ElMessage.success(`考核方案 ${row.schemeName} 已归档`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除考核方案 ${row.schemeName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出方案功能开发中')
}

const handleSubmit = async () => {
  try {
    // 表单验证通过后提交
    const schemeData: AppraisalScheme = {
      schemeCode: formData.schemeCode,
      schemeName: formData.schemeName,
      appraisalYear: new Date().getFullYear(),
      appraisalPeriod: getAppraisalPeriodFromType(formData.appraisalType),
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString().split('T')[0],
      description: formData.description,
      targetDepartments: getTargetDepartmentsFromGroup(formData.targetGroup),
      targetPositions: [],
      evaluationCriteria: formData.scoringCriteria,
      weightSettings: parseWeightSettings(formData.scoringCriteria),
      status: 'DRAFT'
    }
    
    if (formData.id) {
      // 更新
      await appraisalSchemeApi.update(formData.id, schemeData)
      ElMessage.success('更新成功')
    } else {
      // 新增
      await appraisalSchemeApi.create(schemeData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (__error) {
    console.error('保存考核方案失败:', error)
    ElMessage.error('保存失败')
  }
}

// 辅助函数：从考核类型获取考核周期
const getAppraisalPeriodFromType = (type: string) => {
  const periodMap: Record<string, string> = {
    annual: '年度',
    quarterly: '季度',
    monthly: '月度',
    special: '专项'
  }
  return periodMap[type] || ''
}

// 辅助函数：从适用对象获取目标部门
const getTargetDepartmentsFromGroup = (group: string) => {
  const deptMap: Record<string, string[]> = {
    teaching: ['教学部门', '各学院'],
    management: ['管理部门', '行政部门'],
    technical: ['技术部门', '信息中心'],
    service: ['服务部门', '后勤部门']
  }
  return deptMap[group] || []
}

// 辅助函数：解析权重设置
const parseWeightSettings = (criteria: string) => {
  const settings: Record<string, number> = {}
  const matches = criteria.match(/(\S+)(\d+)%/g)
  if (matches) {
    matches.forEach(match => {
      const parts = match.match(/(\S+)(\d+)%/)
      if (parts) {
        settings[parts[1]] = parseInt(parts[2])
      }
    })
  }
  return settings
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    schemeCode: '',
    schemeName: '',
    appraisalType: '',
    targetGroup: '',
    cycle: '',
    totalScore: 100,
    description: '',
    objectives: '',
    scoringCriteria: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage - 1, // API通常是从0开始
      size: pagination.pageSize,
      ...searchForm
    }
    
    const response = await appraisalSchemeApi.getPage(params)
    if (response.data.success) {
      tableData.value = response.data.data.content
      pagination.total = response.data.data.totalElements
    } else {
      throw new Error(response.data.message)
    }
  } catch (__error) {
    console.error('加载考核方案失败:', error)
    // 使用模拟数据
    tableData.value = [
      {
        id: '1',
        schemeCode: '*********',
        schemeName: '2024年度教学人员考核方案',
        appraisalType: 'annual',
        targetGroup: 'teaching',
        cycle: '2024年度',
        indicatorCount: 12,
        participantCount: 156,
        totalScore: 100,
        status: 'active',
        createTime: '2024-01-15',
        updateTime: '2024-06-10',
        description: '针对教学人员的年度综合考核方案',
        objectives: '全面评估教学人员的教学质量、科研能力和服务水平',
        scoringCriteria: '教学质量40%、科研成果30%、服务贡献20%、师德师风10%',
        notes: '重点关注教学质量和学生满意度'
      }
    ]
    pagination.total = 28
    ElMessage.warning('数据加载失败，显示默认数据')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    annual: 'primary',
    quarterly: 'success',
    monthly: 'warning',
    special: 'info'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    annual: '年度考核',
    quarterly: '季度考核',
    monthly: '月度考核',
    special: '专项考核'
  }
  return labels[type] || type
}

const getGroupColor = (group: string) => {
  const colors: Record<string, string> = {
    teaching: 'primary',
    management: 'success',
    technical: 'warning',
    service: 'info'
  }
  return colors[group] || ''
}

const getGroupLabel = (group: string) => {
  const labels: Record<string, string> = {
    teaching: '教学人员',
    management: '管理人员',
    technical: '技术人员',
    service: '服务人员'
  }
  return labels[group] || group
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    active: 'success',
    inactive: 'warning',
    archived: 'danger'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '草稿',
    active: '启用',
    inactive: '停用',
    archived: '归档'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.appraisal-scheme-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.participants {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.indicators {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
