<template>
  <div class="kpi-configuration">
    <!-- 指标类别管理 -->
    <el-card class="category-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">指标类别管理</span>
          <el-button type="primary" size="small" @click="handleAddCategory">
            <el-icon><Plus /></el-icon>
            新增类别
          </el-button>
        </div>
      </template>

      <el-tree
        ref="categoryTreeRef"
        :data="categoryTree"
        :props="treeProps"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        draggable
        @node-drag-start="handleDragStart"
        @node-drag-enter="handleDragEnter"
        @node-drag-leave="handleDragLeave"
        @node-drag-over="handleDragOver"
        @node-drag-end="handleDragEnd"
        @node-drop="handleDrop"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <div class="node-content">
              <el-icon v-if="data.level === 1" class="node-icon">
                <Folder />
              </el-icon>
              <el-icon v-else class="node-icon">
                <Document />
              </el-icon>
              <span class="node-label">{{ data.name }}</span>
              <el-tag v-if="data.weight" type="warning" size="small" class="node-weight">
                权重: {{ data.weight }}%
              </el-tag>
            </div>
            <div class="node-actions">
              <el-button
                v-if="data.level === 1"
                type="primary"
                link
                size="small"
                @click.stop="handleAddKPI(data)"
              >
                添加指标
              </el-button>
              <el-button
                type="primary"
                link
                size="small"
                @click.stop="handleEdit(data)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                link
                size="small"
                @click.stop="handleDelete(data)"
              >
                删除
              </el-button>
            </div>
          </div>
        </template>
      </el-tree>
    </el-card>

    <!-- 指标详情 -->
    <el-card class="detail-card" shadow="never">
      <template #header>
        <span class="card-title">指标详情</span>
      </template>

      <el-table
        v-loading="loading"
        :data="selectedKPIs"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="指标名称" width="180"  />
        <el-table-column prop="code" label="指标编码" width="120"  />
        <el-table-column prop="category" label="所属类别" width="120"  />
        <el-table-column prop="weight" label="权重(%)" width="100">
          <template #default="scope">
            <el-tag type="warning">{{ scope.row.weight }}%</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="指标类型" width="100">
          <template #default="scope">
            <el-tag :type="getKPITypeTagType(scope.row.type)" size="small">
              {{ getKPITypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scoreType" label="计分方式" width="100">
          <template #default="scope">
            {{ getScoreTypeText(scope.row.scoreType) }}
          </template>
        </el-table-column>
        <el-table-column prop="target" label="目标值" width="100"  />
        <el-table-column prop="description" label="指标说明" min-width="200" show-overflow-tooltip  />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleViewFormula(scope.row)">
              计算公式
            </el-button>
            <el-button size="small" type="primary" link @click="handleEditKPI(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 权重汇总 -->
      <div class="weight-summary">
        <el-alert
          :title="`当前类别权重汇总：${totalWeight}%`"
          :type="totalWeight === 100 ? 'success' : 'warning'"
          :closable="false"
          show-icon
        >
          <template v-if="totalWeight !== 100">
            <span>权重总和必须等于100%，请调整各指标权重</span>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 类别编辑对话框 -->
    <el-dialog
      v-model="categoryDialogVisible"
      :title="categoryDialogTitle"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="类别名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入类别名称"   />
        </el-form-item>
        <el-form-item label="类别编码" prop="code">
          <el-input v-model="categoryForm.code" placeholder="请输入类别编码"   />
        </el-form-item>
        <el-form-item label="类别说明" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入类别说明"
            />
        </el-form-item>
        <el-form-item label="排序号" prop="sort">
          <el-input-number v-model="categoryForm.sort" :min="0" :max="999"   />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="categoryDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSaveCategory">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- KPI编辑对话框 -->
    <el-dialog
      v-model="kpiDialogVisible"
      :title="kpiDialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="kpiFormRef"
        :model="kpiForm"
        :rules="kpiRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="name">
              <el-input v-model="kpiForm.name" placeholder="请输入指标名称"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标编码" prop="code">
              <el-input v-model="kpiForm.code" placeholder="请输入指标编码"   />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标类型" prop="type">
              <el-select v-model="kpiForm.type" placeholder="请选择指标类型">
                <el-option label="定量指标" value="QUANTITATIVE"  />
                <el-option label="定性指标" value="QUALITATIVE"  />
                <el-option label="加分项" value="BONUS"  />
                <el-option label="扣分项" value="PENALTY"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权重(%)" prop="weight">
              <el-input-number v-model="kpiForm.weight" :min="0" :max="100" :step="5"   />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计分方式" prop="scoreType">
              <el-select v-model="kpiForm.scoreType" placeholder="请选择计分方式">
                <el-option label="直接打分" value="DIRECT"  />
                <el-option label="达成率计算" value="ACHIEVEMENT"  />
                <el-option label="级差计分" value="GRADIENT"  />
                <el-option label="扣分制" value="DEDUCTION"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标值" prop="target">
              <el-input v-model="kpiForm.target" placeholder="请输入目标值">
                <template #append>
                  <el-select v-model="kpiForm.unit" style="width: 80px">
                    <el-option label="%" value="PERCENT"  />
                    <el-option label="个" value="COUNT"  />
                    <el-option label="元" value="AMOUNT"  />
                    <el-option label="分" value="SCORE"  />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="评分标准" prop="scoringCriteria">
          <div class="scoring-criteria">
            <el-button type="primary" size="small" @click="handleAddCriteria">
              <el-icon><Plus /></el-icon>
              添加标准
            </el-button>
            <el-table :data="kpiForm.scoringCriteria" size="small" class="criteria-table">
              <el-table-column prop="level" label="等级" width="80">
                <template #default="scope">
                  <el-select v-model="scope.row.level" size="small">
                    <el-option label="优秀" value="A"  />
                    <el-option label="良好" value="B"  />
                    <el-option label="合格" value="C"  />
                    <el-option label="不合格" value="D"  />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="minValue" label="最小值" width="100">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.minValue"
                    size="small"
                    :min="0"
                    :max="scope.row.maxValue"
                    />
                </template>
              </el-table-column>
              <el-table-column prop="maxValue" label="最大值" width="100">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.maxValue"
                    size="small"
                    :min="scope.row.minValue"
                    />
                </template>
              </el-table-column>
              <el-table-column prop="score" label="得分" width="100">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.score"
                    size="small"
                    :min="0"
                    :max="100"
                    />
                </template>
              </el-table-column>
              <el-table-column prop="description" label="说明">
                <template #default="scope">
                  <el-input v-model="scope.row.description" size="small"   />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="60">
                <template #default="scope">
                  <el-button
                    type="danger"
                    link
                    size="small"
                    @click="handleRemoveCriteria(scope.$index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>

        <el-form-item label="数据来源" prop="dataSource">
          <el-select v-model="kpiForm.dataSource" placeholder="请选择数据来源">
            <el-option label="手工录入" value="MANUAL"  />
            <el-option label="系统自动" value="SYSTEM"  />
            <el-option label="接口导入" value="API"  />
            <el-option label="Excel导入" value="EXCEL"  />
          </el-select>
        </el-form-item>

        <el-form-item label="指标说明" prop="description">
          <el-input
            v-model="kpiForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入指标说明"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="kpiDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSaveKPI">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 计算公式对话框 -->
    <el-dialog
      v-model="formulaDialogVisible"
      title="计算公式配置"
      width="600px"
    >
      <div class="formula-config">
        <el-form label-width="100px">
          <el-form-item label="公式类型">
            <el-radio-group v-model="formulaForm.type">
              <el-radio value="SIMPLE">简单公式</el-radio>
              <el-radio value="COMPLEX">复杂公式</el-radio>
              <el-radio value="CUSTOM">自定义公式</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="formulaForm.type === 'SIMPLE'" label="计算方式">
            <el-select v-model="formulaForm.method">
              <el-option label="实际值/目标值 × 100%" value="ACHIEVEMENT_RATE"  />
              <el-option label="(实际值-基准值)/目标值 × 100%" value="GROWTH_RATE"  />
              <el-option label="基础分 × 完成率" value="BASE_MULTIPLY"  />
              <el-option label="基础分 - 扣分项" value="BASE_MINUS"  />
            </el-select>
          </el-form-item>

          <el-form-item v-else-if="formulaForm.type === 'COMPLEX'" label="公式编辑">
            <div class="formula-editor">
              <el-input
                v-model="formulaForm.expression"
                type="textarea"
                :rows="4"
                placeholder="请输入计算公式，支持变量：{actual}实际值, {target}目标值, {base}基准值"
                />
              <div class="formula-variables">
                <el-tag
                  v-for="variable in formulaVariables"
                  :key="variable.key"
                  @click="insertVariable(variable.key)"
                  class="variable-tag"
                >
                  {{ variable.name }}
                </el-tag>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="预览结果">
            <div class="formula-preview">
              <el-descriptions :column="2" border size="small">
                <el-descriptions-item label="测试数据">
                  实际值: 85, 目标值: 100, 基准值: 80
                </el-descriptions-item>
                <el-descriptions-item label="计算结果">
                  <el-tag type="success">{{ calculatePreview() }} 分</el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="formulaDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleSaveFormula">保存公式</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */

import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElTree } from 'element-plus'
import {
  Plus,
  Folder,
  Document
} from '@element-plus/icons-vue'

// Props定义
interface Props {
  schemeId?: string
}

const props = withDefaults(defineProps<Props>(), {
  schemeId: ''
})

// 响应式数据
const categoryTreeRef = ref<InstanceType<typeof ElTree>>()
const loading = ref(false)
const saveLoading = ref(false)
const categoryTree = ref<any[]>([])
const selectedKPIs = ref<any[]>([])

// 对话框控制
const categoryDialogVisible = ref(false)
const categoryDialogTitle = ref('')
const kpiDialogVisible = ref(false)
const kpiDialogTitle = ref('')
const formulaDialogVisible = ref(false)

// 表单数据
const categoryFormRef = ref()
const categoryForm = reactive({
  id: '',
  name: '',
  code: '',
  description: '',
  sort: 0,
  parentId: ''
})

const kpiFormRef = ref()
const kpiForm = reactive({
  id: '',
  name: '',
  code: '',
  type: 'QUANTITATIVE',
  weight: 0,
  scoreType: 'DIRECT',
  target: '',
  unit: 'PERCENT',
  scoringCriteria: [] as unknown[],
  dataSource: 'MANUAL',
  description: '',
  categoryId: ''
})

const formulaForm = reactive({
  type: 'SIMPLE',
  method: 'ACHIEVEMENT_RATE',
  expression: '',
  kpiId: ''
})

// 公式变量
const formulaVariables = [
  { key: '{actual}', name: 'HrHr实际值' },
  { key: '{target}', name: '目标值' },
  { key: '{base}', name: '基准值' },
  { key: '{weight}', name: '权重' },
  { key: '{score}', name: '基础分' }
]

// 树形控件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 表单验证规则
const categoryRules = {
  name: [
    { required: true, message: '请输入类别名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入类别编码', trigger: 'blur' }
  ]
}

const kpiRules = {
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入指标编码', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择指标类型', trigger: 'change' }
  ],
  weight: [
    { required: true, message: '请输入权重', trigger: 'blur' }
  ],
  scoreType: [
    { required: true, message: '请选择计分方式', trigger: 'change' }
  ]
}

// 计算属性
const totalWeight = computed(() => {
  return selectedKPIs.value.reduce((sum, kpi) => sum + (kpi.weight || 0), 0)
})

// 初始化数据
const initData = async () => {
  try {
    loading.value = true
    // 模拟获取数据
    categoryTree.value = [
      {
        id: '1',
        name: '工作业绩',
        code: 'PERFORMANCE',
        level: 1,
        weight: 60,
        children: [
          {
            id: '11',
            name: '教学工作量',
            code: 'TEACHING_LOAD',
            level: 2,
            weight: 30,
            parentId: '1'
          },
          {
            id: '12',
            name: '科研成果',
            code: 'RESEARCH',
            level: 2,
            weight: 20,
            parentId: '1'
          },
          {
            id: '13',
            name: '社会服务',
            code: 'SERVICE',
            level: 2,
            weight: 10,
            parentId: '1'
          }
        ]
      },
      {
        id: '2',
        name: '工作能力',
        code: 'ABILITY',
        level: 1,
        weight: 25,
        children: [
          {
            id: '21',
            name: '专业能力',
            code: 'PROFESSIONAL',
            level: 2,
            weight: 15,
            parentId: '2'
          },
          {
            id: '22',
            name: '管理能力',
            code: 'MANAGEMENT',
            level: 2,
            weight: 10,
            parentId: '2'
          }
        ]
      },
      {
        id: '3',
        name: '工作态度',
        code: 'ATTITUDE',
        level: 1,
        weight: 15,
        children: []
      }
    ]

    // 默认选中第一个类别的指标
    if(categoryTree.value.length > 0 && categoryTree.value[0].children.length > 0: unknown) {
      await loadKPIs(categoryTree.value[0].children[0].id)
    }
  } catch(_error: unknown) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  } finally {
    loading.value = false
  }
}

// 加载指标数据
const loadKPIs = async (categoryId: string) => {
  try {
    loading.value = true
    // 模拟获取指标数据
    selectedKPIs.value = [
      {
        id: '1',
        name: '授课学时数',
        code: 'TEACHING_HOURS',
        category: '教学工作量',
        weight: 15,
        type: 'QUANTITATIVE',
        scoreType: 'ACHIEVEMENT',
        target: '320',
        unit: '学时',
        description: '完成规定的授课学时数'
      },
      {
        id: '2',
        name: '教学质量评价',
        code: 'TEACHING_QUALITY',
        category: '教学工作量',
        weight: 10,
        type: 'QUALITATIVE',
        scoreType: 'DIRECT',
        target: '90',
        unit: '分',
        description: '学生评教和督导评价综合得分'
      },
      {
        id: '3',
        name: '指导学生竞赛',
        code: 'COMPETITION_GUIDE',
        category: '教学工作量',
        weight: 5,
        type: 'BONUS',
        scoreType: 'GRADIENT',
        target: '2',
        unit: '项',
        description: '指导学生参加各类竞赛获奖'
      }
    ]
  } catch(_error: unknown) {
    console.error('加载指标数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 新增类别
const handleAddCategory = () => {
  Object.assign(categoryForm, {
    id: '',
    name: '',
    code: '',
    description: '',
    sort: 0,
    parentId: ''
  })
  categoryDialogTitle.value = '新增指标类别'
  categoryDialogVisible.value = true
}

// 新增KPI
   
const handleAddKPI = (category: unknown) => {
  Object.assign(kpiForm, {
    id: '',
    name: '',
    code: '',
    type: 'QUANTITATIVE',
    weight: 0,
    scoreType: 'DIRECT',
    target: '',
    unit: 'PERCENT',
    scoringCriteria: [],
    dataSource: 'MANUAL',
    description: '',
    categoryId: category.id
  })
  kpiDialogTitle.value = `新增考核指标 - ${category.name}`
  kpiDialogVisible.value = true
}

// 编辑
   
const handleEdit = (data: unknown) => {
  if(data.level === 1: unknown) {
    Object.assign(categoryForm, data)
    categoryDialogTitle.value = '编辑指标类别'
    categoryDialogVisible.value = true
  } else {
    handleEditKPI(data)
  }
}

// 编辑KPI
   
const handleEditKPI = (kpi: unknown) => {
  Object.assign(kpiForm, kpi)
  kpiDialogTitle.value = '编辑考核指标'
  kpiDialogVisible.value = true
}

// 删除
   
const handleDelete = async (data: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除"${data.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用删除API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('删除成功')
    initData()
  } catch(_error: unknown) {
    if(error !== 'cancel': unknown) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 保存类别
const handleSaveCategory = async () => {
  try {
    await categoryFormRef.value.validate()
    saveLoading.value = true
    
    // 调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('保存成功')
    categoryDialogVisible.value = false
    initData()
  } catch(_error: unknown) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 保存KPI
const handleSaveKPI = async () => {
  try {
    await kpiFormRef.value.validate()
    saveLoading.value = true
    
    // 调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('保存成功')
    kpiDialogVisible.value = false
    loadKPIs(kpiForm.categoryId)
  } catch(_error: unknown) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 添加评分标准
const handleAddCriteria = () => {
  kpiForm.scoringCriteria.push({
    level: 'A',
    minValue: 90,
    maxValue: 100,
    score: 100,
    description: ''
  })
}

// 移除评分标准
const handleRemoveCriteria = (index: number) => {
  kpiForm.scoringCriteria.splice(index, 1)
}

// 查看计算公式
   
const handleViewFormula = (kpi: unknown) => {
  formulaForm.kpiId = kpi.id
  formulaDialogVisible.value = true
}

// 插入变量
const insertVariable = (variable: string) => {
  formulaForm.expression += variable
}

// 预览计算结果
const calculatePreview = () => {
  // 简单的计算示例
  if(formulaForm.type === 'SIMPLE' && formulaForm.method === 'ACHIEVEMENT_RATE': unknown) {
    return ((85 / 100) * 100).toFixed(2)
  }
  return '85.00'
}

// 保存公式
const handleSaveFormula = () => {
  ElMessage.success('公式保存成功')
  formulaDialogVisible.value = false
}

// 拖拽相关
   
const handleDragStart = (node: unknown, event: unknown) => {
  console.log('drag start', node)
}

   
const handleDragEnter = (draggingNode: unknown, dropNode: unknown, event: unknown) => {
  console.log('drag enter')
}

   
const handleDragLeave = (draggingNode: unknown, dropNode: unknown, event: unknown) => {
  console.log('drag leave')
}

   
const handleDragOver = (draggingNode: unknown, dropNode: unknown, event: unknown) => {
  console.log('drag over')
}

   
const handleDragEnd = (draggingNode: unknown, dropNode: unknown, dropType: unknown, event: unknown) => {
  console.log('drag end')
}

   
const handleDrop = (draggingNode: unknown, dropNode: unknown, dropType: unknown, event: unknown) => {
  console.log('tree drop')
}

// 获取KPI类型标签类型
const getKPITypeTagType = (type: string) => {
  switch(type: unknown) {
    case 'QUANTITATIVE': return 'primary'
    case 'QUALITATIVE': return 'success'
    case 'BONUS': return 'warning'
    case 'PENALTY': return 'danger'
    default: return ''
  }
}

// 获取KPI类型文本
const getKPITypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'QUANTITATIVE': '定量',
    'QUALITATIVE': '定性',
    'BONUS': '加分',
    'PENALTY': '扣分'
  }
  return typeMap[type] || type
}

// 获取计分方式文本
const getScoreTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'DIRECT': '直接打分',
    'ACHIEVEMENT': '达成率',
    'GRADIENT': '级差计分',
    'DEDUCTION': '扣分制'
  }
  return typeMap[type] || type
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<style scoped>
.kpi-configuration {
  display: flex;
  gap: 20px;
  height: 100%;
}

.category-card {
  flex: 0 0 400px;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.detail-card {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
}

:deep(.el-card__body) {
  flex: 1;
  overflow: auto;
}

.tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-icon {
  color: #909399;
}

.node-label {
  color: #303133;
}

.node-weight {
  margin-left: 8px;
}

.node-actions {
  display: none;
  gap: 8px;
}

.tree-node:hover .node-actions {
  display: flex;
}

.weight-summary {
  margin-top: 20px;
}

.scoring-criteria {
  width: 100%;
}

.criteria-table {
  margin-top: 10px;
}

.formula-config {
  padding: 10px;
}

.formula-editor {
  width: 100%;
}

.formula-variables {
  margin-top: 10px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.variable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.variable-tag:hover {
  color: #409eff;
  border-color: #409eff;
}

.formula-preview {
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>