<template>
  <div class="appraisal-record-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Notebook /></el-icon>
        考核记录管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增记录
        </el-button>
        <el-button @click="handleBatchScore">
          <el-icon><Edit /></el-icon>
          批量评分
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出记录
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="员工姓名">
          <el-input
            v-model="searchForm.employeeName"
            placeholder="请输入员工姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="考核方案">
          <el-select
            v-model="searchForm.schemeId"
            placeholder="请选择考核方案"
            clearable
            style="width: 200px"
          >
            <el-option 
              v-for="scheme in schemeList"
              :key="scheme.id"
              :label="scheme.schemeName"
              :value="scheme.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="考核状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待评分" value="pending"  />
            <el-option label="评分中" value="scoring"  />
            <el-option label="已完成" value="completed"  />
            <el-option label="已提交" value="submitted"  />
          </el-select>
        </el-form-item>
        <el-form-item label="考核周期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Notebook /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总记录数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.pending }}</div>
              <div class="stat-label">待评分</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon average">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.average }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 考核记录列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="recordId" label="记录编号" width="120"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="employeeId" label="员工工号" width="120"  />
        <el-table-column prop="department" label="所属部门" width="120"  />
        <el-table-column prop="schemeName" label="考核方案" min-width="180"  />
        <el-table-column prop="appraisalPeriod" label="考核周期" width="120"  />
        <el-table-column prop="totalScore" label="总分" width="80">
          <template #default="{ row }">
            <span :class="getScoreClass(row.totalScore)">
              {{ row.totalScore || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="考核等级" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.level)" v-if="row.level">
              {{ getLevelLabel(row.level) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="evaluator" label="评分人" width="100"  />
        <el-table-column prop="evaluateTime" label="评分时间" width="120"  />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleScore(row)" v-if="row.status === 'pending'">
              评分
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)" v-if="row.status !== 'submitted'">
              编辑
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="submit" v-if="row.status === 'completed'">
                    提交记录
                  </el-dropdown-item>
                  <el-dropdown-item command="feedback">查看反馈</el-dropdown-item>
                  <el-dropdown-item command="history">评分历史</el-dropdown-item>
                  <el-dropdown-item command="print">打印记录</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 考核记录详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="记录编号" prop="recordId">
              <el-input v-model="formData.recordId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工姓名" prop="employeeName">
              <el-select v-model="formData.employeeName" :disabled="isView" style="width: 100%">
                <el-option label="张三" value="张三"  />
                <el-option label="李四" value="李四"  />
                <el-option label="王五" value="王五"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="考核方案" prop="schemeName">
              <el-select 
                v-model="formData.schemeId" 
                :disabled="isView" 
                style="width: 100%"
                @change="handleSchemeChange"
              >
                <el-option 
                  v-for="scheme in schemeList"
                  :key="scheme.id"
                  :label="scheme.schemeName"
                  :value="scheme.id"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核周期" prop="appraisalPeriod">
              <el-input v-model="formData.appraisalPeriod" :disabled="isView" placeholder="如：2024年度"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="总分" prop="totalScore">
              <el-input-number
                v-model="formData.totalScore"
                :min="0"
                :max="100"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">分</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核等级" prop="level">
              <el-select v-model="formData.level" :disabled="isView" style="width: 100%">
                <el-option label="优秀" value="excellent"  />
                <el-option label="良好" value="good"  />
                <el-option label="合格" value="qualified"  />
                <el-option label="不合格" value="unqualified"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="自评内容" prop="selfEvaluation">
          <el-input
            v-model="formData.selfEvaluation"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入自评内容"
            />
        </el-form-item>
        <el-form-item label="评价意见" prop="evaluationComments">
          <el-input
            v-model="formData.evaluationComments"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入评价意见"
            />
        </el-form-item>
        <el-form-item label="改进建议" prop="improvementSuggestions">
          <el-input
            v-model="formData.improvementSuggestions"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入改进建议"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AppraisalRecordManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Notebook,
  Plus,
  Edit,
  Download,
  Search,
  Refresh,
  Clock,
  CircleCheck,
  TrendCharts,
  ArrowDown
} from '@element-plus/icons-vue'
import { appraisalRecordApi, appraisalSchemeApi } from '@/api/appraisal'
import type { AppraisalRecord, AppraisalScheme, EvaluationResult } from '@/api/appraisal'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)
const selectedRecords = ref([])

const searchForm = reactive({
  employeeName: '',
  schemeId: '',
  status: '',
  dateRange: []
})

const stats = reactive({
  total: 456,
  pending: 89,
  completed: 312,
  average: 85.6
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref<any[]>([])
const schemeList = ref<AppraisalScheme[]>([])

const formData = reactive({
  id: '',
  recordId: '',
  employeeName: '',
  employeeId: '',
  schemeId: '',
  schemeName: '',
  appraisalPeriod: '',
  totalScore: 0,
  level: '',
  selfEvaluation: '',
  evaluationComments: '',
  improvementSuggestions: '',
  notes: ''
})

const formRules = {
  recordId: [{ required: true, message: '请输入记录编号', trigger: 'blur' }],
  employeeName: [{ required: true, message: '请选择员工', trigger: 'change' }],
  schemeName: [{ required: true, message: '请选择考核方案', trigger: 'change' }],
  appraisalPeriod: [{ required: true, message: '请输入考核周期', trigger: 'blur' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    employeeName: '',
    schemeId: '',
    status: '',
    dateRange: []
  })
  loadData()
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRecords.value = selection as unknown // 临时修复类型不匹配
}

const handleAdd = () => {
  dialogTitle.value = '新增考核记录'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看考核记录'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑考核记录'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleScore = (row: unknown) => {
  ElMessage.info(`为 ${row.employeeName} 进行考核评分`)
}

const handleBatchScore = () => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请先选择要批量评分的记录')
    return
  }
  ElMessage.info('批量评分功能开发中')
}

   
const handleCommand = async (command: string, row: unknown) => {
  switch (command) {
    case 'submit':
      try {
        // 提交HR审核
        const review: EvaluationResult = {
          evaluatorId: 'HR001',
          evaluatorName: 'HR部门',
          evaluatorType: 'HR',
          itemScores: {},
          totalScore: row.totalScore,
          comments: '审核通过',
          evaluationTime: new Date().toISOString()
        }
        await appraisalRecordApi.submitHrReview(row.id, review)
        ElMessage.success(`${row.employeeName} 的考核记录已提交`)
        loadData()
      } catch (__error) {
        console.error('提交考核记录失败:', error)
        ElMessage.error('提交失败')
      }
      break
    case 'feedback':
      ElMessage.info(`查看 ${row.employeeName} 的考核反馈`)
      break
    case 'history':
      ElMessage.info(`查看 ${row.employeeName} 的评分历史`)
      break
    case 'print':
      ElMessage.info(`打印 ${row.employeeName} 的考核记录`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除 ${row.employeeName} 的考核记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.info('删除功能需要特殊权限')
      })
      break
  }
}

const handleExport = () => {
  ElMessage.info('导出记录功能开发中')
}

const handleSubmit = async () => {
  try {
    const recordData: AppraisalRecord = {
      schemeId: formData.schemeId,
      schemeName: formData.schemeName,
      employeeId: formData.employeeId,
      employeeName: formData.employeeName,
      departmentId: 'DEPT001', // 简化处理
      departmentName: '计算机学院',
      position: '教师',
      templateId: 'TPL001', // 简化处理
      templateName: '教学人员考核模板',
      finalScore: formData.totalScore,
      finalGrade: formData.level,
      status: 'NOT_STARTED',
      comments: formData.notes
    }
    
    if (formData.id) {
      // 更新
      await appraisalRecordApi.update(formData.id, recordData)
      ElMessage.success('更新成功')
    } else {
      // 新增
      await appraisalRecordApi.create(recordData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (__error) {
    console.error('保存考核记录失败:', error)
    ElMessage.error('保存失败')
  }
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    recordId: '',
    employeeName: '',
    employeeId: '',
    schemeId: '',
    schemeName: '',
    appraisalPeriod: '',
    totalScore: 0,
    level: '',
    selfEvaluation: '',
    evaluationComments: '',
    improvementSuggestions: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      schemeId: searchForm.schemeId,
      status: mapStatusToApi(searchForm.status)
    }
    
    // 如果有员工姓名搜索，需要通过员工API获取员工ID
    if (searchForm.employeeName) {
      // 这里简化处理，实际需要调用员工API搜索
      params.employeeId = 'EMP001'
    }
    
    const response = await appraisalRecordApi.getPage(params)
    if (response && response.data) {
      tableData.value = response.data.content.map(item => ({
        ...item,
        recordId: item.id,
        level: calculateLevel(item.finalScore),
        evaluator: item.supervisorEvaluation?.evaluatorName || '-',
        evaluateTime: item.completedTime ? item.completedTime.split(' ')[0] : '-',
        totalScore: item.finalScore || 0,
        selfEvaluation: item.selfEvaluation?.comments || '',
        evaluationComments: item.supervisorEvaluation?.comments || '',
        improvementSuggestions: item.hrReview?.comments || '',
        notes: item.comments || ''
      }))
      pagination.total = response.data.totalElements
      
      // 更新统计数据
      const pendingCount = tableData.value.filter(item => 
        item.status === 'NOT_STARTED' || item.status === 'SELF_EVALUATION'
      ).length
      const completedCount = tableData.value.filter(item => 
        item.status === 'COMPLETED'
      ).length
      const totalScores = tableData.value
        .filter(item => item.finalScore)
        .map(item => item.finalScore)
      const avgScore = totalScores.length > 0 
        ? totalScores.reduce((a, b) => a + b, 0) / totalScores.length 
        : 0
      
      stats.total = response.data.totalElements
      stats.pending = pendingCount
      stats.completed = completedCount
      stats.average = Math.round(avgScore * 10) / 10
    }
  } catch (__error) {
    console.error('加载考核记录列表失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载考核方案列表
const loadSchemes = async () => {
  try {
    const response = await appraisalSchemeApi.getPage({ 
      page: 0, 
      size: 100,
      status: 'PUBLISHED'
    })
    if (response && response.data) {
      schemeList.value = response.data.content
    }
  } catch (__error) {
    console.error('加载考核方案列表失败:', error)
  }
}

// 辅助函数：映射状态到API
const mapStatusToApi = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'NOT_STARTED',
    scoring: 'SELF_EVALUATION',
    completed: 'COMPLETED',
    submitted: 'COMPLETED'
  }
  return statusMap[status] || status
}

// 辅助函数：计算等级
const calculateLevel = (score?: number) => {
  if (!score) return ''
  if (score >= 90) return 'excellent'
  if (score >= 80) return 'good'
  if (score >= 60) return 'qualified'
  return 'unqualified'
}

// 处理考核方案选择变化
const handleSchemeChange = (schemeId: string) => {
  const scheme = schemeList.value.find(s => s.id === schemeId)
  if (scheme) {
    formData.schemeName = scheme.schemeName
    formData.appraisalPeriod = `${scheme.appraisalYear}年${scheme.appraisalPeriod}`
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 60) return 'score-qualified'
  return 'score-unqualified'
}

const getLevelType = (level: string) => {
  const types: Record<string, string> = {
    excellent: 'success',
    good: 'primary',
    qualified: 'warning',
    unqualified: 'danger'
  }
  return types[level] || ''
}

const getLevelLabel = (level: string) => {
  const labels: Record<string, string> = {
    excellent: '优秀',
    good: '良好',
    qualified: '合格',
    unqualified: '不合格'
  }
  return labels[level] || level
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    scoring: 'warning',
    completed: 'primary',
    submitted: 'success'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待评分',
    scoring: '评分中',
    completed: '已完成',
    submitted: '已提交'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
  loadSchemes()
})
</script>

<style scoped>
.appraisal-record-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.average {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.score-excellent {
  color: #67c23a;
  font-weight: 600;
}

.score-good {
  color: #409eff;
  font-weight: 600;
}

.score-qualified {
  color: #e6a23c;
  font-weight: 600;
}

.score-unqualified {
  color: #f56c6c;
  font-weight: 600;
}
</style>
