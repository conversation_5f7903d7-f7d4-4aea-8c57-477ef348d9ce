<template>
  <div class="appraisal-scoring-flow">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>考核评分流程</h2>
      <p>进行绩效考核的多维度评分和流程管理</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filterForm" inline>
        <el-form-item label="考核期间">
          <el-select v-model="filterForm.periodId" placeholder="请选择考核期间">
            <el-option
              v-for="period in periodOptions"
              :key="period.id"
              :label="period.name"
              :value="period.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filterForm.departmentId" clearable>
            <el-option label="全部部门" value=""  />
            <el-option
              v-for="dept in departmentOptions"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="评分状态">
          <el-select v-model="filterForm.status" clearable>
            <el-option label="全部状态" value=""  />
            <el-option label="待自评" value="PENDING_SELF"  />
            <el-option label="待上级评分" value="PENDING_SUPERIOR"  />
            <el-option label="待部门评分" value="PENDING_DEPARTMENT"  />
            <el-option label="待HR评分" value="PENDING_HR"  />
            <el-option label="已完成" value="COMPLETED"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 评分进度统计 -->
    <el-row :gutter="20" class="progress-row">
      <el-col :span="6">
        <el-card class="progress-card">
          <div class="progress-content">
            <div class="progress-icon total">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="progress-info">
              <div class="progress-number">{{ progressStats.total }}</div>
              <div class="progress-label">参评人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="progress-card">
          <div class="progress-content">
            <div class="progress-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="progress-info">
              <div class="progress-number">{{ progressStats.pending }}</div>
              <div class="progress-label">待评分</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="progress-card">
          <div class="progress-content">
            <div class="progress-icon processing">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="progress-info">
              <div class="progress-number">{{ progressStats.processing }}</div>
              <div class="progress-label">评分中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="progress-card">
          <div class="progress-content">
            <div class="progress-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="progress-info">
              <div class="progress-number">{{ progressStats.completed }}</div>
              <div class="progress-label">已完成</div>
              <el-progress
                :percentage="progressPercentage"
                :stroke-width="4"
                :show-text="false"
                class="progress-bar"
               />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 评分任务列表 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">评分任务列表</span>
          <div class="card-actions">
            <el-button type="primary" @click="handleBatchAssign">
              <el-icon><User /></el-icon>
              批量分配
            </el-button>
            <el-button @click="handleExport">
              <el-icon><Download /></el-icon>
              导出进度
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="department" label="部门" width="150"  />
        <el-table-column prop="position" label="岗位" width="120"  />
        <el-table-column label="评分进度" width="300">
          <template #default="scope">
            <div class="scoring-progress">
              <el-steps :active="scope.row.currentStep" finish-status="success" simple>
                <el-step
                  v-for="step in scope.row.steps"
                  :key="step.id"
                  :title="step.title"
                  :status="step.status"
                 />
              </el-steps>
              <div class="progress-detail">
                当前环节：{{ scope.row.currentStepName }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="selfScore" label="自评分" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.selfScore" type="primary">
              {{ scope.row.selfScore }}
            </el-tag>
            <span v-else class="score-empty">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="superiorScore" label="上级评分" width="90" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.superiorScore" type="success">
              {{ scope.row.superiorScore }}
            </el-tag>
            <span v-else class="score-empty">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="finalScore" label="最终得分" width="90" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.finalScore" type="warning" effect="dark">
              {{ scope.row.finalScore }}
            </el-tag>
            <span v-else class="score-empty">-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button
              v-if="canScore(scope.row)"
              size="small"
              type="primary"
              @click="handleScore(scope.row)"
            >
              评分
            </el-button>
            <el-button
              size="small"
              @click="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button
              size="small"
              type="info"
              link
              @click="handleRemind(scope.row)"
            >
              催办
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 评分对话框 -->
    <el-dialog
      v-model="scoreDialogVisible"
      :title="`${currentEmployee?.employeeName} - ${scoreType}评分`"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="score-dialog-content">
        <!-- 基本信息 -->
        <el-descriptions :column="3" border class="employee-info">
          <el-descriptions-item label="姓名">{{ currentEmployee?.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="工号">{{ currentEmployee?.employeeNo }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ currentEmployee?.department }}</el-descriptions-item>
          <el-descriptions-item label="岗位">{{ currentEmployee?.position }}</el-descriptions-item>
          <el-descriptions-item label="考核期间">{{ currentEmployee?.period }}</el-descriptions-item>
          <el-descriptions-item label="考核方案">{{ currentEmployee?.scheme }}</el-descriptions-item>
        </el-descriptions>

        <!-- 评分表单 -->
        <el-form
          ref="scoreFormRef"
          :model="scoreForm"
          label-width="120px"
          class="score-form"
        >
          <!-- 指标评分 -->
          <div class="kpi-scoring">
            <h4>考核指标评分</h4>
            <el-table :data="scoreForm.kpiScores" show-summary :summary-method="getSummaries">
              <el-table-column prop="category" label="指标类别" width="120"  />
              <el-table-column prop="name" label="指标名称" width="180"  />
              <el-table-column prop="weight" label="权重(%)" width="80" align="center">
                <template #default="scope">
                  <el-tag size="small">{{ scope.row.weight }}%</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="target" label="目标值" width="100"  />
              <el-table-column prop="actual" label="实际值" width="120">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.actual"
                    :min="0"
                    :disabled="!canEditScore(scope.row)"
                    size="small"
                    @change="calculateScore(scope.row)"
                    />
                </template>
              </el-table-column>
              <el-table-column prop="score" label="得分" width="100" align="center">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.score"
                    :min="0"
                    :max="100"
                    :disabled="scope.row.scoreType === 'ACHIEVEMENT'"
                    size="small"
                    />
                </template>
              </el-table-column>
              <el-table-column prop="weightedScore" label="加权得分" width="100" align="center">
                <template #default="scope">
                  <el-tag type="warning">{{ (scope.row.score * scope.row.weight / 100).toFixed(2) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleViewKPIDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 综合评价 -->
          <el-form-item label="综合评价" prop="comments" class="comments-item">
            <el-input
              v-model="scoreForm.comments"
              type="textarea"
              :rows="4"
              placeholder="请输入综合评价意见..."
              maxlength="500"
              show-word-limit
              />
          </el-form-item>

          <!-- 改进建议 -->
          <el-form-item label="改进建议" prop="suggestions">
            <el-input
              v-model="scoreForm.suggestions"
              type="textarea"
              :rows="3"
              placeholder="请输入改进建议..."
              maxlength="300"
              show-word-limit
              />
          </el-form-item>

          <!-- 附件上传 -->
          <el-form-item label="相关附件">
            <el-upload
              ref="uploadRef"
              action="#"
              :auto-upload="false"
              :limit="5"
              :on-change="handleFileChange"
              accept=".pdf,.doc,.docx,.xls,.xlsx"
            >
              <el-button type="primary" plain>
                <el-icon><Upload /></el-icon>
                选择文件
              </el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持 PDF、Word、Excel 格式，单个文件不超过 10MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="scoreDialogVisible = false">取消</el-button>
        <el-button type="warning" @click="handleSaveDraft">暂存</el-button>
        <el-button type="primary" @click="handleSubmitScore">提交评分</el-button>
      </template>
    </el-dialog>

    <!-- 批量分配对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="批量分配评分任务"
      width="600px"
    >
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="评分环节">
          <el-select v-model="assignForm.step" placeholder="请选择评分环节">
            <el-option label="上级评分" value="SUPERIOR"  />
            <el-option label="部门评分" value="DEPARTMENT"  />
            <el-option label="HR评分" value="HR"  />
          </el-select>
        </el-form-item>
        <el-form-item label="评分人">
          <el-select
            v-model="assignForm.scorerId"
            filterable
            placeholder="请选择评分人"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.id"
              :label="`${user.name} (${user.position})`"
              :value="user.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="截止时间">
          <el-date-picker
            v-model="assignForm.deadline"
            type="datetime"
            placeholder="选择截止时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
           />
        </el-form-item>
        <el-form-item label="备注说明">
          <el-input
            v-model="assignForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注说明"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmAssign">确定分配</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  User,
  Download,
  UserFilled,
  Clock,
  Loading,
  CircleCheck,
  Upload
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const periodOptions = ref<any[]>([])
const departmentOptions = ref<any[]>([])
const userOptions = ref<any[]>([])

// 筛选表单
const filterForm = reactive({
  periodId: '',
  departmentId: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 进度统计
const progressStats = reactive({
  total: 100,
  pending: 25,
  processing: 45,
  completed: 30
})

// 对话框控制
const scoreDialogVisible = ref(false)
const assignDialogVisible = ref(false)
const currentEmployee = ref<unknown>(null)
const scoreType = ref('自评')

// 评分表单
const scoreFormRef = ref()
const scoreForm = reactive({
  kpiScores: [] as unknown[],
  comments: '',
  suggestions: '',
  attachments: [] as unknown[]
})

// 分配表单
const assignForm = reactive({
  step: '',
  scorerId: '',
  deadline: '',
  remark: ''
})

// 计算属性
const progressPercentage = computed(() => {
  if (progressStats.total === 0) return 0
  return Math.round((progressStats.completed / progressStats.total) * 100)
})

// 初始化数据
const initData = async () => {
  // 加载期间选项
  periodOptions.value = [
    { id: '1', name: 'HrHr2025年度考核' },
    { id: '2', name: '2025年第一季度考核' },
    { id: '3', name: '2024年度考核' }
  ]
  
  // 加载部门选项
  departmentOptions.value = [
    { id: '1', name: '计算机学院' },
    { id: '2', name: '机械工程学院' },
    { id: '3', name: '电气工程学院' }
  ]
  
  // 加载用户选项
  userOptions.value = [
    { id: '1', name: '张主任', position: '部门主任' },
    { id: '2', name: '李院长', position: '学院院长' },
    { id: '3', name: '王处长', position: '人事处长' }
  ]
  
  // 默认选中第一个期间
  if (periodOptions.value.length > 0) {
    filterForm.periodId = periodOptions.value[0].id
  }
  
  fetchList()
}

// 获取列表数据
const fetchList = async () => {
  try {
    loading.value = true
    
    // 模拟数据
    tableData.value = [
      {
        id: '1',
        employeeNo: 'EMP001',
        employeeName: '张三',
        department: '计算机学院',
        position: '讲师',
        period: '2025年度考核',
        scheme: '教学人员考核方案',
        currentStep: 1,
        currentStepName: '自评',
        selfScore: 88,
        superiorScore: null,
        finalScore: null,
        steps: [
          { id: '1', title: '自评', status: 'finish' },
          { id: '2', title: '上级评分', status: 'process' },
          { id: '3', title: '部门评分', status: 'wait' },
          { id: '4', title: 'HR审核', status: 'wait' }
        ]
      },
      {
        id: '2',
        employeeNo: 'EMP002',
        employeeName: '李四',
        department: '机械工程学院',
        position: '副教授',
        period: '2025年度考核',
        scheme: '教学人员考核方案',
        currentStep: 2,
        currentStepName: '上级评分',
        selfScore: 92,
        superiorScore: 90,
        finalScore: null,
        steps: [
          { id: '1', title: '自评', status: 'finish' },
          { id: '2', title: '上级评分', status: 'finish' },
          { id: '3', title: '部门评分', status: 'process' },
          { id: '4', title: 'HR审核', status: 'wait' }
        ]
      },
      {
        id: '3',
        employeeNo: 'EMP003',
        employeeName: '王五',
        department: '电气工程学院',
        position: '教授',
        period: '2025年度考核',
        scheme: '管理人员考核方案',
        currentStep: 4,
        currentStepName: '已完成',
        selfScore: 95,
        superiorScore: 93,
        finalScore: 94,
        steps: [
          { id: '1', title: '自评', status: 'finish' },
          { id: '2', title: '上级评分', status: 'finish' },
          { id: '3', title: '部门评分', status: 'finish' },
          { id: '4', title: 'HR审核', status: 'finish' }
        ]
      }
    ]
    
    pagination.total = tableData.value.length
  } catch (__error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchList()
}

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    periodId: periodOptions.value[0]?.id || '',
    departmentId: '',
    status: ''
  })
  fetchList()
}

// 批量分配
const handleBatchAssign = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要分配的评分任务')
    return
  }
  assignDialogVisible.value = true
}

// 导出进度
const handleExport = () => {
  ElMessage.success('正在导出评分进度报表...')
}

// 表格选择
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 判断是否可以评分
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const canScore = (row: unknown) => {
  // 根据当前用户角色和评分环节判断
  return row.currentStep < 4
}

// 评分
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleScore = (row: unknown) => {
  currentEmployee.value = row
  scoreType.value = getScoreType(row.currentStep)
  loadScoreForm(row)
  scoreDialogVisible.value = true
}

// 查看
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleView = (row: unknown) => {
  currentEmployee.value = row
  scoreType.value = '查看'
  loadScoreForm(row)
  scoreDialogVisible.value = true
}

// 催办
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleRemind = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要发送催办通知吗？',
      '催办确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('催办通知已发送')
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('催办失败:', error)
    }
  }
}

// 获取评分类型
const getScoreType = (step: number) => {
  const typeMap: Record<number, string> = {
    1: '自评',
    2: '上级',
    3: '部门',
    4: 'HR'
  }
  return typeMap[step] || ''
}

// 加载评分表单
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const loadScoreForm = (employee: unknown) => {
  // 模拟加载KPI指标数据
  scoreForm.kpiScores = [
    {
      id: '1',
      category: '工作业绩',
      name: '教学工作量',
      weight: 30,
      target: 320,
      actual: 0,
      score: 0,
      scoreType: 'ACHIEVEMENT'
    },
    {
      id: '2',
      category: '工作业绩',
      name: '科研成果',
      weight: 20,
      target: 100,
      actual: 0,
      score: 0,
      scoreType: 'DIRECT'
    },
    {
      id: '3',
      category: '工作能力',
      name: '专业能力',
      weight: 25,
      target: 100,
      actual: 0,
      score: 0,
      scoreType: 'DIRECT'
    },
    {
      id: '4',
      category: '工作态度',
      name: '工作责任心',
      weight: 15,
      target: 100,
      actual: 0,
      score: 0,
      scoreType: 'DIRECT'
    },
    {
      id: '5',
      category: '工作态度',
      name: '团队协作',
      weight: 10,
      target: 100,
      actual: 0,
      score: 0,
      scoreType: 'DIRECT'
    }
  ]
  
  scoreForm.comments = ''
  scoreForm.suggestions = ''
  scoreForm.attachments = []
}

// 判断是否可以编辑分数
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const canEditScore = (kpi: unknown) => {
  return kpi.scoreType === 'ACHIEVEMENT'
}

// 计算得分
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const calculateScore = (kpi: unknown) => {
  if (kpi.scoreType === 'ACHIEVEMENT' && kpi.target > 0) {
    kpi.score = Math.min(100, Math.round((kpi.actual / kpi.target) * 100))
  }
}

// 表格汇总方法
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSummaries = (param: unknown) => {
  const {columns: _columns, data: _data} =  param
  const sums: string[] 
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.progress-row {
  margin-bottom: 20px;
}

.progress-card {
  cursor: pointer;
  transition: all 0.3s;
}

.progress-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.progress-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.progress-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.progress-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.progress-icon.pending {
  background: linear-gradient(135deg, #ffd93d 0%, #ffb347 100%);
}

.progress-icon.processing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.progress-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.progress-info {
  flex: 1;
}

.progress-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.progress-label {
  font-size: 14px;
  color: #909399;
}

.progress-bar {
  margin-top: 8px;
}

.table-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.scoring-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-detail {
  font-size: 12px;
  color: #909399;
}

.score-empty {
  color: #c0c4cc;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.score-dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.employee-info {
  margin-bottom: 20px;
}

.score-form {
  margin-top: 20px;
}

.kpi-scoring {
  margin-bottom: 20px;
}

.kpi-scoring h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.comments-item {
  margin-top: 20px;
}

:deep(.el-descriptions__label) {
  width: 100px;
}

:deep(.el-step__title.is-wait) {
  color: #c0c4cc;
}

:deep(.el-step__title.is-process) {
  color: #409eff;
}

:deep(.el-step__title.is-finish) {
  color: #67c23a;
}
</style>