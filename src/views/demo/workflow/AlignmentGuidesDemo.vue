<template>
  <div class="alignment-guides-demo">
    <el-card class="demo-header">
      <template #header>
        <div class="card-header">
          <span>对齐辅助线功能演示</span>
          <el-tag type="success">WF-DESIGNER-011</el-tag>
        </div>
      </template>
      
      <el-alert
        title="功能说明"
        type="info"
        :closable="false"
        show-icon
      >
        对齐辅助线功能可以帮助您在拖拽元素时自动对齐到其他元素，提供视觉辅助线，使流程图更加整齐美观。
      </el-alert>
      
      <el-row :gutter="20" class="feature-list">
        <el-col :span="6">
          <div class="feature-item">
            <el-icon :size="24" color="#409EFF"><Grid /></el-icon>
            <div>智能对齐检测</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="feature-item">
            <el-icon :size="24" color="#67C23A"><Aim /></el-icon>
            <div>实时辅助线显示</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="feature-item">
            <el-icon :size="24" color="#E6A23C"><Magnet /></el-icon>
            <div>磁吸对齐效果</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="feature-item">
            <el-icon :size="24" color="#F56C6C"><Setting /></el-icon>
            <div>可配置阈值</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 控制面板 -->
    <el-card class="control-panel">
      <template #header>
        <span>对齐设置</span>
      </template>
      
      <el-form label-width="120px">
        <el-form-item label="启用对齐">
          <el-switch
            v-model="alignmentEnabled"
            @change="handleAlignmentToggle"
           />
        </el-form-item>
        
        <el-form-item label="显示辅助线">
          <el-switch
            v-model="showGuides"
            @change="handleGuidesToggle"
           />
        </el-form-item>
        
        <el-form-item label="网格吸附">
          <el-switch
            v-model="snapToGrid"
            @change="handleGridToggle"
           />
        </el-form-item>
        
        <el-form-item label="水平阈值">
          <el-slider
            v-model="horizontalThreshold"
            :min="1"
            :max="20"
            :step="1"
            show-input
            @change="updateThreshold"
           />
        </el-form-item>
        
        <el-form-item label="垂直阈值">
          <el-slider
            v-model="verticalThreshold"
            :min="1"
            :max="20"
            :step="1"
            show-input
            @change="updateThreshold"
           />
        </el-form-item>
        
        <el-form-item label="辅助线颜色">
          <el-color-picker
            v-model="guideColor"
            @change="updateGuideStyle"
           />
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 设计器区域 -->
    <el-card class="designer-container">
      <template #header>
        <div class="designer-header">
          <span>流程设计器</span>
          <el-space>
            <el-button type="primary" size="small" @click="addRandomElement">
              <el-icon><Plus /></el-icon>
              添加元素
            </el-button>
            <el-button size="small" @click="clearElements">
              <el-icon><Delete /></el-icon>
              清空画布
            </el-button>
            <el-button size="small" @click="resetView">
              <el-icon><RefreshRight /></el-icon>
              重置视图
            </el-button>
          </el-space>
        </div>
      </template>
      
      <div class="designer-wrapper">
        <BpmnDesigner
          ref="designerRef"
          v-model="processXml"
          :height="600"
          :show-minimap="false"
          @selection-changed="handleSelectionChanged"
        />
      </div>
    </el-card>
    
    <!-- 对齐信息面板 -->
    <el-card class="info-panel">
      <template #header>
        <span>对齐信息</span>
      </template>
      
      <div v-if="alignmentInfo" class="alignment-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前元素">
            {{ alignmentInfo.elementId || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="对齐状态">
            <el-tag :type="alignmentInfo.aligned ? 'success' : 'info'">
              {{ alignmentInfo.aligned ? '已对齐' : '未对齐' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="水平对齐">
            {{ alignmentInfo.horizontalGuides || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="垂直对齐">
            {{ alignmentInfo.verticalGuides || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="对齐元素">
            {{ alignmentInfo.alignedElements || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="吸附偏移">
            X: {{ alignmentInfo.snapDeltaX || 0 }}, Y: {{ alignmentInfo.snapDeltaY || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <el-empty v-else description="拖拽元素查看对齐信息"  />
    </el-card>
    
    <!-- 使用说明 -->
    <el-card class="usage-guide">
      <template #header>
        <span>使用说明</span>
      </template>
      
      <el-timeline>
        <el-timeline-item>
          <h4>基本操作</h4>
          <p>拖拽画布中的元素，当接近其他元素时会自动显示对齐辅助线</p>
        </el-timeline-item>
        
        <el-timeline-item>
          <h4>对齐类型</h4>
          <ul>
            <li>边缘对齐：元素的上下左右边缘对齐</li>
            <li>中心对齐：元素的水平或垂直中心线对齐</li>
            <li>间距对齐：元素之间保持相等间距</li>
          </ul>
        </el-timeline-item>
        
        <el-timeline-item>
          <h4>配置选项</h4>
          <ul>
            <li>对齐阈值：控制多近的距离才会触发对齐</li>
            <li>网格吸附：启用后元素会吸附到网格点</li>
            <li>辅助线样式：自定义辅助线的颜色和样式</li>
          </ul>
        </el-timeline-item>
        
        <el-timeline-item>
          <h4>快捷键</h4>
          <ul>
            <li><el-tag size="small">Alt</el-tag> + 拖拽：临时禁用对齐</li>
            <li><el-tag size="small">Shift</el-tag> + 拖拽：只允许水平或垂直移动</li>
          </ul>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AlignmentGuidesDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Grid,
  Aim,
  Magnet,
  Setting,
  Plus,
  Delete,
  RefreshRight
} from '@element-plus/icons-vue'
import HrBpmnDesigner from '@/workflow/components/designer/HrBpmnDesigner.vue'

// 状态
const designerRef = ref()
const processXml = ref('')
const alignmentEnabled = ref(true)
const showGuides = ref(true)
const snapToGrid = ref(false)
const horizontalThreshold = ref(5)
const verticalThreshold = ref(5)
const guideColor = ref('#409EFF')
const alignmentInfo = ref<unknown>(null)

// 初始化流程
const initProcess = () => {
  processXml.value = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:userTask id="Task_1" name="任务1">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:userTask id="Task_2" name="任务2">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_1" name="条件判断">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <bpmn:userTask id="Task_3" name="任务3">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:userTask id="Task_4" name="任务4">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:incoming>Flow_7</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_1" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_1" targetRef="Task_2" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_2" targetRef="Gateway_1" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Gateway_1" targetRef="Task_3" name="条件1" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Gateway_1" targetRef="Task_4" name="条件2" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_3" targetRef="EndEvent_1" />
    <bpmn:sequenceFlow id="Flow_7" sourceRef="Task_4" targetRef="EndEvent_1" />
  </bpmn:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_1" bpmnElement="StartEvent_1">
        <dc:Bounds x="180" y="180" width="36" height="36" />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="_BPMNShape_Task_1" bpmnElement="Task_1">
        <dc:Bounds x="280" y="158" width="100" height="80" />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="_BPMNShape_Task_2" bpmnElement="Task_2">
        <dc:Bounds x="450" y="158" width="100" height="80" />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="_BPMNShape_Gateway_1" bpmnElement="Gateway_1" isMarkerVisible="true">
        <dc:Bounds x="620" y="173" width="50" height="50" />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="_BPMNShape_Task_3" bpmnElement="Task_3">
        <dc:Bounds x="740" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="_BPMNShape_Task_4" bpmnElement="Task_4">
        <dc:Bounds x="740" y="250" width="100" height="80" />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="_BPMNShape_EndEvent_1" bpmnElement="EndEvent_1">
        <dc:Bounds x="920" y="180" width="36" height="36" />
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNEdge id="_BPMNEdge_Flow_1" bpmnElement="Flow_1">
        <di:waypoint x="216" y="198" />
        <di:waypoint x="280" y="198" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="_BPMNEdge_Flow_2" bpmnElement="Flow_2">
        <di:waypoint x="380" y="198" />
        <di:waypoint x="450" y="198" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="_BPMNEdge_Flow_3" bpmnElement="Flow_3">
        <di:waypoint x="550" y="198" />
        <di:waypoint x="620" y="198" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="_BPMNEdge_Flow_4" bpmnElement="Flow_4">
        <di:waypoint x="645" y="173" />
        <di:waypoint x="645" y="120" />
        <di:waypoint x="740" y="120" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="_BPMNEdge_Flow_5" bpmnElement="Flow_5">
        <di:waypoint x="645" y="223" />
        <di:waypoint x="645" y="290" />
        <di:waypoint x="740" y="290" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="_BPMNEdge_Flow_6" bpmnElement="Flow_6">
        <di:waypoint x="840" y="120" />
        <di:waypoint x="938" y="120" />
        <di:waypoint x="938" y="180" />
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="_BPMNEdge_Flow_7" bpmnElement="Flow_7">
        <di:waypoint x="840" y="290" />
        <di:waypoint x="938" y="290" />
        <di:waypoint x="938" y="216" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`
}

// 处理对齐开关
const handleAlignmentToggle = (value: boolean) => {
  const modeler = designerRef.value?.getModeler()
  if (!modeler) return
  
  // 获取对齐管理器并切换状态
  // 实际实现中需要通过 modeler 访问 alignmentGuides
  ElMessage.success(value ? '对齐功能已启用' : '对齐功能已禁用')
}

// 处理辅助线显示开关
const handleGuidesToggle = (value: boolean) => {
  const modeler = designerRef.value?.getModeler()
  if (!modeler) return
  
  // 更新辅助线显示配置
  ElMessage.success(value ? '辅助线显示已启用' : '辅助线显示已禁用')
}

// 处理网格吸附开关
const handleGridToggle = (value: boolean) => {
  const modeler = designerRef.value?.getModeler()
  if (!modeler) return
  
  // 更新网格吸附配置
  ElMessage.success(value ? '网格吸附已启用' : '网格吸附已禁用')
}

// 更新阈值
const updateThreshold = () => {
  const modeler = designerRef.value?.getModeler()
  if (!modeler) return
  
  // 更新对齐阈值配置
  ElMessage.success('对齐阈值已更新')
}

// 更新辅助线样式
const updateGuideStyle = () => {
  const modeler = designerRef.value?.getModeler()
  if (!modeler) return
  
  // 更新辅助线颜色配置
  ElMessage.success('辅助线样式已更新')
}

// 添加随机元素
const addRandomElement = () => {
  const modeler = designerRef.value?.getModeler()
  if (!modeler) return
  
  // 模拟添加元素
  const types = ['bpmn:UserTask', 'bpmn:ServiceTask', 'bpmn:ExclusiveGateway']
  const type = types[Math.floor(Math.random() * types.length)]
  const x = 200 + Math.random() * 600
  const y = 100 + Math.random() * 300
  
  ElMessage.success(`已添加新元素到位置 (${Math.round(x)}, ${Math.round(y)})`)
}

// 清空画布
const clearElements = () => {
  initProcess()
  ElMessage.success('画布已清空')
}

// 重置视图
const resetView = () => {
  const modeler = designerRef.value?.getModeler()
  if (!modeler) return
  
  // 重置视图到适合大小
  ElMessage.success('视图已重置')
}

// 处理选择变化
   
const handleSelectionChanged = (element: unknown) => {
  if (element) {
    // 模拟对齐信息
    alignmentInfo.value = {
      elementId: element.id,
      aligned: Math.random() > 0.5,
      horizontalGuides: Math.random() > 0.5 ? '顶部对齐' : '无',
      verticalGuides: Math.random() > 0.5 ? '左侧对齐' : '无',
      alignedElements: Math.random() > 0.5 ? 'Task_1, Task_2' : '无',
      snapDeltaX: Math.floor(Math.random() * 10 - 5),
      snapDeltaY: Math.floor(Math.random() * 10 - 5)
    }
  } else {
    alignmentInfo.value = null
  }
}

// 生命周期
onMounted(() => {
  initProcess()
})
</script>

<style lang="scss" scoped>
.alignment-guides-demo {
  padding: 20px;
  
  .demo-header {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .feature-list {
      margin-top: 20px;
      
      .feature-item {
        text-align: center;
        padding: 20px;
        background: #f5f7fa;
        border-radius: 4px;
        
        .el-icon {
          margin-bottom: 10px;
        }
      }
    }
  }
  
  .control-panel {
    margin-bottom: 20px;
    
    .el-form {
      max-width: 600px;
    }
  }
  
  .designer-container {
    margin-bottom: 20px;
    
    .designer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .designer-wrapper {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      overflow: hidden;
    }
  }
  
  .info-panel {
    margin-bottom: 20px;
    
    .alignment-info {
      .el-descriptions {
        margin-top: 10px;
      }
    }
  }
  
  .usage-guide {
    .el-timeline {
      padding: 0;
      
      h4 {
        margin: 0 0 10px 0;
        color: #303133;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        color: #606266;
        
        li {
          margin-bottom: 5px;
        }
      }
    }
  }
}
</style>