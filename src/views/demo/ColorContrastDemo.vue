<template>
  <div class="color-contrast-demo">
    <el-card class="demo-header">
      <h1>颜色对比度检查工具</h1>
      <p>基于WCAG 2.1标准的颜色对比度检查和优化工具，确保界面的可访问性。</p>
    </el-card>

    <!-- 实时对比度检查器 -->
    <el-card class="demo-section">
      <template #header>
        <span>实时对比度检查器</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="color-picker-group">
            <h3>前景色（文字）</h3>
            <el-color-picker
              v-model="foregroundColor"
              show-alpha
              @change="checkCurrentContrast"
             />
            <el-input
              v-model="foregroundColor"
              placeholder="#000000"
              style="width: 120px; margin-left: 10px;"
              @input="checkCurrentContrast"
              />
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="color-picker-group">
            <h3>背景色</h3>
            <el-color-picker
              v-model="backgroundColor"
              show-alpha
              @change="checkCurrentContrast"
             />
            <el-input
              v-model="backgroundColor"
              placeholder="#FFFFFF"
              style="width: 120px; margin-left: 10px;"
              @input="checkCurrentContrast"
              />
          </div>
        </el-col>
      </el-row>
      
      <div class="preview-area" :style="{ backgroundColor, color: foregroundColor }">
        <h2>预览文本 Preview Text</h2>
        <p>这是普通文本的预览效果。The quick brown fox jumps over the lazy dog.</p>
        <p style="font-size: 18px; font-weight: bold;">这是大文本的预览效果（18px粗体）</p>
        <p style="font-size: 14px; font-weight: bold;">这是14px粗体文本的预览效果</p>
      </div>
      
      <div class="contrast-result" v-if="currentResult">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="result-item">
              <div class="result-label">对比度</div>
              <div class="result-value" :class="{ 'pass': currentResult.passAA, 'fail': !currentResult.passAA }">
                {{ currentResult.ratio }}:1
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="result-item">
              <div class="result-label">AA标准</div>
              <div class="result-status">
                <el-icon v-if="currentResult.passAA" style="color: #67c23a;"><CircleCheck /></el-icon>
                <el-icon v-else style="color: #f56c6c;"><CircleClose /></el-icon>
                <span>{{ currentResult.passAA ? '通过' : '未通过' }}</span>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="result-item">
              <div class="result-label">AAA标准</div>
              <div class="result-status">
                <el-icon v-if="currentResult.passAAA" style="color: #67c23a;"><CircleCheck /></el-icon>
                <el-icon v-else style="color: #f56c6c;"><CircleClose /></el-icon>
                <span>{{ currentResult.passAAA ? '通过' : '未通过' }}</span>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="result-item" v-if="currentResult.suggestedColor">
              <div class="result-label">建议颜色</div>
              <div class="suggested-color">
                <div 
                  class="color-sample" 
                  :style="{ backgroundColor: currentResult.suggestedColor }"
                ></div>
                <span>{{ currentResult.suggestedColor }}</span>
                <el-button 
                  size="small" 
                  @click="applySuggestedColor"
                  style="margin-left: 10px;"
                >
                  应用
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <el-divider   />
        
        <div class="detail-results">
          <h4>详细结果</h4>
          <el-table :data="detailTableData" stripe size="small">
            <el-table-column prop="type" label="文本类型" width="150"  />
            <el-table-column prop="aa" label="AA标准" width="150">
              <template #default="{ row }">
                <el-icon v-if="row.aa" style="color: #67c23a;"><CircleCheck /></el-icon>
                <el-icon v-else style="color: #f56c6c;"><CircleClose /></el-icon>
                {{ row.aaText }}
              </template>
            </el-table-column>
            <el-table-column prop="aaa" label="AAA标准" width="150">
              <template #default="{ row }">
                <el-icon v-if="row.aaa" style="color: #67c23a;"><CircleCheck /></el-icon>
                <el-icon v-else style="color: #f56c6c;"><CircleClose /></el-icon>
                {{ row.aaaText }}
              </template>
            </el-table-column>
            <el-table-column prop="usage" label="适用场景"  />
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 系统颜色检查 -->
    <el-card class="demo-section">
      <template #header>
        <div class="card-header">
          <span>系统颜色对比度检查</span>
          <el-button @click="checkSystemColors" type="primary" size="small">检查所有颜色</el-button>
        </div>
      </template>
      
      <div v-if="systemColorResults.length > 0">
        <el-table :data="systemColorResults" stripe>
          <el-table-column prop="name" label="颜色名称" width="200"  />
          <el-table-column label="预览" width="200">
            <template #default="{ row }">
              <div class="color-preview" :style="{ backgroundColor: row.background }">
                <span :style="{ color: row.foreground }">示例文本</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ratio" label="对比度" width="100">
            <template #default="{ row }">
              <span :class="{ 'text-success': row.result.passAA, 'text-danger': !row.result.passAA }">
                {{ row.result.ratio }}:1
              </span>
            </template>
          </el-table-column>
          <el-table-column label="AA" width="80">
            <template #default="{ row }">
              <el-icon v-if="row.result.passAA" style="color: #67c23a;"><CircleCheck /></el-icon>
              <el-icon v-else style="color: #f56c6c;"><CircleClose /></el-icon>
            </template>
          </el-table-column>
          <el-table-column label="AAA" width="80">
            <template #default="{ row }">
              <el-icon v-if="row.result.passAAA" style="color: #67c23a;"><CircleCheck /></el-icon>
              <el-icon v-else style="color: #f56c6c;"><CircleClose /></el-icon>
            </template>
          </el-table-column>
          <el-table-column prop="suggestion" label="建议"  />
        </el-table>
        
        <div class="summary-stats">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="总检查项" :value="systemColorResults.length"  />
            </el-col>
            <el-col :span="8">
              <el-statistic 
                title="通过AA标准" 
                :value="systemColorResults.filter(r => r.result.passAA).length"
                :suffix="`/ ${systemColorResults.length}`"
              />
            </el-col>
            <el-col :span="8">
              <el-statistic 
                title="通过AAA标准" 
                :value="systemColorResults.filter(r => r.result.passAAA).length"
                :suffix="`/ ${systemColorResults.length}`"
              />
            </el-col>
          </el-row>
        </div>
      </div>
      
      <el-empty v-else description="点击「检查所有颜色」按钮开始检查"  />
    </el-card>

    <!-- WCAG标准说明 -->
    <el-card class="demo-section">
      <template #header>
        <span>WCAG 2.1 对比度标准</span>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="标准说明" name="standards">
          <div class="standards-content">
            <h3>文本对比度要求</h3>
            <el-table :data="wcagStandards" stripe>
              <el-table-column prop="level" label="级别" width="100"  />
              <el-table-column prop="normalText" label="普通文本" width="150"  />
              <el-table-column prop="largeText" label="大文本" width="150"  />
              <el-table-column prop="description" label="说明"  />
            </el-table>
            
            <h3 style="margin-top: 20px;">文本大小定义</h3>
            <ul>
              <li><strong>普通文本</strong>：小于18pt（24px）的常规字重文本，或小于14pt（18.66px）的粗体文本</li>
              <li><strong>大文本</strong>：至少18pt（24px）的常规字重文本，或至少14pt（18.66px）的粗体文本</li>
            </ul>
            
            <h3 style="margin-top: 20px;">特殊情况</h3>
            <ul>
              <li><strong>装饰性文本</strong>：纯装饰用途的文本不需要满足对比度要求</li>
              <li><strong>禁用控件</strong>：禁用状态的控件文本不需要满足对比度要求</li>
              <li><strong>Logo和品牌</strong>：Logo中的文本不需要满足对比度要求</li>
            </ul>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="最佳实践" name="practices">
          <div class="practices-content">
            <h3>设计建议</h3>
            <ol>
              <li>
                <strong>优先考虑AA标准</strong>
                <p>对于大多数网站，满足AA标准（4.5:1）就足够了。AAA标准（7:1）主要用于特殊需求场景。</p>
              </li>
              <li>
                <strong>测试真实场景</strong>
                <p>在不同的显示器、光照条件下测试颜色对比度，确保在各种环境下都能良好显示。</p>
              </li>
              <li>
                <strong>避免仅依赖颜色</strong>
                <p>不要仅通过颜色来传达信息，应该配合图标、文字等其他视觉元素。</p>
              </li>
              <li>
                <strong>提供主题切换</strong>
                <p>提供高对比度主题选项，让用户可以根据需要切换。</p>
              </li>
            </ol>
            
            <h3>常见错误</h3>
            <el-alert type="warning" :closable="false">
              <ul style="margin: 0; padding-left: 20px;">
                <li>浅灰色文字配白色背景（对比度过低）</li>
                <li>蓝色链接配蓝色背景（颜色过于接近）</li>
                <li>仅通过颜色区分状态（色盲用户无法识别）</li>
                <li>忽略hover/focus状态的对比度</li>
              </ul>
            </el-alert>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="工具推荐" name="tools">
          <div class="tools-content">
            <h3>在线工具</h3>
            <ul>
              <li><a href="https://webaim.org/resources/contrastchecker/" target="_blank">WebAIM Contrast Checker</a></li>
              <li><a href="https://contrast-ratio.com/" target="_blank">Contrast Ratio</a></li>
              <li><a href="https://www.adobe.com/accessibility/color-contrast.html" target="_blank">Adobe Color Contrast Analyzer</a></li>
            </ul>
            
            <h3>浏览器插件</h3>
            <ul>
              <li>WAVE (WebAIM)</li>
              <li>axe DevTools</li>
              <li>Lighthouse (Chrome内置)</li>
            </ul>
            
            <h3>设计工具插件</h3>
            <ul>
              <li>Stark (Sketch, Figma)</li>
              <li>Able (Figma)</li>
              <li>Color Oracle (系统级色盲模拟器)</li>
            </ul>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, onMounted } from 'vue'
import { CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { 
  checkContrast, 
  checkElementPlusThemeContrast,
  type ContrastResult 
} from '@/utils/color-contrast'

// 状态
const foregroundColor = ref('#606266')
const backgroundColor = ref('#FFFFFF')
const currentResult = ref<ContrastResult | null>(null)
const systemColorResults = ref<any[]>([])
const activeTab = ref('standards')

// 系统默认颜色配置
const systemColors = [
  { name: 'HrHr主要文本', foreground: '#303133', background: '#FFFFFF' },
  { name: '常规文本', foreground: '#606266', background: '#FFFFFF' },
  { name: '次要文本', foreground: '#909399', background: '#FFFFFF' },
  { name: '占位文本', foreground: '#A8ABB2', background: '#FFFFFF' },
  { name: '禁用文本', foreground: '#C0C4CC', background: '#FFFFFF' },
  { name: '主色按钮', foreground: '#FFFFFF', background: '#409EFF' },
  { name: '成功按钮', foreground: '#FFFFFF', background: '#529B2E' },
  { name: '警告按钮', foreground: '#FFFFFF', background: '#B88230' },
  { name: '危险按钮', foreground: '#FFFFFF', background: '#C45656' },
  { name: '信息按钮', foreground: '#FFFFFF', background: '#73767A' },
  { name: '主色链接', foreground: '#2B7FD9', background: '#FFFFFF' },
  { name: '成功文本', foreground: '#529B2E', background: '#FFFFFF' },
  { name: '警告文本', foreground: '#B88230', background: '#FFFFFF' },
  { name: '危险文本', foreground: '#C45656', background: '#FFFFFF' },
  { name: '信息文本', foreground: '#73767A', background: '#FFFFFF' }
]

// WCAG标准数据
const wcagStandards = [
  {
    level: 'AA',
    normalText: '4.5:1',
    largeText: '3:1',
    description: '最低合规标准，适用于大多数网站'
  },
  {
    level: 'AAA',
    normalText: '7:1',
    largeText: '4.5:1',
    description: '增强标准，提供最佳可读性'
  }
]

// 计算详细结果表格数据
const detailTableData = computed(() => {
  if (!currentResult.value) return []
  
  return [
    {
      type: '普通文本',
      aa: currentResult.value.details.normalTextAA,
      aaText: currentResult.value.details.normalTextAA ? '通过 (≥4.5:1)' : '未通过 (<4.5:1)',
      aaa: currentResult.value.details.normalTextAAA,
      aaaText: currentResult.value.details.normalTextAAA ? '通过 (≥7:1)' : '未通过 (<7:1)',
      usage: '正文、标签、说明文字等'
    },
    {
      type: '大文本',
      aa: currentResult.value.details.largeTextAA,
      aaText: currentResult.value.details.largeTextAA ? '通过 (≥3:1)' : '未通过 (<3:1)',
      aaa: currentResult.value.details.largeTextAAA,
      aaaText: currentResult.value.details.largeTextAAA ? '通过 (≥4.5:1)' : '未通过 (<4.5:1)',
      usage: '标题、大号字体、粗体文字等'
    }
  ]
})

// 方法
const checkCurrentContrast = () => {
  if (foregroundColor.value && backgroundColor.value) {
    currentResult.value = checkContrast(foregroundColor.value, backgroundColor.value)
  }
}

const applySuggestedColor = () => {
  if (currentResult.value?.suggestedColor) {
    foregroundColor.value = currentResult.value.suggestedColor
    checkCurrentContrast()
  }
}

const checkSystemColors = () => {
  systemColorResults.value = systemColors.map(color => {
    const result = checkContrast(color.foreground, color.background)
    let suggestion = ''
    
    if (!result.passAA) {
      suggestion = `建议使用 ${result.suggestedColor} 作为前景色`
    } else if (!result.passAAA) {
      suggestion = '满足AA标准，如需AAA标准可进一步优化'
    } else {
      suggestion = '完全符合WCAG标准'
    }
    
    return {
      ...color,
      result,
      suggestion
    }
  })
}

// 生命周期
onMounted(() => {
  checkCurrentContrast()
})
</script>

<style scoped lang="scss">
.color-contrast-demo {
  padding: 20px;
  
  .demo-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 10px;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .demo-section {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  
  .color-picker-group {
    h3 {
      margin: 0 0 10px;
      font-size: 16px;
    }
  }
  
  .preview-area {
    margin: 20px 0;
    padding: 30px;
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    min-height: 200px;
    
    h2 {
      margin: 0 0 15px;
    }
    
    p {
      margin: 10px 0;
      line-height: 1.6;
    }
  }
  
  .contrast-result {
    .result-item {
      text-align: center;
      
      .result-label {
        font-size: 12px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .result-value {
        font-size: 24px;
        font-weight: bold;
        
        &.pass {
          color: #67c23a;
        }
        
        &.fail {
          color: #f56c6c;
        }
      }
      
      .result-status {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        font-size: 14px;
      }
      
      .suggested-color {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        
        .color-sample {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
        }
      }
    }
  }
  
  .detail-results {
    h4 {
      margin: 20px 0 10px;
      font-size: 16px;
    }
  }
  
  .color-preview {
    padding: 8px 12px;
    border-radius: 4px;
    text-align: center;
  }
  
  .summary-stats {
    margin-top: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .standards-content,
  .practices-content,
  .tools-content {
    line-height: 1.8;
    
    h3 {
      margin: 20px 0 10px;
      font-size: 16px;
      color: #303133;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    ul, ol {
      margin: 10px 0;
      padding-left: 30px;
      
      li {
        margin-bottom: 8px;
        
        strong {
          color: #303133;
        }
        
        p {
          margin: 5px 0 0;
          color: #606266;
        }
      }
    }
    
    a {
      color: #2b7fd9;
      text-decoration: none;
      
      &:hover {
        color: #409eff;
        text-decoration: underline;
      }
    }
  }
}

// 全局文本颜色类
.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}
</style>