<template>
  <div class="cache-strategy-demo">
    <h1>缓存策略优化演示</h1>
    
    <!-- 缓存统计 -->
    <el-card class="stats-card">
      <template #header>
        <div class="card-header">
          <span>缓存统计</span>
          <el-button-group>
            <el-button @click="refreshStats" :icon="Refresh"   />
            <el-button @click="clearAllCache" :icon="Delete"   />
          </el-button-group>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <el-statistic title="命中次数" :value="stats.hits"  />
        </el-col>
        <el-col :xs="12" :sm="6">
          <el-statistic title="未命中" :value="stats.misses"  />
        </el-col>
        <el-col :xs="12" :sm="6">
          <el-statistic title="命中率" :value="stats.hitRate" :precision="2" suffix="%">
            <template #suffix>
              <span style="font-size: 14px">%</span>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="6">
          <el-statistic title="缓存项数" :value="stats.size"  />
        </el-col>
      </el-row>
      
      <div class="memory-usage">
        <div class="usage-label">内存使用: {{ formatSize(stats.memoryUsage) }}</div>
        <el-progress :percentage="memoryPercentage" :stroke-width="10"  />
      </div>
    </el-card>
    
    <!-- API 缓存测试 -->
    <el-card class="demo-card">
      <template #header>
        <span>API 缓存测试</span>
      </template>
      
      <el-space direction="vertical" fill style="width: 100%">
        <!-- 员工查询测试 -->
        <div class="test-item">
          <h3>员工询类 API 缓存</h3>
          <el-space wrap>
            <el-button @click="testEmployeeDetail" :loading="loading.employee">
              获取员工详情
            </el-button>
            <el-button @click="testEmployeeList" :loading="loading.list">
              获取员工列表
            </el-button>
            <el-button @click="testEmployeeCount" :loading="loading.count">
              统计员工数量
            </el-button>
          </el-space>
          <div v-if="apiResults.employee" class="result-display">
            <el-tag type="success">耗时: {{ apiResults.employee.duration }}ms</el-tag>
            <el-tag :type="apiResults.employee.cached ? 'warning' : 'info'">
              {{ apiResults.employee.cached ? '缓存命中' : '实际请求' }}
            </el-tag>
          </div>
        </div>
        
        <!-- 批量请求测试 -->
        <div class="test-item">
          <h3>批量请求合并</h3>
          <el-space wrap>
            <el-button @click="testBatchRequests" :loading="loading.batch">
              发起 10 个并发请求
            </el-button>
            <el-button @click="testSequentialRequests" :loading="loading.sequential">
              发起 10 个顺序请求
            </el-button>
          </el-space>
          <div v-if="batchResults.length > 0" class="batch-results">
            <el-table :data="batchResults" size="small" max-height="200">
              <el-table-column prop="index" label="请求#" width="80"  />
              <el-table-column prop="duration" label="耗时(ms)" width="100"  />
              <el-table-column prop="cached" label="状态">
                <template #default="{ row }">
                  <el-tag :type="row.cached ? 'warning' : 'info'" size="small">
                    {{ row.cached ? '缓存' : '请求' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
            <div class="batch-summary">
              总耗时: {{ totalDuration }}ms | 
              平均: {{ avgDuration }}ms
            </div>
          </div>
        </div>
        
        <!-- 缓存失效测试 -->
        <div class="test-item">
          <h3>缓存失效策略</h3>
          <el-space wrap>
            <el-button type="primary" @click="createEmployee">
              创建员工（触发失效）
            </el-button>
            <el-button type="warning" @click="updateEmployee">
              更新员工（触发失效）
            </el-button>
            <el-button type="danger" @click="deleteEmployee">
              删除员工（触发失效）
            </el-button>
          </el-space>
        </div>
      </el-space>
    </el-card>
    
    <!-- 缓存类型对比 -->
    <el-card class="demo-card">
      <template #header>
        <span>缓存类型对比</span>
      </template>
      
      <el-table :data="cacheTypes" style="width: 100%">
        <el-table-column prop="type" label="缓存类型" width="150"  />
        <el-table-column prop="storage" label="存储位置" width="150"  />
        <el-table-column prop="ttl" label="默认TTL" width="100"  />
        <el-table-column prop="size" label="当前大小" width="100">
          <template #default="{ row }">
            {{ row.size || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="testCacheType(row)">测试</el-button>
            <el-button size="small" type="danger" @click="clearCacheType(row)">清空</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="适用场景"  />
      </el-table>
    </el-card>
    
    <!-- LRU 缓存演示 -->
    <el-card class="demo-card">
      <template #header>
        <span>LRU 缓存策略演示</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="lru-controls">
            <el-input-number v-model="lruMaxSize" :min="3" :max="10"   />
            <el-button @click="initLRU" type="primary">初始化 LRU</el-button>
          </div>
          
          <el-space direction="vertical" fill>
            <el-input 
              v-model="lruKey" 
              placeholder="输入键"
              @keyup.enter="addToLRU"
            >
              <template #append>
                <el-button @click="addToLRU">添加</el-button>
              </template>
            </el-input>
            
            <el-input 
              v-model="lruGetKey" 
              placeholder="获取键"
              @keyup.enter="getFromLRU"
            >
              <template #append>
                <el-button @click="getFromLRU">获取</el-button>
              </template>
            </el-input>
          </el-space>
        </el-col>
        
        <el-col :span="12">
          <div class="lru-visualization">
            <h4>LRU 缓存状态（最大容量: {{ lruMaxSize }}）</h4>
            <div class="lru-items">
              <div 
                v-for="(item, index) in lruItems" 
                :key="item.key"
                class="lru-item"
                :class="{ 'most-recent': index === lruItems.length - 1 }"
              >
                <span class="key">{{ item.key }}</span>
                <span class="value">{{ item.value }}</span>
                <el-tag size="small" :type="index === lruItems.length - 1 ? 'success' : 'info'">
                  {{ index === lruItems.length - 1 ? '最近' : index + 1 }}
                </el-tag>
              </div>
              <el-empty v-if="lruItems.length === 0" description="缓存为空" :image-size="60"  />
            </div>
            <div v-if="lruGetResult" class="lru-result">
              获取结果: {{ lruGetResult }}
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'CacheStrategyDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Delete } from '@element-plus/icons-vue'
import { employeeApi } from '@/api/employee'
import { memoryCache, localCache, sessionCache, cacheInvalidator, LRUCache } from '@/utils/cache'
import type { CacheManager } from '@/utils/cache'

// 缓存统计
const stats = ref({
  hits: 0,
  misses: 0,
  sets: 0,
  deletes: 0,
  evictions: 0,
  size: 0,
  memoryUsage: 0,
  hitRate: 0
})

// 加载状态
const loading = reactive({
  employee: false,
  list: false,
  count: false,
  batch: false,
  sequential: false
})

// API 结果
const apiResults = reactive<unknown>({
  employee: null
})

// 批量请求结果
const batchResults = ref<any[]>([])

// 缓存类型列表
const cacheTypes = ref([
  {
    type: 'Memory Cache',
    storage: '内存',
    ttl: '5分钟',
    size: 0,
    description: '高频访问数据，如用户信息、权限数据',
    instance: memoryCache
  },
  {
    type: 'Local Cache',
    storage: 'LocalStorage',
    ttl: '24小时',
    size: 0,
    description: '持久化数据，如配置信息、字典数据',
    instance: localCache
  },
  {
    type: 'Session Cache',
    storage: 'SessionStorage',
    ttl: '30分钟',
    size: 0,
    description: '会话级数据，如查询条件、临时状态',
    instance: sessionCache
  }
])

// LRU 缓存演示
const lruMaxSize = ref(5)
const lruKey = ref('')
const lruGetKey = ref('')
const lruGetResult = ref('')
const lruItems = ref<any[]>([])
let lruCache: LRUCache<string> | null = null

// 计算属性
const memoryPercentage = computed(() => {
  const maxMemory = 50 * 1024 * 1024 // 50MB
  return Math.min((stats.value.memoryUsage / maxMemory) * 100, 100)
})

const totalDuration = computed(() => {
  return batchResults.value.reduce((sum, item) => sum + item.duration, 0)
})

const avgDuration = computed(() => {
  if (batchResults.value.length === 0) return 0
  return Math.round(totalDuration.value / batchResults.value.length)
})

// 刷新统计
const refreshStats = async () => {
  stats.value = memoryCache.getStats()
  stats.value.hitRate = stats.value.hitRate * 100
  
  // 更新各缓存大小
  cacheTypes.value[0].size = await memoryCache.size()
  cacheTypes.value[1].size = await localCache.size()
  cacheTypes.value[2].size = await sessionCache.size()
}

// 清空所有缓存
const clearAllCache = () => {
  cacheInvalidator.invalidateAll()
  refreshStats()
}

// 测试员工详情 API
const testEmployeeDetail = async () => {
  loading.employee = true
  const startTime = Date.now()
  
  try {
    // 第一次请求
    await employeeApi.getEmployeeById(1)
    const firstDuration = Date.now() - startTime
    
    // 第二次请求（应该命中缓存）
    const secondStart = Date.now()
    await employeeApi.getEmployeeById(1)
    const secondDuration = Date.now() - secondStart
    
    apiResults.employee = {
      duration: secondDuration,
      cached: secondDuration < 10 // 如果小于10ms，认为是缓存命中
    }
    
    ElMessage.success(`第一次: ${firstDuration}ms, 第二次: ${secondDuration}ms`)
  } catch (__error) {
    console.error(error)
  } finally {
    loading.employee = false
    refreshStats()
  }
}

// 测试员工列表 API
const testEmployeeList = async () => {
  loading.list = true
  
  try {
    const startTime = Date.now()
    await employeeApi.queryEmployees({ page: 1, size: 10 })
    const duration = Date.now() - startTime
    
    ElMessage.success(`列表查询耗时: ${duration}ms`)
  } catch (__error) {
    console.error(error)
  } finally {
    loading.list = false
    refreshStats()
  }
}

// 测试员工统计 API
const testEmployeeCount = async () => {
  loading.count = true
  
  try {
    const startTime = Date.now()
    await employeeApi.countEmployees()
    const duration = Date.now() - startTime
    
    ElMessage.success(`统计查询耗时: ${duration}ms`)
  } catch (__error) {
    console.error(error)
  } finally {
    loading.count = false
    refreshStats()
  }
}

// 测试批量请求
const testBatchRequests = async () => {
  loading.batch = true
  batchResults.value = []
  
  try {
    const promises = Array.from({ length: 10 }, (_, i) => {
      const startTime = Date.now()
      return employeeApi.getEmployeeById(i + 1).then(() => {
        const duration = Date.now() - startTime
        batchResults.value.push({
          index: i + 1,
          duration,
          cached: duration < 10
        })
      })
    })
    
    await Promise.all(promises)
    ElMessage.success('批量请求完成')
  } catch (__error) {
    console.error(error)
  } finally {
    loading.batch = false
    refreshStats()
  }
}

// 测试顺序请求
const testSequentialRequests = async () => {
  loading.sequential = true
  batchResults.value = []
  
  try {
    for (let i = 0; i < 10; i++) {
      const startTime = Date.now()
      await employeeApi.getEmployeeById(i + 1)
      const duration = Date.now() - startTime
      
      batchResults.value.push({
        index: i + 1,
        duration,
        cached: duration < 10
      })
    }
    
    ElMessage.success('顺序请求完成')
  } catch (__error) {
    console.error(error)
  } finally {
    loading.sequential = false
    refreshStats()
  }
}

// 创建员工（触发缓存失效）
const createEmployee = async () => {
  try {
    // 模拟创建操作
    await new Promise(resolve => setTimeout(resolve, 500))
    cacheInvalidator.invalidateByEntity('employee', 'create')
    ElMessage.success('创建成功，相关缓存已失效')
    refreshStats()
  } catch (__error) {
    console.error(error)
  }
}

// 更新员工
const updateEmployee = async () => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    cacheInvalidator.invalidateByEntity('employee', 'update')
    ElMessage.success('更新成功，相关缓存已失效')
    refreshStats()
  } catch (__error) {
    console.error(error)
  }
}

// 删除员工
const deleteEmployee = async () => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    cacheInvalidator.invalidateByEntity('employee', 'delete')
    ElMessage.success('删除成功，相关缓存已失效')
    refreshStats()
  } catch (__error) {
    console.error(error)
  }
}

// 测试缓存类型
   
const testCacheType = async (row: unknown) => {
  const testKey = `test_${Date.now()}`
  const testValue = { data: 'test data', timestamp: Date.now() }
  
  // 设置缓存
  await row.instance.set(testKey, testValue)
  
  // 获取缓存
  const cached = await row.instance.get(testKey)
  
  if (cached) {
    ElMessage.success(`${row.type} 测试成功`)
  } else {
    ElMessage.error(`${row.type} 测试失败`)
  }
  
  refreshStats()
}

// 清空缓存类型
   
const clearCacheType = async (row: unknown) => {
  await row.instance.clear()
  ElMessage.success(`${row.type} 已清空`)
  refreshStats()
}

// 初始化 LRU
const initLRU = () => {
  lruCache = new LRUCache<string>(lruMaxSize.value)
  lruItems.value = []
  lruGetResult.value = ''
  ElMessage.success(`LRU 缓存已初始化，容量: ${lruMaxSize.value}`)
}

// 添加到 LRU
const addToLRU = () => {
  if (!lruCache) {
    ElMessage.warning('请先初始化 LRU 缓存')
    return
  }
  
  if (!lruKey.value) {
    ElMessage.warning('请输入键')
    return
  }
  
  const value = `value_${Date.now()}`
  lruCache.set(lruKey.value, value)
  updateLRUVisualization()
  
  lruKey.value = ''
  ElMessage.success('已添加到 LRU 缓存')
}

// 从 LRU 获取
const getFromLRU = () => {
  if (!lruCache) {
    ElMessage.warning('请先初始化 LRU 缓存')
    return
  }
  
  if (!lruGetKey.value) {
    ElMessage.warning('请输入键')
    return
  }
  
  const value = lruCache.get(lruGetKey.value)
  lruGetResult.value = value ? `获取成功: ${value}` : '未找到'
  
  if (value) {
    updateLRUVisualization()
  }
  
  lruGetKey.value = ''
}

// 更新 LRU 可视化
const updateLRUVisualization = () => {
  if (!lruCache) return
  
  // 获取 LRU 内部数据（通过反射）
  const cache = (lruCache as unknown).cache as Map<string, string>
  lruItems.value = Array.from(cache.entries()).map(([key, value]) => ({ key, value }))
}

// 格式化大小
const formatSize = (bytes: number) => {
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(2)} MB`
}

// 挂载时初始化
onMounted(() => {
  refreshStats()
  initLRU()
  
  // 定时刷新统计
  setInterval(refreshStats, 5000)
})
</script>

<style scoped lang="scss">
.cache-strategy-demo {
  padding: 20px;
  
  h1 {
    text-align: center;
    margin-bottom: 30px;
  }
}

.stats-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .memory-usage {
    margin-top: 20px;
    
    .usage-label {
      margin-bottom: 10px;
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }
}

.demo-card {
  margin-bottom: 20px;
}

.test-item {
  padding: 20px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  
  h3 {
    margin-top: 0;
    margin-bottom: 15px;
  }
  
  .result-display {
    margin-top: 10px;
    
    .el-tag {
      margin-right: 10px;
    }
  }
}

.batch-results {
  margin-top: 15px;
  
  .batch-summary {
    margin-top: 10px;
    text-align: right;
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.lru-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  align-items: center;
}

.lru-visualization {
  background: var(--el-fill-color-lighter);
  padding: 20px;
  border-radius: 4px;
  
  h4 {
    margin-top: 0;
    margin-bottom: 15px;
  }
  
  .lru-items {
    min-height: 200px;
    
    .lru-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
      margin-bottom: 8px;
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      transition: all 0.3s;
      
      &.most-recent {
        border-color: var(--el-color-success);
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.2);
      }
      
      .key {
        font-weight: bold;
        color: var(--el-color-primary);
      }
      
      .value {
        color: var(--el-text-color-secondary);
        font-size: 12px;
      }
    }
  }
  
  .lru-result {
    margin-top: 15px;
    padding: 10px;
    background: var(--el-color-success-light-9);
    border-radius: 4px;
    color: var(--el-color-success);
  }
}

// 响应式
@media (max-width: 768px) {
  .lru-controls {
    flex-direction: column;
    align-items: stretch;
    
    .el-input-number {
      width: 100%;
    }
  }
}
</style>