<template>
  <div class="notification-demo">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>消息推送演示</h3>
          <el-tag :type="connectionStatus.type">{{ connectionStatus.text }}</el-tag>
        </div>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="消息类型">
          <el-select v-model="testForm.type" placeholder="选择消息类型">
            <el-option label="系统通知" value="system"  />
            <el-option label="信息通知" value="info"  />
            <el-option label="成功通知" value="success"  />
            <el-option label="警告通知" value="warning"  />
            <el-option label="错误通知" value="error"  />
            <el-option label="公告通知" value="announcement"  />
            <el-option label="任务提醒" value="task"  />
            <el-option label="审批通知" value="approval"  />
          </el-select>
        </el-form-item>
        
        <el-form-item label="消息标题">
          <el-input v-model="testForm.title" placeholder="输入消息标题"   />
        </el-form-item>
        
        <el-form-item label="消息内容">
          <el-input
            v-model="testForm.content"
            type="textarea"
            :rows="3"
            placeholder="输入消息内容"
            />
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-radio-group v-model="testForm.priority">
            <el-radio label="low">低</el-radio>
            <el-radio label="normal">普通</el-radio>
            <el-radio label="high">高</el-radio>
            <el-radio label="urgent">紧急</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="操作按钮">
          <el-switch v-model="testForm.hasActions"  />
        </el-form-item>
        
        <el-form-item v-if="testForm.hasActions" label="按钮配置">
          <el-button @click="addAction" size="small">添加按钮</el-button>
          <div v-for="(action, index) in testForm.actions" :key="index" class="action-item">
            <el-input v-model="action.label" placeholder="按钮文字" style="width: 100px"   />
            <el-select v-model="action.type" placeholder="类型" style="width: 100px">
              <el-option label="主要" value="primary"  />
              <el-option label="成功" value="success"  />
              <el-option label="警告" value="warning"  />
              <el-option label="危险" value="danger"  />
            </el-select>
            <el-button @click="removeAction(index)" type="danger" size="small" :icon="Delete"   />
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="sendTestNotification">发送测试通知</el-button>
          <el-button @click="sendBatchNotifications">批量发送通知</el-button>
          <el-button @click="toggleConnection">
            {{ wsManager?.isConnected() ? '断开连接' : '连接服务器' }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card style="margin-top: 20px">
      <template #header>
        <h3>WebSocket 状态信息</h3>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="连接状态">
          <el-tag :type="connectionStatus.type">{{ connectionStatus.text }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="消息队列">
          {{ queueLength }} 条待发送
        </el-descriptions-item>
        <el-descriptions-item label="已发送">
          {{ stats.sent }} 条
        </el-descriptions-item>
        <el-descriptions-item label="已接收">
          {{ stats.received }} 条
        </el-descriptions-item>
      </el-descriptions>
      
      <div class="message-log">
        <h4>消息日志</h4>
        <el-scrollbar height="300px">
          <div v-for="log in messageLogs" :key="log.id" class="log-item">
            <el-tag :type="log.type" size="small">{{ log.direction }}</el-tag>
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-content">{{ log.content }}</span>
          </div>
        </el-scrollbar>
      </div>
    </el-card>
    
    <el-card style="margin-top: 20px">
      <template #header>
        <h3>模拟业务场景</h3>
      </template>
      
      <el-row :gutter="16">
        <el-col :span="6">
          <el-button @click="simulateContractExpiry" class="scenario-btn">
            合同到期提醒
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button @click="simulateLeaveApproval" class="scenario-btn">
            请假审批通知
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button @click="simulateSalarySlip" class="scenario-btn">
            工资条发放
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button @click="simulateAttendanceAlert" class="scenario-btn">
            考勤异常提醒
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'NotificationDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { getWebSocketManager } from '@/utils/notification/WebSocketManager'
import { createNotificationMessage, NotificationType } from '@/utils/notification'
import type { WebSocketManager } from '@/utils/notification/WebSocketManager'

// 状态
const wsManager = ref<WebSocketManager | null>(null)
const connectionState = ref('disconnected')
const queueLength = ref(0)
const stats = ref({
  sent: 0,
  received: 0
})
const messageLogs = ref<any[]>([])

// 测试表单
const testForm = ref({
  type: 'info',
  title: '测试通知',
  content: '这是一条测试通知消息',
  priority: 'normal' as 'low' | 'normal' | 'high' | 'urgent',
  hasActions: false,
  actions: [] as Array<{ label: string; type: string }>
})

// 计算属性
const connectionStatus = computed(() => {
  const statusMap = {
    connecting: { text: '连接中...', type: 'warning' },
    connected: { text: '已连接', type: 'success' },
    disconnected: { text: '未连接', type: 'info' },
    error: { text: '连接错误', type: 'danger' }
  }
  return statusMap[connectionState.value as keyof typeof statusMap] || statusMap.disconnected
})

// 方法
const initWebSocket = () => {
  wsManager.value = getWebSocketManager({
    url: import.meta.env.VITE_WS_URL || 'ws://localhost:3000/ws',
    reconnect: true,
    heartbeat: true,
    showNotification: true
  })
  
  // 监听连接状态
  wsManager.value.on('connected', () => {
    connectionState.value = 'connected'
    addLog('连接成功', 'success', 'system')
    ElMessage.success('WebSocket 连接成功')
  })
  
  wsManager.value.on('disconnected', () => {
    connectionState.value = 'disconnected'
    addLog('连接断开', 'warning', 'system')
  })
  
  wsManager.value.on('error', (_error) => {
    connectionState.value = 'error'
    addLog('连接错误: ' + error, 'error', 'system')
  })
  
  wsManager.value.on('message', (message) => {
    stats.value.received++
    addLog(JSON.stringify(message), 'info', 'received')
  })
  
  wsManager.value.on('sent', (message) => {
    stats.value.sent++
    addLog(JSON.stringify(message), 'success', 'sent')
  })
  
  // 连接服务器
  wsManager.value.connect().catch(() => {
    console.error('连接失败:', error)
    ElMessage.error('WebSocket 连接失败')
  })
  
  // 定时更新队列长度
  setInterval(() => {
    if (wsManager.value) {
      queueLength.value = wsManager.value.getQueueLength()
      connectionState.value = wsManager.value.getState()
    }
  }, 1000)
}

const sendTestNotification = () => {
  if (!wsManager.value || !wsManager.value.isConnected()) {
    ElMessage.warning('请先连接服务器')
    return
  }
  
  const data = {
    title: testForm.value.title,
    content: testForm.value.content,
    actions: testForm.value.hasActions ? testForm.value.actions : undefined
  }
  
  const message = createNotificationMessage(
    testForm.value.type,
    data,
    { priority: testForm.value.priority }
  )
  
  // 模拟从服务器推送消息
  wsManager.value.emit('message', message)
  ElMessage.success('测试通知已发送')
}

const sendBatchNotifications = () => {
  if (!wsManager.value || !wsManager.value.isConnected()) {
    ElMessage.warning('请先连接服务器')
    return
  }
  
  const types = ['info', 'success', 'warning', 'error', 'task', 'approval']
  const priorities = ['low', 'normal', 'high', 'urgent'] as const
  
  for (let i = 0; i < 10; i++) {
    setTimeout(() => {
      const type = types[Math.floor(Math.random() * types.length)]
      const priority = priorities[Math.floor(Math.random() * priorities.length)]
      
      const message = createNotificationMessage(
        type,
        {
          title: `批量通知 ${i + 1}`,
          content: `这是第 ${i + 1} 条批量测试通知，类型: ${type}，优先级: ${priority}`
        },
        { priority }
      )
      
      wsManager.value!.emit('message', message)
    }, i * 500)
  }
  
  ElMessage.success('批量通知发送中...')
}

const toggleConnection = () => {
  if (!wsManager.value) return
  
  if (wsManager.value.isConnected()) {
    wsManager.value.disconnect()
    ElMessage.info('已断开连接')
  } else {
    wsManager.value.connect().catch(() => {
      console.error('连接失败:', error)
      ElMessage.error('连接失败')
    })
  }
}

const addAction = () => {
  testForm.value.actions.push({
    label: '操作',
    type: 'primary'
  })
}

const removeAction = (index: number) => {
  testForm.value.actions.splice(index, 1)
}

const addLog = (content: string, type: string, direction: 'sent' | 'received' | 'system') => {
  messageLogs.value.unshift({
    id: Date.now() + Math.random(),
    content,
    type,
    direction,
    timestamp: Date.now()
  })
  
  // 限制日志数量
  if (messageLogs.value.length > 100) {
    messageLogs.value = messageLogs.value.slice(0, 100)
  }
}

const formatTime = (timestamp: number) => {
  return format(timestamp, 'HH:mm:ss.SSS')
}

// 模拟业务场景
const simulateContractExpiry = () => {
  const message = createNotificationMessage(
    'reminder',
    {
      title: '合同到期提醒',
      content: '员工张三的劳动合同将于30天后到期，请及时处理续签事宜。',
      link: '/contract/management',
      actions: [
        { label: '查看详情', action: 'view', type: 'primary' },
        { label: '立即续签', action: 'renew', type: 'success' }
      ]
    },
    { priority: 'high', category: 'contract' }
  )
  
  if (wsManager.value) {
    wsManager.value.emit('message', message)
  }
}

const simulateLeaveApproval = () => {
  const message = createNotificationMessage(
    'approval',
    {
      title: '请假审批待处理',
      content: '李四申请事假3天（2024-01-15至2024-01-17），请审批。',
      actions: [
        { label: '同意', action: 'approve', type: 'success' },
        { label: '拒绝', action: 'reject', type: 'danger' },
        { label: '查看详情', action: 'view', type: 'primary' }
      ]
    },
    { priority: 'urgent', category: 'leave' }
  )
  
  if (wsManager.value) {
    wsManager.value.emit('message', message)
  }
}

const simulateSalarySlip = () => {
  const message = createNotificationMessage(
    'salary_slip',
    {
      title: '工资条已发放',
      content: '您的2024年1月工资条已生成，请查看。',
      link: '/salary/slip',
      actions: [
        { label: '查看工资条', action: 'view', type: 'primary' }
      ]
    },
    { priority: 'normal', category: 'salary' }
  )
  
  if (wsManager.value) {
    wsManager.value.emit('message', message)
  }
}

const simulateAttendanceAlert = () => {
  const message = createNotificationMessage(
    'attendance_exception',
    {
      title: '考勤异常提醒',
      content: '您今日上午未打卡，请确认考勤状态。',
      actions: [
        { label: '补打卡', action: 'clock_in', type: 'warning' },
        { label: '申请补卡', action: 'apply', type: 'primary' }
      ]
    },
    { priority: 'high', category: 'attendance' }
  )
  
  if (wsManager.value) {
    wsManager.value.emit('message', message)
  }
}

// 生命周期
onMounted(() => {
  initWebSocket()
})

onUnmounted(() => {
  if (wsManager.value) {
    wsManager.value.disconnect()
  }
})
</script>

<style lang="scss" scoped>
.notification-demo {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
    }
  }
  
  .action-item {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    align-items: center;
  }
  
  .message-log {
    margin-top: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    .log-item {
      padding: 8px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      font-size: 12px;
      font-family: 'Monaco', 'Consolas', monospace;
      
      .log-time {
        margin: 0 8px;
        color: var(--el-text-color-secondary);
      }
      
      .log-content {
        color: var(--el-text-color-regular);
        word-break: break-all;
      }
    }
  }
  
  .scenario-btn {
    width: 100%;
    height: 60px;
    white-space: normal;
    line-height: 1.4;
  }
}
</style>