<template>
  <div class="screen-reader-demo">
    <el-card class="demo-header">
      <h1>屏幕阅读器支持演示</h1>
      <p>本页面展示了针对屏幕阅读器优化的表单组件，包括语义化标签、ARIA属性、键盘导航等功能。</p>
    </el-card>

    <!-- 基础表单演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>基础表单 - 语义化标签</span>
      </template>
      
      <AccessibleForm
        :model="basicFormData"
        :fields="basicFields"
        :rules="basicRules"
        title="员工基本信息"
        description="请填写员工的基本信息，带星号的为必填项。"
        @submit="handleBasicSubmit"
      />
      
      <div class="demo-tips">
        <el-alert type="info" :closable="false">
          <template #title>
            <strong>屏幕阅读器优化点：</strong>
            <ul style="margin: 5px 0 0 20px;">
              <li>每个输入框都有正确的 label 关联（for 属性）</li>
              <li>必填字段有 aria-required="true" 标记</li>
              <li>错误信息使用 role="alert" 实时通知</li>
              <li>帮助文本通过 aria-describedby 关联</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 分组表单演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>分组表单 - Fieldset 和 Legend</span>
      </template>
      
      <AccessibleForm
        :model="groupFormData"
        :groups="formGroups"
        :rules="groupRules"
        title="员工详细信息表"
        description="本表单使用 fieldset 和 legend 元素组织相关字段，帮助屏幕阅读器用户理解表单结构。"
        show-progress
        :steps="formSteps"
        :current-step="currentStep"
        @submit="handleGroupSubmit"
      />
      
      <div class="demo-tips">
        <el-alert type="info" :closable="false">
          <template #title>
            <strong>分组优化：</strong>
            <ul style="margin: 5px 0 0 20px;">
              <li>使用 fieldset 将相关字段分组</li>
              <li>legend 提供组标题，屏幕阅读器会朗读</li>
              <li>可折叠组支持键盘操作（Enter/Space）</li>
              <li>进度指示器帮助用户了解表单完成度</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 复杂表单元素演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>复杂表单元素</span>
      </template>
      
      <AccessibleForm
        :model="complexFormData"
        :fields="complexFields"
        title="高级表单控件"
        @submit="handleComplexSubmit"
      >
        <!-- 自定义插槽示例 -->
        <template #custom-field>
          <div role="group" aria-labelledby="custom-field-label">
            <span id="custom-field-label" class="custom-label">自定义字段组</span>
            <p class="custom-description">这是一个自定义的表单字段示例。</p>
          </div>
        </template>
      </AccessibleForm>
    </el-card>

    <!-- 实时验证演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>实时验证与错误处理</span>
      </template>
      
      <AccessibleForm
        :model="validationFormData"
        :fields="validationFields"
        :rules="validationRules"
        title="表单验证演示"
        description="本表单展示了实时验证和错误信息的无障碍处理。"
        show-validation-summary
        @submit="handleValidationSubmit"
        @validation-failed="handleValidationFailed"
      />
      
      <div class="demo-tips">
        <el-alert type="info" :closable="false">
          <template #title>
            <strong>验证优化：</strong>
            <ul style="margin: 5px 0 0 20px;">
              <li>错误信息通过 aria-live="assertive" 立即通知</li>
              <li>无效字段有 aria-invalid="true" 标记</li>
              <li>错误汇总提供快速跳转链接</li>
              <li>焦点自动定位到第一个错误字段</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 屏幕阅读器测试指南 -->
    <el-card class="demo-section">
      <template #header>
        <span>屏幕阅读器测试指南</span>
      </template>
      
      <el-tabs v-model="activeTab" <!-- eslint-disable-line vue/no-mutating-props -->>
        <el-tab-pane label="NVDA" name="nvda">
          <div class="reader-guide">
            <h3>NVDA 快捷键</h3>
            <el-table :data="nvdaShortcuts" stripe size="small">
              <el-table-column prop="key" label="快捷键" width="200"  />
              <el-table-column prop="description" label="功能说明"  />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="JAWS" name="jaws">
          <div class="reader-guide">
            <h3>JAWS 快捷键</h3>
            <el-table :data="jawsShortcuts" stripe size="small">
              <el-table-column prop="key" label="快捷键" width="200"  />
              <el-table-column prop="description" label="功能说明"  />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="VoiceOver" name="voiceover">
          <div class="reader-guide">
            <h3>VoiceOver 快捷键（macOS）</h3>
            <el-table :data="voiceoverShortcuts" stripe size="small">
              <el-table-column prop="key" label="快捷键" width="200"  />
              <el-table-column prop="description" label="功能说明"  />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="测试要点" name="testing">
          <div class="testing-guide">
            <h3>屏幕阅读器测试检查清单</h3>
            <el-checkbox-group v-model="checkedTests" <!-- eslint-disable-line vue/no-mutating-props -->>
              <el-checkbox 
                v-for="test in testChecklist" 
                :key="test.value"
                :value="test.value"
              >
                {{ test.label }}
              </el-checkbox>
            </el-checkbox-group>
            
            <el-divider   />
            
            <h3>常见问题及解决方案</h3>
            <el-collapse v-model="activeNames" <!-- eslint-disable-line vue/no-mutating-props -->>
              <el-collapse-item 
                v-for="(issue, index) in commonIssues" 
                :key="index"
                :title="issue.problem" 
                :name="index"
              >
                <p><strong>问题描述：</strong>{{ issue.description }}</p>
                <p><strong>解决方案：</strong>{{ issue.solution }}</p>
                <pre v-if="issue.code" class="code-example">{{ issue.code }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 实时朗读测试 -->
    <el-card class="demo-section">
      <template #header>
        <span>实时朗读区域测试</span>
      </template>
      
      <div class="live-region-test">
        <el-row :gutter="20">
          <el-col :span="12">
            <h3>操作区</h3>
            <el-button @click="announcePolite">发送礼貌通知</el-button>
            <el-button @click="announceAssertive" type="warning">发送紧急通知</el-button>
            <el-button @click="updateProgress" type="primary">更新进度</el-button>
            
            <el-divider   />
            
            <el-form-item label="测试输入">
              <el-input 
                v-model="testInput" <!-- eslint-disable-line vue/no-mutating-props -->
                placeholder="输入内容查看实时反馈"
                @input="handleTestInput"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <h3>通知历史</h3>
            <div class="notification-log">
              <div 
                v-for="(log, index) in notificationLogs" 
                :key="index"
                class="log-item"
                :class="`log-${log.type}`"
              >
                <span class="log-time">{{ log.time }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <!-- 隐藏的 live region -->
        <div aria-live="polite" aria-atomic="true" class="sr-only">{{ politeMessage }}</div>
        <div aria-live="assertive" aria-atomic="true" class="sr-only">{{ assertiveMessage }}</div>
        <div role="status" aria-live="polite" aria-atomic="true" class="sr-only">{{ progressMessage }}</div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import HrAccessibleForm from '@/components/form/HrAccessibleForm.vue'
import type { FormFieldConfig, FormGroupConfig } from '@/components/form-builder/HrFormBuilder.vue'
import type { FormRules } from 'element-plus'

// 状态
const activeTab = ref('nvda')
const activeNames = ref([0])
const checkedTests = ref<string[]>([])
const currentStep = ref(0)

// 基础表单数据
const basicFormData = reactive({
  name: '',
  email: '',
  phone: '',
  department: ''
})

const basicFields: FormFieldConfig[] = [
  {
    prop: 'name',
    label: '姓名',
    type: 'input',
    required: true,
    description: '请输入您的真实姓名',
    helpText: '姓名将用于系统显示和证件制作',
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    type: 'input',
    required: true,
    description: '请输入有效的邮箱地址',
    placeholder: '<EMAIL>',
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    type: 'input',
    required: true,
    description: '请输入11位手机号码',
    span: 12
  },
  {
    prop: 'department',
    label: '部门',
    type: 'select',
    required: true,
    options: [
      { label: '技术部', value: 'tech' },
      { label: '产品部', value: 'product' },
      { label: '设计部', value: 'design' },
      { label: '人事部', value: 'hr' }
    ],
    span: 12
  }
]

const basicRules: FormRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ]
}

// 分组表单数据
const groupFormData = reactive({
  // 基本信息
  fullName: '',
  gender: '',
  birthDate: '',
  idNumber: '',
  // 联系方式
  email: '',
  phone: '',
  address: '',
  emergencyContact: '',
  // 工作信息
  department: '',
  position: '',
  joinDate: '',
  workType: ''
})

const formGroups: FormGroupConfig[] = [
  {
    name: 'HrBasic',
    title: '基本信息',
    description: '个人基本资料',
    collapsible: true,
    fields: [
      { prop: 'fullName', label: '姓名', type: 'input', required: true, span: 12 },
      { 
        prop: 'gender', 
        label: '性别', 
        type: 'radio', 
        required: true, 
        span: 12,
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' }
        ]
      },
      { prop: 'birthDate', label: '出生日期', type: 'date', required: true, span: 12 },
      { prop: 'idNumber', label: '身份证号', type: 'input', required: true, span: 12 }
    ]
  },
  {
    name: 'contact',
    title: '联系方式',
    description: '联系信息和地址',
    collapsible: true,
    fields: [
      { prop: 'email', label: '邮箱', type: 'input', required: true, span: 12 },
      { prop: 'phone', label: '手机', type: 'input', required: true, span: 12 },
      { prop: 'address', label: '地址', type: 'textarea', required: true, span: 24 },
      { prop: 'emergencyContact', label: '紧急联系人', type: 'input', span: 12 }
    ]
  },
  {
    name: 'work',
    title: '工作信息',
    description: '职位和入职信息',
    collapsible: true,
    fields: [
      { 
        prop: 'department', 
        label: '部门', 
        type: 'select', 
        required: true, 
        span: 12,
        options: [
          { label: '技术部', value: 'tech' },
          { label: '产品部', value: 'product' },
          { label: '设计部', value: 'design' }
        ]
      },
      { prop: 'position', label: '职位', type: 'input', required: true, span: 12 },
      { prop: 'joinDate', label: '入职日期', type: 'date', required: true, span: 12 },
      {
        prop: 'workType',
        label: '工作类型',
        type: 'radio',
        required: true,
        span: 12,
        options: [
          { label: '全职', value: 'fulltime', description: '标准工作时间，享受全部福利' },
          { label: '兼职', value: 'parttime', description: '灵活工作时间，部分福利' },
          { label: '实习', value: 'intern', description: '学习为主，有导师指导' }
        ]
      }
    ]
  }
]

const groupRules: FormRules = {
  fullName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  birthDate: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
  idNumber: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^\d{17}[\dX]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
  department: [{ required: true, message: '请选择部门', trigger: 'change' }],
  position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
  joinDate: [{ required: true, message: '请选择入职日期', trigger: 'change' }],
  workType: [{ required: true, message: '请选择工作类型', trigger: 'change' }]
}

const formSteps = [
  { title: '基本信息', description: '填写个人基本资料' },
  { title: '联系方式', description: '填写联系信息' },
  { title: '工作信息', description: '填写职位信息' },
  { title: '完成', description: '确认并提交' }
]

// 复杂表单数据
const complexFormData = reactive({
  multiSelect: [],
  checkboxGroup: [],
  switch: false,
  slider: 50,
  rate: 3,
  color: '#409EFF',
  dateRange: [],
  timeRange: []
})

const complexFields: FormFieldConfig[] = [
  {
    prop: 'multiSelect',
    label: '多选下拉',
    type: 'select',
    multiple: true,
    filterable: true,
    description: '支持多选和搜索过滤',
    options: [
      { label: '选项1', value: '1' },
      { label: '选项2', value: '2' },
      { label: '选项3', value: '3' },
      { label: '选项4', value: '4' },
      { label: '选项5', value: '5' }
    ],
    span: 12
  },
  {
    prop: 'checkboxGroup',
    label: '复选框组',
    type: 'checkbox',
    description: '选择您感兴趣的技能',
    options: [
      { label: 'JavaScript', value: 'js', description: '前端必备语言' },
      { label: 'TypeScript', value: 'ts', description: '类型安全的JS超集' },
      { label: 'Vue', value: 'vue', description: '渐进式框架' },
      { label: 'React', value: 'react', description: 'Facebook开发的框架' }
    ],
    span: 12
  },
  {
    prop: 'switch',
    label: '开关',
    type: 'switch',
    description: '启用或禁用某项功能',
    span: 12
  },
  {
    prop: 'slider',
    label: '滑块',
    type: 'slider',
    description: '拖动选择数值',
    props: {
      min: 0,
      max: 100,
      step: 10,
      showStops: true,
      showTooltip: true
    },
    span: 12
  },
  {
    prop: 'rate',
    label: '评分',
    type: 'rate',
    description: '点击星星进行评分',
    props: {
      allowHalf: true,
      showText: true,
      texts: ['极差', '失望', '一般', '满意', '惊喜']
    },
    span: 12
  },
  {
    prop: 'color',
    label: '颜色选择',
    type: 'color',
    description: '选择您喜欢的颜色',
    span: 12
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    type: 'daterange',
    description: '选择开始和结束日期',
    span: 12
  },
  {
    prop: 'timeRange',
    label: '时间范围',
    type: 'timerange',
    description: '选择时间段',
    span: 12
  }
]

// 验证表单数据
const validationFormData = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  age: null,
  agreement: false
})

const validationFields: FormFieldConfig[] = [
  {
    prop: 'username',
    label: '用户名',
    type: 'input',
    required: true,
    description: '用户名将作为您的唯一标识',
    showWordLimit: true,
    maxLength: 20,
    span: 24
  },
  {
    prop: 'password',
    label: '密码',
    type: 'input',
    required: true,
    props: { type: 'password', showPassword: true },
    description: '密码至少8位，包含字母和数字',
    span: 24
  },
  {
    prop: 'confirmPassword',
    label: '确认密码',
    type: 'input',
    required: true,
    props: { type: 'password', showPassword: true },
    span: 24
  },
  {
    prop: 'age',
    label: '年龄',
    type: 'number',
    required: true,
    props: { min: 18, max: 65 },
    description: '请输入18-65之间的数字',
    span: 24
  },
  {
    prop: 'agreement',
    label: '',
    type: 'checkbox',
    required: true,
    options: [{ label: '我已阅读并同意服务条款', value: true }],
    span: 24
  }
]

   
const validateConfirmPassword = (rule: unknown, value: unknown, callback: unknown) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== validationFormData.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const validationRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于8位', trigger: 'blur' },
    { pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    { type: 'number', message: '年龄必须是数字', trigger: 'blur' },
    { type: 'number', min: 18, max: 65, message: '年龄必须在18-65之间', trigger: 'blur' }
  ],
  agreement: [
    { 
      validator: (rule, value, callback) => {
        if (!validationFormData.agreement) {
          callback(new Error('请阅读并同意服务条款'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 实时朗读测试
const testInput = ref('')
const politeMessage = ref('')
const assertiveMessage = ref('')
const progressMessage = ref('')
const progress = ref(0)
const notificationLogs = ref<Array<{
  time: string
  message: string
  type: 'polite' | 'assertive' | 'progress'
}>>([])

// 快捷键数据
const nvdaShortcuts = [
  { key: 'NVDA + Tab', description: '朗读当前焦点元素' },
  { key: 'Tab', description: '移动到下一个可聚焦元素' },
  { key: 'Shift + Tab', description: '移动到上一个可聚焦元素' },
  { key: 'Enter', description: '激活按钮或链接' },
  { key: 'Space', description: '选择复选框或单选框' },
  { key: 'F', description: '跳转到下一个表单字段' },
  { key: 'Shift + F', description: '跳转到上一个表单字段' },
  { key: 'E', description: '跳转到下一个编辑框' },
  { key: 'B', description: '跳转到下一个按钮' },
  { key: 'NVDA + F7', description: '显示元素列表' }
]

const jawsShortcuts = [
  { key: 'Insert + Tab', description: '朗读当前元素' },
  { key: 'Tab / Shift + Tab', description: '在表单元素间导航' },
  { key: 'F', description: '跳转到下一个表单字段' },
  { key: 'Shift + F', description: '跳转到上一个表单字段' },
  { key: 'Enter', description: '激活按钮或提交表单' },
  { key: 'Insert + F5', description: '显示表单字段列表' },
  { key: 'Insert + F7', description: '显示链接列表' },
  { key: 'Insert + F9', description: '显示标题列表' },
  { key: 'X', description: '跳转到下一个复选框' },
  { key: 'A', description: '跳转到下一个单选按钮' }
]

const voiceoverShortcuts = [
  { key: 'VO + A', description: '开始朗读' },
  { key: 'Control', description: '停止朗读' },
  { key: 'VO + 右箭头', description: '移动到下一个元素' },
  { key: 'VO + 左箭头', description: '移动到上一个元素' },
  { key: 'VO + Space', description: '激活元素' },
  { key: 'VO + U', description: '打开转子（元素列表）' },
  { key: 'VO + Command + J', description: '跳转到下一个表单控件' },
  { key: 'VO + Command + Shift + J', description: '跳转到上一个表单控件' },
  { key: 'VO + Command + H', description: '跳转到下一个标题' },
  { key: 'VO + Shift + I', description: '朗读元素描述' }
]

const testChecklist = [
  { value: 'labels', label: '所有表单控件都有明确的标签' },
  { value: 'required', label: '必填字段有清晰的标识' },
  { value: 'errors', label: '错误信息能被立即朗读' },
  { value: 'groups', label: '相关字段正确分组' },
  { value: 'navigation', label: '可以使用键盘完成所有操作' },
  { value: 'focus', label: '焦点顺序符合逻辑' },
  { value: 'descriptions', label: '复杂控件有充分的说明' },
  { value: 'feedback', label: '操作反馈及时且清晰' },
  { value: 'headings', label: '标题层级结构正确' },
  { value: 'landmarks', label: '使用了合适的地标角色' }
]

const commonIssues = [
  {
    problem: '表单标签未关联',
    description: '输入框没有相应的 label 元素，或 label 的 for 属性未正确设置',
    solution: '确保每个输入框都有对应的 label，并使用 for 属性关联',
    code: '<label for="username">用户名</label>\n<input id="username" type="text" />'
  },
  {
    problem: '缺少错误提示关联',
    description: '错误信息显示了，但屏幕阅读器不会自动朗读',
    solution: '使用 aria-describedby 关联错误信息，并设置 aria-invalid',
    code: '<input\n  id="email"\n  aria-describedby="email-error"\n  aria-invalid="true"\n/>\n<span id="email-error" role="alert">邮箱格式不正确</span>'
  },
  {
    problem: '动态内容更新无通知',
    description: '页面内容动态更新后，屏幕阅读器用户无法感知',
    solution: '使用 aria-live 区域通知内容变化',
    code: '<div aria-live="polite" aria-atomic="true">\n  {{ statusMessage }}\n</div>'
  },
  {
    problem: '复选框/单选框组缺少分组',
    description: '相关的选项没有被正确分组，用户难以理解选项之间的关系',
    solution: '使用 fieldset 和 legend 或 role="group" 和 aria-labelledby',
    code: '<fieldset>\n  <legend>选择您的技能</legend>\n  <input type="checkbox" id="js" />\n  <label for="js">JavaScript</label>\n  <!-- 更多选项 -->\n</fieldset>'
  }
]

// 方法
   
const handleBasicSubmit = (data: unknown) => {
  ElMessage.success('基础表单提交成功')
  console.log('Basic form data:', data)
}

   
const handleGroupSubmit = (data: unknown) => {
  ElMessage.success('分组表单提交成功')
  console.log('Group form data:', data)
  currentStep.value =   3
}

   
const handleComplexSubmit = (data: unknown) => {
  ElMessage.success('复杂表单提交成功')
  console.log('Complex form data:', data)
}

   
const handleValidationSubmit = (data: unknown) => {
  ElMessage.success('验证表单提交成功')
  console.log('Validation form data:', data)
}

   
const handleValidationFailed = (errors: unknown) => {
  console.log('Validation errors:', errors)
}

const announcePolite = () => {
  const message = `这是一条礼貌通知，发送于 ${new Date().toLocaleTimeString()}`
  politeMessage.value =   message
  addLog(message, 'polite')
}

const announceAssertive = () => {
  const message = `紧急通知！请立即注意！时间：${new Date().toLocaleTimeString()}`
  assertiveMessage.value =   message
  addLog(message, 'assertive')
}

const updateProgress = () => {
  progress.value += 20
  if (progress.value > 100) progress.value =   0
  progressMessage.value =   `当前进度：${progress.value}%`
  addLog(progressMessage.value, 'progress')
}

const handleTestInput = (value: string) => {
  if (value.length > 0) {
    politeMessage.value =   `您输入了：${value}，共 ${value.length} 个字符`
  }
}

const addLog = (message: string, type: 'polite' | 'assertive' | 'progress') => {
  notificationLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  if (notificationLogs.value.length > 10) {
    notificationLogs.value.pop()
  }
}
</script>

<style scoped lang="scss">
.screen-reader-demo {
  padding: 20px;
  
  .demo-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 10px;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .demo-section {
    margin-bottom: 20px;
    
    .demo-tips {
      margin-top: 20px;
    }
  }
  
  .reader-guide,
  .testing-guide {
    padding: 20px;
    
    h3 {
      margin: 0 0 16px;
      font-size: 16px;
    }
    
    .el-checkbox {
      display: block;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .live-region-test {
    h3 {
      margin: 0 0 16px;
      font-size: 16px;
    }
    
    .notification-log {
      height: 300px;
      padding: 12px;
      background-color: #f5f7fa;
      border-radius: 4px;
      overflow-y: auto;
      
      .log-item {
        padding: 8px;
        margin-bottom: 8px;
        background-color: white;
        border-radius: 4px;
        border-left: 3px solid;
        
        &.log-polite {
          border-color: #409eff;
        }
        
        &.log-assertive {
          border-color: #e6a23c;
        }
        
        &.log-progress {
          border-color: #67c23a;
        }
        
        .log-time {
          display: inline-block;
          margin-right: 8px;
          font-size: 12px;
          color: #909399;
        }
        
        .log-message {
          font-size: 14px;
        }
      }
    }
  }
  
  .code-example {
    padding: 12px;
    margin-top: 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
    overflow-x: auto;
  }
  
  .custom-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .custom-description {
    margin: 8px 0;
    font-size: 12px;
    color: #909399;
  }
}

// 屏幕阅读器专用样式
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>