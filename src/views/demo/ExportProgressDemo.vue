<template>
  <div class="export-progress-demo">
    <el-card class="demo-header">
      <h1>导出进度展示演示</h1>
      <p>支持大数据量导出的实时进度展示、多任务管理、断点续传等功能</p>
    </el-card>

    <!-- 功能演示 -->
    <el-row :gutter="20">
      <!-- 基础导出 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>基础导出功能</span>
          </template>
          
          <div class="demo-section">
            <h4>简单导出示例</h4>
            <el-space>
              <el-button @click="exportSmallData">
                导出小数据量 (1,000条)
              </el-button>
              <el-button @click="exportMediumData">
                导出中等数据量 (10,000条)
              </el-button>
              <el-button @click="exportLargeData">
                导出大数据量 (100,000条)
              </el-button>
            </el-space>
          </div>

          <el-divider   />

          <div class="demo-section">
            <h4>多格式导出</h4>
            <el-space>
              <el-button @click="exportExcel">Excel 格式</el-button>
              <el-button @click="exportCSV">CSV 格式</el-button>
              <el-button @click="exportPDF">PDF 格式</el-button>
              <el-button @click="exportJSON">JSON 格式</el-button>
            </el-space>
          </div>

          <el-divider   />

          <div class="demo-section">
            <h4>并发导出测试</h4>
            <el-space wrap>
              <el-button @click="startMultipleExports">
                同时导出5个任务
              </el-button>
              <el-button @click="startSequentialExports">
                顺序导出5个任务
              </el-button>
            </el-space>
          </div>
        </el-card>
      </el-col>

      <!-- 高级功能 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>高级导出功能</span>
          </template>
          
          <div class="demo-section">
            <h4>完整数据导出</h4>
            <HrDataExportWithProgress
              :fields="tableFields"
              :data="tableData"
              :total-count="totalCount"
              :filtered-count="filteredCount"
              :selected-data="selectedRows"
              :export-api="handleExport"
              button-text="高级导出"
              button-type="primary"
            />
          </div>

          <el-divider   />

          <div class="demo-section">
            <h4>错误处理测试</h4>
            <el-space>
              <el-button @click="exportWithError" type="danger">
                模拟导出失败
              </el-button>
              <el-button @click="exportWithTimeout" type="warning">
                模拟超时
              </el-button>
              <el-button @click="exportWithCancel" type="info">
                可取消的导出
              </el-button>
            </el-space>
          </div>

          <el-divider   />

          <div class="demo-section">
            <h4>性能测试</h4>
            <el-space>
              <el-button @click="exportWithCompression">
                压缩导出
              </el-button>
              <el-button @click="exportWithStreaming">
                流式导出
              </el-button>
            </el-space>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格示例 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="table-header">
          <span>数据表格示例</span>
          <el-space>
            <el-button
              size="small"
              @click="refreshData"
              :loading="loading"
            >
              刷新数据
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="selectAll"
            >
              {{ selectedRows.length === tableData.length ? '取消全选' : '全选' }}
            </el-button>
          </el-space>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="id" label="ID" width="80"  />
        <el-table-column prop="name" label="姓名"  />
        <el-table-column prop="department" label="部门"  />
        <el-table-column prop="position" label="职位"  />
        <el-table-column prop="email" label="邮箱" show-overflow-tooltip  />
        <el-table-column prop="phone" label="电话"  />
        <el-table-column prop="salary" label="薪资">
          <template #default="{ row }">
            ¥{{ row.salary.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="joinDate" label="入职日期">
          <template #default="{ row }">
            {{ formatDate(row.joinDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'info'">
              {{ row.status === 'active' ? '在职' : '离职' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 16px"
       />
    </el-card>

    <!-- 导出历史 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>导出历史记录</span>
      </template>

      <el-table :data="exportHistory" empty-text="暂无导出记录">
        <el-table-column prop="name" label="文件名"  />
        <el-table-column prop="format" label="格式" width="80">
          <template #default="{ row }">
            <el-tag size="small">{{ row.format.toUpperCase() }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="recordCount" label="记录数" width="100">
          <template #default="{ row }">
            {{ row.recordCount.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="导出时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'success'"
              type="primary"
              link
              size="small"
              @click="downloadHistoryFile(row)"
            >
              下载
            </el-button>
            <el-button
              v-else-if="row.status === 'failed'"
              type="warning"
              link
              size="small"
              @click="retryExport(row)"
            >
              重试
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 导出进度管理器 -->
    <ExportProgressManager
      ref="progressManager"
      :max-concurrent="3"
      :show-notifications="true"
      :persist-tasks="true"
      @task-complete="onExportComplete"
      @task-failed="onExportFailed"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import HrDataExportWithProgress from '@/components/common/HrDataExportWithProgress.vue'
import HrExportProgressManager from '@/components/common/HrExportProgressManager.vue'
import dayjs from 'dayjs'

// Mock 数据生成
const departments = ['技术部', '产品部', '市场部', '人事部', '财务部', '运营部']
const positions = ['工程师', '经理', '主管', '专员', '总监', '副总裁']
const firstNames = ['张', '李', '王', '刘', '陈', '杨', '黄', '赵', '周', '吴']
const lastNames = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '洋', '艳']

// 生成模拟数据
const generateMockData = (count: number) => {
  const data = []
  for (let i = 0; i < count; i++) {
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]
    
    data.push({
      id: i + 1,
      name: firstName + lastName,
      department: departments[Math.floor(Math.random() * departments.length)],
      position: positions[Math.floor(Math.random() * positions.length)],
      email: `user${i + 1}@example.com`,
      phone: `1${Math.floor(Math.random() * 9000000000 + 1000000000)}`,
      salary: Math.floor(Math.random() * 50000 + 10000),
      joinDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28)),
      status: Math.random() > 0.1 ? 'active' : 'inactive'
    })
  }
  return data
}

// Refs
const progressManager = ref<InstanceType<typeof ExportProgressManager>>()

// 响应式数据
const loading = ref(false)
const tableData = ref(generateMockData(10))
const selectedRows = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(10000)
const filteredCount = ref(5000)
const exportHistory = ref<any[]>([])

// 表格字段配置
const tableFields = [
  { key: 'id', label: 'ID', required: true, group: '基本信息' },
  { key: 'name', label: '姓名', required: true, group: '基本信息' },
  { key: 'department', label: '部门', group: '基本信息' },
  { key: 'position', label: '职位', group: '基本信息' },
  { key: 'email', label: '邮箱', group: '联系方式' },
  { key: 'phone', label: '电话', group: '联系方式' },
  { key: 'salary', label: '薪资', group: '薪资信息' },
  { key: 'joinDate', label: '入职日期', group: '工作信息' },
  { key: 'status', label: '状态', group: '工作信息' }
]

// 生命周期
onMounted(() => {
  loadExportHistory()
})

// 方法
const formatDate = (date: Date | string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const formatFileSize = (bytes: number) => {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`
}

const formatDuration = (ms: number) => {
  const seconds = Math.floor(ms / 1000)
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  } else {
    return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分`
  }
}

const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    success: 'success',
    failed: 'danger',
    processing: 'primary',
    cancelled: 'warning'
  }
  return map[status] || 'info'
}

const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    success: '成功',
    failed: '失败',
    processing: '处理中',
    cancelled: '已取消'
  }
  return map[status] || status
}

// 数据操作
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    tableData.value = generateMockData(pageSize.value)
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

const selectAll = () => {
  if (selectedRows.value.length === tableData.value.length) {
    ;(document.querySelector('.el-table') as unknown)?.__vue__?.clearSelection()
  } else {
    ;(document.querySelector('.el-table') as unknown)?.__vue__?.toggleAllSelection()
  }
}

// 导出功能
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const simulateExport = async (options: unknown) => {
  const {totalCount, format, onProgress, signal: _signal} =  options
  const batchSize charset=utf-8,${encodeURIComponent('导出数据内容')}`,
    fileSize: totalCount * 50, // 假设每条记录50字节
    downloadUrl: '#'
  }
}

const exportSmallData = () => {
  progressManager.value?.addTask({
    name: 'HrHr小数据量导出测试',
    format: 'xlsx',
    totalCount: 1000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: (options: unknown) => simulateExport({ ...options, totalCount: 1000, format: 'xlsx' })
  })
}

const exportMediumData = () => {
  progressManager.value?.addTask({
    name: '中等数据量导出测试',
    format: 'xlsx',
    totalCount: 10000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: (options: unknown) => simulateExport({ ...options, totalCount: 10000, format: 'xlsx' })
  })
}

const exportLargeData = () => {
  progressManager.value?.addTask({
    name: '大数据量导出测试',
    format: 'xlsx',
    totalCount: 100000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: (options: unknown) => simulateExport({ ...options, totalCount: 100000, format: 'xlsx' })
  })
}

const exportExcel = () => {
  progressManager.value?.addTask({
    name: 'Excel格式导出',
    format: 'xlsx',
    totalCount: 5000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: (options: unknown) => simulateExport({ ...options, totalCount: 5000, format: 'xlsx' })
  })
}

const exportCSV = () => {
  progressManager.value?.addTask({
    name: 'CSV格式导出',
    format: 'csv',
    totalCount: 5000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: (options: unknown) => simulateExport({ ...options, totalCount: 5000, format: 'csv' })
  })
}

const exportPDF = () => {
  progressManager.value?.addTask({
    name: 'PDF格式导出',
    format: 'pdf',
    totalCount: 5000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: (options: unknown) => simulateExport({ ...options, totalCount: 5000, format: 'pdf' })
  })
}

const exportJSON = () => {
  progressManager.value?.addTask({
    name: 'JSON格式导出',
    format: 'json',
    totalCount: 5000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: (options: unknown) => simulateExport({ ...options, totalCount: 5000, format: 'json' })
  })
}

const startMultipleExports = () => {
  const formats = ['xlsx', 'csv', 'pdf', 'json', 'xml']
  formats.forEach((format, index) => {
    progressManager.value?.addTask({
      name: `并发导出任务${index + 1}`,
      format,
      totalCount: Math.floor(Math.random() * 10000 + 5000),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
      exportFn: (options: unknown) => simulateExport(options)
    })
  })
}

const startSequentialExports = () => {
  const formats = ['xlsx', 'csv', 'pdf', 'json', 'xml']
  let delay = 0
  
  formats.forEach((format, index) => {
    setTimeout(() => {
      progressManager.value?.addTask({
        name: `顺序导出任务${index + 1}`,
        format,
        totalCount: Math.floor(Math.random() * 10000 + 5000),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
        exportFn: (options: unknown) => simulateExport(options)
      })
    }, delay)
    delay += 2000
  })
}

const exportWithError = () => {
  progressManager.value?.addTask({
    name: '错误测试导出',
    format: 'xlsx',
    totalCount: 5000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: async (options: unknown) => {
      const {onProgress} =  options
      
      // 模拟处理到50%时出错
      for (let i  i <= 50; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200))
        if (onProgress) {
          onProgress(i, (5000 * i) / 100)
        }
      }
      
      throw new Error('模拟导出错误：磁盘空间不足')
    }
  })
}

const exportWithTimeout = () => {
  progressManager.value?.addTask({
    name: '超时测试导出',
    format: 'xlsx',
    totalCount: 5000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: async (options: unknown) => {
      const {onProgress} =  options
      
      // 模拟超慢处理
      for (let i  i <= 30; i += 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
        if (onProgress) {
          onProgress(i, (5000 * i) / 100)
        }
      }
      
      throw new Error('导出超时：处理时间过长')
    }
  })
}

const exportWithCancel = () => {
  progressManager.value?.addTask({
    name: '可取消的导出任务',
    format: 'xlsx',
    totalCount: 50000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: (options: unknown) => simulateExport({ ...options, totalCount: 50000, format: 'xlsx' })
  })
  
  setTimeout(() => {
    ElMessage.info('您可以在进度管理器中取消这个任务')
    progressManager.value!.showManager = true
  }, 500)
}

const exportWithCompression = () => {
  progressManager.value?.addTask({
    name: '压缩导出测试',
    format: 'zip',
    totalCount: 20000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: async (options: unknown) => {
      const result = await simulateExport({ ...options, totalCount: 20000, format: 'zip' })
      return {
        ...result,
        fileSize: result.fileSize * 0.3 // 模拟压缩后的大小
      }
    }
  })
}

const exportWithStreaming = () => {
  progressManager.value?.addTask({
    name: '流式导出测试',
    format: 'csv',
    totalCount: 50000,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    exportFn: (options: unknown) => simulateExport({ ...options, totalCount: 50000, format: 'csv' })
  })
}

// 高级导出处理
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleExport = async (options: unknown) => {
  return simulateExport({
    ...options,
    totalCount: options.scope === 'all' ? totalCount.value :
                options.scope === 'selected' ? selectedRows.value.length :
                options.scope === 'filtered' ? filteredCount.value :
                tableData.value.length
  })
}

// 导出历史
const loadExportHistory = () => {
  // 模拟加载历史记录
  exportHistory.value = [
    {
      id: '1',
      name: '员工数据导出_20240119.xlsx',
      format: 'xlsx',
      recordCount: 10000,
      fileSize: 512000,
      duration: 15000,
      status: 'success',
      createTime: new Date()
    },
    {
      id: '2',
      name: '部门统计_20240118.csv',
      format: 'csv',
      recordCount: 5000,
      fileSize: 256000,
      duration: 8000,
      status: 'success',
      createTime: new Date(Date.now() - 86400000)
    },
    {
      id: '3',
      name: '薪资报表_20240117.pdf',
      format: 'pdf',
      recordCount: 3000,
      fileSize: 1024000,
      duration: 20000,
      status: 'failed',
      createTime: new Date(Date.now() - 172800000)
    }
  ]
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const downloadHistoryFile = (record: unknown) => {
  ElMessage.success(`下载文件: ${record.name}`)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const retryExport = (record: unknown) => {
  ElMessage.info(`重试导出: ${record.name}`)
}

// 事件处理
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const onExportComplete = (task: unknown) => {
  // 添加到历史记录
  exportHistory.value.unshift({
    id: task.id,
    name: task.name,
    format: task.format,
    recordCount: task.totalCount,
    fileSize: task.fileSize || 0,
    duration: task.duration || 0,
    status: 'success',
    createTime: new Date()
  })
  
  // 只保留最近20条记录
  if (exportHistory.value.length > 20) {
    exportHistory.value = exportHistory.value.slice(0, 20)
  }
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const onExportFailed = (task: unknown) => {
  // 添加失败记录
  exportHistory.value.unshift({
    id: task.id,
    name: task.name,
    format: task.format,
    recordCount: task.totalCount,
    fileSize: 0,
    duration: task.duration || 0,
    status: 'failed',
    createTime: new Date(),
    error: task.error
  })
}
</script>

<style lang="scss" scoped>
.export-progress-demo {
  padding: 20px;
  
  .demo-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 10px;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: var(--el-text-color-secondary);
    }
  }
  
  .demo-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px;
      font-size: 16px;
      color: var(--el-text-color-regular);
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// 响应式
@media (max-width: 768px) {
  .export-progress-demo {
    padding: 10px;
    
    .el-row {
      margin-left: 0 !important;
      margin-right: 0 !important;
      
      .el-col {
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-bottom: 20px;
      }
    }
    
    .table-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }
}
</style>