<template>
  <div class="hr-page-container">
    <div class="page-header">
      <h1>部门选择器演示</h1>
      <p class="page-desc">展示部门选择器组件的使用方法</p>
    </div>
    <el-card>
      <el-empty description="该演示功能正在开发中，敬请期待..."  />
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'DepartmentSelectorDemo'
})
 
// 部门选择器演示组件
// TODO: 实现部门选择器的各种使用场景演示
</script>

<style scoped>
.hr-page-container {
  padding: 20px;
}
.page-header {
  margin-bottom: 20px;
}
.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}
.page-desc {
  color: #909399;
  margin-top: 8px;
  font-size: 14px;
}
</style>