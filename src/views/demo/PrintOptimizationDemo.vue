<template>
  <div class="print-optimization-demo">
    <!-- 打印操作栏 -->
    <div class="print-actions no-print">
      <el-button type="primary" @click="handlePrint">
        <el-icon><Printer /></el-icon>
        打印当前页
      </el-button>
      <el-button @click="handlePrintPreview">
        <el-icon><View /></el-icon>
        打印预览
      </el-button>
      <el-button @click="handlePrintSelection">
        <el-icon><Document /></el-icon>
        打印选中内容
      </el-button>
    </div>

    <hr-page-header title="打印样式优化演示" :show-back="true" class="no-print">
      展示各种打印优化效果和最佳实践
    </hr-page-header>

    <!-- 打印说明 -->
    <el-alert
      class="no-print demo-section"
      type="info"
      :closable="false"
      show-icon
    >
      点击上方的打印按钮查看打印效果，所有带有 "no-print" 类的元素在打印时会被隐藏
    </el-alert>

    <!-- 基础打印样式 -->
    <el-card class="demo-section">
      <template #header>
        <span>员工信息表（打印示例）</span>
      </template>
      
      <div class="employee-detail">
        <div class="detail-header">
          <img 
            src="https://via.placeholder.com/100x100.png?text=Avatar" 
            alt="员工照片" 
            class="employee-photo"
          />
          <div class="employee-name">张三</div>
          <div class="employee-title">高级前端工程师</div>
        </div>
        
        <el-descriptions :column="2" border class="detail-section">
          <template #title>
            <span class="section-title">基本信息</span>
          </template>
          <el-descriptions-item label="工号">EMP001234</el-descriptions-item>
          <el-descriptions-item label="部门">技术研发部</el-descriptions-item>
          <el-descriptions-item label="入职日期">2020-03-15</el-descriptions-item>
          <el-descriptions-item label="工作年限">3年</el-descriptions-item>
          <el-descriptions-item label="手机号码">138****5678</el-descriptions-item>
          <el-descriptions-item label="邮箱"><EMAIL></el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 表格打印 -->
    <el-card class="demo-section">
      <template #header>
        <span>员工考勤记录表</span>
      </template>
      
      <div class="attendance-report">
        <div class="report-header print-only">
          <div class="report-title">2024年1月考勤报表</div>
          <div class="report-period">统计期间：2024-01-01 至 2024-01-31</div>
        </div>
        
        <el-table :data="attendanceData" border class="print-table">
          <el-table-column type="selection" width="55" class="no-print"  />
          <el-table-column prop="date" label="日期" width="120"  />
          <el-table-column prop="checkIn" label="上班打卡" width="100"  />
          <el-table-column prop="checkOut" label="下班打卡" width="100"  />
          <el-table-column prop="workHours" label="工作时长" width="100"  />
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" class="no-print">
            <template #default>
              <el-button link type="primary" size="small">查看</el-button>
              <el-button link type="primary" size="small">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="summary-cards print-only">
          <div class="summary-card">
            <div class="card-title">应出勤天数</div>
            <div class="card-value">22</div>
          </div>
          <div class="summary-card">
            <div class="card-title">实际出勤</div>
            <div class="card-value">20</div>
          </div>
          <div class="summary-card">
            <div class="card-title">迟到次数</div>
            <div class="card-value">2</div>
          </div>
          <div class="summary-card">
            <div class="card-title">请假天数</div>
            <div class="card-value">2</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 工资条打印 -->
    <el-card class="demo-section print-page-break">
      <template #header>
        <span>工资条打印样式</span>
      </template>
      
      <div class="salary-slip">
        <div class="slip-header">
          <div class="company-name">杭州科技职业技术学院</div>
          <div class="slip-title">2024年1月工资条</div>
        </div>
        
        <div class="slip-info">
          <div class="info-item">
            <strong>姓名：</strong>张三
          </div>
          <div class="info-item">
            <strong>工号：</strong>EMP001234
          </div>
          <div class="info-item">
            <strong>部门：</strong>技术研发部
          </div>
        </div>
        
        <table class="slip-table">
          <thead>
            <tr>
              <th colspan="2">收入项目</th>
              <th colspan="2">扣除项目</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>基本工资</td>
              <td>8,000.00</td>
              <td>养老保险</td>
              <td>640.00</td>
            </tr>
            <tr>
              <td>岗位津贴</td>
              <td>2,000.00</td>
              <td>医疗保险</td>
              <td>160.00</td>
            </tr>
            <tr>
              <td>绩效奖金</td>
              <td>1,500.00</td>
              <td>失业保险</td>
              <td>40.00</td>
            </tr>
            <tr>
              <td>餐费补贴</td>
              <td>300.00</td>
              <td>住房公积金</td>
              <td>960.00</td>
            </tr>
            <tr>
              <td>交通补贴</td>
              <td>200.00</td>
              <td>个人所得税</td>
              <td>245.00</td>
            </tr>
            <tr class="total-row">
              <td>应发合计</td>
              <td>12,000.00</td>
              <td>扣除合计</td>
              <td>2,045.00</td>
            </tr>
            <tr class="total-row">
              <td colspan="3" style="text-align: right">实发工资：</td>
              <td>9,955.00</td>
            </tr>
          </tbody>
        </table>
        
        <div class="slip-footer print-only">
          <div class="signature">
            <div>财务主管：__________</div>
          </div>
          <div class="signature">
            <div>经办人：__________</div>
          </div>
          <div class="signature">
            <div>领款人：__________</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 表单打印 -->
    <el-card class="demo-section">
      <template #header>
        <span>请假申请表（表单打印）</span>
      </template>
      
      <el-form label-width="120px" class="print-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人：">
              <span class="print-value">张三</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门：">
              <span class="print-value">技术研发部</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请假类型：">
              <span class="print-value">事假</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请假天数：">
              <span class="print-value">2天</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="请假时间：">
              <span class="print-value">2024-01-15 至 2024-01-16</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="请假原因：">
              <span class="print-value">
                家中有急事需要处理，特申请事假两天，望批准。
              </span>
            </el-form-item>
          </el-col>
        </el-row>
        
        <div class="approval-section print-only">
          <el-divider   />
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="approval-box">
                <div class="approval-title">部门主管意见</div>
                <div class="approval-content"></div>
                <div class="approval-sign">
                  签字：__________ 日期：__________
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="approval-box">
                <div class="approval-title">人事部门意见</div>
                <div class="approval-content"></div>
                <div class="approval-sign">
                  签字：__________ 日期：__________
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="approval-box">
                <div class="approval-title">分管领导意见</div>
                <div class="approval-content"></div>
                <div class="approval-sign">
                  签字：__________ 日期：__________
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>

    <!-- 图表打印 -->
    <el-card class="demo-section">
      <template #header>
        <span>统计图表打印</span>
      </template>
      
      <div ref="chartContainer" style="height: 300px"></div>
      
      <div class="chart-description print-only">
        <p>说明：上图展示了2024年第一季度各部门人员分布情况。</p>
      </div>
    </el-card>

    <!-- 打印预览对话框 -->
    <el-dialog
      v-model="showPrintPreview"
      title="打印预览"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="print-preview-container">
        <iframe
          ref="printFrame"
          :src="printPreviewUrl"
          style="width: 100%; height: 600px; border: 1px solid #ddd;"
        />
      </div>
      <template #footer>
        <el-button @click="showPrintPreview = false">关闭</el-button>
        <el-button type="primary" @click="confirmPrint">确认打印</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Printer, View, Document } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import HrPageHeader from '@/components/common/HrPageHeader.vue'

// 状态
const showPrintPreview = ref(false)
const printPreviewUrl = ref('')
const chartContainer = ref<HTMLElement>()
const printFrame = ref<HTMLIFrameElement>()

// 考勤数据
const attendanceData = ref([
  { date: '2024-01-02', checkIn: '08:55', checkOut: '18:05', workHours: '9.2', status: '正常' },
  { date: '2024-01-03', checkIn: '09:15', checkOut: '18:30', workHours: '9.3', status: '迟到' },
  { date: '2024-01-04', checkIn: '08:50', checkOut: '18:10', workHours: '9.3', status: '正常' },
  { date: '2024-01-05', checkIn: '08:45', checkOut: '17:50', workHours: '9.1', status: '正常' },
  { date: '2024-01-08', checkIn: '09:00', checkOut: '18:15', workHours: '9.3', status: '正常' }
])

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '正常':
      return 'success'
    case '迟到':
      return 'warning'
    case '早退':
      return 'warning'
    case '缺勤':
      return 'danger'
    default:
      return 'info'
  }
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  const chart = echarts.init(chartContainer.value)
  
  const option = {
    title: {
      text: '部门人员分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: 'HrHr人员分布',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35, name: '技术研发部' },
          { value: 20, name: '市场营销部' },
          { value: 15, name: '人力资源部' },
          { value: 10, name: '财务部' },
          { value: 20, name: '行政部' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 打印当前页
const handlePrint = () => {
  // 设置打印时间
  document.body.setAttribute('data-print-time', new Date().toLocaleString())
  
  // 执行打印
  window.print()
}

// 打印预览
const handlePrintPreview = async () => {
  showPrintPreview.value = true
  
  await nextTick()
  
  // 创建预览内容
  const printContent = document.querySelector('.print-optimization-demo')?.cloneNode(true) as HTMLElement
  
  if (printContent) {
    // 移除不需要的元素
    printContent.querySelectorAll('.no-print').forEach(el => el.remove())
    
    // 创建完整的HTML文档
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>打印预览</title>
          <link rel="stylesheet" href="/src/styles/print.css">
          <style>
            body { margin: 0; padding: 20px; }
            ${getComputedStyle(document.documentElement).cssText}
          </style>
        </head>
        <body>
          ${printContent.innerHTML}
        </body>
      </html>
    `
    
    // 创建Blob URL
    const blob = new Blob([html], { type: 'text/html' })
    printPreviewUrl.value = URL.createObjectURL(blob)
  }
}

// 打印选中内容
const handlePrintSelection = () => {
  const selection = window.getSelection()
  
  if (!selection || selection.toString().trim() === '') {
    ElMessage.warning('请先选择要打印的内容')
    return
  }
  
  // 创建临时打印区域
  const printArea = document.createElement('div')
  printArea.innerHTML = `
    <h2>选中内容打印</h2>
    <div>${selection.toString()}</div>
  `
  
  // 创建隐藏的iframe
  const iframe = document.createElement('iframe')
  iframe.style.position = 'absolute'
  iframe.style.width = '0'
  iframe.style.height = '0'
  iframe.style.border = 'none'
  
  document.body.appendChild(iframe)
  
  const doc = iframe.contentWindow?.document
  if (doc) {
    doc.open()
    doc.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>打印选中内容</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            h2 { color: #333; }
          </style>
        </head>
        <body>
          ${printArea.innerHTML}
        </body>
      </html>
    `)
    doc.close()
    
    iframe.contentWindow?.print()
  }
  
  // 清理
  setTimeout(() => {
    document.body.removeChild(iframe)
  }, 100)
}

// 确认打印
const confirmPrint = () => {
  printFrame.value?.contentWindow?.print()
}

// 生命周期
onMounted(() => {
  initChart()
})
</script>

<style lang="scss" scoped>
.print-optimization-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
  }
  
  // 员工详情
  .employee-detail {
    .detail-header {
      text-align: center;
      margin-bottom: 30px;
      
      .employee-photo {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: #f5f5f5;
        display: block;
        margin: 0 auto 15px;
      }
      
      .employee-name {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .employee-title {
        color: var(--el-text-color-regular);
      }
    }
  }
  
  // 表单打印
  .print-form {
    .print-value {
      display: inline-block;
      padding: 4px 0;
      border-bottom: 1px solid transparent;
      
      @media print {
        border-bottom-color: #ddd;
      }
    }
    
    .approval-section {
      margin-top: 30px;
      
      .approval-box {
        border: 1px solid #ddd;
        padding: 15px;
        height: 150px;
        display: flex;
        flex-direction: column;
        
        .approval-title {
          font-weight: bold;
          margin-bottom: 10px;
        }
        
        .approval-content {
          flex: 1;
          border-bottom: 1px solid #ddd;
          margin-bottom: 10px;
        }
        
        .approval-sign {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
  
  // 打印预览
  .print-preview-container {
    border: 1px solid #ddd;
    background: #f5f5f5;
    padding: 10px;
  }
}

// 打印时显示的元素
.print-only {
  display: none;
  
  @media print {
    display: block !important;
  }
}

// 屏幕显示时隐藏
.screen-only {
  @media print {
    display: none !important;
  }
}
</style>

<!-- 引入打印样式 -->
<style lang="scss">
@import '@/styles/print.scss';
</style>