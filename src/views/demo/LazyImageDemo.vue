<template>
  <div class="lazy-image-demo">
    <h1>图片懒加载组件演示</h1>
    
    <!-- 基础用法 -->
    <el-card class="demo-section">
      <template #header>
        <span>基础用法</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="8" v-for="(image, index) in basicImages" :key="index">
          <div class="image-item">
            <LazyImage
              :src="image.src"
              :alt="image.alt"
              :width="300"
              :height="200"
              fit="cover"
            />
            <p>{{ image.title }}</p>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 渐进式加载 -->
    <el-card class="demo-section">
      <template #header>
        <span>渐进式加载（先加载缩略图）</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="8" v-for="(image, index) in progressiveImages" :key="index">
          <div class="image-item">
            <LazyImage
              :src="image.src"
              :thumbnail-src="image.thumbnail"
              :alt="image.alt"
              :width="300"
              :height="200"
              load-strategy="progressive"
              fit="cover"
            />
            <p>{{ image.title }}</p>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 证件查看器 -->
    <el-card class="demo-section">
      <template #header>
        <div class="card-header">
          <span>证件查看器</span>
          <el-radio-group v-model="certificateViewMode" size="small">
            <el-radio-button label="grid">网格视图</el-radio-button>
            <el-radio-button label="list">列表视图</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <CertificateViewer
        :certificates="certificates"
        :view-mode="certificateViewMode"
        :downloadable="true"
        :deletable="true"
        :uploadable="true"
        @view="handleViewCertificate"
        @download="handleDownloadCertificate"
        @delete="handleDeleteCertificate"
        @upload="handleUploadCertificate"
      />
    </el-card>

    <!-- 组织架构图懒加载 -->
    <el-card class="demo-section">
      <template #header>
        <span>组织架构图懒加载</span>
      </template>
      <div style="height: 600px;">
        <LazyOrganizationChart
          :root-id="'1'"
          :initial-data="organizationData"
          :load-node-data="loadNodeData"
          :show-minimap="true"
          :default-expand-level="2"
          @node-click="handleNodeClick"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse"
        />
      </div>
    </el-card>

    <!-- 大量图片懒加载 -->
    <el-card class="demo-section">
      <template #header>
        <span>大量图片懒加载测试（滚动查看效果）</span>
      </template>
      <div class="image-grid">
        <div
          v-for="i in 50"
          :key="i"
          class="grid-item"
        >
          <LazyImage
            :src="`https://picsum.photos/300/200?random=${i}`"
            :alt="`测试图片 ${i}`"
            :width="150"
            :height="100"
            :threshold="200"
            fit="cover"
          />
          <p>图片 {{ i }}</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import HrLazyImage from '@/components/common/HrLazyImage.vue'
import HrCertificateViewer from '@/components/common/HrCertificateViewer.vue'
import HrLazyOrganizationChart from '@/components/organization/HrLazyOrganizationChart.vue'

// 证件视图模式
const certificateViewMode = ref<'grid' | 'list'>('grid')

// 基础图片
const basicImages = ref([
  {
    src: 'https://picsum.photos/400/300?random=1',
    alt: '示例图片1',
    title: '基础懒加载'
  },
  {
    src: 'https://picsum.photos/400/300?random=2',
    alt: '示例图片2',
    title: '自动适应尺寸'
  },
  {
    src: 'https://picsum.photos/400/300?random=3',
    alt: '示例图片3',
    title: '点击预览'
  }
])

// 渐进式加载图片
const progressiveImages = ref([
  {
    src: 'https://picsum.photos/800/600?random=4',
    thumbnail: 'https://picsum.photos/80/60?random=4&blur=2',
    alt: '高清图片1',
    title: '先模糊后清晰'
  },
  {
    src: 'https://picsum.photos/800/600?random=5',
    thumbnail: 'https://picsum.photos/80/60?random=5&blur=2',
    alt: '高清图片2',
    title: '渐进式加载'
  },
  {
    src: 'https://picsum.photos/800/600?random=6',
    thumbnail: 'https://picsum.photos/80/60?random=6&blur=2',
    alt: '高清图片3',
    title: '提升用户体验'
  }
])

// 证件列表
const certificates = ref([
  {
    id: '1',
    url: 'https://picsum.photos/800/600?random=10',
    thumbnailUrl: 'https://picsum.photos/200/150?random=10',
    name: 'HrHr身份证正面',
    type: 'idCard',
    size: 1024 * 500,
    uploadTime: new Date('2024-01-15')
  },
  {
    id: '2',
    url: 'https://picsum.photos/800/600?random=11',
    thumbnailUrl: 'https://picsum.photos/200/150?random=11',
    name: '身份证反面',
    type: 'idCard',
    size: 1024 * 480,
    uploadTime: new Date('2024-01-15')
  },
  {
    id: '3',
    url: 'https://picsum.photos/800/600?random=12',
    thumbnailUrl: 'https://picsum.photos/200/150?random=12',
    name: '学位证书',
    type: 'degree',
    size: 1024 * 750,
    uploadTime: new Date('2024-01-16')
  },
  {
    id: '4',
    url: 'https://picsum.photos/800/600?random=13',
    thumbnailUrl: 'https://picsum.photos/200/150?random=13',
    name: '毕业证书',
    type: 'diploma',
    size: 1024 * 820,
    uploadTime: new Date('2024-01-16')
  }
])

// 组织架构数据
const organizationData = ref([
  {
    id: '1',
    name: '杭州科技大学',
    code: 'HUST',
    type: 'company',
    level: 1,
    status: 'active' as const,
    memberCount: 5000,
    hasChildren: true,
    expanded: false,
    loaded: false
  }
])

// 加载节点数据
const loadNodeData = async (nodeId: string) => {
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 返回子节点数据
  const childrenMap: Record<string, any[]> = {
    '1': [
      {
        id: '1-1',
        name: '教务处',
        code: 'JWC',
        type: 'department',
        level: 2,
        status: 'active',
        parentId: '1',
        memberCount: 50,
        hasChildren: true
      },
      {
        id: '1-2',
        name: '科研处',
        code: 'KYC',
        type: 'department',
        level: 2,
        status: 'active',
        parentId: '1',
        memberCount: 30,
        hasChildren: true
      },
      {
        id: '1-3',
        name: '人事处',
        code: 'RSC',
        type: 'department',
        level: 2,
        status: 'active',
        parentId: '1',
        memberCount: 20,
        hasChildren: true
      }
    ],
    '1-1': [
      {
        id: '1-1-1',
        name: '教学管理科',
        code: 'JXGL',
        type: 'team',
        level: 3,
        status: 'active',
        parentId: '1-1',
        memberCount: 15,
        hasChildren: false,
        isLeaf: true
      },
      {
        id: '1-1-2',
        name: '教学质量科',
        code: 'JXZL',
        type: 'team',
        level: 3,
        status: 'active',
        parentId: '1-1',
        memberCount: 10,
        hasChildren: false,
        isLeaf: true
      }
    ]
  }
  
  return childrenMap[nodeId] || []
}

// 事件处理
   
const handleViewCertificate = (cert: unknown, index: number) => {
  console.log('查看证件:', cert, index)
}

   
const handleDownloadCertificate = (cert: unknown) => {
  ElMessage.success(`下载证件: ${cert.name}`)
}

   
const handleDeleteCertificate = (cert: unknown, index: number) => {
  certificates.value.splice(index, 1)
  ElMessage.success('删除成功')
}

const handleUploadCertificate = (files: File[]) => {
  console.log('上传文件:', files)
  ElMessage.success(`成功接收 ${files.length} 个文件`)
}

   
const handleNodeClick = (node: unknown) => {
  console.log('节点点击:', node)
  ElMessage.info(`点击了: ${node.name}`)
}

   
const handleNodeExpand = (node: unknown) => {
  console.log('节点展开:', node)
}

   
const handleNodeCollapse = (node: unknown) => {
  console.log('节点收起:', node)
}
</script>

<style lang="scss" scoped>
.lazy-image-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
  }
  
  .demo-section {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .image-item {
    text-align: center;
    
    p {
      margin-top: 10px;
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }
  
  .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    
    .grid-item {
      text-align: center;
      
      p {
        margin-top: 8px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}
</style>