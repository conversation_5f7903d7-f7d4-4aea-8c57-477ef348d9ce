<template>
  <div class="data-entry-demo">
    <hr-page-header title="数据录入优化演示" :show-back="true">
      演示智能输入、历史记录、批量导入等功能
    </hr-page-header>

    <!-- 基础使用 -->
    <el-card class="demo-section">
      <template #header>
        <span>基础使用</span>
      </template>
      
      <el-form label-width="120px">
        <el-form-item label="默认输入">
          <DataEntryOptimization
            v-model="form.default"
            placeholder="输入任意内容，会自动保存历史记录"
          />
        </el-form-item>
        
        <el-form-item label="禁用历史">
          <DataEntryOptimization
            v-model="form.noHistory"
            :enable-history="false"
            placeholder="不会保存历史记录"
          />
        </el-form-item>
        
        <el-form-item label="自定义建议">
          <DataEntryOptimization
            v-model="form.withSuggestions"
            :suggestions="departments"
            placeholder="输入部门名称，如：技术"
            custom-group-name="部门列表"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 智能推荐 -->
    <el-card class="demo-section">
      <template #header>
        <span>智能推荐</span>
      </template>
      
      <el-form label-width="120px">
        <el-form-item label="员工姓名">
          <DataEntryOptimization
            v-model="form.employeeName"
            :enable-smart-suggest="true"
            :smart-suggest-api="fetchEmployeeSuggestions"
            placeholder="输入员工姓名，如：张"
            history-key="employee-name"
          />
        </el-form-item>
        
        <el-form-item label="职位名称">
          <DataEntryOptimization
            v-model="form.position"
            :enable-smart-suggest="true"
            :smart-suggest-api="fetchPositionSuggestions"
            :suggestions="positionSuggestions"
            placeholder="输入职位名称"
            history-key="position"
            custom-group-name="常用职位"
          />
        </el-form-item>
        
        <el-form-item label="邮箱地址">
          <DataEntryOptimization
            v-model="form.email"
            :enable-smart-suggest="true"
            :smart-suggest-api="fetchEmailSuggestions"
            placeholder="输入邮箱地址"
            history-key="email"
            :min-suggest-length="3"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </DataEntryOptimization>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量导入 -->
    <el-card class="demo-section">
      <template #header>
        <span>批量导入</span>
      </template>
      
      <el-form label-width="120px">
        <el-form-item label="员工工号">
          <DataEntryOptimization
            v-model="form.employeeIds"
            placeholder="支持批量粘贴，每行一个工号"
            :batch-columns="['工号']"
            @batch-import="handleEmployeeIdImport"
          />
          <div v-if="importedEmployeeIds.length > 0" class="import-result">
            已导入 {{ importedEmployeeIds.length }} 个工号：
            {{ importedEmployeeIds.join(', ') }}
          </div>
        </el-form-item>
        
        <el-form-item label="员工信息">
          <DataEntryOptimization
            v-model="form.employeeInfo"
            placeholder="从Excel复制多列数据（工号、姓名、部门）"
            :batch-columns="['工号', '姓名', '部门']"
            @batch-import="handleEmployeeInfoImport"
          />
          <el-table
            v-if="importedEmployeeInfo.length > 0"
            :data="importedEmployeeInfo"
            size="small"
            class="import-result-table"
          >
            <el-table-column prop="id" label="工号"  />
            <el-table-column prop="name" label="姓名"  />
            <el-table-column prop="department" label="部门"  />
          </el-table>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 高级功能 -->
    <el-card class="demo-section">
      <template #header>
        <span>高级功能</span>
      </template>
      
      <el-form label-width="120px">
        <el-form-item label="显示快捷键">
          <DataEntryOptimization
            v-model="form.withShortcuts"
            placeholder="显示快捷键提示"
            :show-shortcuts="true"
            history-key="shortcuts-demo"
          />
        </el-form-item>
        
        <el-form-item label="自定义操作">
          <DataEntryOptimization
            v-model="form.customActions"
            placeholder="自定义快捷操作按钮"
            :quick-actions="customActions"
            history-key="custom-actions"
          />
        </el-form-item>
        
        <el-form-item label="多选模式">
          <DataEntryOptimization
            v-model="multiSelectValue"
            placeholder="输入多个值，用逗号分隔"
            :suggestions="tagSuggestions"
            custom-group-name="标签列表"
            @select="handleMultiSelect"
          />
          <div v-if="selectedTags.length > 0" class="selected-tags">
            <el-tag
              v-for="tag in selectedTags"
              :key="tag"
              closable
              @close="removeTag(tag)"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 实际应用场景 -->
    <el-card class="demo-section">
      <template #header>
        <span>实际应用场景</span>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="快速填表" name="form">
          <el-form label-width="100px" class="quick-form">
            <el-form-item label="姓名">
              <DataEntryOptimization
                v-model="quickForm.name"
                placeholder="输入姓名"
                history-key="quick-form-name"
              />
            </el-form-item>
            <el-form-item label="手机号">
              <DataEntryOptimization
                v-model="quickForm.phone"
                placeholder="输入手机号"
                history-key="quick-form-phone"
                :suggestions="phoneSuggestions"
              />
            </el-form-item>
            <el-form-item label="地址">
              <DataEntryOptimization
                v-model="quickForm.address"
                placeholder="输入地址"
                history-key="quick-form-address"
                :enable-smart-suggest="true"
                :smart-suggest-api="fetchAddressSuggestions"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="数据筛选" name="filter">
          <div class="filter-demo">
            <DataEntryOptimization
              v-model="filterKeyword"
              placeholder="输入关键词筛选数据"
              :suggestions="filterSuggestions"
              custom-group-name="常用筛选"
              @input="handleFilter"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </DataEntryOptimization>
            
            <el-table :data="filteredData" class="filter-table">
              <el-table-column prop="name" label="姓名"  />
              <el-table-column prop="department" label="部门"  />
              <el-table-column prop="position" label="职位"  />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="批量编辑" name="batch">
          <div class="batch-edit-demo">
            <el-alert
              title="选择需要批量修改的字段，然后输入新值"
              type="info"
              :closable="false"
             />
            
            <el-form label-width="100px">
              <el-form-item label="修改字段">
                <el-select v-model="batchEditField" placeholder="选择字段">
                  <el-option label="部门" value="department"  />
                  <el-option label="职位" value="position"  />
                  <el-option label="状态" value="status"  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="新值">
                <DataEntryOptimization
                  v-model="batchEditValue"
                  :placeholder="`输入新的${batchEditField || '值'}`"
                  :suggestions="getBatchEditSuggestions()"
                  :history-key="`batch-edit-${batchEditField}`"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="applyBatchEdit">
                  应用到选中的 {{ selectedRows.length }} 条数据
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 配置说明 -->
    <el-card class="demo-section">
      <template #header>
        <span>配置说明</span>
      </template>
      
      <el-table :data="configData" stripe>
        <el-table-column prop="prop" label="属性" width="200"  />
        <el-table-column prop="type" label="类型" width="150"  />
        <el-table-column prop="default" label="默认值" width="120"  />
        <el-table-column prop="description" label="说明"  />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Message, Search, Calendar, Document, Setting } from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrDataEntryOptimization from '@/components/ux/HrDataEntryOptimization.vue'

// 表单数据
const form = reactive({
  default: '',
  noHistory: '',
  withSuggestions: '',
  employeeName: '',
  position: '',
  email: '',
  employeeIds: '',
  employeeInfo: '',
  withShortcuts: '',
  customActions: ''
})

// 快速表单
const quickForm = reactive({
  name: '',
  phone: '',
  address: ''
})

// 多选相关
const multiSelectValue = ref('')
const selectedTags = ref<string[]>([])

// 筛选相关
const filterKeyword = ref('')
const activeTab = ref('form')
const batchEditField = ref('')
const batchEditValue = ref('')
const selectedRows = ref<any[]>([{ id: 1 }, { id: 2 }, { id: 3 }])

// 导入结果
const importedEmployeeIds = ref<string[]>([])
const importedEmployeeInfo = ref<any[]>([])

// 部门建议
const departments = [
  { text: '技术部', meta: '研发中心' },
  { text: '产品部', meta: '产品中心' },
  { text: '设计部', meta: '创意中心' },
  { text: '运营部', meta: '运营中心' },
  { text: '市场部', meta: '营销中心' },
  { text: '人事部', meta: '人力资源' },
  { text: '财务部', meta: '财务中心' },
  { text: '行政部', meta: '行政中心' }
]

// 职位建议
const positionSuggestions = [
  { text: '前端工程师', meta: '技术岗' },
  { text: '后端工程师', meta: '技术岗' },
  { text: '产品经理', meta: '产品岗' },
  { text: 'UI设计师', meta: '设计岗' },
  { text: '运营专员', meta: '运营岗' }
]

// 标签建议
const tagSuggestions = [
  { text: 'Vue.js', meta: '前端框架' },
  { text: 'React', meta: '前端框架' },
  { text: 'TypeScript', meta: '编程语言' },
  { text: 'Node.js', meta: '运行环境' },
  { text: 'Python', meta: '编程语言' }
]

// 手机号建议
const phoneSuggestions = [
  { text: '138****8888', meta: '常用' },
  { text: '186****6666', meta: '备用' }
]

// 筛选建议
const filterSuggestions = [
  { text: '在职员工', meta: '状态筛选' },
  { text: '技术部', meta: '部门筛选' },
  { text: '本月入职', meta: '时间筛选' }
]

// 自定义操作
const customActions = [
  {
    key: 'calendar',
    icon: Calendar,
    tooltip: '选择日期',
    handler: () => {
      ElMessage.info('打开日期选择器')
    }
  },
  {
    key: 'template',
    icon: Document,
    tooltip: '使用模板',
    handler: () => {
      form.customActions = '这是一个模板内容'
      ElMessage.success('已应用模板')
    }
  },
  {
    key: 'setting',
    icon: Setting,
    tooltip: '输入设置',
    handler: () => {
      ElMessage.info('打开输入设置')
    }
  }
]

// 模拟数据
const mockData = [
  { name: 'HrHr张三', department: '技术部', position: '前端工程师' },
  { name: '李四', department: '产品部', position: '产品经理' },
  { name: '王五', department: '设计部', position: 'UI设计师' },
  { name: '赵六', department: '运营部', position: '运营专员' },
  { name: '钱七', department: '市场部', position: '市场经理' }
]

// 计算属性
const filteredData = computed(() => {
  if (!filterKeyword.value) return mockData
  const keyword = filterKeyword.value.toLowerCase()
  return mockData.filter(item => 
    item.name.toLowerCase().includes(keyword) ||
    item.department.toLowerCase().includes(keyword) ||
    item.position.toLowerCase().includes(keyword)
  )
})

// 获取员工建议
const fetchEmployeeSuggestions = async (query: string) => {
  // 模拟异步请求
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const employees = [
    { text: '张三', meta: '技术部 - EMP001' },
    { text: '张伟', meta: '产品部 - EMP002' },
    { text: '张丽', meta: '设计部 - EMP003' },
    { text: '李四', meta: '运营部 - EMP004' },
    { text: '李明', meta: '市场部 - EMP005' }
  ]
  
  return employees.filter(emp => emp.text.includes(query))
}

// 获取职位建议
const fetchPositionSuggestions = async (query: string) => {
  await new Promise(resolve => setTimeout(resolve, 200))
  
  const positions = [
    { text: '高级前端工程师', meta: 'P6', icon: 'Monitor' },
    { text: '资深后端工程师', meta: 'P7', icon: 'Cpu' },
    { text: '技术经理', meta: 'M3', icon: 'Management' },
    { text: '技术总监', meta: 'M5', icon: 'Trophy' }
  ]
  
  return positions.filter(pos => pos.text.includes(query))
}

// 获取邮箱建议
const fetchEmailSuggestions = async (query: string) => {
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // 根据输入自动补全邮箱后缀
  const domains = ['@company.com', '@gmail.com', '@qq.com', '@163.com']
  const atIndex = query.indexOf('@')
  
  if (atIndex === -1) {
    return domains.map(domain => ({
      text: query + domain,
      meta: '建议'
    }))
  } else {
    const prefix = query.substring(0, atIndex)
    const suffix = query.substring(atIndex)
    return domains
      .filter(domain => domain.startsWith(suffix))
      .map(domain => ({
        text: prefix + domain,
        meta: '建议'
      }))
  }
}

// 获取地址建议
const fetchAddressSuggestions = async (query: string) => {
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const addresses = [
    { text: '浙江省杭州市西湖区', meta: '常用地址' },
    { text: '浙江省杭州市滨江区', meta: '公司地址' },
    { text: '浙江省杭州市余杭区', meta: '仓库地址' }
  ]
  
  return addresses.filter(addr => addr.text.includes(query))
}

// 处理员工ID导入
   
const handleEmployeeIdImport = (data: unknown[]) => {
  importedEmployeeIds.value = data.map(row => row.col0).filter(Boolean)
}

// 处理员工信息导入
   
const handleEmployeeInfoImport = (data: unknown[]) => {
  importedEmployeeInfo.value = data.map(row => ({
    id: row.col0,
    name: row.col1,
    department: row.col2
  })).filter(row => row.id)
}

// 处理多选
   
const handleMultiSelect = (item: unknown) => {
  if (!selectedTags.value.includes(item.text)) {
    selectedTags.value.push(item.text)
  }
  multiSelectValue.value = ''
}

// 移除标签
const removeTag = (tag: string) => {
  const index = selectedTags.value.indexOf(tag)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  }
}

// 处理筛选
const handleFilter = (value: string) => {
  // 筛选逻辑已通过computed属性实现
}

// 获取批量编辑建议
const getBatchEditSuggestions = () => {
  switch (batchEditField.value) {
    case 'department':
      return departments
    case 'position':
      return positionSuggestions
    case 'status':
      return [
        { text: '在职', meta: '正常' },
        { text: '离职', meta: '已离开' },
        { text: '休假', meta: '暂时离开' }
      ]
    default:
      return []
  }
}

// 应用批量编辑
const applyBatchEdit = () => {
  if (!batchEditField.value || !batchEditValue.value) {
    ElMessage.warning('请选择字段并输入新值')
    return
  }
  
  ElMessage.success(`已将 ${selectedRows.value.length} 条数据的${batchEditField.value}修改为：${batchEditValue.value}`)
}

// 配置数据
const configData = [
  {
    prop: 'modelValue',
    type: 'String | Array',
    default: "''",
    description: '绑定值'
  },
  {
    prop: 'suggestions',
    type: 'Array',
    default: '[]',
    description: '自定义建议列表'
  },
  {
    prop: 'enableHistory',
    type: 'Boolean',
    default: 'true',
    description: '是否启用历史记录'
  },
  {
    prop: 'historyKey',
    type: 'String',
    default: 'default',
    description: '历史记录存储键名'
  },
  {
    prop: 'enableSmartSuggest',
    type: 'Boolean',
    default: 'false',
    description: '是否启用智能推荐'
  },
  {
    prop: 'smartSuggestApi',
    type: 'Function',
    default: '-',
    description: '智能推荐API函数'
  },
  {
    prop: 'enableBatchPaste',
    type: 'Boolean',
    default: 'true',
    description: '是否启用批量粘贴'
  },
  {
    prop: 'showQuickActions',
    type: 'Boolean',
    default: 'true',
    description: '是否显示快捷操作按钮'
  },
  {
    prop: 'showShortcuts',
    type: 'Boolean',
    default: 'false',
    description: '是否显示快捷键提示'
  }
]
</script>

<style lang="scss" scoped>
.data-entry-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
  }
  
  .import-result {
    margin-top: 8px;
    padding: 8px 12px;
    background: var(--el-fill-color-light);
    border-radius: 4px;
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
  
  .import-result-table {
    margin-top: 12px;
  }
  
  .selected-tags {
    margin-top: 8px;
    
    .el-tag {
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }
  
  .quick-form {
    padding: 20px;
  }
  
  .filter-demo {
    padding: 20px;
    
    .filter-table {
      margin-top: 20px;
    }
  }
  
  .batch-edit-demo {
    padding: 20px;
    
    .el-alert {
      margin-bottom: 20px;
    }
  }
}
</style>