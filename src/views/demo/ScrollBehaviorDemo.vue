<template>
  <div class="scroll-behavior-demo">
    <hr-page-header title="滚动行为优化演示" :show-back="true">
      演示平滑滚动、锚点定位、滚动监听等功能
    </hr-page-header>

    <!-- 固定导航 -->
    <div class="demo-nav" :class="{ 'nav-fixed': !isAtTop }">
      <el-menu 
        mode="horizontal" 
        :default-active="activeAnchor"
        @select="handleNavSelect"
      >
        <el-menu-item index="#section1">基础滚动</el-menu-item>
        <el-menu-item index="#section2">锚点导航</el-menu-item>
        <el-menu-item index="#section3">滚动监听</el-menu-item>
        <el-menu-item index="#section4">无限滚动</el-menu-item>
        <el-menu-item index="#section5">滚动动画</el-menu-item>
      </el-menu>
      
      <div class="nav-actions">
        <el-button @click="scrollToTop">回到顶部</el-button>
        <el-button @click="scrollToBottom">滚动到底</el-button>
      </div>
    </div>

    <!-- 章节1：基础滚动 -->
    <section id="section1" class="demo-section">
      <h2>基础滚动功能</h2>
      
      <el-card>
        <template #header>
          <span>平滑滚动演示</span>
        </template>
        
        <el-space wrap>
          <el-button @click="handleScrollTo(0, 0)">滚动到顶部</el-button>
          <el-button @click="handleScrollTo(0, 500)">滚动到 Y:500</el-button>
          <el-button @click="handleScrollTo(0, 1000)">滚动到 Y:1000</el-button>
          <el-button @click="handleScrollToElement('.target-element')">滚动到目标元素</el-button>
        </el-space>
        
        <div class="scroll-options">
          <h4>滚动配置</h4>
          <el-form inline>
            <el-form-item label="动画时长">
              <el-input-number v-model="scrollOptions.duration" :min="100" :max="3000" :step="100"   />
            </el-form-item>
            <el-form-item label="缓动函数">
              <el-select v-model="scrollOptions.easing">
                <el-option label="线性" value="linear"  />
                <el-option label="缓入" value="easeIn"  />
                <el-option label="缓出" value="easeOut"  />
                <el-option label="缓入缓出" value="easeInOut"  />
                <el-option label="弹性" value="easeInElastic"  />
                <el-option label="回弹" value="easeOutBounce"  />
              </el-select>
            </el-form-item>
            <el-form-item label="偏移量">
              <el-input-number v-model="scrollOptions.offset" :min="-200" :max="200" :step="10"   />
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      
      <div class="target-element">
        <el-alert title="这是目标元素" type="success" :closable="false"  />
      </div>
    </section>

    <!-- 章节2：锚点导航 -->
    <section id="section2" class="demo-section">
      <h2>锚点导航</h2>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="anchor-nav">
            <template #header>
              <span>文章目录</span>
            </template>
            
            <el-menu :default-active="activeSubAnchor">
              <el-menu-item
                v-for="(item, index) in subAnchors"
                :key="item.id"
                :index="item.id"
                @click="scrollToSubAnchor(item.id)"
              >
                <span :class="{ 'active': activeSubAnchor === item.id }">
                  {{ item.title }}
                </span>
              </el-menu-item>
            </el-menu>
          </el-card>
        </el-col>
        
        <el-col :span="18">
          <el-card>
            <div v-for="item in subAnchors" :key="item.id" :id="item.id" class="sub-section">
              <h3>{{ item.title }}</h3>
              <p v-for="i in 5" :key="i">
                这是 {{ item.title }} 的内容。Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
                Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
              </p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </section>

    <!-- 章节3：滚动监听 -->
    <section id="section3" class="demo-section">
      <h2>滚动监听</h2>
      
      <el-card>
        <template #header>
          <span>滚动状态监控</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="滚动距离" :value="scrollTop" suffix="px"  />
          </el-col>
          <el-col :span="6">
            <el-statistic title="滚动方向" :value="scrollDirection || '静止'"  />
          </el-col>
          <el-col :span="6">
            <el-statistic title="页面位置">
              <template #default>
                <el-tag v-if="isAtTop" type="success">顶部</el-tag>
                <el-tag v-else-if="isAtBottom" type="warning">底部</el-tag>
                <el-tag v-else>中间</el-tag>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="滚动进度" :value="scrollProgress" :precision="1" suffix="%"  />
          </el-col>
        </el-row>
        
        <div class="scroll-events">
          <h4>滚动事件记录</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(event, index) in scrollEvents"
              :key="index"
              :timestamp="event.time"
              :type="event.type"
            >
              {{ event.message }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
    </section>

    <!-- 章节4：无限滚动 -->
    <section id="section4" class="demo-section">
      <h2>无限滚动</h2>
      
      <el-card>
        <template #header>
          <div class="infinite-header">
            <span>数据列表（已加载 {{ items.length }} 条）</span>
            <el-button 
              size="small" 
              @click="resetInfiniteScroll"
              :disabled="infiniteLoading"
            >
              重置列表
            </el-button>
          </div>
        </template>
        
        <div class="infinite-list">
          <div v-for="item in items" :key="item.id" class="list-item">
            <el-card shadow="hover">
              <h4>{{ item.title }}</h4>
              <p>{{ item.content }}</p>
              <el-tag size="small">ID: {{ item.id }}</el-tag>
            </el-card>
          </div>
          
          <div v-if="infiniteLoading" class="loading-more">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
          
          <div v-else-if="!hasMore" class="no-more">
            没有更多数据了
          </div>
        </div>
      </el-card>
    </section>

    <!-- 章节5：滚动动画 -->
    <section id="section5" class="demo-section">
      <h2>滚动触发动画</h2>
      
      <el-card>
        <template #header>
          <span>滚动时元素动画效果</span>
        </template>
        
        <div class="animation-container">
          <div
            v-for="(item, index) in animationItems"
            :key="index"
            class="animation-item"
            :class="item.visible ? item.animation : ''"
            :ref="el => animationRefs[index] = el"
          >
            <el-card>
              <div class="item-content">
                <el-icon :size="48"><component :is="item.icon" /></el-icon>
                <h3>{{ item.title }}</h3>
                <p>{{ item.description }}</p>
              </div>
            </el-card>
          </div>
        </div>
      </el-card>
    </section>

    <!-- 返回顶部按钮 -->
    <Transition name="fade">
      <el-button
        v-show="!isAtTop"
        class="back-to-top"
        circle
        type="primary"
        :icon="Top"
        @click="scrollToTopAnimated"
        />
    </Transition>

    <!-- 滚动进度条 -->
    <div class="scroll-progress" :style="{ width: `${scrollProgress}%` }" />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ScrollBehaviorDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Top, 
  Loading,
  Document,
  FolderOpened,
  Setting,
  Star,
  TrophyBase
} from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import {
  scrollTo,
  scrollToTop,
  scrollToBottom,
  scrollToElement,
  useAnchorNavigation,
  useScrollListener,
  useInfiniteScroll,
  easings
} from '@/utils/scroll'

// 滚动配置
const scrollOptions = ref({
  duration: 500,
  easing: 'easeInOut',
  offset: 80
})

// 锚点导航
const anchors = ['#section1', '#section2', '#section3', '#section4', '#section5']
const {activeAnchor: _activeAnchor, scrollToAnchor: _scrollToAnchor} =  useAnchorNavigation(anchors, {
  offset: 100,
  onActiveChange: (anchor)  title: string; content: string }>>([])
const hasMore = ref(true)
const infiniteLoading = ref(false)

const loadMoreItems = async () => {
  infiniteLoading.value = true
  
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  const currentLength = items.value.length
  const newItems = Array.from({ length: 10 }, (_, i) => ({
    id: currentLength + i + 1,
    title: `项目 ${currentLength + i + 1}`,
    content: `这是第 ${currentLength + i + 1} 个项目的内容描述。`
  }))
  
  items.value.push(...newItems)
  
  // 模拟数据加载完毕
  if (items.value.length >= 50) {
    hasMore.value = false
  }
  
  infiniteLoading.value = false
}

const {start: _start, stop: _stop} =  useInfiniteScroll(
  loadMoreItems,
  {
    threshold: 100,
    immediate: false,
    disabled: computed(() 
  
  // 固定导航
  .demo-nav {
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color);
    transition: all 0.3s;
    
    &.nav-fixed {
      position: sticky;
      top: 0;
      z-index: 100;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    }
    
    .el-menu {
      display: inline-block;
    }
    
    .nav-actions {
      float: right;
      padding: 10px 0;
    }
  }
  
  // 演示章节
  .demo-section {
    padding: 40px 20px;
    min-height: 600px;
    
    h2 {
      margin-bottom: 24px;
      font-size: 24px;
    }
    
    &#section1 {
      .target-element {
        margin-top: 300px;
        padding: 20px;
        border: 2px dashed var(--el-color-primary);
        border-radius: 8px;
        text-align: center;
      }
      
      .scroll-options {
        margin-top: 24px;
        padding: 20px;
        background: var(--el-fill-color-light);
        border-radius: 4px;
      }
    }
    
    &#section2 {
      .anchor-nav {
        position: sticky;
        top: 120px;
        
        .el-menu-item {
          &.is-active span {
            color: var(--el-color-primary);
            font-weight: 600;
          }
        }
      }
      
      .sub-section {
        padding: 20px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);
        
        &:last-child {
          border-bottom: none;
        }
        
        h3 {
          margin-bottom: 16px;
        }
        
        p {
          margin-bottom: 12px;
          line-height: 1.8;
          color: var(--el-text-color-regular);
        }
      }
    }
    
    &#section3 {
      .scroll-events {
        margin-top: 24px;
        max-height: 300px;
        overflow-y: auto;
        
        h4 {
          margin-bottom: 16px;
        }
      }
    }
    
    &#section4 {
      .infinite-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .infinite-list {
        max-height: 600px;
        overflow-y: auto;
        
        .list-item {
          margin-bottom: 16px;
          
          h4 {
            margin: 0 0 8px;
          }
          
          p {
            margin: 0 0 8px;
            color: var(--el-text-color-regular);
          }
        }
        
        .loading-more,
        .no-more {
          padding: 20px;
          text-align: center;
          color: var(--el-text-color-secondary);
          
          .el-icon {
            margin-right: 8px;
          }
        }
      }
    }
    
    &#section5 {
      .animation-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        
        .animation-item {
          opacity: 0;
          
          .item-content {
            text-align: center;
            padding: 20px;
            
            .el-icon {
              margin-bottom: 16px;
              color: var(--el-color-primary);
            }
            
            h3 {
              margin: 0 0 8px;
            }
            
            p {
              margin: 0;
              color: var(--el-text-color-regular);
            }
          }
        }
      }
    }
  }
  
  // 返回顶部按钮
  .back-to-top {
    position: fixed;
    right: 40px;
    bottom: 40px;
    z-index: 99;
  }
  
  // 滚动进度条
  .scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background: var(--el-color-primary);
    transition: width 0.1s;
    z-index: 200;
  }
}

// 动画效果
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotate-in {
  from {
    opacity: 0;
    transform: rotate(-180deg) scale(0.8);
  }
  to {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.slide-in-left {
  animation: slide-in-left 0.6s ease-out forwards;
}

.scale-in {
  animation: scale-in 0.6s ease-out forwards;
}

.rotate-in {
  animation: rotate-in 0.8s ease-out forwards;
}

.bounce-in {
  animation: bounce-in 0.8s ease-out forwards;
}

// 加载动画
.is-loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>