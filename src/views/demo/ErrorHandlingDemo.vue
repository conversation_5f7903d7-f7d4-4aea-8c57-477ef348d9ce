<template>
  <div class="error-handling-demo">
    <hr-page-header title="错误提示优化演示" :show-back="true">
      演示友好的错误提示、错误边界、错误建议等功能
    </hr-page-header>

    <!-- 错误处理组件 -->
    <hr-error-handling ref="errorHandler" :catch-errors="enableCatch">
      <!-- 错误类型演示 -->
      <el-card class="demo-section">
        <template #header>
          <span>错误类型演示</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="error-type-group">
              <h4>常见错误类型</h4>
              <el-space direction="vertical" :size="12" fill>
                <el-button type="warning" @click="showNetworkError">
                  网络错误
                </el-button>
                <el-button type="danger" @click="showPermissionError">
                  权限错误
                </el-button>
                <el-button type="warning" @click="showValidationError">
                  验证错误
                </el-button>
                <el-button type="danger" @click="showServerError">
                  服务器错误
                </el-button>
              </el-space>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="error-type-group">
              <h4>自定义错误</h4>
              <el-space direction="vertical" :size="12" fill>
                <el-button @click="showCustomError">
                  自定义错误信息
                </el-button>
                <el-button @click="showDetailedError">
                  带详情的错误
                </el-button>
                <el-button @click="showActionError">
                  带操作的错误
                </el-button>
                <el-button @click="showAutoCloseError">
                  自动关闭错误
                </el-button>
              </el-space>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 批量错误演示 -->
      <el-card class="demo-section">
        <template #header>
          <span>批量错误演示</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-button type="primary" @click="showMultipleErrors">
              显示多个错误
            </el-button>
            <el-button @click="clearAllErrors">
              清空所有错误
            </el-button>
          </el-col>
          <el-col :span="12">
            <el-form inline>
              <el-form-item label="错误数量">
                <el-input-number v-model="errorCount" :min="1" :max="10"   />
              </el-form-item>
              <el-form-item label="自动关闭">
                <el-switch v-model="autoClose"  />
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>

      <!-- 错误边界演示 -->
      <el-card class="demo-section">
        <template #header>
          <div class="card-header">
            <span>错误边界演示</span>
            <el-switch v-model="enableCatch" active-text="启用全局错误捕获"  />
          </div>
        </template>
        
        <el-alert
          v-if="!enableCatch"
          title="请先启用全局错误捕获"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
         />
        
        <el-space>
          <el-button type="danger" @click="throwError" :disabled="!enableCatch">
            抛出JS错误
          </el-button>
          <el-button type="danger" @click="throwPromiseError" :disabled="!enableCatch">
            抛出Promise错误
          </el-button>
          <el-button type="danger" @click="triggerErrorBoundary" :disabled="!enableCatch">
            触发错误边界
          </el-button>
        </el-space>
      </el-card>

      <!-- API错误模拟 -->
      <el-card class="demo-section">
        <template #header>
          <span>API错误模拟</span>
        </template>
        
        <el-form :model="apiForm" label-width="100px">
          <el-form-item label="请求URL">
            <el-input v-model="apiForm.url" placeholder="https://api.example.com/users"   />
          </el-form-item>
          <el-form-item label="错误类型">
            <el-select v-model="apiForm.errorType">
              <el-option label="网络超时" value="timeout"  />
              <el-option label="404 Not Found" value="404"  />
              <el-option label="401 Unauthorized" value="401"  />
              <el-option label="403 Forbidden" value="403"  />
              <el-option label="500 Server Error" value="500"  />
              <el-option label="数据验证失败" value="validation"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="simulateApiError">
              模拟API错误
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 表单验证错误 -->
      <el-card class="demo-section">
        <template #header>
          <span>表单验证错误</span>
        </template>
        
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="form.username" placeholder="请输入用户名"   />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱"   />
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="form.mobile" placeholder="请输入手机号"   />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">
              提交（触发验证错误）
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 功能特性 -->
      <el-card class="demo-section">
        <template #header>
          <span>功能特性</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="never">
              <h5>错误类型</h5>
              <ul>
                <li>网络错误：网络连接失败</li>
                <li>权限错误：无权访问</li>
                <li>验证错误：数据不合法</li>
                <li>服务器错误：服务异常</li>
                <li>未知错误：其他错误</li>
              </ul>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="never">
              <h5>错误功能</h5>
              <ul>
                <li>错误建议：操作指引</li>
                <li>错误详情：调试信息</li>
                <li>错误操作：快捷处理</li>
                <li>自动关闭：定时消失</li>
                <li>错误边界：页面保护</li>
              </ul>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="never">
              <h5>视觉设计</h5>
              <ul>
                <li>类型图标：直观识别</li>
                <li>颜色编码：严重程度</li>
                <li>动画效果：平滑过渡</li>
                <li>响应式：移动适配</li>
                <li>交互反馈：hover效果</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
    </ErrorHandling>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrErrorHandling from '@/components/ux/HrErrorHandling.vue'
import type { FormInstance, FormRules } from 'element-plus'

// Refs
const errorHandler = ref()
const formRef = ref<FormInstance>()

// 错误配置
const enableCatch = ref(false)
const errorCount = ref(3)
const autoClose = ref(false)

// API表单
const apiForm = reactive({
  url: 'https://api.example.com/users',
  errorType: 'timeout'
})

// 表单数据
const form = reactive({
  username: '',
  email: '',
  mobile: ''
})

// 验证规则
const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 16, message: '长度在 3 到 16 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 显示网络错误
const showNetworkError = () => {
  errorHandler.value.showError.network(
    '无法连接到服务器，请检查网络设置',
    { url: 'https://api.example.com', method: 'GET', timeout: 5000 }
  )
}

// 显示权限错误
const showPermissionError = () => {
  errorHandler.value.showError.permission(
    '您没有权限访问该资源',
    'admin'
  )
}

// 显示验证错误
const showValidationError = () => {
  errorHandler.value.showError.validation(
    '请检查表单填写是否正确',
    ['username', 'email', 'mobile']
  )
}

// 显示服务器错误
const showServerError = () => {
  errorHandler.value.showError.server(
    '服务器内部错误，请稍后重试',
    500
  )
}

// 显示自定义错误
const showCustomError = () => {
  errorHandler.value.addError({
    type: 'unknown',
    title: '自定义错误',
    message: '这是一个自定义的错误消息，可以包含任何内容'
  })
}

// 显示带详情的错误
const showDetailedError = () => {
  errorHandler.value.addError({
    type: 'server',
    title: '复杂错误示例',
    message: '操作失败，点击查看详细错误信息',
    details: {
      request: {
        url: '/api/users/123',
        method: 'PUT',
        data: { name: 'John Doe', email: '<EMAIL>' }
      },
      response: {
        status: 400,
        data: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          errors: [
            { field: 'email', message: 'Email already exists' }
          ]
        }
      },
      timestamp: new Date().toISOString()
    }
  })
}

// 显示带操作的错误
const showActionError = () => {
  errorHandler.value.addError({
    type: 'network',
    title: '上传失败',
    message: '文件上传失败，可能是网络问题',
    suggestions: [
      '检查文件大小是否超过限制（最大10MB）',
      '确保网络连接稳定',
      {
        text: '查看上传限制',
        action: () => ElMessage.info('最大文件大小：10MB，支持格式：jpg, png, pdf'),
        actionText: '查看'
      }
    ],
    actions: [
      {
        text: '重新上传',
        type: 'primary',
        handler: () => {
          ElMessage.success('重新上传中...')
        }
      },
      {
        text: '选择其他文件',
        handler: () => {
          ElMessage.info('请选择其他文件')
        }
      }
    ]
  })
}

// 显示自动关闭错误
const showAutoCloseError = () => {
  errorHandler.value.addError({
    type: 'validation',
    title: '操作提示',
    message: '该错误将在3秒后自动关闭',
    duration: 3000,
    closable: false
  })
}

// 显示多个错误
const showMultipleErrors = () => {
  const types = ['network', 'permission', 'validation', 'server', 'unknown']
  const messages = [
    '网络连接超时',
    '权限验证失败',
    '数据格式错误',
    '服务器响应异常',
    '未知错误发生'
  ]
  
  for (let i = 0; i < errorCount.value; i++) {
    setTimeout(() => {
      errorHandler.value.addError({
        type: types[i % types.length],
        title: `错误 ${i + 1}`,
        message: messages[i % messages.length],
        duration: autoClose.value ? 5000 : 0
      })
    }, i * 300)
  }
}

// 清空所有错误
const clearAllErrors = () => {
  errorHandler.value.clearErrors()
  ElMessage.success('已清空所有错误')
}

// 抛出JS错误
const throwError = () => {
  throw new Error('这是一个故意抛出的JavaScript错误')
}

// 抛出Promise错误
const throwPromiseError = () => {
  Promise.reject('这是一个未处理的Promise拒绝')
}

// 触发错误边界
const triggerErrorBoundary = () => {
  errorHandler.value.handleError(new Error('手动触发的错误边界'))
}

// 模拟API错误
const simulateApiError = () => {
  const {url: _url, errorType: _errorType} =  apiForm
  
  switch (errorType) {
    case 'timeout':
      errorHandler.value.showError.network(`请求超时：${url}`, { timeout: 30000 })
      break
    case '404':
      errorHandler.value.showError.server(`资源不存在：${url}`, 404)
      break
    case '401':
      errorHandler.value.showError.permission('登录已过期，请重新登录')
      break
    case '403':
      errorHandler.value.showError.permission('您没有权限访问该接口', 'api:write')
      break
    case '500':
      errorHandler.value.showError.server('服务器内部错误', 500)
      break
    case 'validation':
      errorHandler.value.showError.validation('请求参数验证失败', ['name', 'email'])
      break
  }
}

// 提交表单
const submitForm 
  
  .demo-section {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    h4 {
      margin: 0 0 16px;
      font-size: 16px;
      color: var(--el-text-color-primary);
    }
    
    h5 {
      margin: 0 0 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .error-type-group {
      h4 {
        margin-bottom: 12px;
      }
    }
    
    .el-card {
      height: 100%;
      
      &.is-never-shadow {
        border: 1px solid var(--el-border-color-lighter);
      }
    }
  }
}
</style>