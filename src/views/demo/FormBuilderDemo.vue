<template>
  <div class="form-builder-demo">
    <hr-page-header title="表单布局优化演示" :show-back="true">
      演示响应式表单布局、分组表单、动态字段等功能
    </hr-page-header>

    <!-- 基础表单 -->
    <el-card class="demo-section">
      <template #header>
        <div class="card-header">
          <span>基础表单布局</span>
          <el-radio-group v-model="basicLayout" <!-- eslint-disable-line vue/no-mutating-props --> size="small">
            <el-radio-button label="default">默认</el-radio-button>
            <el-radio-button label="vertical">垂直</el-radio-button>
            <el-radio-button label="inline">内联</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      
      <HrFormBuilder
        ref="basicFormRef"
        v-model="basicForm" <!-- eslint-disable-line vue/no-mutating-props -->
        :fields="basicFields"
        :layout="basicLayout"
        :title="basicLayout === 'default' ? '员工基本信息' : undefined"
        :description="basicLayout === 'default' ? '请填写员工的基本信息，带 * 的为必填项' : undefined"
        @submit="handleBasicSubmit"
      />
    </el-card>

    <!-- 响应式表单 -->
    <el-card class="demo-section">
      <template #header>
        <span>响应式表单布局</span>
      </template>
      
      <HrFormBuilder
        ref="responsiveFormRef"
        v-model="responsiveForm" <!-- eslint-disable-line vue/no-mutating-props -->
        :fields="responsiveFields"
        title="响应式表单"
        description="根据屏幕尺寸自动调整布局，请调整浏览器窗口查看效果"
        @submit="handleResponsiveSubmit"
      />
    </el-card>

    <!-- 分组表单 -->
    <el-card class="demo-section">
      <template #header>
        <span>分组表单</span>
      </template>
      
      <HrFormBuilder
        ref="groupFormRef"
        v-model="groupForm" <!-- eslint-disable-line vue/no-mutating-props -->
        :groups="formGroups"
        title="员工详细信息"
        description="包含多个信息分组，可以折叠/展开"
        @submit="handleGroupSubmit"
        @field-change="handleFieldChange"
      />
    </el-card>

    <!-- 动态表单 -->
    <el-card class="demo-section">
      <template #header>
        <span>动态表单</span>
      </template>
      
      <HrFormBuilder
        ref="dynamicFormRef"
        v-model="dynamicForm" <!-- eslint-disable-line vue/no-mutating-props -->
        :fields="dynamicFields"
        title="动态表单示例"
        description="根据条件动态显示/隐藏字段"
        @submit="handleDynamicSubmit"
      />
    </el-card>

    <!-- 调试表单 -->
    <el-card class="demo-section">
      <template #header>
        <span>调试模式</span>
      </template>
      
      <HrFormBuilder
        ref="debugFormRef"
        v-model="debugForm" <!-- eslint-disable-line vue/no-mutating-props -->
        :fields="debugFields"
        :debug="true"
        title="调试表单"
        description="开启调试模式，可以查看表单数据、验证规则等信息"
        @submit="handleDebugSubmit"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrFormBuilder from '@/components/form-builder/HrFormBuilder.vue'
import type { FormFieldConfig, FormGroupConfig } from '@/components/form-builder/HrFormBuilder.vue'
import { validators } from '@/utils/validators'

// 基础表单
const basicFormRef = ref()
const basicLayout = ref<'default' | 'vertical' | 'inline'>('default')
const basicForm = reactive({
  name: '',
  age: null,
  gender: '',
  email: '',
  mobile: '',
  address: ''
})

// 基础字段配置
const basicFields = computed<FormFieldConfig[]>(() => [
  {
    prop: 'name',
    label: '姓名',
    type: 'input',
    required: true,
    span: basicLayout.value = /* eslint-disable-line vue/no-mutating-props */== 'inline' ? undefined : 12
  },
  {
    prop: 'age',
    label: '年龄',
    type: 'number',
    required: true,
    span: basicLayout.value = /* eslint-disable-line vue/no-mutating-props */== 'inline' ? undefined : 12,
    props: {
      min: 1,
      max: 150
    }
  },
  {
    prop: 'gender',
    label: '性别',
    type: 'radio',
    required: true,
    span: basicLayout.value = /* eslint-disable-line vue/no-mutating-props */== 'inline' ? undefined : 12,
    options: [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' }
    ]
  },
  {
    prop: 'email',
    label: '邮箱',
    type: 'input',
    span: basicLayout.value = /* eslint-disable-line vue/no-mutating-props */== 'inline' ? undefined : 12,
    rules: validators.email()
  },
  {
    prop: 'mobile',
    label: '手机号',
    type: 'input',
    required: true,
    span: basicLayout.value = /* eslint-disable-line vue/no-mutating-props */== 'inline' ? undefined : 12,
    rules: validators.mobile()
  },
  {
    prop: 'address',
    label: '地址',
    type: 'input',
    span: basicLayout.value = /* eslint-disable-line vue/no-mutating-props */== 'inline' ? undefined : 12
  }
])

// 响应式表单
const responsiveFormRef = ref()
const responsiveForm = reactive({
  fullName: '',
  nickname: '',
  idCard: '',
  birthday: '',
  nationality: '',
  ethnicity: '',
  maritalStatus: '',
  politicalStatus: '',
  phoneNumber: '',
  email: '',
  wechat: '',
  address: '',
  emergencyContact: '',
  emergencyPhone: '',
  introduction: ''
})

// 响应式字段配置
const responsiveFields: FormFieldConfig[] = [
  {
    prop: 'fullName',
    label: '姓名',
    type: 'input',
    required: true,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6
  },
  {
    prop: 'nickname',
    label: '昵称',
    type: 'input',
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6
  },
  {
    prop: 'idCard',
    label: '身份证号',
    type: 'input',
    required: true,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    rules: validators.idCard()
  },
  {
    prop: 'birthday',
    label: '出生日期',
    type: 'date',
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6
  },
  {
    prop: 'nationality',
    label: '国籍',
    type: 'select',
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    options: [
      { label: '中国', value: 'CN' },
      { label: '美国', value: 'US' },
      { label: '英国', value: 'UK' },
      { label: '其他', value: 'other' }
    ]
  },
  {
    prop: 'ethnicity',
    label: '民族',
    type: 'select',
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    options: [
      { label: '汉族', value: 'han' },
      { label: '满族', value: 'man' },
      { label: '回族', value: 'hui' },
      { label: '其他', value: 'other' }
    ]
  },
  {
    prop: 'maritalStatus',
    label: '婚姻状况',
    type: 'radio',
    xs: 24,
    sm: 12,
    md: 12,
    lg: 8,
    options: [
      { label: '未婚', value: 'single' },
      { label: '已婚', value: 'married' },
      { label: '离异', value: 'divorced' },
      { label: '丧偶', value: 'widowed' }
    ]
  },
  {
    prop: 'politicalStatus',
    label: '政治面貌',
    type: 'select',
    xs: 24,
    sm: 12,
    md: 12,
    lg: 8,
    options: [
      { label: '群众', value: 'masses' },
      { label: '共青团员', value: 'youth' },
      { label: '中共党员', value: 'party' },
      { label: '民主党派', value: 'democratic' }
    ]
  },
  {
    prop: 'phoneNumber',
    label: '联系电话',
    type: 'input',
    required: true,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 8,
    rules: validators.mobile()
  },
  {
    prop: 'email',
    label: '电子邮箱',
    type: 'input',
    xs: 24,
    sm: 12,
    md: 8,
    rules: validators.email()
  },
  {
    prop: 'wechat',
    label: '微信号',
    type: 'input',
    xs: 24,
    sm: 12,
    md: 8
  },
  {
    prop: 'address',
    label: '现居住地址',
    type: 'input',
    xs: 24,
    sm: 24,
    md: 16
  },
  {
    prop: 'emergencyContact',
    label: '紧急联系人',
    type: 'input',
    xs: 24,
    sm: 12,
    md: 8
  },
  {
    prop: 'emergencyPhone',
    label: '紧急联系电话',
    type: 'input',
    xs: 24,
    sm: 12,
    md: 8,
    rules: validators.phone()
  },
  {
    prop: 'introduction',
    label: '个人简介',
    type: 'textarea',
    xs: 24,
    props: {
      rows: 4,
      maxlength: 500,
      showWordLimit: true
    }
  }
]

// 分组表单
const groupFormRef = ref()
const groupForm = reactive({
  // 基本信息
  employeeNumber: '',
  fullName: '',
  gender: '',
  birthday: '',
  idCard: '',
  // 联系信息
  phoneNumber: '',
  email: '',
  address: '',
  zipCode: '',
  // 工作信息
  department: '',
  position: '',
  level: '',
  hireDate: '',
  // 教育信息
  education: '',
  school: '',
  major: '',
  graduateDate: ''
})

// 表单分组配置
const formGroups: FormGroupConfig[] = [
  {
    name: 'HrBasic',
    title: '基本信息',
    description: '员工基本资料',
    collapsible: true,
    fields: [
      {
        prop: 'employeeNumber',
        label: '工号',
        type: 'input',
        required: true,
        span: 12
      },
      {
        prop: 'fullName',
        label: '姓名',
        type: 'input',
        required: true,
        span: 12
      },
      {
        prop: 'gender',
        label: '性别',
        type: 'radio',
        required: true,
        span: 12,
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' }
        ]
      },
      {
        prop: 'birthday',
        label: '出生日期',
        type: 'date',
        span: 12
      },
      {
        prop: 'idCard',
        label: '身份证号',
        type: 'input',
        span: 24,
        rules: validators.idCard()
      }
    ]
  },
  {
    name: 'contact',
    title: '联系信息',
    description: '联系方式和地址',
    collapsible: true,
    fields: [
      {
        prop: 'phoneNumber',
        label: '手机号',
        type: 'input',
        required: true,
        span: 12,
        rules: validators.mobile()
      },
      {
        prop: 'email',
        label: '邮箱',
        type: 'input',
        span: 12,
        rules: validators.email()
      },
      {
        prop: 'address',
        label: '地址',
        type: 'input',
        span: 18
      },
      {
        prop: 'zipCode',
        label: '邮编',
        type: 'input',
        span: 6,
        rules: validators.zipCode()
      }
    ]
  },
  {
    name: 'work',
    title: '工作信息',
    description: '部门和职位信息',
    collapsible: true,
    collapsed: true,
    fields: [
      {
        prop: 'department',
        label: '部门',
        type: 'select',
        required: true,
        span: 12,
        options: [
          { label: '技术部', value: 'tech' },
          { label: '产品部', value: 'product' },
          { label: '运营部', value: 'operation' },
          { label: '市场部', value: 'market' }
        ]
      },
      {
        prop: 'position',
        label: '职位',
        type: 'select',
        required: true,
        span: 12,
        options: [
          { label: '工程师', value: 'engineer' },
          { label: '经理', value: 'manager' },
          { label: '总监', value: 'director' }
        ]
      },
      {
        prop: 'level',
        label: '级别',
        type: 'select',
        span: 12,
        options: [
          { label: 'P1', value: 'P1' },
          { label: 'P2', value: 'P2' },
          { label: 'P3', value: 'P3' },
          { label: 'P4', value: 'P4' },
          { label: 'P5', value: 'P5' }
        ]
      },
      {
        prop: 'hireDate',
        label: '入职日期',
        type: 'date',
        span: 12
      }
    ]
  },
  {
    name: 'education',
    title: '教育信息',
    description: '学历和教育背景',
    collapsible: true,
    collapsed: true,
    fields: [
      {
        prop: 'education',
        label: '学历',
        type: 'select',
        span: 12,
        options: [
          { label: '博士', value: 'doctor' },
          { label: '硕士', value: 'master' },
          { label: '本科', value: 'bachelor' },
          { label: '专科', value: 'college' }
        ]
      },
      {
        prop: 'school',
        label: '毕业院校',
        type: 'input',
        span: 12
      },
      {
        prop: 'major',
        label: '专业',
        type: 'input',
        span: 12
      },
      {
        prop: 'graduateDate',
        label: '毕业时间',
        type: 'date',
        span: 12
      }
    ]
  }
]

// 动态表单
const dynamicFormRef = ref()
const dynamicForm = reactive({
  userType: 'personal',
  // 个人用户字段
  personalName: '',
  personalIdCard: '',
  personalPhone: '',
  // 企业用户字段
  companyName: '',
  creditCode: '',
  legalPerson: '',
  contactPerson: '',
  contactPhone: '',
  // 通用字段
  email: '',
  agreement: false
})

// 动态字段配置
const dynamicFields = computed<FormFieldConfig[]>(() => [
  {
    prop: 'userType',
    label: '用户类型',
    type: 'radio',
    span: 24,
    options: [
      { label: '个人用户', value: 'personal' },
      { label: '企业用户', value: 'enterprise' }
    ]
  },
  // 个人用户字段
  {
    prop: 'personalName',
    label: '姓名',
    type: 'input',
    required: true,
    span: 12,
    show: () => dynamicForm.userType === 'personal'
  },
  {
    prop: 'personalIdCard',
    label: '身份证号',
    type: 'input',
    required: true,
    span: 12,
    show: () => dynamicForm.userType === 'personal',
    rules: validators.idCard()
  },
  {
    prop: 'personalPhone',
    label: '手机号',
    type: 'input',
    required: true,
    span: 12,
    show: () => dynamicForm.userType === 'personal',
    rules: validators.mobile()
  },
  // 企业用户字段
  {
    prop: 'companyName',
    label: '企业名称',
    type: 'input',
    required: true,
    span: 24,
    show: () => dynamicForm.userType === 'enterprise'
  },
  {
    prop: 'creditCode',
    label: '统一社会信用代码',
    type: 'input',
    required: true,
    span: 12,
    show: () => dynamicForm.userType === 'enterprise'
  },
  {
    prop: 'legalPerson',
    label: '法定代表人',
    type: 'input',
    required: true,
    span: 12,
    show: () => dynamicForm.userType === 'enterprise'
  },
  {
    prop: 'contactPerson',
    label: '联系人',
    type: 'input',
    required: true,
    span: 12,
    show: () => dynamicForm.userType === 'enterprise'
  },
  {
    prop: 'contactPhone',
    label: '联系电话',
    type: 'input',
    required: true,
    span: 12,
    show: () => dynamicForm.userType === 'enterprise',
    rules: validators.phone()
  },
  // 通用字段
  {
    prop: 'email',
    label: '邮箱',
    type: 'input',
    required: true,
    span: 12,
    rules: validators.email()
  },
  {
    prop: 'agreement',
    label: '',
    type: 'checkbox',
    span: 24,
    options: [
      { label: '我已阅读并同意用户协议和隐私政策', value: true }
    ]
  }
])

// 调试表单
const debugFormRef = ref()
const debugForm = reactive({
  input: '',
  select: '',
  multiSelect: [],
  date: '',
  switch: false,
  slider: 50,
  rate: 3
})

// 调试字段配置
const debugFields: FormFieldConfig[] = [
  {
    prop: 'input',
    label: '输入框',
    type: 'input',
    required: true,
    span: 12
  },
  {
    prop: 'select',
    label: '选择器',
    type: 'select',
    span: 12,
    options: ['选项1', '选项2', '选项3']
  },
  {
    prop: 'multiSelect',
    label: '多选',
    type: 'select',
    multiple: true,
    span: 12,
    options: [
      { label: '选项A', value: 'A' },
      { label: '选项B', value: 'B' },
      { label: '选项C', value: 'C' }
    ]
  },
  {
    prop: 'date',
    label: '日期',
    type: 'date',
    span: 12
  },
  {
    prop: 'switch',
    label: '开关',
    type: 'switch',
    span: 12
  },
  {
    prop: 'slider',
    label: '滑块',
    type: 'slider',
    span: 12
  },
  {
    prop: 'rate',
    label: '评分',
    type: 'rate',
    span: 24
  }
]

// 提交处理
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleBasicSubmit = (data: unknown) => {
  ElMessage.success('基础表单提交成功')
  console.log('基础表单数据:', data)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleResponsiveSubmit = (data: unknown) => {
  ElMessage.success('响应式表单提交成功')
  console.log('响应式表单数据:', data)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleGroupSubmit = (data: unknown) => {
  ElMessage.success('分组表单提交成功')
  console.log('分组表单数据:', data)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleDynamicSubmit = (data: unknown) => {
  ElMessage.success('动态表单提交成功')
  console.log('动态表单数据:', data)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleDebugSubmit = (data: unknown) => {
  ElMessage.success('调试表单提交成功')
  console.log('调试表单数据:', data)
}

// 字段变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleFieldChange = (prop: string, value: unknown) => {
  console.log('字段变化:', prop, value)
}
</script>

<style lang="scss" scoped>
.form-builder-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>