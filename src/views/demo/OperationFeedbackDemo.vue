<template>
  <div class="operation-feedback-demo">
    <hr-page-header title="操作反馈优化演示" :show-back="true">
      演示成功/失败反馈、加载状态、Toast提示、进度通知等功能
    </hr-page-header>

    <!-- 操作反馈组件 -->
    <hr-operation-feedback ref="feedback" />

    <!-- 成功反馈演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>成功反馈演示</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="feedback-group">
            <h4>基础成功反馈</h4>
            <el-space direction="vertical" :size="12" fill>
              <el-button type="success" @click="showBasicSuccess">
                基础成功提示
              </el-button>
              <el-button type="success" @click="showDetailSuccess">
                带详情的成功提示
              </el-button>
              <el-button type="success" @click="showAutoCloseSuccess">
                自动关闭成功提示
              </el-button>
              <el-button type="success" @click="showActionSuccess">
                带操作的成功提示
              </el-button>
            </el-space>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="feedback-group">
            <h4>业务场景</h4>
            <el-space direction="vertical" :size="12" fill>
              <el-button type="primary" @click="simulateSave">
                模拟保存操作
              </el-button>
              <el-button type="primary" @click="simulateSubmit">
                模拟提交审批
              </el-button>
              <el-button type="primary" @click="simulateImport">
                模拟数据导入
              </el-button>
            </el-space>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 错误反馈演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>错误反馈演示</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="feedback-group">
            <h4>基础错误反馈</h4>
            <el-space direction="vertical" :size="12" fill>
              <el-button type="danger" @click="showBasicError">
                基础错误提示
              </el-button>
              <el-button type="danger" @click="showDetailError">
                带错误代码的提示
              </el-button>
              <el-button type="danger" @click="showSuggestionError">
                带建议的错误提示
              </el-button>
              <el-button type="danger" @click="showRetryError">
                可重试的错误提示
              </el-button>
            </el-space>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="feedback-group">
            <h4>错误场景</h4>
            <el-space direction="vertical" :size="12" fill>
              <el-button type="warning" @click="simulateNetworkError">
                模拟网络错误
              </el-button>
              <el-button type="warning" @click="simulateValidationError">
                模拟验证错误
              </el-button>
              <el-button type="warning" @click="simulateServerError">
                模拟服务器错误
              </el-button>
            </el-space>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 加载状态演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>加载状态演示</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="feedback-group">
            <h4>基础加载状态</h4>
            <el-space direction="vertical" :size="12" fill>
              <el-button @click="showBasicLoading">
                基础加载
              </el-button>
              <el-button @click="showProgressLoading">
                带进度的加载
              </el-button>
              <el-button @click="showStepsLoading">
                分步骤加载
              </el-button>
              <el-button @click="showCancelableLoading">
                可取消的加载
              </el-button>
            </el-space>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="feedback-group">
            <h4>加载场景</h4>
            <el-space direction="vertical" :size="12" fill>
              <el-button type="primary" @click="simulateFileUpload">
                模拟文件上传
              </el-button>
              <el-button type="primary" @click="simulateDataExport">
                模拟数据导出
              </el-button>
              <el-button type="primary" @click="simulateBatchProcess">
                模拟批量处理
              </el-button>
            </el-space>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- Toast提示演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>Toast提示演示</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-button type="success" @click="showSuccessToast">
            成功提示
          </el-button>
          <el-button type="danger" @click="showErrorToast">
            错误提示
          </el-button>
          <el-button type="warning" @click="showWarningToast">
            警告提示
          </el-button>
          <el-button @click="showInfoToast">
            信息提示
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button @click="showMultipleToasts">
            连续多个Toast
          </el-button>
          <el-button @click="showLongToast">
            长时间Toast
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 进度通知演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>进度通知演示</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-button @click="startDownloadTask">
            开始下载任务
          </el-button>
          <el-button @click="startUploadTask">
            开始上传任务
          </el-button>
          <el-button @click="startAnalysisTask">
            开始分析任务
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button @click="startMultipleTasks">
            同时开始多个任务
          </el-button>
          <el-button @click="startFailedTask">
            开始会失败的任务
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 组合使用演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>组合使用演示</span>
      </template>
      
      <div class="complex-demo">
        <h4>完整流程演示</h4>
        <p>演示一个包含加载、成功、错误等多种反馈的完整操作流程</p>
        <el-form :model="form" label-width="100px">
          <el-form-item label="姓名">
            <el-input v-model="form.name" placeholder="请输入姓名"   />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="form.email" placeholder="请输入邮箱"   />
          </el-form-item>
          <el-form-item label="部门">
            <el-select v-model="form.department" placeholder="请选择部门">
              <el-option label="技术部" value="tech"  />
              <el-option label="产品部" value="product"  />
              <el-option label="运营部" value="operation"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleComplexSubmit">
              提交表单
            </el-button>
            <el-button @click="handleComplexReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 功能特性 -->
    <el-card class="demo-section">
      <template #header>
        <span>功能特性</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="never">
            <h5>反馈类型</h5>
            <ul>
              <li>成功反馈：操作成功提示</li>
              <li>错误反馈：错误详情展示</li>
              <li>加载状态：进度跟踪</li>
              <li>Toast提示：轻量级提醒</li>
              <li>进度通知：后台任务</li>
            </ul>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never">
            <h5>交互特性</h5>
            <ul>
              <li>自动关闭：定时消失</li>
              <li>手动关闭：用户控制</li>
              <li>操作按钮：快捷操作</li>
              <li>重试机制：失败重试</li>
              <li>取消操作：中断任务</li>
            </ul>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never">
            <h5>视觉设计</h5>
            <ul>
              <li>遮罩背景：聚焦内容</li>
              <li>动画效果：平滑过渡</li>
              <li>图标提示：状态识别</li>
              <li>颜色编码：严重程度</li>
              <li>响应式：移动适配</li>
            </ul>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrOperationFeedback from '@/components/ux/HrOperationFeedback.vue'

// Refs
const feedback = ref()

// 表单数据
const form = reactive({
  name: '',
  email: '',
  department: ''
})

// 基础成功反馈
const showBasicSuccess = () => {
  feedback.value.success({
    title: '操作成功',
    message: '您的操作已成功完成'
  })
}

const showDetailSuccess = () => {
  feedback.value.success({
    title: '保存成功',
    message: '员工信息已成功保存',
    details: [
      '基本信息已更新',
      '联系方式已验证',
      '部门关系已建立',
      '权限已分配'
    ]
  })
}

const showAutoCloseSuccess = () => {
  feedback.value.success({
    title: '提交成功',
    message: '您的申请已提交，3秒后自动关闭',
    duration: 3000,
    showClose: false
  })
}

const showActionSuccess = () => {
  feedback.value.success({
    title: '创建成功',
    message: '员工账号已创建成功',
    actions: [
      {
        text: '继续创建',
        type: 'primary',
        handler: () => {
          feedback.value.toast({ type: 'info', message: '准备创建下一个' })
        }
      },
      {
        text: '查看详情',
        handler: () => {
          feedback.value.toast({ type: 'info', message: '跳转到详情页' })
        }
      }
    ]
  })
}

// 基础错误反馈
const showBasicError = () => {
  feedback.value.error({
    title: '操作失败',
    message: '抱歉，您的操作未能完成'
  })
}

const showDetailError = () => {
  feedback.value.error({
    title: '保存失败',
    message: '员工信息保存失败',
    error: {
      code: 'ERR_VALIDATION',
      detail: '身份证号码格式不正确，请检查后重试'
    }
  })
}

const showSuggestionError = () => {
  feedback.value.error({
    title: '上传失败',
    message: '文件上传失败',
    suggestions: [
      '检查文件大小是否超过限制（最大10MB）',
      '确保文件格式正确（支持jpg、png、pdf）',
      '检查网络连接是否正常',
      '如问题持续，请联系技术支持'
    ]
  })
}

const showRetryError = () => {
  let retryCount = 0
  feedback.value.error({
    title: '网络错误',
    message: '无法连接到服务器',
    showRetry: true,
    onRetry: async () => {
      // 模拟重试
      await new Promise(resolve => setTimeout(resolve, 1000))
      retryCount++
      if (retryCount < 2) {
        throw new Error('Still failing')
      }
      feedback.value.success({
        title: '连接成功',
        message: '已成功连接到服务器'
      })
    }
  })
}

// 加载状态
const showBasicLoading = () => {
  feedback.value.loading({
    title: '处理中',
    message: '请稍候...'
  })
  setTimeout(() => {
    feedback.value.hideLoading()
    feedback.value.success({ title: '处理完成' })
  }, 2000)
}

const showProgressLoading = () => {
  feedback.value.loading({
    title: '上传中',
    message: '正在上传文件...',
    progress: 0
  })
  
  let progress = 0
  const timer = setInterval(() => {
    progress += 10
    feedback.value.updateLoading({ progress })
    if (progress >= 100) {
      clearInterval(timer)
      feedback.value.hideLoading()
      feedback.value.success({ title: '上传完成' })
    }
  }, 300)
}

const showStepsLoading = () => {
  const steps = ['连接服务器', '验证权限', '读取数据', '生成报表', '完成']
  feedback.value.loading({
    title: '正在生成报表',
    steps,
    currentStep: 0
  })
  
  let currentStep = 0
  const timer = setInterval(() => {
    currentStep++
    feedback.value.updateLoading({ currentStep })
    if (currentStep >= steps.length) {
      clearInterval(timer)
      feedback.value.hideLoading()
      feedback.value.success({ title: '报表生成成功' })
    }
  }, 1000)
}

const showCancelableLoading = () => {
  let cancelled = false
  feedback.value.loading({
    title: '数据同步中',
    message: '正在同步数据，这可能需要几分钟...',
    cancelable: true,
    onCancel: () => {
      cancelled = true
      feedback.value.toast({ type: 'warning', message: '操作已取消' })
    }
  })
  
  setTimeout(() => {
    if (!cancelled) {
      feedback.value.hideLoading()
      feedback.value.success({ title: '同步完成' })
    }
  }, 3000)
}

// Toast提示
const showSuccessToast = () => {
  feedback.value.toast({ type: 'success', message: '操作成功' })
}

const showErrorToast = () => {
  feedback.value.toast({ type: 'error', message: '操作失败' })
}

const showWarningToast = () => {
  feedback.value.toast({ type: 'warning', message: '请注意：数据未保存' })
}

const showInfoToast = () => {
  feedback.value.toast('这是一条普通信息')
}

const showMultipleToasts = () => {
  feedback.value.toast({ type: 'success', message: '第一条消息' })
  setTimeout(() => {
    feedback.value.toast({ type: 'info', message: '第二条消息' })
  }, 500)
  setTimeout(() => {
    feedback.value.toast({ type: 'warning', message: '第三条消息' })
  }, 1000)
}

const showLongToast = () => {
  feedback.value.toast({
    type: 'info',
    message: '这是一条停留时间较长的消息',
    duration: 10000
  })
}

// 进度任务
const startDownloadTask = () => {
  const taskId = feedback.value.progress.add({
    title: '下载文件',
    progress: 0,
    message: '准备下载...'
  })
  
  let progress = 0
  const timer = setInterval(() => {
    progress += Math.random() * 20
    if (progress > 100) progress = 100
    
    feedback.value.progress.update(taskId, {
      progress,
      message: `已下载 ${Math.floor(progress)}%`
    })
    
    if (progress >= 100) {
      clearInterval(timer)
      feedback.value.progress.update(taskId, {
        status: 'success',
        message: '下载完成'
      })
      setTimeout(() => {
        feedback.value.progress.remove(taskId)
      }, 2000)
    }
  }, 500)
}

const startUploadTask = () => {
  const taskId = feedback.value.progress.add({
    title: '上传图片',
    progress: 0,
    cancelable: true,
    onCancel: () => {
      feedback.value.toast({ type: 'warning', message: '上传已取消' })
    }
  })
  
  let progress = 0
  const timer = setInterval(() => {
    progress += 5
    feedback.value.progress.update(taskId, { progress })
    
    if (progress >= 100) {
      clearInterval(timer)
      feedback.value.progress.update(taskId, {
        status: 'success',
        message: '上传成功'
      })
      setTimeout(() => {
        feedback.value.progress.remove(taskId)
      }, 2000)
    }
  }, 200)
}

const startAnalysisTask = () => {
  const taskId = feedback.value.progress.add({
    title: '数据分析',
    progress: 0,
    message: '正在分析数据...',
    onClick: () => {
      feedback.value.toast('点击了分析任务')
    }
  })
  
  let progress = 0
  const timer = setInterval(() => {
    progress += Math.random() * 15
    if (progress > 100) progress = 100
    
    const messages = [
      '正在读取数据...',
      '正在处理数据...',
      '正在生成图表...',
      '正在优化结果...',
      '即将完成...'
    ]
    
    feedback.value.progress.update(taskId, {
      progress,
      message: messages[Math.floor(progress / 20)]
    })
    
    if (progress >= 100) {
      clearInterval(timer)
      feedback.value.progress.update(taskId, {
        status: 'success',
        message: '分析完成，点击查看结果'
      })
    }
  }, 800)
}

const startMultipleTasks = () => {
  startDownloadTask()
  setTimeout(startUploadTask, 500)
  setTimeout(startAnalysisTask, 1000)
}

const startFailedTask = () => {
  const taskId = feedback.value.progress.add({
    title: '备份数据',
    progress: 0,
    cancelable: true
  })
  
  let progress = 0
  const timer = setInterval(() => {
    progress += 10
    feedback.value.progress.update(taskId, { progress })
    
    if (progress >= 60) {
      clearInterval(timer)
      feedback.value.progress.update(taskId, {
        status: 'exception',
        message: '备份失败：磁盘空间不足'
      })
    }
  }, 300)
}

// 业务场景模拟
const simulateSave = async () => {
  feedback.value.loading({ title: '保存中' })
  await new Promise(resolve => setTimeout(resolve, 1000))
  feedback.value.hideLoading()
  feedback.value.success({
    title: '保存成功',
    message: '数据已保存到服务器'
  })
}

const simulateSubmit = async () => {
  feedback.value.loading({
    title: '提交中',
    message: '正在提交审批申请...'
  })
  await new Promise(resolve => setTimeout(resolve, 1500))
  feedback.value.hideLoading()
  feedback.value.success({
    title: '提交成功',
    message: '您的申请已提交给上级审批',
    details: [
      '申请单号：AP202401120001',
      '审批人：张经理',
      '预计处理时间：1-3个工作日'
    ],
    actions: [
      {
        text: '查看申请',
        type: 'primary',
        handler: () => {
          feedback.value.toast('跳转到申请详情页')
        }
      }
    ]
  })
}

const simulateImport = async () => {
  const steps = ['解析文件', '验证数据', '导入数据', '更新索引']
  feedback.value.loading({
    title: '导入数据',
    steps,
    currentStep: 0
  })
  
  for (let i = 0; i < steps.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    feedback.value.updateLoading({ currentStep: i + 1 })
  }
  
  feedback.value.hideLoading()
  feedback.value.success({
    title: '导入成功',
    message: '成功导入 1,234 条记录',
    details: [
      '新增记录：1,200 条',
      '更新记录：34 条',
      '跳过记录：0 条',
      '错误记录：0 条'
    ]
  })
}

const simulateNetworkError = () => {
  feedback.value.error({
    title: '网络错误',
    message: '无法连接到服务器',
    error: {
      code: 'NETWORK_ERROR',
      detail: 'Failed to fetch: net::ERR_CONNECTION_REFUSED'
    },
    suggestions: [
      '检查您的网络连接',
      '尝试刷新页面',
      '检查防火墙设置'
    ],
    showRetry: true,
    onRetry: async () => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      feedback.value.toast({ type: 'success', message: '重连成功' })
    }
  })
}

const simulateValidationError = () => {
  feedback.value.error({
    title: '验证失败',
    message: '提交的数据不符合要求',
    error: {
      code: 'VALIDATION_ERROR'
    },
    suggestions: [
      '手机号格式不正确',
      '邮箱地址无效',
      '必填字段不能为空'
    ]
  })
}

const simulateServerError = () => {
  feedback.value.error({
    title: '服务器错误',
    message: '服务器处理请求时发生错误',
    error: {
      code: '500',
      detail: 'Internal Server Error: Database connection failed'
    },
    suggestions: [
      '请稍后重试',
      '如问题持续，请联系技术支持'
    ],
    actions: [
      {
        text: '联系支持',
        handler: () => {
          feedback.value.toast('正在打开帮助中心...')
        }
      }
    ]
  })
}

const simulateFileUpload = async () => {
  const taskId = feedback.value.progress.add({
    title: '上传文件.zip',
    progress: 0,
    cancelable: true,
    message: '0 MB / 25.6 MB'
  })
  
  feedback.value.toast({ type: 'info', message: '开始上传文件' })
  
  let progress = 0
  const timer = setInterval(() => {
    progress += Math.random() * 10
    if (progress > 100) progress = 100
    
    const uploaded = (progress / 100 * 25.6).toFixed(1)
    feedback.value.progress.update(taskId, {
      progress,
      message: `${uploaded} MB / 25.6 MB`
    })
    
    if (progress >= 100) {
      clearInterval(timer)
      feedback.value.progress.update(taskId, {
        status: 'success',
        message: '上传完成'
      })
      feedback.value.toast({ type: 'success', message: '文件上传成功' })
      setTimeout(() => {
        feedback.value.progress.remove(taskId)
      }, 3000)
    }
  }, 500)
}

const simulateDataExport = async () => {
  feedback.value.loading({
    title: '导出数据',
    message: '正在生成Excel文件...',
    progress: 0
  })
  
  for (let i = 0; i <= 100; i += 20) {
    await new Promise(resolve => setTimeout(resolve, 500))
    feedback.value.updateLoading({ 
      progress: i,
      message: i < 100 ? '正在处理数据...' : '即将完成...'
    })
  }
  
  feedback.value.hideLoading()
  feedback.value.success({
    title: '导出成功',
    message: '数据已导出为Excel文件',
    actions: [
      {
        text: '下载文件',
        type: 'primary',
        handler: () => {
          feedback.value.toast({ type: 'info', message: '开始下载...' })
        }
      }
    ]
  })
}

const simulateBatchProcess = async () => {
  const items = ['初始化系统', '加载配置', '连接数据库', '同步数据', '更新缓存', '完成']
  feedback.value.loading({
    title: '批量处理',
    steps: items,
    currentStep: 0,
    cancelable: true,
    onCancel: () => {
      feedback.value.toast({ type: 'warning', message: '批量处理已取消' })
    }
  })
  
  for (let i = 0; i < items.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 800))
    feedback.value.updateLoading({ currentStep: i + 1 })
  }
  
  feedback.value.hideLoading()
  feedback.value.success({
    title: '处理完成',
    message: '所有任务已成功完成',
    duration: 2000
  })
}

// 复杂操作
const handleComplexSubmit = async () => {
  // 验证
  if (!form.name || !form.email || !form.department) {
    feedback.value.error({
      title: '验证失败',
      message: '请填写所有必填字段',
      suggestions: ['姓名不能为空', '邮箱不能为空', '请选择部门']
    })
    return
  }
  
  // 提交
  feedback.value.loading({
    title: '提交中',
    steps: ['验证数据', '保存到数据库', '发送通知', '完成'],
    currentStep: 0
  })
  
  try {
    // 模拟步骤
    for (let i = 0; i < 4; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      feedback.value.updateLoading({ currentStep: i + 1 })
      
      // 模拟第三步失败
      if (i === 2 && Math.random() > 0.5) {
        throw new Error('发送通知失败')
      }
    }
    
    feedback.value.hideLoading()
    feedback.value.success({
      title: '提交成功',
      message: '员工信息已成功录入系统',
      details: [
        `员工姓名：${form.name}`,
        `电子邮箱：${form.email}`,
        `所属部门：${form.department}`,
        '账号已创建并发送到邮箱'
      ],
      actions: [
        {
          text: '继续添加',
          type: 'primary',
          handler: () => {
            handleComplexReset()
            feedback.value.toast({ type: 'info', message: '请继续添加下一个员工' })
          }
        },
        {
          text: '查看列表',
          handler: () => {
            feedback.value.toast({ type: 'info', message: '跳转到员工列表' })
          }
        }
      ]
    })
  } catch (__error) {
    feedback.value.hideLoading()
    feedback.value.error({
      title: '提交失败',
      message: '在处理您的请求时发生错误',
      error: {
        detail: error.message
      },
      showRetry: true,
      onRetry: handleComplexSubmit
    })
  }
}

const handleComplexReset = () => {
  form.name = ''
  form.email = ''
  form.department = ''
  feedback.value.toast({ type: 'info', message: '表单已重置' })
}
</script>

<style lang="scss" scoped>
.operation-feedback-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
    
    .feedback-group {
      h4 {
        margin: 0 0 12px;
        font-size: 16px;
        color: var(--el-text-color-primary);
      }
    }
    
    h5 {
      margin: 0 0 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .el-card {
      height: 100%;
      
      &.is-never-shadow {
        border: 1px solid var(--el-border-color-lighter);
      }
    }
  }
  
  .complex-demo {
    h4 {
      margin: 0 0 8px;
      font-size: 16px;
      color: var(--el-text-color-primary);
    }
    
    p {
      margin: 0 0 20px;
      color: var(--el-text-color-regular);
    }
  }
}
</style>