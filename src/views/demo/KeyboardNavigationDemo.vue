<template>
  <div class="keyboard-navigation-demo">
    <hr-page-header title="键盘导航支持演示" :show-back="true">
      演示Tab键导航、方向键导航、快捷键等功能
    </hr-page-header>

    <!-- 基础Tab导航 -->
    <el-card class="demo-section">
      <template #header>
        <span>基础Tab键导航</span>
      </template>
      
      <div 
        v-keyboard="{ 
          enableArrows: false,
          activeClass: 'tab-active',
          focusClass: 'tab-focus'
        }"
        class="tab-navigation-demo"
      >
        <el-alert 
          type="info" 
          :closable="false"
          show-icon
        >
          使用Tab键在按钮之间导航，Shift+Tab反向导航
        </el-alert>
        
        <div class="button-group">
          <el-button data-keyboard-item>按钮 1</el-button>
          <el-button data-keyboard-item type="primary">按钮 2</el-button>
          <el-button data-keyboard-item type="success">按钮 3</el-button>
          <el-button data-keyboard-item type="warning">按钮 4</el-button>
          <el-button data-keyboard-item type="danger">按钮 5</el-button>
        </div>
      </div>
    </el-card>

    <!-- 方向键导航 -->
    <el-card class="demo-section">
      <template #header>
        <span>方向键导航列表</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>垂直列表</h4>
          <div 
            v-keyboard="{
              onSelect: handleListSelect,
              onNavigate: handleListNavigate
            }"
            class="vertical-list"
          >
            <div 
              v-for="item in listItems" 
              :key="item.id"
              class="keyboard-item list-item"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.label }}</span>
              <el-tag size="small">{{ item.tag }}</el-tag>
            </div>
          </div>
        </el-col>
        
        <el-col :span="12">
          <h4>网格布局</h4>
          <div 
            v-keyboard="{
              loop: false,
              onSelect: handleGridSelect
            }"
            class="grid-layout"
          >
            <div 
              v-for="i in 9" 
              :key="i"
              class="keyboard-item grid-item"
            >
              <el-icon :size="32"><Grid /></el-icon>
              <span>项目 {{ i }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <el-divider   />
      
      <div class="navigation-tips">
        <el-tag>↑↓←→ 方向键导航</el-tag>
        <el-tag>Home 跳到开始</el-tag>
        <el-tag>End 跳到结尾</el-tag>
        <el-tag>Enter/Space 选择</el-tag>
        <el-tag>ESC 退出</el-tag>
      </div>
    </el-card>

    <!-- 快捷键支持 -->
    <el-card class="demo-section">
      <template #header>
        <span>快捷键支持</span>
      </template>
      
      <div 
        v-shortcuts="globalShortcuts"
        tabindex="0"
        class="shortcuts-demo"
      >
        <el-alert type="success" :closable="false">
          聚焦此区域后可使用快捷键
        </el-alert>
        
        <div class="shortcut-list">
          <div v-for="(shortcut, key) in shortcutList" :key="key" class="shortcut-item">
            <kbd>{{ formatShortcut(key) }}</kbd>
            <span>{{ shortcut }}</span>
          </div>
        </div>
        
        <div class="shortcut-result">
          <h4>操作结果</h4>
          <el-input 
            v-model="shortcutResult" 
            type="textarea" 
            :rows="4" 
            readonly 
            />
        </div>
      </div>
    </el-card>

    <!-- 焦点陷阱 -->
    <el-card class="demo-section">
      <template #header>
        <div class="card-header">
          <span>焦点陷阱（模态框）</span>
          <el-button type="primary" size="small" @click="showModal = true">
            打开模态框
          </el-button>
        </div>
      </template>
      
      <p>焦点陷阱确保用户在模态框内使用Tab键时，焦点不会离开模态框。</p>
      
      <Teleport to="body">
        <Transition name="modal">
          <div v-if="showModal" class="modal-overlay" @click.self="showModal = false">
            <div v-focus-trap class="modal-content">
              <h3>模态框示例</h3>
              <p>使用Tab键在表单元素间导航，焦点会被限制在模态框内。</p>
              
              <el-form label-width="80px">
                <el-form-item label="姓名">
                  <el-input placeholder="请输入姓名"   />
                </el-form-item>
                <el-form-item label="邮箱">
                  <el-input placeholder="请输入邮箱"   />
                </el-form-item>
                <el-form-item label="描述">
                  <el-input type="textarea" placeholder="请输入描述"   />
                </el-form-item>
              </el-form>
              
              <div class="modal-footer">
                <el-button @click="showModal = false">取消</el-button>
                <el-button type="primary" @click="handleModalSubmit">确定</el-button>
              </div>
            </div>
          </div>
        </Transition>
      </Teleport>
    </el-card>

    <!-- 表单键盘优化 -->
    <el-card class="demo-section">
      <template #header>
        <span>表单键盘导航优化</span>
      </template>
      
      <el-form 
        v-keyboard="{
          itemSelector: '.el-form-item__content > *:first-child',
          enableArrows: true,
          shortcuts: {
            'ctrl+s': handleFormSave,
            'ctrl+enter': handleFormSubmit
          }
        }"
        :model="form"
        label-width="100px"
        class="keyboard-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名">
              <el-input v-model="form.username" placeholder="使用方向键导航"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱">
              <el-input v-model="form.email" placeholder="按Enter跳到下一个"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门">
              <el-select v-model="form.department" placeholder="请选择">
                <el-option label="技术部" value="tech"  />
                <el-option label="产品部" value="product"  />
                <el-option label="市场部" value="market"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位">
              <el-select v-model="form.position" placeholder="请选择">
                <el-option label="工程师" value="engineer"  />
                <el-option label="经理" value="manager"  />
                <el-option label="主管" value="supervisor"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input 
                v-model="form.remark" 
                type="textarea" 
                placeholder="Ctrl+S 保存 | Ctrl+Enter 提交" 
                />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="handleFormSubmit">提交</el-button>
          <el-button @click="handleFormReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 可访问性增强 -->
    <el-card class="demo-section">
      <template #header>
        <span>可访问性增强</span>
      </template>
      
      <div class="accessibility-demo">
        <h4>ARIA属性示例</h4>
        
        <div 
          v-keyboard
          role="listbox"
          aria-label="选择你喜欢的水果"
          class="aria-list"
        >
          <div 
            v-for="(fruit, index) in fruits" 
            :key="fruit"
            class="keyboard-item aria-item"
            role="option"
            :aria-selected="selectedFruit === fruit"
            @click="selectedFruit = fruit"
          >
            <el-icon v-if="selectedFruit === fruit"><Check /></el-icon>
            <span>{{ fruit }}</span>
          </div>
        </div>
        
        <el-divider   />
        
        <h4>跳转链接（Skip Links）</h4>
        <div class="skip-links">
          <a href="#main-content" class="skip-link">跳到主要内容</a>
          <a href="#navigation" class="skip-link">跳到导航</a>
          <a href="#search" class="skip-link">跳到搜索</a>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Grid, 
  Document, 
  FolderOpened, 
  Setting, 
  User,
  Message,
  Check
} from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import { vKeyboard, vShortcuts, vFocusTrap } from '@/directives/keyboard'

// 列表数据
const listItems = [
  { id: 1, label: '文档管理', icon: Document, tag: 'DOC' },
  { id: 2, label: '文件夹', icon: FolderOpened, tag: 'DIR' },
  { id: 3, label: '系统设置', icon: Setting, tag: 'SYS' },
  { id: 4, label: '用户管理', icon: User, tag: 'USER' },
  { id: 5, label: '消息中心', icon: Message, tag: 'MSG' }
]

// 快捷键列表
const shortcutList = {
  'ctrl+n': '新建',
  'ctrl+s': '保存',
  'ctrl+o': '打开',
  'ctrl+f': '查找',
  'ctrl+z': '撤销',
  'ctrl+shift+z': '重做',
  'alt+1': '切换到标签1',
  'alt+2': '切换到标签2'
}

// 全局快捷键
const globalShortcuts = {
  'ctrl+n': () => addShortcutResult('执行了新建操作'),
  'ctrl+s': () => addShortcutResult('执行了保存操作'),
  'ctrl+o': () => addShortcutResult('执行了打开操作'),
  'ctrl+f': () => addShortcutResult('执行了查找操作'),
  'ctrl+z': () => addShortcutResult('执行了撤销操作'),
  'ctrl+shift+z': () => addShortcutResult('执行了重做操作'),
  'alt+1': () => addShortcutResult('切换到标签1'),
  'alt+2': () => addShortcutResult('切换到标签2')
}

// 状态
const showModal = ref(false)
const shortcutResult = ref('')
const selectedFruit = ref('')

// 表单数据
const form = reactive({
  username: '',
  email: '',
  department: '',
  position: '',
  remark: ''
})

// 水果列表
const fruits = ['苹果', '香蕉', '橙子', '葡萄', '西瓜', '芒果']

// 格式化快捷键显示
const formatShortcut = (shortcut: string): string => {
  return shortcut
    .split('+')
    .map(key => key.charAt(0).toUpperCase() + key.slice(1))
    .join(' + ')
}

// 添加快捷键结果
const addShortcutResult = (message: string) => {
  const time = new Date().toLocaleTimeString()
  shortcutResult.value += `[${time}] ${message}\n`
  ElMessage.success(message)
}

// 列表选择处理
const handleListSelect = (element: HTMLElement, index: number) => {
  const item = listItems[index]
  ElMessage.info(`选择了：${item.label}`)
}

// 列表导航处理
const handleListNavigate = (element: HTMLElement, index: number) => {
  console.log('导航到索引:', index)
}

// 网格选择处理
const handleGridSelect = (element: HTMLElement, index: number) => {
  ElMessage.info(`选择了网格项目 ${index + 1}`)
}

// 模态框提交
const handleModalSubmit = () => {
  ElMessage.success('模态框提交成功')
  showModal.value = false
}

// 表单保存
const handleFormSave = () => {
  ElMessage.success('表单已保存（Ctrl+S）')
}

// 表单提交
const handleFormSubmit = () => {
  ElMessage.success('表单已提交')
}

// 表单重置
const handleFormReset = () => {
  Object.keys(form).forEach(key => {
    form[key as keyof typeof form] = ''
  })
  ElMessage.info('表单已重置')
}
</script>

<style lang="scss" scoped>
.keyboard-navigation-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
  }
  
  // Tab导航演示
  .tab-navigation-demo {
    .button-group {
      margin-top: 20px;
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
    
    .tab-active {
      box-shadow: 0 0 0 2px var(--el-color-primary) !important;
    }
    
    .tab-focus {
      outline: 2px solid var(--el-color-primary);
      outline-offset: 2px;
    }
  }
  
  // 列表样式
  .vertical-list {
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    
    .list-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      cursor: pointer;
      transition: all 0.3s;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background: var(--el-fill-color-light);
      }
      
      &.keyboard-active {
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      }
      
      &:focus {
        outline: 2px solid var(--el-color-primary);
        outline-offset: -2px;
      }
      
      .el-icon {
        font-size: 20px;
      }
      
      span {
        flex: 1;
      }
    }
  }
  
  // 网格布局
  .grid-layout {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    
    .grid-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 20px;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        background: var(--el-fill-color-light);
      }
      
      &.keyboard-active {
        background: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }
      
      &:focus {
        outline: 2px solid var(--el-color-primary);
        outline-offset: 2px;
      }
    }
  }
  
  // 导航提示
  .navigation-tips {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  // 快捷键演示
  .shortcuts-demo {
    &:focus {
      outline: 2px dashed var(--el-color-primary);
      outline-offset: 4px;
    }
    
    .shortcut-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      margin: 20px 0;
      
      .shortcut-item {
        display: flex;
        align-items: center;
        gap: 12px;
        
        kbd {
          padding: 4px 8px;
          background: var(--el-fill-color);
          border: 1px solid var(--el-border-color);
          border-radius: 4px;
          font-family: monospace;
          font-size: 12px;
          box-shadow: 0 2px 0 var(--el-border-color-darker);
        }
      }
    }
    
    .shortcut-result {
      margin-top: 20px;
      
      h4 {
        margin-bottom: 12px;
      }
    }
  }
  
  // 模态框
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
  }
  
  .modal-content {
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 24px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
    
    h3 {
      margin: 0 0 16px;
    }
    
    p {
      margin-bottom: 20px;
      color: var(--el-text-color-regular);
    }
    
    .modal-footer {
      margin-top: 24px;
      text-align: right;
    }
  }
  
  // 表单键盘优化
  .keyboard-form {
    &:focus-within {
      .el-form-item__content > *:focus {
        box-shadow: 0 0 0 2px var(--el-color-primary-light-7);
      }
    }
  }
  
  // 可访问性演示
  .accessibility-demo {
    h4 {
      margin: 20px 0 12px;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    .aria-list {
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      overflow: hidden;
      
      .aria-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        cursor: pointer;
        transition: all 0.3s;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          background: var(--el-fill-color-light);
        }
        
        &[aria-selected="true"] {
          background: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
        }
        
        &:focus {
          outline: 2px solid var(--el-color-primary);
          outline-offset: -2px;
        }
        
        .el-icon {
          width: 16px;
          flex-shrink: 0;
        }
      }
    }
    
    .skip-links {
      display: flex;
      gap: 16px;
      
      .skip-link {
        position: absolute;
        left: -9999px;
        top: 0;
        padding: 8px 16px;
        background: var(--el-color-primary);
        color: #fff;
        text-decoration: none;
        border-radius: 4px;
        
        &:focus {
          position: static;
          left: auto;
        }
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// 模态框过渡
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s;
  
  .modal-content {
    transition: transform 0.3s;
  }
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  
  .modal-content {
    transform: scale(0.9);
  }
}
</style>