<template>
  <div class="file-upload-demo">
    <h1>文件上传组件演示 - 拖拽增强版</h1>
    
    <!-- 基础上传 -->
    <el-card>
      <template #header>
        <span>基础文件上传</span>
      </template>
      
      <FileUpload
        v-model="basicFile"
        :upload-url="uploadUrl"
        :headers="headers"
        :max-size="10 * 1024 * 1024"
        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
        tip="支持 jpg/png/pdf/doc 格式，文件大小不超过 10MB"
        @success="handleBasicSuccess"
        @error="handleBasicError"
      />
      
      <div class="result" v-if="basicFile">
        <p>上传结果：{{ basicFile }}</p>
      </div>
    </el-card>
    
    <!-- 多文件上传 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>多文件上传</span>
      </template>
      
      <FileUpload
        v-model="multipleFiles"
        :upload-url="uploadUrl"
        :headers="headers"
        :multiple="true"
        :limit="5"
        :max-size="20 * 1024 * 1024"
        accept=".jpg,.jpeg,.png,.pdf"
        tip="最多上传5个文件，每个文件不超过 20MB"
        @success="handleMultipleSuccess"
      />
      
      <div class="result" v-if="multipleFiles.length > 0">
        <p>上传结果：</p>
        <ul>
          <li v-for="(file, index) in multipleFiles" :key="index">{{ file }}</li>
        </ul>
      </div>
    </el-card>
    
    <!-- 大文件分片上传 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>大文件分片上传</span>
      </template>
      
      <FileUpload
        ref="bigFileUploadRef"
        v-model="bigFile"
        :upload-url="chunkUploadUrl"
        :headers="headers"
        :max-size="500 * 1024 * 1024"
        :chunk-upload="true"
        :chunk-size="5 * 1024 * 1024"
        :concurrent="3"
        :auto-upload="false"
        accept=".zip,.rar,.mp4,.avi"
        tip="支持上传最大 500MB 的文件，自动分片上传"
        @success="handleBigFileSuccess"
        @progress="handleProgress"
      />
      
      <div class="result" v-if="uploadProgress > 0">
        <p>总体进度：{{ uploadProgress }}%</p>
      </div>
    </el-card>
    
    <!-- 入职材料上传示例 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>入职材料上传（实际业务场景）</span>
      </template>
      
      <el-form :model="onboardingForm" label-width="120px">
        <el-form-item label="身份证正面">
          <FileUpload
            v-model="onboardingForm.idCardFront"
            :upload-url="uploadUrl"
            :headers="headers"
            :max-size="5 * 1024 * 1024"
            :drag="false"
            accept="image/*"
            tip="请上传身份证正面照片，支持 jpg/png 格式"
          />
        </el-form-item>
        
        <el-form-item label="身份证反面">
          <FileUpload
            v-model="onboardingForm.idCardBack"
            :upload-url="uploadUrl"
            :headers="headers"
            :max-size="5 * 1024 * 1024"
            :drag="false"
            accept="image/*"
            tip="请上传身份证反面照片，支持 jpg/png 格式"
          />
        </el-form-item>
        
        <el-form-item label="学历证书">
          <FileUpload
            v-model="onboardingForm.educationCert"
            :upload-url="uploadUrl"
            :headers="headers"
            :max-size="10 * 1024 * 1024"
            :drag="false"
            accept=".pdf,.jpg,.jpeg,.png"
            tip="请上传最高学历证书扫描件"
          />
        </el-form-item>
        
        <el-form-item label="其他材料">
          <FileUpload
            v-model="onboardingForm.otherFiles"
            :upload-url="uploadUrl"
            :headers="headers"
            :multiple="true"
            :limit="10"
            :max-size="20 * 1024 * 1024"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            tip="可上传工作证明、资格证书等其他材料，最多10个文件"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSubmitOnboarding">
            提交材料
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'FileUploadDemo'
})
 
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import HrFileUpload from '@/components/common/HrFileUpload.vue'

// 基础上传
const basicFile = ref('')

// 多文件上传
const multipleFiles = ref<string[]>([])

// 大文件上传
const bigFile = ref('')
const bigFileUploadRef = ref()
const uploadProgress = ref(0)

// 入职材料表单
const onboardingForm = reactive({
  idCardFront: '',
  idCardBack: '',
  educationCert: '',
  otherFiles: [] as string[]
})

// 上传配置
const uploadUrl = '/api/upload/file'
const chunkUploadUrl = '/api/upload'
const headers = {
  'Authorization': 'Bearer ' + localStorage.getItem('token')
}

// 基础上传成功
   
const handleBasicSuccess = (response: unknown, file: unknown, fileList: unknown) => {
  ElMessage.success('文件上传成功')
  console.log('上传成功:', response)
}

// 基础上传失败
   
const handleBasicError = (error: unknown, file: unknown, fileList: unknown) => {
  ElMessage.error('文件上传失败')
  console.error('上传失败:', error)
}

// 多文件上传成功
   
const handleMultipleSuccess = (response: unknown, file: unknown, fileList: unknown) => {
  ElMessage.success(`文件 ${file.name} 上传成功`)
}

// 大文件上传成功
   
const handleBigFileSuccess = (response: unknown, file: unknown, fileList: unknown) => {
  ElMessage.success('大文件上传成功')
  uploadProgress.value = 100
}

// 上传进度
   
const handleProgress = (event: unknown, file: unknown, fileList: unknown) => {
  uploadProgress.value = Math.round(event.percent)
}

// 提交入职材料
const handleSubmitOnboarding = () => {
  console.log('提交的材料:', onboardingForm)
  
  // 验证必填材料
  if (!onboardingForm.idCardFront || !onboardingForm.idCardBack) {
    ElMessage.error('请上传身份证正反面')
    return
  }
  
  if (!onboardingForm.educationCert) {
    ElMessage.error('请上传学历证书')
    return
  }
  
  ElMessage.success('材料提交成功')
}
</script>

<style scoped lang="scss">
.file-upload-demo {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
  }
  
  .el-card {
    .result {
      margin-top: 20px;
      padding: 10px;
      background: #f5f7fa;
      border-radius: 4px;
      
      p {
        margin: 0 0 10px;
        font-weight: bold;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin: 5px 0;
        }
      }
    }
  }
}
</style>