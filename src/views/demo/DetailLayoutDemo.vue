<template>
  <div class="detail-layout-demo">
    <hr-page-header title="详情页面布局演示" :show-back="true">
      演示信息分组展示、快速导航、卡片布局等功能
    </hr-page-header>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleEdit">编辑</el-button>
      <el-button @click="handlePrint">打印</el-button>
      <el-button @click="handleExport">导出</el-button>
      <el-dropdown trigger="click" @command="handleCommand">
        <el-button>
          更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="history">查看变更历史</el-dropdown-item>
            <el-dropdown-item command="duplicate">复制信息</el-dropdown-item>
            <el-dropdown-item command="archive">归档</el-dropdown-item>
            <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 详情布局组件 -->
    <EmployeeDetailLayout
      :employee="mockEmployee"
      :education-history="mockEducation"
      :work-experience="mockWorkExperience"
      :family-members="mockFamilyMembers"
      :show-actions="showActions"
      @edit="handleEditSection"
      @add="handleAddItem"
      @edit-item="handleEditItem"
      @delete-item="handleDeleteItem"
    >
      <!-- 合同信息插槽 -->
      <template #contract>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="合同编号">HT-2024-0001</el-descriptions-item>
          <el-descriptions-item label="合同类型">固定期限劳动合同</el-descriptions-item>
          <el-descriptions-item label="合同期限">3年</el-descriptions-item>
          <el-descriptions-item label="签订日期">2024-01-01</el-descriptions-item>
          <el-descriptions-item label="合同起始日">2024-01-01</el-descriptions-item>
          <el-descriptions-item label="合同终止日">2026-12-31</el-descriptions-item>
          <el-descriptions-item label="合同状态">
            <el-tag type="success">生效中</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="剩余天数">
            <span style="color: #67C23A;">365天</span>
          </el-descriptions-item>
        </el-descriptions>
      </template>

      <!-- 考核记录插槽 -->
      <template #assessment>
        <el-table :data="mockAssessments" style="width: 100%">
          <el-table-column prop="year" label="年度" width="100"  />
          <el-table-column prop="type" label="考核类型" width="120"  />
          <el-table-column prop="result" label="考核结果" width="120">
            <template #default="{ row }">
              <el-tag :type="getAssessmentType(row.result)">
                {{ row.result }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="考核分数" width="100"  />
          <el-table-column prop="rank" label="部门排名" width="100"  />
          <el-table-column prop="remark" label="备注"  />
        </el-table>
      </template>

      <!-- 奖惩记录插槽 -->
      <template #rewards>
        <el-timeline>
          <el-timeline-item
            v-for="item in mockRewards"
            :key="item.id"
            :timestamp="item.date"
            :type="item.type === 'reward' ? 'success' : 'danger'"
            placement="top"
          >
            <el-card>
              <h4>{{ item.title }}</h4>
              <p>{{ item.description }}</p>
              <el-tag :type="item.type === 'reward' ? 'success' : 'danger'" size="small">
                {{ item.type === 'reward' ? '奖励' : '处罚' }}
              </el-tag>
              <el-tag size="small" style="margin-left: 8px;">
                {{ item.level }}
              </el-tag>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </template>

      <!-- 人才项目插槽 -->
      <template #talent>
        <el-row :gutter="16">
          <el-col v-for="item in mockTalentProjects" :key="item.id" :span="12">
            <el-card class="talent-card">
              <div class="talent-header">
                <el-icon :size="24" color="#E6A23C"><Trophy /></el-icon>
                <h4>{{ item.name }}</h4>
              </div>
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="项目级别">{{ item.level }}</el-descriptions-item>
                <el-descriptions-item label="获批时间">{{ item.date }}</el-descriptions-item>
                <el-descriptions-item label="资助金额">{{ item.funding }}</el-descriptions-item>
                <el-descriptions-item label="项目状态">
                  <el-tag :type="item.status === 'active' ? 'success' : 'info'" size="small">
                    {{ item.status === 'active' ? '进行中' : '已结题' }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
      </template>

      <!-- 附件资料插槽 -->
      <template #attachments>
        <div class="attachment-list">
          <div v-for="item in mockAttachments" :key="item.id" class="attachment-item">
            <div class="attachment-icon">
              <el-icon :size="32" :color="getFileIconColor(item.type)">
                <component :is="getFileIcon(item.type)" />
              </el-icon>
            </div>
            <div class="attachment-info">
              <h5>{{ item.name }}</h5>
              <div class="attachment-meta">
                <span>{{ item.size }}</span>
                <span>{{ item.uploadTime }}</span>
                <span>{{ item.uploader }}</span>
              </div>
            </div>
            <div class="attachment-actions">
              <el-button size="small" link type="primary" @click="handlePreview(item)">
                预览
              </el-button>
              <el-button size="small" link type="primary" @click="handleDownload(item)">
                下载
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </EmployeeDetailLayout>

    <!-- 功能说明 -->
    <el-card class="feature-card">
      <template #header>
        <span>功能特性</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <h4>信息分组展示</h4>
          <ul>
            <li>基本信息、工作信息、教育背景等分组展示</li>
            <li>每组信息独立卡片，层次清晰</li>
            <li>重要信息突出显示，次要信息折叠</li>
            <li>支持自定义分组和排序</li>
          </ul>
        </el-col>
        <el-col :span="8">
          <h4>快速导航</h4>
          <ul>
            <li>顶部固定导航栏，快速定位</li>
            <li>滚动自动高亮当前区域</li>
            <li>显示各区域数据条数</li>
            <li>支持键盘快捷键导航</li>
          </ul>
        </el-col>
        <el-col :span="8">
          <h4>交互优化</h4>
          <ul>
            <li>分组内容支持展开/折叠</li>
            <li>行内编辑和删除操作</li>
            <li>响应式布局适配</li>
            <li>打印样式优化</li>
          </ul>
        </el-col>
      </el-row>
    </el-card>

    <!-- 配置选项 -->
    <el-card class="config-card">
      <template #header>
        <span>配置选项</span>
      </template>
      
      <el-form label-width="120px">
        <el-form-item label="显示操作按钮">
          <el-switch v-model="showActions"  />
        </el-form-item>
        <el-form-item label="布局模式">
          <el-radio-group v-model="layoutMode">
            <el-radio label="default">默认</el-radio>
            <el-radio label="compact">紧凑</el-radio>
            <el-radio label="comfortable">宽松</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="主题风格">
          <el-select v-model="theme" placeholder="选择主题">
            <el-option label="默认" value="default"  />
            <el-option label="简约" value="minimal"  />
            <el-option label="彩色" value="colorful"  />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Trophy, Document, Picture, VideoPlay } from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrEmployeeDetailLayout from '@/components/employee/HrEmployeeDetailLayout.vue'

// 配置状态
const showActions = ref(true)
const layoutMode = ref('default')
const theme = ref('default')

// 模拟数据
const mockEmployee = {
  employeeId: 'EMP001',
  fullName: '张三',
  employeeNumber: 'EMP001',
  gender: 'male',
  dateOfBirth: '1990-01-01',
  ethnicity: '汉族',
  politicalStatus: '中共党员',
  idType: 'idCard',
  idNumber: '330106199001010001',
  nativePlace: '浙江杭州',
  placeOfBirth: '浙江杭州',
  phoneNumber: '13800138000',
  email: '<EMAIL>',
  contactAddress: '浙江省杭州市西湖区某某路123号',
  postalCode: '310000',
  photoUrl: '',
  institutionName: '技术研发部',
  positionName: '高级工程师',
  jobLevel: 'P7',
  workStartDate: '2012-07-01',
  hireDate: '2015-03-15',
  regularDate: '2015-06-15',
  personnelStatus: 'active',
  highLevelTalentCategory: 'B',
  infoCompletenessPercentage: 85
}

const mockEducation = [
  {
    recordId: '1',
    startDate: '2008-09',
    endDate: '2012-07',
    graduationSchool: '浙江大学',
    degree: '本科',
    major: '计算机科学与技术',
    studyForm: '全日制'
  },
  {
    recordId: '2',
    startDate: '2012-09',
    endDate: '2015-07',
    graduationSchool: '浙江大学',
    degree: '硕士研究生',
    major: '软件工程',
    studyForm: '全日制'
  }
]

const mockWorkExperience = [
  {
    recordId: '1',
    startDate: '2015-03',
    endDate: null,
    companyName: '杭州科技大学',
    positionHeld: '高级工程师',
    salaryGrade: 'T7',
    jobDescription: '负责核心系统架构设计和技术团队管理'
  },
  {
    recordId: '2',
    startDate: '2012-07',
    endDate: '2015-02',
    companyName: '某科技有限公司',
    positionHeld: '软件工程师',
    salaryGrade: 'P5',
    jobDescription: '参与企业级应用开发'
  }
]

const mockFamilyMembers = [
  {
    recordId: '1',
    fullName: '李四',
    relationship: '配偶',
    dateOfBirth: '1992-05-15',
    politicalStatus: '群众',
    workUnitAndPosition: '某公司 会计'
  },
  {
    recordId: '2',
    fullName: '张小明',
    relationship: '子女',
    dateOfBirth: '2018-10-20',
    politicalStatus: '群众',
    workUnitAndPosition: '某小学 学生'
  }
]

const mockAssessments = [
  {
    year: '2023',
    type: '年度考核',
    result: '优秀',
    score: 95,
    rank: '3/50'
  },
  {
    year: '2022',
    type: '年度考核',
    result: '优秀',
    score: 92,
    rank: '5/48'
  },
  {
    year: '2021',
    type: '年度考核',
    result: '合格',
    score: 85,
    rank: '12/45'
  }
]

const mockRewards = [
  {
    id: '1',
    date: '2023-12-01',
    type: 'reward',
    title: '优秀员工奖',
    description: '表彰在2023年度工作中的突出贡献',
    level: '校级'
  },
  {
    id: '2',
    date: '2022-06-15',
    type: 'reward',
    title: '技术创新奖',
    description: '在教学管理系统优化项目中的创新贡献',
    level: '部门级'
  }
]

const mockTalentProjects = [
  {
    id: '1',
    name: 'HrHr青年人才培养计划',
    level: '省级',
    date: '2022-01-01',
    funding: '50万元',
    status: 'active'
  },
  {
    id: '2',
    name: '优秀教师支持计划',
    level: '校级',
    date: '2021-09-01',
    funding: '20万元',
    status: 'completed'
  }
]

const mockAttachments = [
  {
    id: '1',
    name: '身份证扫描件.pdf',
    type: 'pdf',
    size: '2.3MB',
    uploadTime: '2024-01-15',
    uploader: '张三'
  },
  {
    id: '2',
    name: '学历证明.jpg',
    type: 'image',
    size: '1.5MB',
    uploadTime: '2024-01-15',
    uploader: '张三'
  },
  {
    id: '3',
    name: '职称证书.docx',
    type: 'word',
    size: '856KB',
    uploadTime: '2024-01-16',
    uploader: '人事专员'
  }
]

// 处理操作
const handleEdit = () => {
  ElMessage.info('编辑员工信息')
}

const handlePrint = () => {
  window.print()
}

const handleExport = () => {
  ElMessage.success('导出成功')
}

const handleCommand = (command: string) => {
  ElMessage.info(`执行操作: ${command}`)
}

const handleEditSection = (section: string) => {
  ElMessage.info(`编辑${section}信息`)
}

const handleAddItem = (section: string) => {
  ElMessage.info(`新增${section}记录`)
}

   
const handleEditItem = (section: string, item: unknown) => {
  ElMessage.info(`编辑${section}记录: ${item.recordId}`)
}

   
const handleDeleteItem = async (section: string, item: unknown) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
  } catch {
    // 取消
  }
}

   
const handlePreview = (item: unknown) => {
  ElMessage.info(`预览文件: ${item.name}`)
}

   
const handleDownload = (item: unknown) => {
  ElMessage.success(`下载文件: ${item.name}`)
}

// 工具函数
const getAssessmentType = (result: string) => {
  const typeMap: Record<string, string> = {
    '优秀': 'success',
    '合格': '',
    '基本合格': 'warning',
    '不合格': 'danger'
  }
  return typeMap[result] || ''
}

const getFileIcon = (type: string) => {
  const iconMap: Record<string, unknown> = {
    pdf: Document,
    image: Picture,
    word: Document,
    video: VideoPlay
  }
  return iconMap[type] || Document
}

const getFileIconColor = (type: string) => {
  const colorMap: Record<string, string> = {
    pdf: '#FF6B6B',
    image: '#4ECDC4',
    word: '#3498DB',
    video: '#9B59B6'
  }
  return colorMap[type] || '#95A5A6'
}
</script>

<style lang="scss" scoped>
.detail-layout-demo {
  padding: 20px;
  
  .action-bar {
    margin: 20px 0;
    display: flex;
    gap: 12px;
  }
  
  .feature-card,
  .config-card {
    margin-top: 20px;
    
    h4 {
      margin: 0 0 12px;
      font-size: 16px;
      color: var(--el-text-color-primary);
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: var(--el-text-color-regular);
      }
    }
  }
  
  .talent-card {
    margin-bottom: 16px;
    
    .talent-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        font-size: 16px;
      }
    }
  }
  
  .attachment-list {
    .attachment-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      background: var(--el-fill-color-light);
      border-radius: 4px;
      margin-bottom: 12px;
      
      .attachment-icon {
        flex-shrink: 0;
      }
      
      .attachment-info {
        flex: 1;
        
        h5 {
          margin: 0 0 4px;
          font-size: 14px;
          font-weight: 500;
        }
        
        .attachment-meta {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
      
      .attachment-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

@media print {
  .action-bar,
  .feature-card,
  .config-card {
    display: none !important;
  }
}
</style>