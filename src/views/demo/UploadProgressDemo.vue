<template>
  <div class="upload-progress-demo">
    <h1>批量上传进度管理演示</h1>
    
    <!-- 批量上传区域 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>批量文件上传</h3>
          <el-button type="primary" @click="showProgressManager = true">
            <el-icon><DataAnalysis /></el-icon>
            打开上传管理器
          </el-button>
        </div>
      </template>
      
      <div class="demo-content">
        <FileUpload
          v-model="uploadedFiles"
          upload-url="/api/upload"
          :headers="uploadHeaders"
          :multiple="true"
          :limit="20"
          :auto-upload="false"
          :chunk-upload="true"
          :chunk-size="2 * 1024 * 1024"
          :concurrent="3"
          accept="*"
          tip="支持批量上传，文件将自动分片上传（每片2MB），可在上传管理器中查看进度"
          @success="handleUploadSuccess"
          @error="handleUploadError"
          @progress="handleUploadProgress"
        />
        
        <el-divider   />
        
        <div class="demo-actions">
          <el-button type="primary" @click="simulateBatchUpload">
            <el-icon><Upload /></el-icon>
            模拟批量上传（10个文件）
          </el-button>
          
          <el-button @click="simulateLargeFileUpload">
            <el-icon><Document /></el-icon>
            模拟大文件上传
          </el-button>
          
          <el-button @click="simulateMixedUpload">
            <el-icon><Files /></el-icon>
            模拟混合上传
          </el-button>
        </div>
        
        <el-alert type="info" :closable="false" style="margin-top: 20px;">
          <template #title>
            <div class="feature-list">
              <h4>批量上传管理功能：</h4>
              <ul>
                <li>📊 <strong>实时进度监控</strong> - 查看每个文件的上传进度和速度</li>
                <li>⏸️ <strong>暂停/恢复控制</strong> - 支持单个或批量暂停/恢复</li>
                <li>🔄 <strong>失败重试</strong> - 自动或手动重试失败的文件</li>
                <li>📈 <strong>总体进度显示</strong> - 查看所有文件的整体上传进度</li>
                <li>🚀 <strong>并发控制</strong> - 控制同时上传的文件数量</li>
                <li>📋 <strong>任务管理</strong> - 清理已完成任务，取消进行中任务</li>
                <li>💾 <strong>断点续传</strong> - 大文件自动分片，支持断点续传</li>
                <li>🎯 <strong>优先级队列</strong> - 重要文件优先上传</li>
              </ul>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
    
    <!-- 上传统计 -->
    <el-card class="demo-card">
      <template #header>
        <h3>上传统计</h3>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总文件数" :value="totalFiles"  />
        </el-col>
        <el-col :span="6">
          <el-statistic title="上传中" :value="uploadingFiles"  />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已完成" :value="completedFiles"  />
        </el-col>
        <el-col :span="6">
          <el-statistic title="失败" :value="failedFiles"  />
        </el-col>
      </el-row>
      
      <el-divider   />
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic 
            title="总上传量" 
            :value="totalUploadSize" 
            suffix="MB"
            :precision="2"
           />
        </el-col>
        <el-col :span="8">
          <el-statistic 
            title="平均速度" 
            :value="averageSpeed" 
            suffix="MB/s"
            :precision="2"
           />
        </el-col>
        <el-col :span="8">
          <el-statistic 
            title="剩余时间" 
            :value="remainingTime" 
            suffix="分钟"
           />
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 上传日志 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>上传日志</h3>
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </template>
      
      <div class="upload-logs">
        <div
          v-for="(log, index) in uploadLogs"
          :key="index"
          class="log-item"
          :class="`log-${log.type}`"
        >
          <span class="log-time">{{ formatTime(log.time) }}</span>
          <el-tag :type="getLogTagType(log.type)" size="small">
            {{ log.type }}
          </el-tag>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <el-empty v-if="uploadLogs.length === 0" description="暂无上传日志"  />
      </div>
    </el-card>
    
    <!-- 上传进度管理器 -->
    <UploadProgressManager
      v-model="showProgressManager"
      :show-completed="true"
      :max-visible="50"
    />
  </div>
</template>

<script setup lang="ts">
 
import { ref, computed, reactive } from 'vue'
import HrFileUpload from '@/components/common/HrFileUpload.vue'
import HrUploadProgressManager from '@/components/common/HrUploadProgressManager.vue'
import { 
  Upload, 
  Document, 
  Files, 
  DataAnalysis 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { fileUploader, type UploadTask } from '@/utils/upload'

// 状态
const showProgressManager = ref(false)
const uploadedFiles = ref<string[]>([])
const uploadHeaders = {
  Authorization: 'Bearer ' + localStorage.getItem('token')
}

// 统计数据
const totalFiles = ref(0)
const uploadingFiles = ref(0)
const completedFiles = ref(0)
const failedFiles = ref(0)
const totalUploadSize = ref(0)
const averageSpeed = ref(0)
const remainingTime = ref(0)

// 上传日志
interface UploadLog {
  time: Date
  type: 'start' | 'progress' | 'success' | 'error' | 'pause' | 'resume'
  message: string
}

const uploadLogs = ref<UploadLog[]>([])

// 添加日志
const addLog = (type: UploadLog['type'], message: string) => {
  uploadLogs.value.unshift({
    time: new Date(),
    type,
    message
  })
  
  // 限制日志数量
  if (uploadLogs.value.length > 100) {
    uploadLogs.value = uploadLogs.value.slice(0, 100)
  }
}

// 清空日志
const clearLogs = () => {
  uploadLogs.value = []
}

// 格式化时间
const formatTime = (date: Date) => {
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')
  return `${hours}:${minutes}:${seconds}`
}

// 获取日志标签类型
const getLogTagType = (type: UploadLog['type']) => {
  const typeMap = {
    start: 'info',
    progress: '',
    success: 'success',
    error: 'danger',
    pause: 'warning',
    resume: 'primary'
  }
  return typeMap[type] || 'info'
}

// 处理上传成功
   
const handleUploadSuccess = (response: unknown, file: unknown) => {
  completedFiles.value++
  uploadingFiles.value--
  addLog('success', `文件 ${file.name} 上传成功`)
  updateStatistics()
}

// 处理上传失败
   
const handleUploadError = (error: unknown, file: unknown) => {
  failedFiles.value++
  uploadingFiles.value--
  addLog('error', `文件 ${file.name} 上传失败: ${error.message}`)
  updateStatistics()
}

// 处理上传进度
   
const handleUploadProgress = (event: unknown, file: unknown) => {
  if (event.percent === 0) {
    uploadingFiles.value++
    totalFiles.value++
    addLog('start', `开始上传文件 ${file.name}`)
  }
  
  // 每10%记录一次进度
  if (event.percent % 10 === 0 && event.percent > 0 && event.percent < 100) {
    addLog('progress', `文件 ${file.name} 上传进度: ${event.percent}%`)
  }
  
  updateStatistics()
}

// 更新统计信息
const updateStatistics = () => {
  const tasks = fileUploader.getAllTasks()
  
  // 计算总上传量
  const totalBytes = tasks.reduce((sum, task) => {
    return sum + (task.fileSize * task.progress / 100)
  }, 0)
  totalUploadSize.value = totalBytes / (1024 * 1024)
  
  // 计算平均速度
  const uploadingTasks = tasks.filter(t => t.status === 'uploading')
  if (uploadingTasks.length > 0) {
    const totalSpeed = uploadingTasks.reduce((sum, task) => sum + task.speed, 0)
    averageSpeed.value = totalSpeed / (1024 * 1024)
  } else {
    averageSpeed.value = 0
  }
  
  // 计算剩余时间
  if (uploadingTasks.length > 0 && averageSpeed.value > 0) {
    const remainingBytes = tasks.reduce((sum, task) => {
      return sum + (task.fileSize * (100 - task.progress) / 100)
    }, 0)
    remainingTime.value = Math.round(remainingBytes / (averageSpeed.value * 1024 * 1024) / 60)
  } else {
    remainingTime.value = 0
  }
}

// 模拟批量上传
const simulateBatchUpload = async () => {
  showProgressManager.value = true
  
  for (let i = 1; i <= 10; i++) {
    const fileSize = Math.random() * 10 * 1024 * 1024 + 1024 * 1024 // 1MB-11MB
    const file = new File([new ArrayBuffer(fileSize)], `测试文件${i}.txt`, {
      type: 'text/plain'
    })
    
    const task = await fileUploader.createTask(file, {
      url: '/api/upload',
      headers: uploadHeaders,
      chunkSize: 2 * 1024 * 1024
    })
    
    // 模拟上传进度
    setTimeout(() => {
      simulateTaskProgress(task)
    }, i * 500)
  }
  
  ElMessage.info('已添加10个文件到上传队列')
}

// 模拟大文件上传
const simulateLargeFileUpload = async () => {
  showProgressManager.value = true
  
  const fileSize = 100 * 1024 * 1024 // 100MB
  const file = new File([new ArrayBuffer(fileSize)], '大文件.zip', {
    type: 'application/zip'
  })
  
  const task = await fileUploader.createTask(file, {
    url: '/api/upload',
    headers: uploadHeaders,
    chunkSize: 5 * 1024 * 1024
  })
  
  simulateTaskProgress(task)
  
  ElMessage.info('已添加100MB大文件到上传队列')
}

// 模拟混合上传
const simulateMixedUpload = async () => {
  showProgressManager.value = true
  
  const files = [
    { name: 'HrHr图片1.jpg', size: 2 * 1024 * 1024, type: 'image/jpeg' },
    { name: '图片2.png', size: 3 * 1024 * 1024, type: 'image/png' },
    { name: '文档.pdf', size: 5 * 1024 * 1024, type: 'application/pdf' },
    { name: '视频.mp4', size: 50 * 1024 * 1024, type: 'video/mp4' },
    { name: '压缩包.zip', size: 20 * 1024 * 1024, type: 'application/zip' }
  ]
  
  for (const fileInfo of files) {
    const file = new File([new ArrayBuffer(fileInfo.size)], fileInfo.name, {
      type: fileInfo.type
    })
    
    const task = await fileUploader.createTask(file, {
      url: '/api/upload',
      headers: uploadHeaders,
      chunkSize: 2 * 1024 * 1024
    })
    
    setTimeout(() => {
      simulateTaskProgress(task)
    }, Math.random() * 2000)
  }
  
  ElMessage.info('已添加5个不同类型文件到上传队列')
}

// 模拟任务进度
const simulateTaskProgress = (task: UploadTask) => {
  task.status = 'uploading'
  task.startTime = Date.now()
  
  const interval = setInterval(() => {
    if (task.status !== 'uploading') {
      clearInterval(interval)
      return
    }
    
    // 模拟进度增长
    task.progress = Math.min(task.progress + Math.random() * 10, 100)
    task.speed = Math.random() * 2 * 1024 * 1024 // 0-2MB/s
    
    const remainingBytes = task.fileSize * (100 - task.progress) / 100
    task.remainTime = task.speed > 0 ? remainingBytes / task.speed : 0
    
    if (task.progress >= 100) {
      task.status = 'success'
      task.progress = 100
      handleUploadSuccess({ url: `/files/${task.fileName}` }, { name: task.fileName })
      clearInterval(interval)
    }
    
    // 模拟随机错误
    if (Math.random() < 0.05) {
      task.status = 'error'
      handleUploadError(new Error('网络错误'), { name: task.fileName })
      clearInterval(interval)
    }
  }, 500)
}
</script>

<style lang="scss" scoped>
.upload-progress-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: #303133;
  }
  
  .demo-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }
  
  .demo-content {
    .demo-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
    
    .feature-list {
      h4 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #303133;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          line-height: 1.8;
          color: #606266;
          
          strong {
            color: #303133;
          }
        }
      }
    }
  }
  
  .upload-logs {
    max-height: 300px;
    overflow-y: auto;
    
    .log-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;
      font-size: 13px;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.log-error {
        background-color: #fef0f0;
      }
      
      &.log-success {
        background-color: #f0f9ff;
      }
      
      .log-time {
        color: #909399;
        font-family: monospace;
      }
      
      .log-message {
        flex: 1;
        color: #606266;
      }
    }
  }
}

// 统计卡片样式
:deep(.el-statistic) {
  text-align: center;
  
  .el-statistic__head {
    font-size: 13px;
    color: #909399;
    margin-bottom: 8px;
  }
  
  .el-statistic__content {
    font-size: 24px;
    color: #303133;
  }
}

@media (max-width: 768px) {
  .upload-progress-demo {
    padding: 10px;
    
    .demo-actions {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
    
    .el-row {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style>