<template>
  <div class="data-table-enhanced-demo">
    <h1>增强数据表格演示</h1>
    <p class="demo-desc">展示数据表格的高级功能，包括多选、排序、筛选、导出等功能</p>

    <!-- 基础表格 -->
    <section class="demo-section">
      <h2>基础表格</h2>
      <el-card>
        <template #header>
          <div class="card-header">
            <span>员工信息列表</span>
            <el-space>
              <el-button type="primary" size="small" @click="handleExport">导出数据</el-button>
              <el-button size="small" @click="handleRefresh">刷新</el-button>
            </el-space>
          </div>
        </template>
        
        <el-table
          ref="basicTableRef"
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="id" label="ID" width="80" sortable="custom"  />
          <el-table-column prop="name" label="姓名" width="120"  />
          <el-table-column prop="department" label="部门" width="120"  />
          <el-table-column prop="position" label="职位" width="120"  />
          <el-table-column prop="email" label="邮箱" min-width="180"  />
          <el-table-column prop="phone" label="电话" width="140"  />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'info'">
                {{ row.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" sortable="custom"  />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-space>
                <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
              </el-space>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
           />
        </div>
      </el-card>
    </section>

    <!-- 高级筛选 -->
    <section class="demo-section">
      <h2>高级筛选</h2>
      <el-card>
        <el-form :inline="true" :model="filterForm" class="demo-form-inline">
          <el-form-item label="姓名">
            <el-input v-model="filterForm.name" placeholder="请输入姓名" clearable   />
          </el-form-item>
          <el-form-item label="部门">
            <el-select v-model="filterForm.department" placeholder="请选择部门" clearable>
              <el-option label="技术部" value="tech"  />
              <el-option label="人事部" value="hr"  />
              <el-option label="财务部" value="finance"  />
              <el-option label="市场部" value="market"  />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
              <el-option label="在职" value="active"  />
              <el-option label="离职" value="inactive"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </section>

    <!-- 批量操作 -->
    <section class="demo-section">
      <h2>批量操作</h2>
      <el-card>
        <div class="batch-operations">
          <el-space>
            <span>已选择 {{ selectedRows.length }} 项</span>
            <el-button :disabled="selectedRows.length === 0" @click="handleBatchEdit">
              批量编辑
            </el-button>
            <el-button 
              type="danger" 
              :disabled="selectedRows.length === 0" 
              @click="handleBatchDelete"
            >
              批量删除
            </el-button>
            <el-button :disabled="selectedRows.length === 0" @click="handleBatchExport">
              导出选中
            </el-button>
          </el-space>
        </div>
      </el-card>
    </section>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 表格数据
const loading = ref(false)
const tableData = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const selectedRows = ref<any[]>([])
const basicTableRef = ref()

// 筛选表单
const filterForm = reactive({
  name: '',
  department: '',
  status: ''
})

// 模拟数据
const mockData = () => {
  const departments = ['技术部', '人事部', '财务部', '市场部']
  const positions = ['经理', '主管', '专员', '工程师']
  const statuses = ['active', 'inactive']
  
  return Array.from({ length: 100 }, (_, i) => ({
    id: i + 1,
    name: `员工${i + 1}`,
    department: departments[Math.floor(Math.random() * departments.length)],
    position: positions[Math.floor(Math.random() * positions.length)],
    email: `employee${i + 1}@company.com`,
    phone: `138${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    createTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toLocaleString()
  }))
}

// 获取数据
const fetchData = () => {
  loading.value = true
  setTimeout(() => {
    const allData = mockData()
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    tableData.value = allData.slice(start, end)
    total.value = allData.length
    loading.value = false
  }, 500)
}

// 处理选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 处理排序
   
const handleSortChange = ({ column, prop, order }: unknown) => {
  console.log('排序变化:', { column, prop, order })
  fetchData()
}

// 处理分页
const handleSizeChange = () => {
  currentPage.value = 1
  fetchData()
}

const handleCurrentChange = () => {
  fetchData()
}

// 操作方法
const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const handleRefresh = () => {
  fetchData()
  ElMessage.success('数据已刷新')
}

   
const handleEdit = (row: unknown) => {
  ElMessage.info(`编辑员工: ${row.name}`)
}

   
const handleDelete = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(`确定要删除员工 ${row.name} 吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    fetchData()
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleFilter = () => {
  console.log('筛选条件:', filterForm)
  fetchData()
}

const handleReset = () => {
  filterForm.name = ''
  filterForm.department = ''
  filterForm.status = ''
  fetchData()
}

const handleBatchEdit = () => {
  ElMessage.info(`批量编辑 ${selectedRows.value.length} 条记录`)
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条记录吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('批量删除成功')
    fetchData()
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleBatchExport = () => {
  ElMessage.success(`导出选中的 ${selectedRows.value.length} 条记录`)
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.data-table-enhanced-demo {
  padding: 20px;
}

.demo-desc {
  color: #666;
  margin-bottom: 20px;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-section h2 {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.demo-form-inline {
  margin-bottom: 20px;
}

.batch-operations {
  padding: 10px 0;
}
</style>