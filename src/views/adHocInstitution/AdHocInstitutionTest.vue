<template>
  <div class="test-page">
    <el-card>
      <template #header>
        <span>临时机构管理功能测试</span>
      </template>

      <el-space direction="vertical" size="large" style="width: 100%">
        <!-- API连接测试 -->
        <el-card>
          <template #header>
            <span>API连接测试</span>
          </template>
          
          <el-space>
            <el-button @click="testHealth" :loading="healthLoading">
              健康检查
            </el-button>
            <el-button @click="testStatistics" :loading="statsLoading">
              统计数据
            </el-button>
            <el-button @click="testQuery" :loading="queryLoading">
              查询测试
            </el-button>
            <el-button @click="testExpiration" :loading="expirationLoading">
              到期检查
            </el-button>
          </el-space>

          <div v-if="testResults.length > 0" style="margin-top: 20px;">
            <h4>测试结果：</h4>
            <el-timeline>
              <el-timeline-item
                v-for="(result, index) in testResults"
                :key="index"
                :timestamp="result.timestamp"
                :type="result.success ? 'success' : 'danger'"
              >
                <div>
                  <strong>{{ result.test }}</strong>: {{ result.message }}
                  <pre v-if="result.data" style="margin-top: 8px; font-size: 12px;">{{ JSON.stringify(result.data, null, 2) }}</pre>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>

        <!-- 组件测试 -->
        <el-card>
          <template #header>
            <span>组件功能测试</span>
          </template>

          <el-space>
            <el-button @click="showCreateDialog">
              测试创建对话框
            </el-button>
            <el-button @click="showDetailDialog">
              测试详情对话框
            </el-button>
            <el-button @click="showExtensionDialog">
              测试延期对话框
            </el-button>
            <el-button @click="showExpirationDialog">
              测试到期处理对话框
            </el-button>
            <el-button @click="showStatisticsDialog">
              测试统计对话框
            </el-button>
          </el-space>
        </el-card>

        <!-- 类型定义测试 -->
        <el-card>
          <template #header>
            <span>类型定义测试</span>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="机构类型枚举">
              {{ Object.values(AdHocInstitutionType).join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="状态枚举">
              {{ Object.values(AdHocInstitutionStatus).join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="到期处理枚举">
              {{ Object.values(ExpirationHandling).join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="优先级枚举">
              {{ Object.values(Priority).join(', ') }}
            </el-descriptions-item>
          </el-descriptions>

          <div style="margin-top: 20px;">
            <h4>选项配置测试：</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <h5>机构类型选项</h5>
                <el-tag 
                  v-for="option in adHocInstitutionTypeOptions" 
                  :key="option.value"
                  :type="typeColors[option.value]"
                  style="margin: 2px;"
                >
                  {{ option.label }}
                </el-tag>
              </el-col>
              <el-col :span="6">
                <h5>状态选项</h5>
                <el-tag 
                  v-for="option in adHocInstitutionStatusOptions" 
                  :key="option.value"
                  :type="statusColors[option.value]"
                  style="margin: 2px;"
                >
                  {{ option.label }}
                </el-tag>
              </el-col>
              <el-col :span="6">
                <h5>到期处理选项</h5>
                <el-tag 
                  v-for="option in expirationHandlingOptions" 
                  :key="option.value"
                  :type="expirationHandlingColors[option.value]"
                  style="margin: 2px;"
                >
                  {{ option.label }}
                </el-tag>
              </el-col>
              <el-col :span="6">
                <h5>优先级选项</h5>
                <el-tag 
                  v-for="option in priorityOptions" 
                  :key="option.value"
                  :type="priorityColors[option.value]"
                  style="margin: 2px;"
                >
                  {{ option.label }}
                </el-tag>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 模拟数据展示 -->
        <el-card>
          <template #header>
            <span>模拟数据展示</span>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="机构编码">
              {{ mockInstitution.institutionCode }}
            </el-descriptions-item>
            <el-descriptions-item label="机构名称">
              {{ mockInstitution.institutionName }}
            </el-descriptions-item>
            <el-descriptions-item label="机构类型">
              <el-tag :type="typeColors[mockInstitution.institutionType]">
                {{ getTypeLabel(mockInstitution.institutionType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="statusColors[mockInstitution.status]">
                {{ getStatusLabel(mockInstitution.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="负责人">
              {{ mockInstitution.responsiblePersonName }}
            </el-descriptions-item>
            <el-descriptions-item label="成员数量">
              {{ mockInstitution.memberCount }}人
            </el-descriptions-item>
            <el-descriptions-item label="建立日期">
              {{ mockInstitution.establishDate }}
            </el-descriptions-item>
            <el-descriptions-item label="计划结束日期">
              {{ mockInstitution.plannedEndDate }}
            </el-descriptions-item>
            <el-descriptions-item label="剩余天数">
              <span :class="getRemainingDaysClass()">
                {{ getRemainingDays() }}天
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="延期次数">
              {{ mockInstitution.extensionCount }} / {{ mockInstitution.maxExtensionCount }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 功能状态检查 -->
        <el-card>
          <template #header>
            <span>功能状态检查</span>
          </template>

          <el-table :data="featureStatus" border>
            <el-table-column prop="feature" label="功能模块"  />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === '完成' ? 'success' : row.status === '开发中' ? 'warning' : 'info'">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明"  />
          </el-table>
        </el-card>
      </el-space>
    </el-card>

    <!-- 测试对话框 -->
    <AdHocInstitutionDialog
      v-model="createDialogVisible"
      :is-edit="false"
      @success="handleDialogSuccess"
    />

    <AdHocInstitutionDetail
      v-model="detailDialogVisible"
      :institution="mockInstitution"
    />

    <ExtensionDialog
      v-model="extensionDialogVisible"
      :institution="mockInstitution"
      @success="handleExtensionSuccess"
    />

    <ExpirationHandlingDialog
      v-model="expirationDialogVisible"
      :institution="mockInstitution"
      @success="handleExpirationSuccess"
    />

    <StatisticsDialog
      v-model="statisticsDialogVisible"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AdHocInstitutionTest'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { adHocInstitutionApi } from '@/api/adHocInstitution'
import type { AdHocInstitution } from '@/types/adHocInstitution'
import {
  AdHocInstitutionType,
  AdHocInstitutionStatus,
  ExpirationHandling,
  Priority,
  adHocInstitutionTypeOptions,
  adHocInstitutionStatusOptions,
  expirationHandlingOptions,
  priorityOptions,
  statusColors,
  typeColors,
  expirationHandlingColors,
  priorityColors
} from '@/types/adHocInstitution'
import HrAdHocInstitutionDialog from '@/components/adHocInstitution/HrAdHocInstitutionDialog.vue'
import HrAdHocInstitutionDetail from '@/components/adHocInstitution/HrAdHocInstitutionDetail.vue'
import HrExtensionDialog from '@/components/adHocInstitution/HrExtensionDialog.vue'
import HrExpirationHandlingDialog from '@/components/adHocInstitution/HrExpirationHandlingDialog.vue'
import HrStatisticsDialog from '@/components/adHocInstitution/HrStatisticsDialog.vue'

// 响应式数据
const healthLoading = ref(false)
const statsLoading = ref(false)
const queryLoading = ref(false)
const expirationLoading = ref(false)
const testResults = ref<any[]>([])

const createDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const extensionDialogVisible = ref(false)
const expirationDialogVisible = ref(false)
const statisticsDialogVisible = ref(false)

// 模拟数据
const mockInstitution = reactive<AdHocInstitution>({
  id: 1,
  institutionCode: 'TEMP-2024-001',
  institutionName: '数字化转型项目组',
  institutionType: AdHocInstitutionType.PROJECT_TEAM,
  parentOrganizationId: 1, // 修复类型：string → number
  parentOrganizationName: '信息技术中心',
  description: '负责学校数字化转型相关工作',
  purpose: '推进学校信息化建设，提升数字化管理水平',
  establishDate: '2024-01-01',
  plannedEndDate: '2024-06-30',
  actualEndDate: '',
  status: AdHocInstitutionStatus.ACTIVE,
  responsiblePersonId: 'USER-001',
  responsiblePersonName: '李四',
  responsiblePersonDepartment: '信息技术中心',
  memberCount: 8,
  memberList: '李四-信息技术中心-组长\n张三-教务处-成员\n王五-财务处-成员',
  budget: 100000,
  budgetDescription: '项目运行费用，包括设备采购、人员培训等',
  expirationHandling: ExpirationHandling.EXTEND,
  autoNotifyDays: 30,
  priority: Priority.HIGH,
  isUrgent: true,
  remark: '重要项目，需要重点关注',
  attachmentJson: '[{"fileName":"项目计划书.pdf","fileUrl":"/files/plan.pdf"}]',
  createTime: '2024-01-01T09:00:00',
  createBy: 'admin',
  updateTime: '2024-01-15T14:30:00',
  updateBy: 'admin',
  lastNotifyTime: '2024-01-15T10:00:00',
  extensionCount: 1,
  maxExtensionCount: 3
})

// 功能状态
const featureStatus = ref([
  { feature: 'TypeScript类型定义', status: '完成', description: '完整的枚举和接口定义' },
  { feature: 'API接口层', status: '完成', description: '完整的CRUD和特殊功能API' },
  { feature: '主管理页面', status: '完成', description: '列表、搜索、批量操作等功能' },
  { feature: '创建/编辑对话框', status: '完成', description: '分标签页表单设计' },
  { feature: '详情查看组件', status: '完成', description: '完整信息展示和操作历史' },
  { feature: '延期处理对话框', status: '完成', description: '延期申请和历史记录' },
  { feature: '到期处理对话框', status: '完成', description: '多种处理方式和影响评估' },
  { feature: '统计分析对话框', status: '完成', description: 'ECharts图表和数据导出' },
  { feature: '路由和导航', status: '完成', description: '完整的页面路由配置' },
  { feature: '功能测试页面', status: '完成', description: '组件和API功能验证' }
])

// API测试函数
const testHealth = async () => {
  healthLoading.value = true
  try {
    const result = await adHocInstitutionApi.health()
    addTestResult('健康检查', true, '服务正常', result)
  } catch (__error) {
    addTestResult('健康检查', false, '服务异常', error)
  } finally {
    healthLoading.value = false
  }
}

const testStatistics = async () => {
  statsLoading.value = true
  try {
    const result = await adHocInstitutionApi.getStatistics()
    addTestResult('统计数据', true, '获取成功', result)
  } catch (__error) {
    addTestResult('统计数据', false, '获取失败', error)
  } finally {
    statsLoading.value = false
  }
}

const testQuery = async () => {
  queryLoading.value = true
  try {
    const result = await adHocInstitutionApi.query({ page: 0, size: 10 })
    addTestResult('查询测试', true, '查询成功', result)
  } catch (__error) {
    addTestResult('查询测试', false, '查询失败', error)
  } finally {
    queryLoading.value = false
  }
}

const testExpiration = async () => {
  expirationLoading.value = true
  try {
    const result = await adHocInstitutionApi.checkExpirationStatus()
    addTestResult('到期检查', true, '检查完成', result)
  } catch (__error) {
    addTestResult('到期检查', false, '检查失败', error)
  } finally {
    expirationLoading.value = false
  }
}

// 添加测试结果
   
const addTestResult = (test: string, success: boolean, message: string, data?: unknown) => {
  testResults.value.unshift({
    test,
    success,
    message,
    data,
    timestamp: new Date().toLocaleString()
  })

  if (success) {
    ElMessage.success(`${test}: ${message}`)
  } else {
    ElMessage.error(`${test}: ${message}`)
  }
}

// 对话框测试函数
const showCreateDialog = () => {
  createDialogVisible.value = true
}

const showDetailDialog = () => {
  detailDialogVisible.value = true
}

const showExtensionDialog = () => {
  extensionDialogVisible.value = true
}

const showExpirationDialog = () => {
  expirationDialogVisible.value = true
}

const showStatisticsDialog = () => {
  statisticsDialogVisible.value = true
}

// 事件处理
const handleDialogSuccess = () => {
  ElMessage.success('对话框操作成功')
}

const handleExtensionSuccess = () => {
  ElMessage.success('延期操作成功')
}

const handleExpirationSuccess = () => {
  ElMessage.success('到期处理成功')
}

// 计算剩余天数
const getRemainingDays = () => {
  const end = new Date(mockInstitution.plannedEndDate)
  const now = new Date()
  const diffTime = end.getTime() - now.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

// 获取剩余天数样式
const getRemainingDaysClass = () => {
  const days = getRemainingDays()
  if (days < 0) return 'expired-days'
  if (days <= 7) return 'warning-days'
  if (days <= 30) return 'caution-days'
  return 'normal-days'
}

// 获取标签文本
const getTypeLabel = (type: AdHocInstitutionType) => {
  const option = adHocInstitutionTypeOptions.find(opt => opt.value === type)
  return option?.label || type
}

const getStatusLabel = (status: AdHocInstitutionStatus) => {
  const option = adHocInstitutionStatusOptions.find(opt => opt.value === status)
  return option?.label || status
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 200px;
}

/* 剩余天数样式 */
.expired-days {
  color: #f56c6c;
  font-weight: bold;
}

.warning-days {
  color: #e6a23c;
  font-weight: bold;
}

.caution-days {
  color: #409eff;
}

.normal-days {
  color: #67c23a;
}
</style>
