/**
 * @name MockManager
 * @description Mock数据管理界面，用于开发环境查看和管理Mock数据
 * <AUTHOR>
 * @since 2025-01-24
 */

<template>
  <div class="mock-manager">
    <!-- 顶部标题区 -->
    <div class="mock-manager__header">
      <h1>Mock数据管理</h1>
      <el-alert type="warning" :closable="false" show-icon style="margin-top: 10px">
        <template #title>开发工具 - 仅在开发环境可用</template>
      </el-alert>
    </div>

    <!-- 主体内容区 -->
    <div class="mock-manager__body">
      <!-- 集合选择器 -->
      <div class="collection-selector">
        <el-radio-group v-model="activeCollection" @change="handleCollectionChange">
          <el-radio-button
            v-for="collection in collections"
            :key="collection.name"
            :value="collection.name"
          >
            <span class="collection-label">
              {{ collection.label }}
              <el-badge :value="collection.count" :max="999" />
            </span>
          </el-radio-button>
        </el-radio-group>

        <div class="global-actions">
          <el-button type="primary" @click="handleInitializeData" :loading="initLoading">
            <el-icon><Refresh /></el-icon>
            初始化所有数据
          </el-button>
        </div>
      </div>

      <!-- 数据管理区 -->
      <el-card v-if="activeCollection" class="data-container">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索..."
              clearable
              style="width: 300px"
              @clear="handleSearch"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="toolbar-right">
            <el-button-group>
              <el-button @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
              <el-button @click="handleRegenerate" :loading="regenerateLoading">
                <el-icon><RefreshRight /></el-icon>
                重新生成
              </el-button>
              <el-button type="danger" @click="handleClear" :loading="clearLoading">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
          <el-table
            :data="tableData"
            v-loading="loading"
            border
            stripe
            height="100%"
            :empty-text="emptyText"
          >
            <el-table-column type="index" label="#" width="60" fixed="left" />

            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
              show-overflow-tooltip
            />

            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" link @click="handleView(row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>

      <!-- 空状态 -->
      <el-empty v-else description="请选择一个数据集合" />
    </div>

    <!-- 数据查看对话框 -->
    <el-dialog 
      v-model="viewDialogVisible" 
      title="数据详情" 
      width="70%" 
      top="5vh"
      destroy-on-close
    >
      <div class="data-viewer">
        <el-descriptions :column="1" border>
          <el-descriptions-item
            v-for="(value, key) in viewData"
            :key="key"
            :label="formatLabel(String(key))"
          >
            <template v-if="isJSON(value)">
              <el-tag
                v-for="(item, index) in parseJSON(value)"
                :key="index"
                style="margin-right: 5px"
              >
                {{ item }}
              </el-tag>
            </template>
            <template v-else-if="isBoolean(value)">
              <el-tag :type="value ? 'success' : 'info'">
                {{ value ? '是' : '否' }}
              </el-tag>
            </template>
            <template v-else-if="isDate(value)">
              {{ formatDate(value) }}
            </template>
            <template v-else>
              {{ value || '-' }}
            </template>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  RefreshRight,
  Delete
} from '@element-plus/icons-vue'

// 集合配置
const COLLECTION_CONFIG = [
  { name: 'users', label: '用户', columns: ['username', 'fullName', 'email', 'status'] },
  { name: 'employees', label: '员工', columns: ['employeeId', 'fullName', 'departmentName', 'positionName', 'status'] },
  { name: 'departments', label: '部门', columns: ['institutionName', 'institutionCode', 'institutionType', 'employeeCount'] },
  { name: 'positions', label: '岗位', columns: ['name', 'code', 'type', 'level', 'departmentId'] },
  { name: 'roles', label: '角色', columns: ['name', 'description'] },
  { name: 'notices', label: '通知', columns: ['title', 'type', 'publishTime'] },
  { name: 'dicts', label: '字典', columns: ['type', 'label', 'value', 'sort'] },
  { name: 'configs', label: '配置', columns: ['key', 'value', 'category'] },
  { name: 'tokens', label: 'Token', columns: ['userId', 'type', 'expiresAt'] }
]

// 列配置映射
const COLUMN_CONFIG: Record<string, any> = {
  // 通用列
  id: { label: 'ID', minWidth: 100 },
  createTime: { label: '创建时间', minWidth: 160, formatter: (_row: any, _column: any, value: any) => formatDate(value) },
  updateTime: { label: '更新时间', minWidth: 160, formatter: (_row: any, _column: any, value: any) => formatDate(value) },
  
  // 用户相关
  username: { label: '用户名', minWidth: 120 },
  fullName: { label: '姓名', minWidth: 100 },
  email: { label: '邮箱', minWidth: 180 },
  status: { label: '状态', minWidth: 80, formatter: (_row: any, _column: any, value: any) => {
    const statusMap: Record<string, string> = {
      active: '正常',
      inactive: '禁用',
      ACTIVE: '在职',
      RESIGNED: '离职'
    }
    return statusMap[value] || value
  }},
  
  // 员工相关
  employeeId: { label: '工号', minWidth: 120 },
  employeeNumber: { label: '员工编号', minWidth: 140 },
  departmentName: { label: '部门', minWidth: 150 },
  positionName: { label: '岗位', minWidth: 120 },
  gender: { label: '性别', minWidth: 80, formatter: (_row: any, _column: any, value: any) => value === 'male' ? '男' : '女' },
  
  // 部门相关
  institutionName: { label: '机构名称', minWidth: 180 },
  institutionCode: { label: '机构代码', minWidth: 120 },
  institutionType: { label: '机构类型', minWidth: 100 },
  employeeCount: { label: '员工数', minWidth: 80 },
  
  // 其他
  title: { label: '标题', minWidth: 200 },
  content: { label: '内容', minWidth: 300 },
  type: { label: '类型', minWidth: 100 },
  publishTime: { label: '发布时间', minWidth: 160, formatter: (_row: any, _column: any, value: any) => formatDate(value) },
  name: { label: '名称', minWidth: 150 },
  code: { label: '代码', minWidth: 120 },
  level: { label: '级别', minWidth: 100 },
  key: { label: '键', minWidth: 200 },
  value: { label: '值', minWidth: 200 },
  category: { label: '分类', minWidth: 100 },
  label: { label: '标签', minWidth: 100 },
  sort: { label: '排序', minWidth: 80 },
  description: { label: '描述', minWidth: 200 },
  userId: { label: '用户ID', minWidth: 120 },
  expiresAt: { label: '过期时间', minWidth: 160, formatter: (_row: any, _column: any, value: any) => formatDate(value) }
}

// 状态管理
const collections = ref<Array<{ name: string; label: string; count: number }>>([])
const activeCollection = ref('')
const loading = ref(false)
const tableData = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const searchKeyword = ref('')

// 对话框状态
const viewDialogVisible = ref(false)
const viewData = ref<any>({})

// 操作状态
const initLoading = ref(false)
const regenerateLoading = ref(false)
const clearLoading = ref(false)

// 计算属性
const tableColumns = computed(() => {
  const config = COLLECTION_CONFIG.find(c => c.name === activeCollection.value)
  if (!config) return []
  
  return config.columns.map(col => ({
    prop: col,
    ...(COLUMN_CONFIG[col] || { label: col, minWidth: 120 })
  }))
})

const emptyText = computed(() => {
  if (loading.value) return '加载中...'
  if (!activeCollection.value) return '请选择数据集合'
  return '暂无数据'
})

// 获取集合统计
const refreshCollections = async () => {
  try {
    const response = await fetch('/api/mock-manager/collections/stats')
    const result = await response.json()
    
    if (result.success && result.data) {
      const statsMap = new Map(result.data.map((item: any) => [item.name, item.count]))
      collections.value = COLLECTION_CONFIG.map(config => ({
        name: config.name,
        label: config.label,
        count: Number(statsMap.get(config.name) || 0)
      }))
    }
  } catch (error) {
    console.error('获取集合统计失败:', error)
  }
}

// 加载数据
const loadData = async () => {
  if (!activeCollection.value) return
  
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: String(currentPage.value),
      size: String(pageSize.value),
      keyword: searchKeyword.value || ''
    })
    
    const response = await fetch(
      `/api/mock-manager/collections/${activeCollection.value}/data?${params}`
    )
    const result = await response.json()
    
    if (result.success && result.data) {
      tableData.value = result.data.list || []
      total.value = result.data.total || 0
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 集合切换
const handleCollectionChange = () => {
  currentPage.value = 1
  searchKeyword.value = ''
  loadData()
}

// 刷新数据
const refreshData = () => {
  loadData()
  refreshCollections()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 分页
const handlePageChange = () => loadData()
const handleSizeChange = () => {
  currentPage.value = 1
  loadData()
}

// 初始化所有数据
const handleInitializeData = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将为所有空集合生成初始数据，是否继续？',
      '初始化确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    initLoading.value = true
    const response = await fetch('/api/mock-manager/initialize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })
    
    const result = await response.json()
    if (result.success) {
      ElMessage.success('初始化成功')
      await refreshCollections()
      if (activeCollection.value) {
        await loadData()
      }
    } else {
      ElMessage.error(result.message || '初始化失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('初始化失败')
    }
  } finally {
    initLoading.value = false
  }
}

// 重新生成数据
const handleRegenerate = async () => {
  try {
    const config = COLLECTION_CONFIG.find(c => c.name === activeCollection.value)
    await ElMessageBox.confirm(
      `此操作将清空并重新生成${config?.label}数据，是否继续？`,
      '重新生成确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    regenerateLoading.value = true
    const response = await fetch(
      `/api/mock-manager/collections/${activeCollection.value}/regenerate`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }
    )
    
    const result = await response.json()
    if (result.success) {
      ElMessage.success('重新生成成功')
      await refreshCollections()
      await loadData()
    } else {
      ElMessage.error(result.message || '重新生成失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('重新生成失败')
    }
  } finally {
    regenerateLoading.value = false
  }
}

// 清空数据
const handleClear = async () => {
  try {
    const config = COLLECTION_CONFIG.find(c => c.name === activeCollection.value)
    await ElMessageBox.confirm(
      `此操作将清空${config?.label}的所有数据，是否继续？`,
      '清空确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    clearLoading.value = true
    const response = await fetch(
      `/api/mock-manager/collections/${activeCollection.value}/clear`,
      { method: 'DELETE' }
    )
    
    const result = await response.json()
    if (result.success) {
      ElMessage.success('清空成功')
      await refreshCollections()
      await loadData()
    } else {
      ElMessage.error(result.message || '清空失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('清空失败')
    }
  } finally {
    clearLoading.value = false
  }
}

// 查看数据
const handleView = (row: any) => {
  viewData.value = row
  viewDialogVisible.value = true
}

// 工具函数
const formatDate = (date: any) => {
  if (!date) return '-'
  const d = new Date(date)
  if (isNaN(d.getTime())) return date
  return d.toLocaleString('zh-CN')
}

const formatLabel = (key: string) => {
  return COLUMN_CONFIG[key]?.label || key
}

const isJSON = (value: any) => {
  if (typeof value !== 'string') return false
  try {
    const parsed = JSON.parse(value)
    return Array.isArray(parsed)
  } catch {
    return false
  }
}

const parseJSON = (value: string) => {
  try {
    return JSON.parse(value)
  } catch {
    return []
  }
}

const isBoolean = (value: any) => typeof value === 'boolean'

const isDate = (value: any) => {
  if (!value) return false
  const date = new Date(value)
  return !isNaN(date.getTime()) && value.includes('-')
}

// 初始化
onMounted(async () => {
  await refreshCollections()
  if (collections.value.length > 0) {
    activeCollection.value = collections.value[0].name
    await loadData()
  }
})
</script>

<style lang="scss" scoped>
.mock-manager {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  &__header {
    padding: 20px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: #303133;
    }
  }

  &__body {
    flex: 1;
    padding: 20px;
    overflow: auto;
  }

  .collection-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 16px 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);

    .collection-label {
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    :deep(.el-badge__content) {
      top: -2px;
      transform: scale(0.9);
    }
  }

  .data-container {
    height: calc(100% - 100px);
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 0;
    }

    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #e4e7ed;
      background-color: #fafafa;

      &-left, &-right {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    .table-container {
      flex: 1;
      padding: 0;
      overflow: hidden;
    }

    .pagination-container {
      padding: 16px 20px;
      border-top: 1px solid #e4e7ed;
      background-color: #fafafa;
      display: flex;
      justify-content: flex-end;
    }
  }

  .data-viewer {
    max-height: 70vh;
    overflow-y: auto;

    :deep(.el-descriptions__label) {
      width: 150px;
      font-weight: 500;
      color: #606266;
    }

    :deep(.el-descriptions__content) {
      color: #303133;
    }
  }
}

// 响应式优化
@media (max-width: 1200px) {
  .mock-manager {
    .collection-selector {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      :deep(.el-radio-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch !important;

      &-left, &-right {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}
</style>