/**
 * 编制管理相关类型定义 - 主入口文件
 *
 * 为了更好的代码组织和维护，原有的类型定义已经拆分为多个模块。
 * 本文件保留作为向后兼容的入口，重新导出所有类型定义。
 *
 * @deprecated 建议直接从 './staffing/index' 或具体的子模块导入所需类型
 * @since v2.0.0
 */

// 重新导出所有类型，保持向后兼容
export * from './staffing'

/**
 * 模块化重构说明：
 *
 * 原staffing.ts文件（2,231行）已拆分为以下8个模块：
 *
 * 1. staffing/enums.ts - 枚举定义（17个枚举）
 * 2. staffing/constants.ts - 常量和配置
 * 3. staffing/plan.ts - 编制规划相关（15个接口）
 * 4. staffing/statistics.ts - 统计分析相关（28个接口）
 * 5. staffing/alert.ts - 预警相关（23个接口）
 * 6. staffing/approval.ts - 审批流程相关（21个接口）
 * 7. staffing/model.ts - 模型相关（27个接口）
 * 8. staffing/common.ts - 通用类型（20个接口）
 *
 * 新的导入方式示例：
 *
 * // 导入所有类型（推荐）
 * import * as StaffingTypes from '@/types/staffing'
 *
 * // 导入特定模块
 * import { StaffingPlan, StaffingPlanCreateRequest } from '@/types/staffing/plan'
 * import { StaffingAlertType, AlertLevel } from '@/types/staffing/enums'
 * import { positionCategoryOptions } from '@/types/staffing/constants'
 *
 * // 从主索引导入
 * import {
 *   StaffingPlan,
 *   StaffingAlertType,
 *   positionCategoryOptions
 * } from '@/types/staffing'
 */

// 添加迁移提示
if (import.meta.env.DEV) {
  }

// 职位类别选项
export const positionCategoryOptions = [
  { label: '技术类', value: 'TECH' },
  { label: '管理类', value: 'MANAGEMENT' },
  { label: '销售类', value: 'SALES' },
  { label: '运营类', value: 'OPERATION' },
  { label: '财务类', value: 'FINANCE' },
  { label: '人力资源类', value: 'HR' },
  { label: '行政类', value: 'ADMIN' },
  { label: '其他', value: 'OTHER' }
]

// 编制计划状态选项
export const staffingPlanStatusOptions = [
  { label: '草稿', value: 'DRAFT' },
  { label: '待审批', value: 'PENDING' },
  { label: '已批准', value: 'APPROVED' },
  { label: '已拒绝', value: 'REJECTED' },
  { label: '执行中', value: 'EXECUTING' },
  { label: '已完成', value: 'COMPLETED' },
  { label: '已取消', value: 'CANCELLED' }
]

// 编制计划状态颜色
export const staffingPlanStatusColors = {
  DRAFT: 'info',
  PENDING: 'warning',
  APPROVED: 'success',
  REJECTED: 'danger',
  EXECUTING: 'primary',
  COMPLETED: 'success',
  CANCELLED: 'info'
}
