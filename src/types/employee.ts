/**
 * 员工管理相关类型定义
 */

// 人员状态枚举
export enum PersonnelStatus {
  Active = 'active', // 在职
  Resigned = 'resigned', // 离职
  Retired = 'retired', // 离退休
  Rehired = 'rehired', // 返聘
  Intern = 'intern', // 实习生
  PartTime = 'partTime' // 兼职
}

// 性别枚举
export enum Gender {
  Male = 'male',
  Female = 'female'
}

// 证件类型枚举
export enum IDType {
  IDCard = 'idCard', // 身份证
  Passport = 'passport', // 护照
  HKMacaoPass = 'hkMacaoPass', // 港澳通行证
  TaiwanPass = 'taiwanPass', // 台胞证
  ResidencePermit = 'residencePermit' // 居留许可
}

// 员工基本信息
export interface Employee {
  // 系统字段
  employeeId: string // 教职工ID
  employeeNumber: string // 工号

  // 基本信息
  fullName: string // 姓名
  gender: Gender // 性别
  dateOfBirth: string // 出生年月
  ethnicity: string // 民族
  politicalStatus: string // 政治面貌
  nativePlace: string // 籍贯
  placeOfBirth: string // 出生地
  idType: IDType // 证件类型
  idNumber: string // 证件号码
  photoUrl?: string // 个人照片URL

  // 工作信息
  workStartDate: string // 参加工作时间
  hireDate: string // 入职日期
  personnelStatus: PersonnelStatus // 人员状态
  institutionId: string // 所属机构ID
  institutionName?: string // 所属机构名称
  positionId: string // 当前岗位ID
  positionName?: string // 当前岗位名称

  // 个人信息
  healthStatus?: string // 健康状况
  maritalStatus?: string // 婚姻状况
  height?: number // 身高(cm)
  phoneNumber: string // 联系电话
  email: string // 邮箱
  contactAddress?: string // 联系地址
  postalCode?: string // 邮编

  // 学历职称
  educationDegree?: string // 学历学位
  professionalTitle?: string // 职称

  // 人才分类
  highLevelTalentCategory?: string // 高层次人才分类认定

  // 系统信息
  infoCompletenessPercentage: number // 信息完整度百分比
  otherBasicInfo?: Record<string, unknown> // 其他基本信息

  // 时间戳
  createdAt?: string
  updatedAt?: string
}

// 员工搜索参数
export interface EmployeeSearchParams {
  // 基础搜索
  keyword?: string // 关键词(姓名/工号)
  fullName?: string // 姓名
  employeeNumber?: string // 工号

  // 组织岗位
  institutionId?: string // 机构ID
  positionId?: string // 岗位ID

  // 人员信息
  gender?: Gender // 性别
  personnelStatus?: PersonnelStatus // 人员状态
  educationDegree?: string // 学历
  professionalTitle?: string // 职称
  politicalStatus?: string // 政治面貌

  // 日期范围
  hireDateStart?: string // 入职日期开始
  hireDateEnd?: string // 入职日期结束
  birthDateStart?: string // 出生日期开始
  birthDateEnd?: string // 出生日期结束

  // 其他
  highLevelTalentCategory?: string // 高层次人才分类
  infoCompletenessMin?: number // 最小信息完整度
  infoCompletenessMax?: number // 最大信息完整度

  // 分页排序
  page?: number
  pageSize?: number
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 员工列表响应
export interface EmployeeListResponse {
  list: Employee[]
  total: number
  page: number
  pageSize: number
}

// 学习经历
export interface EducationHistory {
  recordId: string
  employeeId: string
  startDate: string // 开始日期
  endDate?: string // 结束日期
  degree: string // 学历/学位
  graduationSchool: string // 毕业院校
  major: string // 专业
  studyForm?: string // 学习形式
  coreCourses?: string // 核心课程
  thesisTitle?: string // 毕业论文题目

  // 国外学历认证
  overseasCertAgency?: string // 认证机构
  overseasCertNumber?: string // 认证编号
  overseasCertDate?: string // 认证时间
  certificateDocUrl?: string // 证书电子文档URL
}

// 工作经历
export interface WorkExperience {
  recordId: string
  employeeId: string
  startDate: string // 开始日期
  endDate?: string // 结束日期
  companyName: string // 工作单位
  positionHeld: string // 任职情况
  workProofUrl?: string // 工作证明URL
  resignationProofUrl?: string // 离职证明URL
  salaryGrade?: string // 薪酬等级

  // 海外工作经历
  overseasLocation?: string // 海外工作地点
  overseasTimeZone?: string // 海外工作时区
  overseasCurrency?: string // 海外工作币种
}

// 家庭成员
export interface FamilyMember {
  recordId: string
  employeeId: string
  fullName: string // 姓名
  relationship: string // 关系
  dateOfBirth?: string // 出生年月
  politicalStatus?: string // 政治面貌
  workUnitAndPosition?: string // 工作单位及职务
}

// 员工详情（包含所有子集信息）
export interface EmployeeDetail extends Employee {
  educationHistory: EducationHistory[] // 学习经历
  workExperience: WorkExperience[] // 工作经历
  familyMembers: FamilyMember[] // 家庭成员
  // 其他子集信息根据需要添加
}

// 导出配置
export interface EmployeeExportConfig {
  fields: string[] // 要导出的字段
  format: 'excel' | 'pdf' | 'csv' // 导出格式
  filename?: string // 文件名
  includePhoto?: boolean // 是否包含照片
  dataMasking?: boolean // 是否脱敏
}

// 批量操作
export interface EmployeeBatchOperation {
  employeeIds: string[] // 员工ID列表
  operation: 'export' | 'delete' | 'updateStatus' | 'assign' // 操作类型
  params?: Record<string, unknown> // 操作参数
}
