/**
 * 标签相关类型定义
 */

/**
 * 标签类型
 */
export enum TagType {
  System = 'system', // 系统标签（自动生成）
  Manual = 'manual', // 人工标签（手动添加）
  Computed = 'computed' // 计算标签（基于规则计算）
}

/**
 * 标签分类
 */
export enum TagCategory {
  Basic = 'basic', // 基本信息
  Education = 'education', // 教育背景
  Skill = 'skill', // 技能特长
  Performance = 'performance', // 绩效表现
  Character = 'character', // 性格特征
  Potential = 'potential', // 发展潜力
  Risk = 'risk', // 风险预警
  Honor = 'honor', // 荣誉奖项
  Project = 'project', // 项目经验
  Training = 'training' // 培训发展
}

/**
 * 标签权重等级
 */
export enum TagWeight {
  Low = 1, // 低权重
  Medium = 3, // 中权重
  High = 5, // 高权重
  Core = 10 // 核心权重
}

/**
 * 标签状态
 */
export enum TagStatus {
  Active = 'active', // 启用
  Inactive = 'inactive', // 停用
  Pending = 'pending' // 待审核
}

/**
 * 标签基础信息
 */
export interface Tag {
  tagId: string
  tagName: string
  category: TagCategory
  type: TagType
  weight: TagWeight
  color?: string
  icon?: string
  description?: string
  status: TagStatus
  parentId?: string // 父标签ID，支持标签层级
  ruleExpression?: string // 计算规则表达式（计算标签）
  threshold?: number // 阈值（系统标签）
  keywords?: string[] // 关键词（用于匹配）
  createTime: string
  updateTime: string
  createdBy: string
  updatedBy: string
}

/**
 * 员工标签关联
 */
export interface EmployeeTag {
  id: string
  employeeId: string
  tagId: string
  tagName: string // 冗余字段，方便显示
  category: TagCategory // 冗余字段，方便分组
  type: TagType // 冗余字段，方便筛选
  score?: number // 标签得分（0-100）
  confidence?: number // 置信度（0-1）
  source?: string // 来源（系统计算/手动添加/导入等）
  reason?: string // 添加原因/计算依据
  attachTime: string // 关联时间
  attachedBy: string // 关联人
  validUntil?: string // 有效期
  verified?: boolean // 是否已验证
}

/**
 * 标签统计信息
 */
export interface TagStatistics {
  tagId: string
  tagName: string
  category: TagCategory
  employeeCount: number // 拥有该标签的员工数
  percentage: number // 占比
  trend: 'up' | 'down' | 'stable' // 趋势
  avgScore: number // 平均得分
  distribution: {
    // 分布情况
    department: Record<string, number>
    position: Record<string, number>
    ageGroup: Record<string, number>
  }
}

/**
 * 员工画像
 */
export interface EmployeePortrait {
  employeeId: string
  employeeNumber: string
  fullName: string
  photoUrl?: string
  department: string
  position: string
  tags: EmployeeTag[]
  tagSummary: {
    total: number
    byCategory: Record<TagCategory, number>
    byType: Record<TagType, number>
  }
  coreCompetencies: string[] // 核心能力（基于标签提取）
  developmentPotential: number // 发展潜力指数（0-100）
  riskLevel: 'low' | 'medium' | 'high' // 风险等级
  similarEmployees?: string[] // 相似员工ID列表
  lastUpdateTime: string
}

/**
 * 标签推荐
 */
export interface TagRecommendation {
  tagId: string
  tagName: string
  category: TagCategory
  recommendScore: number // 推荐分数（0-100）
  reason: string // 推荐理由
  evidence: string[] // 证据/依据
  confidence: number // 置信度（0-1）
}

/**
 * 标签操作日志
 */
export interface TagOperationLog {
  id: string
  operationType: 'add' | 'remove' | 'update' | 'merge'
  targetType: 'tag' | 'employee_tag'
  targetId: string
  employeeId?: string
  tagId?: string

  oldValue?: unknown

  newValue?: unknown
  reason?: string
  operatorId: string
  operatorName: string
  operateTime: string
  ip?: string
}

/**
 * 标签搜索参数
 */
export interface TagSearchParams {
  keyword?: string
  category?: TagCategory
  type?: TagType
  status?: TagStatus
  minEmployeeCount?: number
  maxEmployeeCount?: number
  page: number
  pageSize: number
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

/**
 * 员工标签搜索参数
 */
export interface EmployeeTagSearchParams {
  employeeId?: string
  tagIds?: string[]
  categories?: TagCategory[]
  types?: TagType[]
  minScore?: number
  hasVerified?: boolean
  attachedDateStart?: string
  attachedDateEnd?: string
  page: number
  pageSize: number
}

/**
 * 标签批量操作
 */
export interface TagBatchOperation {
  operationType: 'add' | 'remove' | 'update'
  employeeIds: string[]
  tagIds: string[]
  score?: number
  reason?: string
  validUntil?: string
}

/**
 * 画像雷达图数据
 */
export interface PortraitRadarData {
  category: string
  label: string
  value: number // 0-100
  fullMark: number // 满分值，通常为100
}

/**
 * 标签词云数据
 */
export interface TagCloudData {
  text: string
  value: number
  color?: string
  link?: string
}

/**
 * 相似度计算结果
 */
export interface SimilarityResult {
  employeeId: string
  employeeNumber: string
  fullName: string
  department: string
  position: string
  similarity: number // 相似度（0-1）
  commonTags: string[] // 共同标签
  differTags: string[] // 差异标签
}
