import type { PageData } from '@/types/common/api'

// 岗位类别枚举
export enum PositionCategory {
  MANAGEMENT = 'MANAGEMENT', // 管理岗
  PROFESSIONAL = 'PROFESSIONAL', // 专业技术岗
  WORKER = 'WORKER', // 工勤岗
  TEACHER = 'TEACHER', // 教师岗
  ADMINISTRATIVE = 'ADMINISTRATIVE' // 行政岗
}

// 岗位等级枚举
export enum PositionLevel {
  LEVEL_1 = 'LEVEL_1', // 一级岗（正高级）
  LEVEL_2 = 'LEVEL_2', // 二级岗
  LEVEL_3 = 'LEVEL_3', // 三级岗
  LEVEL_4 = 'LEVEL_4', // 四级岗（副高级）
  LEVEL_5 = 'LEVEL_5', // 五级岗
  LEVEL_6 = 'LEVEL_6', // 六级岗
  LEVEL_7 = 'LEVEL_7', // 七级岗（中级）
  LEVEL_8 = 'LEVEL_8', // 八级岗
  LEVEL_9 = 'LEVEL_9', // 九级岗
  LEVEL_10 = 'LEVEL_10', // 十级岗（初级）
  LEVEL_11 = 'LEVEL_11', // 十一级岗
  LEVEL_12 = 'LEVEL_12', // 十二级岗
  LEVEL_13 = 'LEVEL_13' // 十三级岗
}

// 岗位类型枚举
export enum PositionType {
  FULL_TIME = 'FULL_TIME', // 专任
  PART_TIME = 'PART_TIME', // 兼任
  TRAINING = 'TRAINING', // 实训
  TEMPORARY = 'TEMPORARY' // 临时
}

// 岗位状态枚举
export enum PositionStatus {
  ACTIVE = 'ACTIVE', // 正常
  FROZEN = 'FROZEN', // 冻结
  CANCELLED = 'CANCELLED', // 已撤销
  MERGED = 'MERGED' // 已合并
}

// 学历要求枚举
export enum EducationRequirement {
  DOCTOR = 'DOCTOR', // 博士
  MASTER = 'MASTER', // 硕士
  BACHELOR = 'BACHELOR', // 本科
  COLLEGE = 'COLLEGE', // 专科
  HIGH_SCHOOL = 'HIGH_SCHOOL', // 高中
  OTHER = 'OTHER' // 其他
}

// 岗位基本信息
export interface Position {
  positionId: string // 岗位ID
  positionCode: string // 岗位编码
  positionName: string // 岗位名称
  positionCategory: PositionCategory // 岗位类别
  positionLevel?: PositionLevel // 岗位等级
  positionType: PositionType // 岗位类型
  institutionId: string // 所属机构ID
  institutionName?: string // 所属机构名称

  // 岗位详情
  jobResponsibilities?: string // 岗位职责
  qualifications?: string // 任职资格
  majorRequirements?: string // 专业要求
  educationRequirement?: EducationRequirement // 学历要求
  experienceRequirement?: number // 工作经验要求（年）
  skillRequirements?: string[] // 技能要求
  certificateRequirements?: string[] // 证书要求

  // 编制信息
  establishmentCount?: number // 编制数
  actualCount?: number // 实际人数
  vacancyCount?: number // 空缺数

  // 薪酬范围
  salaryMin?: number // 最低薪酬
  salaryMax?: number // 最高薪酬

  // 状态和时间
  status: PositionStatus // 岗位状态
  createTime: string // 创建时间
  updateTime?: string // 更新时间
  createBy: string // 创建人
  updateBy?: string // 更新人

  // 扩展信息
  sortOrder?: number // 排序
  remark?: string // 备注
  tags?: string[] // 标签
}

// 岗位序列
export interface PositionSequence {
  sequenceId: string
  sequenceName: string // 序列名称（如：教学序列、科研序列、管理序列）
  sequenceCode: string // 序列编码
  description?: string // 序列描述
  positionCount?: number // 包含岗位数
  status: 'ACTIVE' | 'INACTIVE'
}

// 岗位族群
export interface PositionFamily {
  familyId: string
  familyName: string // 族群名称（如：计算机类、管理类、语言类）
  familyCode: string // 族群编码
  sequenceId: string // 所属序列
  description?: string // 族群描述
  positionCount?: number // 包含岗位数
  status: 'ACTIVE' | 'INACTIVE'
}

// 任职资格
export interface Qualification {
  qualificationId: string
  positionId: string // 关联岗位
  type: 'REQUIRED' | 'PREFERRED' // 必要/优先
  category: string // 资格类别
  description: string // 资格描述
  weight?: number // 权重（用于匹配度计算）
}

// 岗位变更记录
export interface PositionChange {
  changeId: string
  positionId: string
  positionName?: string
  changeType: 'CREATE' | 'UPDATE' | 'DELETE' | 'MERGE' | 'FREEZE' | 'ACTIVE'
  changeReason: string
  changeDetails: Record<string, unknown>
  changeTime: string
  operator: string
  operatorName?: string
  approvalStatus?: 'PENDING' | 'APPROVED' | 'REJECTED'
  approvalTime?: string
  approver?: string
}

// 岗位人员配置
export interface PositionStaffing {
  positionId: string
  positionName?: string
  establishmentCount: number // 编制数
  actualCount: number // 实际人数
  vacancyCount: number // 空缺数
  overStaffCount: number // 超编数
  employees: PositionEmployee[] // 在岗人员列表
}

// 岗位上的员工
export interface PositionEmployee {
  employeeId: string
  employeeName: string
  employeeCode: string
  startDate: string // 任职开始日期
  endDate?: string // 任职结束日期
  isPrimary: boolean // 是否主岗
  workload?: number // 工作量占比(%)
}

// 岗位匹配度评估结果
export interface PositionMatchResult {
  positionId: string
  positionName: string
  employeeId: string
  employeeName: string
  overallScore: number // 总体匹配度（0-100）
  details: MatchDetail[] // 详细匹配项
  suggestions: string[] // 改进建议
  evaluationTime: string
}

// 匹配详情
export interface MatchDetail {
  criterion: string // 评估标准
  weight: number // 权重
  score: number // 得分
  gap?: string // 差距说明
}

// 岗位价值评估
export interface PositionValuation {
  positionId: string
  positionName: string
  valuationDate: string

  // 评估维度
  dimensions: {
    responsibility: number // 职责重要性 (0-100)
    complexity: number // 工作复杂度 (0-100)
    impact: number // 影响范围 (0-100)
    knowledge: number // 知识要求 (0-100)
    innovation: number // 创新要求 (0-100)
  }

  totalScore: number // 总分
  grade: string // 评估等级
  suggestions?: string[] // 优化建议
}

// 岗位查询参数
export interface PositionQueryParams {
  keyword?: string // 关键词（名称/编码）
  positionCategory?: PositionCategory // 岗位类别
  positionLevel?: PositionLevel // 岗位等级
  positionType?: PositionType // 岗位类型
  institutionId?: string // 所属机构
  status?: PositionStatus // 状态
  hasVacancy?: boolean // 是否有空缺
  page?: number
  size?: number
  sort?: string
}

// 岗位统计数据
export interface PositionStatistics {
  totalCount: number // 岗位总数
  activeCount: number // 在用岗位数
  vacancyCount: number // 空缺岗位数
  categoryDistribution: Array<{
    // 类别分布
    category: PositionCategory
    count: number
    percentage: number
  }>
  levelDistribution: Array<{
    // 等级分布
    level: PositionLevel
    count: number
    percentage: number
  }>
  institutionDistribution: Array<{
    // 机构分布
    institutionId: string
    institutionName: string
    count: number
  }>
}

// 岗位批量操作请求
export interface PositionBatchRequest {
  positionIds: string[]
  operation: 'FREEZE' | 'ACTIVE' | 'DELETE' | 'UPDATE_LEVEL' | 'UPDATE_CATEGORY'
  params?: Record<string, unknown>
  reason: string
}

// 岗位导入数据
export interface PositionImportData {
  positionCode: string
  positionName: string
  positionCategory: string
  positionLevel?: string
  positionType: string
  institutionCode: string // 使用机构编码关联
  jobResponsibilities?: string
  qualifications?: string
  educationRequirement?: string
  establishmentCount?: number
}

// 岗位创建请求
export interface PositionCreateRequest {
  positionCode: string // 岗位编码
  positionName: string // 岗位名称
  organizationId: number // 所属组织ID
  positionType: PositionType // 岗位类型
  positionLevel?: PositionLevel // 岗位等级
  sortOrder?: number // 排序
  responsibilities?: string // 岗位职责
  requirements?: string // 任职要求
  minSalary?: number // 最低薪资
  maxSalary?: number // 最高薪资
  headcount?: number // 编制人数
}

// 岗位查询请求
export interface PositionQueryRequest {
  keyword?: string // 关键词（名称/编码）
  positionCategory?: PositionCategory // 岗位类别
  positionLevel?: PositionLevel // 岗位等级
  positionType?: PositionType // 岗位类型
  organizationId?: number // 所属机构
  status?: PositionStatus // 状态
  hasVacancy?: boolean // 是否有空缺
  page?: number
  size?: number
  sort?: string
}

// 分页响应
// 使用通用的PageData类型
export type PageResponse<T> = PageData<T>

// 岗位选项（用于下拉选择）
export const positionCategoryOptions = [
  { label: '管理岗', value: PositionCategory.MANAGEMENT },
  { label: '专业技术岗', value: PositionCategory.PROFESSIONAL },
  { label: '工勤岗', value: PositionCategory.WORKER },
  { label: '教师岗', value: PositionCategory.TEACHER },
  { label: '行政岗', value: PositionCategory.ADMINISTRATIVE }
]

export const positionLevelOptions = [
  { label: '一级岗', value: PositionLevel.LEVEL_1 },
  { label: '二级岗', value: PositionLevel.LEVEL_2 },
  { label: '三级岗', value: PositionLevel.LEVEL_3 },
  { label: '四级岗', value: PositionLevel.LEVEL_4 },
  { label: '五级岗', value: PositionLevel.LEVEL_5 },
  { label: '六级岗', value: PositionLevel.LEVEL_6 },
  { label: '七级岗', value: PositionLevel.LEVEL_7 },
  { label: '八级岗', value: PositionLevel.LEVEL_8 },
  { label: '九级岗', value: PositionLevel.LEVEL_9 },
  { label: '十级岗', value: PositionLevel.LEVEL_10 },
  { label: '十一级岗', value: PositionLevel.LEVEL_11 },
  { label: '十二级岗', value: PositionLevel.LEVEL_12 },
  { label: '十三级岗', value: PositionLevel.LEVEL_13 }
]

export const positionTypeOptions = [
  { label: '专任', value: PositionType.FULL_TIME },
  { label: '兼任', value: PositionType.PART_TIME },
  { label: '实训', value: PositionType.TRAINING },
  { label: '临时', value: PositionType.TEMPORARY }
]

export const positionStatusOptions = [
  { label: '正常', value: PositionStatus.ACTIVE },
  { label: '冻结', value: PositionStatus.FROZEN },
  { label: '已撤销', value: PositionStatus.CANCELLED },
  { label: '已合并', value: PositionStatus.MERGED }
]

export const educationRequirementOptions = [
  { label: '博士', value: EducationRequirement.DOCTOR },
  { label: '硕士', value: EducationRequirement.MASTER },
  { label: '本科', value: EducationRequirement.BACHELOR },
  { label: '专科', value: EducationRequirement.COLLEGE },
  { label: '高中', value: EducationRequirement.HIGH_SCHOOL },
  { label: '其他', value: EducationRequirement.OTHER }
]
