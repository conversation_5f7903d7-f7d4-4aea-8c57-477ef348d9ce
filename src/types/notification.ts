/**
import type { PageData } from '@/types/common/api'
 * 通知相关类型定义
 */

// ==================== 消息相关 ====================

export interface NotificationMessage {
  messageId: string
  channelType: ChannelType
  recipient: string
  title?: string
  content: string
  templateId?: string
  variables?: Record<string, unknown>
  priority: number
  status: MessageStatus
  sendTime?: string
  readTime?: string
  externalId?: string
  errorMessage?: string
  retryCount: number
  maxRetries: number
  businessType?: string
  businessId?: string
  createTime: string
  updateTime: string
}

export interface NotificationSendRequest {
  channelType: string
  recipients: string[]
  title?: string
  content?: string
  templateId?: string
  variables?: Record<string, unknown>
  priority?: number
  async?: boolean
  batch?: boolean
  businessType?: string
  businessId?: string
  scheduledTime?: string
  expiryTime?: string
}

export interface NotificationQueryRequest {
  page: number
  size: number
  channelType?: string
  recipient?: string
  status?: string
  businessType?: string
  businessId?: string
  startTime?: string
  endTime?: string
  keyword?: string
}

// ==================== 消息模板相关 ====================

export interface NotificationTemplate {
  id: string
  name: string
  code: string
  channelType: ChannelType
  category: TemplateCategory
  subject?: string
  content: string
  variables: string[]
  enabled: boolean
  description?: string
  createTime: string
  updateTime: string
  createBy?: string
  updateBy?: string
}

export interface NotificationTemplateQueryRequest {
  page: number
  size: number
  name?: string
  code?: string
  channelType?: string
  category?: string
  enabled?: boolean
  keyword?: string
}

// ==================== 消息订阅相关 ====================

export interface NotificationSubscription {
  id: string
  userId: string
  category: string
  channelTypes: ChannelType[]
  enabled: boolean
  settings?: SubscriptionSettings
  createTime: string
  updateTime: string
}

export interface SubscriptionSettings {
  frequency?: 'IMMEDIATE' | 'DAILY' | 'WEEKLY'
  quietHours?: {
    start: string
    end: string
  }
  keywords?: string[]
  excludeKeywords?: string[]
}

export interface SubscriptionCategory {
  code: string
  name: string
  description?: string
  defaultChannels: ChannelType[]
  required: boolean
  order: number
}

// ==================== 统计分析相关 ====================

export interface NotificationStatistics {
  totalSent: number
  successCount: number
  failureCount: number
  pendingCount: number
  successRate: number
  channelStats: ChannelStatistics[]
  timeRange: {
    startDate: string
    endDate: string
  }
  updateTime: string
}

export interface ChannelStatistics {
  channelType: ChannelType
  totalSent: number
  successCount: number
  failureCount: number
  successRate: number
  averageDeliveryTime: number
}

export interface UserActivityStatistics {
  userId: string
  userName?: string
  totalReceived: number
  readCount: number
  unreadCount: number
  readRate: number
  lastActiveTime?: string
  preferredChannels: ChannelType[]
}

export interface TrendStatistics {
  date: string
  totalSent: number
  successCount: number
  failureCount: number
  channelBreakdown: Record<string, number>
}

// ==================== 渠道管理相关 ====================

export interface NotificationChannelStatus {
  channelType: ChannelType
  enabled: boolean
  available: boolean
  status: 'UP' | 'DOWN' | 'DEGRADED'
  lastCheckTime: string
  errorMessage?: string
  configuration: ChannelConfiguration
  statistics: ChannelStatistics
}

export interface ChannelConfiguration {
  provider?: string
  endpoint?: string
  timeout?: number
  retryCount?: number
  rateLimit?: number

  [key: string]: unknown
}

// ==================== WebSocket 实时通知相关 ====================

export interface WebSocketMessage {
  type: 'NOTIFICATION' | 'SYSTEM' | 'HEARTBEAT'

  data: unknown
  timestamp: string
}

export interface RealTimeNotification {
  messageId: string
  type: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS'
  title: string
  content: string
  actions?: NotificationAction[]
  autoClose?: boolean
  duration?: number
  createTime: string
}

export interface NotificationAction {
  label: string
  action: string
  type?: 'primary' | 'success' | 'warning' | 'danger'
  url?: string
}

// ==================== 系统配置相关 ====================

export interface NotificationSystemConfig {
  globalSettings: {
    enabled: boolean
    defaultPriority: number
    maxRetries: number
    retryInterval: number
    batchSize: number
    queueSize: number
  }
  channelConfigs: Record<string, ChannelConfiguration>
  templateSettings: {
    defaultLanguage: string
    supportedLanguages: string[]
    variablePrefix: string
    variableSuffix: string
  }
  securitySettings: {
    encryptionEnabled: boolean
    signatureRequired: boolean
    rateLimitEnabled: boolean
    blacklistEnabled: boolean
  }
}

// ==================== 枚举类型 ====================

export enum ChannelType {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  INTERNAL = 'INTERNAL',
  WECHAT = 'WECHAT',
  DINGTALK = 'DINGTALK',
  WEBHOOK = 'WEBHOOK',
  PUSH = 'PUSH'
}

export enum MessageStatus {
  PENDING = 'PENDING',
  SENDING = 'SENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED'
}

export enum TemplateCategory {
  SYSTEM = 'SYSTEM',
  WORKFLOW = 'WORKFLOW',
  BUSINESS = 'BUSINESS',
  MARKETING = 'MARKETING',
  SECURITY = 'SECURITY',
  CUSTOM = 'CUSTOM'
}

export enum NotificationPriority {
  LOW = 10,
  NORMAL = 50,
  HIGH = 80,
  URGENT = 100
}

// ==================== 表单相关 ====================

export interface NotificationForm {
  channelType: ChannelType
  recipients: string[]
  title?: string
  content: string
  templateId?: string
  variables?: Record<string, unknown>
  priority: NotificationPriority
  scheduledTime?: string
  expiryTime?: string
  businessType?: string
  businessId?: string
}

export interface TemplateForm {
  name: string
  code: string
  channelType: ChannelType
  category: TemplateCategory
  subject?: string
  content: string
  variables: string[]
  enabled: boolean
  description?: string
}

export interface SubscriptionForm {
  category: string
  channelTypes: ChannelType[]
  enabled: boolean
  frequency?: 'IMMEDIATE' | 'DAILY' | 'WEEKLY'
  quietHours?: {
    start: string
    end: string
  }
}

// ==================== 响应类型 ====================

export interface NotificationResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp: string
}

// 使用通用的PageData类型
export type PageResponse<T> = PageData<T>

// ==================== 错误类型 ====================

export interface NotificationError {
  code: string
  message: string
  details?: string
  timestamp: string
  requestId?: string
}

// ==================== 批量操作相关 ====================

export interface BatchOperation {
  operation: 'SEND' | 'DELETE' | 'READ' | 'CANCEL'
  targets: string[]
  parameters?: Record<string, unknown>
}

export interface BatchResult {
  total: number
  success: number
  failure: number
  results: BatchItemResult[]
}

export interface BatchItemResult {
  target: string
  success: boolean
  error?: string

  data?: unknown
}

// ==================== 导入导出相关 ====================

export interface ImportTemplateRequest {
  file: File
  overwrite: boolean
  category?: TemplateCategory
}

export interface ExportTemplateRequest {
  templateIds?: string[]
  category?: TemplateCategory
  format: 'JSON' | 'EXCEL' | 'CSV'
}

export interface ImportResult {
  total: number
  success: number
  failure: number
  skipped: number
  details: ImportItemResult[]
}

export interface ImportItemResult {
  name: string
  status: 'SUCCESS' | 'FAILURE' | 'SKIPPED'
  error?: string

  data?: unknown
}

// ==================== 监控告警相关 ====================

export interface NotificationAlert {
  id: string
  type: 'DELIVERY_FAILURE' | 'RATE_LIMIT' | 'CHANNEL_DOWN' | 'QUEUE_FULL'
  level: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL'
  message: string
  channelType?: ChannelType
  count: number
  firstOccurrence: string
  lastOccurrence: string
  resolved: boolean
  resolveTime?: string
}

export interface MonitoringMetrics {
  queueSize: number
  processingRate: number
  errorRate: number
  averageDeliveryTime: number
  channelHealth: Record<string, boolean>
  systemLoad: number
  memoryUsage: number
  updateTime: string
}
