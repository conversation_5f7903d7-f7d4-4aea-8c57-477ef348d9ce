/**
 * 组织管理视图层使用的类型定义
 * 用于适配页面组件的数据结构
 */

import type {
  Organization as BaseOrganization,
  OrganizationType,
  OrganizationStatus,
  Establishment as BaseEstablishment
} from './organization'

// 视图层使用的组织机构接口（简化字段名）
export interface Organization {
  id: string // institutionId
  name: string // institutionName
  code: string // institutionCode
  type: string // institutionType (转为字符串)
  parentId?: string // parentInstitutionId
  parentName?: string // parentInstitutionName
  establishDate?: string // establishDate
  status: string // status (转为字符串)
  level?: number // level
  leader?: string // leaderName
  phone?: string // contactPhone
  establishment?: Establishment // 编制信息
  children?: Organization[] // 子节点
  [key: string]: any // 支持动态属性访问
}

// 页面使用的编制信息
export interface Establishment {
  approved: number // 核定编制
  current: number // 在编人数
}

// 查询请求接口
export interface QueryOrganizationRequest {
  name?: string
  code?: string
  type?: string
  status?: string
  establishDateRange?: string[]
  establishmentStatus?: string
  parentId?: string
  level?: number
  includeChildren?: boolean
  page?: number
  pageSize?: number
}

// 转换函数：基础类型转视图类型
export function toViewOrganization(org: BaseOrganization): Organization {
  const viewOrg: Organization = {
    id: org.institutionId,
    name: org.institutionName,
    code: org.institutionCode,
    type: org.institutionType.toLowerCase().replace(/_/g, ''),
    status: org.status.toLowerCase(),
    level: org.level,
    leader: org.leaderName,
    phone: org.contactPhone
  }

  if (org.parentInstitutionId) {
    viewOrg.parentId = org.parentInstitutionId
  }

  if (org.parentInstitutionName) {
    viewOrg.parentName = org.parentInstitutionName
  }

  if (org.establishDate) {
    viewOrg.establishDate = org.establishDate
  }

  // 转换编制信息
  if (org.establishmentCount !== undefined) {
    viewOrg.establishment = {
      approved: org.establishmentCount,
      current: org.employeeCount || 0
    }
  }

  // 递归转换子节点
  if ((org as any).children && Array.isArray((org as any).children)) {
    viewOrg.children = (org as any).children.map(toViewOrganization)
  }

  return viewOrg
}

// 转换函数：视图类型转基础类型（用于保存时）
export function toBaseOrganization(viewOrg: Organization): Partial<BaseOrganization> {
  const baseOrg: Partial<BaseOrganization> = {
    institutionId: viewOrg.id,
    institutionName: viewOrg.name,
    institutionCode: viewOrg.code,
    institutionType: viewOrg.type.toUpperCase() as OrganizationType,
    status: viewOrg.status.toUpperCase() as OrganizationStatus,
    level: viewOrg.level,
    leaderName: viewOrg.leader,
    contactPhone: viewOrg.phone
  }

  if (viewOrg.parentId) {
    baseOrg.parentInstitutionId = viewOrg.parentId
  }

  if (viewOrg.parentName) {
    baseOrg.parentInstitutionName = viewOrg.parentName
  }

  if (viewOrg.establishDate) {
    baseOrg.establishDate = viewOrg.establishDate
  }

  if (viewOrg.establishment) {
    baseOrg.establishmentCount = viewOrg.establishment.approved
    baseOrg.employeeCount = viewOrg.establishment.current
  }

  return baseOrg
}

// 批量转换
export function toViewOrganizationList(orgs: BaseOrganization[]): Organization[] {
  return orgs.map(toViewOrganization)
}

// 统计数据类型定义
export interface TypeDistributionItem {
  type: string
  name?: string
  count: number
}

export interface EstablishmentDepartmentItem {
  id?: string
  name: string
  approved: number
  current: number
  vacancy?: number
  usageRate?: number
}

export interface StatisticsData {
  total: number
  totalStaff: number
  totalApproved: number
  usageRate: number
  activeCount?: number
  disabledCount?: number
}

export interface TypeStatisticsData {
  typeDistribution: TypeDistributionItem[]
  totalTypes?: number
  lastUpdateTime?: string
}

export interface EstablishmentStatisticsData {
  departments: EstablishmentDepartmentItem[]
  summary?: {
    totalDepartments: number
    totalApproved: number
    totalCurrent: number
    totalVacancy: number
    overStaffedCount: number
    fullyStaffedCount: number
    underStaffedCount: number
  }
  lastUpdateTime?: string
}

// 导出常用的类型映射函数
export const getOrgTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    college: '学院',
    department: '部门',
    office: '处室',
    center: '中心',
    institute: '研究所',
    teaching_dept: '教学部',
    research_inst: '科研机构',
    admin_dept: '行政机构',
    direct_unit: '直属单位',
    temp_org: '临时组织',
    school: '学校'
  }
  return typeMap[type.toLowerCase()] || type
}

export const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    normal: '正常',
    active: '正常',
    revoked: '已撤销',
    cancelled: '已撤销',
    merged: '已合并',
    disabled: '已停用'
  }
  return statusMap[status.toLowerCase()] || status
}
