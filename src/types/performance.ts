// 绩效考核相关类型定义

// 考核方案
export interface AssessmentPlan {
  id: string
  name: string
  type: 'annual' | 'quarterly' | 'monthly' | 'project' // 年度/季度/月度/项目
  templateId?: string
  status: 'draft' | 'published' | 'active' | 'completed'
  period: string // 考核周期
  startDate: string
  endDate: string
  description?: string
  // 参与范围
  scope: {
    departments: string[]
    positions?: string[]
    employees?: string[]
    excludeEmployees?: string[]
  }
  // 考核配置
  config: {
    allowSelfEval: boolean
    allow360Review: boolean
    requireCalibration: boolean
    enableAppeal: boolean
    gradeDistribution?: GradeDistribution
  }
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 考核指标
export interface AssessmentMetric {
  id: string
  name: string
  category: 'kpi' | 'okr' | 'behavior' | 'skill' | 'custom'
  type: 'quantitative' | 'qualitative' // 定量/定性
  unit?: string // 单位
  targetValue?: number // 目标值
  weight: number // 权重
  description?: string
  // 评分标准
  scoringCriteria: ScoringCriterion[]
  // 数据来源
  dataSource?: {
    type: 'manual' | 'system' | 'formula'
    formula?: string
    systemField?: string
  }
  status: 'active' | 'inactive'
  applicableTo: {
    departments?: string[]
    positions?: string[]
    levels?: string[]
  }
}

// 评分标准
export interface ScoringCriterion {
  level: number // 等级 1-5
  minValue?: number
  maxValue?: number
  description: string
  score: number // 得分
}

// 考核任务
export interface AssessmentTask {
  id: string
  planId: string
  planName: string
  employeeId: string
  employeeName: string
  department: string
  position: string
  status:
    | 'pending'
    | 'self_evaluating'
    | 'supervisor_evaluating'
    | 'calibrating'
    | 'completed'
    | 'appealing'
  currentStep: string
  // 各阶段截止时间
  deadlines: {
    selfEval?: string
    supervisorEval?: string
    calibration?: string
    appeal?: string
  }
  // 评价人
  evaluators: {
    self?: string
    supervisor?: string
    peers?: string[]
    subordinates?: string[]
    others?: string[]
  }
  // 考核结果
  result?: AssessmentResult
  createdAt: string
  updatedAt: string
}

// 考核结果
export interface AssessmentResult {
  totalScore: number
  grade: string // A/B/C/D/E
  ranking?: number // 排名
  // 各维度得分
  dimensionScores: {
    dimension: string
    score: number
    weight: number
    metrics: MetricScore[]
  }[]
  // 评语
  comments: {
    self?: string
    supervisor?: string
    calibration?: string
  }
  // 改进建议
  improvements?: string[]
  appealStatus?: 'none' | 'pending' | 'approved' | 'rejected'
  finalizedAt?: string
}

// 指标得分
export interface MetricScore {
  metricId: string
  metricName: string
  actualValue?: number | string
  targetValue?: number | string
  score: number
  weight: number
  comment?: string
}

// 等级分布
export interface GradeDistribution {
  A: { min: number; max: number } // 优秀
  B: { min: number; max: number } // 良好
  C: { min: number; max: number } // 合格
  D: { min: number; max: number } // 需改进
  E?: { min: number; max: number } // 不合格
}

// 360度评价
export interface Review360 {
  id: string
  taskId: string
  evaluatorId: string
  evaluatorName: string
  evaluatorType: 'self' | 'supervisor' | 'peer' | 'subordinate' | 'other'
  status: 'pending' | 'in_progress' | 'completed'
  // 评价内容
  evaluations: {
    dimensionId: string
    dimensionName: string
    score: number
    comment?: string
  }[]
  overallComment?: string
  submittedAt?: string
}

// 绩效看板数据
export interface PerformanceDashboard {
  // 个人绩效
  personal?: {
    currentScore: number
    currentGrade: string
    trend: number[] // 历史趋势
    ranking: {
      department: number
      company?: number
    }
    strengths: string[]
    weaknesses: string[]
  }
  // 团队绩效（管理者视角）
  team?: {
    averageScore: number
    gradeDistribution: Record<string, number>
    topPerformers: EmployeePerformance[]
    needsImprovement: EmployeePerformance[]
    completionRate: number
  }
  // 部门绩效
  department?: {
    averageScore: number
    ranking: number
    trend: number[]
    comparison: DepartmentComparison[]
  }
}

// 员工绩效简要信息
export interface EmployeePerformance {
  employeeId: string
  employeeName: string
  position: string
  score: number
  grade: string
  trend: 'up' | 'down' | 'stable'
}

// 部门对比
export interface DepartmentComparison {
  departmentId: string
  departmentName: string
  averageScore: number
  employeeCount: number
}

// 绩效改进计划
export interface ImprovementPlan {
  id: string
  employeeId: string
  employeeName: string
  assessmentId: string
  period: string
  status: 'draft' | 'active' | 'completed'
  // 改进目标
  goals: {
    area: string
    currentLevel: string
    targetLevel: string
    actions: string[]
    deadline: string
    progress?: number
  }[]
  // 支持措施
  support: {
    training?: string[]
    mentoring?: string
    resources?: string[]
  }
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 考核流程记录
export interface AssessmentFlowRecord {
  id: string
  taskId: string
  action: string
  operator: string
  operatorName: string
  comment?: string
  createdAt: string
}

// 绩效报表
export interface PerformanceReport {
  type: 'individual' | 'department' | 'company'
  period: string
  generatedAt: string

  data: unknown // 根据报表类型不同而不同

  charts?: unknown[] // 图表数据
}
