/**
import type { SpringPageData } from '@/types/common/api'
 * 奖惩记录管理模块 TypeScript 类型定义
 *
 * 此文件定义了与后端 AwardsPunishmentsService 完全兼容的 TypeScript 类型
 * 确保前后端数据结构100%匹配
 *
 * <AUTHOR>
 * @since 1.0.0
 */

// ==================== 枚举类型定义 ====================

/**
 * 奖惩类型枚举
 */
export enum AwardsPunishmentsType {
  AWARD = 'AWARD', // 奖励
  PUNISHMENT = 'PUNISHMENT' // 惩罚
}

/**
 * 奖惩类别枚举
 */
export enum AwardsPunishmentsCategory {
  COMMENDATION = 'COMMENDATION', // 表彰
  MERIT = 'MERIT', // 嘉奖
  MAJOR_MERIT = 'MAJOR_MERIT', // 记功
  WARNING = 'WARNING', // 警告
  DEMERIT = 'DEMERIT', // 记过
  MAJOR_DEMERIT = 'MAJOR_DEMERIT', // 记大过
  DISMISSAL = 'DISMISSAL', // 开除
  OTHER = 'OTHER' // 其他
}

/**
 * 审批状态枚举
 */
export enum ApprovalStatus {
  PENDING = 'PENDING', // 待审批
  APPROVED = 'APPROVED', // 已审批
  REJECTED = 'REJECTED' // 已驳回
}

/**
 * 严重程度枚举
 */
export enum Severity {
  LOW = 'LOW', // 轻微
  MEDIUM = 'MEDIUM', // 一般
  HIGH = 'HIGH', // 严重
  CRITICAL = 'CRITICAL' // 特别严重
}

/**
 * 排序方向枚举
 */
export enum SortDirection {
  ASC = 'ASC', // 升序
  DESC = 'DESC' // 降序
}

// ==================== 基础实体类型 ====================

/**
 * 奖惩记录实体类型（与后端 AwardsPunishments 实体完全匹配）
 */
export interface AwardsPunishments {
  /** 主键ID */
  id?: string

  /** 员工ID */
  employeeId: string

  /** 员工姓名（冗余字段，用于显示） */
  employeeName?: string

  /** 奖惩类型 */
  type: AwardsPunishmentsType

  /** 奖惩类型名称（用于显示） */
  typeName?: string

  /** 奖惩类别 */
  category: AwardsPunishmentsCategory

  /** 奖惩类别名称（用于显示） */
  categoryName?: string

  /** 奖惩标题 */
  title: string

  /** 奖惩原因 */
  reason?: string

  /** 详细描述 */
  description?: string

  /** 奖惩日期 */
  awardDate?: string

  /** 颁发单位 */
  issuingUnit?: string

  /** 审批人 */
  approver?: string

  /** 审批状态 */
  approvalStatus: ApprovalStatus

  /** 审批状态名称（用于显示） */
  approvalStatusName?: string

  /** 审批日期 */
  approvalDate?: string

  /** 审批备注 */
  approvalNote?: string

  /** 严重程度 */
  severity?: Severity

  /** 严重程度名称（用于显示） */
  severityName?: string

  /** 奖惩金额 */
  amount?: number

  /** 货币类型 */
  currency?: string

  /** 相关文档URL */
  documentUrl?: string

  /** 是否公开 */
  isPublic?: boolean

  /** 生效日期 */
  effectiveDate?: string

  /** 失效日期 */
  expiryDate?: string

  /** 相关政策 */
  relatedPolicy?: string

  /** 备注 */
  remarks?: string

  /** 创建时间 */
  createTime?: string

  /** 创建人 */
  createBy?: string

  /** 更新时间 */
  updateTime?: string

  /** 更新人 */
  updateBy?: string

  /** 是否删除 */
  deleted?: boolean
}

// ==================== 请求类型定义 ====================

/**
 * 奖惩记录创建请求类型（与后端 AwardsPunishmentsCreateRequest 完全匹配）
 */
export interface AwardsPunishmentsCreateRequest {
  /** 员工ID */
  employeeId: string

  /** 奖惩类型 */
  type: AwardsPunishmentsType

  /** 奖惩类别 */
  category: AwardsPunishmentsCategory

  /** 奖惩标题 */
  title: string

  /** 奖惩原因 */
  reason?: string

  /** 详细描述 */
  description?: string

  /** 奖惩日期 */
  awardDate?: string

  /** 颁发单位 */
  issuingUnit?: string

  /** 审批人 */
  approver?: string

  /** 严重程度 */
  severity?: Severity

  /** 奖惩金额 */
  amount?: number

  /** 货币类型 */
  currency?: string

  /** 相关文档URL */
  documentUrl?: string

  /** 是否公开 */
  isPublic?: boolean

  /** 生效日期 */
  effectiveDate?: string

  /** 失效日期 */
  expiryDate?: string

  /** 相关政策 */
  relatedPolicy?: string

  /** 备注 */
  remarks?: string
}

/**
 * 奖惩记录更新请求类型（与后端 AwardsPunishmentsUpdateRequest 完全匹配）
 */
export interface AwardsPunishmentsUpdateRequest {
  /** 奖惩类型 */
  type?: AwardsPunishmentsType

  /** 奖惩类别 */
  category?: AwardsPunishmentsCategory

  /** 奖惩标题 */
  title?: string

  /** 奖惩原因 */
  reason?: string

  /** 详细描述 */
  description?: string

  /** 奖惩日期 */
  awardDate?: string

  /** 颁发单位 */
  issuingUnit?: string

  /** 审批人 */
  approver?: string

  /** 严重程度 */
  severity?: Severity

  /** 奖惩金额 */
  amount?: number

  /** 货币类型 */
  currency?: string

  /** 相关文档URL */
  documentUrl?: string

  /** 是否公开 */
  isPublic?: boolean

  /** 生效日期 */
  effectiveDate?: string

  /** 失效日期 */
  expiryDate?: string

  /** 相关政策 */
  relatedPolicy?: string

  /** 备注 */
  remarks?: string
}

/**
 * 奖惩记录查询请求类型（与后端 AwardsPunishmentsQueryRequest 完全匹配）
 */
export interface AwardsPunishmentsQueryRequest {
  /** 页码（从0开始） */
  page?: number

  /** 页大小 */
  size?: number

  /** 关键词搜索 */
  keyword?: string

  /** 员工ID */
  employeeId?: string

  /** 员工ID列表 */
  employeeIds?: string[]

  /** 奖惩类型 */
  type?: AwardsPunishmentsType

  /** 奖惩类型列表 */
  types?: AwardsPunishmentsType[]

  /** 奖惩类别 */
  category?: AwardsPunishmentsCategory

  /** 奖惩类别列表 */
  categories?: AwardsPunishmentsCategory[]

  /** 奖惩标题 */
  title?: string

  /** 颁发单位 */
  issuingUnit?: string

  /** 审批人 */
  approver?: string

  /** 审批状态 */
  approvalStatus?: ApprovalStatus

  /** 审批状态列表 */
  approvalStatuses?: ApprovalStatus[]

  /** 严重程度 */
  severity?: Severity

  /** 严重程度列表 */
  severities?: Severity[]

  /** 奖惩日期范围-开始 */
  awardDateStart?: string

  /** 奖惩日期范围-结束 */
  awardDateEnd?: string

  /** 审批日期范围-开始 */
  approvalDateStart?: string

  /** 审批日期范围-结束 */
  approvalDateEnd?: string

  /** 生效日期范围-开始 */
  effectiveDateStart?: string

  /** 生效日期范围-结束 */
  effectiveDateEnd?: string

  /** 最小金额 */
  minAmount?: number

  /** 最大金额 */
  maxAmount?: number

  /** 货币类型 */
  currency?: string

  /** 是否公开 */
  isPublic?: boolean

  /** 是否有文档 */
  hasDocument?: boolean

  /** 是否已过期 */
  isExpired?: boolean

  /** 排序字段 */
  sortBy?: string

  /** 排序方向 */
  sortDirection?: SortDirection
}

// ==================== 响应类型定义 ====================

/**
 * 分页响应类型（与后端 Page<T> 完全匹配）
 */
// 使用通用的SpringPageData类型
export type PageResponse<T> = SpringPageData<T>

/**
 * 统一响应结果类型（与后端 Result<T> 完全匹配）
 */
export interface Result<T> {
  /** 响应码 */
  code: number

  /** 响应消息 */
  message?: string

  /** 响应数据 */
  data?: T

  /** 时间戳 */
  timestamp?: number
}

// ==================== 统计分析类型 ====================

/**
 * 奖惩记录统计信息类型
 */
export interface AwardsPunishmentsStatistics {
  /** 总数量 */
  totalCount: number

  /** 奖励数量 */
  awardCount: number

  /** 惩罚数量 */
  punishmentCount: number

  /** 待审批数量 */
  pendingApprovalCount: number

  /** 已审批数量 */
  approvedCount: number

  /** 已驳回数量 */
  rejectedCount: number

  /** 类型分布 */
  typeDistribution: Record<string, number>

  /** 类别分布 */
  categoryDistribution: Record<string, number>

  /** 严重程度分布 */
  severityDistribution: Record<string, number>

  /** 审批状态分布 */
  approvalStatusDistribution: Record<string, number>

  /** 月度趋势 */
  monthlyTrend: MonthlyTrendItem[]

  /** 热门颁发单位 */
  topIssuingUnits: IssuingUnitStats[]

  /** 热门审批人 */
  topApprovers: ApproverStats[]

  /** 平均金额 */
  averageAmount: number

  /** 总金额 */
  totalAmount: number
}

/**
 * 月度趋势项
 */
export interface MonthlyTrendItem {
  /** 月份 */
  month: string

  /** 奖励数量 */
  awardCount: number

  /** 惩罚数量 */
  punishmentCount: number
}

/**
 * 颁发单位统计
 */
export interface IssuingUnitStats {
  /** 颁发单位 */
  issuingUnit: string

  /** 数量 */
  count: number
}

/**
 * 审批人统计
 */
export interface ApproverStats {
  /** 审批人 */
  approver: string

  /** 数量 */
  count: number
}

/**
 * 员工奖惩汇总
 */
export interface EmployeeSummary {
  /** 总奖励数 */
  totalAwards: number

  /** 总惩罚数 */
  totalPunishments: number

  /** 最新奖励 */
  latestAward?: AwardsPunishments

  /** 最新惩罚 */
  latestPunishment?: AwardsPunishments
}

// ==================== 审批管理类型 ====================

/**
 * 审批请求
 */
export interface ApprovalRequest {
  /** 审批状态 */
  status: ApprovalStatus

  /** 审批备注 */
  note?: string
}

/**
 * 批量审批请求
 */
export interface BatchApprovalRequest {
  /** ID列表 */
  ids: string[]

  /** 审批状态 */
  status: ApprovalStatus

  /** 审批备注 */
  note?: string
}

/**
 * 撤销审批请求
 */
export interface RevokeApprovalRequest {
  /** 撤销原因 */
  reason?: string
}

// ==================== 批量操作类型 ====================

/**
 * 批量删除请求
 */
export interface BatchDeleteRequest {
  /** ID列表 */
  ids: string[]
}

/**
 * 批量公开设置请求
 */
export interface BatchPublicRequest {
  /** ID列表 */
  ids: string[]

  /** 是否公开 */
  isPublic: boolean
}

/**
 * 批量导入结果
 */
export interface ImportResult {
  /** 成功数量 */
  successCount: number

  /** 失败数量 */
  failureCount: number

  /** 错误信息列表 */
  errors: string[]
}

// ==================== 表单验证类型 ====================

/**
 * 表单验证规则类型
 */
export interface FormValidationRule {
  /** 是否必填 */
  required?: boolean

  /** 错误消息 */
  message?: string

  /** 触发方式 */
  trigger?: 'blur' | 'change'

  /** 最小长度 */
  min?: number

  /** 最大长度 */
  max?: number

  /** 最小值 */
  minValue?: number

  /** 最大值 */
  maxValue?: number

  /** 正则表达式 */
  pattern?: RegExp

  /** 自定义验证函数 */

  validator?: (rule: unknown, value: unknown, callback: unknown) => void
}

/**
 * 表单验证规则集合
 */
export interface AwardsPunishmentsFormRules {
  employeeId: FormValidationRule[]
  type: FormValidationRule[]
  category: FormValidationRule[]
  title: FormValidationRule[]
  reason?: FormValidationRule[]
  awardDate?: FormValidationRule[]
  issuingUnit?: FormValidationRule[]
  approver?: FormValidationRule[]
  amount?: FormValidationRule[]
  effectiveDate?: FormValidationRule[]
  expiryDate?: FormValidationRule[]
}

// ==================== UI组件类型 ====================

/**
 * 表格列配置
 */
export interface TableColumn {
  /** 属性名 */
  prop: string

  /** 列标题 */
  label: string

  /** 列宽度 */
  width?: number

  /** 最小宽度 */
  minWidth?: number

  /** 是否可排序 */
  sortable?: boolean

  /** 是否显示溢出提示 */
  showOverflowTooltip?: boolean

  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
}

/**
 * 搜索表单配置
 */
export interface SearchFormConfig {
  /** 是否显示高级搜索 */
  showAdvanced: boolean

  /** 搜索字段配置 */
  fields: SearchFieldConfig[]
}

/**
 * 搜索字段配置
 */
export interface SearchFieldConfig {
  /** 字段名 */
  field: string

  /** 字段标签 */
  label: string

  /** 字段类型 */
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'switch'

  /** 选项列表（用于select类型） */

  options?: Array<{ label: string; value: unknown }>

  /** 占位符 */
  placeholder?: string

  /** 是否在基础搜索中显示 */
  basic?: boolean
}
