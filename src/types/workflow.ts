/**
 * 工作流相关类型定义
 */

// ==================== 流程定义相关 ====================

export interface ProcessDefinitionDTO {
  id: string
  key: string
  name: string
  version: number
  category: string
  description?: string
  deploymentId: string
  deploymentTime: string
  resourceName: string
  suspended: boolean
  tenantId?: string
  hasStartFormKey: boolean
  hasGraphicalNotation: boolean
  startFormDefined: boolean
  suspensionState: number
  versionTag?: string
}

export interface ProcessDefinitionQueryRequest {
  page: number
  size: number
  key?: string
  name?: string
  category?: string
  suspended?: boolean
  tenantId?: string
  latest?: boolean
  startableByUser?: string
}

export interface ProcessDeploymentRequest {
  name: string
  category?: string
  tenantId?: string
  activate?: boolean
  description?: string
}

export interface ProcessDefinitionMetadataUpdateRequest {
  name?: string
  description?: string
  category?: string
  enabled?: boolean
  updateReason?: string
}

// ==================== 流程实例相关 ====================

export interface ProcessInstanceResponse {
  id: string
  processDefinitionId: string
  processDefinitionKey: string
  processDefinitionName: string
  processDefinitionVersion: number
  businessKey?: string
  startTime: string
  endTime?: string
  startUserId?: string
  suspended: boolean
  tenantId?: string
  variables?: Record<string, unknown>
  activeActivityIds?: string[]
}

export interface ProcessStartRequest {
  processDefinitionId?: string
  processDefinitionKey?: string
  businessKey?: string
  variables?: Record<string, unknown>
  tenantId?: string
  startFormData?: Record<string, unknown>
}

// ==================== 任务相关 ====================

export interface TaskResponse {
  id: string
  name: string
  description?: string
  assignee?: string
  owner?: string
  createTime: string
  dueDate?: string
  priority: number
  processInstanceId: string
  processDefinitionId: string
  processDefinitionKey: string
  taskDefinitionKey: string
  formKey?: string
  suspended: boolean
  tenantId?: string
  variables?: Record<string, unknown>
  localVariables?: Record<string, unknown>
}

export interface TaskCompleteRequest {
  variables?: Record<string, unknown>
  localVariables?: Record<string, unknown>
  formData?: Record<string, unknown>
  comment?: string
}

export interface TaskQueryRequest {
  page: number
  size: number
  assignee?: string
  candidateUser?: string
  candidateGroup?: string
  processInstanceId?: string
  processDefinitionKey?: string
  taskDefinitionKey?: string
  name?: string
  priority?: number
  createTimeAfter?: string
  createTimeBefore?: string
  dueTimeAfter?: string
  dueTimeBefore?: string
  suspended?: boolean
  active?: boolean
}

// ==================== 流程监控相关 ====================

export interface ProcessMonitoringDashboard {
  activeInstances: number
  completedInstances: number
  totalInstances: number
  errorInstances: number
  completionRate: number
  timeRange: number
  updateTime: string
  recentActivities: ProcessActivity[]
}

export interface ProcessActivity {
  processInstanceId: string
  processDefinitionKey: string
  processDefinitionName: string
  startTime: string
  endTime?: string
  startUserId?: string
  status: 'ACTIVE' | 'COMPLETED' | 'SUSPENDED' | 'CANCELLED'
}

export interface ProcessStatistics {
  totalInstances: number
  completedInstances: number
  activeInstances: number
  completionRate: number
  averageDuration: number
  days: number
  statisticsTime: string
}

export interface ProcessTrend {
  date: string
  startedCount: number
  completedCount: number
  activeCount: number
}

export interface ProcessBottleneck {
  activityId: string
  activityName: string
  averageDuration: number
  instanceCount: number
  bottleneckScore: number
}

// ==================== 流程可视化相关 ====================

export interface ProcessVisualizationData {
  processDefinitionId: string
  processDefinitionKey: string
  processDefinitionName: string
  version: number
  bpmnXml: string
  elements: ProcessElement[]
}

export interface ProcessElement {
  id: string
  name: string
  type: string
  x?: number
  y?: number
  width?: number
  height?: number
}

export interface ProcessInstanceVisualization {
  processInstanceId: string
  processDefinitionId: string
  processDefinitionKey: string
  processDefinitionName: string
  activeActivityIds: string[]
  completedActivityIds: string[]
  startTime: string
  suspended: boolean
  bpmnXml: string
}

// ==================== 版本管理相关 ====================

export interface ProcessVersionComparison {
  version1: ProcessDefinitionDTO
  version2: ProcessDefinitionDTO
  differences: ProcessDifference[]
  summary: {
    addedElements: number
    removedElements: number
    modifiedElements: number
  }
}

export interface ProcessDifference {
  type: 'ADDED' | 'REMOVED' | 'MODIFIED'
  elementId: string
  elementName: string
  elementType: string
  description: string

  oldValue?: unknown

  newValue?: unknown
}

// ==================== 导入导出相关 ====================

export interface ImportResult {
  total: number
  success: number
  failure: number
  skipped: number
  successList: string[]
  failureList: string[]
  skippedList: string[]
  importTime: string
}

export interface ValidationResult {
  valid: boolean
  processId?: string
  processName?: string
  elementCount?: number
  error?: string
  warnings?: string[]
  validationTime: string
}

// ==================== 告警相关 ====================

export interface ProcessAlert {
  id: string
  name: string
  level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  status: 'ACTIVE' | 'RESOLVED'
  condition: string
  message: string
  processDefinitionKey?: string
  createTime: string
  resolveTime?: string
  notificationChannels: string[]
  recipients: string[]
}

export interface AlertRule {
  id?: string
  name: string
  condition: string
  level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  enabled: boolean
  processDefinitionKey?: string
  notificationChannels: string[]
  recipients: string[]
  description?: string
  createTime?: string
  updateTime?: string
}

// ==================== 系统健康相关 ====================

export interface SystemHealth {
  status: 'UP' | 'DOWN' | 'DEGRADED'
  database: 'UP' | 'DOWN'
  flowableEngine: 'UP' | 'DOWN'
  memory: {
    total: number
    used: number
    free: number
    usagePercent: number
  }
  processDefinitionCount: number
  engineVersion?: string
  checkTime: string
  databaseError?: string
  engineError?: string
}

export interface SystemMetrics {
  processDefinitions: {
    total: number
    active: number
    suspended: number
  }
  processInstances: {
    active: number
    historic: number
    total: number
  }
  tasks: {
    active: number
    historic: number
  }
  system: {
    processors: number
    totalMemory: number
    freeMemory: number
    maxMemory: number
  }
  metricsTime: string
}

// ==================== 表单相关 ====================

export interface FormProperty {
  id: string
  name: string
  type: 'string' | 'long' | 'boolean' | 'date' | 'enum'

  value?: unknown
  readable: boolean
  writable: boolean
  required: boolean
  enumValues?: FormEnumValue[]
}

export interface FormEnumValue {
  id: string
  name: string
}

export interface FormData {
  formKey?: string
  formProperties: FormProperty[]
  taskId?: string
  processDefinitionId?: string
}

// ==================== 历史相关 ====================

export interface HistoricProcessInstance {
  id: string
  processDefinitionId: string
  processDefinitionKey: string
  processDefinitionName: string
  processDefinitionVersion: number
  businessKey?: string
  startTime: string
  endTime?: string
  durationInMillis?: number
  startUserId?: string
  deleteReason?: string
  tenantId?: string
}

export interface HistoricTaskInstance {
  id: string
  name: string
  description?: string
  assignee?: string
  owner?: string
  createTime: string
  endTime?: string
  durationInMillis?: number
  deleteReason?: string
  processInstanceId: string
  processDefinitionId: string
  taskDefinitionKey: string
  formKey?: string
  priority: number
  tenantId?: string
}

export interface HistoricActivityInstance {
  id: string
  activityId: string
  activityName: string
  activityType: string
  processDefinitionId: string
  processInstanceId: string
  executionId: string
  taskId?: string
  assignee?: string
  startTime: string
  endTime?: string
  durationInMillis?: number
  deleteReason?: string
  tenantId?: string
}
