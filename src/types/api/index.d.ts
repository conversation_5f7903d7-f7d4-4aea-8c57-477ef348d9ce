/**
 * API响应基础类型定义
 *
 * @deprecated 请使用 @/types/common/api 中的类型定义
 * 此文件仅用于向后兼容，将在未来版本中移除
 */

// 从通用类型定义中导入
import type {
  ApiResponse as BaseApiResponse,
  ApiErrorResponse,
  PageData,
  PageParams,
  BaseQuery,
  BatchResponse,
  ID
} from '@/types/common/api'

// 重新导出通用类型
export { UploadResponse, WsMessage } from '@/types/common/api'

// 类型别名，保持向后兼容
export type ApiResponse<T = unknown> = BaseApiResponse<T>

/**
 * 分页响应格式
 * @deprecated 请使用 PageData 类型
 */
export interface PagedResponse<T> extends PageData<T> {
  /** 是否有下一页 */
  hasNext?: boolean
  /** 是否有上一页 */
  hasPrev?: boolean
}

/**
 * 分页请求参数
 * @deprecated 请使用 PageParams 或 BaseQuery 类型
 */
export interface PageQuery extends PageParams {
  /** 排序字段 */
  sortField?: string
  /** 排序方向：asc升序，desc降序 */
  sortOrder?: 'asc' | 'desc'
}

/**
 * 批量操作结果
 * @deprecated 请使用 BatchResponse 类型
 */
export interface BatchResult extends Omit<BatchResponse, 'fail' | 'failList'> {
  /** 失败数量 */
  failed: number
  /** 失败详情 */
  errors?: Array<{
    /** 操作项ID */
    id: ID
    /** 错误原因 */
    reason: string
  }>
}

/**
 * 错误响应
 * @deprecated 请使用 ApiErrorResponse 类型
 */
export interface ErrorResponse extends ApiErrorResponse {
  /** 错误堆栈（开发环境） */
  stack?: string
}

/**
 * 导出任务响应
 */
export interface ExportResponse {
  /** 任务ID */
  taskId: string
  /** 任务状态 */
  status: 'pending' | 'processing' | 'completed' | 'failed'
  /** 进度（0-100） */
  progress?: number
  /** 下载URL（任务完成后） */
  downloadUrl?: string
  /** 错误信息（任务失败时） */
  error?: string
}
