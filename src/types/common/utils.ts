/**
 * 工具类型定义
 * @description TypeScript工具类型和辅助类型定义
 * @module types/common/utils
 */

// ==================== 类型工具 ====================

/**
 * 提取Promise的返回类型
 * @description 从Promise类型中提取实际的返回类型
 */
export type UnwrapPromise<T> = T extends Promise<infer U> ? U : T

/**
 * 提取数组元素类型
 * @description 从数组类型中提取元素类型
 */
export type ArrayElement<T> = T extends readonly (infer U)[] ? U : never

/**
 * 将联合类型转换为交叉类型
 * @description 用于合并多个类型
 */
export type UnionToIntersection<U> = (U extends unknown ? (k: U) => void : never) extends (
  k: infer I
) => void
  ? I
  : never

/**
 * 获取对象的所有键的联合类型
 * @description 包括可选键
 */
export type AllKeys<T> = T extends unknown ? keyof T : never

/**
 * 合并两个对象类型
 * @description 后者的属性会覆盖前者
 */
export type Merge<T, U> = Omit<T, keyof U> & U

/**
 * 将对象的某些键设为必选
 * @description 指定的键将变为必选，其他保持不变
 */
export type RequiredKeys<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>

/**
 * 将对象的某些键设为可选
 * @description 指定的键将变为可选，其他保持不变
 */
export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/**
 * 排除值为null或undefined的键
 * @description 创建一个新类型，排除所有值可能为null或undefined的键
 */
export type NonNullableKeys<T> = {
  [K in keyof T]-?: NonNullable<T[K]>
}

/**
 * 只读部分属性
 * @description 将指定的属性设为只读
 */
export type ReadonlyKeys<T, K extends keyof T> = Omit<T, K> & Readonly<Pick<T, K>>

/**
 * 可写部分属性
 * @description 将指定的只读属性设为可写
 */
export type WritableKeys<T, K extends keyof T> = Omit<T, K> & {
  -readonly [P in K]: T[P]
}

// ==================== 函数类型工具 ====================

/**
 * 获取函数的参数类型
 * @description 提取函数的参数类型为元组
 */
export type FunctionArgs<T extends (...args: any) => any> = T extends (...args: infer P) => any
  ? P
  : never

/**
 * 获取函数的返回类型
 * @description 提取函数的返回类型
 */
export type FunctionReturn<T extends (...args: any) => any> = T extends (...args: any) => infer R
  ? R
  : never

/**
 * 异步函数类型
 * @description 将函数转换为返回Promise的异步函数
 */
export type AsyncFunction<T extends (...args: any) => any> = (
  ...args: FunctionArgs<T>
) => Promise<FunctionReturn<T>>

/**
 * 可能是函数类型
 * @description 值可能是T类型，也可能是返回T类型的函数
 */
export type MaybeFunction<T> = T | (() => T)

/**
 * 构造函数类型
 * @description 表示一个构造函数
 */
export type Constructor<T = unknown> = new (...args: any[]) => T

// ==================== 条件类型工具 ====================

/**
 * If条件类型
 * @description 根据条件选择类型
 */
export type If<C extends boolean, T, F> = C extends true ? T : F

/**
 * 是否相等
 * @description 判断两个类型是否相等
 */
export type Equals<X, Y> =
  (<T>() => T extends X ? 1 : 2) extends <T>() => T extends Y ? 1 : 2 ? true : false

/**
 * 是否为any类型
 * @description 判断类型是否为any
 */
export type IsAny<T> = 0 extends 1 & T ? true : false

/**
 * 是否为never类型
 * @description 判断类型是否为never
 */
export type IsNever<T> = [T] extends [never] ? true : false

/**
 * 是否为联合类型
 * @description 判断类型是否为联合类型
 */
export type IsUnion<T> = [T] extends [UnionToIntersection<T>] ? false : true

// ==================== 字符串类型工具 ====================

/**
 * 字符串字面量转大写
 * @description 将字符串字面量类型转换为大写
 */
export type Uppercase<S extends string> = intrinsic

/**
 * 字符串字面量转小写
 * @description 将字符串字面量类型转换为小写
 */
export type Lowercase<S extends string> = intrinsic

/**
 * 首字母大写
 * @description 将字符串字面量类型的首字母转换为大写
 */
export type Capitalize<S extends string> = intrinsic

/**
 * 首字母小写
 * @description 将字符串字面量类型的首字母转换为小写
 */
export type Uncapitalize<S extends string> = intrinsic

/**
 * 字符串模板类型
 * @description 用于构建字符串字面量类型
 */
export type StringTemplate<T extends string, U extends string> = `${T}${U}`

// ==================== 对象类型工具 ====================

/**
 * 获取对象中指定类型的键
 * @description 获取对象中值为指定类型的所有键
 */
export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never
}[keyof T]

/**
 * 排除对象中指定类型的键
 * @description 排除对象中值为指定类型的所有键
 */
export type ExcludeKeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? never : K
}[keyof T]

/**
 * 提取对象中指定类型的属性
 * @description 创建只包含指定类型值的新对象类型
 */
export type PickByType<T, U> = Pick<T, KeysOfType<T, U>>

/**
 * 排除对象中指定类型的属性
 * @description 创建排除指定类型值的新对象类型
 */
export type OmitByType<T, U> = Omit<T, KeysOfType<T, U>>

/**
 * 将对象所有属性转为联合类型
 * @description 获取对象所有属性值的联合类型
 */
export type ValueOf<T> = T[keyof T]

/**
 * 对象路径类型
 * @description 生成对象的所有可能路径
 */
export type Path<T> = T extends object
  ? {
      [K in keyof T]: K extends string
        ? T[K] extends object
          ? K | `${K}.${Path<T[K]>}`
          : K
        : never
    }[keyof T]
  : never

/**
 * 根据路径获取值类型
 * @description 根据路径字符串获取嵌套对象的值类型
 */
export type PathValue<T, P extends string> = P extends `${infer K}.${infer Rest}`
  ? K extends keyof T
    ? Rest extends Path<T[K]>
      ? PathValue<T[K], Rest>
      : never
    : never
  : P extends keyof T
    ? T[P]
    : never

// ==================== 类型守卫工具 ====================

/**
 * 类型守卫函数类型
 * @description 表示一个类型守卫函数
 */
export type TypeGuard<T> = (value: unknown) => value is T

/**
 * 类型断言函数类型
 * @description 表示一个类型断言函数
 */
export type TypeAssertion<T> = (value: unknown) => asserts value is T

/**
 * 可能是数组
 * @description 值可能是T或T数组
 */
export type MaybeArray<T> = T | T[]

/**
 * 确保是数组
 * @description 如果不是数组则包装成数组
 */
export type EnsureArray<T> = T extends any[] ? T : [T]

// ==================== 实用工具类型 ====================

/**
 * 部分深度可选
 * @description 将对象及其嵌套属性部分设为可选
 */
export type PartialDeep<T> = T extends object
  ? {
      [P in keyof T]?: PartialDeep<T[P]>
    }
  : T

/**
 * 部分深度必选
 * @description 将对象及其嵌套属性部分设为必选
 */
export type RequiredDeep<T> = T extends object
  ? {
      [P in keyof T]-?: RequiredDeep<T[P]>
    }
  : T

/**
 * 标记类型
 * @description 用于创建名义类型
 */
export type Brand<T, B> = T & { __brand: B }

/**
 * 不透明类型
 * @description 创建不透明类型，防止类型混用
 */
export type Opaque<T, K> = T & { __opaque: K }

/**
 * 元组转联合类型
 * @description 将元组类型转换为联合类型
 */
export type TupleToUnion<T extends readonly any[]> = T[number]

/**
 * 联合类型转元组
 * @description 将联合类型转换为元组（顺序不确定）
 */
export type UnionToTuple<T> =
  UnionToIntersection<T extends any ? () => T : never> extends () => infer R
    ? [...UnionToTuple<Exclude<T, R>>, R]
    : []
