/**
 * 通用类型定义
 */

/**
 * 时间范围
 */
export type DateRange = [string, string] | null

/**
 * 数字范围
 */
export type NumberRange = [number, number] | null

/**
 * 选项类型
 */
export interface Option<T = any> {
  /** 显示文本 */
  label: string
  /** 选项值 */
  value: T
  /** 是否禁用 */
  disabled?: boolean
  /** 子选项 */
  children?: Option<T>[]
  /** 额外数据 */
  extra?: any
}

/**
 * 树节点类型
 */
export interface TreeNode<T = any> {
  /** 节点ID */
  id: string
  /** 节点标签 */
  label: string
  /** 子节点 */
  children?: TreeNode<T>[]
  /** 是否叶子节点 */
  isLeaf?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 节点数据 */
  data?: T
}

/**
 * 表格列定义
 */
export interface TableColumn<T = any> {
  /** 列标识 */
  prop: keyof T | string
  /** 列标题 */
  label: string
  /** 列宽度 */
  width?: string | number
  /** 最小宽度 */
  minWidth?: string | number
  /** 是否固定 */
  fixed?: boolean | 'left' | 'right'
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 是否可排序 */
  sortable?: boolean | 'custom'
  /** 格式化函数 */
  formatter?: (row: T, column: TableColumn<T>, cellValue: any, index: number) => any
  /** 是否显示 */
  visible?: boolean
  /** 插槽名称 */
  slot?: string
}

/**
 * 表单规则
 */
export interface FormRule {
  /** 是否必填 */
  required?: boolean
  /** 错误消息 */
  message?: string
  /** 触发方式 */
  trigger?: 'blur' | 'change' | ['blur', 'change']
  /** 类型 */
  type?: 'string' | 'number' | 'boolean' | 'array' | 'date' | 'email' | 'url'
  /** 最小长度 */
  min?: number
  /** 最大长度 */
  max?: number
  /** 正则表达式 */
  pattern?: RegExp
  /** 自定义验证器 */
  validator?: (rule: any, value: any, callback: (error?: Error) => void) => void
}

/**
 * 表单规则集合
 */
export type FormRules<T = any> = Partial<Record<keyof T, FormRule | FormRule[]>>

/**
 * 操作类型
 */
export enum ActionType {
  /** 创建 */
  CREATE = 'create',
  /** 更新 */
  UPDATE = 'update',
  /** 删除 */
  DELETE = 'delete',
  /** 查看 */
  VIEW = 'view',
  /** 导入 */
  IMPORT = 'import',
  /** 导出 */
  EXPORT = 'export',
  /** 审批 */
  APPROVE = 'approve',
  /** 驳回 */
  REJECT = 'reject'
}

/**
 * 审批状态
 */
export enum ApprovalStatus {
  /** 待审批 */
  PENDING = 'pending',
  /** 审批中 */
  PROCESSING = 'processing',
  /** 已通过 */
  APPROVED = 'approved',
  /** 已驳回 */
  REJECTED = 'rejected',
  /** 已撤销 */
  CANCELLED = 'cancelled'
}

/**
 * 文件类型
 */
export interface FileInfo {
  /** 文件ID */
  id: string
  /** 文件名 */
  name: string
  /** 文件大小（字节） */
  size: number
  /** 文件类型 */
  type: string
  /** 文件URL */
  url: string
  /** 上传时间 */
  uploadTime: string
  /** 上传人 */
  uploadBy?: string
}

/**
 * 地址信息
 */
export interface Address {
  /** 省 */
  province: string
  /** 市 */
  city: string
  /** 区/县 */
  district: string
  /** 详细地址 */
  detail: string
  /** 完整地址 */
  fullAddress?: string
}

/**
 * 键值对
 */
export interface KeyValue<T = any> {
  /** 键 */
  key: string
  /** 值 */
  value: T
}

/**
 * 可空类型
 */
export type Nullable<T> = T | null

/**
 * 可选类型
 */
export type Optional<T> = T | undefined

/**
 * 递归部分可选
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * 提取Promise类型
 */
export type Awaited<T> = T extends Promise<infer U> ? U : T

/**
 * 函数类型
 */
export type Fn<T = any, R = void> = (...args: T[]) => R

/**
 * 异步函数类型
 */
export type AsyncFn<T = any, R = void> = (...args: T[]) => Promise<R>

/**
 * 时间戳
 */
export type Timestamp = number

/**
 * ID类型
 */
export type ID = string | number
