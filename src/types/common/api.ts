/**
 * API相关通用类型定义
 * @description API请求、响应、分页等相关的类型定义
 * @module types/common/api
 */

import type { ID } from './base'

// ==================== API响应类型 ====================

/**
 * 标准API响应格式
 * @description 统一的API响应格式，所有后端接口都应该返回这种格式
 * @template T 响应数据类型
 */
export interface ApiResponse<T = unknown> {
  /** 响应码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 响应时间戳 */
  timestamp?: number
  /** 请求ID，用于追踪 */
  requestId?: string
}

/**
 * API错误响应格式
 * @description 当API发生错误时的响应格式
 */
export interface ApiErrorResponse {
  /** 错误码 */
  code: number
  /** 错误消息 */
  message: string
  /** 错误详情 */
  details?: unknown
  /** 错误时间戳 */
  timestamp: number
  /** 请求路径 */
  path: string
  /** 请求ID */
  requestId?: string
}

/**
 * API响应状态码枚举
 * @description 常用的HTTP状态码
 */
export enum ApiStatusCode {
  /** 成功 */
  SUCCESS = 200,
  /** 已创建 */
  CREATED = 201,
  /** 无内容 */
  NO_CONTENT = 204,
  /** 错误请求 */
  BAD_REQUEST = 400,
  /** 未授权 */
  UNAUTHORIZED = 401,
  /** 禁止访问 */
  FORBIDDEN = 403,
  /** 未找到 */
  NOT_FOUND = 404,
  /** 方法不允许 */
  METHOD_NOT_ALLOWED = 405,
  /** 请求超时 */
  REQUEST_TIMEOUT = 408,
  /** 冲突 */
  CONFLICT = 409,
  /** 服务器错误 */
  INTERNAL_SERVER_ERROR = 500,
  /** 网关错误 */
  BAD_GATEWAY = 502,
  /** 服务不可用 */
  SERVICE_UNAVAILABLE = 503
}

// ==================== 分页相关类型 ====================

/**
 * 分页请求参数
 * @description 用于分页查询的请求参数
 */
export interface PageParams {
  /** 页码，从1开始 */
  page: number
  /** 每页大小 */
  size: number
  /** 排序字段 */
  sort?: string
  /** 排序方向 */
  order?: 'asc' | 'desc'
}

/**
 * 分页数据格式1：简单格式
 * @description 最常用的分页数据格式
 * @template T 数据项类型
 */
export interface PageData<T = unknown> {
  /** 数据列表 */
  list: T[]
  /** 总记录数 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页大小 */
  size: number
  /** 总页数 */
  pages: number
}

/**
 * 分页数据格式2：Spring Page格式
 * @description 与Spring框架的Page对象对应
 * @template T 数据项类型
 */
export interface SpringPageData<T = unknown> {
  /** 数据内容 */
  content: T[]
  /** 总元素数量 */
  totalElements: number
  /** 总页数 */
  totalPages: number
  /** 页大小 */
  size: number
  /** 当前页码(从0开始) */
  number: number
  /** 是否为第一页 */
  first: boolean
  /** 是否为最后一页 */
  last: boolean
  /** 是否为空 */
  empty: boolean
  /** 当前页的元素数量 */
  numberOfElements?: number
}

/**
 * 分页响应类型（简单格式）
 * @description API响应中包含分页数据的格式
 * @template T 数据项类型
 */
export type PageResponse<T = unknown> = ApiResponse<PageData<T>>

/**
 * 分页响应类型（Spring格式）
 * @description API响应中包含Spring分页数据的格式
 * @template T 数据项类型
 */
export type SpringPageResponse<T = unknown> = ApiResponse<SpringPageData<T>>

// ==================== 查询相关类型 ====================

/**
 * 基础查询参数
 * @description 所有查询参数的基类
 */
export interface BaseQuery extends Partial<PageParams> {
  /** 关键字搜索 */
  keyword?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
}

/**
 * 排序参数
 * @description 排序相关的参数
 */
export interface SortParam {
  /** 排序字段 */
  field: string
  /** 排序方向 */
  order: 'asc' | 'desc'
}

/**
 * 批量查询参数
 * @description 批量查询使用的参数
 */
export interface BatchQuery {
  /** ID列表 */
  ids: ID[]
}

// ==================== 请求相关类型 ====================

/**
 * 请求方法枚举
 * @description HTTP请求方法
 */
export enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
  HEAD = 'HEAD',
  OPTIONS = 'OPTIONS'
}

/**
 * 请求配置
 * @description 扩展的请求配置选项
 */
export interface RequestConfig {
  /** 是否显示加载中提示 */
  showLoading?: boolean
  /** 加载提示文字 */
  loadingText?: string
  /** 是否显示错误提示 */
  showError?: boolean
  /** 是否显示成功提示 */
  showSuccess?: boolean
  /** 成功提示文字 */
  successText?: string
  /** 是否使用缓存 */
  useCache?: boolean
  /** 缓存时间(毫秒) */
  cacheTime?: number
  /** 重试次数 */
  retry?: number
  /** 重试延迟(毫秒) */
  retryDelay?: number
  /** 超时时间(毫秒) */
  timeout?: number
  /** 自定义错误处理 */
  errorHandler?: (error: unknown) => void
  /** 自定义成功处理 */
  successHandler?: (data: unknown) => void
}

// ==================== 批量操作相关类型 ====================

/**
 * 批量操作请求
 * @description 批量操作的请求参数
 */
export interface BatchRequest {
  /** 操作的ID列表 */
  ids: ID[]
  /** 批量操作的参数 */
  params?: Record<string, unknown>
}

/**
 * 批量操作响应
 * @description 批量操作的响应结果
 */
export interface BatchResponse {
  /** 成功数量 */
  success: number
  /** 失败数量 */
  fail: number
  /** 总数量 */
  total: number
  /** 失败详情 */
  failList?: Array<{
    /** 失败的ID */
    id: ID
    /** 失败原因 */
    reason: string
  }>
}

// ==================== 文件操作相关类型 ====================

/**
 * 文件上传响应
 * @description 文件上传成功后的响应
 */
export interface UploadResponse {
  /** 文件URL */
  url: string
  /** 文件名 */
  name: string
  /** 文件大小 */
  size: number
  /** 文件类型 */
  type: string
  /** 上传时间 */
  uploadTime: string
  /** 文件ID */
  fileId?: ID
}

/**
 * 导出请求参数
 * @description 数据导出的请求参数
 */
export interface ExportParams {
  /** 导出格式 */
  format: 'excel' | 'csv' | 'pdf' | 'json'
  /** 导出的字段列表 */
  fields?: string[]
  /** 导出的过滤条件 */
  filters?: Record<string, unknown>
  /** 文件名 */
  filename?: string
  /** 是否包含表头 */
  includeHeader?: boolean
}

/**
 * 导入响应
 * @description 数据导入的响应结果
 */
export interface ImportResponse {
  /** 总行数 */
  total: number
  /** 成功行数 */
  success: number
  /** 失败行数 */
  fail: number
  /** 跳过行数 */
  skip?: number
  /** 错误详情 */
  errors?: Array<{
    /** 行号 */
    row: number
    /** 列名 */
    column: string
    /** 原始值 */
    value: unknown
    /** 错误原因 */
    reason: string
  }>
}

// ==================== WebSocket相关类型 ====================

/**
 * WebSocket消息类型
 * @description WebSocket通信的消息格式
 */
export interface WsMessage<T = unknown> {
  /** 消息类型 */
  type: string
  /** 消息数据 */
  data: T
  /** 消息ID */
  id?: string
  /** 时间戳 */
  timestamp: number
}

/**
 * WebSocket连接状态
 * @description WebSocket连接的状态枚举
 */
export enum WsState {
  /** 连接中 */
  CONNECTING = 0,
  /** 已连接 */
  OPEN = 1,
  /** 关闭中 */
  CLOSING = 2,
  /** 已关闭 */
  CLOSED = 3
}
