/**
 * 业务通用类型定义
 * @description 业务领域相关的通用类型定义
 * @module types/common/business
 */

import type { ID, BaseEntity, CommonStatus } from './base'

// ==================== 用户相关类型 ====================

/**
 * 用户基础信息
 * @description 用户的基本信息接口
 */
export interface UserInfo extends BaseEntity {
  /** 用户名 */
  username: string
  /** 真实姓名 */
  realName: string
  /** 昵称 */
  nickname?: string
  /** 头像 */
  avatar?: string
  /** 邮箱 */
  email?: string
  /** 手机号 */
  phone?: string
  /** 状态 */
  status: CommonStatus
  /** 部门ID */
  deptId?: ID
  /** 部门名称 */
  deptName?: string
}

/**
 * 用户性别枚举
 * @description 用户性别
 */
export enum Gender {
  /** 男 */
  MALE = '1',
  /** 女 */
  FEMALE = '2',
  /** 未知 */
  UNKNOWN = '0'
}

// ==================== 组织架构相关类型 ====================

/**
 * 部门信息
 * @description 部门/组织的基本信息
 */
export interface DepartmentInfo extends BaseEntity {
  /** 部门名称 */
  name: string
  /** 部门编码 */
  code: string
  /** 父部门ID */
  parentId?: ID
  /** 负责人ID */
  leaderId?: ID
  /** 负责人姓名 */
  leaderName?: string
  /** 排序 */
  sort: number
  /** 状态 */
  status: CommonStatus
  /** 备注 */
  remark?: string
}

/**
 * 职位信息
 * @description 职位/岗位的基本信息
 */
export interface PositionInfo extends BaseEntity {
  /** 职位名称 */
  name: string
  /** 职位编码 */
  code: string
  /** 职位等级 */
  level?: number
  /** 所属部门ID */
  deptId?: ID
  /** 状态 */
  status: CommonStatus
  /** 备注 */
  remark?: string
}

// ==================== 审批流程相关类型 ====================

/**
 * 审批状态枚举
 * @description 通用的审批状态
 */
export enum ApprovalStatus {
  /** 草稿 */
  DRAFT = 'DRAFT',
  /** 待审批 */
  PENDING = 'PENDING',
  /** 审批中 */
  APPROVING = 'APPROVING',
  /** 已通过 */
  APPROVED = 'APPROVED',
  /** 已拒绝 */
  REJECTED = 'REJECTED',
  /** 已撤回 */
  WITHDRAWN = 'WITHDRAWN',
  /** 已取消 */
  CANCELLED = 'CANCELLED'
}

/**
 * 审批操作枚举
 * @description 审批操作类型
 */
export enum ApprovalAction {
  /** 提交 */
  SUBMIT = 'SUBMIT',
  /** 同意 */
  APPROVE = 'APPROVE',
  /** 拒绝 */
  REJECT = 'REJECT',
  /** 转办 */
  DELEGATE = 'DELEGATE',
  /** 加签 */
  ADD_SIGN = 'ADD_SIGN',
  /** 减签 */
  REDUCE_SIGN = 'REDUCE_SIGN',
  /** 撤回 */
  WITHDRAW = 'WITHDRAW',
  /** 取消 */
  CANCEL = 'CANCEL',
  /** 暂存 */
  SAVE = 'SAVE'
}

/**
 * 审批记录
 * @description 审批流程的操作记录
 */
export interface ApprovalRecord {
  /** 记录ID */
  id: ID
  /** 流程实例ID */
  processInstanceId: string
  /** 任务ID */
  taskId?: string
  /** 审批人ID */
  userId: ID
  /** 审批人姓名 */
  userName: string
  /** 审批操作 */
  action: ApprovalAction
  /** 审批意见 */
  opinion?: string
  /** 审批时间 */
  approveTime: string
  /** 耗时(秒) */
  duration?: number
}

// ==================== 业务通用枚举 ====================

/**
 * 紧急程度枚举
 * @description 表示事项的紧急程度
 */
export enum UrgencyLevel {
  /** 低 */
  LOW = 'LOW',
  /** 中 */
  MEDIUM = 'MEDIUM',
  /** 高 */
  HIGH = 'HIGH',
  /** 紧急 */
  URGENT = 'URGENT'
}

/**
 * 优先级枚举
 * @description 表示事项的优先级
 */
export enum Priority {
  /** 最低 */
  LOWEST = 1,
  /** 低 */
  LOW = 2,
  /** 中 */
  MEDIUM = 3,
  /** 高 */
  HIGH = 4,
  /** 最高 */
  HIGHEST = 5
}

/**
 * 业务类型枚举
 * @description 通用的业务类型
 */
export enum BusinessType {
  /** 员工管理 */
  EMPLOYEE = 'EMPLOYEE',
  /** 组织管理 */
  ORGANIZATION = 'ORGANIZATION',
  /** 考勤管理 */
  ATTENDANCE = 'ATTENDANCE',
  /** 薪资管理 */
  SALARY = 'SALARY',
  /** 招聘管理 */
  RECRUITMENT = 'RECRUITMENT',
  /** 绩效管理 */
  PERFORMANCE = 'PERFORMANCE',
  /** 培训管理 */
  TRAINING = 'TRAINING',
  /** 合同管理 */
  CONTRACT = 'CONTRACT',
  /** 请假管理 */
  LEAVE = 'LEAVE',
  /** 报销管理 */
  EXPENSE = 'EXPENSE',
  /** 其他 */
  OTHER = 'OTHER'
}

// ==================== 通知消息相关类型 ====================

/**
 * 消息类型枚举
 * @description 系统消息的类型
 */
export enum MessageType {
  /** 系统消息 */
  SYSTEM = 'SYSTEM',
  /** 通知 */
  NOTICE = 'NOTICE',
  /** 提醒 */
  REMINDER = 'REMINDER',
  /** 警告 */
  WARNING = 'WARNING',
  /** 错误 */
  ERROR = 'ERROR',
  /** 成功 */
  SUCCESS = 'SUCCESS'
}

/**
 * 消息状态枚举
 * @description 消息的阅读状态
 */
export enum MessageStatus {
  /** 未读 */
  UNREAD = 'UNREAD',
  /** 已读 */
  READ = 'READ',
  /** 已删除 */
  DELETED = 'DELETED'
}

/**
 * 通知消息
 * @description 系统通知消息
 */
export interface NotificationMessage extends BaseEntity {
  /** 标题 */
  title: string
  /** 内容 */
  content: string
  /** 类型 */
  type: MessageType
  /** 状态 */
  status: MessageStatus
  /** 发送人ID */
  senderId?: ID
  /** 发送人姓名 */
  senderName?: string
  /** 接收人ID */
  receiverId: ID
  /** 业务类型 */
  businessType?: BusinessType
  /** 业务ID */
  businessId?: ID
  /** 跳转链接 */
  link?: string
  /** 阅读时间 */
  readTime?: string
}

// ==================== 附件相关类型 ====================

/**
 * 附件信息
 * @description 业务附件信息
 */
export interface Attachment extends BaseEntity {
  /** 文件名 */
  fileName: string
  /** 文件大小(字节) */
  fileSize: number
  /** 文件类型 */
  fileType: string
  /** 文件路径 */
  filePath: string
  /** 文件URL */
  fileUrl: string
  /** 业务类型 */
  businessType: BusinessType
  /** 业务ID */
  businessId: ID
  /** 上传人ID */
  uploaderId: ID
  /** 上传人姓名 */
  uploaderName: string
}

// ==================== 地址相关类型 ====================

/**
 * 地址信息
 * @description 通用的地址信息
 */
export interface AddressInfo {
  /** 省份 */
  province: string
  /** 省份编码 */
  provinceCode?: string
  /** 城市 */
  city: string
  /** 城市编码 */
  cityCode?: string
  /** 区县 */
  district: string
  /** 区县编码 */
  districtCode?: string
  /** 详细地址 */
  detail: string
  /** 邮政编码 */
  postalCode?: string
  /** 经度 */
  longitude?: number
  /** 纬度 */
  latitude?: number
}

// ==================== 审计日志相关类型 ====================

/**
 * 操作类型枚举
 * @description 审计日志的操作类型
 */
export enum OperationType {
  /** 创建 */
  CREATE = 'CREATE',
  /** 更新 */
  UPDATE = 'UPDATE',
  /** 删除 */
  DELETE = 'DELETE',
  /** 查询 */
  QUERY = 'QUERY',
  /** 导入 */
  IMPORT = 'IMPORT',
  /** 导出 */
  EXPORT = 'EXPORT',
  /** 登录 */
  LOGIN = 'LOGIN',
  /** 登出 */
  LOGOUT = 'LOGOUT',
  /** 其他 */
  OTHER = 'OTHER'
}

/**
 * 审计日志
 * @description 系统操作审计日志
 */
export interface AuditLog extends BaseEntity {
  /** 操作类型 */
  operationType: OperationType
  /** 业务类型 */
  businessType: BusinessType
  /** 业务ID */
  businessId?: ID
  /** 操作描述 */
  description: string
  /** 操作人ID */
  operatorId: ID
  /** 操作人姓名 */
  operatorName: string
  /** 操作IP */
  operatorIp?: string
  /** 操作前数据 */
  beforeData?: string
  /** 操作后数据 */
  afterData?: string
  /** 操作结果 */
  result: 'SUCCESS' | 'FAIL'
  /** 错误信息 */
  errorMsg?: string
}
