/**
 * 基础通用类型定义
 * @description 整个项目的基础类型定义，包括API响应、分页、ID等
 * @module types/common/base
 */

// ==================== 基础类型 ====================

/**
 * 通用ID类型
 * @description 支持字符串和数字类型的ID
 */
export type ID = string | number

/**
 * 可空类型
 * @description 表示一个值可能为null或undefined
 */
export type Nullable<T> = T | null | undefined

/**
 * 深度只读类型
 * @description 递归地将对象的所有属性设置为只读
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * 深度可选类型
 * @description 递归地将对象的所有属性设置为可选
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * 时间戳类型
 * @description 统一使用毫秒时间戳
 */
export type Timestamp = number

/**
 * 日期时间字符串类型
 * @description ISO 8601格式的日期时间字符串
 */
export type DateTimeString = string

// ==================== 状态枚举 ====================

/**
 * 通用状态枚举
 * @description 表示启用/禁用状态
 */
export enum CommonStatus {
  /** 启用 */
  ENABLE = 1,
  /** 禁用 */
  DISABLE = 0
}

/**
 * 删除标记枚举
 * @description 软删除标记
 */
export enum DeleteFlag {
  /** 正常 */
  NORMAL = 0,
  /** 已删除 */
  DELETED = 1
}

/**
 * 布尔数字枚举
 * @description 用数字表示布尔值
 */
export enum BooleanNumber {
  /** 假/否 */
  FALSE = 0,
  /** 真/是 */
  TRUE = 1
}

// ==================== 基础实体接口 ====================

/**
 * 基础实体接口
 * @description 所有实体类的基础接口
 */
export interface BaseEntity {
  /** 主键ID */
  id?: ID
  /** 创建时间 */
  createTime?: DateTimeString
  /** 更新时间 */
  updateTime?: DateTimeString
  /** 创建人ID */
  createBy?: ID
  /** 更新人ID */
  updateBy?: ID
}

/**
 * 可删除实体接口
 * @description 支持软删除的实体接口
 */
export interface DeletableEntity extends BaseEntity {
  /** 删除标记 */
  deleteFlag?: DeleteFlag
  /** 删除时间 */
  deleteTime?: DateTimeString
  /** 删除人ID */
  deleteBy?: ID
}

/**
 * 树形结构实体接口
 * @description 支持树形结构的实体接口
 */
export interface TreeEntity<T = unknown> extends BaseEntity {
  /** 父节点ID */
  parentId?: ID
  /** 子节点列表 */
  children?: T[]
  /** 层级 */
  level?: number
  /** 排序 */
  sort?: number
}

// ==================== 键值对类型 ====================

/**
 * 通用键值对接口
 * @description 用于下拉框、单选框等选项
 */
export interface KeyValue<V = unknown> {
  /** 键 */
  key: string
  /** 值 */
  value: V
}

/**
 * 标签值对接口
 * @description 用于下拉框、单选框等选项的显示
 */
export interface LabelValue<V = unknown> {
  /** 显示标签 */
  label: string
  /** 值 */
  value: V
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 选项接口
 * @description 扩展的选项接口，支持分组、图标等
 */
export interface Option<V = unknown> extends LabelValue<V> {
  /** 图标 */
  icon?: string
  /** 颜色 */
  color?: string
  /** 分组 */
  group?: string
  /** 子选项 */
  children?: Option<V>[]
}

// ==================== 操作结果类型 ====================

/**
 * 操作结果接口
 * @description 表示一个操作的执行结果
 */
export interface OperationResult<T = unknown> {
  /** 是否成功 */
  success: boolean
  /** 结果消息 */
  message?: string
  /** 结果数据 */
  data?: T
  /** 错误详情 */
  error?: unknown
}

/**
 * 批量操作结果接口
 * @description 批量操作的执行结果
 */
export interface BatchOperationResult<T = unknown> {
  /** 总数 */
  total: number
  /** 成功数 */
  success: number
  /** 失败数 */
  fail: number
  /** 成功列表 */
  successList?: T[]
  /** 失败列表 */
  failList?: Array<{
    /** 数据项 */
    item: T
    /** 失败原因 */
    reason: string
  }>
}

// ==================== 文件相关类型 ====================

/**
 * 文件信息接口
 * @description 文件上传、下载等场景使用
 */
export interface FileInfo {
  /** 文件ID */
  id?: ID
  /** 文件名 */
  name: string
  /** 文件大小(字节) */
  size: number
  /** 文件类型(MIME) */
  type: string
  /** 文件URL */
  url: string
  /** 上传时间 */
  uploadTime?: DateTimeString
  /** 上传人 */
  uploadBy?: ID
}

/**
 * 文件上传参数
 * @description 文件上传时的参数
 */
export interface FileUploadParams {
  /** 文件 */
  file: File
  /** 业务类型 */
  businessType?: string
  /** 业务ID */
  businessId?: ID
  /** 是否公开 */
  isPublic?: boolean
}

// ==================== 其他通用类型 ====================

/**
 * 坐标接口
 * @description 表示二维坐标
 */
export interface Coordinate {
  /** X坐标 */
  x: number
  /** Y坐标 */
  y: number
}

/**
 * 尺寸接口
 * @description 表示宽高尺寸
 */
export interface Size {
  /** 宽度 */
  width: number
  /** 高度 */
  height: number
}

/**
 * 范围接口
 * @description 表示一个数值范围
 */
export interface Range<T = number> {
  /** 最小值 */
  min: T
  /** 最大值 */
  max: T
}

/**
 * 时间范围接口
 * @description 表示一个时间范围
 */
export interface DateRange {
  /** 开始时间 */
  startDate: DateTimeString
  /** 结束时间 */
  endDate: DateTimeString
}
