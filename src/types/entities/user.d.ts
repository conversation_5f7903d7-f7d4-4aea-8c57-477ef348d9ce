/**
 * 用户实体类型定义
 */

import type { Department } from './organization'

/**
 * 用户状态枚举
 */
export enum UserStatus {
  /** 正常 */
  ACTIVE = 'active',
  /** 停用 */
  INACTIVE = 'inactive',
  /** 锁定 */
  LOCKED = 'locked',
  /** 待激活 */
  PENDING = 'pending'
}

/**
 * 用户性别枚举
 */
export enum Gender {
  /** 男 */
  MALE = 'male',
  /** 女 */
  FEMALE = 'female',
  /** 未知 */
  UNKNOWN = 'unknown'
}

/**
 * 角色信息
 */
export interface Role {
  /** 角色ID */
  id: string
  /** 角色编码 */
  code: string
  /** 角色名称 */
  name: string
  /** 角色描述 */
  description?: string
  /** 权限列表 */
  permissions: string[]
  /** 是否系统角色 */
  isSystem?: boolean
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 用户基础信息
 */
export interface UserBase {
  /** 用户ID */
  id: string
  /** 用户名 */
  username: string
  /** 真实姓名 */
  realName: string
  /** 工号 */
  employeeId?: string
  /** 邮箱 */
  email?: string
  /** 手机号 */
  phone?: string
  /** 性别 */
  gender?: Gender
  /** 头像URL */
  avatar?: string
  /** 状态 */
  status: UserStatus
}

/**
 * 用户详细信息
 */
export interface User extends UserBase {
  /** 所属部门 */
  department?: Department
  /** 部门ID */
  departmentId?: string
  /** 职位 */
  position?: string
  /** 职位ID */
  positionId?: string
  /** 角色列表 */
  roles: Role[]
  /** 权限列表（合并所有角色的权限） */
  permissions: string[]
  /** 入职日期 */
  joinDate?: string
  /** 最后登录时间 */
  lastLoginTime?: string
  /** 最后登录IP */
  lastLoginIp?: string
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 创建人 */
  createdBy?: string
  /** 更新人 */
  updatedBy?: string
}

/**
 * 用户登录信息
 */
export interface LoginInfo {
  /** 访问令牌 */
  accessToken: string
  /** 刷新令牌 */
  refreshToken: string
  /** 令牌类型 */
  tokenType: string
  /** 过期时间（秒） */
  expiresIn: number
  /** 用户信息 */
  userInfo: User
}

/**
 * 登录请求参数
 */
export interface LoginParams {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 验证码 */
  captcha?: string
  /** 验证码ID */
  captchaId?: string
  /** 记住我 */
  rememberMe?: boolean
}

/**
 * 修改密码参数
 */
export interface ChangePasswordParams {
  /** 旧密码 */
  oldPassword: string
  /** 新密码 */
  newPassword: string
  /** 确认密码 */
  confirmPassword: string
}

/**
 * 用户查询参数
 */
export interface UserQuery {
  /** 关键词（用户名、姓名、工号） */
  keyword?: string
  /** 部门ID */
  departmentId?: string
  /** 角色ID */
  roleId?: string
  /** 状态 */
  status?: UserStatus
  /** 入职日期范围 */
  joinDateRange?: [string, string]
}

/**
 * 用户创建参数
 */
export interface UserCreateParams {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 真实姓名 */
  realName: string
  /** 工号 */
  employeeId?: string
  /** 邮箱 */
  email?: string
  /** 手机号 */
  phone?: string
  /** 性别 */
  gender?: Gender
  /** 部门ID */
  departmentId?: string
  /** 职位ID */
  positionId?: string
  /** 角色ID列表 */
  roleIds: string[]
  /** 入职日期 */
  joinDate?: string
}

/**
 * 用户更新参数
 */
export interface UserUpdateParams extends Partial<Omit<UserCreateParams, 'username' | 'password'>> {
  /** 用户ID */
  id: string
  /** 状态 */
  status?: UserStatus
}
