/**
 * 员工实体类型定义
 */

import type { User, Gender } from './user'
import type { Department, Position } from './organization'

/**
 * 员工状态枚举
 */
export enum EmployeeStatus {
  /** 在职 */
  ACTIVE = 'active',
  /** 试用期 */
  PROBATION = 'probation',
  /** 离职 */
  RESIGNED = 'resigned',
  /** 退休 */
  RETIRED = 'retired',
  /** 停职 */
  SUSPENDED = 'suspended'
}

/**
 * 员工类型枚举
 */
export enum EmployeeType {
  /** 正式员工 */
  REGULAR = 'regular',
  /** 合同工 */
  CONTRACT = 'contract',
  /** 实习生 */
  INTERN = 'intern',
  /** 临时工 */
  TEMPORARY = 'temporary',
  /** 外包 */
  OUTSOURCED = 'outsourced'
}

/**
 * 学历枚举
 */
export enum Education {
  /** 博士 */
  DOCTOR = 'doctor',
  /** 硕士 */
  MASTER = 'master',
  /** 本科 */
  BACHELOR = 'bachelor',
  /** 专科 */
  COLLEGE = 'college',
  /** 高中 */
  HIGH_SCHOOL = 'high_school',
  /** 初中及以下 */
  OTHER = 'other'
}

/**
 * 婚姻状况枚举
 */
export enum MaritalStatus {
  /** 未婚 */
  SINGLE = 'single',
  /** 已婚 */
  MARRIED = 'married',
  /** 离异 */
  DIVORCED = 'divorced',
  /** 丧偶 */
  WIDOWED = 'widowed'
}

/**
 * 员工基础信息
 */
export interface EmployeeBase {
  /** 员工ID */
  id: string
  /** 工号 */
  employeeNo: string
  /** 姓名 */
  name: string
  /** 性别 */
  gender: Gender
  /** 身份证号 */
  idCard: string
  /** 手机号 */
  phone: string
  /** 邮箱 */
  email?: string
  /** 状态 */
  status: EmployeeStatus
  /** 员工类型 */
  type: EmployeeType
}

/**
 * 员工详细信息
 */
export interface Employee extends EmployeeBase {
  /** 关联用户 */
  user?: User
  /** 用户ID */
  userId?: string
  /** 部门 */
  department?: Department
  /** 部门ID */
  departmentId: string
  /** 职位 */
  position?: Position
  /** 职位ID */
  positionId: string
  /** 直属上级ID */
  managerId?: string
  /** 直属上级 */
  manager?: Employee
  /** 出生日期 */
  birthDate: string
  /** 年龄 */
  age?: number
  /** 民族 */
  ethnicity?: string
  /** 籍贯 */
  hometown?: string
  /** 政治面貌 */
  politicalStatus?: string
  /** 婚姻状况 */
  maritalStatus?: MaritalStatus
  /** 学历 */
  education?: Education
  /** 毕业院校 */
  graduateSchool?: string
  /** 专业 */
  major?: string
  /** 毕业时间 */
  graduateDate?: string
  /** 入职日期 */
  joinDate: string
  /** 转正日期 */
  regularDate?: string
  /** 离职日期 */
  resignDate?: string
  /** 工龄（年） */
  workYears?: number
  /** 司龄（年） */
  companyYears?: number
  /** 合同开始日期 */
  contractStartDate?: string
  /** 合同结束日期 */
  contractEndDate?: string
  /** 社保账号 */
  socialSecurityNo?: string
  /** 公积金账号 */
  housingFundNo?: string
  /** 银行卡号 */
  bankCardNo?: string
  /** 开户银行 */
  bankName?: string
  /** 现住址 */
  currentAddress?: string
  /** 户籍地址 */
  permanentAddress?: string
  /** 紧急联系人 */
  emergencyContact?: string
  /** 紧急联系人电话 */
  emergencyPhone?: string
  /** 紧急联系人关系 */
  emergencyRelation?: string
  /** 技能特长 */
  skills?: string[]
  /** 证书资质 */
  certificates?: string[]
  /** 备注 */
  remark?: string
  /** 头像 */
  avatar?: string
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 员工查询参数
 */
export interface EmployeeQuery {
  /** 关键词（姓名、工号、手机号） */
  keyword?: string
  /** 部门ID */
  departmentId?: string
  /** 职位ID */
  positionId?: string
  /** 状态 */
  status?: EmployeeStatus
  /** 员工类型 */
  type?: EmployeeType
  /** 性别 */
  gender?: Gender
  /** 学历 */
  education?: Education
  /** 入职日期范围 */
  joinDateRange?: [string, string]
  /** 年龄范围 */
  ageRange?: [number, number]
}

/**
 * 员工创建参数
 */
export interface EmployeeCreateParams {
  /** 工号 */
  employeeNo: string
  /** 姓名 */
  name: string
  /** 性别 */
  gender: Gender
  /** 身份证号 */
  idCard: string
  /** 手机号 */
  phone: string
  /** 邮箱 */
  email?: string
  /** 员工类型 */
  type: EmployeeType
  /** 部门ID */
  departmentId: string
  /** 职位ID */
  positionId: string
  /** 直属上级ID */
  managerId?: string
  /** 出生日期 */
  birthDate: string
  /** 民族 */
  ethnicity?: string
  /** 籍贯 */
  hometown?: string
  /** 政治面貌 */
  politicalStatus?: string
  /** 婚姻状况 */
  maritalStatus?: MaritalStatus
  /** 学历 */
  education?: Education
  /** 毕业院校 */
  graduateSchool?: string
  /** 专业 */
  major?: string
  /** 毕业时间 */
  graduateDate?: string
  /** 入职日期 */
  joinDate: string
  /** 试用期（月） */
  probationPeriod?: number
  /** 合同开始日期 */
  contractStartDate?: string
  /** 合同结束日期 */
  contractEndDate?: string
  /** 社保账号 */
  socialSecurityNo?: string
  /** 公积金账号 */
  housingFundNo?: string
  /** 银行卡号 */
  bankCardNo?: string
  /** 开户银行 */
  bankName?: string
  /** 现住址 */
  currentAddress?: string
  /** 户籍地址 */
  permanentAddress?: string
  /** 紧急联系人 */
  emergencyContact?: string
  /** 紧急联系人电话 */
  emergencyPhone?: string
  /** 紧急联系人关系 */
  emergencyRelation?: string
  /** 备注 */
  remark?: string
}

/**
 * 员工更新参数
 */
export interface EmployeeUpdateParams extends Partial<EmployeeCreateParams> {
  /** 员工ID */
  id: string
  /** 状态 */
  status?: EmployeeStatus
  /** 转正日期 */
  regularDate?: string
  /** 离职日期 */
  resignDate?: string
}

/**
 * 员工导入数据
 */
export interface EmployeeImportData {
  /** 工号 */
  employeeNo: string
  /** 姓名 */
  name: string
  /** 性别 */
  gender: string
  /** 身份证号 */
  idCard: string
  /** 手机号 */
  phone: string
  /** 邮箱 */
  email?: string
  /** 部门名称 */
  departmentName: string
  /** 职位名称 */
  positionName: string
  /** 员工类型 */
  typeName: string
  /** 入职日期 */
  joinDate: string
  /** 其他字段... */
  [key: string]: any
}

/**
 * 员工统计信息
 */
export interface EmployeeStatistics {
  /** 总人数 */
  total: number
  /** 在职人数 */
  active: number
  /** 试用期人数 */
  probation: number
  /** 本月入职 */
  joinedThisMonth: number
  /** 本月离职 */
  resignedThisMonth: number
  /** 本月转正 */
  regularThisMonth: number
  /** 按部门统计 */
  byDepartment: Array<{
    departmentId: string
    departmentName: string
    count: number
  }>
  /** 按类型统计 */
  byType: Array<{
    type: EmployeeType
    count: number
  }>
  /** 按学历统计 */
  byEducation: Array<{
    education: Education
    count: number
  }>
}
