/**
 * 组织结构实体类型定义
 */

/**
 * 部门类型枚举
 */
export enum DepartmentType {
  /** 公司 */
  COMPANY = 'company',
  /** 部门 */
  DEPARTMENT = 'department',
  /** 分公司 */
  BRANCH = 'branch',
  /** 小组 */
  GROUP = 'group'
}

/**
 * 部门状态枚举
 */
export enum DepartmentStatus {
  /** 正常 */
  ACTIVE = 'active',
  /** 停用 */
  INACTIVE = 'inactive',
  /** 撤销 */
  CANCELLED = 'cancelled'
}

/**
 * 部门基础信息
 */
export interface DepartmentBase {
  /** 部门ID */
  id: string
  /** 部门编码 */
  code: string
  /** 部门名称 */
  name: string
  /** 部门简称 */
  shortName?: string
  /** 部门类型 */
  type: DepartmentType
  /** 状态 */
  status: DepartmentStatus
  /** 排序号 */
  sort: number
}

/**
 * 部门详细信息
 */
export interface Department extends DepartmentBase {
  /** 父部门ID */
  parentId?: string
  /** 父部门 */
  parent?: Department
  /** 子部门列表 */
  children?: Department[]
  /** 部门路径（从根到当前部门的ID路径） */
  path?: string[]
  /** 部门层级 */
  level?: number
  /** 负责人ID */
  managerId?: string
  /** 负责人姓名 */
  managerName?: string
  /** 联系电话 */
  phone?: string
  /** 办公地址 */
  address?: string
  /** 备注 */
  remark?: string
  /** 员工数量 */
  employeeCount?: number
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 创建人 */
  createdBy?: string
  /** 更新人 */
  updatedBy?: string
}

/**
 * 职位级别枚举
 */
export enum PositionLevel {
  /** 高级管理层 */
  SENIOR = 'senior',
  /** 中级管理层 */
  MIDDLE = 'middle',
  /** 基层管理 */
  JUNIOR = 'junior',
  /** 普通员工 */
  STAFF = 'staff',
  /** 实习生 */
  INTERN = 'intern'
}

/**
 * 职位状态枠举
 */
export enum PositionStatus {
  /** 正常 */
  ACTIVE = 'active',
  /** 停用 */
  INACTIVE = 'inactive',
  /** 待审核 */
  PENDING = 'pending'
}

/**
 * 职位信息
 */
export interface Position {
  /** 职位ID */
  id: string
  /** 职位编码 */
  code: string
  /** 职位名称 */
  name: string
  /** 职位级别 */
  level: PositionLevel
  /** 所属部门ID */
  departmentId?: string
  /** 所属部门 */
  department?: Department
  /** 职位描述 */
  description?: string
  /** 职责说明 */
  responsibilities?: string
  /** 任职要求 */
  requirements?: string
  /** 编制人数 */
  headcount?: number
  /** 在职人数 */
  currentCount?: number
  /** 状态 */
  status: PositionStatus
  /** 排序号 */
  sort: number
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 部门树节点
 */
export interface DepartmentTreeNode extends Department {
  /** 子节点 */
  children?: DepartmentTreeNode[]
  /** 是否叶子节点 */
  isLeaf?: boolean
  /** 是否展开 */
  expanded?: boolean
  /** 是否选中 */
  selected?: boolean
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 部门查询参数
 */
export interface DepartmentQuery {
  /** 关键词（名称、编码） */
  keyword?: string
  /** 部门类型 */
  type?: DepartmentType
  /** 状态 */
  status?: DepartmentStatus
  /** 父部门ID */
  parentId?: string
  /** 是否包含子部门 */
  includeChildren?: boolean
}

/**
 * 部门创建参数
 */
export interface DepartmentCreateParams {
  /** 部门编码 */
  code: string
  /** 部门名称 */
  name: string
  /** 部门简称 */
  shortName?: string
  /** 部门类型 */
  type: DepartmentType
  /** 父部门ID */
  parentId?: string
  /** 负责人ID */
  managerId?: string
  /** 联系电话 */
  phone?: string
  /** 办公地址 */
  address?: string
  /** 备注 */
  remark?: string
  /** 排序号 */
  sort?: number
}

/**
 * 部门更新参数
 */
export interface DepartmentUpdateParams extends Partial<DepartmentCreateParams> {
  /** 部门ID */
  id: string
  /** 状态 */
  status?: DepartmentStatus
}

/**
 * 职位查询参数
 */
export interface PositionQuery {
  /** 关键词（名称、编码） */
  keyword?: string
  /** 部门ID */
  departmentId?: string
  /** 职位级别 */
  level?: PositionLevel
  /** 状态 */
  status?: PositionStatus
}

/**
 * 职位创建参数
 */
export interface PositionCreateParams {
  /** 职位编码 */
  code: string
  /** 职位名称 */
  name: string
  /** 职位级别 */
  level: PositionLevel
  /** 所属部门ID */
  departmentId?: string
  /** 职位描述 */
  description?: string
  /** 职责说明 */
  responsibilities?: string
  /** 任职要求 */
  requirements?: string
  /** 编制人数 */
  headcount?: number
  /** 排序号 */
  sort?: number
}

/**
 * 职位更新参数
 */
export interface PositionUpdateParams extends Partial<PositionCreateParams> {
  /** 职位ID */
  id: string
  /** 状态 */
  status?: PositionStatus
}
