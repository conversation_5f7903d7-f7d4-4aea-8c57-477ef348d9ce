// 类型声明文件：bpmn-js-properties-panel
// 用于修复第三方库类型声明问题

declare module 'bpmn-js-properties-panel' {
  export interface PropertiesPanel {
    attachTo(container: HTMLElement): void
    detach(): void
    destroy(): void
  }

  export interface PropertiesPanelOptions {
    container?: HTMLElement
    parent?: HTMLElement
     
    groups?: any[]
     
    entries?: any[]
  }

  export default class BpmnPropertiesPanel {
    constructor(options: PropertiesPanelOptions)
    attachTo(container: HTMLElement): void
    detach(): void
    destroy(): void
  }
}

declare module 'bpmn-js-properties-panel/lib/provider/camunda' {
   
  export default function CamundaPropertiesProvider(): any
}

declare module 'bpmn-js-properties-panel/lib/provider/bpmn' {
   
  export default function BpmnPropertiesProvider(): any
}

// 添加缺失的模块导出
 
export const BpmnPropertiesPanelModule: any
 
export const BpmnPropertiesProviderModule: any
