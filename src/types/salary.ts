// 薪资相关类型定义

// 薪资项目
export interface SalaryItem {
  id?: string
  tempId?: string // 临时ID，用于前端新增时
  name: string // 项目名称
  code: string // 项目编码
  type: 'fixed' | 'calculated' | 'deduction' // 项目类型：固定项、计算项、扣除项
  category:
    | 'basic'
    | 'position'
    | 'performance'
    | 'allowance'
    | 'bonus'
    | 'insurance'
    | 'tax'
    | 'other' // 项目分类
  value?: number // 固定值
  formula?: string // 计算公式
  visible: boolean // 是否在工资条中显示
  editable: boolean // 是否允许手动编辑
  sort: number // 排序号
  remark?: string // 备注
  validationRules?: {
    required?: boolean
    min?: number
    max?: number
    precision?: number
  }
  dependencies?: string[] // 依赖的其他项目编码
  createdAt?: string
  updatedAt?: string
}

// 薪资结构
export interface SalaryStructure {
  id: string
  code: string // 结构编码
  name: string // 结构名称
  scope: 'all' | 'teacher' | 'admin' | 'support' // 适用范围
  description?: string // 描述信息
  items: SalaryItem[] // 薪资项目列表
  version: string // 版本号
  status: 'active' | 'inactive' // 状态
  effectiveDate?: string // 生效日期
  expiryDate?: string // 失效日期
  autoCalculate: boolean // 是否自动计算
  allowOverride: boolean // 是否允许覆盖
  totalItems: number // 项目总数
  fixedItems: number // 固定项目数
  calculatedItems: number // 计算项目数
  deductionItems: number // 扣除项目数
  usageCount?: number // 使用次数
  lastUsedAt?: string // 最后使用时间
  remark?: string // 备注
  isTemplate?: boolean // 是否为模板
  templateId?: string // 模板ｉＤ（如果从模板创建）
  createTime?: string
  updateTime?: string
  createBy?: string
  updateBy?: string
}

// 薪资结构查询参数
export interface SalaryStructureQuery {
  name?: string
  scope?: string
  status?: string
}

// 薪资结构创建请求
export interface SalaryStructureCreateRequest {
  code: string // 结构编码（必填，唯一）
  name: string // 结构名称（必填）
  scope: 'all' | 'teacher' | 'admin' | 'support' // 适用范围（必填）
  description?: string // 描述信息
  items: SalaryItemCreateRequest[] // 薪资项目列表（必填，至少一项）
  effectiveDate?: string // 生效日期（默认为当前日期）
  version?: string // 版本号（默认为1.0）
  status?: 'active' | 'inactive' // 状态（默认为inactive）
  remark?: string // 备注
  autoCalculate?: boolean // 是否自动计算（默认true）
  allowOverride?: boolean // 是否允许覆盖（默认false）
}

// 薪资结构更新请求
export interface SalaryStructureUpdateRequest {
  name?: string // 结构名称
  scope?: 'all' | 'teacher' | 'admin' | 'support' // 适用范围
  description?: string // 描述信息
  items?: SalaryItemCreateRequest[] // 薪资项目列表
  version?: string // 版本号
  remark?: string // 备注
  autoCalculate?: boolean // 是否自动计算
  allowOverride?: boolean // 是否允许覆盖
}

// 薪资项目创建请求
export interface SalaryItemCreateRequest {
  name: string // 项目名称（必填）
  code: string // 项目编码（必填，在结构内唯一）
  type: 'fixed' | 'calculated' | 'deduction' // 项目类型（必填）
  category:
    | 'basic'
    | 'position'
    | 'performance'
    | 'allowance'
    | 'bonus'
    | 'insurance'
    | 'tax'
    | 'other' // 项目分类
  value?: number // 固定值（type为fixed时必填）
  formula?: string // 计算公式（type为calculated时必填）
  visible: boolean // 是否在工资条中显示（默认true）
  editable: boolean // 是否允许手动编辑（默认false）
  sort: number // 排序号（必填，从1开始）
  remark?: string // 备注
  validationRules?: {
    required?: boolean // 是否必填
    min?: number // 最小值
    max?: number // 最大值
    precision?: number // 小数位数（默认2）
  }
  dependencies?: string[] // 依赖的其他项目编码列表（用于公式计算）
}

// 薪资标准
export interface SalaryStandard {
  id: string
  structureId: string // 关联的薪资结构ID
  structureName?: string
  positionId: string // 岗位ID
  positionName?: string
  levelId: string // 级别ID
  levelName?: string
  items: SalaryStandardItem[] // 标准项目列表
  effectiveDate: string // 生效日期
  status: string // 状态
  remark?: string
  createTime?: string
  updateTime?: string
}

// 薪资标准项目
export interface SalaryStandardItem {
  itemId: string // 薪资项目ID
  itemName?: string
  itemCode?: string
  standardValue: number // 标准值
  minValue?: number // 最小值
  maxValue?: number // 最大值
}

// 个人薪资记录
export interface PersonalSalary {
  id: string
  employeeId: string // 员工ID
  employeeNo: string // 工号
  employeeName: string // 姓名
  departmentId: string // 部门ID
  departmentName: string // 部门名称
  positionId: string // 岗位ID
  positionName: string // 岗位名称
  levelId: string // 级别ID
  levelName: string // 级别名称
  salaryMonth: string // 薪资月份 (YYYY-MM)

  // 收入项目
  baseSalary: number // 基本工资
  positionSalary: number // 岗位工资
  rankSalary: number // 薪级工资
  performanceSalary: number // 绩效工资
  allowance: number // 津贴
  subsidy: number // 补贴
  bonus: number // 奖金
  otherIncome: number // 其他收入

  // 扣除项目
  pensionInsurance: number // 养老保险
  medicalInsurance: number // 医疗保险
  unemploymentInsurance: number // 失业保险
  injuryInsurance: number // 工伤保险
  maternityInsurance: number // 生育保险
  housingFund: number // 公积金
  incomeTax: number // 个人所得税
  otherDeduction: number // 其他扣除

  // 汇总
  grossSalary: number // 应发工资
  totalDeduction: number // 扣款合计
  netSalary: number // 实发工资

  paymentStatus: 'pending' | 'paid' | 'failed' // 发放状态
  paymentDate?: string // 发放日期
  remark?: string // 备注

  createdBy?: string
  createdAt?: string
  updatedBy?: string
  updatedAt?: string
}

// 个人薪资明细项
export interface PersonalSalaryItem {
  itemId: string
  itemName: string
  itemCode: string
  itemType: string
  value: number // 金额
  formula?: string // 使用的公式
  visible: boolean // 是否显示
  sort: number
}

// 薪资计算参数
export interface SalaryCalculateParams {
  employeeId: string
  period: string
  structureId: string
  baseData: Record<string, number> // 基础数据，如出勤天数、加班时数等
}

// 薪资计算结果
export interface SalaryCalculateResult {
  employeeId: string
  period: string
  items: PersonalSalaryItem[]
  totalIncome: number
  totalDeduction: number
  netSalary: number
  warnings?: string[] // 计算警告信息
}

// 个税相关
export interface TaxCalculation {
  income: number // 收入
  insurance: number // 五险一金
  specialDeduction: number // 专项扣除
  otherDeduction: number // 其他扣除
  taxableIncome: number // 应纳税所得额
  taxRate: number // 税率
  quickDeduction: number // 速算扣除数
  tax: number // 应纳税额
}

// 薪资统计
export interface SalaryStatistics {
  period: string
  departmentId?: string
  departmentName?: string
  employeeCount: number // 人数
  totalAmount: number // 总额
  averageAmount: number // 平均值
  items: SalaryStatisticsItem[] // 各项统计
}

// 薪资统计项
export interface SalaryStatisticsItem {
  itemName: string
  itemCode: string
  totalAmount: number
  averageAmount: number
  percentage: number // 占比
}
