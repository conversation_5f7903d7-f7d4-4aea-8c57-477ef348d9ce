/**
 * 编制规划相关类型定义
 */

import { PositionCategory, StaffingPlanStatus } from './enums'

// 编制规划接口
export interface StaffingPlan {
  id: number
  planCode: string
  planName: string
  planYear: number
  organizationId: string
  organizationName?: string
  positionCategory?: PositionCategory
  approvedHeadcount: number
  currentHeadcount: number
  vacantHeadcount: number
  overHeadcount: number
  effectiveDate: string
  expiryDate?: string
  status: StaffingPlanStatus
  description?: string
  documentUrl?: string
  createTime: string
  createBy?: string
  updateTime?: string
  updateBy?: string
  version: number
}

// 编制规划创建请求
export interface StaffingPlanCreateRequest {
  planCode: string
  planName: string
  planYear: number
  organizationId: string
  positionCategory?: PositionCategory
  approvedHeadcount: number
  effectiveDate: string
  expiryDate?: string
  description?: string
  documentUrl?: string
}

// 编制规划更新请求
export interface StaffingPlanUpdateRequest extends StaffingPlanCreateRequest {
  status?: StaffingPlanStatus
  version: number
}

// 编制规划查询请求
export interface StaffingPlanQueryRequest {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
  keyword?: string
  planYear?: number
  organizationId?: string
  positionCategory?: PositionCategory
  status?: StaffingPlanStatus
  effectiveDateStart?: string
  effectiveDateEnd?: string
}

// 编制规划批量操作请求
export interface StaffingPlanBatchRequest {
  ids: number[]
  action: 'APPROVE' | 'REJECT' | 'DELETE' | 'ACTIVATE' | 'EXPIRE'
  reason?: string
  effectiveDate?: string
}

// 编制规划批量操作响应
export interface StaffingPlanBatchResponse {
  totalCount: number
  successCount: number
  failedCount: number
  results: BatchOperationResult[]
}

// 批量操作结果
export interface BatchOperationResult {
  id: number
  success: boolean
  message?: string
  error?: string
}

// 编制规划模板
export interface StaffingPlanTemplate {
  id: number
  templateCode: string
  templateName: string
  description?: string
  positionCategory?: PositionCategory
  organizationType?: string
  headcountFormula?: string
  parameters: TemplateParameter[]
  createTime: string
  createBy: string
  updateTime?: string
  updateBy?: string
  version: number
}

// 模板参数
export interface TemplateParameter {
  parameterName: string
  parameterType: 'NUMBER' | 'STRING' | 'DATE' | 'BOOLEAN'
  required: boolean

  defaultValue?: unknown
  description?: string
  validationRule?: string
}

// 编制规划历史记录
export interface StaffingPlanHistory {
  id: number
  planId: number
  operationType: 'CREATE' | 'UPDATE' | 'APPROVE' | 'REJECT' | 'DELETE' | 'RESTORE'
  operationTime: string
  operatorId: string
  operatorName: string
  beforeData?: string
  afterData?: string
  changeDescription?: string
  ipAddress?: string
  userAgent?: string
}

// 编制规划对比结果
export interface StaffingPlanComparison {
  baseYear: number
  compareYear: number
  organizationId: string
  organizationName: string
  positionCategory?: PositionCategory
  baseHeadcount: number
  compareHeadcount: number
  difference: number
  percentageChange: number
  trend: 'INCREASE' | 'DECREASE' | 'STABLE'
  details: ComparisonDetail[]
}

// 对比详情
export interface ComparisonDetail {
  fieldName: string

  baseValue: unknown

  compareValue: unknown

  difference: unknown
  percentageChange?: number
}

// 编制规划复制请求
export interface StaffingPlanCopyRequest {
  sourcePlanId: number
  targetYear: number
  targetOrganizationIds?: string[]
  copyOptions: {
    copyHeadcount: boolean
    copyPositions: boolean
    copyBudget: boolean
    adjustmentFactor?: number
  }
}

// 编制规划复制响应
export interface StaffingPlanCopyResponse {
  copiedCount: number
  failedCount: number
  results: CopyResult[]
}

// 复制结果
export interface CopyResult {
  sourceId: number
  targetId?: number
  organizationId: string
  organizationName: string
  success: boolean
  message?: string
}
