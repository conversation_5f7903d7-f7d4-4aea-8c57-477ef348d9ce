/**
 * 编制管理相关常量和配置
 */

import { 
  PositionCategory, 
  StaffingPlanStatus,
  StaffingAlertType,
  AlertLevel,
  UrgencyLevel,
  ApprovalStatus,
  NotificationMethod
} from './enums'

// 岗位类别选项
export const positionCategoryOptions = [
  { label: '管理岗', value: PositionCategory.MANAGEMENT },
  { label: '专业技术岗', value: PositionCategory.PROFESSIONAL },
  { label: '工勤岗', value: PositionCategory.SUPPORT }
]

// 编制规划状态选项
export const staffingPlanStatusOptions = [
  { label: '草稿', value: StaffingPlanStatus.DRAFT },
  { label: '待审批', value: StaffingPlanStatus.PENDING_APPROVAL },
  { label: '已批准', value: StaffingPlanStatus.APPROVED },
  { label: '已驳回', value: StaffingPlanStatus.REJECTED },
  { label: '生效中', value: StaffingPlanStatus.ACTIVE },
  { label: '已过期', value: StaffingPlanStatus.EXPIRED }
]

// 编制规划状态颜色映射
export const staffingPlanStatusColors = {
  [StaffingPlanStatus.DRAFT]: 'info',
  [StaffingPlanStatus.PENDING_APPROVAL]: 'warning',
  [StaffingPlanStatus.APPROVED]: 'success',
  [StaffingPlanStatus.REJECTED]: 'danger',
  [StaffingPlanStatus.ACTIVE]: 'primary',
  [StaffingPlanStatus.EXPIRED]: 'info'
}

// 预警类型选项
export const staffingAlertTypeOptions = [
  { label: '超编预警', value: StaffingAlertType.OVER_STAFFED },
  { label: '缺编预警', value: StaffingAlertType.UNDER_STAFFED },
  { label: '低利用率预警', value: StaffingAlertType.LOW_UTILIZATION },
  { label: '高利用率预警', value: StaffingAlertType.HIGH_UTILIZATION },
  { label: '预算超支预警', value: StaffingAlertType.BUDGET_EXCEEDED },
  { label: '成本异常预警', value: StaffingAlertType.COST_ANOMALY }
]

// 预警级别选项
export const alertLevelOptions = [
  { label: '信息', value: AlertLevel.INFO, color: 'info'
  },
  { label: '警告', value: AlertLevel.WARNING, color: 'warning'
  },
  { label: '错误', value: AlertLevel.ERROR, color: 'danger'
  },
  { label: '严重', value: AlertLevel.CRITICAL, color: 'danger' }
]

// 紧急程度选项
export const urgencyLevelOptions = [
  { label: '低', value: UrgencyLevel.LOW, color: 'info'
  },
  { label: '中', value: UrgencyLevel.MEDIUM, color: 'warning'
  },
  { label: '高', value: UrgencyLevel.HIGH, color: 'danger'
  },
  { label: '紧急', value: UrgencyLevel.URGENT, color: 'danger' }
]

// 审批状态选项
export const approvalStatusOptions = [
  { label: '草稿', value: ApprovalStatus.DRAFT, color: 'info'
  },
  { label: '已提交', value: ApprovalStatus.SUBMITTED, color: 'primary'
  },
  { label: '审核中', value: ApprovalStatus.IN_REVIEW, color: 'warning'
  },
  { label: '已批准', value: ApprovalStatus.APPROVED, color: 'success'
  },
  { label: '已拒绝', value: ApprovalStatus.REJECTED, color: 'danger'
  },
  { label: '已撤回', value: ApprovalStatus.WITHDRAWN, color: 'info'
  },
  { label: '已实施', value: ApprovalStatus.IMPLEMENTED, color: 'success'
  },
  { label: '已取消', value: ApprovalStatus.CANCELLED, color: 'info' }
]

// 通知方式选项
export const notificationMethodOptions = [
  { label: '邮件', value: NotificationMethod.EMAIL, icon: 'el-icon-message'
  },
  { label: '短信', value: NotificationMethod.SMS, icon: 'el-icon-mobile'
  },
  { label: '系统通知', value: NotificationMethod.SYSTEM, icon: 'el-icon-bell'
  },
  { label: '微信', value: NotificationMethod.WECHAT, icon: 'el-icon-chat-dot-round'
  },
  { label: '钉钉', value: NotificationMethod.DINGTALK, icon: 'el-icon-connection' }
]

// 默认配置
export const DEFAULT_PAGE_SIZE = 20
export const MAX_EXPORT_RECORDS = 10000
export const DEFAULT_ALERT_THRESHOLD = {
  overStaffed: 10,      // 超编预警阈值（%）
  underStaffed: 20,     // 缺编预警阈值（%）
  lowUtilization: 60,   // 低利用率预警阈值（%）
  highUtilization: 95,  // 高利用率预警阈值（%）
  budgetExceeded: 10,   // 预算超支预警阈值（%）
  costAnomaly: 20       // 成本异常预警阈值（%）
}

// 图表颜色配置
export const CHART_COLORS = {
  primary: '#409EFF',
  success: '#67C23A',
  warning: '#E6A23C',
  danger: '#F56C6C',
  info: '#909399',
  series: [
    '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
    '#00CED1', '#FF6347', '#FFD700', '#9370DB', '#32CD32'
  ]
}

// 导出配置
export const EXPORT_CONFIG = {
  fileName: {
    staffingPlan: '编制规划',
    staffingStatistics: '编制统计',
    alertRecords: '预警记录',
    adjustmentRequest: '编制调整申请'
  },
  sheetName: {
    summary: '汇总',
    detail: '明细',
    statistics: '统计',
    trend: '趋势'
  }
}