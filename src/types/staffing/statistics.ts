/**
 * 编制统计和分析相关类型定义
 */

import { PositionCategory } from './enums'

// 编制统计数据
export interface StaffingStatistics {
  organizationId: string
  organizationName: string
  positionCategory?: PositionCategory
  approvedHeadcount: number
  currentHeadcount: number
  vacantHeadcount: number
  overHeadcount: number
  utilizationRate: number
  lastUpdateTime: string
}

// 编制统计查询请求
export interface StaffingStatisticsRequest {
  planYear?: number
  organizationIds?: string[]
  positionCategories?: PositionCategory[]
  includeSubOrganizations?: boolean
  statisticsDate?: string
  dateRange?: string[]
  startDate?: string
  endDate?: string
}

// 编制趋势数据
export interface StaffingTrend {
  date: string
  organizationId: string
  organizationName: string
  positionCategory?: PositionCategory
  approvedHeadcount: number
  currentHeadcount: number
  utilizationRate: number
}

// 编制分析报告
export interface StaffingAnalysisReport {
  reportId: string
  reportName: string
  reportType: 'SUMMARY' | 'TREND' | 'COMPARISON' | 'FORECAST'
  planYear: number
  generateTime: string
  summary: {
    totalApproved: number
    totalCurrent: number
    totalVacant: number
    totalOver: number
    averageUtilization: number
  }
  organizationStats: StaffingStatistics[]
  trends?: StaffingTrend[]
  charts?: ChartData[]
}

// 图表数据
export interface ChartData {
  chartType: 'bar' | 'pie' | 'line' | 'area'
  title: string

  data: unknown[]
  xAxis?: string[]
  yAxis?: string[]

  series?: unknown[]
}

// 编制利用率统计（增强版）
export interface StaffingUtilizationStats {
  organizationId: number
  organizationName: string
  organizationCode: string
  parentOrganizationId?: number
  level: number
  totalPositions: number // 总编制数
  filledPositions: number // 已填充岗位数
  vacantPositions: number // 空缺岗位数
  utilizationRate: number // 利用率 (%)
  overStaffed: number // 超编数量
  underStaffed: number // 缺编数量
  budgetAmount: number // 预算金额
  actualCost: number // 实际成本
  costUtilizationRate: number // 成本利用率
  lastUpdated: string // 最后更新时间
  trend: 'UP' | 'DOWN' | 'STABLE' // 趋势
}

// 编制对比分析
export interface StaffingComparisonAnalysis {
  comparisonId: string
  comparisonName: string
  comparisonType: 'PERIOD' | 'ORGANIZATION' | 'CATEGORY'
  baseData: StaffingComparisonData
  compareData: StaffingComparisonData[]
  differences: ComparisonDifference[]
  insights: AnalysisInsight[]
  recommendations: AnalysisRecommendation[]
}

// 对比数据
export interface StaffingComparisonData {
  label: string
  organizationId?: string
  period?: string
  category?: PositionCategory
  metrics: ComparisonMetrics
}

// 对比指标
export interface ComparisonMetrics {
  headcount: number
  utilization: number
  cost: number
  efficiency: number
  growthRate?: number
}

// 对比差异
export interface ComparisonDifference {
  metric: string
  baseValue: number
  compareValue: number
  difference: number
  percentageChange: number
  significance: 'HIGH' | 'MEDIUM' | 'LOW'
}

// 分析洞察
export interface AnalysisInsight {
  insightId: string
  type: 'TREND' | 'ANOMALY' | 'PATTERN' | 'RISK' | 'OPPORTUNITY'
  title: string
  description: string
  impact: 'HIGH' | 'MEDIUM' | 'LOW'
  confidence: number

  supportingData: unknown[]
}

// 分析建议
export interface AnalysisRecommendation {
  recommendationId: string
  priority: 'HIGH' | 'MEDIUM' | 'LOW'
  category: string
  title: string
  description: string
  expectedBenefit: string
  implementationComplexity: 'HIGH' | 'MEDIUM' | 'LOW'
  estimatedCost?: number
  estimatedTimeframe?: string
}

// 预测数据请求
export interface StaffingForecastRequest {
  organizationIds: string[]
  forecastPeriod: number // 预测期数（月）
  forecastModel?: string
  includeSeasonality?: boolean
  confidenceLevel?: number
  scenarioType?: 'OPTIMISTIC' | 'REALISTIC' | 'PESSIMISTIC'
}

// 预测数据响应
export interface StaffingForecastResponse {
  forecastId: string
  generatedAt: string
  forecastPeriod: number
  model: string
  accuracy: number
  forecasts: ForecastData[]
  confidenceIntervals: ConfidenceInterval[]
  assumptions: string[]
  risks: string[]
}

// 预测数据
export interface ForecastData {
  period: string
  organizationId: string
  predictedHeadcount: number
  predictedUtilization: number
  predictedCost: number
  probability: number
}

// 置信区间
export interface ConfidenceInterval {
  period: string
  metric: string
  lowerBound: number
  upperBound: number
  confidenceLevel: number
}

// 编制效率分析
export interface StaffingEfficiencyAnalysis {
  organizationId: string
  analysisDate: string
  overallEfficiencyScore: number
  categoryScores: CategoryEfficiencyScore[]
  bottlenecks: EfficiencyBottleneck[]
  improvementOpportunities: ImprovementOpportunity[]
}

// 类别效率评分
export interface CategoryEfficiencyScore {
  category: string
  score: number
  benchmark: number
  gap: number
  trend: 'IMPROVING' | 'DECLINING' | 'STABLE'
}

// 效率瓶颈
export interface EfficiencyBottleneck {
  area: string
  severity: 'HIGH' | 'MEDIUM' | 'LOW'
  impact: string
  rootCause: string
  suggestedAction: string
}

// 改进机会
export interface ImprovementOpportunity {
  opportunityId: string
  area: string
  currentState: string
  desiredState: string
  potentialSaving: number
  implementationEffort: 'HIGH' | 'MEDIUM' | 'LOW'
  priority: number
}
