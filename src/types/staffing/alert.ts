/**
 * 编制预警相关类型定义
 */

import {
  StaffingAlertType,
  NotificationMethod,
  AlertLevel,
  AlertStatus,
  AlertAction,
  ProcessingResult
} from './enums'

// 编制预警配置
export interface StaffingAlertConfig {
  id: number
  organizationId?: number // 组织ID，为空表示全局配置
  alertType: StaffingAlertType
  threshold: number // 阈值
  enabled: boolean // 是否启用
  notificationMethods: NotificationMethod[] // 通知方式
  recipients: string[] // 接收人列表
  description?: string // 描述
  updating?: boolean // 是否正在更新
  createTime: string
  updateTime: string
  createBy: string
  updateBy: string
}

// 编制预警记录
export interface StaffingAlertRecord {
  id: number
  organizationId: number
  organizationName: string
  alertType: StaffingAlertType
  alertLevel: AlertLevel
  currentValue: number
  thresholdValue: number
  message: string
  description?: string
  suggestion?: string
  status: AlertStatus
  triggerTime: string
  resolveTime?: string
  processor?: string
  processComment?: string
  source: string
  metadata?: Record<string, unknown>
}

// 预警处理记录
export interface AlertProcessingRecord {
  id: number
  alertId: number
  action: AlertAction
  processor: string
  processTime: string
  comment?: string
  attachments?: string[]
  result: ProcessingResult
}

// 预警统计查询参数
export interface AlertStatisticsQuery {
  organizationIds?: number[]
  alertTypes?: StaffingAlertType[]
  alertLevels?: AlertLevel[]
  startDate?: string
  endDate?: string
  dateRange?: string[]
  groupBy?: 'DATE' | 'TYPE' | 'LEVEL' | 'ORGANIZATION'
  timeGranularity?: 'HOUR' | 'DAY' | 'WEEK' | 'MONTH'
}

// 预警统计结果
export interface AlertStatisticsResult {
  query: AlertStatisticsQuery
  summary: AlertSummary
  trends: AlertTrendData[]
  distribution: AlertDistributionData[]
  topAlerts: StaffingAlertRecord[]
  performanceMetrics: AlertPerformanceMetrics
}

// 预警汇总
export interface AlertSummary {
  totalAlerts: number
  activeAlerts: number
  resolvedAlerts: number
  averageResolutionTime: number
  alertsByType: Record<string, number>
  alertsByLevel: Record<string, number>
  alertsByStatus: Record<string, number>
}

// 预警趋势数据
export interface AlertTrendData {
  period: string
  totalCount: number
  newCount: number
  resolvedCount: number
  activeCount: number
  byType: Record<string, number>
  byLevel: Record<string, number>
}

// 预警分布数据
export interface AlertDistributionData {
  dimension: string
  value: string
  count: number
  percentage: number
  subDistribution?: Record<string, number>
}

// 预警性能指标
export interface AlertPerformanceMetrics {
  averageDetectionTime: number // 平均检测时间（分钟）
  averageResponseTime: number // 平均响应时间（小时）
  averageResolutionTime: number // 平均解决时间（小时）
  falsePositiveRate: number // 误报率（%）
  escalationRate: number // 升级率（%）
  automationRate: number // 自动化处理率（%）
}

// 预警配置验证结果
export interface AlertConfigValidation {
  valid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  suggestions: string[]
}

// 验证错误
export interface ValidationError {
  field: string
  message: string
  code: string
}

// 验证警告
export interface ValidationWarning {
  field: string
  message: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH'
}

// 预警配置导入请求
export interface AlertConfigImportRequest {
  fileId: string
  overwriteExisting: boolean
  validateOnly: boolean
}

// 预警配置导入响应
export interface AlertConfigImportResponse {
  totalRecords: number
  importedRecords: number
  skippedRecords: number
  failedRecords: number
  errors: ImportError[]
  warnings: ImportWarning[]
}

// 导入错误
export interface ImportError {
  row: number
  field: string

  value: unknown
  message: string
}

// 导入警告
export interface ImportWarning {
  row: number
  field: string

  value: unknown
  message: string
}

// 预警订阅配置
export interface AlertSubscription {
  id: number
  userId: string
  userName: string
  organizationIds: number[]
  alertTypes: StaffingAlertType[]
  alertLevels: AlertLevel[]
  notificationMethods: NotificationMethod[]
  emailAddress?: string
  phoneNumber?: string
  wechatId?: string
  dingtalkId?: string
  enabled: boolean
  createTime: string
  updateTime: string
}

// 预警通知模板
export interface AlertNotificationTemplate {
  id: number
  templateCode: string
  templateName: string
  alertType: StaffingAlertType
  notificationMethod: NotificationMethod
  subject?: string
  content: string
  variables: string[]
  enabled: boolean
  createTime: string
  updateTime: string
}

// 预警规则引擎配置
export interface AlertRuleEngine {
  id: number
  ruleName: string
  ruleDescription: string
  alertType: StaffingAlertType
  conditions: AlertCondition[]
  actions: AlertRuleAction[]
  priority: number
  enabled: boolean
  effectiveStartDate?: string
  effectiveEndDate?: string
  createTime: string
  updateTime: string
}

// 预警条件
export interface AlertCondition {
  field: string
  operator: 'EQ' | 'NE' | 'GT' | 'GE' | 'LT' | 'LE' | 'IN' | 'NOT_IN' | 'BETWEEN' | 'CONTAINS'

  value: unknown
  logicalOperator?: 'AND' | 'OR'
}

// 预警规则动作
export interface AlertRuleAction {
  actionType: 'NOTIFY' | 'ESCALATE' | 'AUTO_RESOLVE' | 'EXECUTE_SCRIPT' | 'CALL_API'
  actionConfig: Record<string, unknown>
  delayMinutes?: number
}
