/**
 * 编制管理类型定义模块导出
 *
 * 该模块包含了人力资源编制管理系统的所有类型定义，
 * 按功能域组织成不同的子模块，便于维护和使用。
 */

// 导出所有枚举
export * from './enums'

// 导出常量和配置
export * from './constants'

// 导出编制规划相关类型
export * from './plan'

// 导出统计和分析相关类型
export * from './statistics'

// 导出预警相关类型
export * from './alert'

// 导出审批流程相关类型
export * from './approval'

// 导出预测和优化模型相关类型
export * from './model'

// 导出通用类型
export * from './common'

/**
 * 类型定义模块说明：
 *
 * 1. enums.ts - 所有枚举定义（17个枚举）
 *    - 岗位类别、编制状态、预警类型、审批状态等
 *
 * 2. constants.ts - 常量和配置选项
 *    - 下拉选项、颜色映射、默认配置等
 *
 * 3. plan.ts - 编制规划相关类型（15个接口）
 *    - 编制规划、创建/更新请求、查询、批量操作等
 *
 * 4. statistics.ts - 统计和分析相关类型（28个接口）
 *    - 统计数据、趋势分析、预测、效率分析等
 *
 * 5. alert.ts - 预警相关类型（23个接口）
 *    - 预警配置、预警记录、统计、订阅、规则引擎等
 *
 * 6. approval.ts - 审批流程相关类型（21个接口）
 *    - 调整申请、成本分析、风险评估、实施计划等
 *
 * 7. model.ts - 预测和优化模型相关类型（27个接口）
 *    - 优化建议、预测模型、场景分析、控制策略等
 *
 * 8. common.ts - 通用类型（20个接口）
 *    - 分页、文件上传、组织架构、通知、审计等
 *
 * 总计：17个枚举 + 164个接口 = 181个类型定义
 */

// 类型守卫函数

export function isStaffingPlan(obj: unknown): obj is import('./plan').StaffingPlan {
  return obj && typeof obj.id === 'number' && typeof obj.planCode === 'string'
}

export function isStaffingAlert(obj: unknown): obj is import('./alert').StaffingAlertRecord {
  return obj && typeof obj.id === 'number' && obj.alertType && obj.alertLevel
}

export function isAdjustmentRequest(
  obj: unknown
): obj is import('./approval').StaffingAdjustmentRequest {
  return obj && typeof obj.id === 'number' && obj.requestType && obj.currentStatus
}

// 工具类型
export type StaffingModuleType = 'plan' | 'statistics' | 'alert' | 'approval' | 'model' | 'common'

export type StaffingEntityType =
  | 'StaffingPlan'
  | 'StaffingAlert'
  | 'AdjustmentRequest'
  | 'ForecastModel'
  | 'OptimizationRecommendation'

// 版本信息
export const STAFFING_TYPES_VERSION = '2.0.0'
export const STAFFING_TYPES_LAST_UPDATED = '2025-07-20'
