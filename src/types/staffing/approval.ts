/**
 * 编制审批流程相关类型定义
 */

import { AdjustmentRequestType, UrgencyLevel, ApprovalStatus } from './enums'

// 编制调整申请
export interface StaffingAdjustmentRequest {
  id: number
  requestNumber: string
  requestType: AdjustmentRequestType
  title: string
  description: string
  organizationId: number
  organizationName: string
  applicantId: string
  applicantName: string
  applicationDate: string
  urgencyLevel: UrgencyLevel
  expectedCompletionDate: string
  currentStatus: ApprovalStatus
  workflowInstanceId?: string
  adjustmentDetails: AdjustmentDetail[]
  attachments: RequestAttachment[]
  businessJustification: string
  costImpactAnalysis: CostImpactAnalysis
  riskAssessment: RiskAssessment
  implementationPlan: ImplementationPlan
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
}

// 调整详情
export interface AdjustmentDetail {
  id: number
  positionId?: number
  positionName: string
  positionLevel: string
  departmentId: number
  departmentName: string
  adjustmentType: 'ADD' | 'REMOVE' | 'MODIFY' | 'TRANSFER'
  currentCount: number
  targetCount: number
  adjustmentCount: number
  salaryRange: SalaryRange
  qualificationRequirements: string[]
  responsibilities: string
  reportingRelationship: string
  effectiveDate: string
  reason: string
}

// 薪资范围
export interface SalaryRange {
  minSalary: number
  maxSalary: number
  currency: string
  payFrequency: 'MONTHLY' | 'ANNUALLY'
}

// 申请附件
export interface RequestAttachment {
  id: number
  fileName: string
  fileSize: number
  fileType: string
  uploadTime: string
  uploadBy: string
  downloadUrl: string
  description?: string
}

// 成本影响分析
export interface CostImpactAnalysis {
  totalCostImpact: number
  salaryImpact: number
  benefitsImpact: number
  trainingCostImpact: number
  recruitmentCostImpact: number
  infrastructureCostImpact: number
  annualRecurringCost: number
  oneTimeCost: number
  costSavings: number
  netCostImpact: number
  budgetSource: string
  costJustification: string
}

// 风险评估
export interface RiskAssessment {
  overallRiskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  identifiedRisks: IdentifiedRisk[]
  mitigationStrategies: MitigationStrategy[]
  contingencyPlans: ContingencyPlan[]
  riskMatrix: RiskMatrixItem[]
}

// 已识别风险
export interface IdentifiedRisk {
  riskId: string
  riskCategory: string
  riskDescription: string
  probability: 'LOW' | 'MEDIUM' | 'HIGH'
  impact: 'LOW' | 'MEDIUM' | 'HIGH'
  riskScore: number
  riskOwner: string
  identifiedDate: string
}

// 缓解策略
export interface MitigationStrategy {
  strategyId: string
  riskId: string
  strategyDescription: string
  implementationSteps: string[]
  responsiblePerson: string
  timeline: string
  estimatedCost: number
  effectiveness: 'LOW' | 'MEDIUM' | 'HIGH'
}

// 应急计划
export interface ContingencyPlan {
  planId: string
  triggerCondition: string
  actionSteps: string[]
  responsibleTeam: string
  activationProcedure: string
  communicationPlan: string
}

// 风险矩阵项
export interface RiskMatrixItem {
  probability: 'LOW' | 'MEDIUM' | 'HIGH'
  impact: 'LOW' | 'MEDIUM' | 'HIGH'
  count: number
  riskIds: string[]
}

// 实施计划
export interface ImplementationPlan {
  phases: ImplementationPhase[]
  milestones: Milestone[]
  dependencies: Dependency[]
  resourceRequirements: ResourceRequirement[]
  successCriteria: string[]
  monitoringPlan: MonitoringPlan
}

// 实施阶段
export interface ImplementationPhase {
  phaseId: string
  phaseName: string
  phaseDescription: string
  startDate: string
  endDate: string
  deliverables: string[]
  responsiblePerson: string
  status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'DELAYED'
  completionPercentage: number
}

// 里程碑
export interface Milestone {
  milestoneId: string
  milestoneName: string
  milestoneDate: string
  description: string
  criteria: string[]
  status: 'PENDING' | 'ACHIEVED' | 'MISSED'
  actualDate?: string
}

// 依赖项
export interface Dependency {
  dependencyId: string
  dependencyType: 'INTERNAL' | 'EXTERNAL'
  description: string
  owner: string
  requiredByDate: string
  status: 'PENDING' | 'RESOLVED' | 'BLOCKED'
  impact: 'LOW' | 'MEDIUM' | 'HIGH'
}

// 资源需求
export interface ResourceRequirement {
  resourceType: 'HUMAN' | 'FINANCIAL' | 'TECHNICAL' | 'PHYSICAL'
  resourceDescription: string
  quantity: number
  unit: string
  estimatedCost: number
  availabilityStatus: 'AVAILABLE' | 'PARTIAL' | 'NOT_AVAILABLE'
  allocationPlan: string
}

// 监控计划
export interface MonitoringPlan {
  monitoringFrequency: 'DAILY' | 'WEEKLY' | 'MONTHLY'
  keyPerformanceIndicators: KPI[]
  reportingSchedule: string
  escalationMatrix: EscalationItem[]
}

// 关键绩效指标
export interface KPI {
  kpiId: string
  kpiName: string
  kpiDescription: string
  targetValue: number
  currentValue: number
  unit: string
  measurementMethod: string
  updateFrequency: string
}

// 升级项
export interface EscalationItem {
  level: number
  condition: string
  escalateTo: string
  timeframe: string
  actionRequired: string
}

// 审批流程实例
export interface ApprovalWorkflowInstance {
  instanceId: string
  requestId: number
  workflowDefinitionId: string
  currentStage: string
  currentApprovers: string[]
  startTime: string
  endTime?: string
  status: 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'SUSPENDED'
  history: ApprovalHistory[]
}

// 审批历史
export interface ApprovalHistory {
  historyId: string
  stage: string
  approver: string
  approverName: string
  action: 'APPROVE' | 'REJECT' | 'RETURN' | 'DELEGATE'
  actionTime: string
  comments?: string
  attachments?: string[]
  delegateTo?: string
  duration: number
}
