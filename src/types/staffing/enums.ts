/**
 * 编制管理相关枚举定义
 */

// 岗位类别枚举
export enum PositionCategory {
  MANAGEMENT = 'MANAGEMENT', // 管理岗
  PROFESSIONAL = 'PROFESSIONAL', // 专业技术岗
  SUPPORT = 'SUPPORT' // 工勤岗
}

// 编制规划状态枚举
export enum StaffingPlanStatus {
  DRAFT = 'DRAFT', // 草稿
  PENDING_APPROVAL = 'PENDING_APPROVAL', // 待审批
  APPROVED = 'APPROVED', // 已批准
  REJECTED = 'REJECTED', // 已驳回
  ACTIVE = 'ACTIVE', // 生效中
  EXPIRED = 'EXPIRED' // 已过期
}

// 预警类型
export enum StaffingAlertType {
  OVER_STAFFED = 'OVER_STAFFED', // 超编预警
  UNDER_STAFFED = 'UNDER_STAFFED', // 缺编预警
  LOW_UTILIZATION = 'LOW_UTILIZATION', // 低利用率预警
  HIGH_UTILIZATION = 'HIGH_UTILIZATION', // 高利用率预警
  BUDGET_EXCEEDED = 'BUDGET_EXCEEDED', // 预算超支预警
  COST_ANOMALY = 'COST_ANOMALY' // 成本异常预警
}

// 通知方式
export enum NotificationMethod {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  SYSTEM = 'SYSTEM',
  WECHAT = 'WECHAT',
  DINGTALK = 'DINGTALK'
}

// 预警级别
export enum AlertLevel {
  INFO = 'INFO', // 信息
  WARNING = 'WARNING', // 警告
  ERROR = 'ERROR', // 错误
  CRITICAL = 'CRITICAL' // 严重
}

// 预警状态
export enum AlertStatus {
  ACTIVE = 'ACTIVE', // 活动
  ACKNOWLEDGED = 'ACKNOWLEDGED', // 已确认
  RESOLVED = 'RESOLVED', // 已解决
  IGNORED = 'IGNORED', // 已忽略
  ESCALATED = 'ESCALATED' // 已升级
}

// 预警操作
export enum AlertAction {
  ACKNOWLEDGE = 'ACKNOWLEDGE', // 确认
  RESOLVE = 'RESOLVE', // 解决
  IGNORE = 'IGNORE', // 忽略
  ESCALATE = 'ESCALATE', // 升级
  ASSIGN = 'ASSIGN', // 分配
  COMMENT = 'COMMENT' // 评论
}

// 处理结果
export enum ProcessingResult {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  PENDING = 'PENDING'
}

// 调整申请类型
export enum AdjustmentRequestType {
  INCREASE = 'INCREASE', // 编制增加
  DECREASE = 'DECREASE', // 编制减少
  REDISTRIBUTE = 'REDISTRIBUTE', // 重新分配
  RESTRUCTURE = 'RESTRUCTURE', // 结构调整
  TEMPORARY = 'TEMPORARY' // 临时调整
}

// 紧急程度
export enum UrgencyLevel {
  LOW = 'LOW', // 低
  MEDIUM = 'MEDIUM', // 中
  HIGH = 'HIGH', // 高
  URGENT = 'URGENT' // 紧急
}

// 审批状态
export enum ApprovalStatus {
  DRAFT = 'DRAFT', // 草稿
  SUBMITTED = 'SUBMITTED', // 已提交
  IN_REVIEW = 'IN_REVIEW', // 审核中
  APPROVED = 'APPROVED', // 已批准
  REJECTED = 'REJECTED', // 已拒绝
  WITHDRAWN = 'WITHDRAWN', // 已撤回
  IMPLEMENTED = 'IMPLEMENTED', // 已实施
  CANCELLED = 'CANCELLED' // 已取消
}

// 预测模型类型
export enum ForecastModelType {
  LINEAR_REGRESSION = 'LINEAR_REGRESSION',
  TIME_SERIES = 'TIME_SERIES',
  ARIMA = 'ARIMA',
  EXPONENTIAL_SMOOTHING = 'EXPONENTIAL_SMOOTHING',
  NEURAL_NETWORK = 'NEURAL_NETWORK',
  ENSEMBLE = 'ENSEMBLE'
}

// 报告格式
export enum ReportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  WORD = 'WORD',
  HTML = 'HTML',
  JSON = 'JSON'
}

// 控制状态
export enum ControlStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// 控制动作结果
export enum ControlActionResult {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  PARTIAL_SUCCESS = 'PARTIAL_SUCCESS',
  PENDING = 'PENDING'
}

// 部门类型
export enum DepartmentType {
  HEADQUARTERS = 'HEADQUARTERS', // 总部
  BRANCH = 'BRANCH', // 分支机构
  SUBSIDIARY = 'SUBSIDIARY', // 子公司
  DEPARTMENT = 'DEPARTMENT', // 部门
  TEAM = 'TEAM' // 团队
}
