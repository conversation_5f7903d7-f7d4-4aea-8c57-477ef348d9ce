# 编制管理类型定义模块

## 概述

本目录包含了人力资源编制管理系统的所有TypeScript类型定义。原`staffing.ts`文件（2,231行）已拆分为8个功能模块，共包含17个枚举和164个接口定义。

## 模块结构

```
staffing/
├── index.ts          # 主索引文件，导出所有类型
├── enums.ts         # 枚举定义（17个）
├── constants.ts     # 常量和配置选项
├── plan.ts          # 编制规划相关类型（15个接口）
├── statistics.ts    # 统计分析相关类型（28个接口）
├── alert.ts         # 预警相关类型（23个接口）
├── approval.ts      # 审批流程相关类型（21个接口）
├── model.ts         # 预测和优化模型相关类型（27个接口）
├── common.ts        # 通用类型（20个接口）
└── README.md        # 本文档
```

## 模块说明

### 1. enums.ts - 枚举定义

包含所有编制管理相关的枚举类型：

- `PositionCategory` - 岗位类别
- `StaffingPlanStatus` - 编制规划状态
- `StaffingAlertType` - 预警类型
- `AlertLevel` - 预警级别
- `ApprovalStatus` - 审批状态
- 等等...

### 2. constants.ts - 常量配置

提供各种常量和配置选项：

- 下拉选项配置（如`positionCategoryOptions`）
- 状态颜色映射
- 默认配置值
- 图表颜色配置
- 导出配置

### 3. plan.ts - 编制规划

编制规划核心功能相关类型：

- `StaffingPlan` - 编制规划主体
- 创建、更新、查询请求类型
- 批量操作相关类型
- 模板和历史记录

### 4. statistics.ts - 统计分析

数据统计和分析相关类型：

- `StaffingStatistics` - 统计数据
- `StaffingTrend` - 趋势数据
- `StaffingAnalysisReport` - 分析报告
- 预测和效率分析类型

### 5. alert.ts - 预警管理

预警系统相关类型：

- `StaffingAlertConfig` - 预警配置
- `StaffingAlertRecord` - 预警记录
- 预警统计和处理类型
- 规则引擎配置

### 6. approval.ts - 审批流程

编制调整审批相关类型：

- `StaffingAdjustmentRequest` - 调整申请
- `CostImpactAnalysis` - 成本影响分析
- `RiskAssessment` - 风险评估
- `ImplementationPlan` - 实施计划

### 7. model.ts - 预测模型

AI/ML模型相关类型：

- `StaffingOptimizationRecommendation` - 优化建议
- `ForecastModelConfig` - 预测模型配置
- `ScenarioAnalysis` - 场景分析
- `StaffingControlStrategy` - 控制策略

### 8. common.ts - 通用类型

跨模块使用的通用类型：

- `PageResponse<T>` - 分页响应
- `ApiResponse<T>` - API响应包装
- 文件上传、导入导出类型
- 审计日志、通知消息等

## 使用方式

### 导入所有类型

```typescript
import * as StaffingTypes from '@/types/staffing'

const plan: StaffingTypes.StaffingPlan = { ... }
```

### 从特定模块导入

```typescript
// 导入编制规划类型
import { StaffingPlan, StaffingPlanCreateRequest } from '@/types/staffing/plan'

// 导入枚举
import { PositionCategory, StaffingAlertType } from '@/types/staffing/enums'

// 导入常量
import { positionCategoryOptions, CHART_COLORS } from '@/types/staffing/constants'
```

### 从主索引导入

```typescript
import { StaffingPlan, StaffingAlertType, positionCategoryOptions } from '@/types/staffing'
```

## 向后兼容

原`staffing.ts`文件仍然保留并重新导出所有类型，确保现有代码无需修改即可继续工作：

```typescript
// 旧的导入方式仍然有效
import { StaffingPlan } from '@/types/staffing.ts'
```

## 类型守卫

主索引文件提供了类型守卫函数：

```typescript
import { isStaffingPlan, isStaffingAlert } from '@/types/staffing'

if (isStaffingPlan(data)) {
  // data 被推断为 StaffingPlan 类型
}
```

## 最佳实践

1. **按需导入**：从具体模块导入所需类型，有助于tree-shaking
2. **类型复用**：使用通用类型避免重复定义
3. **枚举使用**：优先使用枚举而非字符串字面量
4. **常量配置**：UI相关的配置从constants.ts导入

## 版本信息

- 模块化版本：2.0.0
- 最后更新：2025-07-20
- 类型总数：181个（17个枚举 + 164个接口）

## 维护指南

1. 新增类型时，请添加到相应的模块文件中
2. 保持模块职责单一，避免循环依赖
3. 为复杂类型添加JSDoc注释
4. 更新类型时同步更新版本信息
