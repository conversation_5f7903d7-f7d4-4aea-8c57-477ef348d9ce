/**
 * 编制管理通用类型定义
 */

import { ReportFormat, ForecastModelType, DepartmentType } from './enums'
import type { SpringPageData, ApiResponse as BaseApiResponse } from '@/types/common/api'

// 重新导出通用类型，保持向后兼容
export type PageResponse<T> = SpringPageData<T>

// 编制文件上传响应
export interface StaffingFileUploadResponse {
  fileId: string
  fileName: string
  fileUrl: string
  fileSize: number
  uploadTime: string
}

// 编制数据导入请求
export interface StaffingImportRequest {
  fileId: string
  planYear: number
  overwriteExisting?: boolean
  validateOnly?: boolean
}

// 编制数据导入响应
export interface StaffingImportResponse {
  importId: string
  totalRecords: number
  successRecords: number
  failedRecords: number
  errors?: ImportError[]
  warnings?: ImportWarning[]
}

// 导入错误
export interface ImportError {
  row: number
  field: string

  value: unknown
  message: string
}

// 导入警告
export interface ImportWarning {
  row: number
  field: string

  value: unknown
  message: string
}

// 组织架构信息
export interface OrganizationInfo {
  organizationId: number
  organizationCode: string
  organizationName: string
  parentOrganizationId?: number
  organizationType: DepartmentType
  level: number
  path: string
  managerPosition?: string
  establishedDate?: string
  description?: string
  active: boolean
  childCount: number
  employeeCount: number
  positionCount: number
}

// 职位信息
export interface PositionInfo {
  positionId: number
  positionCode: string
  positionName: string
  positionLevel: string
  positionCategory: string
  organizationId: number
  organizationName: string
  jobFamily?: string
  jobGrade?: string
  reportToPosition?: string
  responsibilities: string[]
  qualifications: string[]
  competencies: string[]
  headcount: number
  filledCount: number
  vacantCount: number
}

// 员工信息（简化版）
export interface EmployeeBasicInfo {
  employeeId: string
  employeeCode: string
  employeeName: string
  positionId: number
  positionName: string
  organizationId: number
  organizationName: string
  hireDate: string
  employmentType: string
  status: string
}

// 报告生成请求
export interface ReportGenerationRequest {
  reportType: string
  reportName: string
  parameters: Record<string, unknown>
  format: ReportFormat
  templateId?: string
  recipients?: string[]
  scheduleTime?: string
}

// 报告生成响应
export interface ReportGenerationResponse {
  reportId: string
  reportName: string
  generateTime: string
  fileUrl: string
  fileSize: number
  expiryTime: string
  status: 'SUCCESS' | 'FAILED' | 'PROCESSING'
  errorMessage?: string
}

// 审计日志
export interface AuditLog {
  logId: string
  entityType: string
  entityId: string
  operationType: string
  operationTime: string
  operatorId: string
  operatorName: string
  operatorIp: string
  beforeData?: string
  afterData?: string
  changeDescription?: string
  module: string
  subModule?: string
  result: 'SUCCESS' | 'FAILED'
  errorMessage?: string
}

// 系统配置
export interface SystemConfiguration {
  configId: string
  configCategory: string
  configKey: string
  configValue: string
  configType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'JSON'
  description?: string
  editable: boolean
  effectiveTime?: string
  expiryTime?: string
  createTime: string
  updateTime: string
}

// 数据字典
export interface DataDictionary {
  dictId: string
  dictType: string
  dictCode: string
  dictName: string
  dictValue: string
  parentCode?: string
  sortOrder: number
  status: 'ACTIVE' | 'INACTIVE'
  description?: string
  extendProperties?: Record<string, unknown>
}

// 通知消息
export interface NotificationMessage {
  messageId: string
  messageType: string
  title: string
  content: string
  sender: string
  recipients: string[]
  sendTime: string
  priority: 'HIGH' | 'MEDIUM' | 'LOW'
  tags?: string[]
  attachments?: NotificationAttachment[]
  readStatus: Record<string, boolean>
  actionRequired: boolean
  actionUrl?: string
  expiryTime?: string
}

// 通知附件
export interface NotificationAttachment {
  attachmentId: string
  fileName: string
  fileSize: number
  fileUrl: string
  fileType: string
}

// 定时任务配置
export interface ScheduledTaskConfig {
  taskId: string
  taskName: string
  taskType: string
  cronExpression: string
  enabled: boolean
  description?: string
  lastExecutionTime?: string
  nextExecutionTime?: string
  executionStatus?: 'SUCCESS' | 'FAILED' | 'RUNNING'
  parameters?: Record<string, unknown>
  notifyOnFailure: boolean
  notificationRecipients?: string[]
}

// 为了兼容性，创建扩展的ApiResponse类型
export interface ApiResponse<T> extends BaseApiResponse<T> {
  success?: boolean
  traceId?: string
}

// 批量操作结果
export interface BatchOperationResult<T> {
  totalCount: number
  successCount: number
  failedCount: number
  results: Array<{
    item: T
    success: boolean
    message?: string

    error?: unknown
  }>
}

// 导出任务
export interface ExportTask {
  taskId: string
  taskName: string
  exportType: string
  parameters: Record<string, unknown>
  format: 'EXCEL' | 'CSV' | 'PDF'
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  progress: number
  totalRecords?: number
  processedRecords?: number
  fileUrl?: string
  fileSize?: number
  startTime: string
  endTime?: string
  errorMessage?: string
  createdBy: string
}
