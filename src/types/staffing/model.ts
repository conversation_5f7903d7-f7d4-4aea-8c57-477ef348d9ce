/**
 * 编制预测和优化模型相关类型定义
 */

import { ForecastModelType, ControlStatus, ControlActionResult } from './enums'

// 编制优化建议
export interface StaffingOptimizationRecommendation {
  recommendationId: string
  organizationId: string
  organizationName: string
  recommendationType: 'RESTRUCTURE' | 'REBALANCE' | 'REDUCE' | 'EXPAND' | 'MERGE'
  priority: 'HIGH' | 'MEDIUM' | 'LOW'
  description: string
  rationale: string
  currentState: OptimizationState
  targetState: OptimizationState
  estimatedBenefit: BenefitEstimation
  implementationSteps: ImplementationStep[]
  constraints: OptimizationConstraint[]
  risks: OptimizationRisk[]
  confidence: number
  generatedDate: string
  validUntil: string
}

// 优化状态
export interface OptimizationState {
  headcount: number
  cost: number
  efficiency: number
  utilizationRate: number
  structureDescription: string
}

// 效益估算
export interface BenefitEstimation {
  costSaving: number
  efficiencyGain: number
  utilizationImprovement: number
  timeToRealize: string
  breakEvenPoint: string
}

// 实施步骤
export interface ImplementationStep {
  stepNumber: number
  description: string
  duration: string
  dependencies: string[]
  resources: string[]
  milestone: string
}

// 优化约束
export interface OptimizationConstraint {
  constraintType: 'BUDGET' | 'HEADCOUNT' | 'SKILL' | 'LOCATION' | 'TIME'
  description: string

  value: unknown
  flexible: boolean
}

// 优化风险
export interface OptimizationRisk {
  riskType: string
  description: string
  probability: 'LOW' | 'MEDIUM' | 'HIGH'
  impact: 'LOW' | 'MEDIUM' | 'HIGH'
  mitigation: string
}

// 预测模型配置
export interface ForecastModelConfig {
  modelId: string
  modelName: string
  modelType: ForecastModelType
  description: string
  parameters: ModelParameter[]
  trainingDataRequirements: DataRequirement[]
  performanceMetrics: ModelPerformance
  lastTrainedDate: string
  version: string
  active: boolean
}

// 模型参数
export interface ModelParameter {
  parameterName: string
  parameterType: 'NUMBER' | 'STRING' | 'BOOLEAN' | 'ARRAY'

  defaultValue: unknown

  currentValue: unknown
  description: string
  constraints?: ParameterConstraint[]
}

// 参数约束
export interface ParameterConstraint {
  constraintType: 'MIN' | 'MAX' | 'PATTERN' | 'OPTIONS'

  constraintValue: unknown
}

// 数据需求
export interface DataRequirement {
  dataType: string
  minimumRecords: number
  timeRange: string
  quality: 'HIGH' | 'MEDIUM' | 'LOW'
  mandatory: boolean
}

// 模型性能
export interface ModelPerformance {
  accuracy: number
  precision: number
  recall: number
  f1Score: number
  mse?: number
  rmse?: number
  mae?: number
  r2Score?: number
  evaluationDate: string
}

// 场景分析请求
export interface ScenarioAnalysisRequest {
  scenarioName: string
  baselineData: BaselineData
  scenarios: ScenarioDefinition[]
  analysisType: 'WHAT_IF' | 'SENSITIVITY' | 'MONTE_CARLO'
  outputMetrics: string[]
  constraints?: ScenarioConstraint[]
}

// 基线数据
export interface BaselineData {
  organizationId: string
  referenceDate: string
  currentHeadcount: number
  currentCost: number
  currentUtilization: number
  otherMetrics?: Record<string, unknown>
}

// 场景定义
export interface ScenarioDefinition {
  scenarioId: string
  scenarioName: string
  description: string
  variables: ScenarioVariable[]
  assumptions: string[]
  probability?: number
}

// 场景变量
export interface ScenarioVariable {
  variableName: string
  variableType: 'ABSOLUTE' | 'PERCENTAGE' | 'DELTA'
  baseValue: number
  scenarioValue: number
  unit: string
}

// 场景约束
export interface ScenarioConstraint {
  constraintName: string
  constraintType: 'BUDGET' | 'HEADCOUNT' | 'RATIO' | 'CUSTOM'
  operator: 'EQ' | 'LT' | 'LE' | 'GT' | 'GE' | 'BETWEEN'

  value: unknown
  priority: number
}

// 场景分析结果
export interface ScenarioAnalysisResult {
  analysisId: string
  executionTime: string
  baselineScenario: ScenarioResult
  alternativeScenarios: ScenarioResult[]
  comparison: ScenarioComparison[]
  insights: ScenarioInsight[]
  recommendations: string[]
}

// 场景结果
export interface ScenarioResult {
  scenarioId: string
  scenarioName: string
  metrics: Record<string, unknown>
  feasibility: boolean
  constraintViolations: string[]
  confidence: number
}

// 场景对比
export interface ScenarioComparison {
  metric: string
  baseline: number
  scenarios: Record<string, number>
  bestScenario: string
  worstScenario: string
}

// 场景洞察
export interface ScenarioInsight {
  insightType: 'RISK' | 'OPPORTUNITY' | 'TRADEOFF' | 'SENSITIVITY'
  description: string
  affectedScenarios: string[]
  significance: 'HIGH' | 'MEDIUM' | 'LOW'
  actionableRecommendation: string
}

// 编制控制策略
export interface StaffingControlStrategy {
  strategyId: string
  strategyName: string
  targetOrganizations: string[]
  controlType: 'FREEZE' | 'CAP' | 'RATIO' | 'BUDGET'
  parameters: ControlParameter[]
  effectiveDate: string
  expiryDate?: string
  status: ControlStatus
  approvalRequired: boolean
  exceptions: ControlException[]
  monitoring: ControlMonitoring
}

// 控制参数
export interface ControlParameter {
  parameterName: string

  parameterValue: unknown
  unit: string
  tolerance?: number
}

// 控制例外
export interface ControlException {
  exceptionType: string
  condition: string
  approver: string
  maxDuration?: string
  documentation: string
}

// 控制监控
export interface ControlMonitoring {
  checkFrequency: 'REALTIME' | 'DAILY' | 'WEEKLY' | 'MONTHLY'
  alertThreshold: number
  escalationPath: string[]
  reportingSchedule: string
}

// 控制执行结果
export interface ControlExecutionResult {
  executionId: string
  strategyId: string
  executionTime: string
  affectedOrganizations: string[]
  totalChecks: number
  violations: ControlViolation[]
  actions: ControlAction[]
  overallResult: ControlActionResult
}

// 控制违规
export interface ControlViolation {
  violationId: string
  organizationId: string
  violationType: string

  currentValue: unknown

  limitValue: unknown
  severity: 'HIGH' | 'MEDIUM' | 'LOW'
  description: string
}

// 控制动作
export interface ControlAction {
  actionId: string
  actionType: 'BLOCK' | 'ALERT' | 'APPROVE' | 'ROLLBACK'
  targetEntity: string
  actionTime: string
  result: ControlActionResult
  message?: string
}

// 智能编制优化建议
export interface IntelligentOptimization {
  optimizationId: string
  organizationId: number
  organizationName: string
  optimizationStrategy: string
  primaryObjective: string
  timeHorizon: string
  budgetConstraint: number
  riskTolerance: number
  optimizationDimensions: string[]
  constraints: string[]

  // AI 分析结果
  aiScore: number
  expectedCostSaving: number
  efficiencyImprovement: number
  roi: number

  // 优化方案
  optimizationPlans: OptimizationPlan[]

  // 成本效益分析
  costBenefitAnalysis: CostBenefitAnalysis

  // 实施路径
  implementationPath: ImplementationPath

  // 风险评估
  riskAssessment: RiskAssessment

  // 敏感性分析
  sensitivityAnalysis: SensitivityAnalysis

  // 监控仪表板
  monitoringDashboard: MonitoringDashboard

  // AI 洞察
  aiInsights: AIInsight[]

  // 智能建议
  intelligentRecommendations: IntelligentRecommendation[]

  generatedAt: string
  validUntil: string
}

// 优化配置
export interface OptimizationConfig {
  organizationId?: number
  optimizationStrategy: string
  primaryObjective: string
  timeHorizon: string
  budgetConstraint: number
  riskTolerance: number
  optimizationDimensions: string[]
  constraints: string[]
}

// 优化方案
export interface OptimizationPlan {
  planId: string
  planName: string
  description: string
  priority: 'HIGH' | 'MEDIUM' | 'LOW'
  estimatedCost: number
  estimatedBenefit: number
  implementationTime: string
  feasibility: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
}

// 成本效益分析
export interface CostBenefitAnalysis {
  totalCost: number
  totalBenefit: number
  netBenefit: number
  roi: number
  paybackPeriod: string
  breakEvenPoint: string
  costBreakdown: CostBreakdownItem[]
  benefitBreakdown: BenefitBreakdownItem[]
}

// 成本分解项
export interface CostBreakdownItem {
  category: string
  amount: number
  percentage: number
  description: string
}

// 效益分解项
export interface BenefitBreakdownItem {
  category: string
  amount: number
  percentage: number
  description: string
}

// 实施路径
export interface ImplementationPath {
  phases: ImplementationPhase[]
  totalDuration: string
  criticalPath: string[]
  dependencies: Dependency[]
  milestones: Milestone[]
}

// 实施阶段
export interface ImplementationPhase {
  phaseId: string
  phaseName: string
  description: string
  duration: string
  startDate: string
  endDate: string
  tasks: Task[]
  resources: Resource[]
}

// 任务
export interface Task {
  taskId: string
  taskName: string
  description: string
  duration: string
  dependencies: string[]
  assignee: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'BLOCKED'
}

// 资源
export interface Resource {
  resourceId: string
  resourceName: string
  resourceType: 'HUMAN' | 'FINANCIAL' | 'TECHNICAL' | 'MATERIAL'
  quantity: number
  unit: string
  cost: number
}

// 依赖关系
export interface Dependency {
  dependencyId: string
  fromTask: string
  toTask: string
  dependencyType: 'FINISH_TO_START' | 'START_TO_START' | 'FINISH_TO_FINISH' | 'START_TO_FINISH'
  lag: number
}

// 里程碑
export interface Milestone {
  milestoneId: string
  milestoneName: string
  description: string
  targetDate: string
  criteria: string[]
  status: 'PENDING' | 'ACHIEVED' | 'MISSED'
}

// 风险评估
export interface RiskAssessment {
  overallRiskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  riskFactors: RiskFactor[]
  mitigationStrategies: MitigationStrategy[]
  contingencyPlans: ContingencyPlan[]
}

// 风险因素
export interface RiskFactor {
  riskId: string
  riskName: string
  description: string
  category: string
  probability: number
  impact: number
  riskScore: number
  status: 'IDENTIFIED' | 'ASSESSED' | 'MITIGATED' | 'ACCEPTED'
}

// 缓解策略
export interface MitigationStrategy {
  strategyId: string
  strategyName: string
  description: string
  targetRisks: string[]
  cost: number
  effectiveness: number
  timeline: string
}

// 应急计划
export interface ContingencyPlan {
  planId: string
  planName: string
  description: string
  triggerConditions: string[]
  actions: string[]
  resources: Resource[]
  timeline: string
}

// 敏感性分析
export interface SensitivityAnalysis {
  baselineScenario: Scenario
  scenarios: Scenario[]
  sensitivityFactors: SensitivityFactor[]
  results: SensitivityResult[]
}

// 场景
export interface Scenario {
  scenarioId: string
  scenarioName: string
  description: string
  parameters: ScenarioParameter[]
  results: ScenarioResults
}

// 场景参数
export interface ScenarioParameter {
  parameterName: string
  baseValue: number
  scenarioValue: number
  changePercentage: number
}

// 场景结果
export interface ScenarioResults {
  cost: number
  benefit: number
  roi: number
  paybackPeriod: string
  feasibility: number
}

// 敏感性因素
export interface SensitivityFactor {
  factorName: string
  impactLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  elasticity: number
  description: string
}

// 敏感性结果
export interface SensitivityResult {
  factorName: string
  changeRange: number[]
  impactRange: number[]
  criticalThreshold: number
}

// 监控仪表板
export interface MonitoringDashboard {
  kpis: KPI[]
  alerts: MonitoringAlert[]
  reports: MonitoringReport[]
  dashboardConfig: DashboardConfig
}

// 关键绩效指标
export interface KPI {
  kpiId: string
  kpiName: string
  description: string
  currentValue: number
  targetValue: number
  unit: string
  trend: 'UP' | 'DOWN' | 'STABLE'
  status: 'ON_TRACK' | 'AT_RISK' | 'OFF_TRACK'
}

// 监控警报
export interface MonitoringAlert {
  alertId: string
  alertName: string
  description: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  status: 'ACTIVE' | 'RESOLVED' | 'ACKNOWLEDGED'
  triggerCondition: string
  createdAt: string
}

// 监控报告
export interface MonitoringReport {
  reportId: string
  reportName: string
  description: string
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
  lastGenerated: string
  nextScheduled: string
  recipients: string[]
}

// 仪表板配置
export interface DashboardConfig {
  layout: DashboardLayout[]
  refreshInterval: number
  autoRefresh: boolean
  theme: 'LIGHT' | 'DARK'
  customizations: Record<string, unknown>
}

// 仪表板布局
export interface DashboardLayout {
  widgetId: string
  widgetType: string
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  config: Record<string, unknown>
}

// AI 洞察
export interface AIInsight {
  insightId: string
  insightType: 'OPPORTUNITY' | 'RISK' | 'TREND' | 'ANOMALY'
  title: string
  description: string
  confidence: number
  impact: 'LOW' | 'MEDIUM' | 'HIGH'
  actionable: boolean
  relatedData: Record<string, unknown>
  generatedAt: string
}

// 智能建议
export interface IntelligentRecommendation {
  recommendationId: string
  recommendationType: 'OPTIMIZATION' | 'COST_REDUCTION' | 'EFFICIENCY' | 'RISK_MITIGATION'
  title: string
  description: string
  rationale: string
  priority: 'HIGH' | 'MEDIUM' | 'LOW'
  estimatedImpact: number
  implementationEffort: 'LOW' | 'MEDIUM' | 'HIGH'
  timeline: string
  prerequisites: string[]
  expectedOutcomes: string[]
  confidence: number
  generatedAt: string
}
