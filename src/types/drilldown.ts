// 数据钻取相关类型定义

// 钻取上下文
export interface DrillDownContext {
  path: DrillDownPath[]
  currentLevel: number
  maxLevel: number
  canDrillDown: boolean
  canDrillUp: boolean
}

// 钻取路径节点
export interface DrillDownPath {
  id: string
  level: number
  dimension: string

  value: unknown
  label: string
  filters: Record<string, unknown>

  metadata?: unknown
  timestamp?: number
}

// 钻取配置
export interface DrillDownConfig {
  dimensions: DrillDownDimension[]
  maxDepth: number
  enableBreadcrumb: boolean
  enableHistory: boolean
  enableCache?: boolean
  cacheTimeout?: number
  permissions?: DrillDownPermission[]
  onDrillDown?: (context: DrillDownContext) => void
  onDrillUp?: (context: DrillDownContext) => void
  onReset?: () => void
}

// 钻取维度定义
export interface DrillDownDimension {
  key: string
  name: string
  levels: DrillDownLevel[]
  type: 'hierarchy' | 'time' | 'category'

  defaultFormatter?: (value: unknown) => string
}

// 钻取层级定义
export interface DrillDownLevel {
  id: string
  name: string
  field: string
  component?: string

  formatter?: (value: unknown) => string

  filter?: (item: unknown) => boolean
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max'
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 钻取参数
export interface DrillDownParams {
  dimension: string

  value: unknown
  label: string
  filters?: Record<string, unknown>

  metadata?: unknown
}

// 钻取权限
export interface DrillDownPermission {
  role: string
  dimension: string
  maxLevel: number
  conditions?: PermissionCondition[]
}

// 权限条件
export interface PermissionCondition {
  field: string
  operator: 'eq' | 'ne' | 'in' | 'notIn' | 'gt' | 'gte' | 'lt' | 'lte'

  value: unknown
}

// 钻取事件
export interface DrillDownEvent {
  type: 'drill-down' | 'drill-up' | 'drill-reset' | 'drill-jump'
  path: DrillDownPath[]
  level: number
  dimension?: string
  timestamp: number
}

// 缓存条目
export interface DrillDownCacheEntry {
  data: unknown
  timestamp: number
  hits: number
}

// 历史记录
export interface DrillDownHistoryEntry {
  context: DrillDownContext
  timestamp: number
  action: 'drill-down' | 'drill-up' | 'reset' | 'jump'
}

// 面包屑项
export interface BreadcrumbItem {
  id: string
  label: string
  level: number
  dimension: string
  clickable: boolean
  icon?: string
}

// 钻取数据请求
export interface DrillDownDataRequest {
  dimension: string
  level: number
  filters: Record<string, unknown>
  page?: number
  pageSize?: number
  sort?: {
    field: string
    order: 'asc' | 'desc'
  }
}

// 钻取数据响应
export interface DrillDownDataResponse {
  data: unknown[]
  total: number
  level: number
  dimension: string
  aggregations?: Record<string, unknown>
}

// 时间维度钻取级别
export interface TimeDrillDownLevel {
  year?: number
  quarter?: number
  month?: number
  week?: number
  day?: number
}

// 层级维度钻取
export interface HierarchyDrillDownLevel {
  parentId?: string
  parentName?: string
  currentId: string
  currentName: string
  hasChildren: boolean
  childrenCount?: number
}

// 钻取状态
export interface DrillDownState {
  isLoading: boolean
  error: string | null

  currentData: unknown[]
  totalCount: number
  aggregations: Record<string, unknown>
}

// 钻取导航操作
export interface DrillDownNavigation {
  canGoBack: boolean
  canGoForward: boolean
  historyLength: number
  currentIndex: number
}

// 钻取管理器接口
export interface IDrillDownManager {
  // 基础操作
  drillDown(params: DrillDownParams): Promise<void>
  drillUp(levels?: number): Promise<void>
  drillTo(path: DrillDownPath[]): Promise<void>
  reset(): void

  // 导航操作
  goBack(): void
  goForward(): void
  jumpTo(index: number): void

  // 状态查询
  getCurrentContext(): DrillDownContext
  getBreadcrumb(): BreadcrumbItem[]
  getHistory(): DrillDownHistoryEntry[]
  canDrillDown(): boolean
  canDrillUp(): boolean

  // 数据操作

  getCurrentData(): unknown[]
  refreshCurrentLevel(): Promise<void>

  // 事件订阅
  on(event: string, handler: Function): void
  off(event: string, handler: Function): void
}

// 钻取服务接口
export interface IDrillDownService {
  fetchDrillDownData(request: DrillDownDataRequest): Promise<DrillDownDataResponse>
  validatePermission(params: DrillDownParams): Promise<boolean>
  getDimensionConfig(dimension: string): Promise<DrillDownDimension>
}
