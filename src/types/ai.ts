/**
 * AI分析相关类型定义
 */

// AI分析配置
export interface AIAnalysisConfig {
  models: ModelConfig[]
  features: FeatureConfig[]
  thresholds?: Record<string, number>
  nlpEnabled?: boolean
  voiceEnabled?: boolean
  useWebWorker?: boolean
  warmUp?: boolean
}

// 模型配置
export interface ModelConfig {
  id: string
  name: string
  type: 'classification' | 'regression' | 'clustering' | 'anomaly'
  path: string
  version: string
  inputShape: number[]
  outputShape: number[]

  metadata?: unknown
}

// 特征配置
export interface FeatureConfig {
  name: string
  type: 'numeric' | 'categorical' | 'text' | 'date'
  importance: number
  preprocessing?: 'normalize' | 'standardize' | 'encode'
}

// 预测输入
export interface PredictionInput {
  [key: string]: unknown
}

// 预测结果
export interface PredictionResult {
  id: string
  type: string
  value?: number | string

  predictions?: unknown[]
  confidence: number

  explanation?: unknown
  factors?: InfluenceFactor[]
  timestamp: number
  // 趋势预测特有
  peak?: number
  average?: number
  growthRate?: number
}

// 影响因素
export interface InfluenceFactor {
  name: string

  value: unknown
  impact: number
  direction: 'positive' | 'negative' | 'neutral'
}

// 异常检测结果
export interface AnomalyResult {
  id: string
  type: string
  severity: 'low' | 'medium' | 'high'
  score: number
  description: string

  affectedData: unknown[]
  suggestedAction?: string
  timestamp: number
}

// 趋势预测选项
export interface TrendPredictionOptions {
  period: number // 预测周期（月）
  algorithm?: 'arima' | 'prophet' | 'lstm' | 'rf' | 'xgboost'
  granularity?: 'day' | 'week' | 'month' | 'quarter'
  features?: string[]
  confidenceLevel?: number
  includeHolidays?: boolean
}

// 离职预测输入
export interface TurnoverPredictionInput {
  employeeId: string
  performance: number[] // 历史绩效
  satisfaction: number // 满意度
  salary: number // 薪资水平
  workYears: number // 工作年限
  promotions: number // 晋升次数
  trainingHours: number // 培训时长
}

// 离职预测输出
export interface TurnoverPredictionOutput {
  probability: number // 离职概率
  riskLevel: 'low' | 'medium' | 'high'
  keyFactors: string[] // 关键因素
  suggestions?: string[] // 建议措施
}

// 绩效预测输入
export interface PerformancePredictionInput {
  employeeId: string
  historicalScores: number[]
  skillsAssessment: number[]
  projectCompletion: number
  teamCollaboration: number
  learningProgress: number
}

// 绩效预测输出
export interface PerformancePredictionOutput {
  predictedScore: number
  trend: 'improving' | 'stable' | 'declining'
  strengths: string[]
  improvements: string[]
}

// 成本预测输入
export interface CostPredictionInput {
  departmentId: string
  historicalCosts: number[]
  headcount: number
  averageSalary: number
  benefitsRatio: number
  turnoverRate: number
}

// 成本预测输出
export interface CostPredictionOutput {
  predictedCost: number
  breakdown: {
    salary: number
    benefits: number
    recruitment: number
    training: number
    other: number
  }
  optimization: string[]
}

// 自然语言查询
export interface NLPQuery {
  query: string

  context?: unknown
  language?: 'zh' | 'en'
}

// 查询结果
export interface QueryResult {
  intent: string
  entities: Record<string, unknown>
  response: string

  data?: unknown
  visualization?: string
}

// 性能指标
export interface PerformanceMetrics {
  inferenceTime: {
    average: number
    min: number
    max: number
  }
  accuracy: {
    overall: number
    byModel: Record<string, number>
  }
  usage: {
    totalPredictions: number
    totalAnomalies: number
    cacheHitRate: number
  }
}

// 模型解释结果
export interface ExplanationResult {
  features: Array<{
    name: string
    importance: number

    value: unknown
    contribution: number
  }>

  visualization?: unknown
  summary: string
}
