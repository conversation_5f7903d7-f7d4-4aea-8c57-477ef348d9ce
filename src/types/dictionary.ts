/**
 * 字典相关类型定义
 */

/**
 * 字典项
 */
export interface DictItem {
  /** 显示标签 */
  label: string
  /** 字典值 */
  value: string
  /** 排序顺序 */
  sortOrder?: number
  /** 颜色（用于状态类字典） */
  color?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 子项（用于树形字典） */
  children?: DictItem[]
  /** 扩展属性 */

  [key: string]: unknown
}

/**
 * 字典分组
 */
export interface DictGroup {
  /** 分组名称 */
  name: string
  /** 分组代码 */
  code: string
  /** 包含的字典类型 */
  types: string[]
}

/**
 * 字典类型
 */
export interface DictType {
  /** 类型ID */
  id: string
  /** 类型名称 */
  name: string
  /** 类型代码 */
  code: string
  /** 描述 */
  description?: string
  /** 是否系统内置 */
  isSystem?: boolean
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
}

/**
 * 字典管理查询参数
 */
export interface DictQueryParams {
  /** 类型代码 */
  typeCode?: string
  /** 类型名称 */
  typeName?: string
  /** 标签 */
  label?: string
  /** 状态 */
  status?: boolean
  /** 页码 */
  pageNum?: number
  /** 每页数量 */
  pageSize?: number
}

/**
 * 字典缓存信息
 */
export interface DictCacheInfo {
  /** 类型数量 */
  typeCount: number
  /** 项目数量 */
  itemCount: number
  /** 估算大小（字节） */
  estimatedSize: number
  /** 格式化大小 */
  formattedSize: string
}

/**
 * 常用字典类型枚举
 */
export enum CommonDictType {
  /** 性别 */
  GENDER = 'gender',
  /** 学历 */
  EDUCATION = 'education',
  /** 婚姻状况 */
  MARITAL_STATUS = 'maritalStatus',
  /** 员工状态 */
  EMPLOYEE_STATUS = 'employeeStatus',
  /** 合同类型 */
  CONTRACT_TYPE = 'contractType',
  /** 请假类型 */
  LEAVE_TYPE = 'leaveType',
  /** 审批状态 */
  APPROVAL_STATUS = 'approvalStatus',
  /** 部门类型 */
  DEPT_TYPE = 'deptType',
  /** 职位级别 */
  POSITION_LEVEL = 'positionLevel',
  /** 职称等级 */
  TITLE_LEVEL = 'titleLevel',
  /** 政治面貌 */
  POLITICAL_STATUS = 'politicalStatus',
  /** 民族 */
  ETHNICITY = 'ethnicity',
  /** 证件类型 */
  ID_TYPE = 'idType',
  /** 血型 */
  BLOOD_TYPE = 'bloodType',
  /** 户口类型 */
  HOUSEHOLD_TYPE = 'householdType'
}

/**
 * 字典值映射（用于类型安全的字典值）
 */
export interface DictValueMap {
  // 性别
  gender: '1' | '2' | '3'
  // 学历
  education: 'phd' | 'master' | 'bachelor' | 'college' | 'high_school' | 'middle_school'
  // 婚姻状况
  maritalStatus: 'single' | 'married' | 'divorced' | 'widowed'
  // 员工状态
  employeeStatus: 'active' | 'probation' | 'resigned' | 'retired'
  // 合同类型
  contractType: 'fixed' | 'permanent' | 'project' | 'intern' | 'service'
  // 请假类型
  leaveType: 'annual' | 'personal' | 'sick' | 'marriage' | 'maternity' | 'paternity' | 'bereavement'
  // 审批状态
  approvalStatus: 'pending' | 'processing' | 'approved' | 'rejected' | 'cancelled'
}
