// 招聘需求
export interface RecruitmentNeed {
  id: number
  departmentId: number
  departmentName: string
  positionId: number
  positionName: string
  needCount: number // 需求人数
  currentCount: number // 现有人数
  recruitType: string // 招聘类型：社会招聘/校园招聘/内部招聘
  urgencyLevel: string // 紧急程度：高/中/低
  reason: string // 招聘原因
  requirements: string // 任职要求
  jobDescription: string // 岗位职责
  salaryRange: string // 薪资范围
  expectedDate: string // 期望到岗日期
  status: string // 状态：草稿/待审批/已通过/已拒绝
  applyUserId: number
  applyUserName: string
  applyTime: string
  approveUserId?: number
  approveUserName?: string
  approveTime?: string
  approveComment?: string
  createTime: string
  updateTime: string
}

// 职位发布
export interface JobPost {
  id: number
  recruitmentNeedId?: number
  title: string // 职位标题
  departmentId: number
  departmentName: string
  positionId: number
  positionName: string
  jobType: string // 工作类型：全职/兼职/实习
  workLocation: string // 工作地点
  recruitCount: number // 招聘人数
  salaryMin?: number
  salaryMax?: number
  salaryUnit?: string // 薪资单位：月/年/时
  education: string // 学历要求
  experience: string // 经验要求
  jobDescription: string // 职位描述
  requirements: string // 任职要求
  benefits?: string // 福利待遇
  tags?: string[] // 标签
  isUrgent: boolean // 是否急聘
  isHot: boolean // 是否热门
  publishTime?: string // 发布时间
  expireTime?: string // 截止时间
  status: string // 状态：草稿/已发布/已下线/已关闭
  viewCount: number // 浏览次数
  applyCount: number // 申请人数
  createUserId: number
  createUserName: string
  createTime: string
  updateTime: string
}

// 简历信息
export interface Resume {
  id: number
  applicantId: number // 应聘者ID
  jobPostId?: number // 申请职位ID
  jobPostTitle?: string
  // 基本信息
  name: string
  gender: string
  birthDate: string
  phone: string
  email: string
  idCard: string
  currentLocation: string
  expectedLocation?: string
  photo?: string

  // 教育背景
  education: EducationInfo[]

  // 工作经历
  workExperience: WorkExperience[]

  // 项目经验
  projectExperience?: ProjectExperience[]

  // 技能特长
  skills?: string[]
  certificates?: Certificate[] // 证书
  languages?: Language[] // 语言能力

  // 求职意向
  expectedPosition?: string
  expectedSalaryMin?: number
  expectedSalaryMax?: number
  jobStatus?: string // 在职状态
  availableTime?: string // 到岗时间

  // 其他信息
  selfEvaluation?: string // 自我评价
  attachments?: Attachment[] // 附件

  // AI分析结果
  aiParsed?: boolean
  aiMatchScore?: number
  aiSummary?: string
  aiTags?: string[]

  // 状态信息
  source: string // 来源：网申/内推/猎头/校招
  status: string // 状态：待筛选/初筛通过/复筛通过/面试中/已录用/已拒绝
  currentStep?: string // 当前步骤
  score?: number // 综合评分
  comments?: Comment[] // 评价记录

  createTime: string
  updateTime: string
}

// 教育信息
export interface EducationInfo {
  school: string
  major: string
  degree: string
  startDate: string
  endDate: string
  isFullTime: boolean
  achievements?: string
}

// 工作经历
export interface WorkExperience {
  company: string
  position: string
  startDate: string
  endDate: string
  isCurrent: boolean
  description: string
  achievements?: string
}

// 项目经验
export interface ProjectExperience {
  name: string
  role: string
  startDate: string
  endDate: string
  description: string
  technologies?: string[]
  achievements?: string
}

// 证书
export interface Certificate {
  name: string
  issueOrg: string
  issueDate: string
  expiryDate?: string
}

// 语言能力
export interface Language {
  language: string
  proficiency: string // 精通/熟练/一般
}

// 附件
export interface Attachment {
  name: string
  url: string
  type: string
  size: number
}

// 评论
export interface Comment {
  userId: number
  userName: string
  content: string
  time: string
}

// 人才库
export interface TalentPool {
  id: number
  resumeId: number
  resume?: Resume
  category: string // 分类：技术类/管理类/营销类等
  level: string // 级别：高级/中级/初级
  tags: string[] // 标签
  source: string // 来源
  status: string // 状态：活跃/储备/已入职
  lastContactTime?: string // 最后联系时间
  nextContactTime?: string // 下次联系时间
  contactRecords?: ContactRecord[] // 联系记录
  notes?: string // 备注
  createUserId: number
  createUserName: string
  createTime: string
  updateTime: string
}

// 人才标签
export interface TalentTag {
  id: number
  name: string
  category: string // 技能类/性格类/行业类/资质类等
  color: string // 标签颜色
  description?: string // 标签描述
  useCount: number // 使用次数
  status: 'active' | 'inactive' // 状态
  createTime: string
  updateTime: string
}

// 标签分类
export interface TagCategory {
  category: string
  name: string // 分类显示名称
  count: number // 该分类下的标签数量
  tags: Array<{
    id: number
    name: string
    color: string
  }>
}

// 标签统计
export interface TagStatistics {
  totalTags: number
  activeTags: number
  topUsedTags: Array<{
    id: number
    name: string
    category: string
    useCount: number
    percentage: number
  }>
  categoryDistribution: Array<{
    category: string
    count: number
    percentage: number
  }>
  usageTrend: Array<{
    date: string
    count: number
  }>
}

// 标签推荐
export interface TagRecommendation {
  id: number
  name: string
  category: string
  score: number // 推荐分数 0-100
  reason: string // 推荐理由
}

// 标签云项
export interface TagCloudItem {
  text: string
  value: number // 权重值
  color: string
  category: string
}

// 匹配权重配置
export interface MatchWeights {
  skills: number
  experience: number
  education: number
  salary: number
  location: number
}

// 匹配详情
export interface MatchDetails {
  skills: {
    score: number
    matched: string[]
    missing: string[]
  }
  experience: {
    score: number
    years: number
    required: number
  }
  education: {
    score: number
    level: string
    required: string
  }
  salary: {
    score: number
    expected: number
    offered: number
  }
  location: {
    score: number
    distance: number
  }
}

// 人才匹配结果
export interface TalentMatchResult {
  talent: TalentPool
  matchScore: number
  matchDetails: MatchDetails
  matchReasons: string[]
  concerns: string[]
}

// 职位匹配结果
export interface PositionMatchResult {
  position: JobPosition
  matchScore: number
  matchReasons: string[]
  benefits: string[]
  challenges: string[]
}

// 匹配配置
export interface MatchConfig {
  defaultWeights: MatchWeights
  scoreThresholds: {
    excellent: number
    good: number
    acceptable: number
  }
  rules: Array<{
    id: number
    name: string
    condition: string
    action: string
    priority: number
    enabled: boolean
  }>
}

// 匹配历史记录
export interface MatchHistory {
  id: number
  matchType: string
  positionId: number
  positionName: string
  talentId: number
  talentName: string
  matchScore: number
  outcome?: string
  feedback?: string
  createTime: string
}

// 匹配优化建议
export interface MatchOptimizationSuggestion {
  type: 'requirement' | 'skill' | 'salary' | 'description'
  priority: 'high' | 'medium' | 'low'
  suggestion: string
  expectedImprovement: number
  implementation: string
}

// 跟踪计划
export interface TrackingPlan {
  id: number
  talentId: number
  talentName?: string
  planName: string
  trackingType: 'regular' | 'important' | 'potential'
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  objectives: string[]
  startDate: string
  endDate?: string
  progress: number
  lastTrackTime?: string
  nextTrackTime?: string
  status: 'active' | 'paused' | 'completed'
  reminders?: Array<{
    type: 'email' | 'system' | 'sms'
    advanceDays: number
  }>
  createTime: string
  updateTime: string
}

// 跟踪活动记录
export interface TrackingActivity {
  id: number
  planId: number
  planName: string
  talentId: number
  talentName: string
  trackingMethod: 'phone' | 'email' | 'meeting' | 'wechat' | 'other'
  content: string
  result: 'positive' | 'neutral' | 'negative'
  keyFindings: string[]
  nextAction?: string
  attachments?: string[]
  followUpRequired: boolean
  followUpDate?: string
  trackingUserId: number
  trackingUserName: string
  trackingTime: string
  trackingScore?: number
}

// 跟踪任务
export interface TrackingTask {
  id: number
  talentId: number
  talentName: string
  taskType: 'greeting' | 'birthday' | 'holiday' | 'career' | 'custom'
  taskName: string
  description?: string
  dueDate: string
  status: 'pending' | 'completed' | 'overdue'
  priority: 'high' | 'medium' | 'low'
  assigneeId: number
  assigneeName: string
  completedTime?: string
  completionNotes?: string
}

// 跟踪提醒
export interface TrackingReminder {
  id: number
  type: 'plan' | 'task' | 'follow-up'
  relatedId: number
  talentId: number
  talentName: string
  message: string
  dueDate: string
  priority: string
  acknowledged: boolean
}

// 跟踪模板
export interface TrackingTemplate {
  id: number
  type: string
  name: string
  content: string
  variables: string[]
  useCount: number
  createTime: string
  updateTime: string
}

// 评估维度
export interface EvaluationDimension {
  professional: {
    score: number
    details: {
      skills: number
      experience: number
      education: number
      certifications: number
      achievements: number
    }
  }
  potential: {
    score: number
    details: {
      learningAbility: number
      adaptability: number
      leadership: number
      innovation: number
      stability: number
    }
  }
  market: {
    score: number
    details: {
      demandLevel: number
      scarcity: number
      salaryExpectation: number
      competitiveness: number
    }
  }
  cultural: {
    score: number
    details: {
      values: number
      teamwork: number
      communication: number
      workStyle: number
    }
  }
}

// 评估洞察
export interface EvaluationInsight {
  type: 'strength' | 'weakness' | 'opportunity' | 'risk'
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
}

// 评估结果
export interface EvaluationResult {
  talentInfo: {
    id: number
    name: string
    currentPosition?: string
    experience: number
    education: string
  }
  overallScore: number
  level: 'S' | 'A' | 'B' | 'C' | 'D'
  dimensions: EvaluationDimension
  insights: EvaluationInsight[]
  recommendations: {
    hiringDecision: 'highly_recommend' | 'recommend' | 'consider' | 'not_recommend'
    suggestedPositions: string[]
    developmentPlan?: string[]
    retentionStrategy?: string[]
  }
}

// 评估历史记录
export interface EvaluationHistory {
  id: number
  evaluationTime: string
  evaluationType: string
  overallScore: number
  level: string
  evaluatorId: number
  evaluatorName: string
  keyFindings: string[]
  scoreChange?: number
}

// 潜力预测
export interface PotentialPrediction {
  year: number
  predictedLevel: string
  predictedScore: number
  confidence: number
  keyMilestones: string[]
}

// 联系记录
export interface ContactRecord {
  time: string
  type: string // 电话/邮件/面谈
  content: string
  result?: string
  nextAction?: string
  userId: number
  userName: string
}

// 面试安排
export interface InterviewSchedule {
  id: number
  resumeId: number
  resume?: Resume
  jobPostId: number
  jobPost?: JobPost
  round: number // 面试轮次
  type: string // 面试类型：初试/复试/终试
  method: string // 面试方式：现场/电话/视频
  startTime: string
  endTime: string
  location?: string // 面试地点
  meetingUrl?: string // 视频面试链接
  interviewers: Interviewer[] // 面试官
  status: string // 状态：待面试/进行中/已完成/已取消
  result?: string // 面试结果：通过/待定/淘汰
  evaluation?: InterviewEvaluation[] // 面试评价
  notificationSent: boolean // 是否已发送通知
  createTime: string
  updateTime: string
}

// 面试官
export interface Interviewer {
  userId: number
  userName: string
  department: string
  role: string // 面试官角色：主面试官/辅助面试官
}

// 面试评价
export interface InterviewEvaluation {
  interviewerId: number
  interviewerName: string
  dimensions: EvaluationDimension[] // 评价维度
  overallScore: number // 总体评分
  recommendation: string // 推荐意见：强烈推荐/推荐/待定/不推荐
  strengths?: string // 优势
  weaknesses?: string // 不足
  comments?: string // 评价意见
  createTime: string
}

// 评价维度
export interface EvaluationDimension {
  name: string // 维度名称：专业能力/沟通能力/团队协作等
  score: number // 评分
  weight?: number // 权重
  comment?: string
}

// 招聘计划
export interface RecruitmentPlan {
  id: number
  year: number
  quarter?: number
  name: string
  departmentId?: number
  departmentName?: string
  totalCount: number // 计划招聘总数
  completedCount: number // 已完成数量
  budget?: number // 预算
  startDate: string
  endDate: string
  status: string // 状态：草稿/执行中/已完成/已取消
  details: RecruitmentPlanDetail[] // 计划明细
  createTime: string
  updateTime: string
}

// 招聘计划明细
export interface RecruitmentPlanDetail {
  positionId: number
  positionName: string
  count: number
  priority: string // 优先级
  completed: number
  requirements?: string
}

// 招聘统计
export interface RecruitmentStatistics {
  // 招聘漏斗
  funnel: {
    applied: number // 申请人数
    screened: number // 筛选通过
    interviewed: number // 面试人数
    offered: number // 发放offer
    hired: number // 入职人数
  }

  // 招聘周期
  cycle: {
    averageDays: number // 平均招聘周期
    screening: number // 筛选阶段天数
    interview: number // 面试阶段天数
    offer: number // offer阶段天数
  }

  // 渠道效果
  channel: Array<{
    source: string
    applied: number
    hired: number
    cost?: number
    roi?: number
  }>

  // 汇总数据
  summary: {
    totalPosts: number // 发布职位数
    activePosts: number // 在招职位数
    totalApplicants: number // 总申请人数
    hireRate: number // 录用率
    satisfaction: number // 满意度
  }
}

// 招聘流程配置
export interface RecruitmentProcess {
  id: number
  name: string
  steps: ProcessStep[]
  isDefault: boolean
  createTime: string
}

// 流程步骤
export interface ProcessStep {
  id: string
  name: string
  type: string // 筛选/面试/审批/其他
  order: number

  config?: unknown // 步骤配置

  conditions?: unknown // 流转条件
}

// ===== 职位发布管理相关接口 =====

// 职位发布配置接口
export interface RecruitmentJobPostConfig {
  configId: string
  configName: string
  departmentId: string
  departmentName: string
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  publishSettings: {
    autoPublish: boolean
    publishChannels: ('INTERNAL' | 'EXTERNAL' | 'SOCIAL' | 'PROFESSIONAL' | 'CAMPUS')[]
    defaultVisibility: 'PUBLIC' | 'INTERNAL' | 'DEPARTMENT' | 'CUSTOM'
    requireApproval: boolean
    approvalFlow: {
      stepId: string
      stepName: string
      approverType: 'HR' | 'DEPARTMENT_HEAD' | 'HIRING_MANAGER' | 'SYSTEM'
      approverId?: string
      approverName?: string
      isRequired: boolean
      timeout: number
    }[]
    autoExpiry: {
      enabled: boolean
      defaultDays: number
      reminderDays: number[]
      autoRenew: boolean
    }
  }
  templates: {
    defaultTemplate: string
    customTemplates: {
      templateId: string
      templateName: string
      templateType: 'DESCRIPTION' | 'REQUIREMENTS' | 'BENEFITS' | 'COMPLETE'
      content: string
      variables: string[]
    }[]
  }
  validation: {
    requiredFields: string[]
    minSalary?: number
    maxSalary?: number
    allowedEducationLevels: string[]
    allowedExperienceLevels: string[]
    customValidationRules: {
      field: string
      rule: string
      message: string
    }[]
  }
  seo: {
    enableSeo: boolean
    defaultKeywords: string[]
    metaTemplate: string
    structuredData: boolean
    urlPattern: string
  }
  analytics: {
    trackViews: boolean
    trackApplications: boolean
    trackSources: boolean
    conversionGoals: string[]
    customEvents: {
      eventName: string
      trigger: string

      properties: { [key: string]: unknown }
    }[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位发布状态接口
export interface RecruitmentJobPostStatus {
  postId: string
  postTitle: string
  currentStatus:
    | 'DRAFT'
    | 'PENDING_APPROVAL'
    | 'APPROVED'
    | 'PUBLISHED'
    | 'PAUSED'
    | 'EXPIRED'
    | 'CLOSED'
    | 'CANCELLED'
  statusHistory: {
    status: string
    timestamp: string
    operatorId: string
    operatorName: string
    reason?: string
    comments?: string
    systemGenerated: boolean
  }[]
  publishInfo: {
    publishedAt?: string
    publishedBy?: string
    scheduledPublishAt?: string
    expiryDate?: string
    actualExpiryDate?: string
    channels: {
      channelId: string
      channelName: string
      channelType: string
      publishStatus: 'PENDING' | 'PUBLISHED' | 'FAILED' | 'REMOVED'
      publishedAt?: string
      externalId?: string
      externalUrl?: string
      metrics?: {
        views: number
        applications: number
        clicks: number
        shares: number
      }
    }[]
  }
  approvalInfo: {
    currentStep?: number
    totalSteps: number
    approvalFlow: {
      stepId: string
      stepName: string
      approverType: string
      approverId?: string
      approverName?: string
      status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SKIPPED'
      processedAt?: string
      comments?: string
      attachments?: string[]
    }[]
    finalDecision?: 'APPROVED' | 'REJECTED'
    finalDecisionAt?: string
    finalDecisionBy?: string
  }
  metrics: {
    totalViews: number
    uniqueViews: number
    applicationCount: number
    qualifiedApplications: number
    averageViewTime: number
    bounceRate: number
    sourceBreakdown: {
      source: string
      views: number
      applications: number
      conversionRate: number
    }[]
  }
  alerts: {
    alertType: 'EXPIRY_WARNING' | 'LOW_APPLICATIONS' | 'HIGH_TRAFFIC' | 'APPROVAL_PENDING'
    severity: 'INFO' | 'WARNING' | 'ERROR'
    message: string
    triggeredAt: string
    isAcknowledged: boolean
  }[]
  lastModifiedAt: string
  lastModifiedBy: string
}

// 职位发布模板接口
export interface RecruitmentJobPostTemplate {
  templateId: string
  templateName: string
  templateType: 'STANDARD' | 'EXECUTIVE' | 'TECHNICAL' | 'SALES' | 'INTERN' | 'CUSTOM'
  category: string
  isPublic: boolean
  isActive: boolean
  createdBy: string
  createdAt: string
  structure: {
    sections: {
      sectionId: string
      sectionName: string
      sectionType: 'TEXT' | 'LIST' | 'TABLE' | 'MEDIA'
      order: number
      isRequired: boolean
      content: {
        title?: string
        content?: string
        placeholder?: string

        defaultValue?: unknown
        validation?: {
          minLength?: number
          maxLength?: number
          pattern?: string
          required?: boolean
        }
      }
      variables: string[]
    }[]
    style: {
      theme: string
      colors: { [key: string]: string }
      fonts: { [key: string]: string }
      layout: string
    }
  }
  usage: {
    totalUsage: number
    recentUsage: number
    successRate: number
    avgApplications: number
    topDepartments: {
      departmentName: string
      usageCount: number
    }[]
  }
  metadata: {
    tags: string[]
    description: string
    version: string
    compatibility: string[]
    lastUpdated: string
    changeLog: {
      version: string
      changes: string[]
      updatedAt: string
      updatedBy: string
    }[]
  }
}

// 职位发布渠道接口
export interface RecruitmentJobPostChannel {
  channelId: string
  channelName: string
  channelType: 'INTERNAL' | 'EXTERNAL' | 'SOCIAL' | 'PROFESSIONAL' | 'CAMPUS' | 'AGENCY'
  provider: string
  isActive: boolean
  configuration: {
    apiEndpoint?: string
    authMethod?: 'API_KEY' | 'OAUTH' | 'BASIC' | 'TOKEN'
    credentials: {
      apiKey?: string
      clientId?: string
      clientSecret?: string
      accessToken?: string
      username?: string
      password?: string
    }
    settings: {
      autoPublish: boolean
      autoRemove: boolean
      syncInterval: number
      retryAttempts: number
      timeout: number
    }
    fieldMapping: {
      localField: string
      remoteField: string
      transformation?: string
      required: boolean
    }[]
    restrictions: {
      maxPostsPerDay?: number
      allowedCategories?: string[]
      requiredFields?: string[]
      characterLimits?: { [field: string]: number }
    }
  }
  performance: {
    totalPosts: number
    successfulPosts: number
    failedPosts: number
    averageViews: number
    averageApplications: number
    conversionRate: number
    lastSyncAt?: string
    lastErrorAt?: string
    lastError?: string
  }
  costs: {
    pricingModel: 'FREE' | 'PER_POST' | 'SUBSCRIPTION' | 'PERFORMANCE'
    costPerPost?: number
    monthlyFee?: number
    conversionCost?: number
    totalSpent: number
    costPerHire?: number
  }
  compliance: {
    termsAccepted: boolean
    termsVersion: string
    privacyCompliant: boolean
    dataRetentionDays: number
    lastComplianceCheck: string
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位发布分析接口
export interface RecruitmentJobPostAnalytics {
  analyticsId: string
  postId: string
  postTitle: string
  period: [string, string]
  metrics: {
    visibility: {
      totalImpressions: number
      uniqueImpressions: number
      impressionRate: number
      reach: number
      frequency: number
      shareOfVoice: number
    }
    engagement: {
      totalViews: number
      uniqueViews: number
      avgViewDuration: number
      bounceRate: number
      pageDepth: number
      returnVisitorRate: number
    }
    applications: {
      totalApplications: number
      uniqueApplications: number
      applicationRate: number
      qualifiedApplications: number
      qualificationRate: number
      multipleApplications: number
    }
    conversion: {
      impressionToView: number
      viewToApplication: number
      applicationToQualified: number
      overallConversion: number
      funnelDropoff: {
        stage: string
        dropoffRate: number
      }[]
    }
  }
  demographics: {
    geographic: {
      country: string
      region: string
      city: string
      applicantCount: number
      percentage: number
    }[]
    professional: {
      industry: string
      experience: string
      education: string
      applicantCount: number
      percentage: number
    }[]
    temporal: {
      hourOfDay: { hour: number; views: number; applications: number }[]
      dayOfWeek: { day: string; views: number; applications: number }[]
      dateRange: { date: string; views: number; applications: number }[]
    }
  }
  sources: {
    sourceId: string
    sourceName: string
    sourceType: string
    referrals: number
    applications: number
    qualifiedApplications: number
    conversionRate: number
    cost?: number
    roi?: number
  }[]
  competitors: {
    competitorName?: string
    competitorPost?: string
    similarity: number
    rankingComparison: number
    metricComparison: {
      metric: string
      ourValue: number
      competitorValue: number
      difference: number
    }[]
  }[]
  insights: {
    performanceInsights: {
      insight: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      recommendation: string
      potentialGain?: string
    }[]
    anomalies: {
      metric: string
      description: string
      deviation: number
      detectedAt: string
    }[]
    predictions: {
      metric: string
      predictedValue: number
      confidence: number
      timeframe: string
    }[]
  }
  benchmarks: {
    industryBenchmarks: {
      metric: string
      industryAverage: number
      ourValue: number
      percentile: number
    }[]
    internalBenchmarks: {
      metric: string
      historicalAverage: number
      ourValue: number
      improvement: number
    }[]
  }
  generatedAt: string
  generatedBy: string
}

// 职位发布批量操作接口
export interface RecruitmentJobPostBatchOperation {
  operationId: string
  operationType: 'PUBLISH' | 'UNPUBLISH' | 'APPROVE' | 'REJECT' | 'UPDATE' | 'DELETE' | 'CLONE'
  operationName: string
  targetPosts: {
    postId: string
    postTitle: string
    currentStatus: string
  }[]
  parameters: {
    publishChannels?: string[]
    publishDate?: string
    expiryDate?: string

    updateFields?: { [field: string]: unknown }
    approvalComments?: string
    rejectionReason?: string
    cloneSettings?: {
      includeTemplate: boolean
      includeSettings: boolean
      updateTitle: boolean
      titleSuffix?: string
    }
  }
  validation: {
    isValid: boolean
    errors: {
      postId: string
      errorType: string
      errorMessage: string
      severity: 'ERROR' | 'WARNING'
    }[]
    warnings: {
      postId: string
      warningType: string
      warningMessage: string
    }[]
    summary: {
      totalPosts: number
      validPosts: number
      invalidPosts: number
      warningPosts: number
    }
  }
  execution: {
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
    progress: {
      completed: number
      total: number
      percentage: number
      currentPost?: string
      estimatedTimeRemaining?: number
    }
    results: {
      successful: {
        postId: string
        postTitle: string
        result: string
      }[]
      failed: {
        postId: string
        postTitle: string
        error: string
        retryable: boolean
      }[]
    }
    startedAt?: string
    completedAt?: string
    executedBy: string
  }
  notifications: {
    notifyOnCompletion: boolean
    notifyOnError: boolean
    recipients: string[]
    emailTemplate?: string
  }
  createdAt: string
  createdBy: string
}

// ===== 职位需求分析相关接口 =====

// 职位需求分析配置接口
export interface RecruitmentDemandAnalysisConfig {
  configId: string
  configName: string
  analysisScope: 'DEPARTMENT' | 'POSITION' | 'SKILL' | 'MARKET' | 'ORGANIZATION'
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  analysisSettings: {
    enablePredictiveAnalysis: boolean
    analysisFrequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
    historicalDataRange: number // 月数
    includedMetrics: (
      | 'HEADCOUNT'
      | 'TURNOVER'
      | 'HIRING_RATE'
      | 'TIME_TO_FILL'
      | 'COST_PER_HIRE'
      | 'QUALITY_OF_HIRE'
    )[]
    benchmarkSources: ('INTERNAL' | 'INDUSTRY' | 'MARKET' | 'COMPETITOR')[]
    alertThresholds: {
      criticalShortage: number // 百分比
      highTurnover: number
      longTimeToFill: number // 天数
      highCostPerHire: number
    }
  }
  dataSources: {
    internalSources: {
      hrms: boolean
      ats: boolean
      performanceSystem: boolean
      learningSystem: boolean
    }
    externalSources: {
      jobBoards: string[]
      salaryData: string[]
      marketReports: string[]
      industryBenchmarks: string[]
    }
  }
  reportingSettings: {
    autoGenerateReports: boolean
    reportSchedule: string // cron expression
    recipients: string[]
    reportFormats: ('PDF' | 'EXCEL' | 'DASHBOARD' | 'EMAIL')[]
    customDashboards: {
      dashboardId: string
      dashboardName: string
      widgets: string[]

      filters: { [key: string]: unknown }
    }[]
  }
  aiSettings: {
    enableAIInsights: boolean
    modelVersion: string
    confidenceThreshold: number
    insights: {
      demandForecasting: boolean
      skillGapAnalysis: boolean
      talentSupplyAnalysis: boolean
      competitiveAnalysis: boolean
      riskAssessment: boolean
    }
    customModels: {
      modelId: string
      modelName: string
      modelType: string

      parameters: { [key: string]: unknown }
    }[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位需求分析报告接口
export interface RecruitmentDemandAnalysisReport {
  reportId: string
  reportTitle: string
  reportType:
    | 'COMPREHENSIVE'
    | 'DEPARTMENTAL'
    | 'POSITIONAL'
    | 'SKILL_BASED'
    | 'MARKET_TRENDS'
    | 'PREDICTIVE'
  generatedAt: string
  reportPeriod: [string, string]
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'SCHEDULED'
  summary: {
    totalPositions: number
    vacantPositions: number
    urgentVacancies: number
    averageTimeToFill: number
    totalHiringCost: number
    overallSatisfactionScore: number
  }
  demandAnalysis: {
    currentDemand: {
      byDepartment: {
        departmentId: string
        departmentName: string
        totalPositions: number
        vacantPositions: number
        vacancyRate: number
        urgentVacancies: number
        avgTimeToFill: number
        priority: 'HIGH' | 'MEDIUM' | 'LOW'
      }[]
      byPosition: {
        positionId: string
        positionName: string
        currentHeadcount: number
        targetHeadcount: number
        vacancy: number
        growth: number
        difficulty: 'HIGH' | 'MEDIUM' | 'LOW'
      }[]
      bySkill: {
        skillName: string
        skillCategory: string
        demandLevel: 'HIGH' | 'MEDIUM' | 'LOW'
        supplyLevel: 'HIGH' | 'MEDIUM' | 'LOW'
        gapSeverity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW'
        marketTrend: 'INCREASING' | 'STABLE' | 'DECREASING'
      }[]
    }
    historicalTrends: {
      period: string
      totalDemand: number
      filledPositions: number
      averageTimeToFill: number
      costPerHire: number
      qualityScore: number
    }[]
    seasonalPatterns: {
      month: string
      demandIndex: number
      hiringSuccess: number
      costEfficiency: number
    }[]
  }
  supplyAnalysis: {
    internalSupply: {
      readyForPromotion: number
      crossFunctionalCandidates: number
      returningEmployees: number
      internalMobilityRate: number
    }
    externalSupply: {
      activeJobSeekers: number
      passiveCandidates: number
      talentPoolSize: number
      marketCompetitiveness: 'HIGH' | 'MEDIUM' | 'LOW'
    }
    pipelineAnalysis: {
      stage: string
      candidateCount: number
      conversionRate: number
      averageStageTime: number
      dropoffRate: number
    }[]
  }
  gapAnalysis: {
    criticalGaps: {
      gapType: 'HEADCOUNT' | 'SKILL' | 'EXPERIENCE' | 'LEADERSHIP'
      affectedArea: string
      gapSize: number
      urgency: 'IMMEDIATE' | 'SHORT_TERM' | 'MEDIUM_TERM' | 'LONG_TERM'
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      recommendedActions: string[]
    }[]
    skillGaps: {
      skillName: string
      currentLevel: number
      requiredLevel: number
      gap: number
      affectedRoles: string[]
      developmentOptions: string[]
    }[]
    capacityGaps: {
      area: string
      currentCapacity: number
      requiredCapacity: number
      gap: number
      timeline: string
      solutions: string[]
    }[]
  }
  predictiveInsights: {
    futureRecruitmentNeeds: {
      timeframe: string
      estimatedVacancies: number
      confidenceLevel: number
      drivingFactors: string[]
      recommendations: string[]
    }[]
    riskAssessment: {
      riskType: 'TURNOVER' | 'SKILL_SHORTAGE' | 'SUCCESSION' | 'COMPLIANCE'
      probability: number
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      mitigationStrategies: string[]
    }[]
    marketTrends: {
      trend: string
      impact: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL'
      timeline: string
      preparation: string[]
    }[]
  }
  competitiveAnalysis: {
    industryBenchmarks: {
      metric: string
      industryAverage: number
      ourValue: number
      percentile: number
      trend: 'IMPROVING' | 'STABLE' | 'DECLINING'
    }[]
    competitorIntelligence: {
      competitorName?: string
      hiringActivity: 'HIGH' | 'MEDIUM' | 'LOW'
      targetSkills: string[]
      compensationTrends: string
      strategicMoves: string[]
    }[]
  }
  recommendations: {
    immediate: {
      action: string
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
      resources: string[]
    }[]
    shortTerm: {
      action: string
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
      dependencies: string[]
    }[]
    longTerm: {
      action: string
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
      strategicAlignment: string
    }[]
  }
  appendices: {
    dataSource: string
    methodology: string
    assumptions: string[]
    limitations: string[]
    definitions: { [key: string]: string }
  }
  generatedBy: string
  approvedBy?: string
  approvedAt?: string
}

// 职位需求预测模型接口
export interface RecruitmentDemandForecastModel {
  modelId: string
  modelName: string
  modelType: 'LINEAR_REGRESSION' | 'TIME_SERIES' | 'MACHINE_LEARNING' | 'ENSEMBLE' | 'CUSTOM'
  version: string
  isActive: boolean
  trainingData: {
    startDate: string
    endDate: string
    dataPoints: number
    features: string[]
    targetVariable: string
  }
  modelParameters: {
    algorithm: string

    hyperparameters: { [key: string]: unknown }
    featureWeights: { [feature: string]: number }
    accuracy: number
    precision: number
    recall: number
    f1Score: number
  }
  validationResults: {
    testPeriod: [string, string]
    predictions: {
      predicted: number
      actual: number
      date: string
      accuracy: number
    }[]
    overallAccuracy: number
    mae: number // Mean Absolute Error
    mse: number // Mean Squared Error
    rmse: number // Root Mean Squared Error
  }
  predictionSettings: {
    forecastHorizon: number // months
    confidenceInterval: number // percentage
    updateFrequency: 'DAILY' | 'WEEKLY' | 'MONTHLY'
    alertThresholds: {
      significantChange: number // percentage
      lowConfidence: number // percentage
    }
  }
  applicableScenarios: {
    departments: string[]
    positions: string[]
    skillAreas: string[]
    businessConditions: string[]
  }
  lastTraining: {
    trainedAt: string
    trainedBy: string
    dataVersion: string
    performance: {
      trainingAccuracy: number
      validationAccuracy: number
      improvementOverPrevious: number
    }
  }
  usage: {
    totalPredictions: number
    successfulPredictions: number
    averageAccuracy: number
    lastUsed: string
    topUsers: {
      userId: string
      userName: string
      usageCount: number
    }[]
  }
  metadata: {
    description: string
    tags: string[]
    category: string
    complexity: 'LOW' | 'MEDIUM' | 'HIGH'
    maintainer: string
    documentationUrl?: string
    changeLog: {
      version: string
      changes: string[]
      date: string
      author: string
    }[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位需求分析指标接口
export interface RecruitmentDemandMetrics {
  metricsId: string
  reportId: string
  calculatedAt: string
  period: [string, string]
  demandMetrics: {
    totalDemand: number
    newDemand: number
    urgentDemand: number
    demandGrowthRate: number
    demandVolatility: number
    averageDemandDuration: number
  }
  supplyMetrics: {
    totalSupply: number
    qualifiedSupply: number
    internalSupply: number
    externalSupply: number
    supplyGrowthRate: number
    supplyQualityScore: number
  }
  balanceMetrics: {
    supplyDemandRatio: number
    vacancyRate: number
    fillRate: number
    timeToFillAverage: number
    timeToFillMedian: number
    costPerHire: number
  }
  qualityMetrics: {
    qualityOfHire: number
    retention90Days: number
    retention1Year: number
    performanceScore: number
    promotionRate: number
    culturalFit: number
  }
  efficiencyMetrics: {
    applicationToHireRatio: number
    screeningEfficiency: number
    interviewToOfferRatio: number
    offerAcceptanceRate: number
    processCompletionRate: number
    recruiterProductivity: number
  }
  costMetrics: {
    totalRecruitmentCost: number
    costPerHire: number
    costPerQualifiedCandidate: number
    agencyCosts: number
    advertisingCosts: number
    internalCosts: number
  }
  trendAnalysis: {
    metric: string
    currentValue: number
    previousValue: number
    changePercentage: number
    trend: 'IMPROVING' | 'STABLE' | 'DECLINING'
    seasonalAdjusted: number
  }[]
  benchmarkComparison: {
    metric: string
    ourValue: number
    industryBenchmark: number
    percentile: number
    gap: number
    performance: 'ABOVE' | 'AT' | 'BELOW'
  }[]
  alerts: {
    alertType: 'CRITICAL' | 'WARNING' | 'INFO'
    metric: string
    threshold: number
    currentValue: number
    description: string
    recommendedAction: string
  }[]
  insights: {
    category: 'DEMAND' | 'SUPPLY' | 'EFFICIENCY' | 'QUALITY' | 'COST'
    insight: string
    impact: 'HIGH' | 'MEDIUM' | 'LOW'
    confidence: number

    supportingData: unknown[]
  }[]
  generatedBy: string
}

// 职位需求分析任务接口
export interface RecruitmentDemandAnalysisTask {
  taskId: string
  taskName: string
  taskType: 'SCHEDULED' | 'ON_DEMAND' | 'TRIGGERED' | 'BATCH'
  analysisType: 'CURRENT_STATE' | 'TREND_ANALYSIS' | 'PREDICTIVE' | 'COMPARATIVE' | 'COMPREHENSIVE'
  priority: 'HIGH' | 'MEDIUM' | 'LOW'
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  requestedBy: string
  requestedAt: string
  parameters: {
    scope: {
      departments?: string[]
      positions?: string[]
      skills?: string[]
      locations?: string[]
    }
    timeframe: [string, string]
    granularity: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
    includePredictive: boolean
    forecastHorizon?: number
    benchmarkIndustry?: string[]

    customFilters?: { [key: string]: unknown }
  }
  dataInputs: {
    source: string
    dataType: string
    recordCount: number
    lastUpdated: string
    quality: 'HIGH' | 'MEDIUM' | 'LOW'
  }[]
  execution: {
    startedAt?: string
    completedAt?: string
    duration?: number
    progress: {
      currentStep: string
      completedSteps: number
      totalSteps: number
      percentage: number
      estimatedTimeRemaining?: number
    }
    resources: {
      cpuUsage: number
      memoryUsage: number
      dataProcessed: number
      apiCalls: number
    }
  }
  results: {
    reportId?: string
    outputFiles: {
      fileName: string
      fileType: string
      filePath: string
      fileSize: number
    }[]
    summary: {
      keyFindings: string[]
      recommendations: string[]
      dataQuality: string
      confidence: number
    }
  }
  errors: {
    errorCode: string
    errorMessage: string
    errorDetails: string
    occurredAt: string
    resolution?: string
  }[]
  notifications: {
    onCompletion: boolean
    onError: boolean
    recipients: string[]
    emailTemplate?: string
    sentAt?: string[]
  }
  schedule: {
    isRecurring: boolean
    cronExpression?: string
    nextRun?: string
    lastRun?: string
    runCount: number
  }
  dependencies: {
    dependsOn: string[]
    blocks: string[]
    triggers: string[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 职位技能需求分析接口
export interface RecruitmentSkillDemandAnalysis {
  analysisId: string
  analysisName: string
  skillCategory: string
  period: [string, string]
  skillInventory: {
    skillId: string
    skillName: string
    skillLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'
    skillType: 'TECHNICAL' | 'SOFT' | 'DOMAIN' | 'LEADERSHIP' | 'LANGUAGE'
    currentSupply: {
      internal: number
      external: number
      total: number
      availability: 'HIGH' | 'MEDIUM' | 'LOW'
    }
    demandAnalysis: {
      currentDemand: number
      projectedDemand: number
      demandGrowth: number
      urgency: 'IMMEDIATE' | 'SHORT_TERM' | 'MEDIUM_TERM' | 'LONG_TERM'
    }
    gapAnalysis: {
      quantitativeGap: number
      qualitativeGap: 'HIGH' | 'MEDIUM' | 'LOW'
      criticalityRating: number
      timeToClose: number
      closureStrategy: string[]
    }
    marketIntelligence: {
      marketDemand: 'HIGH' | 'MEDIUM' | 'LOW'
      salaryTrend: 'INCREASING' | 'STABLE' | 'DECREASING'
      competitionLevel: 'HIGH' | 'MEDIUM' | 'LOW'
      emergingTrends: string[]
    }
  }[]
  skillClusters: {
    clusterId: string
    clusterName: string
    skills: string[]
    demandScore: number
    supplyScore: number
    strategicImportance: 'HIGH' | 'MEDIUM' | 'LOW'
    investmentPriority: number
  }[]
  futureSkillNeeds: {
    timeframe: string
    emergingSkills: {
      skillName: string
      relevanceScore: number
      adoptionTimeline: string
      preparationActions: string[]
    }[]
    decliningSkills: {
      skillName: string
      declineRate: number
      replacementSkills: string[]
      transitionPlan: string[]
    }[]
  }
  developmentPathways: {
    pathway: string
    fromSkills: string[]
    toSkills: string[]
    developmentMethods: string[]
    estimatedTime: number
    cost: number
    roi: number
  }[]
  recommendations: {
    hiring: {
      prioritySkills: string[]
      sourcingStrategies: string[]
      compensationGuidance: string[]
    }
    development: {
      trainingPrograms: string[]
      certificationTargets: string[]
      mentorshipPairs: string[]
    }
    strategic: {
      partnershipsNeeded: string[]
      toolsAndTechnology: string[]
      organizationalChanges: string[]
    }
  }
  riskAssessment: {
    skillRisks: {
      skill: string
      riskType: 'SHORTAGE' | 'OBSOLESCENCE' | 'DEPENDENCY' | 'COMPETITION'
      probability: number
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      mitigation: string[]
    }[]
    businessImpact: {
      impactArea: string
      severity: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
      preventiveActions: string[]
    }[]
  }
  generatedAt: string
  generatedBy: string
  validUntil: string
}

// ===== 职位匹配算法相关接口 =====

// 职位匹配算法配置接口
export interface RecruitmentJobMatchingConfig {
  configId: string
  configName: string
  algorithmType: 'COMPREHENSIVE' | 'SKILL_BASED' | 'EXPERIENCE_BASED' | 'EDUCATION_BASED' | 'CUSTOM'
  isActive: boolean
  version: string
  weights: {
    skills: number
    experience: number
    education: number
    salary: number
    location: number
    industry: number
    culture: number
    additional?: { [key: string]: number }
  }
  thresholds: {
    minimumMatchScore: number
    recommendationThreshold: number
    autoScreeningThreshold: number
    excellentMatchThreshold: number
  }
  matchingRules: {
    skillMatching: {
      exactMatchWeight: number
      similarSkillWeight: number
      skillLevelWeight: number
      skillCategoryWeight: number
      requiredSkillsMode: 'ALL' | 'MAJORITY' | 'ANY'
    }
    experienceMatching: {
      yearExactMatchWeight: number
      yearRangeWeight: number
      industryExperienceWeight: number
      roleLevelWeight: number
      leadershipExperienceWeight: number
    }
    educationMatching: {
      degreeWeight: number
      majorWeight: number
      schoolTierWeight: number
      certificationWeight: number
      additionalQualificationWeight: number
    }
    compensationMatching: {
      salaryRangeWeight: number
      benefitsWeight: number
      equityWeight: number
      bonusWeight: number
      flexibilityWeight: number
    }
  }
  aiSettings: {
    enableMLMatching: boolean
    modelVersion: string
    confidenceThreshold: number
    learningEnabled: boolean
    feedbackWeight: number
    biasDetection: boolean
    explainabilityLevel: 'HIGH' | 'MEDIUM' | 'LOW'
  }
  candidateSources: {
    internalCandidates: boolean
    talentPool: boolean
    activeApplications: boolean
    passiveCandidates: boolean
    referrals: boolean
    externalDatabases: string[]
  }
  outputSettings: {
    maxCandidatesPerJob: number
    includeExplanation: boolean
    includeConfidenceScore: boolean
    includeAlternativeJobs: boolean
    rankingMethod: 'SCORE' | 'WEIGHTED' | 'ML_RANKING'
  }
  qualityControls: {
    diversityRequirements: {
      enableDiversityFilters: boolean
      genderBalance: boolean
      ageDistribution: boolean
      backgroundDiversity: boolean
    }
    fairnessChecks: {
      biasDetection: boolean
      equalOpportunity: boolean
      demographicParity: boolean
    }
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位匹配结果接口
export interface RecruitmentJobMatchingResult {
  matchingId: string
  jobId: string
  jobTitle: string
  candidateId: string
  candidateName: string
  executedAt: string
  algorithmVersion: string
  overallMatchScore: number
  confidence: number
  recommendation: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'NOT_SUITABLE'
  detailedScores: {
    skillMatch: {
      score: number
      breakdown: {
        exactMatches: string[]
        similarMatches: { skill: string; similarity: number }[]
        missingSkills: string[]
        additionalSkills: string[]
      }
      explanation: string
    }
    experienceMatch: {
      score: number
      breakdown: {
        yearsMatch: number
        industryMatch: number
        roleMatch: number
        leadershipMatch: number
        projectMatch: number
      }
      explanation: string
    }
    educationMatch: {
      score: number
      breakdown: {
        degreeMatch: number
        majorMatch: number
        schoolMatch: number
        certificationMatch: number
        additionalMatch: number
      }
      explanation: string
    }
    compensationMatch: {
      score: number
      breakdown: {
        salaryFit: number
        benefitsFit: number
        locationFit: number
        flexibilityFit: number
      }
      explanation: string
    }
    culturalFit: {
      score: number
      breakdown: {
        valuesAlignment: number
        workStyleFit: number
        teamCompatibility: number
        growthPotential: number
      }
      explanation: string
    }
  }
  riskFactors: {
    overqualificationRisk: number
    underqualificationRisk: number
    salaryMismatchRisk: number
    locationRisk: number
    cultureRisk: number
    retentionRisk: number
  }
  recommendations: {
    strengths: string[]
    concerns: string[]
    improvementSuggestions: string[]
    interviewFocus: string[]
    onboardingTips: string[]
  }
  alternativeMatches: {
    jobId: string
    jobTitle: string
    matchScore: number
    reason: string
  }[]
  metadata: {
    processingTime: number
    dataQuality: number
    algorithmUsed: string
    confidenceFactors: string[]
    biasIndicators?: string[]
  }
  executedBy: string
}

// 职位匹配批量操作接口
export interface RecruitmentJobMatchingBatch {
  batchId: string
  batchName: string
  requestType: 'JOB_TO_CANDIDATES' | 'CANDIDATE_TO_JOBS' | 'BIDIRECTIONAL'
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  requestedBy: string
  requestedAt: string
  configuration: {
    algorithmConfigId: string
    targetJobs?: string[]
    targetCandidates?: string[]
    filters: {
      candidateSource?: string[]
      experienceLevel?: string[]
      educationLevel?: string[]
      location?: string[]
      availabilityStatus?: string[]
    }
    options: {
      maxCandidatesPerJob?: number
      minMatchScore?: number
      includeInternalCandidates?: boolean
      includePreviousApplicants?: boolean
      priorityWeighting?: { [factor: string]: number }
    }
  }
  execution: {
    startedAt?: string
    completedAt?: string
    progress: {
      totalItems: number
      processedItems: number
      successfulMatches: number
      failedMatches: number
      percentage: number
      currentProcessing?: string
      estimatedTimeRemaining?: number
    }
    performance: {
      averageProcessingTime: number
      peakMemoryUsage: number
      totalComputeTime: number
      cachehitRate: number
    }
  }
  results: {
    summary: {
      totalJobs: number
      totalCandidates: number
      totalMatches: number
      excellentMatches: number
      goodMatches: number
      fairMatches: number
      avgMatchScore: number
      topMatchScore: number
    }
    matches: RecruitmentJobMatchingResult[]
    insights: {
      mostDemandedSkills: string[]
      skillGaps: string[]
      marketTrends: string[]
      recommendedActions: string[]
    }
  }
  quality: {
    dataQualityScore: number
    algorithmPerformance: number
    resultDiversity: number
    biasIndicators: {
      genderBias?: number
      ageBias?: number
      educationBias?: number
      locationBias?: number
    }
  }
  exportOptions: {
    formats: ('JSON' | 'CSV' | 'EXCEL' | 'PDF')[]
    customFields?: string[]
    includeExplanations: boolean
  }
  notifications: {
    onCompletion: boolean
    onError: boolean
    recipients: string[]
    customMessage?: string
  }
}

// 职位匹配算法性能接口
export interface RecruitmentJobMatchingPerformance {
  performanceId: string
  algorithmConfigId: string
  evaluationPeriod: [string, string]
  metrics: {
    accuracy: {
      precision: number
      recall: number
      f1Score: number
      rocAuc: number
      confusionMatrix: number[][]
    }
    efficiency: {
      avgProcessingTime: number
      throughput: number
      memoryUsage: number
      cpuUtilization: number
      cacheEfficiency: number
    }
    businessImpact: {
      interviewToOfferRatio: number
      timeToFill: number
      qualityOfHire: number
      candidateSatisfaction: number
      hiringManagerSatisfaction: number
      costPerHire: number
    }
    fairness: {
      demographicParity: number
      equalOpportunity: number
      calibration: number
      individualFairness: number
    }
  }
  benchmarkComparisons: {
    industryBenchmark: {
      metric: string
      ourValue: number
      industryAverage: number
      percentile: number
    }[]
    historicalComparison: {
      metric: string
      currentValue: number
      previousValue: number
      improvement: number
    }[]
  }
  feedback: {
    userFeedback: {
      totalRatings: number
      averageRating: number
      positiveFeedbackRate: number
      commonComplaints: string[]
      suggestionCategories: { [category: string]: number }
    }
    hiringOutcomes: {
      acceptanceRate: number
      onboardingSuccess: number
      performanceCorrelation: number
      retentionRate: number
    }
  }
  optimizations: {
    recommendedAdjustments: {
      parameter: string

      currentValue: unknown

      recommendedValue: unknown
      expectedImprovement: string
      rationale: string
    }[]
    modelUpdates: {
      suggestedRetraining: boolean
      newDataAvailable: boolean
      performanceDrift: number
      lastUpdateDate: string
    }
  }
  generatedAt: string
  generatedBy: string
}

// 职位匹配候选人画像接口
export interface RecruitmentCandidateProfile {
  profileId: string
  candidateId: string
  candidateName: string
  generatedAt: string
  profileVersion: string
  skillProfile: {
    technicalSkills: {
      skillName: string
      skillLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'
      yearsOfExperience: number
      lastUsed: string
      certifications: string[]
      projectsUsed: number
      selfAssessed: boolean
      validated: boolean
    }[]
    softSkills: {
      skillName: string
      strength: 'HIGH' | 'MEDIUM' | 'LOW'
      evidenceSources: string[]
      developmentAreas: string[]
    }[]
    skillGaps: string[]
    learningVelocity: number
    adaptabilityScore: number
  }
  experienceProfile: {
    careerProgression: {
      position: string
      company: string
      startDate: string
      endDate?: string
      level: 'ENTRY' | 'MID' | 'SENIOR' | 'LEAD' | 'EXECUTIVE'
      achievements: string[]
      responsibilities: string[]
    }[]
    industryExperience: {
      industry: string
      years: number
      depth: 'SURFACE' | 'MODERATE' | 'DEEP'
    }[]
    leadershipExperience: {
      teamSize: number
      managementYears: number
      leadershipStyle: string[]
      managementAchievements: string[]
    }
    projectExperience: {
      projectType: string
      complexity: 'LOW' | 'MEDIUM' | 'HIGH'
      outcome: 'SUCCESS' | 'PARTIAL' | 'FAILURE'
      roleInProject: string
      technologiesUsed: string[]
    }[]
  }
  educationProfile: {
    formalEducation: {
      degree: string
      major: string
      institution: string
      graduationYear: string
      gpa?: number
      honors?: string[]
    }[]
    certifications: {
      name: string
      issuer: string
      dateEarned: string
      expiryDate?: string
      level: string
    }[]
    continuingEducation: {
      courseName: string
      provider: string
      completionDate: string
      skillsGained: string[]
    }[]
  }
  personalityProfile: {
    workStyle: {
      preferredEnvironment: 'COLLABORATIVE' | 'INDEPENDENT' | 'MIXED'
      communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL'
      decisionMaking: 'QUICK' | 'DELIBERATE' | 'CONSULTATIVE'
      riskTolerance: 'HIGH' | 'MEDIUM' | 'LOW'
    }
    motivations: {
      careerDrivers: string[]
      valuesAlignment: string[]
      workLifeBalance: 'HIGH' | 'MEDIUM' | 'LOW'
      growthOrientation: 'HIGH' | 'MEDIUM' | 'LOW'
    }
    culturalFit: {
      teamworkOrientation: number
      innovationAffinity: number
      processAdherence: number
      changeAdaptability: number
    }
  }
  marketPosition: {
    competitiveness: 'HIGH' | 'MEDIUM' | 'LOW'
    salaryExpectation: {
      min: number
      max: number
      currency: string
      negotiable: boolean
    }
    availability: {
      status: 'IMMEDIATE' | 'TWO_WEEKS' | 'ONE_MONTH' | 'FLEXIBLE'
      constraints: string[]
    }
    locationPreferences: {
      preferred: string[]
      acceptable: string[]
      remoteWillingness: 'FULLY_REMOTE' | 'HYBRID' | 'OFFICE_ONLY'
    }
  }
  predictiveInsights: {
    careerTrajectory: {
      nextLikelyRole: string[]
      timeToNextLevel: number
      skillsToAcquire: string[]
      developmentRecommendations: string[]
    }
    retentionFactors: {
      likelyTenure: number
      retentionRisk: 'LOW' | 'MEDIUM' | 'HIGH'
      keyRetentionFactors: string[]
      flightRisks: string[]
    }
    performancePrediction: {
      estimatedPerformance: 'BELOW_AVERAGE' | 'AVERAGE' | 'ABOVE_AVERAGE' | 'EXCEPTIONAL'
      confidenceLevel: number
      keySuccessFactors: string[]
      potentialChallenges: string[]
    }
  }
  lastUpdated: string
  dataQuality: {
    completeness: number
    accuracy: number
    freshness: number
    sources: string[]
  }
}

// 职位匹配模型训练接口
export interface RecruitmentJobMatchingModel {
  modelId: string
  modelName: string
  modelType: 'NEURAL_NETWORK' | 'RANDOM_FOREST' | 'GRADIENT_BOOSTING' | 'ENSEMBLE' | 'CUSTOM'
  version: string
  isActive: boolean
  trainingData: {
    datasetId: string
    trainingPeriod: [string, string]
    totalSamples: number
    positiveExamples: number
    negativeExamples: number
    featureCount: number
    dataQuality: number
  }
  architecture: {
    inputFeatures: {
      featureName: string
      featureType: 'NUMERIC' | 'CATEGORICAL' | 'TEXT' | 'EMBEDDING'
      importance: number
      preprocessing: string
    }[]
    modelParameters: {
      hyperparameters: { [key: string]: unknown }

      regularization: { [key: string]: unknown }

      optimization: { [key: string]: unknown }
    }
    outputSpec: {
      outputType: 'SCORE' | 'PROBABILITY' | 'RANKING' | 'CLASSIFICATION'
      scoreRange: [number, number]
      interpretability: string[]
    }
  }
  performance: {
    trainingMetrics: {
      accuracy: number
      precision: number
      recall: number
      f1Score: number
      auc: number
      loss: number
    }
    validationMetrics: {
      accuracy: number
      precision: number
      recall: number
      f1Score: number
      auc: number
      loss: number
    }
    crossValidation: {
      folds: number
      meanAccuracy: number
      stdAccuracy: number
      consistency: number
    }
  }
  bias: {
    fairnessMetrics: {
      demographicParity: number
      equalOpportunity: number
      calibration: number
      treatmentEquality: number
    }
    biasDetection: {
      protectedAttributes: string[]
      biasIndicators: {
        attribute: string
        biasScore: number
        severity: 'LOW' | 'MEDIUM' | 'HIGH'
        mitigation: string[]
      }[]
    }
  }
  explainability: {
    featureImportance: {
      feature: string
      importance: number
      explanation: string
    }[]
    globalExplanations: string[]
    interpretabilityScore: number
  }
  deployment: {
    deployedAt?: string
    deploymentEnvironment: 'PRODUCTION' | 'STAGING' | 'DEVELOPMENT'
    scalabilityLimits: {
      maxConcurrentRequests: number
      maxBatchSize: number
      responseTimeTarget: number
    }
    monitoring: {
      performanceDrift: boolean
      dataQualityAlerts: boolean
      biasMonitoring: boolean
      usageStatistics: boolean
    }
  }
  maintenance: {
    lastRetraining: string
    retrainingFrequency: 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY'
    modelLifecycle: 'EXPERIMENTAL' | 'PRODUCTION' | 'DEPRECATED'
    updateTriggers: string[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位状态跟踪相关接口定义
export interface RecruitmentJobStatusTracker {
  trackerId: string
  jobId: string
  jobTitle: string
  department: string
  position: string
  currentStatus: 'DRAFT' | 'PUBLISHED' | 'ACTIVE' | 'PAUSED' | 'CLOSED' | 'ARCHIVED' | 'CANCELLED'
  statusHistory: Array<{
    statusId: string
    previousStatus: string
    currentStatus: string
    changeReason: string
    changeDescription?: string
    triggeredBy: 'SYSTEM' | 'USER' | 'SCHEDULER' | 'WORKFLOW'
    operatorId?: string
    operatorName?: string
    timestamp: string
    metadata?: Record<string, unknown>
    attachments?: Array<{
      fileId: string
      fileName: string
      fileType: string
      fileUrl: string
    }>
  }>
  metrics: {
    totalViews: number
    totalApplications: number
    qualifiedApplications: number
    interviewsScheduled: number
    interviewsCompleted: number
    offersExtended: number
    offersAccepted: number
    finalHires: number
    timeToFill: number // 天数
    timeToHire: number // 天数
    costPerHire: number
    sourcingChannelPerformance: Record<
      string,
      {
        views: number
        applications: number
        conversionRate: number
        qualityScore: number
      }
    >
  }
  timeline: {
    publishedAt?: string
    firstApplicationAt?: string
    firstInterviewAt?: string
    firstOfferAt?: string
    firstHireAt?: string
    closedAt?: string
    estimatedCloseDate?: string
    actualCloseDate?: string
    daysOverdue?: number
  }
  progressAnalysis: {
    currentStage:
      | 'SOURCING'
      | 'SCREENING'
      | 'INTERVIEWING'
      | 'DECISION_MAKING'
      | 'OFFER_NEGOTIATION'
      | 'CLOSED'
    stageProgress: Array<{
      stage: string
      status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'DELAYED' | 'BLOCKED'
      startDate?: string
      endDate?: string
      duration?: number
      target: number
      actual: number
      completionRate: number
      issues?: string[]
      bottlenecks?: string[]
    }>
    overallProgress: number
    isOnTrack: boolean
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    riskFactors: string[]
    recommendations: string[]
  }
  alerts: Array<{
    alertId: string
    alertType:
      | 'DEADLINE_APPROACHING'
      | 'NO_APPLICATIONS'
      | 'LOW_QUALITY_CANDIDATES'
      | 'BUDGET_EXCEEDED'
      | 'TIMELINE_DELAY'
      | 'CUSTOM'
    severity: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL'
    title: string
    message: string
    triggeredAt: string
    isRead: boolean
    isResolved: boolean
    resolvedAt?: string
    resolvedBy?: string
    resolutionNotes?: string
    actionItems?: Array<{
      action: string
      assignee: string
      dueDate: string
      status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
    }>
  }>
  automationRules: Array<{
    ruleId: string
    ruleName: string
    trigger: 'STATUS_CHANGE' | 'TIME_BASED' | 'METRIC_THRESHOLD' | 'APPLICATION_COUNT' | 'CUSTOM'
    conditions: Array<{
      field: string
      operator: 'EQUALS' | 'NOT_EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'CONTAINS' | 'IN_RANGE'

      value: unknown
      logicalOperator?: 'AND' | 'OR'
    }>
    actions: Array<{
      actionType:
        | 'SEND_NOTIFICATION'
        | 'UPDATE_STATUS'
        | 'ASSIGN_TASK'
        | 'TRIGGER_WORKFLOW'
        | 'SEND_EMAIL'
        | 'CUSTOM'
      parameters: Record<string, unknown>
      executionOrder: number
    }>
    isActive: boolean
    createdAt: string
    lastTriggered?: string
  }>
  collaborators: Array<{
    userId: string
    userName: string
    role: 'OWNER' | 'MANAGER' | 'RECRUITER' | 'INTERVIEWER' | 'OBSERVER'
    permissions: string[]
    assignedTasks: Array<{
      taskId: string
      taskName: string
      taskType: string
      dueDate: string
      status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'OVERDUE'
    }>
    lastActivity: string
  }>
  integrations: {
    hrSystemIntegration?: {
      isEnabled: boolean
      systemName: string
      syncStatus: 'SYNCED' | 'SYNCING' | 'ERROR' | 'DISCONNECTED'
      lastSyncAt?: string
      syncErrors?: string[]
    }
    jobBoardsIntegration?: Array<{
      platformName: string
      isActive: boolean
      postingStatus: 'POSTED' | 'PENDING' | 'FAILED' | 'REMOVED'
      postingUrl?: string
      performance: {
        views: number
        applications: number
        clickRate: number
      }
    }>
    applicantTrackingSystem?: {
      isConnected: boolean
      systemName: string
      candidateCount: number
      lastDataRefresh: string
    }
  }
  reportingConfig: {
    reportFrequency: 'DAILY' | 'WEEKLY' | 'MONTHLY'
    recipients: string[]
    includeMetrics: string[]
    customReports: Array<{
      reportId: string
      reportName: string
      reportType: string
      schedule: string
      isActive: boolean
    }>
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

export interface RecruitmentJobStatusChangeLog {
  logId: string
  jobId: string
  trackerId: string
  changeType:
    | 'STATUS_UPDATE'
    | 'METRIC_UPDATE'
    | 'ALERT_TRIGGERED'
    | 'MILESTONE_REACHED'
    | 'DEADLINE_PASSED'
  timestamp: string
  details: {
    field?: string

    oldValue?: unknown

    newValue?: unknown
    changeReason?: string
    automaticChange?: boolean
    impactLevel?: 'LOW' | 'MEDIUM' | 'HIGH'
  }
  context: {
    userAgent?: string
    ipAddress?: string
    sessionId?: string
    workflowId?: string
    integrationSource?: string
  }
  metadata: Record<string, unknown>
}

export interface RecruitmentJobStatusDashboard {
  dashboardId: string
  dashboardName: string
  ownerId: string
  isPublic: boolean
  widgets: Array<{
    widgetId: string
    widgetType:
      | 'METRIC_CARD'
      | 'TIMELINE_CHART'
      | 'FUNNEL_CHART'
      | 'PROGRESS_BAR'
      | 'ALERT_LIST'
      | 'ACTIVITY_FEED'
    title: string
    position: {
      x: number
      y: number
      width: number
      height: number
    }
    configuration: {
      dataSource: string
      filters?: Record<string, unknown>
      timeRange?: {
        start: string
        end: string
        period: 'LAST_7_DAYS' | 'LAST_30_DAYS' | 'LAST_90_DAYS' | 'CUSTOM'
      }
      displayOptions?: Record<string, unknown>
      refreshInterval?: number
    }
    isVisible: boolean
  }>
  filters: {
    departments?: string[]
    positions?: string[]
    statuses?: string[]
    dateRange?: {
      start: string
      end: string
    }
    recruiters?: string[]
    priorities?: string[]
  }
  preferences: {
    autoRefresh: boolean
    refreshInterval: number
    notifications: {
      emailAlerts: boolean
      inAppAlerts: boolean
      smsAlerts: boolean
    }
    themeSettings: {
      colorScheme: 'LIGHT' | 'DARK' | 'AUTO'
      chartColors: string[]
    }
  }
  sharing: {
    isShared: boolean
    sharedWith: Array<{
      userId: string
      userName: string
      permission: 'VIEW' | 'EDIT'
      sharedAt: string
    }>
    publicLink?: {
      isActive: boolean
      linkId: string
      expiresAt?: string
      password?: string
    }
  }
  analytics: {
    viewCount: number
    lastViewed: string
    avgTimeSpent: number
    mostUsedWidgets: Array<{
      widgetId: string
      viewCount: number
      interactionCount: number
    }>
  }
  createdAt: string
  updatedAt: string
}

export interface RecruitmentJobStatusReport {
  reportId: string
  reportName: string
  reportType: 'SUMMARY' | 'DETAILED' | 'COMPARISON' | 'TREND_ANALYSIS' | 'PERFORMANCE' | 'CUSTOM'
  generatedAt: string
  generatedBy: string
  reportPeriod: {
    startDate: string
    endDate: string
    periodType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY' | 'CUSTOM'
  }
  filters: {
    jobIds?: string[]
    departments?: string[]
    positions?: string[]
    statuses?: string[]
    recruiters?: string[]
    priority?: string[]
  }
  summary: {
    totalJobs: number
    activeJobs: number
    completedJobs: number
    averageTimeToFill: number
    averageTimeToHire: number
    averageCostPerHire: number
    totalApplications: number
    totalHires: number
    overallConversionRate: number
  }
  detailedMetrics: {
    statusDistribution: Record<string, number>
    departmentPerformance: Array<{
      department: string
      jobCount: number
      avgTimeToFill: number
      successRate: number
      totalCost: number
    }>
    recruiterPerformance: Array<{
      recruiterId: string
      recruiterName: string
      jobsManaged: number
      avgTimeToFill: number
      successRate: number
      performanceScore: number
    }>
    timelineAnalysis: Array<{
      period: string
      jobsPosted: number
      jobsClosed: number
      avgTimeToFill: number
      trends: string[]
    }>
    channelEffectiveness: Array<{
      channel: string
      totalApplications: number
      qualityScore: number
      conversionRate: number
      costPerApplication: number
    }>
  }
  insights: {
    keyFindings: string[]
    trends: Array<{
      trend: string
      direction: 'IMPROVING' | 'DECLINING' | 'STABLE'
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      recommendation: string
    }>
    bottlenecks: Array<{
      area: string
      description: string
      impact: string
      suggestedActions: string[]
    }>
    successFactors: Array<{
      factor: string
      impact: string
      evidenceData: Record<string, unknown>
    }>
  }
  visualizations: Array<{
    chartId: string
    chartType: 'BAR' | 'LINE' | 'PIE' | 'AREA' | 'SCATTER' | 'FUNNEL' | 'GANTT'
    title: string
    description: string
    dataSource: string
    configuration: Record<string, unknown>
  }>
  recommendations: Array<{
    category: 'PROCESS_IMPROVEMENT' | 'RESOURCE_ALLOCATION' | 'TRAINING' | 'TECHNOLOGY' | 'POLICY'
    priority: 'HIGH' | 'MEDIUM' | 'LOW'
    title: string
    description: string
    expectedImpact: string
    implementationEffort: 'LOW' | 'MEDIUM' | 'HIGH'
    timeline: string
    stakeholders: string[]
  }>
  attachments: Array<{
    fileId: string
    fileName: string
    fileType: 'PDF' | 'EXCEL' | 'CSV' | 'POWERPOINT' | 'IMAGE'
    fileSize: number
    downloadUrl: string
  }>
  exportOptions: {
    supportedFormats: string[]
    lastExported?: {
      format: string
      exportedAt: string
      exportedBy: string
    }
  }
  createdAt: string
  updatedAt: string
}

export interface RecruitmentJobStatusNotification {
  notificationId: string
  jobId: string
  recipientId: string
  recipientType: 'USER' | 'ROLE' | 'DEPARTMENT' | 'SYSTEM'
  notificationType: 'STATUS_CHANGE' | 'ALERT' | 'MILESTONE' | 'DEADLINE' | 'REPORT' | 'REMINDER'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  title: string
  message: string
  content: {
    summary: string
    details?: string
    actionRequired?: boolean
    actionItems?: Array<{
      action: string
      link?: string
      deadline?: string
    }>
    data?: Record<string, unknown>
  }
  channels: Array<{
    channel: 'EMAIL' | 'SMS' | 'IN_APP' | 'WEBHOOK' | 'SLACK' | 'TEAMS'
    status: 'PENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED'
    sentAt?: string
    deliveredAt?: string
    readAt?: string
    errorMessage?: string
    retryCount?: number
  }>
  scheduling: {
    sendImmediately: boolean
    scheduledAt?: string
    timezone: string
    recurring?: {
      isRecurring: boolean
      pattern: 'DAILY' | 'WEEKLY' | 'MONTHLY'
      interval: number
      endDate?: string
    }
  }
  preferences: {
    allowUnsubscribe: boolean
    trackingEnabled: boolean
    personalizedContent: boolean
    languagePreference?: string
  }
  metadata: {
    triggeredBy: string
    triggerEvent: string
    relatedEntities: Record<string, string>
    tags: string[]
  }
  isRead: boolean
  isArchived: boolean
  readAt?: string
  archivedAt?: string
  createdAt: string
  updatedAt: string
}

// 职位效果分析相关接口定义
export interface RecruitmentJobEffectivenessAnalysis {
  analysisId: string
  jobId: string
  jobTitle: string
  department: string
  position: string
  analysisType: 'REAL_TIME' | 'PERIODIC' | 'CUSTOM' | 'COMPARATIVE' | 'PREDICTIVE'
  analysisPeriod: {
    startDate: string
    endDate: string
    periodType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY' | 'CUSTOM'
    isCompleted: boolean
  }
  performanceMetrics: {
    visibility: {
      totalViews: number
      uniqueViews: number
      viewsGrowthRate: number
      avgViewDuration: number
      bounceRate: number
      viewsBySource: Record<string, number>
      geographicDistribution: Array<{
        region: string
        viewCount: number
        percentage: number
      }>
      deviceBreakdown: {
        desktop: number
        mobile: number
        tablet: number
      }
      timeDistribution: Array<{
        hour: number
        viewCount: number
        conversionRate: number
      }>
    }
    attraction: {
      totalApplications: number
      qualifiedApplications: number
      applicationConversionRate: number
      applicationGrowthRate: number
      avgApplicationsPerDay: number
      applicationsBySource: Record<
        string,
        {
          count: number
          qualityScore: number
          conversionRate: number
        }
      >
      candidateQuality: {
        skillMatchScore: number
        experienceMatchScore: number
        educationMatchScore: number
        overallQualityScore: number
      }
      applicationTrends: Array<{
        date: string
        applications: number
        qualityScore: number
      }>
    }
    engagement: {
      interviewScheduled: number
      interviewCompleted: number
      interviewShowUpRate: number
      avgInterviewScore: number
      candidateFeedbackScore: number
      interviewerFeedbackScore: number
      engagementStages: Array<{
        stage: 'APPLICATION' | 'SCREENING' | 'INTERVIEW' | 'ASSESSMENT' | 'FINAL'
        conversionRate: number
        avgDuration: number
        dropOffRate: number
        satisfactionScore: number
      }>
    }
    conversion: {
      offersExtended: number
      offersAccepted: number
      offerAcceptanceRate: number
      finalHires: number
      overallConversionRate: number
      timeToHire: number
      costPerHire: number
      conversionFunnel: Array<{
        stage: string
        candidates: number
        conversionRate: number
        avgTimeInStage: number
      }>
      benchmarkComparison: {
        industryAvgConversionRate: number
        companyAvgConversionRate: number
        similarPositionsAvgRate: number
        performanceRank: number
      }
    }
    retention: {
      newHireRetentionRate30Days: number
      newHireRetentionRate90Days: number
      newHireRetentionRate1Year: number
      probationPassRate: number
      performanceRating: number
      retentionPredictionScore: number
      earlyWarningIndicators: Array<{
        indicator: string
        risk: 'LOW' | 'MEDIUM' | 'HIGH'
        recommendation: string
      }>
    }
  }
  costAnalysis: {
    totalRecruitmentCost: number
    costBreakdown: {
      jobPostingCosts: number
      sourcingCosts: number
      screeningCosts: number
      interviewCosts: number
      assessmentCosts: number
      offerProcessingCosts: number
      systemCosts: number
      recruiterTimeCosts: number
      managerTimeCosts: number
      travelCosts: number
      relocationCosts: number
      signingBonuses: number
      recruitmentAgencyFees: number
      backgroundCheckCosts: number
      otherCosts: number
    }
    costEfficiency: {
      costPerView: number
      costPerApplication: number
      costPerQualifiedCandidate: number
      costPerInterview: number
      costPerOffer: number
      costPerHire: number
      returnOnInvestment: number
      budgetUtilization: number
    }
    costComparison: {
      industryBenchmark: number
      companyAverage: number
      previousPeriodCost: number
      budgetVariance: number
      costTrend: 'INCREASING' | 'DECREASING' | 'STABLE'
    }
    costOptimization: Array<{
      area: string
      currentCost: number
      potentialSaving: number
      optimizationActions: string[]
      implementationEffort: 'LOW' | 'MEDIUM' | 'HIGH'
      expectedROI: number
    }>
  }
  channelEffectiveness: Array<{
    channelId: string
    channelName: string
    channelType:
      | 'JOB_BOARD'
      | 'SOCIAL_MEDIA'
      | 'COMPANY_WEBSITE'
      | 'EMPLOYEE_REFERRAL'
      | 'RECRUITMENT_AGENCY'
      | 'CAMPUS_RECRUITING'
      | 'PROFESSIONAL_NETWORK'
      | 'OTHER'
    performance: {
      views: number
      applications: number
      qualifiedCandidates: number
      interviews: number
      hires: number
      conversionRates: {
        viewToApplication: number
        applicationToQualified: number
        qualifiedToInterview: number
        interviewToHire: number
        overallConversion: number
      }
    }
    costs: {
      totalCost: number
      costPerView: number
      costPerApplication: number
      costPerHire: number
    }
    quality: {
      candidateQualityScore: number
      timeToFill: number
      retentionRate: number
      performanceRating: number
    }
    effectiveness: {
      efficiencyScore: number
      qualityScore: number
      costEffectivenessScore: number
      overallEffectivenessScore: number
      recommendation: 'INCREASE_INVESTMENT' | 'MAINTAIN' | 'REDUCE_INVESTMENT' | 'DISCONTINUE'
    }
    trends: Array<{
      period: string
      views: number
      applications: number
      hires: number
      cost: number
      effectiveness: number
    }>
  }>
  timeAnalysis: {
    recruitmentTimeline: {
      totalDuration: number
      plannedDuration: number
      timeVariance: number
      isOnSchedule: boolean
    }
    stageAnalysis: Array<{
      stage: string
      avgDuration: number
      maxDuration: number
      minDuration: number
      standardDeviation: number
      benchmarkDuration: number
      efficiency: 'EFFICIENT' | 'AVERAGE' | 'SLOW'
      bottlenecks: string[]
      improvementSuggestions: string[]
    }>
    timeToFillAnalysis: {
      actualTimeToFill: number
      targetTimeToFill: number
      industryBenchmark: number
      companyAverage: number
      percentile: number
      trend: 'IMPROVING' | 'DECLINING' | 'STABLE'
    }
    urgencyImpact: {
      urgencyLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
      impactOnTimeline: number
      impactOnQuality: number
      impactOnCost: number
      urgencyOptimization: string[]
    }
  }
  qualityAssessment: {
    candidateQuality: {
      skillAlignment: number
      experienceRelevance: number
      culturalFit: number
      motivationLevel: number
      growthPotential: number
      overallQualityScore: number
    }
    sourceQuality: Record<
      string,
      {
        qualityScore: number
        consistencyScore: number
        improvementTrend: 'IMPROVING' | 'DECLINING' | 'STABLE'
        strengthAreas: string[]
        improvementAreas: string[]
      }
    >
    hiringManagerSatisfaction: {
      satisfactionScore: number
      feedbackComments: string[]
      recommendationLikelihood: number
      processImprovement: string[]
    }
    candidateExperience: {
      applicationExperienceScore: number
      interviewExperienceScore: number
      communicationScore: number
      timelinessScore: number
      overallExperienceScore: number
      netPromoterScore: number
      feedbackThemes: Array<{
        theme: string
        frequency: number
        sentiment: 'POSITIVE' | 'NEUTRAL' | 'NEGATIVE'
      }>
    }
  }
  marketAnalysis: {
    competitivePositioning: {
      marketDemandLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH'
      competitionIntensity: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH'
      talentAvailability: 'ABUNDANT' | 'ADEQUATE' | 'LIMITED' | 'SCARCE'
      salaryCompetitiveness: number
      benefitsCompetitiveness: number
      brandAttractiveness: number
    }
    industryBenchmarks: {
      avgTimeToFill: number
      avgCostPerHire: number
      avgConversionRate: number
      avgCandidateExperience: number
      marketPosition: 'LEADER' | 'ABOVE_AVERAGE' | 'AVERAGE' | 'BELOW_AVERAGE' | 'LAGGARD'
    }
    marketTrends: Array<{
      trend: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      direction: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL'
      recommendation: string
      timeline: string
    }>
    competitorAnalysis: Array<{
      competitor: string
      advantages: string[]
      disadvantages: string[]
      differentiators: string[]
      actionableInsights: string[]
    }>
  }
  performancePrediction: {
    predictiveModels: Array<{
      modelId: string
      modelType: 'TIME_SERIES' | 'REGRESSION' | 'CLASSIFICATION' | 'ENSEMBLE'
      predictionTarget:
        | 'APPLICATION_VOLUME'
        | 'HIRE_SUCCESS'
        | 'TIME_TO_FILL'
        | 'COST_PROJECTION'
        | 'QUALITY_FORECAST'
      accuracy: number
      confidence: number
      predictions: Array<{
        period: string
        predictedValue: number
        confidenceInterval: {
          lower: number
          upper: number
        }
        factors: Array<{
          factor: string
          influence: number
        }>
      }>
    }>
    scenarioAnalysis: Array<{
      scenarioId: string
      scenarioName: string
      assumptions: Record<string, unknown>
      outcomes: {
        expectedApplications: number
        expectedHires: number
        expectedCost: number
        expectedTimeToFill: number
        expectedQuality: number
      }
      probability: number
      riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
      contingencyPlans: string[]
    }>
    recommendations: Array<{
      category: 'SOURCING' | 'PROCESS' | 'BUDGET' | 'TIMELINE' | 'QUALITY'
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      recommendation: string
      expectedImpact: string
      implementationEffort: 'LOW' | 'MEDIUM' | 'HIGH'
      timeline: string
      successMetrics: string[]
    }>
  }
  reporting: {
    executiveSummary: {
      overallEffectiveness: 'EXCELLENT' | 'GOOD' | 'AVERAGE' | 'POOR' | 'CRITICAL'
      keySuccessFactors: string[]
      majorChallenges: string[]
      strategicRecommendations: string[]
      budgetImpact: string
      timelineImpact: string
    }
    detailedFindings: Array<{
      category: string
      finding: string
      evidence: string[]
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      actionRequired: boolean
      recommendedActions: string[]
    }>
    benchmarkResults: {
      industryComparison: Record<
        string,
        {
          value: number
          benchmark: number
          variance: number
          ranking: number
        }
      >
      historicalComparison: Record<
        string,
        {
          currentValue: number
          previousValue: number
          changePercent: number
          trend: 'IMPROVING' | 'DECLINING' | 'STABLE'
        }
      >
    }
    visualizations: Array<{
      chartId: string
      chartType: 'FUNNEL' | 'TREND' | 'COMPARISON' | 'HEATMAP' | 'SCATTER' | 'GAUGE' | 'WATERFALL'
      title: string
      description: string
      dataSource: string
      configuration: Record<string, unknown>
    }>
  }
  alerts: Array<{
    alertId: string
    alertType:
      | 'PERFORMANCE_DECLINE'
      | 'COST_OVERRUN'
      | 'TIMELINE_DELAY'
      | 'QUALITY_ISSUE'
      | 'ANOMALY_DETECTED'
    severity: 'INFO' | 'WARNING' | 'CRITICAL'
    title: string
    description: string
    triggeredAt: string
    metrics: Array<{
      metric: string
      currentValue: number
      thresholdValue: number
      variance: number
    }>
    recommendations: string[]
    isResolved: boolean
  }>
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

export interface RecruitmentJobEffectivenessReport {
  reportId: string
  reportName: string
  reportType:
    | 'EXECUTIVE_SUMMARY'
    | 'DETAILED_ANALYSIS'
    | 'COMPARATIVE_STUDY'
    | 'TREND_ANALYSIS'
    | 'ROI_ANALYSIS'
    | 'CHANNEL_PERFORMANCE'
    | 'CUSTOM'
  generatedAt: string
  generatedBy: string
  reportPeriod: {
    startDate: string
    endDate: string
    periodType: 'MONTHLY' | 'QUARTERLY' | 'YEARLY' | 'CUSTOM'
  }
  scope: {
    jobIds: string[]
    departments: string[]
    positions: string[]
    locations: string[]
    recruiterIds: string[]
  }
  executiveSummary: {
    totalJobs: number
    totalApplications: number
    totalHires: number
    avgTimeToFill: number
    avgCostPerHire: number
    overallROI: number
    effectivenessScore: number
    keyInsights: string[]
    strategicRecommendations: string[]
  }
  performanceAnalysis: {
    topPerformingJobs: Array<{
      jobId: string
      jobTitle: string
      effectivenessScore: number
      keySuccessFactors: string[]
    }>
    underPerformingJobs: Array<{
      jobId: string
      jobTitle: string
      effectivenessScore: number
      identifiedIssues: string[]
      improvementRecommendations: string[]
    }>
    channelPerformance: Array<{
      channel: string
      totalCost: number
      totalHires: number
      costPerHire: number
      qualityScore: number
      effectiveness: number
      recommendation: string
    }>
    timelineAnalysis: {
      onTimeJobs: number
      delayedJobs: number
      avgDelay: number
      commonBottlenecks: string[]
      processImprovements: string[]
    }
  }
  financialAnalysis: {
    totalInvestment: number
    costBreakdown: Record<string, number>
    costTrends: Array<{
      period: string
      cost: number
      hires: number
      costPerHire: number
    }>
    budgetVariance: {
      plannedBudget: number
      actualSpend: number
      variance: number
      variancePercent: number
    }
    roi: {
      totalROI: number
      roiByChannel: Record<string, number>
      roiTrends: Array<{
        period: string
        roi: number
      }>
    }
    costOptimization: Array<{
      area: string
      currentCost: number
      optimizedCost: number
      potentialSaving: number
      implementationPlan: string[]
    }>
  }
  qualityAnalysis: {
    overallQualityScore: number
    qualityTrends: Array<{
      period: string
      qualityScore: number
      retentionRate: number
      performanceRating: number
    }>
    sourceQualityRanking: Array<{
      source: string
      qualityScore: number
      retentionRate: number
      performanceRating: number
      rank: number
    }>
    qualityFactors: Array<{
      factor: string
      impact: number
      correlation: number
      recommendation: string
    }>
  }
  benchmarkAnalysis: {
    industryComparison: Record<
      string,
      {
        ourValue: number
        industryAverage: number
        topPerformer: number
        ourRanking: number
      }
    >
    competitivePosition: {
      overallPosition: 'LEADER' | 'CHALLENGER' | 'FOLLOWER' | 'NICHE'
      strengthAreas: string[]
      improvementAreas: string[]
      competitiveAdvantages: string[]
    }
    marketInsights: Array<{
      insight: string
      relevance: 'HIGH' | 'MEDIUM' | 'LOW'
      actionability: 'IMMEDIATE' | 'SHORT_TERM' | 'LONG_TERM'
      recommendation: string
    }>
  }
  predictiveInsights: {
    forecastAccuracy: number
    predictions: Array<{
      metric: string
      currentValue: number
      predictedValue: number
      predictionPeriod: string
      confidence: number
      factors: string[]
    }>
    scenarioPlanning: Array<{
      scenario: string
      probability: number
      impact: string
      preparedness: string[]
    }>
    riskAssessment: Array<{
      risk: string
      probability: 'LOW' | 'MEDIUM' | 'HIGH'
      impact: 'LOW' | 'MEDIUM' | 'HIGH'
      mitigation: string[]
    }>
  }
  actionPlan: {
    immediateActions: Array<{
      action: string
      owner: string
      deadline: string
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      expectedOutcome: string
    }>
    shortTermInitiatives: Array<{
      initiative: string
      timeline: string
      resources: string[]
      expectedROI: number
      successMetrics: string[]
    }>
    longTermStrategy: Array<{
      strategy: string
      timeline: string
      investment: number
      expectedImpact: string
      milestones: string[]
    }>
  }
  attachments: Array<{
    fileId: string
    fileName: string
    fileType: 'CHART' | 'DATA_EXPORT' | 'PRESENTATION' | 'DETAILED_ANALYSIS'
    fileSize: number
    downloadUrl: string
  }>
  distributionList: Array<{
    recipientId: string
    recipientName: string
    role: string
    deliveryMethod: 'EMAIL' | 'DASHBOARD' | 'API'
    deliveryStatus: 'PENDING' | 'DELIVERED' | 'FAILED'
  }>
  createdAt: string
  updatedAt: string
}

export interface RecruitmentJobEffectivenessMetric {
  metricId: string
  metricName: string
  metricType:
    | 'PERFORMANCE'
    | 'COST'
    | 'QUALITY'
    | 'TIME'
    | 'ENGAGEMENT'
    | 'SATISFACTION'
    | 'RETENTION'
  category: 'PRIMARY' | 'SECONDARY' | 'DERIVED' | 'BENCHMARK'
  dataType: 'NUMERIC' | 'PERCENTAGE' | 'RATIO' | 'INDEX' | 'SCORE'
  calculationMethod: string
  formulaDefinition: string
  dataSources: string[]
  updateFrequency: 'REAL_TIME' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'MONTHLY'
  benchmarkValues: {
    industryBenchmark?: number
    companyBenchmark?: number
    targetValue?: number
    thresholds: {
      excellent: number
      good: number
      average: number
      poor: number
    }
  }
  historicalData: Array<{
    period: string
    value: number
    trend: 'UP' | 'DOWN' | 'STABLE'
    context: string
  }>
  relatedMetrics: string[]
  businessImpact: {
    impactLevel: 'HIGH' | 'MEDIUM' | 'LOW'
    impactDescription: string
    stakeholders: string[]
    decisionInfluence: string
  }
  qualityIndicators: {
    dataQuality: number
    completeness: number
    accuracy: number
    timeliness: number
    consistency: number
  }
  alertRules: Array<{
    ruleId: string
    condition: string
    threshold: number
    severity: 'WARNING' | 'CRITICAL'
    action: string
  }>
  isActive: boolean
  tags: string[]
  metadata: Record<string, unknown>
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

export interface RecruitmentJobEffectivenessBenchmark {
  benchmarkId: string
  benchmarkName: string
  benchmarkType: 'INDUSTRY' | 'COMPANY' | 'ROLE' | 'LOCATION' | 'SIZE' | 'SECTOR' | 'CUSTOM'
  scope: {
    industry?: string[]
    companySize?: 'STARTUP' | 'SMB' | 'ENTERPRISE' | 'LARGE_ENTERPRISE'
    geography?: string[]
    roles?: string[]
    timeframe: {
      startDate: string
      endDate: string
    }
  }
  metrics: Record<
    string,
    {
      value: number
      percentile25: number
      percentile50: number
      percentile75: number
      percentile90: number
      sampleSize: number
      dataSource: string
      reliability: 'HIGH' | 'MEDIUM' | 'LOW'
    }
  >
  comparisons: Array<{
    metric: string
    ourValue: number
    benchmarkValue: number
    variance: number
    percentileRank: number
    performance: 'EXCELLENT' | 'ABOVE_AVERAGE' | 'AVERAGE' | 'BELOW_AVERAGE' | 'POOR'
    recommendation: string
  }>
  insights: Array<{
    insight: string
    category: 'STRENGTH' | 'OPPORTUNITY' | 'THREAT' | 'WEAKNESS'
    evidence: string[]
    actionability: string
  }>
  dataQuality: {
    completeness: number
    accuracy: number
    recency: number
    representativeness: number
    overallQuality: 'HIGH' | 'MEDIUM' | 'LOW'
  }
  sources: Array<{
    sourceId: string
    sourceName: string
    sourceType: 'INTERNAL' | 'INDUSTRY_REPORT' | 'SURVEY' | 'THIRD_PARTY' | 'GOVERNMENT'
    credibility: number
    lastUpdated: string
  }>
  validityPeriod: {
    validFrom: string
    validTo: string
    reviewSchedule: 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY'
  }
  accessControls: {
    visibility: 'PUBLIC' | 'INTERNAL' | 'RESTRICTED'
    authorizedRoles: string[]
    dataClassification: 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED'
  }
  usage: {
    totalViews: number
    activeUsers: number
    lastAccessed: string
    popularMetrics: string[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 招聘渠道
export interface RecruitmentChannel {
  id: number
  name: string
  type: 'online' | 'campus' | 'social' | 'headhunter' | 'internal' | 'other'
  status: 'active' | 'inactive'
  description?: string
  contact: {
    person?: string
    phone?: string
    email?: string
    address?: string
  }
  contract: {
    startDate?: string
    endDate?: string
    terms?: string
    cost?: number
    paymentTerms?: string
  }
  performanceTarget?: {
    monthlyResumes?: number
    monthlyInterviews?: number
    monthlyHires?: number
    conversionRate?: number
    qualityScore?: number
  }
  statistics?: {
    totalResumes: number
    totalInterviews: number
    totalHires: number
    activePositions: number
    avgTimeToHire: number
    satisfaction: number
  }
  config?: {
    apiIntegration?: {
      enabled: boolean
      apiUrl?: string
      apiKey?: string
      syncInterval?: number
    }
    autoPosting?: {
      enabled: boolean
      templates?: Record<string, unknown>
    }
    notifications?: {
      email?: boolean
      webhook?: boolean
      webhookUrl?: string
    }
  }
  evaluation?: {
    lastEvaluationDate?: string
    overallRating?: number
    costEffectiveness?: number
    qualityRating?: number
  }
  createdAt: string
  updatedAt: string
}

// 渠道统计数据
export interface ChannelStatistics {
  channelId: number
  channelName: string
  period: {
    start: string
    end: string
  }
  metrics: {
    resumes: {
      total: number
      qualified: number
      disqualified: number
      pending: number
    }
    interviews: {
      scheduled: number
      completed: number
      passed: number
      failed: number
    }
    offers: {
      extended: number
      accepted: number
      rejected: number
      negotiating: number
    }
    hires: {
      total: number
      stillEmployed: number
      resigned: number
      avgTenure: number
    }
  }
  conversion: {
    resumeToInterview: number
    interviewToOffer: number
    offerToHire: number
    overallConversion: number
  }
  cost: {
    total: number
    perResume: number
    perInterview: number
    perHire: number
    roi: number
  }
  quality: {
    avgCandidateScore: number
    avgInterviewScore: number
    avgPerformanceRating: number
    retentionRate: number
  }
  timeline: {
    avgResumeResponse: number
    avgSchedulingTime: number
    avgTimeToOffer: number
    avgTimeToHire: number
  }
  trends: Array<{
    date: string
    resumes: number
    interviews: number
    hires: number
    cost: number
  }>
}

// 面试安排
export interface InterviewArrangement {
  id: number
  candidateId: number
  candidateName: string
  candidatePhone?: string
  candidateEmail?: string
  positionId: number
  positionName: string
  departmentName: string
  round: number
  type: 'phone' | 'video' | 'onsite' | 'assessment'
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'rescheduled' | 'no_show'
  scheduledTime: string
  actualStartTime?: string
  actualEndTime?: string
  duration: number
  location?: string
  meetingUrl?: string
  roomId?: number
  roomName?: string
  interviewers: Array<{
    id: number
    name: string
    role: string
    department: string
    email: string
    phone?: string
    isPrimary: boolean
    attendance?: 'attended' | 'absent' | 'late'
  }>
  topics?: string[]
  requirements?: string
  preparations?: string
  result?: {
    decision: 'pass' | 'fail' | 'pending' | 'strong_pass' | 'strong_fail'
    nextRound?: boolean
    feedback?: string
    score?: number
  }
  notifications?: {
    candidateNotified: boolean
    candidateNotifiedAt?: string
    interviewersNotified: boolean
    interviewersNotifiedAt?: string
    remindersSent: boolean
    remindersSentAt?: string
  }
  cancellation?: {
    reason: string
    cancelledBy: string
    cancelledAt: string
  }
  rescheduling?: {
    originalTime: string
    reason: string
    rescheduledBy: string
    rescheduledAt: string
  }
  attachments?: Array<{
    id: number
    name: string
    type: string
    size: number
    url: string
  }>
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 面试评价
export interface InterviewEvaluation {
  id: number
  arrangementId: number
  candidateId: number
  candidateName: string
  positionId: number
  positionName: string
  round: number
  interviewType: string
  interviewerId: number
  interviewerName: string
  interviewerTitle: string
  evaluationDate: string
  completionTime: number
  dimensions: Array<{
    dimensionId: number
    dimensionName: string
    category: string
    score: number
    maxScore: number
    weight: number
    comment?: string
    examples?: string[]
  }>
  overallScore: number
  percentileRank?: number
  decision: 'strong_pass' | 'pass' | 'pending' | 'fail' | 'strong_fail'
  strengths?: string[]
  weaknesses?: string[]
  summary: string
  recommendation?: string
  nextSteps?: string
  questions?: Array<{
    question: string
    answer: string
    score?: number
  }>
  competencies?: Array<{
    name: string
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
    evidence: string
  }>
  cultureFit?: {
    score: number
    assessment: string
  }
  riskFactors?: Array<{
    factor: string
    severity: 'low' | 'medium' | 'high'
    mitigation?: string
  }>
  comparisons?: {
    vsRequirements?: {
      match: number
      gaps: string[]
    }
    vsPeers?: {
      rank: number
      total: number
    }
  }
  attachments?: Array<{
    id: number
    name: string
    type: string
    size: number
    url: string
  }>
  calibrated?: boolean
  calibrationNotes?: string
  visibility?: 'private' | 'hr_only' | 'panel' | 'public'
  createdAt: string
  updatedAt: string
  submittedAt?: string
}

// 面试官
export interface Interviewer {
  id: number
  employeeId: number
  employeeName: string
  employeeCode: string
  department: string
  position: string
  email: string
  phone?: string
  status: 'active' | 'inactive' | 'busy'
  expertise: string[]
  interviewTypes: Array<'phone' | 'video' | 'onsite' | 'assessment'>
  positionTypes: string[]
  level: 'junior' | 'intermediate' | 'senior' | 'expert'
  experience: {
    totalInterviews: number
    yearsAsInterviewer: number
    specializations: string[]
  }
  availability: {
    maxInterviewsPerWeek: number
    currentWeekLoad: number
    regularHours: {
      monday?: Array<{ start: string; end: string }>
      tuesday?: Array<{ start: string; end: string }>
      wednesday?: Array<{ start: string; end: string }>
      thursday?: Array<{ start: string; end: string }>
      friday?: Array<{ start: string; end: string }>
      saturday?: Array<{ start: string; end: string }>
      sunday?: Array<{ start: string; end: string }>
    }
    blackoutDates?: Array<{
      start: string
      end: string
      reason?: string
    }>
    nextAvailable?: string
  }
  performance?: {
    rating: number
    completionRate: number
    punctualityRate: number
    feedbackQuality: number
    candidateSatisfaction: number
    hiringSuccessRate: number
    lastEvaluated?: string
  }
  certifications?: Array<{
    name: string
    issuer: string
    issueDate: string
    expiryDate?: string
    credentialId?: string
  }>
  training?: {
    completed: number
    required: number
    inProgress: number
    lastTrainingDate?: string
  }
  preferences?: {
    preferredTimes?: string[]
    preferredTypes?: string[]
    preferredLevels?: string[]
    languages?: string[]
    notes?: string
  }
  statistics?: {
    thisMonth: {
      scheduled: number
      completed: number
      cancelled: number
      hours: number
    }
    thisYear: {
      total: number
      byType: Record<string, number>
      byPosition: Record<string, number>
    }
  }
  createdAt: string
  updatedAt: string
  activatedAt?: string
  deactivatedAt?: string
}
