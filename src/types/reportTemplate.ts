// 报表模板相关类型定义

export interface ReportTemplate {
  id: string
  name: string
  description?: string
  type: 'table' | 'chart' | 'dashboard' | 'mixed'
  categoryId: string
  categoryName?: string
  version: string
  status: 'draft' | 'published' | 'archived' | 'deprecated'
  config: TemplateConfig
  thumbnail?: string
  tags?: string[]
  usageCount: number
  parameters?: TemplateParameter[]
  createdBy: string
  createdByName?: string
  createdAt: Date
  updatedBy: string
  updatedByName?: string
  updatedAt: Date
}

export interface TemplateConfig {
  dataSource?: DataSourceConfig
  widgets?: Widget[]
  layout?: LayoutConfig
  statistics?: StatisticConfig[]
  filters?: FilterConfig[]
  styles?: StyleConfig
  permissions?: PermissionConfig
}

export interface DataSourceConfig {
  type: 'table' | 'sql' | 'api'
  tables?: TableConfig[]
  sql?: string
  api?: ApiConfig
  relations?: RelationConfig[]
  fields?: FieldConfig[]
}

export interface TableConfig {
  name: string
  alias?: string
  fields: string[]
}

export interface ApiConfig {
  url: string
  method: 'GET' | 'POST'
  headers?: Record<string, string>
  params?: Record<string, unknown>
}

export interface RelationConfig {
  type: 'inner' | 'left' | 'right'
  leftTable: string
  leftField: string
  rightTable: string
  rightField: string
}

export interface FieldConfig {
  name: string
  alias?: string
  type: 'string' | 'number' | 'date' | 'boolean'
  format?: string
}

export interface Widget {
  id: string
  type: string
  position: {
    x: number
    y: number
    w: number
    h: number
  }
  props: Record<string, unknown>
  dataBinding?: DataBinding
  style?: Record<string, unknown>
}

export interface DataBinding {
  dataSource?: string
  statistic?: string
  fields?: string[]
}

export interface LayoutConfig {
  type: 'grid' | 'flex' | 'absolute'
  columns?: number
  rowHeight?: number
  margin?: number[]
  containerWidth?: number
}

export interface StatisticConfig {
  id: string
  name: string
  type: 'aggregation' | 'calculation' | 'custom'
  formula?: string
  aggregateFunction?: string
  aggregateField?: string
  groupBy?: GroupByConfig[]
  format?: FormatConfig
}

export interface GroupByConfig {
  field: string
  type: 'field' | 'date' | 'custom'
  dateGranularity?: 'year' | 'quarter' | 'month' | 'week' | 'day'
  customRule?: string
}

export interface FormatConfig {
  type: 'default' | 'integer' | 'decimal' | 'percentage' | 'currency'
  precision?: number
  thousandSeparator?: boolean
  prefix?: string
  suffix?: string
}

export interface FilterConfig {
  type: 'group'
  connector: 'AND' | 'OR'
  conditions: FilterCondition[]
}

export interface FilterCondition {
  field: string
  operator: string

  value: unknown
}

export interface StyleConfig {
  theme?: string
  colors?: string[]
  fontSize?: number
  fontFamily?: string
  background?: string
}

export interface PermissionConfig {
  viewRoles?: string[]
  editRoles?: string[]
  dataPermissions?: DataPermission[]
}

export interface DataPermission {
  type: 'row' | 'column'
  field?: string
  condition?: FilterCondition
}

export interface TemplateParameter {
  name: string
  label: string
  type: 'string' | 'number' | 'date' | 'select' | 'multiselect'
  required?: boolean

  defaultValue?: unknown

  options?: Array<{ label: string; value: unknown }>
  validation?: ValidationRule
}

export interface ValidationRule {
  pattern?: string
  min?: number
  max?: number
  message?: string
}

export interface TemplateCategory {
  id: string
  name: string
  parentId?: string
  icon?: string
  sortOrder: number
  description?: string
}

export interface TemplateVersion {
  id: string
  templateId: string
  version: string
  config: TemplateConfig
  changeLog?: string
  changes?: string[]
  isCurrent: boolean
  createdBy: string
  createdByName?: string
  createdAt: Date
}

export interface TemplateSearchParams {
  page?: number
  pageSize?: number
  keyword?: string
  categoryId?: string
  type?: string
  status?: string
  createdBy?: string
  tags?: string[]
}

export interface TemplateListResult {
  list: ReportTemplate[]
  total: number
  page: number
  pageSize: number
}
