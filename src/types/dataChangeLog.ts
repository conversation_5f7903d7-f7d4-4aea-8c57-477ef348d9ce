/**
import type { SpringPageData } from '@/types/common/api'
 * 数据变更日志模块 TypeScript 类型定义
 *
 * 此文件定义了与后端 DataChangeLogService 完全兼容的 TypeScript 类型
 * 确保前后端数据结构100%匹配
 *
 * <AUTHOR>
 * @since 1.0.0
 */

// ==================== 枚举类型定义 ====================

/**
 * 操作类型枚举
 */
export enum OperationType {
  INSERT = 'INSERT', // 新增
  UPDATE = 'UPDATE', // 更新
  DELETE = 'DELETE', // 删除
  SELECT = 'SELECT', // 查询
  BATCH_INSERT = 'BATCH_INSERT', // 批量新增
  BATCH_UPDATE = 'BATCH_UPDATE', // 批量更新
  BATCH_DELETE = 'BATCH_DELETE' // 批量删除
}

/**
 * 变更类型枚举
 */
export enum ChangeType {
  CREATE = 'CREATE', // 创建
  MODIFY = 'MODIFY', // 修改
  DELETE = 'DELETE', // 删除
  QUERY = 'QUERY', // 查询
  IMPORT = 'IMPORT', // 导入
  EXPORT = 'EXPORT', // 导出
  BATCH_OPERATION = 'BATCH_OPERATION' // 批量操作
}

/**
 * 操作人类型枚举
 */
export enum OperatorType {
  USER = 'USER', // 用户
  SYSTEM = 'SYSTEM', // 系统
  ADMIN = 'ADMIN', // 管理员
  API = 'API', // API
  SCHEDULED_TASK = 'SCHEDULED_TASK' // 定时任务
}

/**
 * 数据状态枚举
 */
export enum DataStatus {
  NORMAL = 'NORMAL', // 正常
  DELETED = 'DELETED', // 已删除
  ARCHIVED = 'ARCHIVED', // 已归档
  LOCKED = 'LOCKED' // 已锁定
}

/**
 * 风险等级枚举
 */
export enum RiskLevel {
  LOW = 'LOW', // 低
  MEDIUM = 'MEDIUM', // 中
  HIGH = 'HIGH', // 高
  CRITICAL = 'CRITICAL' // 紧急
}

/**
 * 排序方向枚举
 */
export enum SortDirection {
  ASC = 'ASC', // 升序
  DESC = 'DESC' // 降序
}

/**
 * 字段变更类型枚举
 */
export enum FieldChangeType {
  ADDED = 'ADDED', // 新增字段
  MODIFIED = 'MODIFIED', // 修改字段
  REMOVED = 'REMOVED' // 删除字段
}

// ==================== 基础实体类型 ====================

/**
 * 数据变更日志实体类型（与后端 DataChangeLog 实体完全匹配）
 */
export interface DataChangeLog {
  /** 主键ID */
  id?: string

  /** 表名 */
  tableName: string

  /** 表注释 */
  tableComment?: string

  /** 记录ID */
  recordId: string

  /** 操作类型 */
  operationType: OperationType

  /** 操作类型名称（用于显示） */
  operationTypeName?: string

  /** 变更类型 */
  changeType: ChangeType

  /** 变更类型名称（用于显示） */
  changeTypeName?: string

  /** 操作人ID */
  operatorId?: string

  /** 操作人姓名 */
  operatorName?: string

  /** 操作人类型 */
  operatorType?: OperatorType

  /** 操作人类型名称（用于显示） */
  operatorTypeName?: string

  /** 变更前数据 */
  beforeData?: string

  /** 变更后数据 */
  afterData?: string

  /** 变更字段 */
  changedFields?: string

  /** 变更描述 */
  changeDescription?: string

  /** 业务模块 */
  businessModule?: string

  /** 业务模块名称（用于显示） */
  businessModuleName?: string

  /** 请求ID */
  requestId?: string

  /** 会话ID */
  sessionId?: string

  /** IP地址 */
  ipAddress?: string

  /** 用户代理 */
  userAgent?: string

  /** 请求URL */
  requestUrl?: string

  /** 请求方法 */
  requestMethod?: string

  /** 请求参数 */
  requestParams?: string

  /** 响应状态码 */
  responseStatus?: number

  /** 响应消息 */
  responseMessage?: string

  /** 执行时间（毫秒） */
  executionTime?: number

  /** 数据状态 */
  dataStatus?: DataStatus

  /** 数据状态名称（用于显示） */
  dataStatusName?: string

  /** 风险等级 */
  riskLevel?: RiskLevel

  /** 风险等级名称（用于显示） */
  riskLevelName?: string

  /** 是否成功 */
  isSuccess?: boolean

  /** 错误消息 */
  errorMessage?: string

  /** 回滚ID */
  rollbackId?: string

  /** 父日志ID */
  parentLogId?: string

  /** 批次ID */
  batchId?: string

  /** 标签 */
  tags?: string

  /** 备注 */
  remarks?: string

  /** 创建时间 */
  createTime?: string

  /** 创建人 */
  createBy?: string

  /** 更新时间 */
  updateTime?: string

  /** 更新人 */
  updateBy?: string
}

// ==================== 请求类型定义 ====================

/**
 * 数据变更日志查询请求类型（与后端 DataChangeLogQueryRequest 完全匹配）
 */
export interface DataChangeLogQueryRequest {
  /** 页码（从0开始） */
  page?: number

  /** 页大小 */
  size?: number

  /** 关键词搜索 */
  keyword?: string

  /** 表名 */
  tableName?: string

  /** 表名列表 */
  tableNames?: string[]

  /** 记录ID */
  recordId?: string

  /** 记录ID列表 */
  recordIds?: string[]

  /** 操作类型 */
  operationType?: OperationType

  /** 操作类型列表 */
  operationTypes?: OperationType[]

  /** 变更类型 */
  changeType?: ChangeType

  /** 变更类型列表 */
  changeTypes?: ChangeType[]

  /** 操作人ID */
  operatorId?: string

  /** 操作人ID列表 */
  operatorIds?: string[]

  /** 操作人姓名 */
  operatorName?: string

  /** 操作人类型 */
  operatorType?: OperatorType

  /** 操作人类型列表 */
  operatorTypes?: OperatorType[]

  /** 业务模块 */
  businessModule?: string

  /** 业务模块列表 */
  businessModules?: string[]

  /** 数据状态 */
  dataStatus?: DataStatus

  /** 数据状态列表 */
  dataStatuses?: DataStatus[]

  /** 风险等级 */
  riskLevel?: RiskLevel

  /** 风险等级列表 */
  riskLevels?: RiskLevel[]

  /** 是否成功 */
  isSuccess?: boolean

  /** IP地址 */
  ipAddress?: string

  /** 请求方法 */
  requestMethod?: string

  /** 响应状态码 */
  responseStatus?: number

  /** 创建时间范围-开始 */
  createTimeStart?: string

  /** 创建时间范围-结束 */
  createTimeEnd?: string

  /** 执行时间最小值 */
  executionTimeMin?: number

  /** 执行时间最大值 */
  executionTimeMax?: number

  /** 是否有变更前数据 */
  hasBeforeData?: boolean

  /** 是否有变更后数据 */
  hasAfterData?: boolean

  /** 是否有变更字段 */
  hasChangedFields?: boolean

  /** 批次ID */
  batchId?: string

  /** 父日志ID */
  parentLogId?: string

  /** 排序字段 */
  sortBy?: string

  /** 排序方向 */
  sortDirection?: SortDirection
}

// ==================== 响应类型定义 ====================

/**
 * 分页响应类型（与后端 Page<T> 完全匹配）
 */
// 使用通用的SpringPageData类型
export type PageResponse<T> = SpringPageData<T>

/**
 * 统一响应结果类型（与后端 Result<T> 完全匹配）
 */
export interface Result<T> {
  /** 响应码 */
  code: number

  /** 响应消息 */
  message?: string

  /** 响应数据 */
  data?: T

  /** 时间戳 */
  timestamp?: number
}

// ==================== 统计分析类型 ====================

/**
 * 数据变更日志统计信息类型
 */
export interface DataChangeLogStatistics {
  /** 总数量 */
  totalCount: number

  /** 今日数量 */
  todayCount: number

  /** 本周数量 */
  weekCount: number

  /** 本月数量 */
  monthCount: number

  /** 成功数量 */
  successCount: number

  /** 失败数量 */
  failureCount: number

  /** 操作类型分布 */
  operationTypeDistribution: Record<string, number>

  /** 变更类型分布 */
  changeTypeDistribution: Record<string, number>

  /** 业务模块分布 */
  businessModuleDistribution: Record<string, number>

  /** 风险等级分布 */
  riskLevelDistribution: Record<string, number>

  /** 热门操作人 */
  topOperators: OperatorStats[]

  /** 热门表 */
  topTables: TableStats[]

  /** 小时趋势 */
  hourlyTrend: HourlyTrendItem[]

  /** 日趋势 */
  dailyTrend: DailyTrendItem[]

  /** 平均执行时间 */
  averageExecutionTime: number

  /** 总执行时间 */
  totalExecutionTime: number
}

/**
 * 操作人统计
 */
export interface OperatorStats {
  /** 操作人姓名 */
  operatorName: string

  /** 操作人ID */
  operatorId: string

  /** 数量 */
  count: number
}

/**
 * 表统计
 */
export interface TableStats {
  /** 表名 */
  tableName: string

  /** 表注释 */
  tableComment: string

  /** 数量 */
  count: number
}

/**
 * 小时趋势项
 */
export interface HourlyTrendItem {
  /** 小时 */
  hour: string

  /** 数量 */
  count: number
}

/**
 * 日趋势项
 */
export interface DailyTrendItem {
  /** 日期 */
  date: string

  /** 数量 */
  count: number
}

/**
 * 操作人汇总
 */
export interface OperatorSummary {
  /** 总操作数 */
  totalOperations: number

  /** 成功操作数 */
  successOperations: number

  /** 失败操作数 */
  failureOperations: number

  /** 平均执行时间 */
  averageExecutionTime: number

  /** 最后操作时间 */
  lastOperationTime: string

  /** 热门表 */
  topTables: Array<{
    tableName: string
    count: number
  }>
}

// ==================== 数据对比类型 ====================

/**
 * 数据对比结果类型
 */
export interface DataComparisonResult {
  /** 表名 */
  tableName: string

  /** 记录ID */
  recordId: string

  /** 变更前数据 */
  beforeData: Record<string, unknown>

  /** 变更后数据 */
  afterData: Record<string, unknown>

  /** 变更字段 */
  changedFields: FieldChange[]

  /** 变更数量 */
  changeCount: number

  /** 变更百分比 */
  changePercentage: number
}

/**
 * 字段变更
 */
export interface FieldChange {
  /** 字段名 */
  fieldName: string

  /** 字段注释 */
  fieldComment?: string

  /** 变更前值 */

  beforeValue: unknown

  /** 变更后值 */

  afterValue: unknown

  /** 变更类型 */
  changeType: FieldChangeType
}

/**
 * 字段历史记录
 */
export interface FieldHistoryItem {
  /** 日志ID */
  logId: string

  /** 操作时间 */
  operationTime: string

  /** 操作人姓名 */
  operatorName: string

  /** 变更前值 */

  beforeValue: unknown

  /** 变更后值 */

  afterValue: unknown
}

// ==================== 批量操作类型 ====================

/**
 * 批量删除请求
 */
export interface BatchDeleteRequest {
  /** ID列表 */
  ids: string[]
}

/**
 * 批量归档请求
 */
export interface BatchArchiveRequest {
  /** ID列表 */
  ids: string[]
}

/**
 * 批量设置风险等级请求
 */
export interface BatchSetRiskLevelRequest {
  /** ID列表 */
  ids: string[]

  /** 风险等级 */
  riskLevel: RiskLevel
}

/**
 * 批量添加标签请求
 */
export interface BatchAddTagsRequest {
  /** ID列表 */
  ids: string[]

  /** 标签列表 */
  tags: string[]
}

/**
 * 清理结果
 */
export interface CleanResult {
  /** 删除数量 */
  deletedCount: number

  /** 消息 */
  message: string
}

// ==================== 系统管理类型 ====================

/**
 * 表信息
 */
export interface TableInfo {
  /** 表名 */
  tableName: string

  /** 表注释 */
  tableComment: string

  /** 记录数量 */
  recordCount: number
}

/**
 * 业务模块信息
 */
export interface BusinessModuleInfo {
  /** 模块名称 */
  moduleName: string

  /** 模块注释 */
  moduleComment: string

  /** 记录数量 */
  recordCount: number
}

// ==================== 表单验证类型 ====================

/**
 * 表单验证规则类型
 */
export interface FormValidationRule {
  /** 是否必填 */
  required?: boolean

  /** 错误消息 */
  message?: string

  /** 触发方式 */
  trigger?: 'blur' | 'change'

  /** 最小长度 */
  min?: number

  /** 最大长度 */
  max?: number

  /** 最小值 */
  minValue?: number

  /** 最大值 */
  maxValue?: number

  /** 正则表达式 */
  pattern?: RegExp

  /** 自定义验证函数 */

  validator?: (rule: unknown, value: unknown, callback: unknown) => void
}

// ==================== UI组件类型 ====================

/**
 * 表格列配置
 */
export interface TableColumn {
  /** 属性名 */
  prop: string

  /** 列标题 */
  label: string

  /** 列宽度 */
  width?: number

  /** 最小宽度 */
  minWidth?: number

  /** 是否可排序 */
  sortable?: boolean

  /** 是否显示溢出提示 */
  showOverflowTooltip?: boolean

  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
}

/**
 * 搜索表单配置
 */
export interface SearchFormConfig {
  /** 是否显示高级搜索 */
  showAdvanced: boolean

  /** 搜索字段配置 */
  fields: SearchFieldConfig[]
}

/**
 * 搜索字段配置
 */
export interface SearchFieldConfig {
  /** 字段名 */
  field: string

  /** 字段标签 */
  label: string

  /** 字段类型 */
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'switch'

  /** 选项列表（用于select类型） */

  options?: Array<{ label: string; value: unknown }>

  /** 占位符 */
  placeholder?: string

  /** 是否在基础搜索中显示 */
  basic?: boolean
}
