/**
 * 请假管理类型定义模块
 *
 * 本模块包含所有请假相关的类型定义，包括：
 * - 请假类型管理
 * - 请假申请流程
 * - 请假审批管理
 * - 请假余额管理
 * - 请假报表分析
 */

// 请假类型接口
export interface AttendanceLeaveType {
  id: string
  name: string
  code: string
  category:
    | 'ANNUAL'
    | 'SICK'
    | 'PERSONAL'
    | 'MATERNITY'
    | 'PATERNITY'
    | 'BEREAVEMENT'
    | 'STUDY'
    | 'COMPENSATORY'
    | 'UNPAID'
    | 'OTHER'
  description: string
  isActive: boolean
  isPaid: boolean
  maxDaysPerYear: number
  maxDaysPerRequest: number
  minDaysPerRequest: number
  requireApproval: boolean
  approvalLevels: number
  carryoverAllowed: boolean
  carryoverMaxDays: number
  canBeNegative: boolean
  requiredDocuments: string[]
  applicableEmployees: {
    employeeTypes: string[]
    departments: string[]
    positions: string[]
    minTenure: number
    gender?: 'MALE' | 'FEMALE'
  }
  advanceNotice: {
    minDays: number
    maxDays: number
    exceptions: string[]
  }
  restrictions: {
    blackoutPeriods: {
      startDate: string
      endDate: string
      reason: string
    }[]
    maxConsecutiveDays: number
    minGapBetweenRequests: number
    yearlyQuota: number
    monthlyQuota: number
  }
  calculations: {
    accrualMethod: 'MONTHLY' | 'ANNUAL' | 'PRORATED' | 'MANUAL'
    accrualRate: number
    accrualStartDate: 'HIRE_DATE' | 'YEAR_START' | 'CUSTOM'
    roundingRule: 'UP' | 'DOWN' | 'NEAREST'
  }
  settings: {
    allowHalfDay: boolean
    allowHourly: boolean
    workingDaysOnly: boolean
    excludeHolidays: boolean
    excludeWeekends: boolean
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假申请接口
export interface AttendanceLeaveRequest {
  id: string
  requestNo: string
  employeeId: string
  employeeName: string
  employeeNo: string
  departmentId: string
  departmentName: string
  positionId: string
  positionName: string
  leaveTypeId: string
  leaveTypeName: string
  leaveTypeCode: string
  requestType: 'FULL_DAY' | 'HALF_DAY' | 'HOURLY'
  startDate: string
  endDate: string
  startTime?: string
  endTime?: string
  totalDays: number
  totalHours?: number
  reason: string
  emergencyContact?: {
    name: string
    relationship: string
    phone: string
  }
  workHandover?: {
    handoverTo: string
    handoverDetails: string
    urgentMatters: string[]
  }
  attachments: {
    id: string
    fileName: string
    fileType: string
    fileSize: number
    uploadedAt: string
    url: string
  }[]
  status: 'DRAFT' | 'SUBMITTED' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED' | 'WITHDRAWN'
  currentStep: number
  approvalHistory: {
    stepNo: number
    approverId: string
    approverName: string
    approverRole: string
    action: 'APPROVE' | 'REJECT' | 'RETURN' | 'DELEGATE'
    actionDate: string
    comments?: string
    delegatedTo?: string
  }[]
  balanceInfo: {
    currentBalance: number
    requestedDays: number
    remainingBalance: number
    pendingRequests: number
    isOverdrawn: boolean
  }
  impact: {
    workingDays: number
    affectedProjects: string[]
    coverageArrangement: {
      coveredBy: string
      coveragePeriod: [string, string]
      tasks: string[]
    }[]
    teamImpact: 'HIGH' | 'MEDIUM' | 'LOW'
  }
  submittedAt: string
  processedAt?: string
  approvedAt?: string
  rejectedAt?: string
  withdrawnAt?: string
  cancelledAt?: string
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假申请提交数据接口
export interface AttendanceLeaveRequestSubmission {
  leaveTypeId: string
  requestType: 'FULL_DAY' | 'HALF_DAY' | 'HOURLY'
  startDate: string
  endDate: string
  startTime?: string
  endTime?: string
  reason: string
  emergencyContact?: {
    name: string
    relationship: string
    phone: string
  }
  workHandover?: {
    handoverTo: string
    handoverDetails: string
    urgentMatters: string[]
  }
  attachmentIds?: string[]
  isDraft?: boolean
  autoSubmit?: boolean
  notifyManager?: boolean
  notifyTeam?: boolean
}

// 请假申请搜索参数接口
export interface AttendanceLeaveRequestSearchParams {
  page?: number
  limit?: number
  employeeId?: string
  employeeName?: string
  employeeNo?: string
  departmentId?: string
  departmentIds?: string[]
  positionId?: string
  leaveTypeId?: string
  leaveTypeIds?: string[]
  status?: (
    | 'DRAFT'
    | 'SUBMITTED'
    | 'PENDING'
    | 'APPROVED'
    | 'REJECTED'
    | 'CANCELLED'
    | 'WITHDRAWN'
  )[]
  requestType?: ('FULL_DAY' | 'HALF_DAY' | 'HOURLY')[]
  startDate?: string
  endDate?: string
  submittedDateRange?: [string, string]
  approvedDateRange?: [string, string]
  sortBy?: 'submittedAt' | 'startDate' | 'endDate' | 'status' | 'employeeName'
  sortOrder?: 'ASC' | 'DESC'
  includeSubDepartments?: boolean
  managedByMe?: boolean
  myRequests?: boolean
  pendingApproval?: boolean
  keyword?: string
}

// 请假申请验证结果接口
export interface AttendanceLeaveRequestValidation {
  isValid: boolean
  errors: {
    field: string
    message: string
    code: string
  }[]
  warnings: {
    field: string
    message: string
    code: string
  }[]
  balanceCheck: {
    isValid: boolean
    currentBalance: number
    requestedDays: number
    remainingBalance: number
    isOverdrawn: boolean
    overdrawAmount?: number
  }
  conflictCheck: {
    hasConflicts: boolean
    conflicts: {
      conflictType: 'OVERLAP' | 'BLACKOUT' | 'QUOTA_EXCEEDED' | 'TEAM_COVERAGE'
      description: string
      suggestions: string[]
    }[]
  }
  policyCheck: {
    isCompliant: boolean
    violations: {
      rule: string
      description: string
      severity: 'ERROR' | 'WARNING'
    }[]
  }
  workflowCheck: {
    approvers: {
      stepNo: number
      approverId: string
      approverName: string
      approverRole: string
      isAvailable: boolean
      expectedDuration: number
    }[]
    estimatedProcessingTime: number
  }
  recommendations: {
    type: 'ALTERNATIVE_DATES' | 'DIFFERENT_TYPE' | 'PARTIAL_APPROVAL'
    description: string

    details: unknown
  }[]
}

// 请假申请批量操作接口
export interface AttendanceLeaveRequestBatchOperation {
  operationType: 'APPROVE' | 'REJECT' | 'CANCEL' | 'WITHDRAW' | 'EXPORT' | 'NOTIFY'
  targetIds: string[]
  criteria?: {
    departmentIds?: string[]
    leaveTypeIds?: string[]
    status?: string[]
    dateRange?: [string, string]
  }
  parameters?: {
    comments?: string
    reason?: string
    notifyEmployees?: boolean
    exportFormat?: 'EXCEL' | 'PDF' | 'CSV'
    emailTemplate?: string
  }
  executedBy: string
  executedAt: string
  batchId: string
}

// 请假申请批量操作结果接口
export interface AttendanceLeaveRequestBatchResult {
  batchId: string
  operationType: string
  status: 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'PARTIAL'
  totalCount: number
  successCount: number
  failedCount: number
  skippedCount: number
  results: {
    requestId: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED'
    message?: string
    error?: string
  }[]
  summary: {
    processedItems: number
    successRate: number
    duration: number
    downloadUrl?: string
  }
  conflicts: {
    requestId: string
    conflictType: string
    description: string
    resolution: string
  }[]
  notifications: {
    notificationType: 'EMAIL' | 'SMS' | 'SYSTEM'
    recipient: string
    status: 'SENT' | 'FAILED' | 'PENDING'
    sentAt?: string
  }[]
  auditLog: {
    action: string
    details: string
    timestamp: string
    userId: string
  }[]
  startedAt: string
  completedAt?: string
  executedBy: string
}

// 请假审批流程接口
export interface AttendanceLeaveApprovalFlow {
  id: string
  flowName: string
  flowCode: string
  leaveTypeId: string
  leaveTypeName: string
  departmentIds: string[]
  employeeTypes: string[]
  isActive: boolean
  isDefault: boolean
  steps: {
    stepNo: number
    stepName: string
    stepType: 'DIRECT_MANAGER' | 'HR' | 'DEPARTMENT_HEAD' | 'LEADERSHIP' | 'CUSTOM'
    approverRoles: string[]
    approverIds?: string[]
    isRequired: boolean
    allowDelegate: boolean
    autoApprove: boolean
    autoApproveConditions?: {
      maxDays?: number
      maxHours?: number
      balanceThreshold?: number
      specificDates?: string[]
    }
    timeoutHours: number
    escalationAction: 'AUTO_APPROVE' | 'ESCALATE' | 'REJECT'
    escalationTo?: string
    notifications: {
      onSubmit: boolean
      onApprove: boolean
      onReject: boolean
      onTimeout: boolean
      recipients: ('APPLICANT' | 'APPROVER' | 'NEXT_APPROVER' | 'HR' | 'MANAGER')[]
    }
  }[]
  conditions: {
    minDays?: number
    maxDays?: number
    requireDocuments?: string[]
    blackoutPeriods?: {
      startDate: string
      endDate: string
      reason: string
    }[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假审批决策接口
export interface AttendanceLeaveApprovalDecision {
  requestId: string
  stepNo: number
  approverId: string
  approverName: string
  approverRole: string
  action: 'APPROVE' | 'REJECT' | 'RETURN' | 'DELEGATE' | 'REQUEST_INFO'
  decision: 'APPROVED' | 'REJECTED' | 'RETURNED' | 'DELEGATED' | 'INFO_REQUESTED'
  comments?: string
  attachments?: {
    id: string
    fileName: string
    fileType: string
    fileSize: number
    uploadedAt: string
    url: string
  }[]
  conditions?: {
    conditionalApproval: boolean
    requiredActions: string[]
    deadlineDate?: string
    followUpRequired?: boolean
  }
  delegation?: {
    delegatedTo: string
    delegatedToName: string
    delegationReason: string
    delegationPeriod?: [string, string]
  }
  infoRequest?: {
    requestedInfo: string[]
    responseDeadline: string
    responseRequired: boolean
  }
  decisionDate: string
  effectiveDate?: string
  ipAddress?: string
  userAgent?: string
  location?: string
}

// 请假审批历史接口
export interface AttendanceLeaveApprovalHistory {
  requestId: string
  requestNo: string
  employeeId: string
  employeeName: string
  leaveTypeId: string
  leaveTypeName: string
  startDate: string
  endDate: string
  totalDays: number
  currentStatus: string
  submittedAt: string
  lastActionAt: string
  timeline: {
    stepNo: number
    stepName: string
    approverId: string
    approverName: string
    action: string
    decision: string
    actionDate: string
    comments?: string
    duration: number
    isCompleted: boolean
  }[]
  flowSummary: {
    totalSteps: number
    completedSteps: number
    currentStep: number
    averageProcessingTime: number
    isOnTime: boolean
    escalationCount: number
  }
  notifications: {
    notificationId: string
    recipient: string
    type: 'EMAIL' | 'SMS' | 'SYSTEM'
    subject: string
    sentAt: string
    status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED'
  }[]
}

// 请假审批权限接口
export interface AttendanceLeaveApprovalPermission {
  userId: string
  userName: string
  roleId: string
  roleName: string
  departmentIds: string[]
  permissions: {
    canApproveLeave: boolean
    canRejectLeave: boolean
    canDelegateApproval: boolean
    canViewAllRequests: boolean
    canModifyApprovalFlow: boolean
    canOverrideDecision: boolean
    maxApprovalAmount?: number
    maxApprovalDays?: number
    leaveTypeRestrictions?: string[]
    departmentRestrictions?: string[]
  }
  approvalScope: {
    scopeType: 'DIRECT_REPORTS' | 'DEPARTMENT' | 'ORGANIZATION' | 'CUSTOM'
    includedEmployees?: string[]
    excludedEmployees?: string[]
    includeSubDepartments: boolean
  }
  delegations: {
    delegationId: string
    delegatedTo: string
    delegatedToName: string
    startDate: string
    endDate: string
    isActive: boolean
    reason: string
    permissions: string[]
  }[]
  workingHours: {
    timezone: string
    workingDays: string[]
    workingHours: [string, string]
    holidayCalendar: string
  }
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  createdAt: string
  updatedAt: string
}

// 请假审批通知接口
export interface AttendanceLeaveApprovalNotification {
  id: string
  requestId: string
  requestNo: string
  notificationType:
    | 'SUBMIT'
    | 'APPROVE'
    | 'REJECT'
    | 'RETURN'
    | 'TIMEOUT'
    | 'ESCALATION'
    | 'DELEGATE'
    | 'INFO_REQUEST'
  templateId: string
  templateName: string
  recipients: {
    recipientType: 'APPLICANT' | 'APPROVER' | 'NEXT_APPROVER' | 'HR' | 'MANAGER' | 'TEAM'
    recipientId: string
    recipientName: string
    recipientEmail?: string
    recipientPhone?: string
  }[]
  channels: ('EMAIL' | 'SMS' | 'SYSTEM' | 'PUSH' | 'WECHAT')[]
  content: {
    subject: string
    body: string
    variables: {
      [key: string]: unknown
    }
  }
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  scheduledAt?: string
  sentAt?: string
  deliveredAt?: string
  readAt?: string
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED' | 'CANCELLED'
  retryCount: number
  maxRetries: number
  error?: string
  metadata: {
    requestInfo: {
      employeeName: string
      leaveType: string
      startDate: string
      endDate: string
      totalDays: number
    }
    approvalInfo?: {
      currentStep: number
      totalSteps: number
      nextApprover?: string
      deadline?: string
    }
  }
  createdAt: string
  updatedAt: string
}

// 请假审批委托接口
export interface AttendanceLeaveApprovalDelegation {
  id: string
  delegatorId: string
  delegatorName: string
  delegateeId: string
  delegateeName: string
  delegationType: 'TEMPORARY' | 'PERMANENT' | 'PARTIAL' | 'EMERGENCY'
  scope: {
    leaveTypes?: string[]
    departments?: string[]
    employees?: string[]
    maxDays?: number
    maxAmount?: number
  }
  startDate: string
  endDate?: string
  reason: string
  status: 'ACTIVE' | 'INACTIVE' | 'EXPIRED' | 'REVOKED'
  permissions: {
    canApprove: boolean
    canReject: boolean
    canDelegate: boolean
    canViewDetails: boolean
    requiresConfirmation: boolean
  }
  conditions: {
    requiresDelegatorNotification: boolean
    requiresJustification: boolean
    autoExpiry: boolean
    maxConcurrentDelegations?: number
  }
  usage: {
    totalDelegatedRequests: number
    approvedRequests: number
    rejectedRequests: number
    lastUsedAt?: string
  }
  notifications: {
    notifyDelegator: boolean
    notifyDelegatee: boolean
    notifyOnAction: boolean
    notifyOnExpiry: boolean
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  revokedAt?: string
  revokedBy?: string
  revokedReason?: string
}

// 请假审批统计接口
export interface AttendanceLeaveApprovalStatistics {
  statisticsId: string
  period: [string, string]
  scope: {
    departmentIds?: string[]
    approverIds?: string[]
    leaveTypeIds?: string[]
  }
  overallMetrics: {
    totalRequests: number
    approvedRequests: number
    rejectedRequests: number
    pendingRequests: number
    withdrawnRequests: number
    approvalRate: number
    rejectionRate: number
    averageProcessingTime: number
    onTimeProcessingRate: number
  }
  approverMetrics: {
    approverId: string
    approverName: string
    totalRequests: number
    approvedCount: number
    rejectedCount: number
    averageProcessingTime: number
    onTimeRate: number
    escalationCount: number
    delegationCount: number
  }[]
  departmentMetrics: {
    departmentId: string
    departmentName: string
    totalRequests: number
    approvalRate: number
    averageProcessingTime: number
    escalationRate: number
  }[]
  leaveTypeMetrics: {
    leaveTypeId: string
    leaveTypeName: string
    totalRequests: number
    approvalRate: number
    averageProcessingTime: number
    complexityScore: number
  }[]
  timeAnalysis: {
    hourlyDistribution: {
      hour: number
      requestCount: number
      averageProcessingTime: number
    }[]
    dailyDistribution: {
      dayOfWeek: string
      requestCount: number
      averageProcessingTime: number
    }[]
    monthlyTrends: {
      month: string
      requestCount: number
      approvalRate: number
      processingTime: number
    }[]
  }
  bottleneckAnalysis: {
    slowestSteps: {
      stepName: string
      averageTime: number
      maxTime: number
      frequentDelays: number
    }[]
    escalationPatterns: {
      stepName: string
      escalationRate: number
      reasons: string[]
    }[]
    approverWorkload: {
      approverId: string
      approverName: string
      workloadScore: number
      peakHours: string[]
      recommendedActions: string[]
    }[]
  }
  generatedAt: string
  generatedBy: string
}

// 请假余额接口
export interface AttendanceLeaveBalance {
  id: string
  employeeId: string
  employeeName: string
  employeeNo: string
  departmentId: string
  departmentName: string
  positionId: string
  positionName: string
  leaveTypeId: string
  leaveTypeName: string
  leaveTypeCode: string
  category:
    | 'ANNUAL'
    | 'SICK'
    | 'PERSONAL'
    | 'MATERNITY'
    | 'PATERNITY'
    | 'BEREAVEMENT'
    | 'STUDY'
    | 'COMPENSATORY'
    | 'UNPAID'
    | 'OTHER'
  balanceYear: number
  currentBalance: number
  totalEntitlement: number
  usedBalance: number
  pendingBalance: number
  carryoverBalance: number
  adjustmentBalance: number
  expiryDate?: string
  lastAccrualDate: string
  nextAccrualDate?: string
  accrualRate: number
  accrualFrequency: 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY' | 'BIANNUALLY'
  maxCarryover: number
  minBalance: number
  isActive: boolean
  isOverdrawn: boolean
  overdraftLimit?: number
  rules: {
    allowNegative: boolean
    autoAccrual: boolean
    carryoverAllowed: boolean
    prorated: boolean
    roundingRule: 'UP' | 'DOWN' | 'NEAREST'
  }
  alerts: {
    lowBalanceThreshold: number
    expiryWarningDays: number
    enableAlerts: boolean
  }
  history: {
    lastUpdatedAt: string
    lastUpdatedBy: string
    lastTransactionId?: string
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假余额变更记录接口
export interface AttendanceLeaveBalanceTransaction {
  id: string
  transactionNo: string
  employeeId: string
  employeeName: string
  leaveTypeId: string
  leaveTypeName: string
  transactionType:
    | 'ACCRUAL'
    | 'USAGE'
    | 'ADJUSTMENT'
    | 'CARRYOVER'
    | 'EXPIRY'
    | 'TRANSFER'
    | 'COMPENSATION'
    | 'CORRECTION'
  transactionSource: 'SYSTEM' | 'MANUAL' | 'REQUEST' | 'APPROVAL' | 'IMPORT' | 'API' | 'BATCH'
  amount: number
  balanceBefore: number
  balanceAfter: number
  description: string
  reason?: string
  referenceId?: string
  referenceType?: 'LEAVE_REQUEST' | 'ADJUSTMENT' | 'POLICY' | 'CORRECTION' | 'TRANSFER'
  relatedRequestId?: string
  relatedTransactionId?: string
  approvedBy?: string
  approvedAt?: string
  effectiveDate: string
  expiryDate?: string
  isReversed: boolean
  reversedBy?: string
  reversedAt?: string
  reversalReason?: string
  reversalTransactionId?: string
  metadata: {
    originalBalance?: number
    calculationDetails?: {
      formula: string

      parameters: { [key: string]: unknown }
      result: number
    }
    systemContext?: {
      policyVersion: string
      ruleVersion: string
      calculationEngine: string
    }
  }
  auditInfo: {
    ipAddress?: string
    userAgent?: string
    location?: string
    sessionId?: string
  }
  createdAt: string
  createdBy: string
}

// 请假余额调整接口
export interface AttendanceLeaveBalanceAdjustment {
  id: string
  adjustmentNo: string
  adjustmentType: 'INCREASE' | 'DECREASE' | 'RESET' | 'TRANSFER'
  reason:
    | 'POLICY_CHANGE'
    | 'ERROR_CORRECTION'
    | 'MANUAL_ADJUSTMENT'
    | 'SYSTEM_MIGRATION'
    | 'SPECIAL_GRANT'
    | 'PENALTY'
    | 'OTHER'
  targetScope: {
    scopeType: 'EMPLOYEE' | 'DEPARTMENT' | 'POSITION' | 'LEAVE_TYPE' | 'BATCH'
    employeeIds?: string[]
    departmentIds?: string[]
    positionIds?: string[]
    leaveTypeIds?: string[]
    criteria?: {
      hireDate?: [string, string]
      balanceRange?: [number, number]
      category?: string[]
    }
  }
  adjustmentDetails: {
    amount?: number
    percentage?: number
    formula?: string
    newBalance?: number
    targetBalance?: number
    maxAdjustment?: number
    minAdjustment?: number
  }
  transferDetails?: {
    fromLeaveTypeId: string
    toLeaveTypeId: string
    conversionRate: number
    maxTransferAmount?: number
  }
  batchDetails?: {
    totalTargets: number
    processedCount: number
    successCount: number
    failedCount: number
    totalAmount: number
  }
  approvalRequired: boolean
  approvedBy?: string
  approvedAt?: string
  approvalComments?: string
  status:
    | 'DRAFT'
    | 'PENDING_APPROVAL'
    | 'APPROVED'
    | 'PROCESSING'
    | 'COMPLETED'
    | 'FAILED'
    | 'CANCELLED'
  scheduledAt?: string
  executedAt?: string
  completedAt?: string
  errors?: {
    employeeId: string
    employeeName: string
    error: string
    details: string
  }[]
  notifications: {
    notifyEmployees: boolean
    notifyManagers: boolean
    notifyHR: boolean
    customRecipients?: string[]
    emailTemplate?: string
  }
  auditTrail: {
    action: string
    timestamp: string
    userId: string
    details: string
  }[]
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假余额预警接口
export interface AttendanceLeaveBalanceAlert {
  id: string
  alertType:
    | 'LOW_BALANCE'
    | 'EXPIRY_WARNING'
    | 'OVERDRAFT'
    | 'UNUSUAL_USAGE'
    | 'POLICY_VIOLATION'
    | 'SYSTEM_ERROR'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  employeeId: string
  employeeName: string
  employeeNo: string
  departmentId: string
  departmentName: string
  leaveTypeId: string
  leaveTypeName: string
  currentBalance: number
  threshold?: number
  triggerValue?: number
  message: string
  description: string
  recommendations: string[]
  actionRequired: boolean
  autoActions?: {
    blockNewRequests: boolean
    notifyManager: boolean
    escalateToHR: boolean
    sendReminder: boolean
  }
  relatedRequestId?: string
  relatedTransactionId?: string
  triggeredAt: string
  acknowledgedAt?: string
  acknowledgedBy?: string
  resolvedAt?: string
  resolvedBy?: string
  resolutionNotes?: string
  status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'DISMISSED' | 'EXPIRED'
  recipients: {
    recipientType: 'EMPLOYEE' | 'MANAGER' | 'HR' | 'ADMIN'
    recipientId: string
    recipientName: string
    notified: boolean
    notifiedAt?: string
  }[]
  recurrence: {
    isRecurring: boolean
    frequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY'
    lastTriggered?: string
    nextTrigger?: string
    suppressUntil?: string
  }
  metadata: {
    ruleId: string
    ruleName: string
    ruleVersion: string

    context: { [key: string]: unknown }
  }
  createdAt: string
  updatedAt: string
}

// 请假余额计算规则接口
export interface AttendanceLeaveBalanceRule {
  id: string
  ruleName: string
  ruleCode: string
  ruleType: 'ACCRUAL' | 'CARRYOVER' | 'EXPIRY' | 'ADJUSTMENT' | 'VALIDATION'
  leaveTypeIds: string[]
  applicableScope: {
    employeeTypes: string[]
    departments: string[]
    positions: string[]
    hireDate?: [string, string]
    tenure?: [number, number]
  }
  priority: number
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  accrualRules?: {
    accrualMethod: 'MONTHLY' | 'ANNUAL' | 'PRORATED' | 'MILESTONE' | 'CUSTOM'
    accrualRate: number
    accrualCap: number
    accrualStartDate: 'HIRE_DATE' | 'YEAR_START' | 'POLICY_START' | 'CUSTOM'
    waitingPeriod?: number
    prorationRule: 'DAILY' | 'MONTHLY' | 'NONE'
    roundingRule: 'UP' | 'DOWN' | 'NEAREST'
    calendar: 'FISCAL' | 'CALENDAR' | 'POLICY'
  }
  carryoverRules?: {
    allowCarryover: boolean
    maxCarryover: number
    carryoverExpiry?: number
    carryoverConditions?: string[]
    useOrder: 'FIFO' | 'LIFO' | 'EXPIRY_FIRST'
  }
  expiryRules?: {
    expiryMethod: 'ANNUAL' | 'ROLLING' | 'FIXED_DATE' | 'NEVER'
    expiryDate?: string
    warningDays: number
    autoExpire: boolean
    gracePeriod?: number
  }
  adjustmentRules?: {
    allowNegative: boolean
    overdraftLimit?: number
    autoAdjustment: boolean
    adjustmentFrequency?: 'REAL_TIME' | 'DAILY' | 'MONTHLY'
  }
  validationRules?: {
    minBalance: number
    maxBalance: number
    requestValidation: string[]
    balanceValidation: string[]
  }
  calculationFormula: {
    formula: string
    parameters: {
      name: string
      type: 'NUMBER' | 'DATE' | 'BOOLEAN' | 'STRING'

      defaultValue: unknown
      description: string
    }[]
    conditions: {
      condition: string
      formula: string
    }[]
  }
  notifications: {
    lowBalanceAlert: boolean
    expiryWarning: boolean
    overdraftAlert: boolean
    recipients: string[]
  }
  auditSettings: {
    trackChanges: boolean
    logTransactions: boolean
    requireApproval: boolean
    approvers: string[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  version: number
  changeLog: {
    version: number
    changes: string
    changedBy: string
    changedAt: string
  }[]
}

// 请假余额统计接口
export interface AttendanceLeaveBalanceStatistics {
  statisticsId: string
  reportType: 'SUMMARY' | 'DETAILED' | 'TREND' | 'ANALYSIS' | 'FORECAST'
  period: [string, string]
  scope: {
    departmentIds?: string[]
    employeeIds?: string[]
    leaveTypeIds?: string[]
    includeInactive?: boolean
  }
  overallMetrics: {
    totalEmployees: number
    totalBalance: number
    totalEntitlement: number
    totalUsed: number
    totalCarryover: number
    utilizationRate: number
    averageBalance: number
    balanceDistribution: {
      range: string
      count: number
      percentage: number
    }[]
  }
  departmentBreakdown: {
    departmentId: string
    departmentName: string
    employeeCount: number
    totalBalance: number
    averageBalance: number
    utilizationRate: number
    ranking: number
  }[]
  leaveTypeBreakdown: {
    leaveTypeId: string
    leaveTypeName: string
    totalBalance: number
    totalUsed: number
    utilizationRate: number
    averageBalance: number
    expiryRisk: number
  }[]
  riskAnalysis: {
    lowBalanceEmployees: {
      employeeId: string
      employeeName: string
      leaveTypeName: string
      currentBalance: number
      threshold: number
      riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
    }[]
    expiryRisk: {
      employeeId: string
      employeeName: string
      leaveTypeName: string
      balance: number
      expiryDate: string
      daysUntilExpiry: number
    }[]
    overdraftEmployees: {
      employeeId: string
      employeeName: string
      leaveTypeName: string
      balance: number
      overdraftAmount: number
    }[]
  }
  trends: {
    period: string
    totalBalance: number
    totalUsed: number
    newAccruals: number
    expirations: number
    adjustments: number
  }[]
  forecasting: {
    projectedUsage: {
      month: string
      projectedUsage: number
      confidence: number
    }[]
    balanceProjection: {
      month: string
      projectedBalance: number
      confidence: number
    }[]
    recommendations: {
      type: 'POLICY_ADJUSTMENT' | 'PROCESS_IMPROVEMENT' | 'TRAINING_NEED' | 'SYSTEM_OPTIMIZATION'
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      description: string
      expectedImpact: string
    }[]
  }
  generatedAt: string
  generatedBy: string
}

// 请假统计报表接口
export interface AttendanceLeaveReport {
  id: string
  reportName: string
  reportCode: string
  reportType: 'SUMMARY' | 'DETAILED' | 'ANALYTICAL' | 'TREND' | 'COMPLIANCE' | 'CUSTOM'
  reportCategory:
    | 'EMPLOYEE'
    | 'DEPARTMENT'
    | 'LEAVE_TYPE'
    | 'PERIOD'
    | 'APPROVAL'
    | 'BALANCE'
    | 'USAGE'
  description?: string
  isActive: boolean
  isScheduled: boolean
  visibility: 'PUBLIC' | 'PRIVATE' | 'DEPARTMENT' | 'ROLE_BASED'
  scope: {
    departmentIds?: string[]
    employeeIds?: string[]
    leaveTypeIds?: string[]
    positionIds?: string[]
    includeSubDepartments: boolean
    includeInactiveEmployees: boolean
  }
  timeRange: {
    rangeType: 'FIXED' | 'RELATIVE' | 'CUSTOM'
    fixedRange?: [string, string]
    relativeRange?: {
      unit: 'DAY' | 'WEEK' | 'MONTH' | 'QUARTER' | 'YEAR'
      value: number
      direction: 'PAST' | 'FUTURE' | 'CURRENT'
    }
    customRange?: {
      startDate: string
      endDate: string
    }
  }
  dimensions: {
    groupBy: ('EMPLOYEE' | 'DEPARTMENT' | 'LEAVE_TYPE' | 'MONTH' | 'QUARTER' | 'YEAR' | 'STATUS')[]
    metrics: (
      | 'TOTAL_REQUESTS'
      | 'TOTAL_DAYS'
      | 'APPROVAL_RATE'
      | 'AVERAGE_DAYS'
      | 'BALANCE_USAGE'
      | 'COST_ANALYSIS'
    )[]
    filters: {
      field: string
      operator:
        | 'EQUALS'
        | 'NOT_EQUALS'
        | 'CONTAINS'
        | 'GREATER_THAN'
        | 'LESS_THAN'
        | 'BETWEEN'
        | 'IN'

      value: unknown
    }[]
    sorting: {
      field: string
      order: 'ASC' | 'DESC'
    }[]
  }
  visualization: {
    chartTypes: ('TABLE' | 'BAR' | 'LINE' | 'PIE' | 'SCATTER' | 'HEATMAP')[]
    layout: {
      sections: {
        sectionId: string
        sectionName: string
        sectionType: 'SUMMARY' | 'CHART' | 'TABLE' | 'KPI'
        position: number

        config: unknown
      }[]
    }
    styling: {
      theme: string
      colorScheme: string[]
      fontSize: string
      showLegend: boolean
      showDataLabels: boolean
    }
  }
  outputFormats: ('PDF' | 'EXCEL' | 'CSV' | 'HTML' | 'JSON')[]
  schedule?: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
    dayOfWeek?: number
    dayOfMonth?: number
    time: string
    timezone: string
    isEnabled: boolean
    lastRunAt?: string
    nextRunAt?: string
  }
  distribution: {
    recipients: {
      recipientType: 'EMPLOYEE' | 'MANAGER' | 'HR' | 'EMAIL'
      recipientId?: string
      recipientEmail?: string
      recipientName: string
    }[]
    emailTemplate?: string
    subject?: string
    message?: string
  }
  permissions: {
    viewPermissions: string[]
    editPermissions: string[]
    deletePermissions: string[]
    sharePermissions: string[]
  }
  metadata: {
    tags: string[]
    category: string
    priority: 'LOW' | 'MEDIUM' | 'HIGH'
    estimatedDuration: number
    dataSource: string
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  version: number
  lastGeneratedAt?: string
  lastGeneratedBy?: string
}

// 请假报表实例接口
export interface AttendanceLeaveReportInstance {
  id: string
  reportId: string
  reportName: string
  instanceName: string
  generationType: 'MANUAL' | 'SCHEDULED' | 'API'
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'EXPIRED'
  progress: number
  currentStep?: string
  parameters: {
    timeRange: [string, string]
    scope: {
      departmentIds?: string[]
      employeeIds?: string[]
      leaveTypeIds?: string[]
    }
    customFilters?: {
      field: string

      value: unknown
    }[]
  }
  results?: {
    summary: {
      totalRecords: number
      totalEmployees: number
      totalDepartments: number
      totalLeaveTypes: number
      reportPeriod: [string, string]
    }
    data: {
      sections: {
        sectionId: string
        sectionName: string
        sectionType: string

        content: unknown
      }[]

      rawData?: unknown[]

      aggregatedData?: unknown[]
    }
    insights: {
      keyFindings: string[]
      trends: string[]
      anomalies: string[]
      recommendations: string[]
    }
  }
  files: {
    fileId: string
    fileName: string
    fileType: string
    fileSize: number
    downloadUrl: string
    expiryDate: string
  }[]
  performance: {
    executionTime: number
    dataSize: number
    queryCount: number
    cacheHitRate?: number
  }
  error?: {
    errorCode: string
    errorMessage: string
    errorDetails: string
    timestamp: string
  }
  generatedAt: string
  completedAt?: string
  generatedBy: string
  expiresAt?: string
}

// 请假报表模板接口
export interface AttendanceLeaveReportTemplate {
  id: string
  templateName: string
  templateCode: string
  templateType: 'STANDARD' | 'CUSTOM' | 'INDUSTRY'
  industry?: string
  description: string
  isBuiltIn: boolean
  isActive: boolean
  configuration: {
    reportStructure: {
      sections: {
        sectionId: string
        sectionName: string
        sectionType: 'HEADER' | 'SUMMARY' | 'DETAIL' | 'CHART' | 'FOOTER'
        isRequired: boolean

        defaultConfig: unknown
      }[]
    }
    defaultParameters: {
      timeRange: {
        defaultType: 'RELATIVE' | 'FIXED'

        defaultValue: unknown
      }
      scope: {
        defaultDepartments?: string[]
        defaultLeaveTypes?: string[]
      }
      metrics: string[]
      dimensions: string[]
    }
    styling: {
      layout: string
      theme: string
      branding?: {
        logo?: string
        colors: string[]
        fonts: string[]
      }
    }
  }
  usage: {
    usageCount: number
    lastUsedAt?: string
    popularityScore: number
    userRating?: number
  }
  samples: {
    sampleId: string
    sampleName: string

    sampleData: unknown
    thumbnail?: string
  }[]
  tags: string[]
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  version: string
}

// 请假报表配置接口
export interface AttendanceLeaveReportConfig {
  id: string
  configName: string
  configType: 'GLOBAL' | 'DEPARTMENT' | 'USER' | 'ROLE'
  targetId?: string
  targetName?: string
  settings: {
    defaultTimeRange: {
      type: 'RELATIVE' | 'FIXED'

      value: unknown
    }
    defaultOutputFormat: string
    defaultFilters: {
      field: string

      value: unknown
    }[]
    maxRecordsPerReport: number
    cacheExpiration: number
    autoRefreshInterval?: number
  }
  permissions: {
    canCreateReports: boolean
    canEditReports: boolean
    canDeleteReports: boolean
    canScheduleReports: boolean
    canShareReports: boolean
    maxReportsPerUser?: number
    allowedOutputFormats: string[]
    allowedDataSources: string[]
  }
  notifications: {
    notifyOnCompletion: boolean
    notifyOnFailure: boolean
    notifyOnSchedule: boolean
    emailTemplate: string
    recipients: string[]
  }
  branding: {
    companyLogo?: string
    companyName: string
    headerColor?: string
    footerText?: string
    watermark?: string
  }
  dataRetention: {
    retentionPeriod: number
    autoCleanup: boolean
    archiveExpiredReports: boolean
  }
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假报表订阅接口
export interface AttendanceLeaveReportSubscription {
  id: string
  subscriptionName: string
  reportId: string
  reportName: string
  subscriberId: string
  subscriberName: string
  subscriberType: 'EMPLOYEE' | 'MANAGER' | 'HR' | 'ADMIN'
  subscriptionType: 'SCHEDULED' | 'EVENT_BASED' | 'ON_DEMAND'
  isActive: boolean
  schedule?: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
    time: string
    timezone: string
    dayOfWeek?: number
    dayOfMonth?: number
    weekOfMonth?: number
  }
  eventTriggers?: {
    triggerType: 'DATA_CHANGE' | 'THRESHOLD_EXCEEDED' | 'ANOMALY_DETECTED' | 'APPROVAL_PENDING'
    triggerConditions: {
      field: string
      operator: string

      value: unknown
    }[]
    cooldownPeriod?: number
  }
  customization: {
    parameters: {
      timeRange?: unknown

      scope?: unknown

      filters?: unknown[]
    }
    outputFormat: string
    includeSummary: boolean
    includeCharts: boolean
    includeRawData: boolean
  }
  delivery: {
    deliveryMethod: 'EMAIL' | 'SYSTEM' | 'API' | 'FILE_SHARE'
    emailSettings?: {
      subject: string
      body: string
      attachReport: boolean
      sendAsLink: boolean
    }
    apiSettings?: {
      webhookUrl: string
      authToken?: string
      headers?: { [key: string]: string }
    }
  }
  history: {
    lastDeliveryAt?: string
    deliveryCount: number
    successfulDeliveries: number
    failedDeliveries: number
    lastError?: string
  }
  preferences: {
    pauseUntil?: string
    maxFileSize?: number
    compressFiles: boolean
    encryptFiles: boolean
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假报表分析接口
export interface AttendanceLeaveReportAnalytics {
  analyticsId: string
  reportId: string
  reportName: string
  period: [string, string]
  analysisType: 'USAGE' | 'PERFORMANCE' | 'CONTENT' | 'USER_BEHAVIOR'
  metrics: {
    usageMetrics: {
      totalGenerations: number
      uniqueUsers: number
      averageGenerationTime: number
      peakUsageHours: string[]
      popularOutputFormats: {
        format: string
        count: number
        percentage: number
      }[]
    }
    performanceMetrics: {
      averageExecutionTime: number
      averageDataSize: number
      cacheHitRate: number
      errorRate: number
      timeoutRate: number
    }
    contentMetrics: {
      averageRecordsPerReport: number
      mostUsedFilters: {
        filter: string
        usageCount: number
      }[]
      mostUsedDimensions: {
        dimension: string
        usageCount: number
      }[]
      dataQualityScore: number
    }
    userBehaviorMetrics: {
      averageReportsPerUser: number
      reportRetentionRate: number
      shareRate: number
      feedbackScore?: number
      mostActiveUsers: {
        userId: string
        userName: string
        reportCount: number
      }[]
    }
  }
  trends: {
    usageTrend: {
      period: string
      generations: number
      users: number
      change: number
    }[]
    performanceTrend: {
      period: string
      avgExecutionTime: number
      errorRate: number
      change: number
    }[]
  }
  insights: {
    topInsights: {
      insight: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      recommendation: string
    }[]
    anomalies: {
      anomalyType: string
      description: string
      severity: 'HIGH' | 'MEDIUM' | 'LOW'
      detectedAt: string
    }[]
    optimizationOpportunities: {
      area: string
      description: string
      potentialBenefit: string
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
    }[]
  }
  generatedAt: string
  generatedBy: string
}
