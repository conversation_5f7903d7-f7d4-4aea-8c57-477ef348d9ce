/**
 * 班次管理类型定义
 * @description 定义班次模板、排班计划、班次变更申请和班次统计分析相关的接口
 * @module shift
 */

// 班次模板接口 (ATT-SHIFT-001)
export interface AttendanceShiftTemplate {
  id: string
  name: string
  code: string
  type: 'FIXED' | 'FLEXIBLE' | 'ROTATING'
  startTime: string
  endTime: string
  duration: number
  color: string
  description?: string
  workingHours: {
    actualWorkTime: number
    overtimeThreshold: number
    lateThreshold: number
    earlyLeaveThreshold: number
  }
  breakTimes: AttendanceShiftBreakTime[]
  shiftRules: {
    allowEarlyClockIn: boolean
    allowLateClockOut: boolean
    earlyClockInLimit: number
    lateClockOutLimit: number
    minWorkingHours: number
    maxWorkingHours: number
    requireBreak: boolean
    roundingRules: {
      clockInRounding: number
      clockOutRounding: number
      roundingMethod: 'ROUND' | 'FLOOR' | 'CEIL'
    }
  }
  applicableScope: {
    departments: string[]
    positions: string[]
    employees: string[]
    workdays: number[]
    effectiveDate: string
    expiryDate?: string
  }
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  isDefault: boolean
  version: string
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 班次休息时间接口
export interface AttendanceShiftBreakTime {
  id: string
  name: string
  startTime: string
  endTime: string
  duration: number
  isPaid: boolean
  isMandatory: boolean
}

// 班次模板分类接口
export interface AttendanceShiftTemplateCategory {
  id: string
  name: string
  description: string
  templateCount: number
  sortOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 班次模板使用统计接口
export interface AttendanceShiftTemplateUsageStats {
  templateId: string
  templateName: string
  usageCount: number
  activeEmployees: number
  departments: {
    departmentId: string
    departmentName: string
    employeeCount: number
  }[]
  lastUsedDate: string
  usageFrequency: number
  popularityRank: number
}

// 班次模板验证结果接口
export interface AttendanceShiftTemplateValidation {
  isValid: boolean
  errors: {
    field: string
    message: string
    severity: 'ERROR' | 'WARNING'
    code: string
  }[]
  warnings: {
    field: string
    message: string
    recommendation: string
    code: string
  }[]
  suggestions: {
    field: string

    currentValue: unknown

    suggestedValue: unknown
    reason: string
  }[]
  validationSummary: {
    totalChecks: number
    passedChecks: number
    failedChecks: number
    warningChecks: number
    validationScore: number
  }
}

// 班次模板批量操作结果接口
export interface AttendanceShiftTemplateBatchResult {
  batchId: string
  operation: 'CREATE' | 'UPDATE' | 'DELETE' | 'ACTIVATE' | 'DEACTIVATE' | 'ARCHIVE'
  totalTemplates: number
  successfulOperations: number
  failedOperations: number
  results: {
    templateId: string
    templateName: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED'
    message: string
    errorDetails?: string
  }[]
  summary: {
    totalProcessed: number
    successful: number
    failed: number
    skipped: number
    averageProcessingTime: number
  }
  operationTime: string
  operatedBy: string
}

// 班次模板冲突检查结果接口
export interface AttendanceShiftTemplateConflictCheck {
  hasConflicts: boolean
  conflicts: {
    conflictId: string
    conflictType: 'TIME_OVERLAP' | 'SCOPE_OVERLAP' | 'RULE_CONFLICT' | 'RESOURCE_CONFLICT'
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    description: string
    conflictingTemplates: {
      templateId: string
      templateName: string
      conflictReason: string
    }[]
    affectedScopes: {
      type: 'DEPARTMENT' | 'POSITION' | 'EMPLOYEE'
      id: string
      name: string
    }[]
    resolution: {
      resolutionType: 'AUTO' | 'MANUAL' | 'APPROVE'
      resolutionSteps: string[]
      requiresApproval: boolean
    }
    priority: number
  }[]
  resolutionSummary: {
    totalConflicts: number
    autoResolvable: number
    manualResolution: number
    requiresApproval: number
    estimatedResolutionTime: string
  }
}

// 班次模板导入导出结果接口
export interface AttendanceShiftTemplateImportExportResult {
  operationType: 'IMPORT' | 'EXPORT'
  fileId: string
  fileName: string
  fileSize: number
  processedTime: string
  processedBy: string
  status: 'SUCCESS' | 'FAILED' | 'PARTIAL'
  summary: {
    totalRecords: number
    successfulRecords: number
    failedRecords: number
    skippedRecords: number
    duplicateRecords: number
  }
  details: {
    recordIndex: number
    templateName: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED' | 'DUPLICATE'
    message: string
    errorDetails?: string
  }[]
  downloadUrl?: string
  expiryDate?: string
}

// 班次排班计划接口 (ATT-SHIFT-002)
export interface AttendanceShiftSchedule {
  id: string
  planName: string
  planCode: string
  description?: string
  planType: 'WEEKLY' | 'MONTHLY' | 'CUSTOM'
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  effectiveDate: string
  expiryDate?: string
  departmentId: string
  departmentName: string
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  publishedBy?: string
  publishedAt?: string
  scheduleRules: {
    priority: number
    condition: {
      timeRange: [string, string]
      weekdays: number[]
      employeeIds?: string[]
      positionIds?: string[]
      shiftTypes?: string[]
    }
    assignments: AttendanceShiftAssignment[]
  }[]
  statistics: {
    totalEmployees: number
    assignedEmployees: number
    unassignedEmployees: number
    totalShifts: number
    conflictCount: number
    coverageRate: number
  }
  metadata: {
    autoApproval: boolean
    allowOverride: boolean
    notificationEnabled: boolean
    conflictResolution: 'manual' | 'auto' | 'template'
    backupPlan?: string
  }
}

// 班次分配接口
export interface AttendanceShiftAssignment {
  id: string
  scheduleId: string
  employeeId: string
  employeeName: string
  employeeNo: string
  positionId: string
  positionName: string
  date: string
  shiftId: string
  shiftName: string
  shiftCode: string
  startTime: string
  endTime: string
  workingHours: number
  breakTimes: {
    startTime: string
    endTime: string
    duration: number
    type: 'LUNCH' | 'REST' | 'OTHER'
  }[]
  status: 'SCHEDULED' | 'CONFIRMED' | 'CHANGED' | 'CANCELLED' | 'COMPLETED'
  assignedBy: string
  assignedAt: string
  confirmedBy?: string
  confirmedAt?: string
  notes?: string
  conflicts?: {
    type: 'OVERLAP' | 'CONSTRAINT' | 'AVAILABILITY' | 'SKILL' | 'OVERTIME'
    description: string
    severity: 'LOW' | 'MEDIUM' | 'HIGH'
    suggestion?: string
  }[]
}

// 班次排班规则接口
export interface AttendanceShiftScheduleRule {
  id: string
  ruleName: string
  ruleType: 'WEEKLY_PATTERN' | 'MONTHLY_PATTERN' | 'ROTATION' | 'FIXED' | 'FLEXIBLE'
  priority: number
  isActive: boolean
  condition: {
    timeRange: [string, string]
    weekdays: number[]
    monthDays?: number[]
    employeeFilters: {
      departmentIds?: string[]
      positionIds?: string[]
      employeeIds?: string[]
      skillIds?: string[]
      employeeTypes?: string[]
    }
    shiftFilters: {
      shiftIds?: string[]
      shiftTypes?: string[]
      workingHours?: { min: number; max: number }
    }
    constraints: {
      maxConsecutiveDays?: number
      minRestHours?: number
      maxWeeklyHours?: number
      maxMonthlyHours?: number
      skillRequirements?: string[]
    }
  }
  assignment: {
    strategy: 'ROUND_ROBIN' | 'LOAD_BALANCE' | 'SKILL_MATCH' | 'PREFERENCE' | 'MANUAL'
    parameters: {
      rotationCycle?: number
      balanceWeight?: { experience: number; workload: number; preference: number }
      skillThreshold?: number
      preferenceWeight?: number
    }
    fallbackStrategy?: 'RANDOM' | 'SENIORITY' | 'AVAILABILITY' | 'MANUAL'
  }
  validation: {
    allowConflicts: boolean
    allowOvertime: boolean
    requireConfirmation: boolean
    autoResolveConflicts: boolean
    escalationLevel: 'NONE' | 'SUPERVISOR' | 'HR' | 'ADMIN'
  }
}

// 班次排班模板接口
export interface AttendanceShiftScheduleTemplate {
  id: string
  templateName: string
  templateCode: string
  templateType: 'DEPARTMENT' | 'POSITION' | 'TEAM' | 'PROJECT' | 'CUSTOM'
  description?: string
  isDefault: boolean
  isActive: boolean
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  applicableScope: {
    departmentIds: string[]
    positionIds: string[]
    employeeTypes: string[]
    minEmployees: number
    maxEmployees: number
  }
  schedulePattern: {
    patternType: 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY' | 'CUSTOM'
    cycleLength: number
    patternData: {
      dayIndex: number
      weekIndex?: number
      shiftId: string
      isRequired: boolean
      alternativeShiftIds?: string[]
    }[]
    rotationRules: {
      rotationType: 'DAILY' | 'WEEKLY' | 'MONTHLY'
      rotationOrder: string[]
      rotationInterval: number
    }
  }
  constraints: {
    maxConsecutiveShifts: number
    minRestPeriod: number
    maxWeeklyHours: number
    maxMonthlyHours: number
    weekendPolicy: 'REQUIRED' | 'OPTIONAL' | 'PROHIBITED'
    overtimePolicy: 'ALLOWED' | 'RESTRICTED' | 'PROHIBITED'
  }
  preview: {
    samplePeriod: [string, string]
    sampleEmployees: string[]
    generatedSchedule: AttendanceShiftAssignment[]
  }
  usage: {
    usageCount: number
    lastUsed?: string
    avgRating?: number
    feedback?: string[]
  }
}

// 班次排班冲突检测接口
export interface AttendanceShiftConflictDetection {
  scheduleId: string
  detectionTime: string
  conflicts: {
    conflictId: string
    conflictType:
      | 'TIME_OVERLAP'
      | 'DOUBLE_BOOKING'
      | 'OVERTIME_LIMIT'
      | 'SKILL_MISMATCH'
      | 'AVAILABILITY'
      | 'CONSTRAINT_VIOLATION'
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    description: string
    affectedEmployees: {
      employeeId: string
      employeeName: string
      conflictDetails: string
    }[]
    affectedAssignments: string[]
    suggestedResolutions: {
      resolutionType: 'REASSIGN' | 'SPLIT_SHIFT' | 'OVERTIME' | 'ADDITIONAL_STAFF' | 'MANUAL'
      description: string
      effort: 'LOW' | 'MEDIUM' | 'HIGH'
      impact: string
      autoApplicable: boolean
    }[]
    resolutionStatus: 'PENDING' | 'RESOLVED' | 'IGNORED' | 'ESCALATED'
    resolvedBy?: string
    resolvedAt?: string
    resolution?: string
  }[]
  summary: {
    totalConflicts: number
    criticalConflicts: number
    resolvedConflicts: number
    pendingConflicts: number
    affectedEmployeeCount: number
    affectedShiftCount: number
  }
}

// 班次排班统计接口
export interface AttendanceShiftScheduleStatistics {
  scheduleId: string
  statisticsDate: string
  period: [string, string]
  overall: {
    totalSchedules: number
    publishedSchedules: number
    draftSchedules: number
    archivedSchedules: number
    totalAssignments: number
    confirmedAssignments: number
    pendingAssignments: number
    cancelledAssignments: number
    averageUtilization: number
    coverageRate: number
  }
  byDepartment: {
    departmentId: string
    departmentName: string
    scheduleCount: number
    assignmentCount: number
    utilization: number
    conflictCount: number
    completionRate: number
  }[]
  byShift: {
    shiftId: string
    shiftName: string
    assignmentCount: number
    utilization: number
    preferenceScore: number
    conflictRate: number
  }[]
  byEmployee: {
    employeeId: string
    employeeName: string
    totalAssignments: number
    confirmedAssignments: number
    workingHours: number
    overtimeHours: number
    utilization: number
    satisfactionScore?: number
  }[]
  trends: {
    utilizationTrend: { period: string; value: number }[]
    conflictTrend: { period: string; value: number }[]
    completionTrend: { period: string; value: number }[]
    satisfactionTrend: { period: string; value: number }[]
  }
  performance: {
    planningEfficiency: number
    conflictResolutionTime: number
    changeRequestRate: number
    noShowRate: number
    overtimeRate: number
    costPerHour: number
  }
}

// 班次排班批量操作接口
export interface AttendanceShiftScheduleBatchOperation {
  operationType: 'CREATE' | 'UPDATE' | 'DELETE' | 'PUBLISH' | 'COPY' | 'ARCHIVE'
  operationName: string
  description?: string
  targetScope: {
    scheduleIds?: string[]
    departmentIds?: string[]
    employeeIds?: string[]
    dateRange?: [string, string]
    shiftIds?: string[]
  }
  operationData: {
    templateId?: string
    sourceScheduleId?: string
    targetScheduleId?: string
    modifications?: {
      field: string

      oldValue: unknown

      newValue: unknown
    }[]
    copyOptions?: {
      includeAssignments: boolean
      includeRules: boolean
      adjustDates: boolean
      targetPeriod?: [string, string]
    }
  }
  validation: {
    skipValidation: boolean
    allowConflicts: boolean
    requireConfirmation: boolean
    notifyAffected: boolean
  }
  scheduling: {
    executeImmediately: boolean
    scheduledTime?: string
    priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  }
}

// 班次排班批量操作结果接口
export interface AttendanceShiftScheduleBatchResult {
  operationId: string
  operationType: string
  operationName: string
  startTime: string
  endTime: string
  duration: number
  status: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED' | 'CANCELLED'
  summary: {
    totalItems: number
    successCount: number
    failedCount: number
    skippedCount: number
    warningCount: number
  }
  results: {
    itemId: string
    itemType: string
    itemName: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED' | 'WARNING'
    message: string

    details?: unknown
    errorCode?: string
  }[]
  conflicts: {
    conflictId: string
    description: string
    affectedItems: string[]
    resolution: string
  }[]
  notifications: {
    notificationType: 'EMAIL' | 'SMS' | 'SYSTEM' | 'WEBHOOK'
    recipients: string[]
    status: 'SENT' | 'PENDING' | 'FAILED'
    sentAt?: string
  }[]
}

// 班次变更申请接口 (ATT-SHIFT-003)
export interface AttendanceShiftChangeRequest {
  id: string
  requestNo: string
  requestType: 'INDIVIDUAL' | 'EXCHANGE' | 'TEMPORARY' | 'PERMANENT' | 'EMERGENCY'
  status: 'DRAFT' | 'SUBMITTED' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED' | 'EXPIRED'
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  requesterId: string
  requesterName: string
  requesterNo: string
  departmentId: string
  departmentName: string
  positionId: string
  positionName: string
  submittedAt: string
  effectiveDate: string
  expiryDate?: string
  reason: string
  reasonCategory: 'PERSONAL' | 'MEDICAL' | 'FAMILY' | 'WORK' | 'EMERGENCY' | 'TRAINING' | 'OTHER'
  description?: string
  attachments?: {
    fileId: string
    fileName: string
    fileSize: number
    fileType: string
    uploadAt: string
  }[]
  originalShift: {
    scheduleId: string
    assignmentId: string
    date: string
    shiftId: string
    shiftName: string
    shiftCode: string
    startTime: string
    endTime: string
    workingHours: number
  }
  requestedShift: {
    date: string
    shiftId: string
    shiftName: string
    shiftCode: string
    startTime: string
    endTime: string
    workingHours: number
    isNewShift?: boolean
  }
  exchangePartner?: {
    partnerId: string
    partnerName: string
    partnerNo: string
    partnerDepartment: string
    partnerPosition: string
    partnerShift: {
      scheduleId: string
      assignmentId: string
      date: string
      shiftId: string
      shiftName: string
      shiftCode: string
      startTime: string
      endTime: string
      workingHours: number
    }
    mutualConsent: boolean
    consentAt?: string
  }
  impact: {
    affectedEmployees: number
    affectedShifts: number
    workingHoursChange: number
    overtimeHoursChange: number
    costImpact: number
    coverageImpact: number
    conflictRisk: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  }
  approval: {
    approvalFlow: {
      stepId: string
      stepName: string
      stepType: 'AUTO' | 'MANUAL'
      approverType: 'SUPERVISOR' | 'HR' | 'DEPARTMENT_HEAD' | 'SYSTEM'
      approverId?: string
      approverName?: string
      status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SKIPPED'
      processedAt?: string
      comments?: string
      decisionReason?: string
    }[]
    currentStep: number
    finalDecision?: 'APPROVED' | 'REJECTED'
    finalDecisionAt?: string
    finalDecisionBy?: string
    finalComments?: string
  }
  notifications: {
    requesterNotified: boolean
    supervisorNotified: boolean
    hrNotified: boolean
    partnerNotified: boolean
    systemNotified: boolean
    lastNotificationAt?: string
  }
  metadata: {
    createdBy: string
    createdAt: string
    updatedBy: string
    updatedAt: string
    processingTime?: number
    automaticApproval: boolean
    urgentProcessing: boolean
    requiresManagerApproval: boolean
    requiresHrApproval: boolean
  }
}

// 班次变更申请配置接口
export interface AttendanceShiftChangeConfig {
  id: string
  configName: string
  departmentId: string
  departmentName: string
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  rules: {
    allowedRequestTypes: ('INDIVIDUAL' | 'EXCHANGE' | 'TEMPORARY' | 'PERMANENT' | 'EMERGENCY')[]
    advanceNoticeHours: {
      normal: number
      urgent: number
      emergency: number
    }
    approvalRequired: {
      individual: boolean
      exchange: boolean
      temporary: boolean
      permanent: boolean
      emergency: boolean
    }
    approvalFlow: {
      stepOrder: number
      stepName: string
      approverType: 'SUPERVISOR' | 'HR' | 'DEPARTMENT_HEAD' | 'SYSTEM'
      isRequired: boolean
      autoApprovalConditions?: {
        maxHoursChange: number
        maxDaysAdvance: number
        allowedReasonCategories: string[]
        requesterLevels: string[]
      }
      escalationTime: number
    }[]
    restrictions: {
      maxRequestsPerMonth: number
      maxHoursChangePerRequest: number
      blackoutPeriods: {
        startDate: string
        endDate: string
        reason: string
        affectedShiftTypes?: string[]
      }[]
      minimumCoverageRatio: number
      conflictResolution: 'REJECT' | 'QUEUE' | 'APPROVE_WITH_CONDITIONS'
    }
    notifications: {
      notifyRequester: boolean
      notifySupervisor: boolean
      notifyHr: boolean
      notifyPartner: boolean
      reminderIntervals: number[]
      escalationNotifications: boolean
    }
  }
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
}

// 班次变更申请统计接口
export interface AttendanceShiftChangeStatistics {
  periodStart: string
  periodEnd: string
  overall: {
    totalRequests: number
    pendingRequests: number
    approvedRequests: number
    rejectedRequests: number
    cancelledRequests: number
    expiredRequests: number
    approvalRate: number
    averageProcessingTime: number
    averageResponseTime: number
  }
  byType: {
    requestType: string
    requestCount: number
    approvalRate: number
    averageProcessingTime: number
    rejectionReasons: {
      reason: string
      count: number
      percentage: number
    }[]
  }[]
  byDepartment: {
    departmentId: string
    departmentName: string
    requestCount: number
    approvalRate: number
    averageProcessingTime: number
    topReasons: {
      reasonCategory: string
      count: number
      percentage: number
    }[]
  }[]
  byEmployee: {
    employeeId: string
    employeeName: string
    employeeNo: string
    requestCount: number
    approvedCount: number
    rejectedCount: number
    averageProcessingTime: number
    lastRequestDate?: string
  }[]
  trends: {
    requestVolumeTrend: { period: string; count: number }[]
    approvalRateTrend: { period: string; rate: number }[]
    processingTimeTrend: { period: string; time: number }[]
    reasonCategoryTrend: { period: string; category: string; count: number }[]
  }
  patterns: {
    peakRequestHours: { hour: number; count: number }[]
    peakRequestDays: { day: string; count: number }[]
    seasonalPatterns: { month: string; count: number; variance: number }[]
    urgencyDistribution: { priority: string; count: number; percentage: number }[]
  }
  performance: {
    slaCompliance: number
    escalationRate: number
    automationRate: number
    satisfactionScore: number
    costPerRequest: number
    efficiencyIndex: number
  }
}

// 班次变更申请批量操作接口
export interface AttendanceShiftChangeBatchOperation {
  operationType: 'APPROVE' | 'REJECT' | 'CANCEL' | 'PROCESS' | 'NOTIFY' | 'EXPORT'
  operationName: string
  description?: string
  targetRequests: {
    requestIds?: string[]
    filters?: {
      departmentIds?: string[]
      requesterIds?: string[]
      requestTypes?: string[]
      status?: string[]
      dateRange?: [string, string]
      priority?: string[]
      reasonCategories?: string[]
    }
  }
  operationData: {
    approvalComments?: string
    rejectionReason?: string
    decisionReason?: string
    notificationTemplate?: string
    exportFormat?: 'EXCEL' | 'CSV' | 'PDF'
    customFields?: {
      field: string

      value: unknown
    }[]
  }
  approvalSettings: {
    requireManagerApproval: boolean
    requireHrApproval: boolean
    allowBulkApproval: boolean
    notifyAffectedEmployees: boolean
    validateConflicts: boolean
  }
  scheduling: {
    executeImmediately: boolean
    scheduledTime?: string
    priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  }
}

// 班次变更申请批量操作结果接口
export interface AttendanceShiftChangeBatchResult {
  operationId: string
  operationType: string
  operationName: string
  startTime: string
  endTime: string
  duration: number
  status: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED' | 'CANCELLED'
  summary: {
    totalRequests: number
    successCount: number
    failedCount: number
    skippedCount: number
    warningCount: number
  }
  results: {
    requestId: string
    requestNo: string
    requesterName: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED' | 'WARNING'
    message: string
    processedAt: string
    errorDetails?: string
  }[]
  conflicts: {
    conflictId: string
    description: string
    affectedRequests: string[]
    resolution: string
    resolvedAt?: string
  }[]
  notifications: {
    totalRecipients: number
    successfulNotifications: number
    failedNotifications: number
    notificationDetails: {
      recipientId: string
      recipientName: string
      notificationType: 'EMAIL' | 'SMS' | 'SYSTEM'
      status: 'SENT' | 'FAILED'
      sentAt?: string
      errorMessage?: string
    }[]
  }
  impact: {
    affectedEmployees: number
    affectedShifts: number
    scheduleChanges: number
    costImpact: number
    coverageImpact: number
  }
}

// 班次变更申请验证接口
export interface AttendanceShiftChangeValidation {
  requestId: string
  validationTime: string
  isValid: boolean
  validationResults: {
    validationType:
      | 'BUSINESS_RULE'
      | 'SCHEDULE_CONFLICT'
      | 'POLICY_COMPLIANCE'
      | 'RESOURCE_AVAILABILITY'
      | 'APPROVAL_FLOW'
    isValid: boolean
    errors: {
      errorCode: string
      errorMessage: string
      field: string
      severity: 'ERROR' | 'WARNING' | 'INFO'
      suggestion?: string
    }[]
    warnings: {
      warningCode: string
      warningMessage: string
      field: string
      recommendation: string
      canProceed: boolean
    }[]
  }[]
  impactAssessment: {
    scheduleImpact: {
      affectedAssignments: number
      conflictingAssignments: string[]
      coverageGaps: {
        date: string
        shiftId: string
        gapHours: number
        severity: 'LOW' | 'MEDIUM' | 'HIGH'
      }[]
    }
    resourceImpact: {
      overtimeRequired: number
      additionalStaffNeeded: number
      skillGaps: string[]
      costIncrease: number
    }
    complianceImpact: {
      policyViolations: string[]
      regulatoryRisks: string[]
      auditFindings: string[]
    }
  }
  recommendations: {
    priority: 'HIGH' | 'MEDIUM' | 'LOW'
    category: 'APPROVAL' | 'MODIFICATION' | 'ALTERNATIVE' | 'REJECTION'
    description: string
    rationale: string
    actionRequired: string
    timeline: string
  }[]
  autoResolutions: {
    resolutionId: string
    resolutionType: 'AUTO_APPROVE' | 'AUTO_REJECT' | 'REQUIRE_ESCALATION' | 'SUGGEST_ALTERNATIVE'
    confidence: number
    reasoning: string
    applicableConditions: string[]
  }[]
}

// 班次统计分析接口 (ATT-SHIFT-004)
export interface AttendanceShiftAnalytics {
  analysisId: string
  analysisName: string
  analysisType: 'COMPREHENSIVE' | 'UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY' | 'COMPLIANCE'
  period: [string, string]
  scope: {
    departmentIds: string[]
    employeeIds: string[]
    shiftIds: string[]
    positionIds: string[]
    includeSubDepartments: boolean
  }
  generatedAt: string
  generatedBy: string
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'EXPIRED'
  overview: {
    totalShifts: number
    totalWorkingHours: number
    totalEmployees: number
    totalDepartments: number
    analysisTimespan: number
    dataQuality: number
    completenessRate: number
  }
  utilizationAnalysis: {
    overall: {
      plannedHours: number
      actualHours: number
      utilizationRate: number
      efficiency: number
      idleTime: number
      overtimeHours: number
      undertimeHours: number
    }
    byShift: {
      shiftId: string
      shiftName: string
      shiftType: string
      plannedHours: number
      actualHours: number
      utilizationRate: number
      assignmentCount: number
      completionRate: number
      averageWorkingHours: number
    }[]
    byDepartment: {
      departmentId: string
      departmentName: string
      plannedHours: number
      actualHours: number
      utilizationRate: number
      efficiency: number
      staffCount: number
      shiftCount: number
    }[]
    byEmployee: {
      employeeId: string
      employeeName: string
      employeeNo: string
      departmentName: string
      positionName: string
      plannedHours: number
      actualHours: number
      utilizationRate: number
      shiftsAssigned: number
      shiftsCompleted: number
      overtime: number
      absentHours: number
    }[]
  }
  efficiencyMetrics: {
    productivity: {
      outputPerHour: number
      qualityScore: number
      efficiencyIndex: number
      performanceRating: number
    }
    timeManagement: {
      punctualityRate: number
      averageEarlyArrival: number
      averageLateArrival: number
      averageEarlyDeparture: number
      averageOvertime: number
    }
    workload: {
      averageWorkload: number
      workloadVariation: number
      peakWorkloadTime: string
      lowWorkloadTime: string
      workloadDistribution: {
        timeSlot: string
        workload: number
        efficiency: number
      }[]
    }
  }
  costAnalysis: {
    totalCost: number
    costPerHour: number
    costPerEmployee: number
    costBreakdown: {
      baseSalary: number
      overtimeCost: number
      benefitsCost: number
      additionalCost: number
      penaltyCost: number
    }
    costComparison: {
      previousPeriod: number
      budgetComparison: number
      industryAverage: number
      variance: number
      variancePercentage: number
    }
    costOptimization: {
      potentialSavings: number
      optimizationAreas: string[]
      recommendations: {
        area: string
        description: string
        expectedSavings: number
        implementationCost: number
        roi: number
      }[]
    }
  }
  qualityMetrics: {
    attendance: {
      attendanceRate: number
      punctualityRate: number
      absenteeismRate: number
      tardiness: number
      earlyLeaveRate: number
    }
    performance: {
      completionRate: number
      qualityScore: number
      customerSatisfaction: number
      errorRate: number
      reworkRate: number
    }
    compliance: {
      policyCompliance: number
      regulatoryCompliance: number
      safetyCompliance: number
      auditScore: number
    }
  }
  trendAnalysis: {
    utilizationTrend: {
      period: string
      utilization: number
      efficiency: number
      cost: number
      quality: number
    }[]
    seasonalPatterns: {
      month: string
      utilization: number
      efficiency: number
      cost: number
      variance: number
    }[]
    workloadPatterns: {
      dayOfWeek: string
      hour: number
      workload: number
      efficiency: number
      cost: number
    }[]
    performanceTrends: {
      metric: string
      trend: 'IMPROVING' | 'STABLE' | 'DECLINING'
      changeRate: number
      projection: number
      confidence: number
    }[]
  }
  comparativeAnalysis: {
    departmentComparison: {
      departmentId: string
      departmentName: string
      utilization: number
      efficiency: number
      cost: number
      quality: number
      ranking: number
    }[]
    shiftComparison: {
      shiftId: string
      shiftName: string
      utilization: number
      efficiency: number
      cost: number
      quality: number
      ranking: number
    }[]
    employeeComparison: {
      employeeId: string
      employeeName: string
      utilization: number
      efficiency: number
      performance: number
      ranking: number
    }[]
  }
  insights: {
    keyFindings: {
      finding: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      category: 'UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY' | 'COMPLIANCE'
      description: string
      recommendation: string
    }[]
    anomalies: {
      anomalyType: 'UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY' | 'PATTERN'
      severity: 'HIGH' | 'MEDIUM' | 'LOW'
      description: string
      affectedArea: string
      possibleCauses: string[]
      suggestedActions: string[]
    }[]
    opportunities: {
      opportunityType: 'OPTIMIZATION' | 'IMPROVEMENT' | 'EXPANSION' | 'REDUCTION'
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      description: string
      potentialBenefit: number
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
    }[]
  }
  predictions: {
    utilizationForecast: {
      period: string
      predictedUtilization: number
      confidence: number
      factors: string[]
    }[]
    costForecast: {
      period: string
      predictedCost: number
      confidence: number
      factors: string[]
    }[]
    demandForecast: {
      period: string
      predictedDemand: number
      confidence: number
      factors: string[]
    }[]
  }
}

// 班次统计配置接口
export interface AttendanceShiftAnalyticsConfig {
  configId: string
  configName: string
  configType: 'STANDARD' | 'CUSTOM' | 'TEMPLATE'
  isActive: boolean
  isDefault: boolean
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  scope: {
    departmentIds: string[]
    employeeIds: string[]
    shiftIds: string[]
    positionIds: string[]
    includeSubDepartments: boolean
  }
  metrics: {
    includedMetrics: ('UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY' | 'COMPLIANCE')[]
    customMetrics: {
      metricName: string
      metricType: 'PERCENTAGE' | 'NUMBER' | 'CURRENCY' | 'TIME'
      calculation: string
      description: string
    }[]
    weightings: {
      utilization: number
      efficiency: number
      cost: number
      quality: number
      compliance: number
    }
  }
  thresholds: {
    utilizationThreshold: {
      excellent: number
      good: number
      fair: number
      poor: number
    }
    efficiencyThreshold: {
      excellent: number
      good: number
      fair: number
      poor: number
    }
    costThreshold: {
      excellent: number
      good: number
      fair: number
      poor: number
    }
    qualityThreshold: {
      excellent: number
      good: number
      fair: number
      poor: number
    }
  }
  reporting: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
    recipients: string[]
    format: 'PDF' | 'EXCEL' | 'HTML' | 'JSON'
    includeCharts: boolean
    includeTrends: boolean
    includeComparisons: boolean
    includeRecommendations: boolean
  }
  alerts: {
    enableAlerts: boolean
    thresholds: {
      utilizationAlert: number
      efficiencyAlert: number
      costAlert: number
      qualityAlert: number
    }
    recipients: string[]
    frequency: 'IMMEDIATE' | 'DAILY' | 'WEEKLY'
  }
}

// 班次统计报表接口
export interface AttendanceShiftReport {
  reportId: string
  reportName: string
  reportType: 'SUMMARY' | 'DETAILED' | 'COMPARISON' | 'TREND' | 'FORECAST'
  period: [string, string]
  scope: {
    departmentIds: string[]
    employeeIds: string[]
    shiftIds: string[]
  }
  generatedAt: string
  generatedBy: string
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'EXPIRED'
  format: 'PDF' | 'EXCEL' | 'HTML' | 'JSON'
  downloadUrl?: string
  expiryDate?: string
  sections: {
    sectionId: string
    sectionName: string
    sectionType: 'CHART' | 'TABLE' | 'SUMMARY' | 'TEXT'

    content: unknown
    order: number
  }[]
  summary: {
    totalShifts: number
    totalHours: number
    totalEmployees: number
    keyMetrics: {
      metricName: string
      value: number
      unit: string
      trend: 'UP' | 'DOWN' | 'STABLE'
      change: number
    }[]
    topPerformers: {
      category: string
      name: string
      value: number
      unit: string
    }[]
    bottomPerformers: {
      category: string
      name: string
      value: number
      unit: string
    }[]
  }
  charts: {
    chartId: string
    chartName: string
    chartType: 'LINE' | 'BAR' | 'PIE' | 'SCATTER' | 'HEATMAP'

    data: unknown

    config: unknown
  }[]
  recommendations: {
    priority: 'HIGH' | 'MEDIUM' | 'LOW'
    category: 'UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY'
    description: string
    action: string
    expectedBenefit: string
    timeline: string
  }[]
}

// 班次统计导出接口
export interface AttendanceShiftAnalyticsExport {
  exportId: string
  exportName: string
  exportType: 'DATA' | 'REPORT' | 'ANALYSIS' | 'DASHBOARD'
  format: 'EXCEL' | 'CSV' | 'PDF' | 'JSON'
  scope: {
    departmentIds: string[]
    employeeIds: string[]
    shiftIds: string[]
    period: [string, string]
  }
  includeData: {
    rawData: boolean
    aggregatedData: boolean
    charts: boolean
    trends: boolean
    comparisons: boolean
    recommendations: boolean
  }
  customization: {
    fields: string[]
    filters: {
      field: string
      operator: 'EQUALS' | 'CONTAINS' | 'GREATER_THAN' | 'LESS_THAN' | 'BETWEEN'

      value: unknown
    }[]
    sorting: {
      field: string
      order: 'ASC' | 'DESC'
    }[]
    grouping: string[]
  }
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  progress: number
  requestedAt: string
  requestedBy: string
  completedAt?: string
  downloadUrl?: string
  expiryDate?: string
  fileSize?: number
  error?: string
}

// 班次统计仪表板接口
export interface AttendanceShiftDashboard {
  dashboardId: string
  dashboardName: string
  dashboardType: 'EXECUTIVE' | 'MANAGER' | 'ANALYST' | 'OPERATIONAL'
  ownerId: string
  ownerName: string
  isPublic: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
  lastViewedAt: string
  layout: {
    columns: number
    rows: number
    responsive: boolean
  }
  widgets: {
    widgetId: string
    widgetName: string
    widgetType: 'METRIC' | 'CHART' | 'TABLE' | 'GAUGE' | 'ALERT'
    position: {
      x: number
      y: number
      width: number
      height: number
    }
    configuration: {
      dataSource: string
      metrics: string[]
      filters: {
        field: string

        value: unknown
      }[]
      refreshInterval: number
      chartType?: 'LINE' | 'BAR' | 'PIE' | 'SCATTER' | 'HEATMAP'
      thresholds?: {
        warning: number
        critical: number
      }
    }

    data: unknown
    lastUpdated: string
  }[]
  filters: {
    filterId: string
    filterName: string
    filterType: 'DEPARTMENT' | 'EMPLOYEE' | 'SHIFT' | 'DATE' | 'POSITION'

    values: unknown[]
    isActive: boolean
  }[]
  sharing: {
    shareId: string
    shareType: 'VIEW' | 'EDIT' | 'ADMIN'
    sharedWith: string[]
    expiryDate?: string
    publicUrl?: string
  }[]
  alerts: {
    alertId: string
    alertName: string
    condition: string
    threshold: number
    isActive: boolean
    recipients: string[]
    lastTriggered?: string
  }[]
}
