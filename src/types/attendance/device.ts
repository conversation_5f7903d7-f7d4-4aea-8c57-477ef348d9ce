/**
 * 考勤设备相关类型定义
 *
 * 本模块包含所有考勤设备相关的接口定义，包括：
 * - 设备基础信息管理
 * - 设备状态监控
 * - 设备配置管理
 * - 设备故障处理
 * - 设备性能分析
 * - 设备预警配置
 */

// 考勤设备接口
export interface AttendanceDevice {
  id: string
  deviceName: string
  deviceType: string
  manufacturer: string
  model: string
  serialNumber: string
  ipAddress: string
  port: number
  username?: string
  password?: string
  location: string
  description?: string
  status: 'online' | 'offline' | 'syncing' | 'error'
  lastSyncTime: string
  nextSyncTime?: string
  syncCount: number
  errorCount: number
  firmware: string
  capacity: number
  recordCount: number
  lastActivity: string
  networkStatus: string
  batteryLevel?: number
  syncConfig: {
    syncInterval: number
    syncTimeRange: [string, string]
    autoSync: boolean
    retryCount: number
    retryInterval: number
    conflictStrategy: 'skip' | 'override' | 'merge'
    deduplication: boolean
    dataValidation: boolean
    notifications: string[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 考勤设备统计接口
export interface AttendanceDeviceStatistics {
  totalDevices: number
  onlineDevices: number
  offlineDevices: number
  syncingDevices: number
  errorDevices: number
  totalRecords: number
  todayRecords: number
  successRate: number
  averageResponseTime: number
  lastSyncTime: string
}

// 考勤设备信息接口
export interface AttendanceDeviceInfo {
  deviceName: string
  firmware: string
  capacity: number
  recordCount: number
  lastActivity: string
  networkStatus: string
  batteryLevel?: number
  temperature?: number
  humidity?: number
  diskSpace?: {
    total: number
    used: number
    available: number
  }
}

// 考勤设备批量配置接口
export interface AttendanceDeviceBatchConfig {
  deviceIds: string[]
  config: {
    syncInterval: number
    syncTimeRange: [string, string]
    autoSync: boolean
    retryCount: number
    retryInterval: number
    conflictStrategy: 'skip' | 'override' | 'merge'
    deduplication: boolean
  }
}

// 考勤设备批量操作结果接口
export interface AttendanceDeviceBatchResult {
  successCount: number
  failedCount: number
  results: {
    deviceId: string
    deviceName: string
    status: 'success' | 'failed'
    message: string
  }[]
}

// 考勤设备状态监控接口 (ATT-DEV-002)
export interface AttendanceDeviceStatusOverview {
  summary: {
    totalDevices: number
    onlineDevices: number
    offlineDevices: number
    errorDevices: number
    syncingDevices: number
    maintenanceDevices: number
    lastUpdateTime: string
  }
  deviceStatusList: {
    deviceId: string
    deviceName: string
    deviceType: string
    location: string
    status: 'online' | 'offline' | 'error' | 'syncing' | 'maintenance'
    lastHeartbeat: string
    responseTime: number
    errorCount: number
    uptime: number
    batteryLevel?: number
    networkSignal?: number
    cpuUsage?: number
    memoryUsage?: number
    temperature?: number
    alertLevel: 'none' | 'low' | 'medium' | 'high' | 'critical'
    alertMessage?: string
  }[]
  systemHealth: {
    overallHealth: 'healthy' | 'warning' | 'critical'
    healthScore: number
    availabilityRate: number
    performanceScore: number
    reliabilityScore: number
  }
}

// 考勤设备详细状态接口
export interface AttendanceDeviceDetailedStatus {
  deviceInfo: {
    deviceId: string
    deviceName: string
    deviceType: string
    manufacturer: string
    model: string
    serialNumber: string
    ipAddress: string
    port: number
    location: string
    installDate: string
    lastMaintenanceDate: string
    warrantyExpiry: string
  }
  currentStatus: {
    status: 'online' | 'offline' | 'error' | 'syncing' | 'maintenance'
    lastHeartbeat: string
    responseTime: number
    uptime: number
    connectionStability: number
    dataIntegrity: number
    performanceScore: number
    healthScore: number
  }
  hardwareMetrics: {
    cpuUsage: number
    memoryUsage: number
    diskUsage: number
    temperature: number
    batteryLevel?: number
    networkSignal?: number
    firmwareVersion: string
    lastFirmwareUpdate: string
  }
  networkStatus: {
    connectionType: 'wired' | 'wireless' | 'mobile'
    ipAddress: string
    macAddress: string
    networkSpeed: number
    latency: number
    packetLoss: number
    dnsStatus: 'resolved' | 'failed'
    gatewayStatus: 'reachable' | 'unreachable'
  }
  operationalMetrics: {
    totalRecords: number
    recordsToday: number
    recordsThisWeek: number
    recordsThisMonth: number
    averageRecordsPerDay: number
    lastRecordTime: string
    errorRate: number
    successRate: number
  }
  alertsAndEvents: {
    activeAlerts: AttendanceDeviceAlert[]
    recentEvents: AttendanceDeviceEvent[]
  }
}

// 考勤设备告警接口
export interface AttendanceDeviceAlert {
  alertId: string
  deviceId: string
  deviceName: string
  deviceType: string
  location: string
  alertType:
    | 'device_offline'
    | 'connection_timeout'
    | 'sync_failure'
    | 'hardware_error'
    | 'performance_degradation'
    | 'maintenance_required'
  severity: 'low' | 'medium' | 'high' | 'critical'
  status: 'active' | 'acknowledged' | 'resolved'
  message: string
  description: string
  triggeredAt: string
  acknowledgedAt?: string
  acknowledgedBy?: string
  resolvedAt?: string
  resolvedBy?: string
  autoResolved: boolean
  notificationSent: boolean
  escalationLevel: number
  affectedUsers: number
  estimatedImpact: string
  recommendedActions: string[]
  relatedAlerts: string[]
}

// 考勤设备事件接口
export interface AttendanceDeviceEvent {
  eventId: string
  eventType: 'status_change' | 'alert_triggered' | 'maintenance_started' | 'configuration_updated'
  description: string
  timestamp: string
  userId?: string
  userName?: string
}

// 考勤设备状态历史接口
export interface AttendanceDeviceStatusHistory {
  deviceId: string
  deviceName: string
  timeRange: {
    startDate: string
    endDate: string
    granularity: string
  }
  metrics: {
    timestamp: string
    status: string
    uptime: number
    responseTime: number
    errorRate: number
    performanceScore: number
    cpuUsage?: number
    memoryUsage?: number
    temperature?: number
    batteryLevel?: number
    recordCount: number
    alerts: {
      alertType: string
      severity: string
      message: string
    }[]
  }[]
  statistics: {
    totalDataPoints: number
    averageUptime: number
    averageResponseTime: number
    averageErrorRate: number
    averagePerformanceScore: number
    maxResponseTime: number
    minResponseTime: number
    totalAlerts: number
    totalDowntime: number
    availabilityRate: number
  }
  total: number
}

// 考勤设备性能指标接口
export interface AttendanceDevicePerformanceMetrics {
  deviceId: string
  deviceName: string
  timeRange: {
    startDate: string
    endDate: string
    granularity: string
  }
  metrics: {
    cpu: AttendanceDeviceMetricData
    memory: AttendanceDeviceMetricData
    disk: AttendanceDeviceMetricData
    network: AttendanceDeviceMetricData
    responseTime: AttendanceDeviceMetricData
    throughput: AttendanceDeviceMetricData
    errorRate: AttendanceDeviceMetricData
  }
  analysis: {
    overallHealth: 'healthy' | 'warning' | 'critical'
    healthScore: number
    recommendations: string[]
    anomalies: {
      metricType: string
      severity: string
      description: string
      timestamp: string
      value: number
      threshold: number
    }[]
    trends: {
      metricType: string
      trend: 'increasing' | 'decreasing' | 'stable'
      confidence: number
      prediction: number
      timeframe: string
    }[]
  }
}

// 考勤设备指标数据接口
export interface AttendanceDeviceMetricData {
  timestamps: string[]
  values: number[]
  average: number
  maximum: number
  minimum: number
  threshold: number
  status: 'normal' | 'warning' | 'critical'
}

// 考勤设备健康检查接口
export interface AttendanceDeviceHealthCheck {
  checkId: string
  deviceId: string
  deviceName: string
  checkType: string
  startTime: string
  endTime?: string
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  results: {
    overall: {
      healthScore: number
      status: 'healthy' | 'warning' | 'critical'
      summary: string
      recommendations: string[]
    }
    connectivity: {
      status: 'pass' | 'fail' | 'warning'
      responseTime: number
      packetLoss: number
      message: string
    }
    hardware: {
      status: 'pass' | 'fail' | 'warning'
      cpuTest: { status: string; value: number; threshold: number }
      memoryTest: { status: string; value: number; threshold: number }
      diskTest: { status: string; value: number; threshold: number }
      temperatureTest: { status: string; value: number; threshold: number }
      message: string
    }
    software: {
      status: 'pass' | 'fail' | 'warning'
      firmwareVersion: string
      isLatest: boolean
      configurationValid: boolean
      serviceStatus: string
      message: string
    }
    dataIntegrity: {
      status: 'pass' | 'fail' | 'warning'
      recordCount: number
      corruptedRecords: number
      missingRecords: number
      dataQualityScore: number
      message: string
    }
    security: {
      status: 'pass' | 'fail' | 'warning'
      certificateValid: boolean
      encryptionEnabled: boolean
      accessControlValid: boolean
      vulnerabilities: string[]
      message: string
    }
    performance: {
      status: 'pass' | 'fail' | 'warning'
      throughput: number
      errorRate: number
      averageResponseTime: number
      performanceScore: number
      message: string
    }
  }
  issues: {
    severity: 'low' | 'medium' | 'high' | 'critical'
    category: 'connectivity' | 'hardware' | 'software' | 'data' | 'security' | 'performance'
    description: string
    impact: string
    recommendedAction: string
    priority: number
  }[]
}

// 考勤设备监控配置接口
export interface AttendanceDeviceMonitoringConfig {
  deviceId: string
  deviceName: string
  monitoringEnabled: boolean
  monitoringConfig: {
    heartbeatInterval: number
    healthCheckInterval: number
    performanceMetricsInterval: number
    alertThresholds: {
      responseTime: { warning: number; critical: number }
      cpuUsage: { warning: number; critical: number }
      memoryUsage: { warning: number; critical: number }
      diskUsage: { warning: number; critical: number }
      temperature: { warning: number; critical: number }
      errorRate: { warning: number; critical: number }
      uptime: { warning: number; critical: number }
    }
    alertSettings: {
      enableEmailAlerts: boolean
      enableSmsAlerts: boolean
      enableWebhookAlerts: boolean
      alertRecipients: string[]
      escalationRules: {
        timeToEscalate: number
        escalationLevels: {
          level: number
          recipients: string[]
          actions: string[]
        }[]
      }
    }
    retentionPolicy: {
      statusHistoryDays: number
      performanceMetricsDays: number
      alertHistoryDays: number
      logRetentionDays: number
    }
    maintenanceWindow: {
      enabled: boolean
      weeklySchedule: {
        dayOfWeek: number
        startTime: string
        endTime: string
      }[]
      disableAlertsInMaintenance: boolean
    }
  }
}

// 考勤设备监控报告接口
export interface AttendanceDeviceMonitoringReport {
  reportId: string
  reportType: string
  generatedAt: string
  timeRange: {
    startDate: string
    endDate: string
    period: string
  }
  summary: {
    totalDevices: number
    monitoredDevices: number
    healthyDevices: number
    warningDevices: number
    criticalDevices: number
    averageUptime: number
    averageResponseTime: number
    totalAlerts: number
    resolvedAlerts: number
    outstandingAlerts: number
  }
  deviceReports: {
    deviceId: string
    deviceName: string
    deviceType: string
    location: string
    overallHealth: 'healthy' | 'warning' | 'critical'
    healthScore: number
    uptime: number
    availabilityRate: number
    averageResponseTime: number
    totalRecords: number
    errorRate: number
    alertsCount: number
    lastMaintenanceDate: string
    recommendedActions: string[]
  }[]
  trends: {
    uptimeTrend: { period: string; value: number }[]
    responseTimeTrend: { period: string; value: number }[]
    errorRateTrend: { period: string; value: number }[]
    alertsTrend: { period: string; value: number }[]
  }
  comparisons: {
    periodComparison: {
      metric: string
      currentValue: number
      previousValue: number
      change: number
      changePercentage: number
      trend: 'improving' | 'declining' | 'stable'
    }[]
    deviceComparison: {
      bestPerforming: string[]
      worstPerforming: string[]
      mostReliable: string[]
      leastReliable: string[]
    }
  }
  recommendations: {
    priority: 'high' | 'medium' | 'low'
    category: 'performance' | 'reliability' | 'maintenance' | 'security' | 'configuration'
    description: string
    impact: string
    effort: 'low' | 'medium' | 'high'
    timeline: string
  }[]
}

// 考勤设备状态订阅接口
export interface AttendanceDeviceStatusSubscription {
  subscriptionId: string
  websocketUrl: string
  heartbeatInterval: number
  expiryTime: string
}

// 考勤设备配置管理接口 (ATT-DEV-003)
export interface AttendanceDeviceConfig {
  deviceId: string
  deviceName: string
  version: string
  lastUpdated: string
  updatedBy: string
  networkConfig: {
    ipAddress: string
    port: number
    protocol: 'tcp' | 'udp' | 'http' | 'https'
    username?: string
    password?: string
    timeout: number
    sslEnabled: boolean
    certificatePath?: string
    proxyEnabled: boolean
    proxyHost?: string
    proxyPort?: number
    keepAliveEnabled: boolean
    maxRetries: number
  }
  syncConfig: {
    syncInterval: number
    syncTimeRange: [string, string]
    autoSync: boolean
    retryCount: number
    retryInterval: number
    conflictStrategy: 'skip' | 'override' | 'merge'
    deduplication: boolean
    dataValidation: boolean
    notifications: string[]
    batchSize: number
    compressionEnabled: boolean
  }
  monitoringConfig: {
    heartbeatInterval: number
    healthCheckInterval: number
    performanceMetricsInterval: number
    alertThresholds: {
      responseTime: { warning: number; critical: number }
      cpuUsage: { warning: number; critical: number }
      memoryUsage: { warning: number; critical: number }
      diskUsage: { warning: number; critical: number }
      temperature: { warning: number; critical: number }
      errorRate: { warning: number; critical: number }
      uptime: { warning: number; critical: number }
    }
    alertSettings: {
      enableEmailAlerts: boolean
      enableSmsAlerts: boolean
      enableWebhookAlerts: boolean
      alertRecipients: string[]
      escalationEnabled: boolean
      escalationTime: number
    }
    retentionPolicy: {
      statusHistoryDays: number
      performanceMetricsDays: number
      alertHistoryDays: number
      logRetentionDays: number
    }
    maintenanceWindow: {
      enabled: boolean
      weeklySchedule: {
        dayOfWeek: number
        startTime: string
        endTime: string
      }[]
      disableAlertsInMaintenance: boolean
    }
  }
  operationalConfig: {
    timeZone: string
    dateFormat: string
    timeFormat: '12h' | '24h'
    workingHours: {
      start: string
      end: string
    }
    holidays: string[]
    attendanceRules: {
      lateThreshold: number
      earlyLeaveThreshold: number
      overtimeThreshold: number
      minWorkingHours: number
      maxWorkingHours: number
      breakDuration: number
      roundingRules: {
        clockInRounding: number
        clockOutRounding: number
        roundingMethod: 'round' | 'floor' | 'ceil'
      }
    }
    biometricSettings: {
      fingerprintEnabled: boolean
      faceRecognitionEnabled: boolean
      cardReaderEnabled: boolean
      multiFactorRequired: boolean
      verificationThreshold: number
    }
  }
  securityConfig: {
    encryptionEnabled: boolean
    encryptionMethod: 'AES128' | 'AES256' | 'RSA'
    authenticationMode: 'none' | 'basic' | 'digest' | 'certificate' | 'oauth'
    accessControlEnabled: boolean
    allowedIPs: string[]
    blockedIPs: string[]
    sessionTimeout: number
    maxFailedAttempts: number
    lockoutDuration: number
    passwordPolicy: {
      minLength: number
      requireUppercase: boolean
      requireLowercase: boolean
      requireNumbers: boolean
      requireSymbols: boolean
      expiryDays: number
    }
    auditLogging: boolean
    dataEncryptionAtRest: boolean
  }
  advancedConfig: {
    logLevel: 'debug' | 'info' | 'warning' | 'error'
    maxConnections: number
    bufferSize: number
    compressionEnabled: boolean
    cacheEnabled: boolean
    cacheSize: number
    performanceOptimization: boolean
    customParameters: Record<string, unknown>
    experimentalFeatures: {
      enabled: boolean
      features: string[]
    }
  }
}

// 考勤设备配置模板接口
export interface AttendanceDeviceConfigTemplate {
  templateId: string
  templateName: string
  templateDescription: string
  templateType: 'default' | 'custom' | 'preset'
  deviceTypes: string[]
  category: string
  tags: string[]
  usageCount: number
  configSections: ('network' | 'sync' | 'monitoring' | 'operational' | 'security' | 'advanced')[]
  isDefault: boolean
  isActive: boolean
  version: string
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  lastUsed: string
  validationStatus: 'valid' | 'invalid' | 'pending'
  compatibility: {
    deviceTypes: string[]
    firmwareVersions: string[]
    supportedFeatures: string[]
  }
  configData: {
    networkConfig?: Record<string, unknown>
    syncConfig?: Record<string, unknown>
    monitoringConfig?: Record<string, unknown>
    operationalConfig?: Record<string, unknown>
    securityConfig?: Record<string, unknown>
    advancedConfig?: Record<string, unknown>
  }
  validationRules?: {
    requiredFields: string[]
    validationSchema: Record<string, unknown>
  }
}

// 考勤设备配置历史记录接口
export interface AttendanceDeviceConfigHistory {
  historyId: string
  deviceId: string
  deviceName: string
  version: string
  changeType: 'create' | 'update' | 'delete' | 'deploy' | 'rollback'
  changeDescription: string
  changedFields: {
    field: string

    oldValue: unknown

    newValue: unknown
    changeType: 'added' | 'modified' | 'removed'
  }[]
  changeReason: string
  userId: string
  userName: string
  timestamp: string
  deploymentId?: string
  deploymentStatus?: 'success' | 'failed' | 'pending'
  rollbackFromVersion?: string
  approvalStatus?: 'pending' | 'approved' | 'rejected'
  approvedBy?: string
  approvedAt?: string
  configDiff: {
    added: Record<string, unknown>

    modified: Record<string, { old: unknown; new: unknown }>
    removed: Record<string, unknown>
  }
}

// 考勤设备配置验证接口
export interface AttendanceDeviceConfigValidation {
  validationId: string
  isValid: boolean
  errors: {
    field: string
    section: string
    message: string
    severity: 'error' | 'warning'
    errorCode: string
    suggestion?: string
  }[]
  warnings: {
    field: string
    section: string
    message: string
    recommendation: string
    warningCode: string
  }[]
  compatibility: {
    deviceType: string
    isCompatible: boolean
    unsupportedFeatures: string[]
    requiredUpdates: string[]
  }
  validationSummary: {
    totalChecks: number
    passedChecks: number
    failedChecks: number
    warningChecks: number
    validationScore: number
  }
  recommendations: {
    priority: 'high' | 'medium' | 'low'
    category: 'security' | 'performance' | 'compliance' | 'best-practice'
    description: string
    action: string
    impact: string
  }[]
}

// 考勤设备配置部署接口
export interface AttendanceDeviceConfigDeployment {
  deploymentId: string
  deviceId: string
  deviceName: string
  deploymentType: string
  status: 'pending' | 'deploying' | 'success' | 'failed' | 'rollback'
  startTime: string
  endTime?: string
  estimatedDuration: number
  progress: number
  currentStep: string
  steps: {
    stepName: string
    stepDescription: string
    status: 'pending' | 'running' | 'success' | 'failed' | 'skipped'
    startTime?: string
    endTime?: string
    duration?: number
    message?: string
    details?: Record<string, unknown>
  }[]
  rollbackPlan?: {
    enabled: boolean
    rollbackVersion: string
    rollbackSteps: string[]
    rollbackConditions: string[]
  }
  validationResults?: {
    isValid: boolean
    errors: {
      field: string
      message: string
      severity: 'error' | 'warning'
    }[]
  }
  errorMessage?: string
  configVersion?: string
}

// 考勤设备配置回滚接口
export interface AttendanceDeviceConfigRollback {
  rollbackId: string
  deviceId: string
  deviceName: string
  rollbackType: string
  status: 'pending' | 'rolling-back' | 'success' | 'failed'
  startTime: string
  endTime?: string
  progress: number
  currentStep: string
  steps: {
    stepName: string
    status: 'pending' | 'running' | 'success' | 'failed'
    message?: string
    timestamp?: string
  }[]
  rollbackFromVersion: string
  rollbackToVersion: string
  backupId?: string
  validationResults?: {
    isValid: boolean
    errors: {
      field: string
      message: string
      severity: 'error' | 'warning'
    }[]
  }
  errorMessage?: string
}

// 考勤设备配置备份接口
export interface AttendanceDeviceConfigBackup {
  backupId: string
  deviceId: string
  deviceName: string
  backupName: string
  backupDescription: string
  backupType: 'full' | 'partial' | 'incremental'
  configVersion: string
  backupSections: string[]
  backupSize: number
  encrypted: boolean
  createdAt: string
  createdBy: string
  expiryDate?: string
  checksum: string
  isCorrupted: boolean
  restoreCount: number
  lastRestored?: string
  metadata: {
    deviceType: string
    firmwareVersion: string
    lastModified: string
    configSections: string[]
  }
}

// 考勤设备配置恢复接口
export interface AttendanceDeviceConfigRestore {
  restoreId: string
  deviceId: string
  deviceName: string
  backupId: string
  backupName: string
  status: 'pending' | 'restoring' | 'success' | 'failed'
  startTime: string
  endTime?: string
  progress: number
  restoreSections: string[]
  backupBeforeRestoreId?: string
  deploymentId?: string
  validationResults?: {
    isValid: boolean
    errors: {
      field: string
      message: string
      severity: 'error' | 'warning'
    }[]
  }
  errorMessage?: string
  configVersion?: string
}

// 考勤设备配置比较接口
export interface AttendanceDeviceConfigComparison {
  comparisonId: string
  deviceConfigs: {
    deviceId: string
    deviceName: string
    configVersion: string
    config: Record<string, unknown>
    lastUpdated: string
  }[]
  differences: {
    field: string
    section: string
    values: {
      deviceId: string
      deviceName: string

      value: unknown
    }[]
    differenceType: 'value' | 'missing' | 'extra'
    severity: 'info' | 'warning' | 'error'
  }[]
  similarities: {
    field: string
    section: string

    commonValue: unknown
    deviceCount: number
    devices: string[]
  }[]
  statistics: {
    totalFields: number
    identicalFields: number
    differentFields: number
    missingFields: number
    similarityPercentage: number
  }
  recommendations: {
    priority: 'high' | 'medium' | 'low'
    action: 'standardize' | 'review' | 'ignore'
    description: string
    affectedDevices: string[]

    suggestedValue?: unknown
  }[]
}

// 考勤设备配置批量操作接口
export interface AttendanceDeviceConfigBatchOperation {
  batchId: string
  totalDevices: number
  successfulOperations: number
  failedOperations: number
  results: {
    deviceId: string
    deviceName: string
    status: 'success' | 'failed' | 'pending'
    deploymentId?: string
    configVersion?: string
    validationResult: {
      isValid: boolean
      errors: {
        field: string
        message: string
        severity: 'error' | 'warning'
      }[]
    }
    message: string
    operationTime?: string
  }[]
  deploymentSummary: {
    totalDeployments: number
    successfulDeployments: number
    failedDeployments: number
    pendingDeployments: number
  }
}

// 考勤设备故障处理接口 (ATT-DEV-004)
// 设备故障记录接口
export interface AttendanceDeviceFault {
  faultId: string
  deviceId: string
  deviceName: string
  deviceType: string
  location: string
  faultType: 'CONNECTION' | 'SYNC' | 'HARDWARE' | 'SOFTWARE' | 'PERFORMANCE' | 'SECURITY'
  faultLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  faultCode: string
  faultMessage: string
  faultDescription: string
  detectionTime: string
  detectionMethod: 'AUTOMATIC' | 'MANUAL' | 'SCHEDULED'
  status: 'OPEN' | 'ASSIGNED' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED'
  assignedTo?: string
  assignedBy?: string
  assignedAt?: string
  priority: number
  estimatedResolutionTime?: string
  actualResolutionTime?: string
  impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  affectedUsers: number
  rootCause?: string
  resolution?: string
  preventiveMeasures?: string
  createdBy: string
  createdAt: string
  updatedAt: string
  resolvedBy?: string
  resolvedAt?: string
  closedBy?: string
  closedAt?: string
  diagnosisResult?: AttendanceDeviceFaultDiagnosis
  repairActions: AttendanceDeviceFaultRepairAction[]
  relatedFaults: string[]
  tags: string[]
  attachments: AttendanceDeviceFaultAttachment[]
  timeline: AttendanceDeviceFaultTimelineEvent[]
}

// 设备故障诊断结果接口
export interface AttendanceDeviceFaultDiagnosis {
  diagnosisId: string
  diagnosisStartTime: string
  diagnosisEndTime?: string
  diagnosisStatus: 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  diagnosisProgress: number
  overallStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'UNKNOWN'
  overallScore: number
  diagnosticTests: AttendanceDeviceFaultDiagnosticTest[]
  identifiedIssues: AttendanceDeviceFaultIdentifiedIssue[]
  recommendations: AttendanceDeviceFaultRecommendation[]
  diagnosticReport?: {
    reportId: string
    reportUrl: string
    reportSize: number
    generatedAt: string
  }
}

// 设备故障诊断测试接口
export interface AttendanceDeviceFaultDiagnosticTest {
  testId: string
  testName: string
  testCategory:
    | 'CONNECTIVITY'
    | 'HARDWARE'
    | 'SOFTWARE'
    | 'PERFORMANCE'
    | 'SECURITY'
    | 'DATA_INTEGRITY'
  testResult: 'PASS' | 'FAIL' | 'WARNING' | 'SKIPPED'
  testScore: number
  testMessage: string
  testStartTime: string
  testEndTime?: string
  testDuration?: number
  testDetails?: {
    connectivity?: {
      pingTest: { result: 'PASS' | 'FAIL'; responseTime: number; packetLoss: number }
      portTest: { result: 'PASS' | 'FAIL'; openPorts: number[]; closedPorts: number[] }
      dnsTest: { result: 'PASS' | 'FAIL'; resolvedIPs: string[] }
      certificateTest?: { result: 'PASS' | 'FAIL'; expiryDate: string; isValid: boolean }
    }
    hardware?: {
      cpuTest: { result: 'PASS' | 'FAIL' | 'WARNING'; usage: number; temperature: number }
      memoryTest: { result: 'PASS' | 'FAIL' | 'WARNING'; usage: number; available: number }
      diskTest: { result: 'PASS' | 'FAIL' | 'WARNING'; usage: number; available: number }
      temperatureTest: { result: 'PASS' | 'FAIL' | 'WARNING'; currentTemp: number; maxTemp: number }
      batteryTest?: { result: 'PASS' | 'FAIL' | 'WARNING'; level: number; health: string }
    }
    software?: {
      firmwareTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        currentVersion: string
        latestVersion: string
      }
      serviceTest: { result: 'PASS' | 'FAIL'; runningServices: string[]; failedServices: string[] }
      configTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        validConfig: boolean
        configErrors: string[]
      }
      licenseTest?: { result: 'PASS' | 'FAIL' | 'WARNING'; isValid: boolean; expiryDate: string }
    }
    performance?: {
      responseTimeTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        avgResponseTime: number
        maxResponseTime: number
      }
      throughputTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        currentThroughput: number
        maxThroughput: number
      }
      errorRateTest: { result: 'PASS' | 'FAIL' | 'WARNING'; errorRate: number; errorCount: number }
      uptimeTest: { result: 'PASS' | 'FAIL' | 'WARNING'; uptime: number; downtime: number }
    }
    security?: {
      authenticationTest: { result: 'PASS' | 'FAIL'; authMethod: string; lastLogin: string }
      encryptionTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        encryptionEnabled: boolean
        encryptionLevel: string
      }
      vulnerabilityTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        vulnerabilities: string[]
        riskLevel: string
      }
      accessControlTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        accessRules: string[]
        violations: string[]
      }
    }
    dataIntegrity?: {
      recordCountTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        expectedCount: number
        actualCount: number
      }
      dataQualityTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        qualityScore: number
        qualityIssues: string[]
      }
      backupTest: { result: 'PASS' | 'FAIL' | 'WARNING'; lastBackup: string; backupSize: number }
      syncTest: { result: 'PASS' | 'FAIL' | 'WARNING'; lastSync: string; syncErrors: string[] }
    }
  }
}

// 设备故障识别问题接口
export interface AttendanceDeviceFaultIdentifiedIssue {
  issueId: string
  issueType:
    | 'CONNECTIVITY'
    | 'HARDWARE'
    | 'SOFTWARE'
    | 'PERFORMANCE'
    | 'SECURITY'
    | 'DATA_INTEGRITY'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  issueDescription: string
  impact: string
  affectedComponents: string[]
  recommendedActions: string[]
  priority: number
  estimatedFixTime: string
  requiresReboot: boolean
  requiresReplacement: boolean
}

// 设备故障修复建议接口
export interface AttendanceDeviceFaultRecommendation {
  recommendationId: string
  category: 'IMMEDIATE' | 'PLANNED' | 'PREVENTIVE' | 'OPTIMIZATION'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  description: string
  detailedSteps: string[]
  estimatedDuration: string
  requiredSkills: string[]
  requiredTools: string[]
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  businessImpact: string
  costEstimate?: number
}

// 设备故障修复操作接口
export interface AttendanceDeviceFaultRepairAction {
  actionId: string
  actionType: 'AUTOMATIC' | 'MANUAL' | 'SCHEDULED'
  actionDescription: string
  actionStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED'
  actionResult?: string
  executedBy?: string
  executedAt?: string
}

// 设备故障附件接口
export interface AttendanceDeviceFaultAttachment {
  attachmentId: string
  fileName: string
  fileType: string
  fileSize: number
  uploadedBy: string
  uploadedAt: string
  downloadUrl: string
}

// 设备故障时间线事件接口
export interface AttendanceDeviceFaultTimelineEvent {
  eventId: string
  eventType: 'CREATED' | 'UPDATED' | 'ASSIGNED' | 'DIAGNOSED' | 'REPAIRED' | 'RESOLVED' | 'CLOSED'
  eventDescription: string
  eventTime: string
  eventBy: string
  eventDetails?: Record<string, unknown>
}

// 设备故障自动修复结果接口
export interface AttendanceDeviceFaultAutoRepairResult {
  faultId: string
  repairId: string
  repairStartTime: string
  repairEndTime?: string
  repairStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED'
  repairProgress: number
  repairType: 'AUTOMATIC' | 'GUIDED' | 'CUSTOM'
  repairScope: 'FAULT_SPECIFIC' | 'COMPREHENSIVE' | 'PREVENTIVE'
  repairActions: AttendanceDeviceFaultAutoRepairAction[]
  overallResult: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED' | 'CANCELLED'
  resolvedIssues: AttendanceDeviceFaultResolvedIssue[]
  remainingIssues: AttendanceDeviceFaultRemainingIssue[]
  rollbackInfo?: AttendanceDeviceFaultRollbackInfo
  nextSteps: AttendanceDeviceFaultNextStep[]
}

// 设备故障自动修复操作接口
export interface AttendanceDeviceFaultAutoRepairAction {
  actionId: string
  actionType:
    | 'RESTART_DEVICE'
    | 'RESET_CONNECTION'
    | 'CLEAR_CACHE'
    | 'UPDATE_FIRMWARE'
    | 'RESTORE_CONFIG'
    | 'SYNC_DATA'
  actionDescription: string
  actionStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'SKIPPED'
  actionStartTime?: string
  actionEndTime?: string
  actionDuration?: number
  actionResult?: string
  actionDetails?: Record<string, unknown>
  retryCount: number
  maxRetries: number
}

// 设备故障已解决问题接口
export interface AttendanceDeviceFaultResolvedIssue {
  issueId: string
  issueDescription: string
  resolutionAction: string
  resolutionStatus: 'RESOLVED' | 'PARTIALLY_RESOLVED' | 'UNRESOLVED'
  resolutionDetails?: string
}

// 设备故障剩余问题接口
export interface AttendanceDeviceFaultRemainingIssue {
  issueId: string
  issueDescription: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  recommendedActions: string[]
  requiresManualIntervention: boolean
}

// 设备故障回滚信息接口
export interface AttendanceDeviceFaultRollbackInfo {
  rollbackAvailable: boolean
  rollbackId: string
  rollbackReason: string
  rollbackSteps: string[]
}

// 设备故障后续步骤接口
export interface AttendanceDeviceFaultNextStep {
  stepType: 'VERIFICATION' | 'MONITORING' | 'MANUAL_REPAIR' | 'REPLACEMENT' | 'PREVENTIVE'
  stepDescription: string
  stepPriority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  estimatedDuration: string
  assignedTo?: string
  dueDate?: string
}

// 设备故障手动修复结果接口
export interface AttendanceDeviceFaultManualRepairResult {
  faultId: string
  repairId: string
  repairType: 'MANUAL'
  repairStatus: 'COMPLETED' | 'PARTIALLY_COMPLETED' | 'FAILED'
  completedActions: AttendanceDeviceFaultManualRepairAction[]
  overallResult: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED'
  nextSteps: string[]
  message: string
}

// 设备故障手动修复操作接口
export interface AttendanceDeviceFaultManualRepairAction {
  actionId: string
  actionType: string
  actionDescription: string
  actionStatus: 'SUCCESS' | 'FAILED' | 'PARTIAL'
  executedBy: string
  executionTime: string
  actionResult: string
  verificationResult?: string
}

// 设备故障修复历史记录接口
export interface AttendanceDeviceFaultRepairHistory {
  repairId: string
  repairType: 'AUTOMATIC' | 'MANUAL' | 'GUIDED' | 'CUSTOM'
  repairStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED'
  repairStartTime: string
  repairEndTime?: string
  repairDuration?: number
  repairScope: 'FAULT_SPECIFIC' | 'COMPREHENSIVE' | 'PREVENTIVE'
  executedBy: string
  repairActions: {
    actionType: string
    actionDescription: string
    actionStatus: 'SUCCESS' | 'FAILED' | 'PARTIAL'
    actionResult?: string
  }[]
  overallResult: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED' | 'CANCELLED'
  resolvedIssues: number
  remainingIssues: number
  rollbackInfo?: {
    rollbackAvailable: boolean
    rollbackId: string
  }
}

// 设备故障批量处理结果接口
export interface AttendanceDeviceFaultBatchProcessResult {
  batchId: string
  action: string
  processedBy: string
  processTime: string
  totalFaults: number
  successfulOperations: number
  failedOperations: number
  results: AttendanceDeviceFaultBatchProcessResultItem[]
  summary: {
    totalProcessed: number
    successful: number
    failed: number
    partial: number
    averageProcessingTime: number
  }
}

// 设备故障批量处理结果项接口
export interface AttendanceDeviceFaultBatchProcessResultItem {
  faultId: string
  faultMessage: string
  operationStatus: 'SUCCESS' | 'FAILED' | 'PARTIAL'
  operationResult: string
  operationDetails?: Record<string, unknown>
}

// 设备故障统计信息接口
export interface AttendanceDeviceFaultStatistics {
  summary: {
    totalFaults: number
    openFaults: number
    resolvedFaults: number
    closedFaults: number
    criticalFaults: number
    highPriorityFaults: number
    averageResolutionTime: number
    averageResponseTime: number
    resolutionRate: number
    faultFrequency: number
    mtbf: number // Mean Time Between Failures
    mttr: number // Mean Time To Repair
  }
  trends: {
    faultTrends: AttendanceDeviceFaultTrend[]
    deviceTrends: AttendanceDeviceFaultDeviceTrend[]
    faultTypeTrends: AttendanceDeviceFaultTypeTrend[]
  }
  distributions: {
    byFaultType: AttendanceDeviceFaultDistribution[]
    byFaultLevel: AttendanceDeviceFaultDistribution[]
    byDevice: AttendanceDeviceFaultDeviceDistribution[]
    byAssignee: AttendanceDeviceFaultAssigneeDistribution[]
  }
  performance: {
    resolutionTimeDistribution: AttendanceDeviceFaultTimeDistribution[]
    responseTimeMetrics: AttendanceDeviceFaultResponseTimeMetrics
    resolutionRateByType: AttendanceDeviceFaultResolutionRateByType[]
    deviceReliability: AttendanceDeviceFaultDeviceReliability[]
  }
  predictions: {
    predictedFaults: AttendanceDeviceFaultPrediction[]
    maintenanceSchedule: AttendanceDeviceFaultMaintenanceSchedule[]
  }
}

// 设备故障趋势接口
export interface AttendanceDeviceFaultTrend {
  date: string
  totalFaults: number
  openFaults: number
  resolvedFaults: number
  newFaults: number
  criticalFaults: number
}

// 设备故障设备趋势接口
export interface AttendanceDeviceFaultDeviceTrend {
  deviceId: string
  deviceName: string
  deviceType: string
  totalFaults: number
  faultRate: number
  avgResolutionTime: number
  reliability: number
}

// 设备故障类型趋势接口
export interface AttendanceDeviceFaultTypeTrend {
  faultType: string
  count: number
  percentage: number
  avgResolutionTime: number
  recurrenceRate: number
}

// 设备故障分布接口
export interface AttendanceDeviceFaultDistribution {
  faultType: string
  count: number
  percentage: number
}

// 设备故障设备分布接口
export interface AttendanceDeviceFaultDeviceDistribution {
  deviceId: string
  deviceName: string
  deviceType: string
  count: number
  percentage: number
}

// 设备故障分配人员分布接口
export interface AttendanceDeviceFaultAssigneeDistribution {
  assignee: string
  assignedCount: number
  resolvedCount: number
  averageResolutionTime: number
  workload: number
}

// 设备故障时间分布接口
export interface AttendanceDeviceFaultTimeDistribution {
  timeRange: string
  count: number
  percentage: number
}

// 设备故障响应时间指标接口
export interface AttendanceDeviceFaultResponseTimeMetrics {
  avgResponseTime: number
  medianResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
}

// 设备故障按类型解决率接口
export interface AttendanceDeviceFaultResolutionRateByType {
  faultType: string
  resolutionRate: number
  avgResolutionTime: number
}

// 设备故障设备可靠性接口
export interface AttendanceDeviceFaultDeviceReliability {
  deviceId: string
  deviceName: string
  uptime: number
  downtime: number
  reliability: number
  mtbf: number
  mttr: number
}

// 设备故障预测接口
export interface AttendanceDeviceFaultPrediction {
  deviceId: string
  deviceName: string
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  predictedFaultType: string
  probability: number
  timeframe: string
  recommendedActions: string[]
}

// 设备故障维护计划接口
export interface AttendanceDeviceFaultMaintenanceSchedule {
  deviceId: string
  deviceName: string
  maintenanceType: 'PREVENTIVE' | 'CORRECTIVE' | 'UPGRADE'
  scheduledDate: string
  estimatedDuration: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

// 设备故障报告接口
export interface AttendanceDeviceFaultReport {
  reportId: string
  reportType: string
  reportTitle: string
  generatedAt: string
  generatedBy: string
  timeRange: {
    startDate: string
    endDate: string
    period: string
  }
  executiveSummary: {
    totalFaults: number
    criticalFaults: number
    resolutionRate: number
    averageResolutionTime: number
    topIssues: string[]
    keyFindings: string[]
    recommendations: string[]
  }
  detailedAnalysis: {
    faultAnalysis: AttendanceDeviceFaultAnalysis
    performanceAnalysis: AttendanceDeviceFaultPerformanceAnalysis
    trendAnalysis: AttendanceDeviceFaultTrendAnalysis
  }
  recommendations: {
    immediate: AttendanceDeviceFaultReportRecommendation[]
    shortTerm: AttendanceDeviceFaultReportRecommendation[]
    longTerm: AttendanceDeviceFaultReportRecommendation[]
  }
  charts?: AttendanceDeviceFaultChart[]
  downloadUrl?: string
  reportSize?: number
  expiryDate?: string
}

// 设备故障分析接口
export interface AttendanceDeviceFaultAnalysis {
  faultBreakdown: {
    faultType: string
    count: number
    percentage: number
    trend: 'INCREASING' | 'DECREASING' | 'STABLE'
  }[]
  severityAnalysis: {
    severity: string
    count: number
    percentage: number
    avgResolutionTime: number
  }[]
  deviceAnalysis: {
    deviceId: string
    deviceName: string
    deviceType: string
    faultCount: number
    faultRate: number
    reliability: number
    recommendations: string[]
  }[]
}

// 设备故障性能分析接口
export interface AttendanceDeviceFaultPerformanceAnalysis {
  resolutionMetrics: {
    avgResolutionTime: number
    medianResolutionTime: number
    slaCompliance: number
    escalationRate: number
  }
  responseMetrics: {
    avgResponseTime: number
    firstResponseTime: number
    acknowledgeRate: number
  }
  teamPerformance: {
    assignee: string
    resolvedCount: number
    avgResolutionTime: number
    customerSatisfaction: number
  }[]
}

// 设备故障趋势分析接口
export interface AttendanceDeviceFaultTrendAnalysis {
  faultTrends: {
    period: string
    totalFaults: number
    criticalFaults: number
    resolutionRate: number
    trendDirection: 'UP' | 'DOWN' | 'STABLE'
  }[]
  seasonalPatterns: {
    month: string
    faultCount: number
    faultTypes: string[]
    patterns: string[]
  }[]
  deviceHealthTrends: {
    deviceId: string
    deviceName: string
    healthScore: number
    healthTrend: 'IMPROVING' | 'DECLINING' | 'STABLE'
    predictedIssues: string[]
  }[]
}

// 设备故障报告建议接口
export interface AttendanceDeviceFaultReportRecommendation {
  priority: 'HIGH' | 'CRITICAL' | 'MEDIUM' | 'LOW'
  action: string
  description: string
  impact: string
  effort: 'LOW' | 'MEDIUM' | 'HIGH'
  timeline: string
}

// 设备故障图表接口
export interface AttendanceDeviceFaultChart {
  chartType: string
  chartTitle: string

  chartData: unknown

  chartConfig: unknown
}

// 设备故障预警配置接口
export interface AttendanceDeviceFaultAlertConfig {
  configId: string
  alertEnabled: boolean
  alertRules: AttendanceDeviceFaultAlertRule[]
  notificationSettings: AttendanceDeviceFaultNotificationSettings
  maintenanceWindows: AttendanceDeviceFaultMaintenanceWindow[]
}

// 设备故障预警规则接口
export interface AttendanceDeviceFaultAlertRule {
  ruleId: string
  ruleName: string
  ruleType:
    | 'FAULT_THRESHOLD'
    | 'FAULT_RATE'
    | 'DEVICE_OFFLINE'
    | 'RESPONSE_TIME'
    | 'PATTERN_DETECTION'
  enabled: boolean
  conditions: AttendanceDeviceFaultAlertCondition[]
  actions: AttendanceDeviceFaultAlertAction[]
  cooldownPeriod: number
  escalationRules: AttendanceDeviceFaultAlertEscalationRule[]
  scheduleConfig: AttendanceDeviceFaultAlertScheduleConfig
}

// 设备故障预警条件接口
export interface AttendanceDeviceFaultAlertCondition {
  metric: string
  operator: 'GT' | 'LT' | 'EQ' | 'NE' | 'GTE' | 'LTE'
  threshold: number
  timeWindow: number
  deviceFilter?: string[]
  faultTypeFilter?: string[]
  severityFilter?: string[]
}

// 设备故障预警动作接口
export interface AttendanceDeviceFaultAlertAction {
  actionType: 'EMAIL' | 'SMS' | 'WEBHOOK' | 'PUSH' | 'TICKET'
  actionConfig: {
    recipients?: string[]
    template?: string
    url?: string
    headers?: Record<string, string>
    priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  }
}

// 设备故障预警升级规则接口
export interface AttendanceDeviceFaultAlertEscalationRule {
  escalationLevel: number
  escalationTime: number
  escalationRecipients: string[]
  escalationActions: string[]
}

// 设备故障预警调度配置接口
export interface AttendanceDeviceFaultAlertScheduleConfig {
  enabled: boolean
  timezone: string
  workingHours: {
    startTime: string
    endTime: string
    weekdays: number[]
  }
  nonWorkingHoursBehavior: 'SUPPRESS' | 'ESCALATE' | 'NORMAL'
}

// 设备故障通知设置接口
export interface AttendanceDeviceFaultNotificationSettings {
  emailEnabled: boolean
  smsEnabled: boolean
  webhookEnabled: boolean
  pushEnabled: boolean
  defaultRecipients: string[]
  templates: AttendanceDeviceFaultNotificationTemplate[]
}

// 设备故障通知模板接口
export interface AttendanceDeviceFaultNotificationTemplate {
  templateId: string
  templateName: string
  templateType: 'EMAIL' | 'SMS' | 'PUSH'
  templateContent: string
  variables: string[]
}

// 设备故障维护窗口接口
export interface AttendanceDeviceFaultMaintenanceWindow {
  windowId: string
  windowName: string
  startTime: string
  endTime: string
  recurrence: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'CUSTOM'
  affectedDevices: string[]
  suppressAlerts: boolean
  notifyBeforeStart: boolean
  notifyAfterEnd: boolean
}

// 设备故障预警测试结果接口
export interface AttendanceDeviceFaultAlertTestResult {
  testId: string
  alertType: string
  testStatus: 'SUCCESS' | 'FAILED' | 'PARTIAL'
  testTime: string
  testResults: {
    recipient: string
    deliveryStatus: 'DELIVERED' | 'FAILED' | 'PENDING'
    deliveryTime?: string
    errorMessage?: string
    responseCode?: number
    responseBody?: string
  }[]
  summary: {
    totalRecipients: number
    successfulDeliveries: number
    failedDeliveries: number
    averageDeliveryTime: number
  }
  recommendations: string[]
}
