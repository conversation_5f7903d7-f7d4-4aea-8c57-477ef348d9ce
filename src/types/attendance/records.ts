/**
 * 考勤记录与同步相关类型定义
 *
 * 本模块包含考勤系统中的所有记录和同步相关的接口定义：
 * - 考勤记录（AttendanceRecord）
 * - 同步任务（AttendanceSyncTask）
 * - 同步配置（AttendanceSyncConfig）
 * - 同步日志（AttendanceSyncLog）
 * - 同步状态（AttendanceSyncStatus）
 * - 数据推送（AttendanceDataPush）
 */

// 考勤记录接口
export interface AttendanceRecord {
  id: string
  recordId: string
  employeeId: string
  employeeNo: string
  employeeName: string
  departmentId: string
  departmentName: string
  recordTime: string
  recordType: string
  verifyMode: string
  workCode: string
  deviceId: string
  deviceName: string
  deviceSN: string
  direction: 'in' | 'out'
  temperature?: number
  photoPath?: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  syncStatus: 'pending' | 'synced' | 'failed'
  syncTime?: string
  errorMessage?: string
  createdAt: string
  updatedAt: string
}

// 考勤同步任务接口
export interface AttendanceSyncTask {
  id: string
  taskId: string
  deviceId: string
  deviceName: string
  syncType: 'full' | 'incremental' | 'realtime' | 'manual' | 'scheduled'
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled'
  priority: 'high' | 'normal' | 'low'
  startTime: string
  endTime?: string
  duration?: number
  progress: number
  totalRecords: number
  processedRecords: number
  successRecords: number
  failedRecords: number
  duplicateRecords: number
  errorMessage?: string
  errorLogs?: {
    recordId: string
    employeeNo: string
    errorType: string
    errorMessage: string
  }[]
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 考勤同步配置接口
export interface AttendanceSyncConfig {
  id: string
  deviceId: string
  syncInterval: number
  syncTimeRange: [string, string]
  autoSync: boolean
  retryCount: number
  retryInterval: number
  conflictStrategy: 'skip' | 'override' | 'merge'
  deduplication: boolean
  dataValidation: boolean
  notifications: string[]
  schedules: {
    scheduleId: string
    scheduleName: string
    scheduleType: 'daily' | 'weekly' | 'monthly'
    scheduleTime: string
    enabled: boolean
    lastExecuted: string
    nextExecution: string
  }[]
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 考勤同步日志接口
export interface AttendanceSyncLog {
  id: string
  logId: string
  deviceId: string
  deviceName: string
  taskId?: string
  logLevel: 'info' | 'warning' | 'error'
  logTime: string
  operation: string
  message: string

  details: unknown
  duration?: number
  recordCount?: number
  createdAt: string
}

// 考勤同步状态接口
export interface AttendanceSyncStatus {
  currentStatus: 'idle' | 'syncing' | 'failed' | 'success'
  lastSyncTime: string
  nextSyncTime: string
  syncProgress: number
  totalRecords: number
  processedRecords: number
  failedRecords: number
  errorMessage?: string
  syncHistory: {
    taskId: string
    syncType: string
    startTime: string
    endTime: string
    status: string
    totalCount: number
    successCount: number
    failedCount: number
    duration: number
  }[]
}

// 考勤数据推送接口
export interface AttendanceDataPush {
  recordType: 'attendance' | 'user' | 'department'
  records: {
    recordId: string
    employeeId: string
    employeeNo: string
    recordTime: string
    recordType: string
    verifyMode: string
    workCode: string
    deviceSN: string
    direction: 'in' | 'out'
    temperature?: number
    photoPath?: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }[]
  deviceInfo: {
    deviceSN: string
    timestamp: string
    firmware: string
    batteryLevel?: number
    networkStatus: string
  }
}
