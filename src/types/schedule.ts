// 报表调度任务相关类型定义

export interface ScheduleTask {
  id: string
  name: string
  description?: string
  reportId: string
  reportName?: string
  type: 'cron' | 'event' | 'manual'
  cronExpression?: string
  timezone?: string
  eventType?: string
  eventCondition?: string
  config: TaskConfig
  status: 'active' | 'paused' | 'disabled'
  enabled: boolean
  running?: boolean
  lastRunTime?: Date
  lastRunStatus?: 'success' | 'failed'
  lastRunMessage?: string
  nextRunTime?: Date
  executionCount?: number
  successCount?: number
  failCount?: number
  createdBy: string
  createdByName?: string
  createdAt: Date
  updatedAt?: Date
}

export interface TaskConfig {
  exportConfig?: ExportConfig
  pushConfig: PushConfig
  retryConfig?: RetryConfig
  timeoutMinutes?: number
  preventConcurrent?: boolean
  holidayPolicy?: 'skip' | 'delay' | 'advance' | 'ignore'
  dependencies?: string[]
}

export interface ExportConfig {
  format: string
  fields?: string[]

  filters?: unknown
  parameters?: Record<string, unknown>
}

export interface PushConfig {
  channels: PushChannel[]
  recipients: Recipient[]
  template?: string
  subject?: string
  content?: string
  attachReport: boolean
}

export interface PushChannel {
  type: 'email' | 'internal' | 'dingtalk' | 'wechat' | 'sms'
  enabled: boolean
  config?: ChannelConfig
}

export interface ChannelConfig {
  // Email配置
  smtpHost?: string
  smtpPort?: number
  smtpUser?: string
  smtpPassword?: string
  smtpSecure?: boolean

  // 钉钉配置
  dingtalkWebhook?: string
  dingtalkSecret?: string

  // 企业微信配置
  wechatCorpId?: string
  wechatAgentId?: string
  wechatSecret?: string

  // 短信配置
  smsProvider?: string
  smsApiKey?: string
  smsTemplate?: string
}

export interface Recipient {
  type: 'user' | 'role' | 'email' | 'dynamic'
  value: string | string[]
  name?: string
}

export interface RetryConfig {
  enabled: boolean
  maxRetries: number
  retryInterval: number // 分钟
  retryStrategy?: 'fixed' | 'exponential'
}

export interface TaskExecution {
  id: string
  taskId: string
  taskName?: string
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled'
  startTime: Date
  endTime?: Date
  duration?: number // 秒
  triggerType: 'scheduled' | 'manual' | 'event' | 'retry'
  triggerBy?: string
  result?: ExecutionResult
  error?: string
  logs?: ExecutionLog[]
}

export interface ExecutionResult {
  reportGenerated: boolean
  reportSize?: number
  reportUrl?: string
  pushResults: PushResult[]
  exportedRows?: number
  warnings?: string[]
}

export interface PushResult {
  channel: string
  success: boolean
  recipients: string[]
  sentCount?: number
  failedCount?: number
  error?: string
}

export interface ExecutionLog {
  timestamp: Date
  level: 'info' | 'warning' | 'error'
  message: string

  details?: unknown
}

export interface TaskStatistics {
  taskId: string
  totalExecutions: number
  successCount: number
  failureCount: number
  averageDuration: number
  lastExecutionTime?: Date
  nextExecutionTime?: Date
  successRate: number
}

export interface ScheduleSearchParams {
  page?: number
  pageSize?: number
  keyword?: string
  status?: string
  type?: string
  reportId?: string
  createdBy?: string
}

export interface ScheduleListResult {
  list: ScheduleTask[]
  total: number
  page: number
  pageSize: number
}

export interface ExecutionSearchParams {
  taskId?: string
  status?: string
  startDate?: Date
  endDate?: Date
  page?: number
  pageSize?: number
}

export interface ExecutionListResult {
  list: TaskExecution[]
  total: number
  page: number
  pageSize: number
}

export interface TaskTemplate {
  id: string
  name: string
  description?: string
  type: ScheduleTask['type']
  config: Partial<TaskConfig>
  cronExpression?: string
  isSystem?: boolean
}

export interface HolidayCalendar {
  year: number
  holidays: Holiday[]
  workdays: string[] // 调休工作日
}

export interface Holiday {
  date: string
  name: string
  type: 'festival' | 'vacation'
}

export interface NotificationTemplate {
  id: string
  name: string
  channel: PushChannel['type']
  subject?: string
  content: string
  variables?: string[]
  isDefault?: boolean
}

export interface ScheduleService {
  // 任务管理
  getTasks(params: ScheduleSearchParams): Promise<ScheduleListResult>
  getTask(taskId: string): Promise<ScheduleTask>
  createTask(task: Partial<ScheduleTask>): Promise<ScheduleTask>
  updateTask(taskId: string, updates: Partial<ScheduleTask>): Promise<void>
  deleteTask(taskId: string): Promise<void>
  toggleTask(taskId: string, enabled: boolean): Promise<void>

  // 任务执行
  executeTask(taskId: string): Promise<TaskExecution>
  cancelExecution(executionId: string): Promise<void>
  getExecutions(params: ExecutionSearchParams): Promise<ExecutionListResult>
  getExecution(executionId: string): Promise<TaskExecution>
  getExecutionLogs(executionId: string): Promise<ExecutionLog[]>

  // 统计分析
  getTaskStatistics(taskId: string): Promise<TaskStatistics>
  getSystemStatistics(): Promise<unknown>

  // 模板管理
  getTaskTemplates(): Promise<TaskTemplate[]>
  getNotificationTemplates(channel?: string): Promise<NotificationTemplate[]>

  // 其他
  testPush(taskId: string): Promise<void>
  validateCron(expression: string): Promise<boolean>
  getNextRunTimes(expression: string, count: number): Promise<Date[]>
}
