/**
 * 扩展 Performance 接口以支持内存信息
 */
interface Performance {
  memory?: {
    jsHeapSizeLimit: number
    totalJSHeapSize: number
    usedJSHeapSize: number
  }
}

/**
 * 网络连接信息接口
 */
interface NetworkInformation {
  downlink?: number
  effectiveType?: '2g' | '3g' | '4g' | 'slow-2g'
  rtt?: number
  saveData?: boolean
  type?: 'bluetooth' | 'cellular' | 'ethernet' | 'none' | 'wifi' | 'wimax' | 'other' | 'unknown'
}

/**
 * 扩展 Navigator 接口以支持网络连接信息
 */
interface Navigator {
  connection?: NetworkInformation
  mozConnection?: NetworkInformation
  webkitConnection?: NetworkInformation
}

/**
 * 扩展 Window 接口
 */
interface Window {
  performance: Performance
}