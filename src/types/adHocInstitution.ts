// 临时机构管理相关类型定义
import type { SpringPageData } from '@/types/common/api'

// 临时机构类型枚举
export enum AdHocInstitutionType {
  PROJECT_TEAM = 'PROJECT_TEAM', // 项目组
  WORKING_GROUP = 'WORKING_GROUP', // 工作组
  COMMITTEE = 'COMMITTEE', // 委员会
  TASK_FORCE = 'TASK_FORCE', // 专项小组
  TEMPORARY_OFFICE = 'TEMPORARY_OFFICE', // 临时办公室
  COORDINATION_GROUP = 'COORDINATION_GROUP' // 协调小组
}

// 临时机构状态枚举
export enum AdHocInstitutionStatus {
  DRAFT = 'DRAFT', // 草稿
  ACTIVE = 'ACTIVE', // 活跃
  EXPIRING_SOON = 'EXPIRING_SOON', // 即将到期
  EXPIRED = 'EXPIRED', // 已到期
  EXTENDED = 'EXTENDED', // 已延期
  DISSOLVED = 'DISSOLVED', // 已撤销
  CONVERTED = 'CONVERTED' // 已转正式
}

// 到期处理方式枚举
export enum ExpirationHandling {
  AUTO_DISSOLVE = 'AUTO_DISSOLVE', // 自动撤销
  EXTEND = 'EXTEND', // 延期
  CONVERT_FORMAL = 'CONVERT_FORMAL', // 转为正式机构
  MANUAL_REVIEW = 'MANUAL_REVIEW' // 人工审核
}

// 优先级枚举
export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// 临时机构接口
export interface AdHocInstitution {
  id: number
  institutionCode: string
  institutionName: string
  institutionType: AdHocInstitutionType
  parentOrganizationId: number
  parentOrganizationName?: string
  description: string
  purpose: string
  establishDate: string
  plannedEndDate: string
  actualEndDate?: string
  status: AdHocInstitutionStatus
  responsiblePersonId: string
  responsiblePersonName: string
  responsiblePersonDepartment: string
  memberCount: number
  memberList?: string
  budget?: number
  budgetDescription?: string
  expirationHandling: ExpirationHandling
  autoNotifyDays: number
  priority: Priority
  isUrgent: boolean
  remark?: string
  attachmentJson?: string
  createTime: string
  createBy?: string
  updateTime?: string
  updateBy?: string
  lastNotifyTime?: string
  extensionCount: number
  maxExtensionCount: number
}

// 创建临时机构请求
export interface AdHocInstitutionCreateRequest {
  institutionName: string
  institutionType: AdHocInstitutionType
  parentOrganizationId: number
  description: string
  purpose: string
  establishDate: string
  plannedEndDate: string
  responsiblePersonId: string
  responsiblePersonName: string
  responsiblePersonDepartment: string
  memberCount: number
  memberList?: string
  budget?: number
  budgetDescription?: string
  expirationHandling: ExpirationHandling
  autoNotifyDays: number
  priority: Priority
  isUrgent: boolean
  remark?: string
  attachmentJson?: string
  maxExtensionCount?: number
}

// 更新临时机构请求
export interface AdHocInstitutionUpdateRequest {
  institutionName?: string
  institutionType?: AdHocInstitutionType
  description?: string
  purpose?: string
  plannedEndDate?: string
  responsiblePersonId?: string
  responsiblePersonName?: string
  responsiblePersonDepartment?: string
  memberCount?: number
  memberList?: string
  budget?: number
  budgetDescription?: string
  expirationHandling?: ExpirationHandling
  autoNotifyDays?: number
  priority?: Priority
  isUrgent?: boolean
  remark?: string
  attachmentJson?: string
  maxExtensionCount?: number
}

// 查询临时机构请求
export interface AdHocInstitutionQueryRequest {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
  institutionCode?: string
  institutionName?: string
  institutionType?: AdHocInstitutionType
  parentOrganizationId?: number
  status?: AdHocInstitutionStatus
  responsiblePersonId?: string
  establishDateStart?: string
  establishDateEnd?: string
  plannedEndDateStart?: string
  plannedEndDateEnd?: string
  keyword?: string
  priority?: Priority
  isUrgent?: boolean
  expirationHandling?: ExpirationHandling
  includeExpired?: boolean
  daysToExpiration?: number
}

// 延期申请
export interface ExtensionRequest {
  newEndDate: string
  extensionReason: string
  extensionDays: number
  approvalRequired: boolean
}

// 到期处理请求
export interface ExpirationHandlingRequest {
  handlingType: ExpirationHandling
  reason: string
  newEndDate?: string
  convertToFormalId?: string
  notifyMembers: boolean
  archiveData: boolean
}

// 批量操作请求
export interface BatchOperationRequest {
  institutionIds: number[]
  operation: 'EXTEND' | 'DISSOLVE' | 'NOTIFY' | 'UPDATE_STATUS'
  parameters?: Record<string, unknown>
}

// 统计概览
export interface AdHocInstitutionStatistics {
  totalCount: number
  activeCount: number
  expiringSoonCount: number
  expiredCount: number
  dissolvedCount: number
  convertedCount: number
  thisMonthCreated: number
  thisMonthExpired: number
  averageDuration: number
  totalBudget: number
}

// 类型分布统计
export interface TypeDistributionStats {
  institutionType: AdHocInstitutionType
  count: number
  percentage: number
  averageDuration: number
}

// 到期趋势分析
export interface ExpirationTrend {
  date: string
  expiringCount: number
  expiredCount: number
  extendedCount: number
  dissolvedCount: number
}

// 成员信息
export interface InstitutionMember {
  userId: string
  userName: string
  department: string
  role: string
  joinDate: string
  leaveDate?: string
  isActive: boolean
}

// 操作历史记录
export interface OperationHistory {
  id: number
  institutionId: number
  operation: string
  operatorId: string
  operatorName: string
  operationTime: string
  description: string
  beforeValue?: string
  afterValue?: string
}

// 分页响应
// 使用通用的SpringPageData类型
export type PageResponse<T> = SpringPageData<T>

// 临时机构类型选项
export const adHocInstitutionTypeOptions = [
  { label: '项目组', value: AdHocInstitutionType.PROJECT_TEAM },
  { label: '工作组', value: AdHocInstitutionType.WORKING_GROUP },
  { label: '委员会', value: AdHocInstitutionType.COMMITTEE },
  { label: '专项小组', value: AdHocInstitutionType.TASK_FORCE },
  { label: '临时办公室', value: AdHocInstitutionType.TEMPORARY_OFFICE },
  { label: '协调小组', value: AdHocInstitutionType.COORDINATION_GROUP }
]

// 状态选项
export const adHocInstitutionStatusOptions = [
  { label: '草稿', value: AdHocInstitutionStatus.DRAFT },
  { label: '活跃', value: AdHocInstitutionStatus.ACTIVE },
  { label: '即将到期', value: AdHocInstitutionStatus.EXPIRING_SOON },
  { label: '已到期', value: AdHocInstitutionStatus.EXPIRED },
  { label: '已延期', value: AdHocInstitutionStatus.EXTENDED },
  { label: '已撤销', value: AdHocInstitutionStatus.DISSOLVED },
  { label: '已转正式', value: AdHocInstitutionStatus.CONVERTED }
]

// 到期处理方式选项
export const expirationHandlingOptions = [
  { label: '自动撤销', value: ExpirationHandling.AUTO_DISSOLVE },
  { label: '延期', value: ExpirationHandling.EXTEND },
  { label: '转为正式机构', value: ExpirationHandling.CONVERT_FORMAL },
  { label: '人工审核', value: ExpirationHandling.MANUAL_REVIEW }
]

// 优先级选项
export const priorityOptions = [
  { label: '低', value: Priority.LOW },
  { label: '中', value: Priority.MEDIUM },
  { label: '高', value: Priority.HIGH },
  { label: '紧急', value: Priority.URGENT }
]

// 状态颜色映射
export const statusColors = {
  [AdHocInstitutionStatus.DRAFT]: 'info',
  [AdHocInstitutionStatus.ACTIVE]: 'success',
  [AdHocInstitutionStatus.EXPIRING_SOON]: 'warning',
  [AdHocInstitutionStatus.EXPIRED]: 'danger',
  [AdHocInstitutionStatus.EXTENDED]: 'primary',
  [AdHocInstitutionStatus.DISSOLVED]: 'info',
  [AdHocInstitutionStatus.CONVERTED]: 'success'
}

// 类型颜色映射
export const typeColors = {
  [AdHocInstitutionType.PROJECT_TEAM]: 'primary',
  [AdHocInstitutionType.WORKING_GROUP]: 'success',
  [AdHocInstitutionType.COMMITTEE]: 'warning',
  [AdHocInstitutionType.TASK_FORCE]: 'danger',
  [AdHocInstitutionType.TEMPORARY_OFFICE]: 'info',
  [AdHocInstitutionType.COORDINATION_GROUP]: 'primary'
}

// 优先级颜色映射
export const priorityColors = {
  [Priority.LOW]: 'info',
  [Priority.MEDIUM]: 'primary',
  [Priority.HIGH]: 'warning',
  [Priority.URGENT]: 'danger'
}

// 到期处理方式颜色映射
export const expirationHandlingColors = {
  [ExpirationHandling.AUTO_DISSOLVE]: 'danger',
  [ExpirationHandling.EXTEND]: 'warning',
  [ExpirationHandling.CONVERT_FORMAL]: 'success',
  [ExpirationHandling.MANUAL_REVIEW]: 'primary'
}
