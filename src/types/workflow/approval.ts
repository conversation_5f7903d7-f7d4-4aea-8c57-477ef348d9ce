// 审批统计数据
export interface ApprovalStatistics {
  todo: number
  todoTrend: number
  urgent: number
  overdue: number
  weekDone: number
  weekDoneTrend: number
  avgProcessTime: number
  onTimeRate: number
}

// 快捷入口数据
export interface QuickAccessData {
  [key: string]: number
}

// 快捷入口项
export interface QuickAccessItem {
  id: string
  label: string
  icon: string
  color: string
  path?: string
  count?: number
}

// 待办任务
export interface TodoTask {
  id: string
  taskName: string
  processNo: string
  processType: string
  processInstanceId: string
  priority: 'urgent' | 'high' | 'normal' | 'low'
  applicantId: string
  applicantName: string
  departmentId: string
  departmentName: string
  createTime: string
  dueTime: string
  currentNode: string
  isNew: boolean
  isOverdue: boolean
}

// 已办任务
export interface DoneTask {
  id: string
  taskName: string
  processNo: string
  processType: string
  processInstanceId: string
  applicantId: string
  applicantName: string
  departmentId: string
  departmentName: string
  result: 'approved' | 'rejected' | 'transferred' | 'withdrawn'
  completeTime: string
  duration: number // 分钟
  comment: string
  isOvertime: boolean
}

// 已办统计
export interface DoneStatistics {
  weekCount: number
  monthCount: number
  avgDuration: string
  onTimeRate: number
}

// 流程定义
export interface ProcessDefinition {
  id: string
  key: string
  name: string
  description?: string
  category: string
  icon: string
  color: string
  enabled: boolean
  useCount: number
  rating: number
  isNew: boolean
  estimatedTime?: string
  approvers?: string
}

// 流程实例
export interface ProcessInstance {
  id: string
  processNo: string
  processName: string
  processType: string
  processDefinitionId: string
  status: 'running' | 'completed' | 'terminated' | 'suspended'
  initiatorId: string
  initiatorName: string
  departmentId: string
  departmentName: string
  createTime: string
  completeTime?: string
  currentNode?: string
  currentHandler?: string
  duration?: number // 小时
  processXml?: string
}

// 流程历史
export interface ProcessHistory {
  id: string
  nodeId: string
  nodeName: string
  handlerId?: string
  handlerName?: string
  status: 'completed' | 'processing' | 'waiting'
  result?: 'approved' | 'rejected' | 'transferred' | 'withdrawn'
  time: string
  duration?: number // 分钟
  comment?: string
  attachments?: Array<{
    id: string
    name: string
    url: string
  }>
}

// 监控流程
export interface MonitorProcess {
  id: string
  processNo: string
  processName: string
  currentNode: string
  currentHandler: string
  waitTime: number // 分钟
  progress: number // 百分比
  completedNodes: number
  totalNodes: number
  isOvertime: boolean
  nodes: Array<{
    name: string
    handler: string
    status: string
    time: string
    duration?: number
  }>
}

// 监控指标
export interface MonitorMetrics {
  todayNew: number
  todayNewTrend: number
  avgDuration: string
  avgDurationTrend: number
  overtime: number
  completionRate: number
}

// 任务详情
export interface TaskDetail {
  id: string
  taskName: string
  processNo: string
  processType: string
  processInstanceId: string
  priority: 'urgent' | 'high' | 'normal' | 'low'
  applicantId: string
  applicantName: string
  departmentId: string
  departmentName: string
  createTime: string
  receiveTime: string
  dueTime: string
  currentNode: string
  currentNodeId: string
  isOverdue: boolean
  waitingTime: number // 分钟
}

// 审批表单
export interface ApprovalForm {
  result: 'approve' | 'reject' | 'return' | 'transfer'
  comment: string
  returnNode?: string
  transferTo?: string

  attachments: unknown[]
}
