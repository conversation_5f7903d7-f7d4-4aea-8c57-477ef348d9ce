/**
 * 调岗流程相关类型定义
 */

// 调岗类型
export type TransferType =
  | 'department' // 跨部门调动
  | 'position' // 岗位级别变更
  | 'special' // 特殊岗位调动
  | 'campus' // 跨校区调动

// 岗位级别
export type PositionLevel =
  | '七级岗'
  | '八级岗'
  | '九级岗'
  | '专任教师'
  | '实训教师'
  | '管理部门专技岗'

// 风险等级
export type RiskLevel = '高' | '中' | '低' | '无'

// 交接状态
export type HandoverStatus = 'pending' | 'in_progress' | 'completed'

// 交接项状态
export type HandoverItemStatus = 'pending' | 'completed'

// 调岗申请基础信息
export interface TransferBasicInfo {
  // 申请人信息
  employeeId: string
  employeeName: string
  employeeNo: string
  birthDate: string
  politicalStatus: string
  education: string
  degree: string
  technicalTitle: string
  appointmentTime: string

  // 当前岗位信息
  currentDepartmentId: string
  currentDepartment: string
  currentDeptType: string
  currentPositionId: string
  currentPosition: string
  currentPositionLevel: PositionLevel
  isOrganizer: boolean
  isCounselor: boolean
}

// 目标岗位信息
export interface TargetPositionInfo {
  transferType: TransferType
  targetDepartmentId: string
  targetDepartment: string
  targetPositionId: string
  targetPosition: string
  targetPositionLevel?: PositionLevel
  applyOrganizer?: boolean
  applyCounselor?: boolean
}

// 调岗申请表单
export interface TransferApplicationForm extends TransferBasicInfo, TargetPositionInfo {
  reason: string
  attachments: Attachment[]
}

// 附件信息
export interface Attachment {
  id: string
  name: string
  url: string
  type: string
  size?: number
  uploadTime?: string
}

// 部门影响分析
export interface DepartmentImpact {
  workloadRedistribution: string // 工作量重新分配
  keyRoleImpact: string // 关键角色影响
  staffShortage: string // 人员短缺情况
  knowledgeLoss: string // 知识流失风险
}

// 岗位匹配度分析
export interface PositionMatchAnalysis {
  score: number // 匹配度分数 (0-100)
  details: {
    skillMatch: MatchDetail // 技能匹配
    experienceMatch: MatchDetail // 经验匹配
    qualificationMatch: MatchDetail // 资质匹配
    potentialMatch: MatchDetail // 潜力评估
  }
}

// 匹配详情
export interface MatchDetail {
  name: string
  status: '完全匹配' | '部分匹配' | '不匹配'
  score: number
  description?: string
}

// 影响分析结果
export interface TransferImpactAnalysis {
  riskLevel: RiskLevel
  departmentImpact: string[]
  positionMatch: {
    score: number
    details: Array<{
      name: string
      status: string
    }>
  }
  recommendations?: string[]
}

// 薪酬调整信息
export interface SalaryAdjustment {
  currentSalary: number
  newSalary: number
  change: number
  changeRate: number

  // 津贴调整
  allowances?: {
    current: AllowanceItem[]
    new: AllowanceItem[]
    changes: string[]
  }

  effectiveDate: string
  remarks?: string
}

// 津贴项
export interface AllowanceItem {
  type: string
  name: string
  amount: number
}

// 工作交接清单
export interface WorkHandoverChecklist {
  id: string
  transferId: string
  employeeId: string

  categories: HandoverCategory[]

  overallStatus: HandoverStatus
  createTime: string
  updateTime: string
  confirmedBy?: string
  confirmedTime?: string
}

// 交接类别
export interface HandoverCategory {
  id: string
  name: string
  items: HandoverItem[]
  status: HandoverStatus
}

// 交接项
export interface HandoverItem {
  id: string
  name: string
  description: string
  type: 'document' | 'task' | 'project' | 'asset' | 'permission' | 'knowledge'
  status: HandoverItemStatus

  // 交接详情
  details?: {
    quantity?: number
    location?: string
    recipient?: string
    deadline?: string
  }

  // 完成信息
  completedBy?: string
  completedTime?: string
  completionNote?: string
  attachments?: Attachment[]
}

// 审批记录
export interface TransferApprovalRecord {
  id: string
  nodeType: 'original_dept' | 'target_dept' | 'organization' | 'student_affairs' | 'hr'
  nodeName: string
  approver: string
  approverName: string
  approvalTime: string
  result: 'approved' | 'rejected' | 'returned'
  comment: string

  // 附加信息
  requirements?: string[] // 目标部门要求
  handoverRequired?: boolean // 是否要求交接
  effectiveDate?: string // 生效日期（HR审批）
}

// 调岗申请完整信息
export interface TransferApplication extends TransferApplicationForm {
  id: string
  applicationNo: string

  // 分析结果
  impactAnalysis?: TransferImpactAnalysis
  salaryAdjustment?: SalaryAdjustment

  // 流程信息
  processInstanceId: string
  status: TransferStatus
  currentNode?: string
  currentApprover?: string

  // 审批记录
  approvalRecords: TransferApprovalRecord[]

  // 交接信息
  handoverChecklist?: WorkHandoverChecklist

  // 时间信息
  createTime: string
  updateTime: string
  completeTime?: string
}

// 调岗申请状态
export enum TransferStatus {
  DRAFT = 'DRAFT', // 草稿
  PENDING = 'PENDING', // 待审批
  ORIGINAL_DEPT_REVIEWING = 'ORIGINAL_DEPT_REVIEWING', // 原部门审批中
  TARGET_DEPT_REVIEWING = 'TARGET_DEPT_REVIEWING', // 目标部门审批中
  ORGANIZATION_REVIEWING = 'ORGANIZATION_REVIEWING', // 组织部审批中
  STUDENT_AFFAIRS_REVIEWING = 'STUDENT_AFFAIRS_REVIEWING', // 学工部审批中
  HR_REVIEWING = 'HR_REVIEWING', // 人事处审批中
  HANDOVER_PENDING = 'HANDOVER_PENDING', // 待交接
  HANDOVER_IN_PROGRESS = 'HANDOVER_IN_PROGRESS', // 交接中
  COMPLETED = 'COMPLETED', // 已完成
  REJECTED = 'REJECTED', // 已拒绝
  CANCELLED = 'CANCELLED' // 已取消
}

// 调岗统计数据
export interface TransferStatistics {
  // 总体统计
  summary: {
    totalApplications: number
    approvedCount: number
    rejectedCount: number
    inProgressCount: number
    avgProcessDays: number
  }

  // 按类型统计
  byType: Array<{
    type: TransferType
    typeName: string
    count: number
    percentage: number
  }>

  // 按部门统计
  byDepartment: Array<{
    departmentId: string
    departmentName: string
    outCount: number // 调出人数
    inCount: number // 调入人数
    netChange: number // 净变化
  }>

  // 趋势数据
  trend: Array<{
    month: string
    count: number
    approvedCount: number
  }>
}

// 调岗查询参数
export interface TransferSearchParams {
  employeeName?: string
  employeeId?: string
  currentDepartmentId?: string
  targetDepartmentId?: string
  transferType?: TransferType
  status?: TransferStatus
  dateRange?: [string, string]
  pageNum?: number
  pageSize?: number
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 调岗文件
export interface TransferDocument {
  id: string
  transferId: string
  documentType: 'notice' | 'approval' | 'handover'
  documentNo: string
  title: string
  content: string
  templateId?: string

  // 签章信息
  signatures?: Array<{
    role: string
    name: string
    signTime: string
    sealImage?: string
  }>

  // 文件信息
  fileUrl: string
  fileSize: number
  createTime: string
  createBy: string
}
