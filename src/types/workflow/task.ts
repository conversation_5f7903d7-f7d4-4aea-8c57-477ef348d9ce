// 任务详情
export interface TaskDetail {
  id: string
  taskName: string
  processNo: string
  processType: string
  processTypeName: string
  processInstanceId: string
  nodeName: string
  nodeId: string
  priority: 'urgent' | 'high' | 'normal' | 'low'
  status: 'pending' | 'completed' | 'rejected' | 'transferred'
  applicantId: string
  applicantName: string
  departmentId: string
  departmentName: string
  createTime: string
  dueTime: string
  completeTime?: string
  duration?: string
  isOverdue: boolean
  isNew: boolean
  formKey: string

  formData: unknown
}

// 审批历史
export interface ApprovalHistory {
  id: string
  nodeId: string
  nodeName: string
  handlerId: string
  handlerName: string
  time: string
  result?: 'approved' | 'rejected' | 'transferred' | 'withdrawn'
  comment?: string
  duration?: number
  attachments?: Array<{
    id: string
    name: string
    url: string
  }>
}

// 审批请求
export interface ApproveRequest {
  comment: string
  nextHandlerId?: string
  attachments?: string[]
}

// 拒绝请求
export interface RejectRequest {
  comment: string
  attachments?: string[]
}

// 转办请求
export interface TransferRequest {
  userId: string
  reason: string
}
