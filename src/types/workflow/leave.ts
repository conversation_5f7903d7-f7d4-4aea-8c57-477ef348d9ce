// 请假记录
export interface LeaveRecord {
  id: string
  processInstanceId: string
  employeeId: string
  employeeName: string
  departmentId: string
  departmentName: string
  positionName: string
  leaveType: string
  startDate: string
  endDate: string
  leaveDays: number
  reason: string
  status: string
  statusName: string
  currentTaskName: string
  currentAssignee: string
  createBy: string
  createTime: string
  updateTime: string
}

// 请假搜索参数
export interface LeaveSearchParams {
  employeeName?: string
  employeeId?: string
  departmentId?: string
  leaveType?: string
  status?: string
  dateRange?: string[]
  leaveDateRange?: string[]
  startDate?: string
  endDate?: string
  leaveStartDate?: string
  leaveEndDate?: string
  pageNum?: number
  pageSize?: number
  sortField?: string
  sortOrder?: string
}

// 请假详情信息
export interface LeaveInfo {
  id: string
  processInstanceId: string
  processDefinitionId: string
  employeeId: string
  employeeName: string
  employeeNumber: string
  departmentId: string
  departmentName: string
  positionId: string
  positionName: string
  leaveType: string
  startDate: string
  endDate: string
  leaveDays: number
  reason: string
  workHandover: string
  emergencyContact: string
  attachments: LeaveAttachment[]
  remark?: string
  status: string
  createTime: string
  updateTime: string
}

// 请假附件
export interface LeaveAttachment {
  id: string
  name: string
  url: string
  size: number
  type: string
  uploadTime: string
}

// 假期余额
export interface LeaveBalance {
  leaveType: string
  totalDays: number
  usedDays: number
  remainingDays: number
  pendingDays: number
  expiryDate?: string
  remark?: string
}

// 请假统计
export interface LeaveStatistics {
  total: number
  pending: number
  approved: number
  rejected: number
  cancelled: number
}

// 流程历史
export interface ProcessHistory {
  id: string
  processInstanceId: string
  taskId: string
  taskName: string
  taskDefinitionKey: string
  assignee: string
  assigneeName: string
  startTime: string
  endTime?: string
  duration?: number
  result?: string
  comment?: string
  attachments?: string[]
}

// 请假统计查询参数
export interface LeaveStatisticsQuery {
  viewType: 'personal' | 'department' | 'school'
  startDate: string
  endDate: string
  userId?: string
  deptId?: string
  compareType?: string
  trendType?: string
}

// 请假统计数据
export interface LeaveStatisticsData {
  summary: {
    totalDays: number
    totalCount: number
    avgDays: number
    leaveRate: number
    totalTrend?: number
    countTrend?: number
  }
  personal?: {
    trend: unknown[]

    detail: unknown[]
  }
  department?: {
    distribution: unknown[]

    ranking: unknown[]

    compare: unknown
  }
  school?: {
    summary: unknown

    deptAnalysis: unknown[]

    typeAnalysis: unknown

    trendForecast: unknown

    anomaly: unknown[]
  }
}

// 导出参数
export interface ExportParams {
  reportType: string
  format: string
  dataRange: string[]
  includeCharts?: boolean
  dataMasking?: boolean
  fileName?: string
  remark?: string
  viewType: string
  dateRange: [string, string]
  userId?: string
  deptId?: string
}
