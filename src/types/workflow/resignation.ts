/**
 * 离职流程相关类型定义
 */

/**
 * 离职类型
 */
export type ResignationType = 
  | 'voluntary'      // 主动离职
  | 'retirement'     // 退休
  | 'dismissal'      // 解聘
  | 'transfer'       // 调离
  | 'other'          // 其他

/**
 * 离职原因类别
 */
export interface ResignationReason {
  id: string
  name: string
  description?: string
  isActive: boolean
}

/**
 * 离职申请表单
 */
export interface ResignationApplication {
  id?: string
  applicationNo?: string              // 申请单号
  
  // 员工基本信息（自动填充）
  employeeId: string
  employeeName: string
  employeeNo: string
  department: string
  departmentId: string
  position: string
  positionId: string
  hireDate: string                    // 入职日期
  workYears?: number                  // 工作年限
  
  // 离职信息
  resignationType: ResignationType    // 离职类型
  lastWorkDate: string               // 最后工作日
  reasonCategories: string[]         // 离职原因类别（多选）
  detailedReason: string            // 详细离职原因
  nextEmployer?: string             // 离职去向
  
  // 附件
  attachments?: {
    id: string
    name: string
    url: string
    size: number
    uploadTime: string
  }[]
  
  // 流程信息
  status?: ResignationStatus
  submitTime?: string
  currentNode?: string
  processInstanceId?: string
}

/**
 * 离职申请状态
 */
export type ResignationStatus = 
  | 'draft'                   // 草稿
  | 'submitted'               // 已提交
  | 'talent_evaluation'       // 人才评估中
  | 'retention_communication' // 挽留沟通中
  | 'dept_approving'         // 部门审批中
  | 'hr_approving'           // 人事审批中
  | 'interview_scheduled'    // 已安排面谈
  | 'handover_in_progress'   // 交接进行中
  | 'asset_recovery'         // 资产回收中
  | 'permission_cleanup'     // 权限清理中
  | 'completed'              // 已完成
  | 'cancelled'              // 已取消
  | 'retained'               // 挽留成功

/**
 * 人才挽留评估结果
 */
export interface TalentEvaluation {
  employeeId: string
  evaluationScore: number           // 评估分数（0-100）
  isKeyTalent: boolean             // 是否关键人才
  evaluationFactors: {
    positionLevel: number           // 职位级别得分
    performance: number             // 绩效表现得分
    skillRarity: number            // 技能稀缺性得分
    projectImportance: number      // 项目重要性得分
    teamImpact: number             // 团队影响得分
  }
  recommendRetention: boolean       // 是否建议挽留
  retentionStrategies?: string[]   // 建议的挽留策略
  evaluationTime: string
  evaluatedBy: string
}

/**
 * 挽留沟通记录
 */
export interface RetentionRecord {
  id: string
  applicationId: string
  employeeId: string
  communicationDate: string
  communicator: string              // 沟通人
  communicationMethod: 'face_to_face' | 'phone' | 'video' | 'email'
  
  // 沟通内容
  mainIssues: string[]             // 主要问题
  proposedSolutions: {
    type: 'salary' | 'position' | 'work_content' | 'flexibility' | 'development' | 'other'
    description: string
    accepted: boolean
  }[]
  
  // 沟通结果
  employeeFeedback: string
  finalDecision: 'retained' | 'resigned'  // 挽留成功/坚持离职
  notes?: string
}

/**
 * 离职审批任务
 */
export interface ResignationApprovalTask {
  id: string
  taskId: string
  applicationId: string
  applicationNo: string
  
  // 申请人信息
  employeeInfo: {
    id: string
    name: string
    employeeNo: string
    department: string
    position: string
    hireDate: string
    workYears: number
  }
  
  // 离职信息
  resignationType: ResignationType
  expectedLastDate: string
  resignationReason: string
  
  // 人才评估（如果有）
  talentEvaluation?: TalentEvaluation
  
  // 挽留记录（如果有）
  retentionRecords?: RetentionRecord[]
  
  // 交接进度
  handoverProgress?: {
    work: number
    asset: number
    workDetails?: {
      completed: number
      total: number
    }
    assetDetails?: {
      returned: number
      total: number
    }
  }
  
  // 任务信息
  taskType: 'dept_approval' | 'hr_approval'
  assignee: string
  assignTime: string
  dueDate?: string
  status: 'pending' | 'completed' | 'rejected'
  completedTime?: string
  decisionResult?: 'approved' | 'rejected' | 'retained'
}

/**
 * 离职常见原因
 */
export const RESIGNATION_REASONS = [
  { id: 'career_development', name: '职业发展', description: '寻求更好的职业发展机会'
  },
  { id: 'salary_benefits', name: '薪资福利', description: '对当前薪资福利不满意'
  },
  { id: 'work_life_balance', name: '工作生活平衡', description: '工作压力大，影响生活'
  },
  { id: 'management_style', name: '管理风格', description: '与管理层理念不合'
  },
  { id: 'team_atmosphere', name: '团队氛围', description: '团队氛围不佳'
  },
  { id: 'personal_reasons', name: '个人原因', description: '家庭、健康等个人原因'
  },
  { id: 'location_change', name: '地点变动', description: '工作地点不合适或需要搬迁'
  },
  { id: 'career_change', name: '转行', description: '转换职业方向'
  },
  { id: 'further_education', name: '深造', description: '继续学习深造'
  },
  { id: 'retirement', name: '退休', description: '到达退休年龄'
  },
  { id: 'other', name: '其他', description: '其他原因' }
]

/**
 * 挽留策略选项
 */
export const RETENTION_STRATEGIES = [
  { value: 'salary_increase', label: '薪资调整', description: '提高基本工资或绩效奖金'
  },
  { value: 'position_promotion', label: '职位晋升', description: '提供更高的职位'
  },
  { value: 'work_adjustment', label: '工作调整', description: '调整工作内容或工作量'
  },
  { value: 'flexible_work', label: '弹性工作', description: '提供弹性工作时间或远程办公'
  },
  { value: 'training_opportunity', label: '培训机会', description: '提供培训和发展机会'
  },
  { value: 'project_opportunity', label: '项目机会', description: '参与重要项目'
  },
  { value: 'team_change', label: '团队调整', description: '调整到其他团队'
  },
  { value: 'benefit_improvement', label: '福利改善', description: '改善福利待遇' }
]

/**
 * 工作交接项类别
 */
export type HandoverCategory = 
  | 'work_tasks'          // 工作任务
  | 'project_materials'   // 项目资料
  | 'documents'          // 文档权限
  | 'knowledge'          // 知识资产
  | 'customer_relations' // 客户关系
  | 'accounts'           // 账号密码
  | 'other'              // 其他

/**
 * 工作交接项
 */
export interface HandoverItem {
  id: string
  category: HandoverCategory
  name: string                      // 交接项名称
  description?: string              // 详细描述
  receiver?: string                 // 接收人
  receiverId?: string              // 接收人ID
  status: 'pending' | 'in_progress' | 'completed'
  priority: 'high' | 'medium' | 'low'
  deadline?: string                 // 交接期限
  attachments?: {
    id: string
    name: string
    url: string
    size: number
    uploadTime: string
  }[]
  completedTime?: string
  notes?: string
}

/**
 * 工作交接清单
 */
export interface WorkHandover {
  id: string
  applicationId: string
  applicationNo: string
  employeeId: string
  employeeName: string
  department: string
  position: string
  
  // 交接信息
  handoverItems: HandoverItem[]     // 交接项列表
  totalItems: number                 // 总项数
  completedItems: number             // 已完成项数
  progress: number                   // 完成进度
  
  // 交接状态
  status: 'pending' | 'in_progress' | 'completed' | 'confirmed'
  startTime?: string                 // 开始时间
  completedTime?: string             // 完成时间
  confirmedBy?: string               // 确认人
  confirmedTime?: string             // 确认时间
  
  // 备注
  remarks?: string
}

/**
 * 工作交接任务
 */
export interface HandoverTask {
  id: string
  taskId: string
  applicationId: string
  applicationNo: string
  
  // 员工信息
  employeeInfo: {
    id: string
    name: string
    no: string
    department: string
    position: string
  }
  
  // 离职信息
  resignationInfo: {
    type: ResignationType
    lastWorkDate: string
  }
  
  // 交接要求（来自部门审批）
  handoverRequirements: string[]
  
  // 交接进度
  handoverProgress: {
    totalItems: number
    completedItems: number
    percentage: number
  }
  
  // 任务信息
  assignee: string
  assignTime: string
  dueDate: string
  status: 'pending' | 'in_progress' | 'completed'
}

/**
 * 预设的交接项模板
 */
export const HANDOVER_TEMPLATES = {
  work_tasks: [
    { name: '日常工作任务清单', priority: 'high'
  },
    { name: '进行中的任务', priority: 'high'
  },
    { name: '待办事项列表', priority: 'medium'
  },
    { name: '定期报告安排', priority: 'medium' }
  ],
  project_materials: [
    { name: '项目文档归档', priority: 'high'
  },
    { name: '项目代码仓库', priority: 'high'
  },
    { name: '项目联系人清单', priority: 'medium'
  },
    { name: '项目进度报告', priority: 'medium' }
  ],
  documents: [
    { name: '工作文档整理', priority: 'high'
  },
    { name: '共享文件夹权限', priority: 'high'
  },
    { name: '个人文档备份', priority: 'low' }
  ],
  knowledge: [
    { name: '核心业务知识', priority: 'high'
  },
    { name: '操作手册/指南', priority: 'high'
  },
    { name: '经验总结文档', priority: 'medium'
  },
    { name: '常见问题解答', priority: 'medium' }
  ],
  customer_relations: [
    { name: '客户联系方式', priority: 'high'
  },
    { name: '客户需求文档', priority: 'high'
  },
    { name: '合作历史记录', priority: 'medium'
  },
    { name: '沟通记录整理', priority: 'medium' }
  ],
  accounts: [
    { name: '系统账号清单', priority: 'high'
  },
    { name: '密码管理文档', priority: 'high'
  },
    { name: '第三方平台账号', priority: 'medium'
  },
    { name: 'API密钥管理', priority: 'high' }
  ]
}

/**
 * 资产类型
 */
export type AssetType = 
  | 'computer'          // 电脑设备
  | 'mobile'            // 移动设备
  | 'office_equipment'  // 办公设备
  | 'access_card'       // 门禁卡
  | 'keys'              // 钥匙
  | 'uniform'           // 工作服
  | 'badge'             // 工牌
  | 'books'             // 图书资料
  | 'other'             // 其他资产

/**
 * 资产状态
 */
export type AssetStatus = 
  | 'normal'            // 正常
  | 'damaged'           // 损坏
  | 'lost'              // 丢失
  | 'returned'          // 已归还

/**
 * 资产项
 */
export interface AssetItem {
  id: string
  assetType: AssetType              // 资产类型
  assetName: string                  // 资产名称
  assetNumber?: string               // 资产编号
  department: string                 // 责任部门
  departmentId: string               // 责任部门ID
  issueDate?: string                 // 发放日期
  description?: string               // 资产描述
  status: AssetStatus                // 资产状态
  returnStatus: 'pending' | 'returned' | 'confirmed'  // 归还状态
  returnDate?: string                // 归还日期
  confirmedBy?: string               // 确认人
  confirmedTime?: string             // 确认时间
  damageDescription?: string         // 损坏说明
  compensationAmount?: number        // 赔偿金额
  notes?: string                     // 备注
  attachments?: {                    // 附件（如损坏照片）
    id: string
    name: string
    url: string
    uploadTime: string
  }[]
}

/**
 * 资产交接清单
 */
export interface AssetHandover {
  id: string
  applicationId: string              // 离职申请ID
  applicationNo: string              // 申请单号
  employeeId: string
  employeeName: string
  employeeNo: string
  department: string
  position: string
  
  // 资产信息
  assetItems: AssetItem[]            // 资产项列表
  totalAssets: number                // 总资产数
  returnedAssets: number             // 已归还数
  confirmedAssets: number            // 已确认数
  progress: number                   // 完成进度
  
  // 交接状态
  status: 'pending' | 'in_progress' | 'completed' | 'confirmed'
  startTime?: string                 // 开始时间
  completedTime?: string             // 完成时间
  
  // 汇总信息
  totalCompensation?: number         // 总赔偿金额
  hasIssues: boolean                 // 是否有问题（损坏/丢失）
  remarks?: string                   // 备注
}

/**
 * 资产交接任务
 */
export interface AssetHandoverTask {
  id: string
  taskId: string
  applicationId: string
  applicationNo: string
  
  // 员工信息
  employeeInfo: {
    id: string
    name: string
    no: string
    department: string
    position: string
    lastWorkDate: string
  }
  
  // 资产概况
  assetSummary: {
    totalAssets: number              // 总资产数
    returnedAssets: number           // 已归还数
    pendingAssets: number            // 待归还数
    issueAssets: number              // 问题资产数（损坏/丢失）
  }
  
  // 部门统计
  departmentStats: {
    departmentId: string
    departmentName: string
    assetCount: number
    returnedCount: number
  }[]
  
  // 任务信息
  assignee: string
  assignTime: string
  dueDate: string
  status: 'pending' | 'in_progress' | 'completed'
}

/**
 * 资产类型配置
 */
export const ASSET_TYPE_CONFIG = {
  computer: { label: '电脑设备', icon: 'Monitor', department: 'IT部'
  },
  mobile: { label: '移动设备', icon: 'Cellphone', department: 'IT部'
  },
  office_equipment: { label: '办公设备', icon: 'Printer', department: '行政部'
  },
  access_card: { label: '门禁卡', icon: 'CreditCard', department: '行政部'
  },
  keys: { label: '钥匙', icon: 'Key', department: '行政部'
  },
  uniform: { label: '工作服', icon: 'ShoppingBag', department: '行政部'
  },
  badge: { label: '工牌', icon: 'UserFilled', department: '行政部'
  },
  books: { label: '图书资料', icon: 'Reading', department: '图书馆'
  },
  other: { label: '其他资产', icon: 'Box', department: '行政部' }
}

/**
 * 系统账号类型
 */
export type SystemType = 
  | 'office'            // 办公系统
  | 'business'          // 业务系统
  | 'development'       // 开发系统
  | 'other'             // 其他系统

/**
 * 账号状态
 */
export type AccountStatus = 
  | 'active'            // 活跃
  | 'pending'           // 待处理
  | 'disabled'          // 已注销
  | 'error'             // 注销失败

/**
 * 系统账号
 */
export interface SystemAccount {
  id: string
  systemType: SystemType             // 系统类型
  systemName: string                 // 系统名称
  accountId: string                  // 账号ID
  accountName: string                // 账号名称
  status: AccountStatus              // 账号状态
  lastLoginTime?: string             // 最后登录时间
  disabledTime?: string              // 注销时间
  scheduledDisableTime?: string      // 计划注销时间
  
  // 权限信息
  privilege?: string                 // 特殊权限
  privilegeType?: string             // 权限类型
  privilegeGrantDate?: string        // 权限授予日期
  privilegeHandled?: boolean         // 权限是否已处理
  privilegeHandledBy?: string        // 权限处理人
  privilegeHandledTime?: string      // 权限处理时间
  
  // 数据备份
  dataBackup?: Array<{
    type: string                     // 备份类型
    size: string                     // 数据大小
    location?: string                // 备份位置
  }>
  
  errorMessage?: string              // 错误信息
  notes?: string                     // 备注
}

/**
 * 特殊权限
 */
export interface SpecialPrivilege {
  id: string
  name: string                       // 权限名称
  type: string                       // 权限类型
  system: string                     // 所属系统
  grantDate: string                  // 授权日期
  handled: boolean                   // 是否已处理
  handledBy?: string                 // 处理人
  handledTime?: string               // 处理时间
  handleMethod?: 'revoke' | 'transfer' | 'expire'  // 处理方式
  transferTo?: string                // 转移给谁
  expireTime?: string                // 过期时间
  handleNotes?: string               // 处理说明
}

/**
 * 账号注销记录
 */
export interface AccountCancellation {
  id: string
  applicationId: string
  applicationNo: string
  
  // 员工信息
  employeeId: string
  employeeName: string
  employeeNo: string
  department: string
  position: string
  
  // 账号信息
  systemAccounts: SystemAccount[]    // 系统账号列表
  totalAccounts: number              // 账号总数
  disabledAccounts: number           // 已注销数
  pendingAccounts: number            // 待处理数
  errorAccounts: number              // 失败数
  
  // 进度信息
  progress: number                   // 完成进度（0-100）
  status: 'pending' | 'in_progress' | 'completed'
  
  // 时间信息
  createTime: string
  updateTime: string
  completeTime?: string
  
  remarks?: string                   // 备注
}

/**
 * 账号注销任务
 */
export interface AccountCancellationTask {
  id: string
  taskId: string
  applicationId: string
  applicationNo: string
  
  // 员工信息
  employeeInfo: {
    id: string
    name: string
    no: string
    department: string
    position: string
    lastWorkDate: string
  }
  
  // 账号概况
  accountSummary: {
    total: number                    // 总账号数
    disabled: number                 // 已注销数
    pending: number                  // 待处理数
    error: number                    // 失败数
  }
  
  // 系统分布
  systemDistribution: {
    type: SystemType
    name: string
    total: number
    disabled: number
    allDisabled: boolean
  }[]
  
  // 特殊权限
  specialPrivileges: string[]        // 特殊权限列表
  
  // 任务信息
  assignee: string
  assignTime: string
  daysRemaining?: number             // 距离最后工作日天数
  status: 'pending' | 'in_progress' | 'completed'
}

/**
 * 离职证明模板
 */
export interface CertificateTemplate {
  id: string
  name: string                       // 模板名称
  type: string                       // 证明类型
  title: string                      // 证明标题
  content: string                    // 证明内容（含变量）
  variables: string[]                // 支持的变量列表
  createTime: string
  updateTime: string
}

/**
 * 离职证明数据
 */
export interface CertificateData {
  id: string
  applicationId: string
  
  // 证明信息
  type: 'standard' | 'detailed' | 'simple' | 'english'
  title: string
  content: string
  
  // 签章信息
  issuer: string                     // 签发人
  department: string                 // 签发部门
  issueDate: string                  // 签发日期
  electronicSealId?: string          // 电子印章ID
  
  // 生成信息
  generated: boolean                 // 是否已生成
  generatedFormats?: string[]        // 生成的格式
  generatedFiles?: {
    format: string
    url: string
    size: number
  }[]
  
  // 发送信息
  sent: boolean                      // 是否已发送
  sendMethod?: 'email' | 'sms' | 'system'
  sendTime?: string
  sendTo?: string
  
  // 归档信息
  archived: boolean                  // 是否已归档
  archiveTime?: string
  archivePath?: string
  
  status: 'draft' | 'generated' | 'sent' | 'archived'
}

/**
 * 离职证明任务
 */
export interface CertificateTask {
  id: string
  taskId: string
  applicationId: string
  applicationNo: string
  
  // 员工信息
  employeeInfo: {
    id: string
    name: string
    no: string
    department: string
    position: string
    lastWorkDate: string
    email?: string
    phone?: string
  }
  
  // 证明信息
  certificateType: 'standard' | 'detailed' | 'simple' | 'english'
  
  // 生成信息
  generatedInfo?: {
    operator: string
    time: string
    formats: string[]
  }
  
  // 发送信息
  sendInfo?: {
    method: string
    time: string
    receiver: string
  }
  
  // 任务状态
  status: 'pending' | 'generated' | 'sent' | 'archived'
}

/**
 * 档案归档汇总
 */
export interface ArchiveSummary {
  fileCount: number
  totalSize: number
  integrityCheck: boolean
  missingItems?: string[]
}

/**
 * 员工档案归档
 */
export interface EmployeeArchive {
  id: string
  applicationId: string
  employeeId: string
  archiveNo: string                    // 档案编号
  
  // 归档内容
  formats: string[]                    // 归档格式
  files: {
    category: string
    name: string
    url: string
    size: number
  }[]
  
  // 归档设置
  retentionPeriod: string             // 保存期限
  accessLevel: string                 // 访问权限
  encryption: boolean                 // 是否加密
  encryptionType?: string             // 加密类型
  digitalSignature: boolean           // 数字签名
  
  // 归档信息
  archiver: string                    // 归档人
  archiveTime: string                 // 归档时间
  storagePath: string                 // 存储路径
  status: 'pending' | 'processing' | 'completed' | 'failed'
  
  // 访问记录
  lastAccessTime?: string
  accessCount: number
  
  remarks?: string
}