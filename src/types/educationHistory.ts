/**
import type { SpringPageData } from '@/types/common/api'
 * 教育经历管理模块 TypeScript 类型定义
 *
 * 此文件定义了与后端 EducationHistoryService 完全兼容的 TypeScript 类型
 * 确保前后端数据结构100%匹配
 *
 * <AUTHOR>
 * @since 1.0.0
 */

// ==================== 枚举类型定义 ====================

/**
 * 学习形式枚举
 */
export enum StudyForm {
  FULL_TIME = 'FULL_TIME', // 全日制
  PART_TIME = 'PART_TIME', // 非全日制
  CORRESPONDENCE = 'CORRESPONDENCE', // 函授
  SELF_STUDY = 'SELF_STUDY', // 自学考试
  ONLINE = 'ONLINE', // 网络教育
  OTHER = 'OTHER' // 其他
}

/**
 * 验证状态枚举
 */
export enum VerificationStatus {
  PENDING = 'PENDING', // 待验证
  VERIFIED = 'VERIFIED', // 已验证
  REJECTED = 'REJECTED' // 验证失败
}

/**
 * 排序方向枚举
 */
export enum SortDirection {
  ASC = 'ASC', // 升序
  DESC = 'DESC' // 降序
}

// ==================== 基础实体类型 ====================

/**
 * 教育经历实体类型（与后端 EducationHistory 实体完全匹配）
 */
export interface EducationHistory {
  /** 主键ID */
  id?: string

  /** 员工ID */
  employeeId: string

  /** 员工姓名（冗余字段，用于显示） */
  employeeName?: string

  /** 学习开始日期 */
  startDate?: string

  /** 学习结束日期 */
  endDate?: string

  /** 学历/学位 */
  degree?: string

  /** 毕业院校 */
  graduationSchool?: string

  /** 专业 */
  major?: string

  /** 学习形式 */
  studyForm?: StudyForm

  /** 学习形式名称（用于显示） */
  studyFormName?: string

  /** 核心课程 */
  coreCourses?: string

  /** 毕业论文题目 */
  thesisTitle?: string

  /** 导师 */
  advisor?: string

  /** 平均绩点 */
  gpa?: number

  /** 排名 */
  ranking?: number

  /** 总人数 */
  totalStudents?: number

  /** 荣誉奖励 */
  honors?: string

  /** 证书编号 */
  certificateNumber?: string

  /** 验证状态 */
  verificationStatus?: VerificationStatus

  /** 验证状态名称（用于显示） */
  verificationStatusName?: string

  /** 验证日期 */
  verificationDate?: string

  /** 验证备注 */
  verificationNote?: string

  /** 创建时间 */
  createTime?: string

  /** 创建人 */
  createBy?: string

  /** 更新时间 */
  updateTime?: string

  /** 更新人 */
  updateBy?: string

  /** 是否删除 */
  deleted?: boolean
}

// ==================== 请求类型定义 ====================

/**
 * 教育经历创建请求类型（与后端 EducationHistoryCreateRequest 完全匹配）
 */
export interface EducationHistoryCreateRequest {
  /** 员工ID */
  employeeId: string

  /** 学习开始日期 */
  startDate?: string

  /** 学习结束日期 */
  endDate?: string

  /** 学历/学位 */
  degree?: string

  /** 毕业院校 */
  graduationSchool?: string

  /** 专业 */
  major?: string

  /** 学习形式 */
  studyForm?: StudyForm

  /** 核心课程 */
  coreCourses?: string

  /** 毕业论文题目 */
  thesisTitle?: string

  /** 导师 */
  advisor?: string

  /** 平均绩点 */
  gpa?: number

  /** 排名 */
  ranking?: number

  /** 总人数 */
  totalStudents?: number

  /** 荣誉奖励 */
  honors?: string

  /** 证书编号 */
  certificateNumber?: string
}

/**
 * 教育经历更新请求类型（与后端 EducationHistoryUpdateRequest 完全匹配）
 */
export interface EducationHistoryUpdateRequest {
  /** 学习开始日期 */
  startDate?: string

  /** 学习结束日期 */
  endDate?: string

  /** 学历/学位 */
  degree?: string

  /** 毕业院校 */
  graduationSchool?: string

  /** 专业 */
  major?: string

  /** 学习形式 */
  studyForm?: StudyForm

  /** 核心课程 */
  coreCourses?: string

  /** 毕业论文题目 */
  thesisTitle?: string

  /** 导师 */
  advisor?: string

  /** 平均绩点 */
  gpa?: number

  /** 排名 */
  ranking?: number

  /** 总人数 */
  totalStudents?: number

  /** 荣誉奖励 */
  honors?: string

  /** 证书编号 */
  certificateNumber?: string
}

/**
 * 教育经历查询请求类型（与后端 EducationHistoryQueryRequest 完全匹配）
 */
export interface EducationHistoryQueryRequest {
  /** 页码（从0开始） */
  page?: number

  /** 页大小 */
  size?: number

  /** 关键词搜索 */
  keyword?: string

  /** 员工ID */
  employeeId?: string

  /** 员工ID列表 */
  employeeIds?: string[]

  /** 学历/学位 */
  degree?: string

  /** 学历/学位列表 */
  degrees?: string[]

  /** 毕业院校 */
  graduationSchool?: string

  /** 专业 */
  major?: string

  /** 学习形式 */
  studyForm?: StudyForm

  /** 学习形式列表 */
  studyForms?: StudyForm[]

  /** 开始日期范围-开始 */
  startDateStart?: string

  /** 开始日期范围-结束 */
  startDateEnd?: string

  /** 结束日期范围-开始 */
  endDateStart?: string

  /** 结束日期范围-结束 */
  endDateEnd?: string

  /** 最小绩点 */
  minGpa?: number

  /** 最大绩点 */
  maxGpa?: number

  /** 验证状态 */
  verificationStatus?: VerificationStatus

  /** 验证状态列表 */
  verificationStatuses?: VerificationStatus[]

  /** 是否有论文 */
  hasThesis?: boolean

  /** 排序字段 */
  sortBy?: string

  /** 排序方向 */
  sortDirection?: SortDirection
}

// ==================== 响应类型定义 ====================

/**
 * 分页响应类型（与后端 Page<T> 完全匹配）
 */
// 使用通用的SpringPageData类型
export type PageResponse<T> = SpringPageData<T>

/**
 * 统一响应结果类型（与后端 Result<T> 完全匹配）
 */
export interface Result<T> {
  /** 响应码 */
  code: number

  /** 响应消息 */
  message?: string

  /** 响应数据 */
  data?: T

  /** 时间戳 */
  timestamp?: number
}

// ==================== 统计分析类型 ====================

/**
 * 教育经历统计信息类型
 */
export interface EducationHistoryStatistics {
  /** 总数量 */
  totalCount: number

  /** 学历分布 */
  degreeDistribution: Record<string, number>

  /** 学习形式分布 */
  studyFormDistribution: Record<string, number>

  /** 验证状态分布 */
  verificationStatusDistribution: Record<string, number>

  /** 热门学校 */
  topSchools: SchoolStats[]

  /** 热门专业 */
  topMajors: MajorStats[]

  /** 年度趋势 */
  yearlyTrend: YearlyTrendItem[]
}

/**
 * 学校统计
 */
export interface SchoolStats {
  /** 学校名称 */
  school: string

  /** 数量 */
  count: number
}

/**
 * 专业统计
 */
export interface MajorStats {
  /** 专业名称 */
  major: string

  /** 数量 */
  count: number
}

/**
 * 年度趋势项
 */
export interface YearlyTrendItem {
  /** 年份 */
  year: number

  /** 数量 */
  count: number
}

// ==================== 验证管理类型 ====================

/**
 * 验证请求
 */
export interface VerificationRequest {
  /** 验证状态 */
  status: VerificationStatus

  /** 验证备注 */
  note?: string
}

/**
 * 批量验证请求
 */
export interface BatchVerificationRequest {
  /** ID列表 */
  ids: string[]

  /** 验证状态 */
  status: VerificationStatus

  /** 验证备注 */
  note?: string
}

// ==================== 批量操作类型 ====================

/**
 * 批量删除请求
 */
export interface BatchDeleteRequest {
  /** ID列表 */
  ids: string[]
}

/**
 * 批量导入结果
 */
export interface ImportResult {
  /** 成功数量 */
  successCount: number

  /** 失败数量 */
  failureCount: number

  /** 错误信息列表 */
  errors: string[]
}

// ==================== 表单验证类型 ====================

/**
 * 表单验证规则类型
 */
export interface FormValidationRule {
  /** 是否必填 */
  required?: boolean

  /** 错误消息 */
  message?: string

  /** 触发方式 */
  trigger?: 'blur' | 'change'

  /** 最小长度 */
  min?: number

  /** 最大长度 */
  max?: number

  /** 最小值 */
  minValue?: number

  /** 最大值 */
  maxValue?: number

  /** 正则表达式 */
  pattern?: RegExp

  /** 自定义验证函数 */

  validator?: (rule: unknown, value: unknown, callback: unknown) => void
}

/**
 * 表单验证规则集合
 */
export interface EducationHistoryFormRules {
  employeeId: FormValidationRule[]
  degree?: FormValidationRule[]
  graduationSchool?: FormValidationRule[]
  major?: FormValidationRule[]
  startDate?: FormValidationRule[]
  endDate?: FormValidationRule[]
  gpa?: FormValidationRule[]
  ranking?: FormValidationRule[]
  totalStudents?: FormValidationRule[]
}

// ==================== UI组件类型 ====================

/**
 * 表格列配置
 */
export interface TableColumn {
  /** 属性名 */
  prop: string

  /** 列标题 */
  label: string

  /** 列宽度 */
  width?: number

  /** 最小宽度 */
  minWidth?: number

  /** 是否可排序 */
  sortable?: boolean

  /** 是否显示溢出提示 */
  showOverflowTooltip?: boolean

  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
}

/**
 * 搜索表单配置
 */
export interface SearchFormConfig {
  /** 是否显示高级搜索 */
  showAdvanced: boolean

  /** 搜索字段配置 */
  fields: SearchFieldConfig[]
}

/**
 * 搜索字段配置
 */
export interface SearchFieldConfig {
  /** 字段名 */
  field: string

  /** 字段标签 */
  label: string

  /** 字段类型 */
  type: 'input' | 'select' | 'date' | 'daterange' | 'number'

  /** 选项列表（用于select类型） */

  options?: Array<{ label: string; value: unknown }>

  /** 占位符 */
  placeholder?: string

  /** 是否在基础搜索中显示 */
  basic?: boolean
}
