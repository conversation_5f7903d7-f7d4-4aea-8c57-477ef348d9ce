// 报表权限相关类型定义

export interface ReportPermissions {
  functions: FunctionPermissionItem[]
  dataScopes: DataPermissionItem[]
  fieldPermissions: FieldPermissionItem[]
  advanced: AdvancedPermissionSettings
}

export interface FunctionPermissionItem {
  principalType: 'role' | 'user' | 'group'
  principalId: string
  principalName: string
  permissions: FunctionPermissionSet
  inherited?: boolean
}

export interface FunctionPermissionSet {
  view: boolean
  edit: boolean
  delete: boolean
  export: boolean
  publish: boolean
  share: boolean
}

export interface DataPermissionItem {
  principalType: 'role' | 'user' | 'group'
  principalId: string
  principalName: string
  dataScope: DataScope
  conditions?: DataCondition[]
  enableTimeLimit?: boolean
  timeRange?: TimeRangeConfig
}

export interface DataScope {
  type: 'all' | 'department' | 'personal' | 'custom'
  departments?: string[]
  includeSubDepts?: boolean
  customRule?: string
}

export interface DataCondition {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'not_in' | 'like' | 'not_like'

  value: unknown
  connector?: 'and' | 'or'
}

export interface TimeRangeConfig {
  type: 'relative' | 'absolute'
  value?: number
  unit?: 'day' | 'month' | 'year'
  dateRange?: Date[]
}

export interface FieldPermissionItem {
  principalType: 'role' | 'user' | 'group'
  principalId: string
  principalName: string
  fields: FieldPermission[]
}

export interface FieldPermission {
  fieldName: string
  fieldLabel: string
  permission: 'hidden' | 'masked' | 'readonly' | 'full'
  maskType?: 'partial' | 'full' | 'custom'
  maskRule?: string
  exportable?: boolean
}

export interface AdvancedPermissionSettings {
  timeRangeLimit: TimeRangeLimit | null
  accessFrequencyLimit: AccessFrequencyLimit | null
  exportLimit: ExportLimit | null
  watermark: boolean
  ipWhitelist?: string[]
  accessTimeWindow?: TimeWindow
  approvalRequired?: ApprovalConfig
}

export interface TimeRangeLimit {
  enabled: boolean
  maxRange: number
  unit: 'day' | 'month' | 'year'
  allowFuture: boolean
}

export interface AccessFrequencyLimit {
  enabled: boolean
  maxRequests: number
  timeWindow: number
  unit: 'minute' | 'hour' | 'day'
}

export interface ExportLimit {
  enabled: boolean
  maxRecords: number
  requireApproval: boolean
  allowedFormats: string[]
}

export interface TimeWindow {
  enabled: boolean
  startTime: string
  endTime: string
  weekdays: number[]
  timezone: string
}

export interface ApprovalConfig {
  enabled: boolean
  approvers: string[]
  approvalLevel: number
  autoExpire: boolean
  expireDays: number
}

export interface PermissionTemplate {
  id: string
  name: string
  description?: string
  category: string
  permissions: ReportPermissions
  isSystem: boolean
  createdBy?: string
  createdAt?: Date
}

export interface PermissionValidationResult {
  allowed: boolean
  deniedReasons?: string[]
  appliedRules?: string[]
  suggestions?: string[]
}

export interface PermissionAuditLog {
  id: string
  reportId: string
  userId: string
  userName: string
  action: string
  timestamp: Date
  ip: string
  userAgent: string
  result: 'success' | 'denied'
  deniedReason?: string

  affectedData?: unknown
}

export interface DataMaskingRule {
  fieldName: string
  maskType: 'partial' | 'full' | 'hash' | 'replace' | 'custom'
  maskChar?: string
  visibleStart?: number
  visibleEnd?: number
  replacement?: string
  customRule?: string
}

export interface PermissionInheritance {
  enabled: boolean
  source: 'parent' | 'template' | 'role'
  parentId?: string
  templateId?: string
  roleId?: string
  overridable: boolean
}

export interface PermissionConflict {
  field: string
  principals: string[]
  conflictType: 'function' | 'data' | 'field'
  resolution: 'deny' | 'allow' | 'merge'

  details?: unknown
}
