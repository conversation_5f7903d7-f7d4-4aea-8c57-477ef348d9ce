/**
 * BPMN.js TypeScript 类型定义
 */

declare module 'bpmn-js/lib/Modeler' {
  import { EventEmitter } from 'events'

  export interface ModelerOptions {
    container: string | HTMLElement
    width?: string | number
    height?: string | number
    additionalModules?: ModuleDeclaration[]
    moddleExtensions?: Record<string, any>
    keyboard?: {
      bindTo?: Element
    }
  }

  export interface ModuleDeclaration {
    [key: string]: ['type' | 'factory' | 'value', any]
  }

  export interface SaveXMLResult {
    xml: string
  }

  export interface SaveSVGResult {
    svg: string
  }

  export interface ImportXMLResult {
    warnings: string[]
  }

  export interface ElementRegistry {
     
    get(id: string): any
     
    getAll(): any[]
     
    filter(fn: (element: any) => boolean): any[]
  }

  export interface Modeling {
     
    updateProperties(element: any, properties: Record<string, any>): void
     
    createShape(shape: any, position: { x: number; y: number }, parent?: any): any
     
    removeShape(shape: any): void
     
    connect(source: any, target: any, connection?: any): any
  }

  export interface Canvas {
    zoom(newScale?: number | 'fit-viewport', center?: boolean | { x: number; y: number }): number
    viewbox(): { x: number; y: number; width: number; height: number }
     
    scrollToElement(element: any): void
  }

  export interface Selection {
     
    select(elements: any | any[]): void
     
    deselect(elements?: any | any[]): void
     
    get(): any[]
  }

  export interface CommandStack {
     
    execute(command: string, context: any): void
    undo(): void
    redo(): void
    canUndo(): boolean
    canRedo(): boolean
    clear(): void
  }

  export default class Modeler extends EventEmitter {
    constructor(options: ModelerOptions)

    importXML(xml: string): Promise<ImportXMLResult>
    saveXML(options?: { format?: boolean; preamble?: boolean }): Promise<SaveXMLResult>
     
    saveSVG(options?: any): Promise<SaveSVGResult>

    createDiagram(): Promise<void>
    clear(): void
    destroy(): void
    detach(): void
    attachTo(container: string | HTMLElement): void

    get<T = any>(serviceName: string): T
     
    invoke<T = any>(fn: Function, context?: any): T

    // 常用服务
    get elementRegistry(): ElementRegistry
    get modeling(): Modeling
    get canvas(): Canvas
    get selection(): Selection
    get commandStack(): CommandStack
  }
}

declare module 'bpmn-js/lib/Viewer' {
  import Modeler from 'bpmn-js/lib/Modeler'

  export default class Viewer extends Modeler {
     
    constructor(options: any)
  }
}

declare module 'bpmn-js/lib/NavigatedViewer' {
  import Viewer from 'bpmn-js/lib/Viewer'

  export default class NavigatedViewer extends Viewer {
     
    constructor(options: any)
  }
}

declare module 'bpmn-js-properties-panel' {
  export interface PropertiesPanelOptions {
    container: string | HTMLElement
     
    modeler: any
     
    propertiesProvider?: any
  }

  export default class PropertiesPanel {
    constructor(options: PropertiesPanelOptions)

    attachTo(container: string | HTMLElement): void
    detach(): void
    destroy(): void

     
    registerProvider(provider: any): void
     
    unregisterProvider(provider: any): void
  }
}

declare module 'diagram-js/lib/features/rules/RuleProvider' {
  export class RuleProvider {
     
    constructor(eventBus: any)

     
    addRule(action: string, priority: number, fn: (context: any) => boolean | any): void
    init(): void
  }
}

declare module 'diagram-js/lib/draw/BaseRenderer' {
  export default class BaseRenderer {
     
    constructor(eventBus: any, priority?: number)

     
    canRender(element: any): boolean
     
    drawShape(parentGfx: any, element: any): any
     
    drawConnection(parentGfx: any, element: any): any
     
    getShapePath(shape: any): string
     
    getConnectionPath(connection: any): string
  }
}

declare module 'bpmn-js/lib/features/palette/Palette' {
  export interface Palette {
     
    registerProvider(provider: any): void
    getEntries(): Record<string, any>
     
    trigger(action: string, event: any, autoActivate?: boolean): void
    close(): void
    open(): void
    toggle(): void
    isOpen(): boolean
  }
}

declare module 'tiny-svg' {
  export function append(parent: SVGElement, child: SVGElement): SVGElement
  export function create(name: string, attrs?: Record<string, any>): SVGElement
  export function attr(element: SVGElement, attrs: Record<string, any>): void
  export function remove(element: SVGElement): void
  export function clear(element: SVGElement): void
  export function classes(element: SVGElement): {
    add(...classNames: string[]): void
    remove(...classNames: string[]): void
    toggle(className: string): void
    contains(className: string): boolean
  }
}
