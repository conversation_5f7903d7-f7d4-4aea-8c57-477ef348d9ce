import type { SpringPageData } from '@/types/common/api'

/**
 * 家庭成员管理模块 TypeScript 类型定义
 *
 * 此文件定义了与后端 FamilyMemberService 完全兼容的 TypeScript 类型
 * 确保前后端数据结构100%匹配
 *
 * <AUTHOR>
 * @since 1.0.0
 */

// ==================== 枚举类型定义 ====================

/**
 * 家庭关系枚举
 */
export enum Relationship {
  SPOUSE = 'SPOUSE', // 配偶
  CHILD = 'CHILD', // 子女
  FATHER = 'FATHER', // 父亲
  MOTHER = 'MOTHER', // 母亲
  BROTHER = 'BROTHER', // 兄弟
  SISTER = 'SISTER', // 姐妹
  GRANDFATHER = 'GRANDFATHER', // 祖父
  GRANDMOTHER = 'GRANDMOTHER', // 祖母
  OTHER = 'OTHER' // 其他
}

/**
 * 性别枚举
 */
export enum Gender {
  MALE = 'MALE', // 男
  FEMALE = 'FEMALE' // 女
}

/**
 * 排序方向枚举
 */
export enum SortDirection {
  ASC = 'ASC', // 升序
  DESC = 'DESC' // 降序
}

// ==================== 基础实体类型 ====================

/**
 * 家庭成员实体类型（与后端 FamilyMember 实体完全匹配）
 */
export interface FamilyMember {
  /** 主键ID */
  id?: string

  /** 员工ID */
  employeeId: string

  /** 员工姓名（冗余字段，用于显示） */
  employeeName?: string

  /** 家庭成员姓名 */
  name: string

  /** 关系类型 */
  relationship: Relationship

  /** 关系类型名称（用于显示） */
  relationshipName?: string

  /** 性别 */
  gender?: Gender

  /** 性别名称（用于显示） */
  genderName?: string

  /** 出生日期 */
  birthDate?: string

  /** 年龄（计算字段） */
  age?: number

  /** 身份证号 */
  idCard?: string

  /** 联系电话 */
  phone?: string

  /** 工作单位 */
  workUnit?: string

  /** 职务 */
  position?: string

  /** 住址 */
  address?: string

  /** 是否为紧急联系人 */
  isEmergencyContact?: boolean

  /** 是否为被抚养人 */
  isDependent?: boolean

  /** 政治面貌 */
  politicalStatus?: string

  /** 学历 */
  education?: string

  /** 健康状况 */
  healthStatus?: string

  /** 备注 */
  remarks?: string

  /** 创建时间 */
  createTime?: string

  /** 创建人 */
  createBy?: string

  /** 更新时间 */
  updateTime?: string

  /** 更新人 */
  updateBy?: string

  /** 是否删除 */
  deleted?: boolean
}

// ==================== 请求类型定义 ====================

/**
 * 家庭成员创建请求类型（与后端 FamilyMemberCreateRequest 完全匹配）
 */
export interface FamilyMemberCreateRequest {
  /** 员工ID */
  employeeId: string

  /** 家庭成员姓名 */
  name: string

  /** 关系类型 */
  relationship: Relationship

  /** 性别 */
  gender?: Gender

  /** 出生日期 */
  birthDate?: string

  /** 身份证号 */
  idCard?: string

  /** 联系电话 */
  phone?: string

  /** 工作单位 */
  workUnit?: string

  /** 职务 */
  position?: string

  /** 住址 */
  address?: string

  /** 是否为紧急联系人 */
  isEmergencyContact?: boolean

  /** 是否为被抚养人 */
  isDependent?: boolean

  /** 政治面貌 */
  politicalStatus?: string

  /** 学历 */
  education?: string

  /** 健康状况 */
  healthStatus?: string

  /** 备注 */
  remarks?: string
}

/**
 * 家庭成员更新请求类型（与后端 FamilyMemberUpdateRequest 完全匹配）
 */
export interface FamilyMemberUpdateRequest {
  /** 家庭成员姓名 */
  name?: string

  /** 关系类型 */
  relationship?: Relationship

  /** 性别 */
  gender?: Gender

  /** 出生日期 */
  birthDate?: string

  /** 身份证号 */
  idCard?: string

  /** 联系电话 */
  phone?: string

  /** 工作单位 */
  workUnit?: string

  /** 职务 */
  position?: string

  /** 住址 */
  address?: string

  /** 是否为紧急联系人 */
  isEmergencyContact?: boolean

  /** 是否为被抚养人 */
  isDependent?: boolean

  /** 政治面貌 */
  politicalStatus?: string

  /** 学历 */
  education?: string

  /** 健康状况 */
  healthStatus?: string

  /** 备注 */
  remarks?: string
}

/**
 * 家庭成员查询请求类型（与后端 FamilyMemberQueryRequest 完全匹配）
 */
export interface FamilyMemberQueryRequest {
  /** 页码（从0开始） */
  page?: number

  /** 页大小 */
  size?: number

  /** 关键词搜索 */
  keyword?: string

  /** 员工ID */
  employeeId?: string

  /** 员工ID列表 */
  employeeIds?: string[]

  /** 家庭成员姓名 */
  name?: string

  /** 关系类型 */
  relationship?: Relationship

  /** 关系类型列表 */
  relationships?: Relationship[]

  /** 性别 */
  gender?: Gender

  /** 性别列表 */
  genders?: Gender[]

  /** 最小年龄 */
  minAge?: number

  /** 最大年龄 */
  maxAge?: number

  /** 出生日期范围-开始 */
  birthDateStart?: string

  /** 出生日期范围-结束 */
  birthDateEnd?: string

  /** 工作单位 */
  workUnit?: string

  /** 职务 */
  position?: string

  /** 是否为紧急联系人 */
  isEmergencyContact?: boolean

  /** 是否为被抚养人 */
  isDependent?: boolean

  /** 政治面貌 */
  politicalStatus?: string

  /** 学历 */
  education?: string

  /** 健康状况 */
  healthStatus?: string

  /** 是否有联系电话 */
  hasPhone?: boolean

  /** 是否有身份证号 */
  hasIdCard?: boolean

  /** 排序字段 */
  sortBy?: string

  /** 排序方向 */
  sortDirection?: SortDirection
}

// ==================== 响应类型定义 ====================

/**
 * 分页响应类型（与后端 Page<T> 完全匹配）
 */
// 使用通用的SpringPageData类型
export type PageResponse<T> = SpringPageData<T>

/**
 * 统一响应结果类型（与后端 Result<T> 完全匹配）
 */
export interface Result<T> {
  /** 响应码 */
  code: number

  /** 响应消息 */
  message?: string

  /** 响应数据 */
  data?: T

  /** 时间戳 */
  timestamp?: number
}

// ==================== 统计分析类型 ====================

/**
 * 家庭成员统计信息类型
 */
export interface FamilyMemberStatistics {
  /** 总数量 */
  totalCount: number

  /** 关系类型分布 */
  relationshipDistribution: Record<string, number>

  /** 性别分布 */
  genderDistribution: Record<string, number>

  /** 年龄组分布 */
  ageGroupDistribution: Record<string, number>

  /** 紧急联系人数量 */
  emergencyContactCount: number

  /** 被抚养人数量 */
  dependentCount: number

  /** 未成年人数量 */
  minorCount: number

  /** 平均年龄 */
  averageAge: number

  /** 热门工作单位 */
  topWorkUnits: WorkUnitStats[]

  /** 学历分布 */
  educationDistribution: Record<string, number>

  /** 健康状况分布 */
  healthStatusDistribution: Record<string, number>
}

/**
 * 工作单位统计
 */
export interface WorkUnitStats {
  /** 工作单位 */
  workUnit: string

  /** 数量 */
  count: number
}

/**
 * 家庭规模统计
 */
export interface FamilySizeStats {
  /** 员工ID */
  employeeId: string

  /** 员工姓名 */
  employeeName: string

  /** 家庭规模 */
  familySize: number
}

// ==================== 关系管理类型 ====================

/**
 * 关系验证请求
 */
export interface RelationshipValidationRequest {
  /** 员工ID */
  employeeId: string

  /** 关系类型 */
  relationship: Relationship

  /** 性别（可选） */
  gender?: Gender
}

/**
 * 关系验证响应
 */
export interface RelationshipValidationResponse {
  /** 是否有效 */
  valid: boolean

  /** 验证消息 */
  message?: string

  /** 建议列表 */
  suggestions?: string[]
}

/**
 * 紧急联系人设置请求
 */
export interface EmergencyContactRequest {
  /** 是否为紧急联系人 */
  isEmergencyContact: boolean
}

/**
 * 被抚养人设置请求
 */
export interface DependentRequest {
  /** 是否为被抚养人 */
  isDependent: boolean
}

// ==================== 批量操作类型 ====================

/**
 * 批量删除请求
 */
export interface BatchDeleteRequest {
  /** ID列表 */
  ids: string[]
}

/**
 * 批量紧急联系人设置请求
 */
export interface BatchEmergencyContactRequest {
  /** ID列表 */
  ids: string[]

  /** 是否为紧急联系人 */
  isEmergencyContact: boolean
}

/**
 * 批量导入结果
 */
export interface ImportResult {
  /** 成功数量 */
  successCount: number

  /** 失败数量 */
  failureCount: number

  /** 错误信息列表 */
  errors: string[]
}

// ==================== 表单验证类型 ====================

/**
 * 表单验证规则类型
 */
export interface FormValidationRule {
  /** 是否必填 */
  required?: boolean

  /** 错误消息 */
  message?: string

  /** 触发方式 */
  trigger?: 'blur' | 'change'

  /** 最小长度 */
  min?: number

  /** 最大长度 */
  max?: number

  /** 最小值 */
  minValue?: number

  /** 最大值 */
  maxValue?: number

  /** 正则表达式 */
  pattern?: RegExp

  /** 自定义验证函数 */

  validator?: (rule: unknown, value: unknown, callback: unknown) => void
}

/**
 * 表单验证规则集合
 */
export interface FamilyMemberFormRules {
  employeeId: FormValidationRule[]
  name: FormValidationRule[]
  relationship: FormValidationRule[]
  gender?: FormValidationRule[]
  birthDate?: FormValidationRule[]
  idCard?: FormValidationRule[]
  phone?: FormValidationRule[]
  workUnit?: FormValidationRule[]
  position?: FormValidationRule[]
  address?: FormValidationRule[]
}

// ==================== UI组件类型 ====================

/**
 * 表格列配置
 */
export interface TableColumn {
  /** 属性名 */
  prop: string

  /** 列标题 */
  label: string

  /** 列宽度 */
  width?: number

  /** 最小宽度 */
  minWidth?: number

  /** 是否可排序 */
  sortable?: boolean

  /** 是否显示溢出提示 */
  showOverflowTooltip?: boolean

  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
}

/**
 * 搜索表单配置
 */
export interface SearchFormConfig {
  /** 是否显示高级搜索 */
  showAdvanced: boolean

  /** 搜索字段配置 */
  fields: SearchFieldConfig[]
}

/**
 * 搜索字段配置
 */
export interface SearchFieldConfig {
  /** 字段名 */
  field: string

  /** 字段标签 */
  label: string

  /** 字段类型 */
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'switch'

  /** 选项列表（用于select类型） */

  options?: Array<{ label: string; value: unknown }>

  /** 占位符 */
  placeholder?: string

  /** 是否在基础搜索中显示 */
  basic?: boolean
}
