/**
import type { SpringPageData } from '@/types/common/api'
 * 职业资格管理模块 TypeScript 类型定义
 *
 * 此文件定义了与后端 VocationalQualificationService 完全兼容的 TypeScript 类型
 * 确保前后端数据结构100%匹配
 *
 * <AUTHOR>
 * @since 1.0.0
 */

// ==================== 枚举类型定义 ====================

/**
 * 证书类型枚举
 */
export enum CertificateType {
  SKILL_CERTIFICATE = 'SKILL_CERTIFICATE', // 技能证书
  PROFESSIONAL_LICENSE = 'PROFESSIONAL_LICENSE', // 执业资格
  TITLE_CERTIFICATE = 'TITLE_CERTIFICATE', // 职称证书
  TRAINING_CERTIFICATE = 'TRAINING_CERTIFICATE', // 培训证书
  OTHER = 'OTHER' // 其他
}

/**
 * 有效期状态枚举
 */
export enum ValidityStatus {
  VALID = 'VALID', // 有效
  EXPIRING_SOON = 'EXPIRING_SOON', // 即将到期
  EXPIRED = 'EXPIRED', // 已过期
  SUSPENDED = 'SUSPENDED' // 已暂停
}

/**
 * 验证状态枚举
 */
export enum VerificationStatus {
  PENDING = 'PENDING', // 待验证
  VERIFIED = 'VERIFIED', // 已验证
  REJECTED = 'REJECTED' // 验证失败
}

/**
 * 优先级枚举
 */
export enum Priority {
  LOW = 'LOW', // 低
  MEDIUM = 'MEDIUM', // 中
  HIGH = 'HIGH', // 高
  CRITICAL = 'CRITICAL' // 紧急
}

/**
 * 排序方向枚举
 */
export enum SortDirection {
  ASC = 'ASC', // 升序
  DESC = 'DESC' // 降序
}

// ==================== 基础实体类型 ====================

/**
 * 职业资格实体类型（与后端 VocationalQualification 实体完全匹配）
 */
export interface VocationalQualification {
  /** 主键ID */
  id?: string

  /** 员工ID */
  employeeId: string

  /** 员工姓名（冗余字段，用于显示） */
  employeeName?: string

  /** 证书名称 */
  certificateName: string

  /** 证书类型 */
  certificateType: CertificateType

  /** 证书类型名称（用于显示） */
  certificateTypeName?: string

  /** 证书编号 */
  certificateNumber?: string

  /** 颁发机构 */
  issuingAuthority?: string

  /** 颁发日期 */
  issueDate?: string

  /** 有效期（月数） */
  validityPeriod?: number

  /** 到期日期 */
  expiryDate?: string

  /** 有效期状态 */
  validityStatus: ValidityStatus

  /** 有效期状态名称（用于显示） */
  validityStatusName?: string

  /** 证书等级 */
  level?: string

  /** 适用范围 */
  scope?: string

  /** 是否需要续期 */
  renewalRequired?: boolean

  /** 最后续期日期 */
  lastRenewalDate?: string

  /** 下次续期日期 */
  nextRenewalDate?: string

  /** 续期次数 */
  renewalCount?: number

  /** 附件URL */
  attachmentUrl?: string

  /** 验证状态 */
  verificationStatus?: VerificationStatus

  /** 验证状态名称（用于显示） */
  verificationStatusName?: string

  /** 验证日期 */
  verificationDate?: string

  /** 验证备注 */
  verificationNote?: string

  /** 是否激活 */
  isActive?: boolean

  /** 优先级 */
  priority?: Priority

  /** 优先级名称（用于显示） */
  priorityName?: string

  /** 相关技能 */
  relatedSkills?: string

  /** 备注 */
  remarks?: string

  /** 创建时间 */
  createTime?: string

  /** 创建人 */
  createBy?: string

  /** 更新时间 */
  updateTime?: string

  /** 更新人 */
  updateBy?: string

  /** 是否删除 */
  deleted?: boolean
}

// ==================== 请求类型定义 ====================

/**
 * 职业资格创建请求类型（与后端 VocationalQualificationCreateRequest 完全匹配）
 */
export interface VocationalQualificationCreateRequest {
  /** 员工ID */
  employeeId: string

  /** 证书名称 */
  certificateName: string

  /** 证书类型 */
  certificateType: CertificateType

  /** 证书编号 */
  certificateNumber?: string

  /** 颁发机构 */
  issuingAuthority?: string

  /** 颁发日期 */
  issueDate?: string

  /** 有效期（月数） */
  validityPeriod?: number

  /** 到期日期 */
  expiryDate?: string

  /** 证书等级 */
  level?: string

  /** 适用范围 */
  scope?: string

  /** 是否需要续期 */
  renewalRequired?: boolean

  /** 附件URL */
  attachmentUrl?: string

  /** 是否激活 */
  isActive?: boolean

  /** 优先级 */
  priority?: Priority

  /** 相关技能 */
  relatedSkills?: string

  /** 备注 */
  remarks?: string
}

/**
 * 职业资格更新请求类型（与后端 VocationalQualificationUpdateRequest 完全匹配）
 */
export interface VocationalQualificationUpdateRequest {
  /** 证书名称 */
  certificateName?: string

  /** 证书类型 */
  certificateType?: CertificateType

  /** 证书编号 */
  certificateNumber?: string

  /** 颁发机构 */
  issuingAuthority?: string

  /** 颁发日期 */
  issueDate?: string

  /** 有效期（月数） */
  validityPeriod?: number

  /** 到期日期 */
  expiryDate?: string

  /** 证书等级 */
  level?: string

  /** 适用范围 */
  scope?: string

  /** 是否需要续期 */
  renewalRequired?: boolean

  /** 附件URL */
  attachmentUrl?: string

  /** 是否激活 */
  isActive?: boolean

  /** 优先级 */
  priority?: Priority

  /** 相关技能 */
  relatedSkills?: string

  /** 备注 */
  remarks?: string
}

/**
 * 职业资格查询请求类型（与后端 VocationalQualificationQueryRequest 完全匹配）
 */
export interface VocationalQualificationQueryRequest {
  /** 页码（从0开始） */
  page?: number

  /** 页大小 */
  size?: number

  /** 关键词搜索 */
  keyword?: string

  /** 员工ID */
  employeeId?: string

  /** 员工ID列表 */
  employeeIds?: string[]

  /** 证书名称 */
  certificateName?: string

  /** 证书类型 */
  certificateType?: CertificateType

  /** 证书类型列表 */
  certificateTypes?: CertificateType[]

  /** 证书编号 */
  certificateNumber?: string

  /** 颁发机构 */
  issuingAuthority?: string

  /** 有效期状态 */
  validityStatus?: ValidityStatus

  /** 有效期状态列表 */
  validityStatuses?: ValidityStatus[]

  /** 验证状态 */
  verificationStatus?: VerificationStatus

  /** 验证状态列表 */
  verificationStatuses?: VerificationStatus[]

  /** 证书等级 */
  level?: string

  /** 证书等级列表 */
  levels?: string[]

  /** 优先级 */
  priority?: Priority

  /** 优先级列表 */
  priorities?: Priority[]

  /** 颁发日期范围-开始 */
  issueDateStart?: string

  /** 颁发日期范围-结束 */
  issueDateEnd?: string

  /** 到期日期范围-开始 */
  expiryDateStart?: string

  /** 到期日期范围-结束 */
  expiryDateEnd?: string

  /** 是否需要续期 */
  renewalRequired?: boolean

  /** 是否激活 */
  isActive?: boolean

  /** 是否有附件 */
  hasAttachment?: boolean

  /** 是否即将到期 */
  isExpiringSoon?: boolean

  /** 到期天数 */
  daysToExpiry?: number

  /** 排序字段 */
  sortBy?: string

  /** 排序方向 */
  sortDirection?: SortDirection
}

// ==================== 响应类型定义 ====================

/**
 * 分页响应类型（与后端 Page<T> 完全匹配）
 */
// 使用通用的SpringPageData类型
export type PageResponse<T> = SpringPageData<T>

/**
 * 统一响应结果类型（与后端 Result<T> 完全匹配）
 */
export interface Result<T> {
  /** 响应码 */
  code: number

  /** 响应消息 */
  message?: string

  /** 响应数据 */
  data?: T

  /** 时间戳 */
  timestamp?: number
}

// ==================== 统计分析类型 ====================

/**
 * 职业资格统计信息类型
 */
export interface VocationalQualificationStatistics {
  /** 总数量 */
  totalCount: number

  /** 有效数量 */
  validCount: number

  /** 即将到期数量 */
  expiringSoonCount: number

  /** 已过期数量 */
  expiredCount: number

  /** 已暂停数量 */
  suspendedCount: number

  /** 证书类型分布 */
  certificateTypeDistribution: Record<string, number>

  /** 有效期状态分布 */
  validityStatusDistribution: Record<string, number>

  /** 等级分布 */
  levelDistribution: Record<string, number>

  /** 优先级分布 */
  priorityDistribution: Record<string, number>

  /** 热门颁发机构 */
  topIssuingAuthorities: IssuingAuthorityStats[]

  /** 月度到期趋势 */
  monthlyExpiryTrend: MonthlyExpiryTrendItem[]

  /** 续期趋势 */
  renewalTrend: RenewalTrendItem[]

  /** 平均有效期 */
  averageValidityPeriod: number

  /** 总续期次数 */
  totalRenewalCount: number
}

/**
 * 颁发机构统计
 */
export interface IssuingAuthorityStats {
  /** 颁发机构 */
  issuingAuthority: string

  /** 数量 */
  count: number
}

/**
 * 月度到期趋势项
 */
export interface MonthlyExpiryTrendItem {
  /** 月份 */
  month: string

  /** 到期数量 */
  expiryCount: number
}

/**
 * 续期趋势项
 */
export interface RenewalTrendItem {
  /** 月份 */
  month: string

  /** 续期数量 */
  renewalCount: number
}

/**
 * 员工职业资格汇总
 */
export interface EmployeeSummary {
  /** 总证书数 */
  totalCertificates: number

  /** 有效证书数 */
  validCertificates: number

  /** 即将到期数 */
  expiringSoon: number

  /** 已过期数 */
  expired: number

  /** 最新证书 */
  latestCertificate?: VocationalQualification
}

// ==================== 有效期管理类型 ====================

/**
 * 续期请求
 */
export interface RenewalRequest {
  /** 新到期日期 */
  newExpiryDate: string

  /** 续期备注 */
  note?: string
}

/**
 * 批量续期请求
 */
export interface BatchRenewalRequest {
  /** ID列表 */
  ids: string[]

  /** 新到期日期 */
  newExpiryDate: string

  /** 续期备注 */
  note?: string
}

/**
 * 暂停请求
 */
export interface SuspendRequest {
  /** 暂停原因 */
  reason?: string
}

/**
 * 恢复请求
 */
export interface ResumeRequest {
  /** 恢复备注 */
  note?: string
}

/**
 * 验证请求
 */
export interface VerificationRequest {
  /** 验证状态 */
  status: VerificationStatus

  /** 验证备注 */
  note?: string
}

/**
 * 批量验证请求
 */
export interface BatchVerificationRequest {
  /** ID列表 */
  ids: string[]

  /** 验证状态 */
  status: VerificationStatus

  /** 验证备注 */
  note?: string
}

// ==================== 批量操作类型 ====================

/**
 * 批量删除请求
 */
export interface BatchDeleteRequest {
  /** ID列表 */
  ids: string[]
}

/**
 * 批量激活设置请求
 */
export interface BatchActiveRequest {
  /** ID列表 */
  ids: string[]

  /** 是否激活 */
  isActive: boolean
}

/**
 * 批量导入结果
 */
export interface ImportResult {
  /** 成功数量 */
  successCount: number

  /** 失败数量 */
  failureCount: number

  /** 错误信息列表 */
  errors: string[]
}

// ==================== 表单验证类型 ====================

/**
 * 表单验证规则类型
 */
export interface FormValidationRule {
  /** 是否必填 */
  required?: boolean

  /** 错误消息 */
  message?: string

  /** 触发方式 */
  trigger?: 'blur' | 'change'

  /** 最小长度 */
  min?: number

  /** 最大长度 */
  max?: number

  /** 最小值 */
  minValue?: number

  /** 最大值 */
  maxValue?: number

  /** 正则表达式 */
  pattern?: RegExp

  /** 自定义验证函数 */

  validator?: (rule: unknown, value: unknown, callback: unknown) => void
}

/**
 * 表单验证规则集合
 */
export interface VocationalQualificationFormRules {
  employeeId: FormValidationRule[]
  certificateName: FormValidationRule[]
  certificateType: FormValidationRule[]
  certificateNumber?: FormValidationRule[]
  issuingAuthority?: FormValidationRule[]
  issueDate?: FormValidationRule[]
  expiryDate?: FormValidationRule[]
  validityPeriod?: FormValidationRule[]
  level?: FormValidationRule[]
  scope?: FormValidationRule[]
}

// ==================== UI组件类型 ====================

/**
 * 表格列配置
 */
export interface TableColumn {
  /** 属性名 */
  prop: string

  /** 列标题 */
  label: string

  /** 列宽度 */
  width?: number

  /** 最小宽度 */
  minWidth?: number

  /** 是否可排序 */
  sortable?: boolean

  /** 是否显示溢出提示 */
  showOverflowTooltip?: boolean

  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
}

/**
 * 搜索表单配置
 */
export interface SearchFormConfig {
  /** 是否显示高级搜索 */
  showAdvanced: boolean

  /** 搜索字段配置 */
  fields: SearchFieldConfig[]
}

/**
 * 搜索字段配置
 */
export interface SearchFieldConfig {
  /** 字段名 */
  field: string

  /** 字段标签 */
  label: string

  /** 字段类型 */
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'switch'

  /** 选项列表（用于select类型） */

  options?: Array<{ label: string; value: unknown }>

  /** 占位符 */
  placeholder?: string

  /** 是否在基础搜索中显示 */
  basic?: boolean
}
