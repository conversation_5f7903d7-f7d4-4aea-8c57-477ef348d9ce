/**
 * 编制管理相关类型定义
 */

// 编制规划审批状态
export enum EstablishmentApprovalStatus {
  DRAFT = 'DRAFT', // 草稿
  PENDING = 'PENDING', // 待审批
  APPROVED = 'APPROVED', // 已批准
  REJECTED = 'REJECTED' // 已驳回
}

// 编制类型
export enum EstablishmentType {
  TOTAL = 'TOTAL', // 总编制
  MANAGEMENT = 'MANAGEMENT', // 管理岗编制
  PROFESSIONAL = 'PROFESSIONAL', // 专业技术岗编制
  WORKER = 'WORKER', // 工勤岗编制
  TEACHER = 'TEACHER', // 教师岗编制
  ADMINISTRATIVE = 'ADMINISTRATIVE' // 行政岗编制
}

// 编制规划周期类型
export enum PlanCycleType {
  ANNUAL = 'ANNUAL', // 年度计划
  FIVE_YEAR = 'FIVE_YEAR', // 五年规划
  TEN_YEAR = 'TEN_YEAR', // 十年规划
  CUSTOM = 'CUSTOM' // 自定义周期
}

// 编制计算方式
export enum CalculationMethod {
  ROUND = 'ROUND', // 四舍五入
  FLOOR = 'FLOOR', // 向下取整
  CEIL = 'CEIL' // 向上取整
}

// 编制规划基本信息
export interface EstablishmentPlan {
  planId: string // 规划ID
  planYear: number // 规划年度
  planName: string // 规划名称
  cycleType: PlanCycleType // 周期类型
  startDate: string // 开始日期
  endDate: string // 结束日期
  institutionId?: string // 机构ID（空表示全校总量）
  institutionName?: string // 机构名称
  positionCategory?: EstablishmentType // 岗位类别（空表示机构总编制）
  approvedCount: number // 核定编制数
  budgetedCount?: number // 预算编制数
  effectiveDate: string // 生效日期
  approvalStatus: EstablishmentApprovalStatus // 审批状态
  staffingDocumentUrl?: string // 编制文件URL
  attachments?: string[] // 附件列表
  remark?: string // 备注
  creatorId: string // 创建人ID
  creatorName: string // 创建人姓名
  createTime: string // 创建时间
  updaterId?: string // 更新人ID
  updaterName?: string // 更新人姓名
  updateTime?: string // 更新时间
}

// 编制统计数据
export interface EstablishmentStatistics {
  institutionId: string // 机构ID
  institutionName: string // 机构名称
  positionCategory?: EstablishmentType // 岗位类别
  approvedCount: number // 核定编制数
  budgetedCount?: number // 预算编制数
  actualCount: number // 实际在编数
  overstaffedCount: number // 超编数
  understaffedCount: number // 缺编数
  utilizationRate: number // 编制使用率（百分比）
  vacancyRate: number // 空缺率（百分比）
  statisticsDate: string // 统计日期
}

// 编制统计快照
export interface EstablishmentSnapshot {
  snapshotId: string // 快照ID
  snapshotDate: string // 快照日期
  institutionId?: string // 机构ID
  institutionName?: string // 机构名称
  positionCategory?: EstablishmentType // 岗位类别
  approvedCount: number // 核定编制数
  actualCount: number // 实际在编数
  overstaffedCount: number // 超编数
  understaffedCount: number // 缺编数
  createTime: string // 创建时间
}

// 编制调整申请
export interface EstablishmentAdjustment {
  adjustmentId: string // 调整ID
  planId: string // 关联的规划ID
  institutionId?: string // 机构ID
  institutionName?: string // 机构名称
  positionCategory?: EstablishmentType // 岗位类别
  originalCount: number // 原编制数
  targetCount: number // 目标编制数
  adjustmentType: 'INCREASE' | 'DECREASE' | 'TRANSFER' // 调整类型
  adjustmentReason: string // 调整原因
  effectiveDate: string // 生效日期
  approvalStatus: EstablishmentApprovalStatus // 审批状态
  attachments?: string[] // 附件列表
  creatorId: string // 创建人ID
  creatorName: string // 创建人姓名
  createTime: string // 创建时间
}

// 编制划转记录
export interface EstablishmentTransfer {
  transferId: string // 划转ID
  fromInstitutionId: string // 源机构ID
  fromInstitutionName: string // 源机构名称
  toInstitutionId: string // 目标机构ID
  toInstitutionName: string // 目标机构名称
  positionCategory?: EstablishmentType // 岗位类别
  transferCount: number // 划转数量
  transferReason: string // 划转原因
  effectiveDate: string // 生效日期
  approvalStatus: EstablishmentApprovalStatus // 审批状态
  attachments?: string[] // 附件列表
  creatorId: string // 创建人ID
  creatorName: string // 创建人姓名
  createTime: string // 创建时间
}

// 编制变更日志
export interface EstablishmentChangeLog {
  logId: string // 日志ID
  planId?: string // 规划ID
  institutionId?: string // 机构ID
  institutionName?: string // 机构名称
  changeType: string // 变更类型
  changeContent: string // 变更内容

  oldValue?: unknown // 原值

  newValue?: unknown // 新值
  operatorId: string // 操作人ID
  operatorName: string // 操作人姓名
  operateTime: string // 操作时间
  remark?: string // 备注
}

// 编制查询参数
export interface EstablishmentQueryParams {
  planYear?: number // 规划年度
  institutionId?: string // 机构ID
  positionCategory?: EstablishmentType // 岗位类别
  approvalStatus?: EstablishmentApprovalStatus // 审批状态
  keyword?: string // 关键字搜索
  startDate?: string // 开始日期
  endDate?: string // 结束日期
  pageNum?: number // 页码
  pageSize?: number // 每页条数
}

// 编制统计查询参数
export interface EstablishmentStatQueryParams {
  statisticsDate?: string // 统计日期
  institutionIds?: string[] // 机构ID列表
  positionCategories?: EstablishmentType[] // 岗位类别列表
  showOverstaffed?: boolean // 只显示超编
  showUnderstaffed?: boolean // 只显示缺编
  utilizationRateMin?: number // 最低使用率
  utilizationRateMax?: number // 最高使用率
}

// 编制预警配置
export interface EstablishmentWarning {
  warningId: string // 预警ID
  institutionId?: string // 机构ID（空表示全局）
  positionCategory?: EstablishmentType // 岗位类别（空表示所有类别）
  warningType: 'OVERSTAFFED' | 'UNDERSTAFFED' | 'HIGH_UTILIZATION' | 'LOW_UTILIZATION' // 预警类型
  threshold: number // 阈值
  enabled: boolean // 是否启用
  notifyRoles: string[] // 通知角色列表
  notifyEmails?: string[] // 通知邮箱列表
  createTime: string // 创建时间
  updateTime?: string // 更新时间
}

// 编制预警规则（扩展版本）
export interface EstablishmentWarningRule {
  ruleId: string // 规则ID
  ruleName: string // 规则名称
  warningType: 'OVERSTAFFED' | 'UNDERSTAFFED' | 'HIGH_USAGE' | 'LOW_USAGE' | 'NEAR_LIMIT' // 预警类型
  scope: 'ALL' | 'SPECIFIC' | 'CATEGORY' // 适用范围
  institutionIds?: string[] // 机构ID列表（当scope为SPECIFIC时）
  positionCategories?: EstablishmentType[] // 岗位类别列表（当scope为CATEGORY时）
  metric: 'UTILIZATION_RATE' | 'ACTUAL_COUNT' | 'AVAILABLE_COUNT' // 监控指标
  operator: 'GT' | 'GTE' | 'LT' | 'LTE' | 'EQ' // 比较操作符
  threshold: number // 阈值
  warningLevel: number // 预警级别（1-3）
  notifyMethods: string[] // 通知方式
  notifyRoles: string[] // 通知角色
  suggestion?: string // 处理建议
  status: 'ACTIVE' | 'INACTIVE' // 状态
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 编制导入模板
export interface EstablishmentImportTemplate {
  templateId: string // 模板ID
  templateName: string // 模板名称
  templateType: 'PLAN' | 'ADJUSTMENT' // 模板类型
  fields: EstablishmentImportField[] // 字段列表

  exampleData?: unknown[] // 示例数据
  downloadUrl?: string // 下载链接
}

// 编制导入字段
export interface EstablishmentImportField {
  fieldName: string // 字段名
  fieldLabel: string // 字段标签
  fieldType: string // 字段类型
  required: boolean // 是否必填

  defaultValue?: unknown // 默认值
  validation?: string // 验证规则
  description?: string // 字段说明
}

// 编制趋势数据
export interface EstablishmentTrend {
  date: string // 日期
  institutionId?: string // 机构ID
  institutionName?: string // 机构名称
  positionCategory?: EstablishmentType // 岗位类别
  approvedCount: number // 核定编制数
  actualCount: number // 实际在编数
  utilizationRate: number // 使用率
}

// 编制对比数据
export interface EstablishmentComparison {
  institutionId: string // 机构ID
  institutionName: string // 机构名称
  data: {
    [key in EstablishmentType]?: {
      approved: number // 核定数
      actual: number // 实际数
      overstaffed: number // 超编数
      understaffed: number // 缺编数
    }
  }
}

// 导出编制统计配置选项
export const establishmentTypeOptions = [
  { label: '管理岗编制', value: EstablishmentType.MANAGEMENT },
  { label: '专业技术岗编制', value: EstablishmentType.PROFESSIONAL },
  { label: '工勤岗编制', value: EstablishmentType.WORKER },
  { label: '教师岗编制', value: EstablishmentType.TEACHER },
  { label: '行政岗编制', value: EstablishmentType.ADMINISTRATIVE }
]

export const approvalStatusOptions = [
  { label: '草稿', value: EstablishmentApprovalStatus.DRAFT },
  { label: '待审批', value: EstablishmentApprovalStatus.PENDING },
  { label: '已批准', value: EstablishmentApprovalStatus.APPROVED },
  { label: '已驳回', value: EstablishmentApprovalStatus.REJECTED }
]

export const planCycleTypeOptions = [
  { label: '年度计划', value: PlanCycleType.ANNUAL },
  { label: '五年规划', value: PlanCycleType.FIVE_YEAR },
  { label: '十年规划', value: PlanCycleType.TEN_YEAR },
  { label: '自定义周期', value: PlanCycleType.CUSTOM }
]
