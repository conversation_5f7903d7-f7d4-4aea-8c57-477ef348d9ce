/**
import type { SpringPageData } from '@/types/common/api'
 * 人才荣誉管理模块 TypeScript 类型定义
 *
 * 此文件定义了与后端 TalentHonorsService 完全兼容的 TypeScript 类型
 * 确保前后端数据结构100%匹配
 *
 * <AUTHOR>
 * @since 1.0.0
 */

// ==================== 枚举类型定义 ====================

/**
 * 荣誉等级枚举
 */
export enum HonorLevel {
  NATIONAL = 'NATIONAL', // 国家级
  PROVINCIAL = 'PROVINCIAL', // 省部级
  MUNICIPAL = 'MUNICIPAL', // 市厅级
  SCHOOL = 'SCHOOL', // 学校级
  DEPARTMENT = 'DEPARTMENT' // 院系级
}

/**
 * 排序方向枚举
 */
export enum SortDirection {
  ASC = 'ASC', // 升序
  DESC = 'DESC' // 降序
}

// ==================== 基础实体类型 ====================

/**
 * 人才荣誉实体类型（与后端 TalentHonors 实体完全匹配）
 */
export interface TalentHonor {
  /** 主键ID */
  id?: string

  /** 员工ID */
  employeeId: string

  /** 员工姓名（冗余字段，用于显示） */
  employeeName?: string

  /** 荣誉名称 */
  name: string

  /** 荣誉等级 */
  level: HonorLevel

  /** 荣誉等级名称（用于显示） */
  levelName?: string

  /** 颁发单位 */
  issuingUnit?: string

  /** 获奖时间 */
  acquireTime: string

  /** 排名 */
  ranking?: number

  /** 描述 */
  description?: string

  /** 证书编号 */
  certificateNumber?: string

  /** 证书文档URL */
  documentUrl?: string

  /** 创建时间 */
  createTime?: string

  /** 创建人 */
  createBy?: string

  /** 更新时间 */
  updateTime?: string

  /** 更新人 */
  updateBy?: string

  /** 是否删除 */
  deleted?: boolean
}

// ==================== 请求类型定义 ====================

/**
 * 人才荣誉创建请求类型（与后端 TalentHonorsCreateRequest 完全匹配）
 */
export interface TalentHonorCreateRequest {
  /** 员工ID */
  employeeId: string

  /** 荣誉名称 */
  name: string

  /** 荣誉等级 */
  level: HonorLevel

  /** 颁发单位 */
  issuingUnit?: string

  /** 获奖时间 */
  acquireTime: string

  /** 排名 */
  ranking?: number

  /** 描述 */
  description?: string

  /** 证书编号 */
  certificateNumber?: string

  /** 证书文档URL */
  documentUrl?: string
}

/**
 * 人才荣誉更新请求类型（与后端 TalentHonorsUpdateRequest 完全匹配）
 */
export interface TalentHonorUpdateRequest {
  /** 荣誉名称 */
  name?: string

  /** 荣誉等级 */
  level?: HonorLevel

  /** 颁发单位 */
  issuingUnit?: string

  /** 获奖时间 */
  acquireTime?: string

  /** 排名 */
  ranking?: number

  /** 描述 */
  description?: string

  /** 证书编号 */
  certificateNumber?: string

  /** 证书文档URL */
  documentUrl?: string
}

/**
 * 人才荣誉查询请求类型（与后端 TalentHonorsQueryRequest 完全匹配）
 */
export interface TalentHonorQueryRequest {
  /** 页码（从0开始） */
  page?: number

  /** 页大小 */
  size?: number

  /** 关键词搜索 */
  keyword?: string

  /** 员工ID */
  employeeId?: string

  /** 员工ID列表 */
  employeeIds?: string[]

  /** 荣誉名称 */
  name?: string

  /** 荣誉等级 */
  level?: HonorLevel

  /** 荣誉等级列表 */
  levels?: HonorLevel[]

  /** 颁发单位 */
  issuingUnit?: string

  /** 获奖时间开始 */
  acquireTimeStart?: string

  /** 获奖时间结束 */
  acquireTimeEnd?: string

  /** 最小排名 */
  minRanking?: number

  /** 最大排名 */
  maxRanking?: number

  /** 是否有证书文档 */
  hasDocument?: boolean

  /** 排序字段 */
  sortBy?: string

  /** 排序方向 */
  sortDirection?: SortDirection
}

// ==================== 响应类型定义 ====================

/**
 * 分页响应类型（与后端 Page<T> 完全匹配）
 */
// 使用通用的SpringPageData类型
export type PageResponse<T> = SpringPageData<T>

/**
 * 统一响应结果类型（与后端 Result<T> 完全匹配）
 */
export interface Result<T> {
  /** 响应码 */
  code: number

  /** 响应消息 */
  message?: string

  /** 响应数据 */
  data?: T

  /** 时间戳 */
  timestamp?: number
}

// ==================== 统计分析类型 ====================

/**
 * 人才荣誉统计信息类型
 */
export interface TalentHonorStatistics {
  /** 总数量 */
  totalCount: number

  /** 等级分布 */
  levelDistribution: Record<string, number>

  /** 年度趋势 */
  yearlyTrend: YearlyTrendItem[]

  /** 热门颁发单位 */
  topIssuingUnits: IssuingUnitStats[]

  /** 员工荣誉排行 */
  employeeRanking: EmployeeHonorRanking[]
}

/**
 * 年度趋势项
 */
export interface YearlyTrendItem {
  /** 年份 */
  year: number

  /** 数量 */
  count: number
}

/**
 * 颁发单位统计
 */
export interface IssuingUnitStats {
  /** 颁发单位 */
  unit: string

  /** 数量 */
  count: number
}

/**
 * 员工荣誉排行
 */
export interface EmployeeHonorRanking {
  /** 员工ID */
  employeeId: string

  /** 员工姓名 */
  employeeName: string

  /** 荣誉数量 */
  honorCount: number
}

// ==================== 批量操作类型 ====================

/**
 * 批量删除请求
 */
export interface BatchDeleteRequest {
  /** ID列表 */
  ids: string[]
}

/**
 * 批量导入结果
 */
export interface ImportResult {
  /** 成功数量 */
  successCount: number

  /** 失败数量 */
  failureCount: number

  /** 错误信息列表 */
  errors: string[]
}

// ==================== 表单验证类型 ====================

/**
 * 表单验证规则类型
 */
export interface FormValidationRule {
  /** 是否必填 */
  required?: boolean

  /** 错误消息 */
  message?: string

  /** 触发方式 */
  trigger?: 'blur' | 'change'

  /** 最小长度 */
  min?: number

  /** 最大长度 */
  max?: number

  /** 正则表达式 */
  pattern?: RegExp

  /** 自定义验证函数 */

  validator?: (rule: unknown, value: unknown, callback: unknown) => void
}

/**
 * 表单验证规则集合
 */
export interface TalentHonorFormRules {
  employeeId: FormValidationRule[]
  name: FormValidationRule[]
  level: FormValidationRule[]
  acquireTime: FormValidationRule[]
  ranking?: FormValidationRule[]
  certificateNumber?: FormValidationRule[]
}

// ==================== UI组件类型 ====================

/**
 * 表格列配置
 */
export interface TableColumn {
  /** 属性名 */
  prop: string

  /** 列标题 */
  label: string

  /** 列宽度 */
  width?: number

  /** 最小宽度 */
  minWidth?: number

  /** 是否可排序 */
  sortable?: boolean

  /** 是否显示溢出提示 */
  showOverflowTooltip?: boolean

  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
}

/**
 * 搜索表单配置
 */
export interface SearchFormConfig {
  /** 是否显示高级搜索 */
  showAdvanced: boolean

  /** 搜索字段配置 */
  fields: SearchFieldConfig[]
}

/**
 * 搜索字段配置
 */
export interface SearchFieldConfig {
  /** 字段名 */
  field: string

  /** 字段标签 */
  label: string

  /** 字段类型 */
  type: 'input' | 'select' | 'date' | 'daterange' | 'number'

  /** 选项列表（用于select类型） */

  options?: Array<{ label: string; value: unknown }>

  /** 占位符 */
  placeholder?: string

  /** 是否在基础搜索中显示 */
  basic?: boolean
}
