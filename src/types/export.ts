// 导出功能相关类型定义

export interface ExportConfig {
  format: 'excel' | 'pdf' | 'image' | 'csv'

  // Excel相关配置
  excelType?: 'xlsx' | 'xls'
  excelOptions?: string[]

  // PDF相关配置
  pdfOptions?: {
    pageSize: 'a4' | 'a3' | 'letter' | 'custom'
    orientation: 'portrait' | 'landscape'
    customSize?: {
      width: number
      height: number
    }
  }
  pdfFeatures?: string[]

  // 图片相关配置
  imageType?: 'png' | 'jpg' | 'svg'
  imageQuality?: number
  imageDPI?: number

  // 数据范围
  dataRange: 'current' | 'all' | 'selected' | 'custom'
  customRange?: {
    start: number
    end: number
  }

  // 字段选择
  selectedFields: string[]

  // 其他选项
  addTimestamp?: boolean
  compress?: boolean
  sendEmail?: boolean
  email?: string
  watermark?: WatermarkConfig
}

export interface WatermarkConfig {
  enabled: boolean
  text?: string
  type: 'text' | 'image'
  position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  opacity?: number
  rotation?: number
  fontSize?: number
  color?: string
  imageUrl?: string
}

export interface ExportTask {
  id: string
  reportId: string
  config: ExportConfig
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  totalRows?: number
  processedRows?: number
  startTime: Date
  endTime?: Date
  fileUrl?: string
  fileSize?: number
  error?: string
  userId: string
  userName?: string
}

export interface ExportResult {
  success: boolean
  taskId?: string
  fileUrl?: string
  fileName?: string
  fileSize?: number
  format: string
  rowCount?: number
  error?: string
}

export interface ExportProgress {
  taskId: string
  status: ExportTask['status']
  progress: number
  currentRow?: number
  totalRows?: number
  message?: string
  error?: string
}

export interface ExportTemplate {
  id: string
  name: string
  description?: string
  format: ExportConfig['format']
  config: Partial<ExportConfig>
  isDefault?: boolean
  createdBy?: string
  createdAt?: Date
}

export interface ExportPermission {
  canExport: boolean
  maxRows?: number
  allowedFormats?: string[]
  requireApproval?: boolean
  approvalThreshold?: number
}

export interface ExportAuditLog {
  id: string
  reportId: string
  reportName?: string
  userId: string
  userName?: string
  exportTime: Date
  format: string
  rowCount: number
  fileSize?: number
  ipAddress?: string
  userAgent?: string
  success: boolean
  error?: string
}

export interface ExportSchedule {
  id: string
  name: string
  reportId: string
  config: ExportConfig
  schedule: string // cron expression
  enabled: boolean
  recipients?: string[]
  lastRun?: Date
  nextRun?: Date
  createdBy?: string
  createdAt?: Date
}

export interface ExportService {
  // 同步导出

  exportToExcel(data: unknown[], config: ExportConfig): Promise<void>

  exportToPDF(data: unknown[], config: ExportConfig): Promise<void>

  exportToImage(data: unknown[], config: ExportConfig): Promise<void>

  exportToCSV(data: unknown[], config: ExportConfig): Promise<void>

  // 异步导出
  submitAsyncExport(params: AsyncExportParams): Promise<string>
  getExportStatus(taskId: string): Promise<ExportProgress>
  cancelExportTask(taskId: string): Promise<void>
  downloadExportFile(taskId: string): Promise<void>

  // 模板管理
  getExportTemplates(): Promise<ExportTemplate[]>
  saveExportTemplate(template: Partial<ExportTemplate>): Promise<ExportTemplate>
  deleteExportTemplate(templateId: string): Promise<void>

  // 权限检查
  checkExportPermission(reportId: string): Promise<ExportPermission>

  // 审计日志
  getExportHistory(params: ExportHistoryParams): Promise<ExportAuditLog[]>
}

export interface AsyncExportParams {
  reportId: string
  config: ExportConfig

  data?: unknown[]

  filters?: unknown
}

export interface ExportHistoryParams {
  reportId?: string
  userId?: string
  startDate?: Date
  endDate?: Date
  format?: string
  page?: number
  pageSize?: number
}

export interface ExcelStyle {
  font?: {
    name?: string
    size?: number
    color?: string
    bold?: boolean
    italic?: boolean
    underline?: boolean
  }
  fill?: {
    type?: 'pattern' | 'gradient'
    pattern?: string
    fgColor?: string
    bgColor?: string
  }
  border?: {
    top?: BorderStyle
    right?: BorderStyle
    bottom?: BorderStyle
    left?: BorderStyle
  }
  alignment?: {
    horizontal?: 'left' | 'center' | 'right'
    vertical?: 'top' | 'middle' | 'bottom'
    wrapText?: boolean
  }
}

export interface BorderStyle {
  style?: 'thin' | 'medium' | 'thick' | 'dotted' | 'dashed'
  color?: string
}

export interface PDFOptions {
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
  header?: {
    height: number
    content: string | ((pageNumber: number, pageCount: number) => string)
  }
  footer?: {
    height: number
    content: string | ((pageNumber: number, pageCount: number) => string)
  }
  pageNumbers?: boolean
  compress?: boolean
  encryption?: {
    userPassword?: string
    ownerPassword?: string
    permissions?: PDFPermissions
  }
}

export interface PDFPermissions {
  printing?: boolean
  modifying?: boolean
  copying?: boolean
  annotating?: boolean
}
