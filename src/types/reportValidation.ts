// 报表校验相关类型定义

export interface ValidationRule {
  id: string
  name: string
  description?: string
  type: 'formula' | 'range' | 'consistency' | 'format' | 'business'
  reportId: string
  expression: string
  errorMessage: string
  severity: 'error' | 'warning' | 'info'
  isEnabled: boolean
  metadata?: RuleMetadata
  createdBy?: string
  createdAt?: Date
  updatedBy?: string
  updatedAt?: Date
}

export interface RuleMetadata {
  fields?: string[]
  functions?: string[]
  parameters?: Record<string, unknown>
  dependencies?: string[]
  rangeConfig?: RangeConfig
  consistencyConfig?: ConsistencyConfig
  formatConfig?: FormatConfig
  businessConfig?: BusinessConfig
}

export interface RangeConfig {
  field: string
  rangeType: 'between' | 'min' | 'max' | 'percentage' | 'dynamic'
  minValue?: number
  maxValue?: number
  baseField?: string
  minPercentage?: string
  maxPercentage?: string
  referenceField?: string
  deviation?: string
  deviationType?: 'value' | 'percentage'
  inclusive?: boolean
}

export interface ConsistencyConfig {
  sourceTable?: string
  sourceField?: string
  targetTable?: string
  targetField?: string
  joinCondition?: string
  aggregateFunction?: string
}

export interface FormatConfig {
  field: string
  formatType: 'email' | 'phone' | 'idcard' | 'date' | 'custom'
  pattern?: string
  dateFormat?: string
  allowEmpty?: boolean
}

export interface BusinessConfig {
  ruleType: string
  parameters: Record<string, unknown>
  customLogic?: string
}

export interface ValidationResult {
  ruleId: string
  ruleName: string
  status: 'passed' | 'failed' | 'skipped'
  message?: string
  affectedData?: AffectedData[]
  executionTime?: number
  timestamp: Date
}

export interface AffectedData {
  row?: number
  column?: string

  value?: unknown

  expectedValue?: unknown
  path?: string
  details?: Record<string, unknown>
}

export interface ValidationReport {
  reportId: string
  timestamp: Date
  totalRules: number
  executedRules: number
  passedRules: number
  failedRules: number
  skippedRules: number
  results: ValidationResult[]
  summary: ValidationSummary
}

export interface ValidationSummary {
  errors: number
  warnings: number
  infos: number
  executionTime: number
  dataQualityScore?: number
  recommendations?: string[]
}

export interface RuleTemplate {
  id: string
  name: string
  description: string
  category: string
  type: ValidationRule['type']
  config: Partial<ValidationRule>
  tags?: string[]
}

export interface ValidationEngine {
  validateReport(reportId: string, data: unknown): Promise<ValidationReport>

  validateRule(rule: ValidationRule, data: unknown): Promise<ValidationResult>
  parseExpression(expression: string): ExpressionAST

  evaluateExpression(ast: ExpressionAST, context: unknown): unknown
}

export interface ExpressionAST {
  type: 'binary' | 'unary' | 'literal' | 'identifier' | 'function' | 'group'
  operator?: string
  left?: ExpressionAST
  right?: ExpressionAST

  value?: unknown
  name?: string
  arguments?: ExpressionAST[]
}

export interface ValidationContext {
  data: unknown
  fields: Map<string, unknown>
  functions: Map<string, Function>
  variables: Map<string, unknown>

  metadata?: unknown
}

export interface RuleGroup {
  id: string
  name: string
  description?: string
  rules: ValidationRule[]
  executeMode: 'parallel' | 'sequential'
  stopOnError?: boolean
  condition?: string
}

export interface ValidationSchedule {
  id: string
  reportId: string
  ruleIds: string[]
  schedule: string // cron expression
  isEnabled: boolean
  notifyOnError?: boolean
  notifyEmails?: string[]
}
