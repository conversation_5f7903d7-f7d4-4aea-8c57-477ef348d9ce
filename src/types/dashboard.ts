// 统计仪表板相关类型定义

// 时间范围枚举
export enum TimeRange {
  TODAY = 'TODAY',
  YESTERDAY = 'YESTERDAY',
  LAST_7_DAYS = 'LAST_7_DAYS',
  LAST_30_DAYS = 'LAST_30_DAYS',
  LAST_90_DAYS = 'LAST_90_DAYS',
  THIS_MONTH = 'THIS_MONTH',
  LAST_MONTH = 'LAST_MONTH',
  THIS_QUARTER = 'THIS_QUARTER',
  LAST_QUARTER = 'LAST_QUARTER',
  THIS_YEAR = 'THIS_YEAR',
  LAST_YEAR = 'LAST_YEAR',
  CUSTOM = 'CUSTOM'
}

// 图表类型枚举
export enum ChartType {
  LINE = 'LINE',
  BAR = 'BAR',
  PIE = 'PIE',
  DOUGHNUT = 'DOUGHNUT',
  AREA = 'AREA',
  SCATTER = 'SCATTER',
  HEATMAP = 'HEATMAP',
  GAUGE = 'GAUGE',
  RADAR = 'RADAR'
}

// 数据类型枚举
export enum DataType {
  INSTITUTION_CHANGE = 'INSTITUTION_CHANGE',
  AD_HOC_INSTITUTION = 'AD_HOC_INSTITUTION',
  COMBINED = 'COMBINED'
}

// 统计维度枚举
export enum StatisticsDimension {
  TIME = 'TIME',
  DEPARTMENT = 'DEPARTMENT',
  TYPE = 'TYPE',
  STATUS = 'STATUS',
  PRIORITY = 'PRIORITY',
  USER = 'USER'
}

// 统计卡片接口
export interface StatisticsCard {
  id: string
  title: string
  value: number | string
  unit?: string
  trend?: number // 趋势百分比，正数为上升，负数为下降
  trendLabel?: string
  icon?: string
  color?: string
  backgroundColor?: string
  clickable?: boolean
  loading?: boolean
}

// 图表配置接口
export interface ChartConfig {
  id: string
  title: string
  type: ChartType
  width?: number | string
  height?: number | string
  dataSource: string
  refreshInterval?: number // 刷新间隔（秒）
  interactive?: boolean
  exportable?: boolean
  drillDown?: boolean
}

// 图表数据接口
export interface ChartData {
  labels: string[]
  datasets: ChartDataset[]
  total?: number
  summary?: string
}

// 图表数据集接口
export interface ChartDataset {
  label: string
  data: number[]
  backgroundColor?: string | string[]
  borderColor?: string | string[]
  borderWidth?: number
  fill?: boolean
  tension?: number
}

// 仪表板配置接口
export interface DashboardConfig {
  id: string
  name: string
  description?: string
  layout: DashboardLayout
  cards: StatisticsCard[]
  charts: ChartConfig[]
  filters: FilterConfig[]
  refreshInterval?: number
  autoRefresh?: boolean
  isDefault?: boolean
  isPublic?: boolean
  createTime?: string
  updateTime?: string
  createBy?: string
}

// 仪表板布局接口
export interface DashboardLayout {
  rows: LayoutRow[]
  responsive?: boolean
  gridSize?: number
}

// 布局行接口
export interface LayoutRow {
  id: string
  height?: number
  columns: LayoutColumn[]
}

// 布局列接口
export interface LayoutColumn {
  id: string
  span: number // 1-24
  component: ComponentConfig
}

// 组件配置接口
export interface ComponentConfig {
  type: 'card' | 'chart' | 'table' | 'custom'
  id: string
  props?: Record<string, unknown>
}

// 筛选配置接口
export interface FilterConfig {
  id: string
  label: string
  type: 'select' | 'daterange' | 'input' | 'checkbox'
  options?: FilterOption[]

  defaultValue?: unknown
  required?: boolean
}

// 筛选选项接口
export interface FilterOption {
  label: string

  value: unknown
  disabled?: boolean
}

// 仪表板数据接口
export interface DashboardData {
  overview: OverviewData
  institutionChange: InstitutionChangeData
  adHocInstitution: AdHocInstitutionData
  combined: CombinedData
  alerts: AlertData[]
  lastUpdateTime: string
}

// 概览数据接口
export interface OverviewData {
  totalInstitutions: number
  activeChanges: number
  pendingApprovals: number
  expiringInstitutions: number
  thisMonthChanges: number
  thisMonthCreated: number
  averageProcessingTime: number
  systemHealth: number
}

// 机构变更数据接口
export interface InstitutionChangeData {
  totalChanges: number
  changesByType: TypeStatistics[]
  changesByStatus: StatusStatistics[]
  changesTrend: TrendData[]
  departmentRanking: DepartmentStatistics[]
  urgentChanges: number
  averageApprovalTime: number
}

// 临时机构数据接口
export interface AdHocInstitutionData {
  totalInstitutions: number
  institutionsByType: TypeStatistics[]
  institutionsByStatus: StatusStatistics[]
  expirationTrend: TrendData[]
  departmentDistribution: DepartmentStatistics[]
  averageDuration: number
  totalBudget: number
}

// 综合数据接口
export interface CombinedData {
  departmentActivity: DepartmentActivity[]
  timelineComparison: TimelineComparison[]
  correlationAnalysis: CorrelationData[]
  performanceMetrics: PerformanceMetrics
}

// 类型统计接口
export interface TypeStatistics {
  type: string
  count: number
  percentage: number
  trend?: number
}

// 状态统计接口
export interface StatusStatistics {
  status: string
  count: number
  percentage: number
  color?: string
}

// 趋势数据接口
export interface TrendData {
  date: string
  value: number
  label?: string
  category?: string
}

// 部门统计接口
export interface DepartmentStatistics {
  department: string
  count: number
  percentage: number
  rank: number
  trend?: number
}

// 部门活跃度接口
export interface DepartmentActivity {
  department: string
  changes: number
  institutions: number
  activity: number // 活跃度评分
  lastActivity: string
}

// 时间线对比接口
export interface TimelineComparison {
  date: string
  current: number
  previous: number
  growth: number
}

// 关联分析接口
export interface CorrelationData {
  metric1: string
  metric2: string
  correlation: number
  significance: number
}

// 性能指标接口
export interface PerformanceMetrics {
  averageResponseTime: number
  systemUptime: number
  dataAccuracy: number
  userSatisfaction: number
}

// 预警数据接口
export interface AlertData {
  id: string
  type: 'warning' | 'error' | 'info'
  title: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  source: string
  createTime: string
  acknowledged?: boolean
  actionRequired?: boolean
  relatedId?: string
}

// 查询参数接口
export interface DashboardQueryParams {
  timeRange: TimeRange
  startDate?: string
  endDate?: string
  departments?: string[]
  dataTypes?: DataType[]
  includeSubDepartments?: boolean
  groupBy?: StatisticsDimension
  limit?: number
  offset?: number
}

// 导出配置接口
export interface ExportConfig {
  format: 'excel' | 'pdf' | 'png' | 'svg'
  includeCharts: boolean
  includeData: boolean
  template?: string
  fileName?: string
  emailTo?: string[]
}

// 实时数据接口
export interface RealTimeData {
  timestamp: string
  metrics: Record<string, number>
  events: RealTimeEvent[]
}

// 实时事件接口
export interface RealTimeEvent {
  id: string
  type: string
  message: string
  timestamp: string
  severity: 'info' | 'warning' | 'error'
}

// 时间范围选项
export const timeRangeOptions = [
  { label: '今天', value: TimeRange.TODAY },
  { label: '昨天', value: TimeRange.YESTERDAY },
  { label: '最近7天', value: TimeRange.LAST_7_DAYS },
  { label: '最近30天', value: TimeRange.LAST_30_DAYS },
  { label: '最近90天', value: TimeRange.LAST_90_DAYS },
  { label: '本月', value: TimeRange.THIS_MONTH },
  { label: '上月', value: TimeRange.LAST_MONTH },
  { label: '本季度', value: TimeRange.THIS_QUARTER },
  { label: '上季度', value: TimeRange.LAST_QUARTER },
  { label: '今年', value: TimeRange.THIS_YEAR },
  { label: '去年', value: TimeRange.LAST_YEAR },
  { label: '自定义', value: TimeRange.CUSTOM }
]

// 图表类型选项
export const chartTypeOptions = [
  { label: '折线图', value: ChartType.LINE, icon: 'line-chart' },
  { label: '柱状图', value: ChartType.BAR, icon: 'bar-chart' },
  { label: '饼图', value: ChartType.PIE, icon: 'pie-chart' },
  { label: '环形图', value: ChartType.DOUGHNUT, icon: 'doughnut-chart' },
  { label: '面积图', value: ChartType.AREA, icon: 'area-chart' },
  { label: '散点图', value: ChartType.SCATTER, icon: 'scatter-chart' },
  { label: '热力图', value: ChartType.HEATMAP, icon: 'heatmap' },
  { label: '仪表盘', value: ChartType.GAUGE, icon: 'gauge' },
  { label: '雷达图', value: ChartType.RADAR, icon: 'radar-chart' }
]

// 数据类型选项
export const dataTypeOptions = [
  { label: '机构变更', value: DataType.INSTITUTION_CHANGE },
  { label: '临时机构', value: DataType.AD_HOC_INSTITUTION },
  { label: '综合数据', value: DataType.COMBINED }
]

// 统计维度选项
export const statisticsDimensionOptions = [
  { label: '时间维度', value: StatisticsDimension.TIME },
  { label: '部门维度', value: StatisticsDimension.DEPARTMENT },
  { label: '类型维度', value: StatisticsDimension.TYPE },
  { label: '状态维度', value: StatisticsDimension.STATUS },
  { label: '优先级维度', value: StatisticsDimension.PRIORITY },
  { label: '用户维度', value: StatisticsDimension.USER }
]
