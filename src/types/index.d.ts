/**
 * 类型定义统一导出
 */

// API相关类型
export * from './api'

// 实体类型
export * from './entities/user'
export * from './entities/organization'
export * from './entities/employee'

// 通用类型
export * from './common'

// Vue相关类型扩展
declare module 'vue' {
  export interface GlobalProperties {
    /** Element Plus Message */
    $message: (typeof import('element-plus'))['ElMessage']
    /** Element Plus MessageBox */
    $msgbox: (typeof import('element-plus'))['ElMessageBox']
    /** Element Plus Notification */
    $notify: (typeof import('element-plus'))['ElNotification']
    /** Element Plus Loading */
    $loading: (typeof import('element-plus'))['ElLoading']
  }
}

// 全局Window扩展
declare global {
  interface Window {
    /** 错误监控实例 */
    __ERROR_MONITOR__?: {
      report: (error: unknown, context?: string) => void
    }
    /** 应用配置 */
    __APP_CONFIG__?: {
      apiBaseUrl: string
      version: string
      env: string
    }
  }
}

// 环境变量类型
interface ImportMetaEnv {
  /** API基础路径 */
  readonly VITE_API_BASE_URL: string
  /** 应用标题 */
  readonly VITE_APP_TITLE: string
  /** 是否启用Mock */
  readonly VITE_ENABLE_MOCK: string
  /** 是否启用加密 */
  readonly VITE_ENABLE_ENCRYPTION: string
  /** AES密钥 */
  readonly VITE_AES_KEY: string
  /** AES向量 */
  readonly VITE_AES_IV: string
  /** RSA公钥 */
  readonly VITE_RSA_PUBLIC_KEY: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 确保是模块
export {}
