// 机构变更日志相关类型定义
import type { SpringPageData } from '@/types/common/api'

// 变更类型枚举
export enum ChangeType {
  ESTABLISH = 'ESTABLISH',
  MODIFY = 'MODIFY',
  TRANSFER = 'TRANSFER',
  REVOKE = 'REVOKE',
  MERGE = 'MERGE'
}

// 审批状态枚举
export enum ApprovalStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

// 优先级枚举
export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// 风险等级枚举
export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// 机构变更日志接口
export interface InstitutionChangeLog {
  id: number
  changeCode: string
  organizationId: number
  organizationName?: string
  changeType: ChangeType
  changeReason: string
  changeContent: string
  effectiveDate: string
  applicantId: string
  applicantName: string
  applicantDepartment: string
  approvalStatus: ApprovalStatus
  applyDate: string
  approvalDate?: string
  approvalBy?: string
  approvalComment?: string
  attachmentJson?: string
  remark?: string
  priority: Priority
  urgent: boolean
  expectedCompletionDate?: string
  impactScope?: string
  riskLevel: RiskLevel
  riskDescription?: string
  createTime: string
  createBy?: string
  updateTime?: string
  updateBy?: string
}

// 创建机构变更日志请求
export interface InstitutionChangeLogCreateRequest {
  organizationId: number
  changeType: ChangeType
  changeReason: string
  changeContent: string
  effectiveDate: string
  applicantName: string
  applicantDepartment: string
  attachmentJson?: string
  remark?: string
  priority: Priority
  urgent: boolean
  expectedCompletionDate?: string
  impactScope?: string
  riskLevel: RiskLevel
  riskDescription?: string
}

// 更新机构变更日志请求
export interface InstitutionChangeLogUpdateRequest {
  changeType?: ChangeType
  changeReason?: string
  changeContent?: string
  effectiveDate?: string
  applicantName?: string
  applicantDepartment?: string
  attachmentJson?: string
  remark?: string
  priority?: Priority
  urgent?: boolean
  expectedCompletionDate?: string
  impactScope?: string
  riskLevel?: RiskLevel
  riskDescription?: string
}

// 查询机构变更日志请求
export interface InstitutionChangeLogQueryRequest {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
  changeCode?: string
  organizationId?: string
  changeType?: ChangeType
  approvalStatus?: ApprovalStatus
  applicantId?: string
  applyDateStart?: string
  applyDateEnd?: string
  effectiveDateStart?: string
  effectiveDateEnd?: string
  approvalDateStart?: string
  approvalDateEnd?: string
  keyword?: string
  priority?: Priority
  urgent?: boolean
  riskLevel?: RiskLevel
  applicantDepartment?: string
  includeDeleted?: boolean
}

// 审批请求
export interface ApprovalRequest {
  approved: boolean
  comment: string
}

// 统计概览
export interface ChangeLogStatistics {
  totalCount: number
  draftCount: number
  pendingCount: number
  approvedCount: number
  rejectedCount: number
  urgentCount: number
  highRiskCount: number
  thisMonthCount: number
  thisYearCount: number
}

// 分类统计
export interface ChangeLogCategoryStats {
  changeType: ChangeType
  count: number
  percentage: number
}

// 趋势分析
export interface ChangeLogTrend {
  date: string
  count: number
  approvedCount: number
  rejectedCount: number
}

// 分页响应（复用组织管理的类型）
// 使用通用的SpringPageData类型
export type PageResponse<T> = SpringPageData<T>

// 变更类型选项
export const changeTypeOptions = [
  { label: '设立', value: ChangeType.ESTABLISH },
  { label: '修改', value: ChangeType.MODIFY },
  { label: '划转', value: ChangeType.TRANSFER },
  { label: '撤销', value: ChangeType.REVOKE },
  { label: '合并', value: ChangeType.MERGE }
]

// 审批状态选项
export const approvalStatusOptions = [
  { label: '草稿', value: ApprovalStatus.DRAFT },
  { label: '待审批', value: ApprovalStatus.PENDING },
  { label: '已批准', value: ApprovalStatus.APPROVED },
  { label: '已拒绝', value: ApprovalStatus.REJECTED }
]

// 优先级选项
export const priorityOptions = [
  { label: '低', value: Priority.LOW },
  { label: '中', value: Priority.MEDIUM },
  { label: '高', value: Priority.HIGH },
  { label: '紧急', value: Priority.URGENT }
]

// 风险等级选项
export const riskLevelOptions = [
  { label: '低风险', value: RiskLevel.LOW },
  { label: '中风险', value: RiskLevel.MEDIUM },
  { label: '高风险', value: RiskLevel.HIGH },
  { label: '严重风险', value: RiskLevel.CRITICAL }
]

// 审批状态颜色映射
export const approvalStatusColors = {
  [ApprovalStatus.DRAFT]: 'info',
  [ApprovalStatus.PENDING]: 'warning',
  [ApprovalStatus.APPROVED]: 'success',
  [ApprovalStatus.REJECTED]: 'danger'
}

// 优先级颜色映射
export const priorityColors = {
  [Priority.LOW]: 'info',
  [Priority.MEDIUM]: 'primary',
  [Priority.HIGH]: 'warning',
  [Priority.URGENT]: 'danger'
}

// 风险等级颜色映射
export const riskLevelColors = {
  [RiskLevel.LOW]: 'success',
  [RiskLevel.MEDIUM]: 'warning',
  [RiskLevel.HIGH]: 'danger',
  [RiskLevel.CRITICAL]: 'danger'
}
