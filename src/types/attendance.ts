// 考勤相关类型定义

// 考勤设备接口
export interface AttendanceDevice {
  id: string
  deviceName: string
  deviceType: string
  manufacturer: string
  model: string
  serialNumber: string
  ipAddress: string
  port: number
  username?: string
  password?: string
  location: string
  description?: string
  status: 'online' | 'offline' | 'syncing' | 'error'
  lastSyncTime: string
  nextSyncTime?: string
  syncCount: number
  errorCount: number
  firmware: string
  capacity: number
  recordCount: number
  lastActivity: string
  networkStatus: string
  batteryLevel?: number
  syncConfig: {
    syncInterval: number
    syncTimeRange: [string, string]
    autoSync: boolean
    retryCount: number
    retryInterval: number
    conflictStrategy: 'skip' | 'override' | 'merge'
    deduplication: boolean
    dataValidation: boolean
    notifications: string[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 考勤记录接口
export interface AttendanceRecord {
  id: string
  recordId: string
  employeeId: string
  employeeNo: string
  employeeName: string
  departmentId: string
  departmentName: string
  recordTime: string
  recordType: string
  verifyMode: string
  workCode: string
  deviceId: string
  deviceName: string
  deviceSN: string
  direction: 'in' | 'out'
  temperature?: number
  photoPath?: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  syncStatus: 'pending' | 'synced' | 'failed'
  syncTime?: string
  errorMessage?: string
  createdAt: string
  updatedAt: string
}

// 考勤同步任务接口
export interface AttendanceSyncTask {
  id: string
  taskId: string
  deviceId: string
  deviceName: string
  syncType: 'full' | 'incremental' | 'realtime' | 'manual' | 'scheduled'
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled'
  priority: 'high' | 'normal' | 'low'
  startTime: string
  endTime?: string
  duration?: number
  progress: number
  totalRecords: number
  processedRecords: number
  successRecords: number
  failedRecords: number
  duplicateRecords: number
  errorMessage?: string
  errorLogs?: {
    recordId: string
    employeeNo: string
    errorType: string
    errorMessage: string
  }[]
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 考勤同步配置接口
export interface AttendanceSyncConfig {
  id: string
  deviceId: string
  syncInterval: number
  syncTimeRange: [string, string]
  autoSync: boolean
  retryCount: number
  retryInterval: number
  conflictStrategy: 'skip' | 'override' | 'merge'
  deduplication: boolean
  dataValidation: boolean
  notifications: string[]
  schedules: {
    scheduleId: string
    scheduleName: string
    scheduleType: 'daily' | 'weekly' | 'monthly'
    scheduleTime: string
    enabled: boolean
    lastExecuted: string
    nextExecution: string
  }[]
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 考勤同步日志接口
export interface AttendanceSyncLog {
  id: string
  logId: string
  deviceId: string
  deviceName: string
  taskId?: string
  logLevel: 'info' | 'warning' | 'error'
  logTime: string
  operation: string
  message: string

  details: unknown
  duration?: number
  recordCount?: number
  createdAt: string
}

// 考勤设备统计接口
export interface AttendanceDeviceStatistics {
  totalDevices: number
  onlineDevices: number
  offlineDevices: number
  syncingDevices: number
  errorDevices: number
  totalRecords: number
  todayRecords: number
  successRate: number
  averageResponseTime: number
  lastSyncTime: string
}

// 考勤同步状态接口
export interface AttendanceSyncStatus {
  currentStatus: 'idle' | 'syncing' | 'failed' | 'success'
  lastSyncTime: string
  nextSyncTime: string
  syncProgress: number
  totalRecords: number
  processedRecords: number
  failedRecords: number
  errorMessage?: string
  syncHistory: {
    taskId: string
    syncType: string
    startTime: string
    endTime: string
    status: string
    totalCount: number
    successCount: number
    failedCount: number
    duration: number
  }[]
}

// 考勤设备信息接口
export interface AttendanceDeviceInfo {
  deviceName: string
  firmware: string
  capacity: number
  recordCount: number
  lastActivity: string
  networkStatus: string
  batteryLevel?: number
  temperature?: number
  humidity?: number
  diskSpace?: {
    total: number
    used: number
    available: number
  }
}

// 考勤数据推送接口
export interface AttendanceDataPush {
  recordType: 'attendance' | 'user' | 'department'
  records: {
    recordId: string
    employeeId: string
    employeeNo: string
    recordTime: string
    recordType: string
    verifyMode: string
    workCode: string
    deviceSN: string
    direction: 'in' | 'out'
    temperature?: number
    photoPath?: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }[]
  deviceInfo: {
    deviceSN: string
    timestamp: string
    firmware: string
    batteryLevel?: number
    networkStatus: string
  }
}

// 考勤设备批量配置接口
export interface AttendanceDeviceBatchConfig {
  deviceIds: string[]
  config: {
    syncInterval: number
    syncTimeRange: [string, string]
    autoSync: boolean
    retryCount: number
    retryInterval: number
    conflictStrategy: 'skip' | 'override' | 'merge'
    deduplication: boolean
  }
}

// 考勤设备批量操作结果接口
export interface AttendanceDeviceBatchResult {
  successCount: number
  failedCount: number
  results: {
    deviceId: string
    deviceName: string
    status: 'success' | 'failed'
    message: string
  }[]
}

// 考勤设备状态监控接口 (ATT-DEV-002)
export interface AttendanceDeviceStatusOverview {
  summary: {
    totalDevices: number
    onlineDevices: number
    offlineDevices: number
    errorDevices: number
    syncingDevices: number
    maintenanceDevices: number
    lastUpdateTime: string
  }
  deviceStatusList: {
    deviceId: string
    deviceName: string
    deviceType: string
    location: string
    status: 'online' | 'offline' | 'error' | 'syncing' | 'maintenance'
    lastHeartbeat: string
    responseTime: number
    errorCount: number
    uptime: number
    batteryLevel?: number
    networkSignal?: number
    cpuUsage?: number
    memoryUsage?: number
    temperature?: number
    alertLevel: 'none' | 'low' | 'medium' | 'high' | 'critical'
    alertMessage?: string
  }[]
  systemHealth: {
    overallHealth: 'healthy' | 'warning' | 'critical'
    healthScore: number
    availabilityRate: number
    performanceScore: number
    reliabilityScore: number
  }
}

// 考勤设备详细状态接口
export interface AttendanceDeviceDetailedStatus {
  deviceInfo: {
    deviceId: string
    deviceName: string
    deviceType: string
    manufacturer: string
    model: string
    serialNumber: string
    ipAddress: string
    port: number
    location: string
    installDate: string
    lastMaintenanceDate: string
    warrantyExpiry: string
  }
  currentStatus: {
    status: 'online' | 'offline' | 'error' | 'syncing' | 'maintenance'
    lastHeartbeat: string
    responseTime: number
    uptime: number
    connectionStability: number
    dataIntegrity: number
    performanceScore: number
    healthScore: number
  }
  hardwareMetrics: {
    cpuUsage: number
    memoryUsage: number
    diskUsage: number
    temperature: number
    batteryLevel?: number
    networkSignal?: number
    firmwareVersion: string
    lastFirmwareUpdate: string
  }
  networkStatus: {
    connectionType: 'wired' | 'wireless' | 'mobile'
    ipAddress: string
    macAddress: string
    networkSpeed: number
    latency: number
    packetLoss: number
    dnsStatus: 'resolved' | 'failed'
    gatewayStatus: 'reachable' | 'unreachable'
  }
  operationalMetrics: {
    totalRecords: number
    recordsToday: number
    recordsThisWeek: number
    recordsThisMonth: number
    averageRecordsPerDay: number
    lastRecordTime: string
    errorRate: number
    successRate: number
  }
  alertsAndEvents: {
    activeAlerts: AttendanceDeviceAlert[]
    recentEvents: AttendanceDeviceEvent[]
  }
}

// 考勤设备告警接口
export interface AttendanceDeviceAlert {
  alertId: string
  deviceId: string
  deviceName: string
  deviceType: string
  location: string
  alertType:
    | 'device_offline'
    | 'connection_timeout'
    | 'sync_failure'
    | 'hardware_error'
    | 'performance_degradation'
    | 'maintenance_required'
  severity: 'low' | 'medium' | 'high' | 'critical'
  status: 'active' | 'acknowledged' | 'resolved'
  message: string
  description: string
  triggeredAt: string
  acknowledgedAt?: string
  acknowledgedBy?: string
  resolvedAt?: string
  resolvedBy?: string
  autoResolved: boolean
  notificationSent: boolean
  escalationLevel: number
  affectedUsers: number
  estimatedImpact: string
  recommendedActions: string[]
  relatedAlerts: string[]
}

// 考勤设备事件接口
export interface AttendanceDeviceEvent {
  eventId: string
  eventType: 'status_change' | 'alert_triggered' | 'maintenance_started' | 'configuration_updated'
  description: string
  timestamp: string
  userId?: string
  userName?: string
}

// 考勤设备状态历史接口
export interface AttendanceDeviceStatusHistory {
  deviceId: string
  deviceName: string
  timeRange: {
    startDate: string
    endDate: string
    granularity: string
  }
  metrics: {
    timestamp: string
    status: string
    uptime: number
    responseTime: number
    errorRate: number
    performanceScore: number
    cpuUsage?: number
    memoryUsage?: number
    temperature?: number
    batteryLevel?: number
    recordCount: number
    alerts: {
      alertType: string
      severity: string
      message: string
    }[]
  }[]
  statistics: {
    totalDataPoints: number
    averageUptime: number
    averageResponseTime: number
    averageErrorRate: number
    averagePerformanceScore: number
    maxResponseTime: number
    minResponseTime: number
    totalAlerts: number
    totalDowntime: number
    availabilityRate: number
  }
  total: number
}

// 考勤设备性能指标接口
export interface AttendanceDevicePerformanceMetrics {
  deviceId: string
  deviceName: string
  timeRange: {
    startDate: string
    endDate: string
    granularity: string
  }
  metrics: {
    cpu: AttendanceDeviceMetricData
    memory: AttendanceDeviceMetricData
    disk: AttendanceDeviceMetricData
    network: AttendanceDeviceMetricData
    responseTime: AttendanceDeviceMetricData
    throughput: AttendanceDeviceMetricData
    errorRate: AttendanceDeviceMetricData
  }
  analysis: {
    overallHealth: 'healthy' | 'warning' | 'critical'
    healthScore: number
    recommendations: string[]
    anomalies: {
      metricType: string
      severity: string
      description: string
      timestamp: string
      value: number
      threshold: number
    }[]
    trends: {
      metricType: string
      trend: 'increasing' | 'decreasing' | 'stable'
      confidence: number
      prediction: number
      timeframe: string
    }[]
  }
}

// 考勤设备指标数据接口
export interface AttendanceDeviceMetricData {
  timestamps: string[]
  values: number[]
  average: number
  maximum: number
  minimum: number
  threshold: number
  status: 'normal' | 'warning' | 'critical'
}

// 考勤设备健康检查接口
export interface AttendanceDeviceHealthCheck {
  checkId: string
  deviceId: string
  deviceName: string
  checkType: string
  startTime: string
  endTime?: string
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  results: {
    overall: {
      healthScore: number
      status: 'healthy' | 'warning' | 'critical'
      summary: string
      recommendations: string[]
    }
    connectivity: {
      status: 'pass' | 'fail' | 'warning'
      responseTime: number
      packetLoss: number
      message: string
    }
    hardware: {
      status: 'pass' | 'fail' | 'warning'
      cpuTest: { status: string; value: number; threshold: number }
      memoryTest: { status: string; value: number; threshold: number }
      diskTest: { status: string; value: number; threshold: number }
      temperatureTest: { status: string; value: number; threshold: number }
      message: string
    }
    software: {
      status: 'pass' | 'fail' | 'warning'
      firmwareVersion: string
      isLatest: boolean
      configurationValid: boolean
      serviceStatus: string
      message: string
    }
    dataIntegrity: {
      status: 'pass' | 'fail' | 'warning'
      recordCount: number
      corruptedRecords: number
      missingRecords: number
      dataQualityScore: number
      message: string
    }
    security: {
      status: 'pass' | 'fail' | 'warning'
      certificateValid: boolean
      encryptionEnabled: boolean
      accessControlValid: boolean
      vulnerabilities: string[]
      message: string
    }
    performance: {
      status: 'pass' | 'fail' | 'warning'
      throughput: number
      errorRate: number
      averageResponseTime: number
      performanceScore: number
      message: string
    }
  }
  issues: {
    severity: 'low' | 'medium' | 'high' | 'critical'
    category: 'connectivity' | 'hardware' | 'software' | 'data' | 'security' | 'performance'
    description: string
    impact: string
    recommendedAction: string
    priority: number
  }[]
}

// 考勤设备监控配置接口
export interface AttendanceDeviceMonitoringConfig {
  deviceId: string
  deviceName: string
  monitoringEnabled: boolean
  monitoringConfig: {
    heartbeatInterval: number
    healthCheckInterval: number
    performanceMetricsInterval: number
    alertThresholds: {
      responseTime: { warning: number; critical: number }
      cpuUsage: { warning: number; critical: number }
      memoryUsage: { warning: number; critical: number }
      diskUsage: { warning: number; critical: number }
      temperature: { warning: number; critical: number }
      errorRate: { warning: number; critical: number }
      uptime: { warning: number; critical: number }
    }
    alertSettings: {
      enableEmailAlerts: boolean
      enableSmsAlerts: boolean
      enableWebhookAlerts: boolean
      alertRecipients: string[]
      escalationRules: {
        timeToEscalate: number
        escalationLevels: {
          level: number
          recipients: string[]
          actions: string[]
        }[]
      }
    }
    retentionPolicy: {
      statusHistoryDays: number
      performanceMetricsDays: number
      alertHistoryDays: number
      logRetentionDays: number
    }
    maintenanceWindow: {
      enabled: boolean
      weeklySchedule: {
        dayOfWeek: number
        startTime: string
        endTime: string
      }[]
      disableAlertsInMaintenance: boolean
    }
  }
}

// 考勤设备监控报告接口
export interface AttendanceDeviceMonitoringReport {
  reportId: string
  reportType: string
  generatedAt: string
  timeRange: {
    startDate: string
    endDate: string
    period: string
  }
  summary: {
    totalDevices: number
    monitoredDevices: number
    healthyDevices: number
    warningDevices: number
    criticalDevices: number
    averageUptime: number
    averageResponseTime: number
    totalAlerts: number
    resolvedAlerts: number
    outstandingAlerts: number
  }
  deviceReports: {
    deviceId: string
    deviceName: string
    deviceType: string
    location: string
    overallHealth: 'healthy' | 'warning' | 'critical'
    healthScore: number
    uptime: number
    availabilityRate: number
    averageResponseTime: number
    totalRecords: number
    errorRate: number
    alertsCount: number
    lastMaintenanceDate: string
    recommendedActions: string[]
  }[]
  trends: {
    uptimeTrend: { period: string; value: number }[]
    responseTimeTrend: { period: string; value: number }[]
    errorRateTrend: { period: string; value: number }[]
    alertsTrend: { period: string; value: number }[]
  }
  comparisons: {
    periodComparison: {
      metric: string
      currentValue: number
      previousValue: number
      change: number
      changePercentage: number
      trend: 'improving' | 'declining' | 'stable'
    }[]
    deviceComparison: {
      bestPerforming: string[]
      worstPerforming: string[]
      mostReliable: string[]
      leastReliable: string[]
    }
  }
  recommendations: {
    priority: 'high' | 'medium' | 'low'
    category: 'performance' | 'reliability' | 'maintenance' | 'security' | 'configuration'
    description: string
    impact: string
    effort: 'low' | 'medium' | 'high'
    timeline: string
  }[]
}

// 考勤设备状态订阅接口
export interface AttendanceDeviceStatusSubscription {
  subscriptionId: string
  websocketUrl: string
  heartbeatInterval: number
  expiryTime: string
}

// 考勤设备配置管理接口 (ATT-DEV-003)
export interface AttendanceDeviceConfig {
  deviceId: string
  deviceName: string
  version: string
  lastUpdated: string
  updatedBy: string
  networkConfig: {
    ipAddress: string
    port: number
    protocol: 'tcp' | 'udp' | 'http' | 'https'
    username?: string
    password?: string
    timeout: number
    sslEnabled: boolean
    certificatePath?: string
    proxyEnabled: boolean
    proxyHost?: string
    proxyPort?: number
    keepAliveEnabled: boolean
    maxRetries: number
  }
  syncConfig: {
    syncInterval: number
    syncTimeRange: [string, string]
    autoSync: boolean
    retryCount: number
    retryInterval: number
    conflictStrategy: 'skip' | 'override' | 'merge'
    deduplication: boolean
    dataValidation: boolean
    notifications: string[]
    batchSize: number
    compressionEnabled: boolean
  }
  monitoringConfig: {
    heartbeatInterval: number
    healthCheckInterval: number
    performanceMetricsInterval: number
    alertThresholds: {
      responseTime: { warning: number; critical: number }
      cpuUsage: { warning: number; critical: number }
      memoryUsage: { warning: number; critical: number }
      diskUsage: { warning: number; critical: number }
      temperature: { warning: number; critical: number }
      errorRate: { warning: number; critical: number }
      uptime: { warning: number; critical: number }
    }
    alertSettings: {
      enableEmailAlerts: boolean
      enableSmsAlerts: boolean
      enableWebhookAlerts: boolean
      alertRecipients: string[]
      escalationEnabled: boolean
      escalationTime: number
    }
    retentionPolicy: {
      statusHistoryDays: number
      performanceMetricsDays: number
      alertHistoryDays: number
      logRetentionDays: number
    }
    maintenanceWindow: {
      enabled: boolean
      weeklySchedule: {
        dayOfWeek: number
        startTime: string
        endTime: string
      }[]
      disableAlertsInMaintenance: boolean
    }
  }
  operationalConfig: {
    timeZone: string
    dateFormat: string
    timeFormat: '12h' | '24h'
    workingHours: {
      start: string
      end: string
    }
    holidays: string[]
    attendanceRules: {
      lateThreshold: number
      earlyLeaveThreshold: number
      overtimeThreshold: number
      minWorkingHours: number
      maxWorkingHours: number
      breakDuration: number
      roundingRules: {
        clockInRounding: number
        clockOutRounding: number
        roundingMethod: 'round' | 'floor' | 'ceil'
      }
    }
    biometricSettings: {
      fingerprintEnabled: boolean
      faceRecognitionEnabled: boolean
      cardReaderEnabled: boolean
      multiFactorRequired: boolean
      verificationThreshold: number
    }
  }
  securityConfig: {
    encryptionEnabled: boolean
    encryptionMethod: 'AES128' | 'AES256' | 'RSA'
    authenticationMode: 'none' | 'basic' | 'digest' | 'certificate' | 'oauth'
    accessControlEnabled: boolean
    allowedIPs: string[]
    blockedIPs: string[]
    sessionTimeout: number
    maxFailedAttempts: number
    lockoutDuration: number
    passwordPolicy: {
      minLength: number
      requireUppercase: boolean
      requireLowercase: boolean
      requireNumbers: boolean
      requireSymbols: boolean
      expiryDays: number
    }
    auditLogging: boolean
    dataEncryptionAtRest: boolean
  }
  advancedConfig: {
    logLevel: 'debug' | 'info' | 'warning' | 'error'
    maxConnections: number
    bufferSize: number
    compressionEnabled: boolean
    cacheEnabled: boolean
    cacheSize: number
    performanceOptimization: boolean
    customParameters: Record<string, unknown>
    experimentalFeatures: {
      enabled: boolean
      features: string[]
    }
  }
}

// 考勤设备配置模板接口
export interface AttendanceDeviceConfigTemplate {
  templateId: string
  templateName: string
  templateDescription: string
  templateType: 'default' | 'custom' | 'preset'
  deviceTypes: string[]
  category: string
  tags: string[]
  usageCount: number
  configSections: ('network' | 'sync' | 'monitoring' | 'operational' | 'security' | 'advanced')[]
  isDefault: boolean
  isActive: boolean
  version: string
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  lastUsed: string
  validationStatus: 'valid' | 'invalid' | 'pending'
  compatibility: {
    deviceTypes: string[]
    firmwareVersions: string[]
    supportedFeatures: string[]
  }
  configData: {
    networkConfig?: Record<string, unknown>
    syncConfig?: Record<string, unknown>
    monitoringConfig?: Record<string, unknown>
    operationalConfig?: Record<string, unknown>
    securityConfig?: Record<string, unknown>
    advancedConfig?: Record<string, unknown>
  }
  validationRules?: {
    requiredFields: string[]
    validationSchema: Record<string, unknown>
  }
}

// 考勤设备配置历史记录接口
export interface AttendanceDeviceConfigHistory {
  historyId: string
  deviceId: string
  deviceName: string
  version: string
  changeType: 'create' | 'update' | 'delete' | 'deploy' | 'rollback'
  changeDescription: string
  changedFields: {
    field: string

    oldValue: unknown

    newValue: unknown
    changeType: 'added' | 'modified' | 'removed'
  }[]
  changeReason: string
  userId: string
  userName: string
  timestamp: string
  deploymentId?: string
  deploymentStatus?: 'success' | 'failed' | 'pending'
  rollbackFromVersion?: string
  approvalStatus?: 'pending' | 'approved' | 'rejected'
  approvedBy?: string
  approvedAt?: string
  configDiff: {
    added: Record<string, unknown>

    modified: Record<string, { old: unknown; new: unknown }>
    removed: Record<string, unknown>
  }
}

// 考勤设备配置验证接口
export interface AttendanceDeviceConfigValidation {
  validationId: string
  isValid: boolean
  errors: {
    field: string
    section: string
    message: string
    severity: 'error' | 'warning'
    errorCode: string
    suggestion?: string
  }[]
  warnings: {
    field: string
    section: string
    message: string
    recommendation: string
    warningCode: string
  }[]
  compatibility: {
    deviceType: string
    isCompatible: boolean
    unsupportedFeatures: string[]
    requiredUpdates: string[]
  }
  validationSummary: {
    totalChecks: number
    passedChecks: number
    failedChecks: number
    warningChecks: number
    validationScore: number
  }
  recommendations: {
    priority: 'high' | 'medium' | 'low'
    category: 'security' | 'performance' | 'compliance' | 'best-practice'
    description: string
    action: string
    impact: string
  }[]
}

// 考勤设备配置部署接口
export interface AttendanceDeviceConfigDeployment {
  deploymentId: string
  deviceId: string
  deviceName: string
  deploymentType: string
  status: 'pending' | 'deploying' | 'success' | 'failed' | 'rollback'
  startTime: string
  endTime?: string
  estimatedDuration: number
  progress: number
  currentStep: string
  steps: {
    stepName: string
    stepDescription: string
    status: 'pending' | 'running' | 'success' | 'failed' | 'skipped'
    startTime?: string
    endTime?: string
    duration?: number
    message?: string
    details?: Record<string, unknown>
  }[]
  rollbackPlan?: {
    enabled: boolean
    rollbackVersion: string
    rollbackSteps: string[]
    rollbackConditions: string[]
  }
  validationResults?: {
    isValid: boolean
    errors: {
      field: string
      message: string
      severity: 'error' | 'warning'
    }[]
  }
  errorMessage?: string
  configVersion?: string
}

// 考勤设备配置回滚接口
export interface AttendanceDeviceConfigRollback {
  rollbackId: string
  deviceId: string
  deviceName: string
  rollbackType: string
  status: 'pending' | 'rolling-back' | 'success' | 'failed'
  startTime: string
  endTime?: string
  progress: number
  currentStep: string
  steps: {
    stepName: string
    status: 'pending' | 'running' | 'success' | 'failed'
    message?: string
    timestamp?: string
  }[]
  rollbackFromVersion: string
  rollbackToVersion: string
  backupId?: string
  validationResults?: {
    isValid: boolean
    errors: {
      field: string
      message: string
      severity: 'error' | 'warning'
    }[]
  }
  errorMessage?: string
}

// 考勤设备配置备份接口
export interface AttendanceDeviceConfigBackup {
  backupId: string
  deviceId: string
  deviceName: string
  backupName: string
  backupDescription: string
  backupType: 'full' | 'partial' | 'incremental'
  configVersion: string
  backupSections: string[]
  backupSize: number
  encrypted: boolean
  createdAt: string
  createdBy: string
  expiryDate?: string
  checksum: string
  isCorrupted: boolean
  restoreCount: number
  lastRestored?: string
  metadata: {
    deviceType: string
    firmwareVersion: string
    lastModified: string
    configSections: string[]
  }
}

// 考勤设备配置恢复接口
export interface AttendanceDeviceConfigRestore {
  restoreId: string
  deviceId: string
  deviceName: string
  backupId: string
  backupName: string
  status: 'pending' | 'restoring' | 'success' | 'failed'
  startTime: string
  endTime?: string
  progress: number
  restoreSections: string[]
  backupBeforeRestoreId?: string
  deploymentId?: string
  validationResults?: {
    isValid: boolean
    errors: {
      field: string
      message: string
      severity: 'error' | 'warning'
    }[]
  }
  errorMessage?: string
  configVersion?: string
}

// 考勤设备配置比较接口
export interface AttendanceDeviceConfigComparison {
  comparisonId: string
  deviceConfigs: {
    deviceId: string
    deviceName: string
    configVersion: string
    config: Record<string, unknown>
    lastUpdated: string
  }[]
  differences: {
    field: string
    section: string
    values: {
      deviceId: string
      deviceName: string

      value: unknown
    }[]
    differenceType: 'value' | 'missing' | 'extra'
    severity: 'info' | 'warning' | 'error'
  }[]
  similarities: {
    field: string
    section: string

    commonValue: unknown
    deviceCount: number
    devices: string[]
  }[]
  statistics: {
    totalFields: number
    identicalFields: number
    differentFields: number
    missingFields: number
    similarityPercentage: number
  }
  recommendations: {
    priority: 'high' | 'medium' | 'low'
    action: 'standardize' | 'review' | 'ignore'
    description: string
    affectedDevices: string[]

    suggestedValue?: unknown
  }[]
}

// 考勤设备配置批量操作接口
export interface AttendanceDeviceConfigBatchOperation {
  batchId: string
  totalDevices: number
  successfulOperations: number
  failedOperations: number
  results: {
    deviceId: string
    deviceName: string
    status: 'success' | 'failed' | 'pending'
    deploymentId?: string
    configVersion?: string
    validationResult: {
      isValid: boolean
      errors: {
        field: string
        message: string
        severity: 'error' | 'warning'
      }[]
    }
    message: string
    operationTime?: string
  }[]
  deploymentSummary: {
    totalDeployments: number
    successfulDeployments: number
    failedDeployments: number
    pendingDeployments: number
  }
}

// 考勤设备故障处理接口 (ATT-DEV-004)
// 设备故障记录接口
export interface AttendanceDeviceFault {
  faultId: string
  deviceId: string
  deviceName: string
  deviceType: string
  location: string
  faultType: 'CONNECTION' | 'SYNC' | 'HARDWARE' | 'SOFTWARE' | 'PERFORMANCE' | 'SECURITY'
  faultLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  faultCode: string
  faultMessage: string
  faultDescription: string
  detectionTime: string
  detectionMethod: 'AUTOMATIC' | 'MANUAL' | 'SCHEDULED'
  status: 'OPEN' | 'ASSIGNED' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED'
  assignedTo?: string
  assignedBy?: string
  assignedAt?: string
  priority: number
  estimatedResolutionTime?: string
  actualResolutionTime?: string
  impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  affectedUsers: number
  rootCause?: string
  resolution?: string
  preventiveMeasures?: string
  createdBy: string
  createdAt: string
  updatedAt: string
  resolvedBy?: string
  resolvedAt?: string
  closedBy?: string
  closedAt?: string
  diagnosisResult?: AttendanceDeviceFaultDiagnosis
  repairActions: AttendanceDeviceFaultRepairAction[]
  relatedFaults: string[]
  tags: string[]
  attachments: AttendanceDeviceFaultAttachment[]
  timeline: AttendanceDeviceFaultTimelineEvent[]
}

// 设备故障诊断结果接口
export interface AttendanceDeviceFaultDiagnosis {
  diagnosisId: string
  diagnosisStartTime: string
  diagnosisEndTime?: string
  diagnosisStatus: 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  diagnosisProgress: number
  overallStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'UNKNOWN'
  overallScore: number
  diagnosticTests: AttendanceDeviceFaultDiagnosticTest[]
  identifiedIssues: AttendanceDeviceFaultIdentifiedIssue[]
  recommendations: AttendanceDeviceFaultRecommendation[]
  diagnosticReport?: {
    reportId: string
    reportUrl: string
    reportSize: number
    generatedAt: string
  }
}

// 设备故障诊断测试接口
export interface AttendanceDeviceFaultDiagnosticTest {
  testId: string
  testName: string
  testCategory:
    | 'CONNECTIVITY'
    | 'HARDWARE'
    | 'SOFTWARE'
    | 'PERFORMANCE'
    | 'SECURITY'
    | 'DATA_INTEGRITY'
  testResult: 'PASS' | 'FAIL' | 'WARNING' | 'SKIPPED'
  testScore: number
  testMessage: string
  testStartTime: string
  testEndTime?: string
  testDuration?: number
  testDetails?: {
    connectivity?: {
      pingTest: { result: 'PASS' | 'FAIL'; responseTime: number; packetLoss: number }
      portTest: { result: 'PASS' | 'FAIL'; openPorts: number[]; closedPorts: number[] }
      dnsTest: { result: 'PASS' | 'FAIL'; resolvedIPs: string[] }
      certificateTest?: { result: 'PASS' | 'FAIL'; expiryDate: string; isValid: boolean }
    }
    hardware?: {
      cpuTest: { result: 'PASS' | 'FAIL' | 'WARNING'; usage: number; temperature: number }
      memoryTest: { result: 'PASS' | 'FAIL' | 'WARNING'; usage: number; available: number }
      diskTest: { result: 'PASS' | 'FAIL' | 'WARNING'; usage: number; available: number }
      temperatureTest: { result: 'PASS' | 'FAIL' | 'WARNING'; currentTemp: number; maxTemp: number }
      batteryTest?: { result: 'PASS' | 'FAIL' | 'WARNING'; level: number; health: string }
    }
    software?: {
      firmwareTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        currentVersion: string
        latestVersion: string
      }
      serviceTest: { result: 'PASS' | 'FAIL'; runningServices: string[]; failedServices: string[] }
      configTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        validConfig: boolean
        configErrors: string[]
      }
      licenseTest?: { result: 'PASS' | 'FAIL' | 'WARNING'; isValid: boolean; expiryDate: string }
    }
    performance?: {
      responseTimeTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        avgResponseTime: number
        maxResponseTime: number
      }
      throughputTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        currentThroughput: number
        maxThroughput: number
      }
      errorRateTest: { result: 'PASS' | 'FAIL' | 'WARNING'; errorRate: number; errorCount: number }
      uptimeTest: { result: 'PASS' | 'FAIL' | 'WARNING'; uptime: number; downtime: number }
    }
    security?: {
      authenticationTest: { result: 'PASS' | 'FAIL'; authMethod: string; lastLogin: string }
      encryptionTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        encryptionEnabled: boolean
        encryptionLevel: string
      }
      vulnerabilityTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        vulnerabilities: string[]
        riskLevel: string
      }
      accessControlTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        accessRules: string[]
        violations: string[]
      }
    }
    dataIntegrity?: {
      recordCountTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        expectedCount: number
        actualCount: number
      }
      dataQualityTest: {
        result: 'PASS' | 'FAIL' | 'WARNING'
        qualityScore: number
        qualityIssues: string[]
      }
      backupTest: { result: 'PASS' | 'FAIL' | 'WARNING'; lastBackup: string; backupSize: number }
      syncTest: { result: 'PASS' | 'FAIL' | 'WARNING'; lastSync: string; syncErrors: string[] }
    }
  }
}

// 设备故障识别问题接口
export interface AttendanceDeviceFaultIdentifiedIssue {
  issueId: string
  issueType:
    | 'CONNECTIVITY'
    | 'HARDWARE'
    | 'SOFTWARE'
    | 'PERFORMANCE'
    | 'SECURITY'
    | 'DATA_INTEGRITY'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  issueDescription: string
  impact: string
  affectedComponents: string[]
  recommendedActions: string[]
  priority: number
  estimatedFixTime: string
  requiresReboot: boolean
  requiresReplacement: boolean
}

// 设备故障修复建议接口
export interface AttendanceDeviceFaultRecommendation {
  recommendationId: string
  category: 'IMMEDIATE' | 'PLANNED' | 'PREVENTIVE' | 'OPTIMIZATION'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  description: string
  detailedSteps: string[]
  estimatedDuration: string
  requiredSkills: string[]
  requiredTools: string[]
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  businessImpact: string
  costEstimate?: number
}

// 设备故障修复操作接口
export interface AttendanceDeviceFaultRepairAction {
  actionId: string
  actionType: 'AUTOMATIC' | 'MANUAL' | 'SCHEDULED'
  actionDescription: string
  actionStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED'
  actionResult?: string
  executedBy?: string
  executedAt?: string
}

// 设备故障附件接口
export interface AttendanceDeviceFaultAttachment {
  attachmentId: string
  fileName: string
  fileType: string
  fileSize: number
  uploadedBy: string
  uploadedAt: string
  downloadUrl: string
}

// 设备故障时间线事件接口
export interface AttendanceDeviceFaultTimelineEvent {
  eventId: string
  eventType: 'CREATED' | 'UPDATED' | 'ASSIGNED' | 'DIAGNOSED' | 'REPAIRED' | 'RESOLVED' | 'CLOSED'
  eventDescription: string
  eventTime: string
  eventBy: string
  eventDetails?: Record<string, unknown>
}

// 设备故障自动修复结果接口
export interface AttendanceDeviceFaultAutoRepairResult {
  faultId: string
  repairId: string
  repairStartTime: string
  repairEndTime?: string
  repairStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED'
  repairProgress: number
  repairType: 'AUTOMATIC' | 'GUIDED' | 'CUSTOM'
  repairScope: 'FAULT_SPECIFIC' | 'COMPREHENSIVE' | 'PREVENTIVE'
  repairActions: AttendanceDeviceFaultAutoRepairAction[]
  overallResult: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED' | 'CANCELLED'
  resolvedIssues: AttendanceDeviceFaultResolvedIssue[]
  remainingIssues: AttendanceDeviceFaultRemainingIssue[]
  rollbackInfo?: AttendanceDeviceFaultRollbackInfo
  nextSteps: AttendanceDeviceFaultNextStep[]
}

// 设备故障自动修复操作接口
export interface AttendanceDeviceFaultAutoRepairAction {
  actionId: string
  actionType:
    | 'RESTART_DEVICE'
    | 'RESET_CONNECTION'
    | 'CLEAR_CACHE'
    | 'UPDATE_FIRMWARE'
    | 'RESTORE_CONFIG'
    | 'SYNC_DATA'
  actionDescription: string
  actionStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'SKIPPED'
  actionStartTime?: string
  actionEndTime?: string
  actionDuration?: number
  actionResult?: string
  actionDetails?: Record<string, unknown>
  retryCount: number
  maxRetries: number
}

// 设备故障已解决问题接口
export interface AttendanceDeviceFaultResolvedIssue {
  issueId: string
  issueDescription: string
  resolutionAction: string
  resolutionStatus: 'RESOLVED' | 'PARTIALLY_RESOLVED' | 'UNRESOLVED'
  resolutionDetails?: string
}

// 设备故障剩余问题接口
export interface AttendanceDeviceFaultRemainingIssue {
  issueId: string
  issueDescription: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  recommendedActions: string[]
  requiresManualIntervention: boolean
}

// 设备故障回滚信息接口
export interface AttendanceDeviceFaultRollbackInfo {
  rollbackAvailable: boolean
  rollbackId: string
  rollbackReason: string
  rollbackSteps: string[]
}

// 设备故障后续步骤接口
export interface AttendanceDeviceFaultNextStep {
  stepType: 'VERIFICATION' | 'MONITORING' | 'MANUAL_REPAIR' | 'REPLACEMENT' | 'PREVENTIVE'
  stepDescription: string
  stepPriority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  estimatedDuration: string
  assignedTo?: string
  dueDate?: string
}

// 设备故障手动修复结果接口
export interface AttendanceDeviceFaultManualRepairResult {
  faultId: string
  repairId: string
  repairType: 'MANUAL'
  repairStatus: 'COMPLETED' | 'PARTIALLY_COMPLETED' | 'FAILED'
  completedActions: AttendanceDeviceFaultManualRepairAction[]
  overallResult: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED'
  nextSteps: string[]
  message: string
}

// 设备故障手动修复操作接口
export interface AttendanceDeviceFaultManualRepairAction {
  actionId: string
  actionType: string
  actionDescription: string
  actionStatus: 'SUCCESS' | 'FAILED' | 'PARTIAL'
  executedBy: string
  executionTime: string
  actionResult: string
  verificationResult?: string
}

// 设备故障修复历史记录接口
export interface AttendanceDeviceFaultRepairHistory {
  repairId: string
  repairType: 'AUTOMATIC' | 'MANUAL' | 'GUIDED' | 'CUSTOM'
  repairStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED'
  repairStartTime: string
  repairEndTime?: string
  repairDuration?: number
  repairScope: 'FAULT_SPECIFIC' | 'COMPREHENSIVE' | 'PREVENTIVE'
  executedBy: string
  repairActions: {
    actionType: string
    actionDescription: string
    actionStatus: 'SUCCESS' | 'FAILED' | 'PARTIAL'
    actionResult?: string
  }[]
  overallResult: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED' | 'CANCELLED'
  resolvedIssues: number
  remainingIssues: number
  rollbackInfo?: {
    rollbackAvailable: boolean
    rollbackId: string
  }
}

// 设备故障批量处理结果接口
export interface AttendanceDeviceFaultBatchProcessResult {
  batchId: string
  action: string
  processedBy: string
  processTime: string
  totalFaults: number
  successfulOperations: number
  failedOperations: number
  results: AttendanceDeviceFaultBatchProcessResultItem[]
  summary: {
    totalProcessed: number
    successful: number
    failed: number
    partial: number
    averageProcessingTime: number
  }
}

// 设备故障批量处理结果项接口
export interface AttendanceDeviceFaultBatchProcessResultItem {
  faultId: string
  faultMessage: string
  operationStatus: 'SUCCESS' | 'FAILED' | 'PARTIAL'
  operationResult: string
  operationDetails?: Record<string, unknown>
}

// 设备故障统计信息接口
export interface AttendanceDeviceFaultStatistics {
  summary: {
    totalFaults: number
    openFaults: number
    resolvedFaults: number
    closedFaults: number
    criticalFaults: number
    highPriorityFaults: number
    averageResolutionTime: number
    averageResponseTime: number
    resolutionRate: number
    faultFrequency: number
    mtbf: number // Mean Time Between Failures
    mttr: number // Mean Time To Repair
  }
  trends: {
    faultTrends: AttendanceDeviceFaultTrend[]
    deviceTrends: AttendanceDeviceFaultDeviceTrend[]
    faultTypeTrends: AttendanceDeviceFaultTypeTrend[]
  }
  distributions: {
    byFaultType: AttendanceDeviceFaultDistribution[]
    byFaultLevel: AttendanceDeviceFaultDistribution[]
    byDevice: AttendanceDeviceFaultDeviceDistribution[]
    byAssignee: AttendanceDeviceFaultAssigneeDistribution[]
  }
  performance: {
    resolutionTimeDistribution: AttendanceDeviceFaultTimeDistribution[]
    responseTimeMetrics: AttendanceDeviceFaultResponseTimeMetrics
    resolutionRateByType: AttendanceDeviceFaultResolutionRateByType[]
    deviceReliability: AttendanceDeviceFaultDeviceReliability[]
  }
  predictions: {
    predictedFaults: AttendanceDeviceFaultPrediction[]
    maintenanceSchedule: AttendanceDeviceFaultMaintenanceSchedule[]
  }
}

// 设备故障趋势接口
export interface AttendanceDeviceFaultTrend {
  date: string
  totalFaults: number
  openFaults: number
  resolvedFaults: number
  newFaults: number
  criticalFaults: number
}

// 设备故障设备趋势接口
export interface AttendanceDeviceFaultDeviceTrend {
  deviceId: string
  deviceName: string
  deviceType: string
  totalFaults: number
  faultRate: number
  avgResolutionTime: number
  reliability: number
}

// 设备故障类型趋势接口
export interface AttendanceDeviceFaultTypeTrend {
  faultType: string
  count: number
  percentage: number
  avgResolutionTime: number
  recurrenceRate: number
}

// 设备故障分布接口
export interface AttendanceDeviceFaultDistribution {
  faultType: string
  count: number
  percentage: number
}

// 设备故障设备分布接口
export interface AttendanceDeviceFaultDeviceDistribution {
  deviceId: string
  deviceName: string
  deviceType: string
  count: number
  percentage: number
}

// 设备故障分配人员分布接口
export interface AttendanceDeviceFaultAssigneeDistribution {
  assignee: string
  assignedCount: number
  resolvedCount: number
  averageResolutionTime: number
  workload: number
}

// 设备故障时间分布接口
export interface AttendanceDeviceFaultTimeDistribution {
  timeRange: string
  count: number
  percentage: number
}

// 设备故障响应时间指标接口
export interface AttendanceDeviceFaultResponseTimeMetrics {
  avgResponseTime: number
  medianResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
}

// 设备故障按类型解决率接口
export interface AttendanceDeviceFaultResolutionRateByType {
  faultType: string
  resolutionRate: number
  avgResolutionTime: number
}

// 设备故障设备可靠性接口
export interface AttendanceDeviceFaultDeviceReliability {
  deviceId: string
  deviceName: string
  uptime: number
  downtime: number
  reliability: number
  mtbf: number
  mttr: number
}

// 设备故障预测接口
export interface AttendanceDeviceFaultPrediction {
  deviceId: string
  deviceName: string
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  predictedFaultType: string
  probability: number
  timeframe: string
  recommendedActions: string[]
}

// 设备故障维护计划接口
export interface AttendanceDeviceFaultMaintenanceSchedule {
  deviceId: string
  deviceName: string
  maintenanceType: 'PREVENTIVE' | 'CORRECTIVE' | 'UPGRADE'
  scheduledDate: string
  estimatedDuration: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

// 设备故障报告接口
export interface AttendanceDeviceFaultReport {
  reportId: string
  reportType: string
  reportTitle: string
  generatedAt: string
  generatedBy: string
  timeRange: {
    startDate: string
    endDate: string
    period: string
  }
  executiveSummary: {
    totalFaults: number
    criticalFaults: number
    resolutionRate: number
    averageResolutionTime: number
    topIssues: string[]
    keyFindings: string[]
    recommendations: string[]
  }
  detailedAnalysis: {
    faultAnalysis: AttendanceDeviceFaultAnalysis
    performanceAnalysis: AttendanceDeviceFaultPerformanceAnalysis
    trendAnalysis: AttendanceDeviceFaultTrendAnalysis
  }
  recommendations: {
    immediate: AttendanceDeviceFaultReportRecommendation[]
    shortTerm: AttendanceDeviceFaultReportRecommendation[]
    longTerm: AttendanceDeviceFaultReportRecommendation[]
  }
  charts?: AttendanceDeviceFaultChart[]
  downloadUrl?: string
  reportSize?: number
  expiryDate?: string
}

// 设备故障分析接口
export interface AttendanceDeviceFaultAnalysis {
  faultBreakdown: {
    faultType: string
    count: number
    percentage: number
    trend: 'INCREASING' | 'DECREASING' | 'STABLE'
  }[]
  severityAnalysis: {
    severity: string
    count: number
    percentage: number
    avgResolutionTime: number
  }[]
  deviceAnalysis: {
    deviceId: string
    deviceName: string
    deviceType: string
    faultCount: number
    faultRate: number
    reliability: number
    recommendations: string[]
  }[]
}

// 设备故障性能分析接口
export interface AttendanceDeviceFaultPerformanceAnalysis {
  resolutionMetrics: {
    avgResolutionTime: number
    medianResolutionTime: number
    slaCompliance: number
    escalationRate: number
  }
  responseMetrics: {
    avgResponseTime: number
    firstResponseTime: number
    acknowledgeRate: number
  }
  teamPerformance: {
    assignee: string
    resolvedCount: number
    avgResolutionTime: number
    customerSatisfaction: number
  }[]
}

// 设备故障趋势分析接口
export interface AttendanceDeviceFaultTrendAnalysis {
  faultTrends: {
    period: string
    totalFaults: number
    criticalFaults: number
    resolutionRate: number
    trendDirection: 'UP' | 'DOWN' | 'STABLE'
  }[]
  seasonalPatterns: {
    month: string
    faultCount: number
    faultTypes: string[]
    patterns: string[]
  }[]
  deviceHealthTrends: {
    deviceId: string
    deviceName: string
    healthScore: number
    healthTrend: 'IMPROVING' | 'DECLINING' | 'STABLE'
    predictedIssues: string[]
  }[]
}

// 设备故障报告建议接口
export interface AttendanceDeviceFaultReportRecommendation {
  priority: 'HIGH' | 'CRITICAL' | 'MEDIUM' | 'LOW'
  action: string
  description: string
  impact: string
  effort: 'LOW' | 'MEDIUM' | 'HIGH'
  timeline: string
}

// 设备故障图表接口
export interface AttendanceDeviceFaultChart {
  chartType: string
  chartTitle: string

  chartData: unknown

  chartConfig: unknown
}

// 设备故障预警配置接口
export interface AttendanceDeviceFaultAlertConfig {
  configId: string
  alertEnabled: boolean
  alertRules: AttendanceDeviceFaultAlertRule[]
  notificationSettings: AttendanceDeviceFaultNotificationSettings
  maintenanceWindows: AttendanceDeviceFaultMaintenanceWindow[]
}

// 设备故障预警规则接口
export interface AttendanceDeviceFaultAlertRule {
  ruleId: string
  ruleName: string
  ruleType:
    | 'FAULT_THRESHOLD'
    | 'FAULT_RATE'
    | 'DEVICE_OFFLINE'
    | 'RESPONSE_TIME'
    | 'PATTERN_DETECTION'
  enabled: boolean
  conditions: AttendanceDeviceFaultAlertCondition[]
  actions: AttendanceDeviceFaultAlertAction[]
  cooldownPeriod: number
  escalationRules: AttendanceDeviceFaultAlertEscalationRule[]
  scheduleConfig: AttendanceDeviceFaultAlertScheduleConfig
}

// 设备故障预警条件接口
export interface AttendanceDeviceFaultAlertCondition {
  metric: string
  operator: 'GT' | 'LT' | 'EQ' | 'NE' | 'GTE' | 'LTE'
  threshold: number
  timeWindow: number
  deviceFilter?: string[]
  faultTypeFilter?: string[]
  severityFilter?: string[]
}

// 设备故障预警动作接口
export interface AttendanceDeviceFaultAlertAction {
  actionType: 'EMAIL' | 'SMS' | 'WEBHOOK' | 'PUSH' | 'TICKET'
  actionConfig: {
    recipients?: string[]
    template?: string
    url?: string
    headers?: Record<string, string>
    priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  }
}

// 设备故障预警升级规则接口
export interface AttendanceDeviceFaultAlertEscalationRule {
  escalationLevel: number
  escalationTime: number
  escalationRecipients: string[]
  escalationActions: string[]
}

// 设备故障预警调度配置接口
export interface AttendanceDeviceFaultAlertScheduleConfig {
  enabled: boolean
  timezone: string
  workingHours: {
    startTime: string
    endTime: string
    weekdays: number[]
  }
  nonWorkingHoursBehavior: 'SUPPRESS' | 'ESCALATE' | 'NORMAL'
}

// 设备故障通知设置接口
export interface AttendanceDeviceFaultNotificationSettings {
  emailEnabled: boolean
  smsEnabled: boolean
  webhookEnabled: boolean
  pushEnabled: boolean
  defaultRecipients: string[]
  templates: AttendanceDeviceFaultNotificationTemplate[]
}

// 设备故障通知模板接口
export interface AttendanceDeviceFaultNotificationTemplate {
  templateId: string
  templateName: string
  templateType: 'EMAIL' | 'SMS' | 'PUSH'
  templateContent: string
  variables: string[]
}

// 设备故障维护窗口接口
export interface AttendanceDeviceFaultMaintenanceWindow {
  windowId: string
  windowName: string
  startTime: string
  endTime: string
  recurrence: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'CUSTOM'
  affectedDevices: string[]
  suppressAlerts: boolean
  notifyBeforeStart: boolean
  notifyAfterEnd: boolean
}

// 设备故障预警测试结果接口
export interface AttendanceDeviceFaultAlertTestResult {
  testId: string
  alertType: string
  testStatus: 'SUCCESS' | 'FAILED' | 'PARTIAL'
  testTime: string
  testResults: {
    recipient: string
    deliveryStatus: 'DELIVERED' | 'FAILED' | 'PENDING'
    deliveryTime?: string
    errorMessage?: string
    responseCode?: number
    responseBody?: string
  }[]
  summary: {
    totalRecipients: number
    successfulDeliveries: number
    failedDeliveries: number
    averageDeliveryTime: number
  }
  recommendations: string[]
}

// 班次模板管理接口 (ATT-SHIFT-001)
// 班次模板接口
export interface AttendanceShiftTemplate {
  id: string
  name: string
  code: string
  type: 'FIXED' | 'FLEXIBLE' | 'ROTATING'
  startTime: string
  endTime: string
  duration: number
  color: string
  description?: string
  workingHours: {
    actualWorkTime: number
    overtimeThreshold: number
    lateThreshold: number
    earlyLeaveThreshold: number
  }
  breakTimes: AttendanceShiftBreakTime[]
  shiftRules: {
    allowEarlyClockIn: boolean
    allowLateClockOut: boolean
    earlyClockInLimit: number
    lateClockOutLimit: number
    minWorkingHours: number
    maxWorkingHours: number
    requireBreak: boolean
    roundingRules: {
      clockInRounding: number
      clockOutRounding: number
      roundingMethod: 'ROUND' | 'FLOOR' | 'CEIL'
    }
  }
  applicableScope: {
    departments: string[]
    positions: string[]
    employees: string[]
    workdays: number[]
    effectiveDate: string
    expiryDate?: string
  }
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  isDefault: boolean
  version: string
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 班次休息时间接口
export interface AttendanceShiftBreakTime {
  id: string
  name: string
  startTime: string
  endTime: string
  duration: number
  isPaid: boolean
  isMandatory: boolean
}

// 班次模板分类接口
export interface AttendanceShiftTemplateCategory {
  id: string
  name: string
  description: string
  templateCount: number
  sortOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 班次模板使用统计接口
export interface AttendanceShiftTemplateUsageStats {
  templateId: string
  templateName: string
  usageCount: number
  activeEmployees: number
  departments: {
    departmentId: string
    departmentName: string
    employeeCount: number
  }[]
  lastUsedDate: string
  usageFrequency: number
  popularityRank: number
}

// 班次模板验证结果接口
export interface AttendanceShiftTemplateValidation {
  isValid: boolean
  errors: {
    field: string
    message: string
    severity: 'ERROR' | 'WARNING'
    code: string
  }[]
  warnings: {
    field: string
    message: string
    recommendation: string
    code: string
  }[]
  suggestions: {
    field: string

    currentValue: unknown

    suggestedValue: unknown
    reason: string
  }[]
  validationSummary: {
    totalChecks: number
    passedChecks: number
    failedChecks: number
    warningChecks: number
    validationScore: number
  }
}

// 班次模板批量操作结果接口
export interface AttendanceShiftTemplateBatchResult {
  batchId: string
  operation: 'CREATE' | 'UPDATE' | 'DELETE' | 'ACTIVATE' | 'DEACTIVATE' | 'ARCHIVE'
  totalTemplates: number
  successfulOperations: number
  failedOperations: number
  results: {
    templateId: string
    templateName: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED'
    message: string
    errorDetails?: string
  }[]
  summary: {
    totalProcessed: number
    successful: number
    failed: number
    skipped: number
    averageProcessingTime: number
  }
  operationTime: string
  operatedBy: string
}

// 班次模板冲突检查结果接口
export interface AttendanceShiftTemplateConflictCheck {
  hasConflicts: boolean
  conflicts: {
    conflictId: string
    conflictType: 'TIME_OVERLAP' | 'SCOPE_OVERLAP' | 'RULE_CONFLICT' | 'RESOURCE_CONFLICT'
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    description: string
    conflictingTemplates: {
      templateId: string
      templateName: string
      conflictReason: string
    }[]
    affectedScopes: {
      type: 'DEPARTMENT' | 'POSITION' | 'EMPLOYEE'
      id: string
      name: string
    }[]
    resolution: {
      resolutionType: 'AUTO' | 'MANUAL' | 'APPROVE'
      resolutionSteps: string[]
      requiresApproval: boolean
    }
    priority: number
  }[]
  resolutionSummary: {
    totalConflicts: number
    autoResolvable: number
    manualResolution: number
    requiresApproval: number
    estimatedResolutionTime: string
  }
}

// 班次模板导入导出结果接口
export interface AttendanceShiftTemplateImportExportResult {
  operationType: 'IMPORT' | 'EXPORT'
  fileId: string
  fileName: string
  fileSize: number
  processedTime: string
  processedBy: string
  status: 'SUCCESS' | 'FAILED' | 'PARTIAL'
  summary: {
    totalRecords: number
    successfulRecords: number
    failedRecords: number
    skippedRecords: number
    duplicateRecords: number
  }
  details: {
    recordIndex: number
    templateName: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED' | 'DUPLICATE'
    message: string
    errorDetails?: string
  }[]
  downloadUrl?: string
  expiryDate?: string
}

// 班次排班计划接口 (ATT-SHIFT-002)
export interface AttendanceShiftSchedule {
  id: string
  planName: string
  planCode: string
  description?: string
  planType: 'WEEKLY' | 'MONTHLY' | 'CUSTOM'
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  effectiveDate: string
  expiryDate?: string
  departmentId: string
  departmentName: string
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  publishedBy?: string
  publishedAt?: string
  scheduleRules: {
    priority: number
    condition: {
      timeRange: [string, string]
      weekdays: number[]
      employeeIds?: string[]
      positionIds?: string[]
      shiftTypes?: string[]
    }
    assignments: AttendanceShiftAssignment[]
  }[]
  statistics: {
    totalEmployees: number
    assignedEmployees: number
    unassignedEmployees: number
    totalShifts: number
    conflictCount: number
    coverageRate: number
  }
  metadata: {
    autoApproval: boolean
    allowOverride: boolean
    notificationEnabled: boolean
    conflictResolution: 'manual' | 'auto' | 'template'
    backupPlan?: string
  }
}

// 班次分配接口
export interface AttendanceShiftAssignment {
  id: string
  scheduleId: string
  employeeId: string
  employeeName: string
  employeeNo: string
  positionId: string
  positionName: string
  date: string
  shiftId: string
  shiftName: string
  shiftCode: string
  startTime: string
  endTime: string
  workingHours: number
  breakTimes: {
    startTime: string
    endTime: string
    duration: number
    type: 'LUNCH' | 'REST' | 'OTHER'
  }[]
  status: 'SCHEDULED' | 'CONFIRMED' | 'CHANGED' | 'CANCELLED' | 'COMPLETED'
  assignedBy: string
  assignedAt: string
  confirmedBy?: string
  confirmedAt?: string
  notes?: string
  conflicts?: {
    type: 'OVERLAP' | 'CONSTRAINT' | 'AVAILABILITY' | 'SKILL' | 'OVERTIME'
    description: string
    severity: 'LOW' | 'MEDIUM' | 'HIGH'
    suggestion?: string
  }[]
}

// 班次排班规则接口
export interface AttendanceShiftScheduleRule {
  id: string
  ruleName: string
  ruleType: 'WEEKLY_PATTERN' | 'MONTHLY_PATTERN' | 'ROTATION' | 'FIXED' | 'FLEXIBLE'
  priority: number
  isActive: boolean
  condition: {
    timeRange: [string, string]
    weekdays: number[]
    monthDays?: number[]
    employeeFilters: {
      departmentIds?: string[]
      positionIds?: string[]
      employeeIds?: string[]
      skillIds?: string[]
      employeeTypes?: string[]
    }
    shiftFilters: {
      shiftIds?: string[]
      shiftTypes?: string[]
      workingHours?: { min: number; max: number }
    }
    constraints: {
      maxConsecutiveDays?: number
      minRestHours?: number
      maxWeeklyHours?: number
      maxMonthlyHours?: number
      skillRequirements?: string[]
    }
  }
  assignment: {
    strategy: 'ROUND_ROBIN' | 'LOAD_BALANCE' | 'SKILL_MATCH' | 'PREFERENCE' | 'MANUAL'
    parameters: {
      rotationCycle?: number
      balanceWeight?: { experience: number; workload: number; preference: number }
      skillThreshold?: number
      preferenceWeight?: number
    }
    fallbackStrategy?: 'RANDOM' | 'SENIORITY' | 'AVAILABILITY' | 'MANUAL'
  }
  validation: {
    allowConflicts: boolean
    allowOvertime: boolean
    requireConfirmation: boolean
    autoResolveConflicts: boolean
    escalationLevel: 'NONE' | 'SUPERVISOR' | 'HR' | 'ADMIN'
  }
}

// 班次排班模板接口
export interface AttendanceShiftScheduleTemplate {
  id: string
  templateName: string
  templateCode: string
  templateType: 'DEPARTMENT' | 'POSITION' | 'TEAM' | 'PROJECT' | 'CUSTOM'
  description?: string
  isDefault: boolean
  isActive: boolean
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  applicableScope: {
    departmentIds: string[]
    positionIds: string[]
    employeeTypes: string[]
    minEmployees: number
    maxEmployees: number
  }
  schedulePattern: {
    patternType: 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY' | 'CUSTOM'
    cycleLength: number
    patternData: {
      dayIndex: number
      weekIndex?: number
      shiftId: string
      isRequired: boolean
      alternativeShiftIds?: string[]
    }[]
    rotationRules: {
      rotationType: 'DAILY' | 'WEEKLY' | 'MONTHLY'
      rotationOrder: string[]
      rotationInterval: number
    }
  }
  constraints: {
    maxConsecutiveShifts: number
    minRestPeriod: number
    maxWeeklyHours: number
    maxMonthlyHours: number
    weekendPolicy: 'REQUIRED' | 'OPTIONAL' | 'PROHIBITED'
    overtimePolicy: 'ALLOWED' | 'RESTRICTED' | 'PROHIBITED'
  }
  preview: {
    samplePeriod: [string, string]
    sampleEmployees: string[]
    generatedSchedule: AttendanceShiftAssignment[]
  }
  usage: {
    usageCount: number
    lastUsed?: string
    avgRating?: number
    feedback?: string[]
  }
}

// 班次排班冲突检测接口
export interface AttendanceShiftConflictDetection {
  scheduleId: string
  detectionTime: string
  conflicts: {
    conflictId: string
    conflictType:
      | 'TIME_OVERLAP'
      | 'DOUBLE_BOOKING'
      | 'OVERTIME_LIMIT'
      | 'SKILL_MISMATCH'
      | 'AVAILABILITY'
      | 'CONSTRAINT_VIOLATION'
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    description: string
    affectedEmployees: {
      employeeId: string
      employeeName: string
      conflictDetails: string
    }[]
    affectedAssignments: string[]
    suggestedResolutions: {
      resolutionType: 'REASSIGN' | 'SPLIT_SHIFT' | 'OVERTIME' | 'ADDITIONAL_STAFF' | 'MANUAL'
      description: string
      effort: 'LOW' | 'MEDIUM' | 'HIGH'
      impact: string
      autoApplicable: boolean
    }[]
    resolutionStatus: 'PENDING' | 'RESOLVED' | 'IGNORED' | 'ESCALATED'
    resolvedBy?: string
    resolvedAt?: string
    resolution?: string
  }[]
  summary: {
    totalConflicts: number
    criticalConflicts: number
    resolvedConflicts: number
    pendingConflicts: number
    affectedEmployeeCount: number
    affectedShiftCount: number
  }
}

// 班次排班统计接口
export interface AttendanceShiftScheduleStatistics {
  scheduleId: string
  statisticsDate: string
  period: [string, string]
  overall: {
    totalSchedules: number
    publishedSchedules: number
    draftSchedules: number
    archivedSchedules: number
    totalAssignments: number
    confirmedAssignments: number
    pendingAssignments: number
    cancelledAssignments: number
    averageUtilization: number
    coverageRate: number
  }
  byDepartment: {
    departmentId: string
    departmentName: string
    scheduleCount: number
    assignmentCount: number
    utilization: number
    conflictCount: number
    completionRate: number
  }[]
  byShift: {
    shiftId: string
    shiftName: string
    assignmentCount: number
    utilization: number
    preferenceScore: number
    conflictRate: number
  }[]
  byEmployee: {
    employeeId: string
    employeeName: string
    totalAssignments: number
    confirmedAssignments: number
    workingHours: number
    overtimeHours: number
    utilization: number
    satisfactionScore?: number
  }[]
  trends: {
    utilizationTrend: { period: string; value: number }[]
    conflictTrend: { period: string; value: number }[]
    completionTrend: { period: string; value: number }[]
    satisfactionTrend: { period: string; value: number }[]
  }
  performance: {
    planningEfficiency: number
    conflictResolutionTime: number
    changeRequestRate: number
    noShowRate: number
    overtimeRate: number
    costPerHour: number
  }
}

// 班次排班批量操作接口
export interface AttendanceShiftScheduleBatchOperation {
  operationType: 'CREATE' | 'UPDATE' | 'DELETE' | 'PUBLISH' | 'COPY' | 'ARCHIVE'
  operationName: string
  description?: string
  targetScope: {
    scheduleIds?: string[]
    departmentIds?: string[]
    employeeIds?: string[]
    dateRange?: [string, string]
    shiftIds?: string[]
  }
  operationData: {
    templateId?: string
    sourceScheduleId?: string
    targetScheduleId?: string
    modifications?: {
      field: string

      oldValue: unknown

      newValue: unknown
    }[]
    copyOptions?: {
      includeAssignments: boolean
      includeRules: boolean
      adjustDates: boolean
      targetPeriod?: [string, string]
    }
  }
  validation: {
    skipValidation: boolean
    allowConflicts: boolean
    requireConfirmation: boolean
    notifyAffected: boolean
  }
  scheduling: {
    executeImmediately: boolean
    scheduledTime?: string
    priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  }
}

// 班次排班批量操作结果接口
export interface AttendanceShiftScheduleBatchResult {
  operationId: string
  operationType: string
  operationName: string
  startTime: string
  endTime: string
  duration: number
  status: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED' | 'CANCELLED'
  summary: {
    totalItems: number
    successCount: number
    failedCount: number
    skippedCount: number
    warningCount: number
  }
  results: {
    itemId: string
    itemType: string
    itemName: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED' | 'WARNING'
    message: string

    details?: unknown
    errorCode?: string
  }[]
  conflicts: {
    conflictId: string
    description: string
    affectedItems: string[]
    resolution: string
  }[]
  notifications: {
    notificationType: 'EMAIL' | 'SMS' | 'SYSTEM' | 'WEBHOOK'
    recipients: string[]
    status: 'SENT' | 'PENDING' | 'FAILED'
    sentAt?: string
  }[]
}

// 班次变更申请接口 (ATT-SHIFT-003)
export interface AttendanceShiftChangeRequest {
  id: string
  requestNo: string
  requestType: 'INDIVIDUAL' | 'EXCHANGE' | 'TEMPORARY' | 'PERMANENT' | 'EMERGENCY'
  status: 'DRAFT' | 'SUBMITTED' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED' | 'EXPIRED'
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  requesterId: string
  requesterName: string
  requesterNo: string
  departmentId: string
  departmentName: string
  positionId: string
  positionName: string
  submittedAt: string
  effectiveDate: string
  expiryDate?: string
  reason: string
  reasonCategory: 'PERSONAL' | 'MEDICAL' | 'FAMILY' | 'WORK' | 'EMERGENCY' | 'TRAINING' | 'OTHER'
  description?: string
  attachments?: {
    fileId: string
    fileName: string
    fileSize: number
    fileType: string
    uploadAt: string
  }[]
  originalShift: {
    scheduleId: string
    assignmentId: string
    date: string
    shiftId: string
    shiftName: string
    shiftCode: string
    startTime: string
    endTime: string
    workingHours: number
  }
  requestedShift: {
    date: string
    shiftId: string
    shiftName: string
    shiftCode: string
    startTime: string
    endTime: string
    workingHours: number
    isNewShift?: boolean
  }
  exchangePartner?: {
    partnerId: string
    partnerName: string
    partnerNo: string
    partnerDepartment: string
    partnerPosition: string
    partnerShift: {
      scheduleId: string
      assignmentId: string
      date: string
      shiftId: string
      shiftName: string
      shiftCode: string
      startTime: string
      endTime: string
      workingHours: number
    }
    mutualConsent: boolean
    consentAt?: string
  }
  impact: {
    affectedEmployees: number
    affectedShifts: number
    workingHoursChange: number
    overtimeHoursChange: number
    costImpact: number
    coverageImpact: number
    conflictRisk: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  }
  approval: {
    approvalFlow: {
      stepId: string
      stepName: string
      stepType: 'AUTO' | 'MANUAL'
      approverType: 'SUPERVISOR' | 'HR' | 'DEPARTMENT_HEAD' | 'SYSTEM'
      approverId?: string
      approverName?: string
      status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SKIPPED'
      processedAt?: string
      comments?: string
      decisionReason?: string
    }[]
    currentStep: number
    finalDecision?: 'APPROVED' | 'REJECTED'
    finalDecisionAt?: string
    finalDecisionBy?: string
    finalComments?: string
  }
  notifications: {
    requesterNotified: boolean
    supervisorNotified: boolean
    hrNotified: boolean
    partnerNotified: boolean
    systemNotified: boolean
    lastNotificationAt?: string
  }
  metadata: {
    createdBy: string
    createdAt: string
    updatedBy: string
    updatedAt: string
    processingTime?: number
    automaticApproval: boolean
    urgentProcessing: boolean
    requiresManagerApproval: boolean
    requiresHrApproval: boolean
  }
}

// 班次变更申请配置接口
export interface AttendanceShiftChangeConfig {
  id: string
  configName: string
  departmentId: string
  departmentName: string
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  rules: {
    allowedRequestTypes: ('INDIVIDUAL' | 'EXCHANGE' | 'TEMPORARY' | 'PERMANENT' | 'EMERGENCY')[]
    advanceNoticeHours: {
      normal: number
      urgent: number
      emergency: number
    }
    approvalRequired: {
      individual: boolean
      exchange: boolean
      temporary: boolean
      permanent: boolean
      emergency: boolean
    }
    approvalFlow: {
      stepOrder: number
      stepName: string
      approverType: 'SUPERVISOR' | 'HR' | 'DEPARTMENT_HEAD' | 'SYSTEM'
      isRequired: boolean
      autoApprovalConditions?: {
        maxHoursChange: number
        maxDaysAdvance: number
        allowedReasonCategories: string[]
        requesterLevels: string[]
      }
      escalationTime: number
    }[]
    restrictions: {
      maxRequestsPerMonth: number
      maxHoursChangePerRequest: number
      blackoutPeriods: {
        startDate: string
        endDate: string
        reason: string
        affectedShiftTypes?: string[]
      }[]
      minimumCoverageRatio: number
      conflictResolution: 'REJECT' | 'QUEUE' | 'APPROVE_WITH_CONDITIONS'
    }
    notifications: {
      notifyRequester: boolean
      notifySupervisor: boolean
      notifyHr: boolean
      notifyPartner: boolean
      reminderIntervals: number[]
      escalationNotifications: boolean
    }
  }
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
}

// 班次变更申请统计接口
export interface AttendanceShiftChangeStatistics {
  periodStart: string
  periodEnd: string
  overall: {
    totalRequests: number
    pendingRequests: number
    approvedRequests: number
    rejectedRequests: number
    cancelledRequests: number
    expiredRequests: number
    approvalRate: number
    averageProcessingTime: number
    averageResponseTime: number
  }
  byType: {
    requestType: string
    requestCount: number
    approvalRate: number
    averageProcessingTime: number
    rejectionReasons: {
      reason: string
      count: number
      percentage: number
    }[]
  }[]
  byDepartment: {
    departmentId: string
    departmentName: string
    requestCount: number
    approvalRate: number
    averageProcessingTime: number
    topReasons: {
      reasonCategory: string
      count: number
      percentage: number
    }[]
  }[]
  byEmployee: {
    employeeId: string
    employeeName: string
    employeeNo: string
    requestCount: number
    approvedCount: number
    rejectedCount: number
    averageProcessingTime: number
    lastRequestDate?: string
  }[]
  trends: {
    requestVolumeTrend: { period: string; count: number }[]
    approvalRateTrend: { period: string; rate: number }[]
    processingTimeTrend: { period: string; time: number }[]
    reasonCategoryTrend: { period: string; category: string; count: number }[]
  }
  patterns: {
    peakRequestHours: { hour: number; count: number }[]
    peakRequestDays: { day: string; count: number }[]
    seasonalPatterns: { month: string; count: number; variance: number }[]
    urgencyDistribution: { priority: string; count: number; percentage: number }[]
  }
  performance: {
    slaCompliance: number
    escalationRate: number
    automationRate: number
    satisfactionScore: number
    costPerRequest: number
    efficiencyIndex: number
  }
}

// 班次变更申请批量操作接口
export interface AttendanceShiftChangeBatchOperation {
  operationType: 'APPROVE' | 'REJECT' | 'CANCEL' | 'PROCESS' | 'NOTIFY' | 'EXPORT'
  operationName: string
  description?: string
  targetRequests: {
    requestIds?: string[]
    filters?: {
      departmentIds?: string[]
      requesterIds?: string[]
      requestTypes?: string[]
      status?: string[]
      dateRange?: [string, string]
      priority?: string[]
      reasonCategories?: string[]
    }
  }
  operationData: {
    approvalComments?: string
    rejectionReason?: string
    decisionReason?: string
    notificationTemplate?: string
    exportFormat?: 'EXCEL' | 'CSV' | 'PDF'
    customFields?: {
      field: string

      value: unknown
    }[]
  }
  approvalSettings: {
    requireManagerApproval: boolean
    requireHrApproval: boolean
    allowBulkApproval: boolean
    notifyAffectedEmployees: boolean
    validateConflicts: boolean
  }
  scheduling: {
    executeImmediately: boolean
    scheduledTime?: string
    priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  }
}

// 班次变更申请批量操作结果接口
export interface AttendanceShiftChangeBatchResult {
  operationId: string
  operationType: string
  operationName: string
  startTime: string
  endTime: string
  duration: number
  status: 'SUCCESS' | 'PARTIAL_SUCCESS' | 'FAILED' | 'CANCELLED'
  summary: {
    totalRequests: number
    successCount: number
    failedCount: number
    skippedCount: number
    warningCount: number
  }
  results: {
    requestId: string
    requestNo: string
    requesterName: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED' | 'WARNING'
    message: string
    processedAt: string
    errorDetails?: string
  }[]
  conflicts: {
    conflictId: string
    description: string
    affectedRequests: string[]
    resolution: string
    resolvedAt?: string
  }[]
  notifications: {
    totalRecipients: number
    successfulNotifications: number
    failedNotifications: number
    notificationDetails: {
      recipientId: string
      recipientName: string
      notificationType: 'EMAIL' | 'SMS' | 'SYSTEM'
      status: 'SENT' | 'FAILED'
      sentAt?: string
      errorMessage?: string
    }[]
  }
  impact: {
    affectedEmployees: number
    affectedShifts: number
    scheduleChanges: number
    costImpact: number
    coverageImpact: number
  }
}

// 班次变更申请验证接口
export interface AttendanceShiftChangeValidation {
  requestId: string
  validationTime: string
  isValid: boolean
  validationResults: {
    validationType:
      | 'BUSINESS_RULE'
      | 'SCHEDULE_CONFLICT'
      | 'POLICY_COMPLIANCE'
      | 'RESOURCE_AVAILABILITY'
      | 'APPROVAL_FLOW'
    isValid: boolean
    errors: {
      errorCode: string
      errorMessage: string
      field: string
      severity: 'ERROR' | 'WARNING' | 'INFO'
      suggestion?: string
    }[]
    warnings: {
      warningCode: string
      warningMessage: string
      field: string
      recommendation: string
      canProceed: boolean
    }[]
  }[]
  impactAssessment: {
    scheduleImpact: {
      affectedAssignments: number
      conflictingAssignments: string[]
      coverageGaps: {
        date: string
        shiftId: string
        gapHours: number
        severity: 'LOW' | 'MEDIUM' | 'HIGH'
      }[]
    }
    resourceImpact: {
      overtimeRequired: number
      additionalStaffNeeded: number
      skillGaps: string[]
      costIncrease: number
    }
    complianceImpact: {
      policyViolations: string[]
      regulatoryRisks: string[]
      auditFindings: string[]
    }
  }
  recommendations: {
    priority: 'HIGH' | 'MEDIUM' | 'LOW'
    category: 'APPROVAL' | 'MODIFICATION' | 'ALTERNATIVE' | 'REJECTION'
    description: string
    rationale: string
    actionRequired: string
    timeline: string
  }[]
  autoResolutions: {
    resolutionId: string
    resolutionType: 'AUTO_APPROVE' | 'AUTO_REJECT' | 'REQUIRE_ESCALATION' | 'SUGGEST_ALTERNATIVE'
    confidence: number
    reasoning: string
    applicableConditions: string[]
  }[]
}

// 班次统计分析接口 (ATT-SHIFT-004)
export interface AttendanceShiftAnalytics {
  analysisId: string
  analysisName: string
  analysisType: 'COMPREHENSIVE' | 'UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY' | 'COMPLIANCE'
  period: [string, string]
  scope: {
    departmentIds: string[]
    employeeIds: string[]
    shiftIds: string[]
    positionIds: string[]
    includeSubDepartments: boolean
  }
  generatedAt: string
  generatedBy: string
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'EXPIRED'
  overview: {
    totalShifts: number
    totalWorkingHours: number
    totalEmployees: number
    totalDepartments: number
    analysisTimespan: number
    dataQuality: number
    completenessRate: number
  }
  utilizationAnalysis: {
    overall: {
      plannedHours: number
      actualHours: number
      utilizationRate: number
      efficiency: number
      idleTime: number
      overtimeHours: number
      undertimeHours: number
    }
    byShift: {
      shiftId: string
      shiftName: string
      shiftType: string
      plannedHours: number
      actualHours: number
      utilizationRate: number
      assignmentCount: number
      completionRate: number
      averageWorkingHours: number
    }[]
    byDepartment: {
      departmentId: string
      departmentName: string
      plannedHours: number
      actualHours: number
      utilizationRate: number
      efficiency: number
      staffCount: number
      shiftCount: number
    }[]
    byEmployee: {
      employeeId: string
      employeeName: string
      employeeNo: string
      departmentName: string
      positionName: string
      plannedHours: number
      actualHours: number
      utilizationRate: number
      shiftsAssigned: number
      shiftsCompleted: number
      overtime: number
      absentHours: number
    }[]
  }
  efficiencyMetrics: {
    productivity: {
      outputPerHour: number
      qualityScore: number
      efficiencyIndex: number
      performanceRating: number
    }
    timeManagement: {
      punctualityRate: number
      averageEarlyArrival: number
      averageLateArrival: number
      averageEarlyDeparture: number
      averageOvertime: number
    }
    workload: {
      averageWorkload: number
      workloadVariation: number
      peakWorkloadTime: string
      lowWorkloadTime: string
      workloadDistribution: {
        timeSlot: string
        workload: number
        efficiency: number
      }[]
    }
  }
  costAnalysis: {
    totalCost: number
    costPerHour: number
    costPerEmployee: number
    costBreakdown: {
      baseSalary: number
      overtimeCost: number
      benefitsCost: number
      additionalCost: number
      penaltyCost: number
    }
    costComparison: {
      previousPeriod: number
      budgetComparison: number
      industryAverage: number
      variance: number
      variancePercentage: number
    }
    costOptimization: {
      potentialSavings: number
      optimizationAreas: string[]
      recommendations: {
        area: string
        description: string
        expectedSavings: number
        implementationCost: number
        roi: number
      }[]
    }
  }
  qualityMetrics: {
    attendance: {
      attendanceRate: number
      punctualityRate: number
      absenteeismRate: number
      tardiness: number
      earlyLeaveRate: number
    }
    performance: {
      completionRate: number
      qualityScore: number
      customerSatisfaction: number
      errorRate: number
      reworkRate: number
    }
    compliance: {
      policyCompliance: number
      regulatoryCompliance: number
      safetyCompliance: number
      auditScore: number
    }
  }
  trendAnalysis: {
    utilizationTrend: {
      period: string
      utilization: number
      efficiency: number
      cost: number
      quality: number
    }[]
    seasonalPatterns: {
      month: string
      utilization: number
      efficiency: number
      cost: number
      variance: number
    }[]
    workloadPatterns: {
      dayOfWeek: string
      hour: number
      workload: number
      efficiency: number
      cost: number
    }[]
    performanceTrends: {
      metric: string
      trend: 'IMPROVING' | 'STABLE' | 'DECLINING'
      changeRate: number
      projection: number
      confidence: number
    }[]
  }
  comparativeAnalysis: {
    departmentComparison: {
      departmentId: string
      departmentName: string
      utilization: number
      efficiency: number
      cost: number
      quality: number
      ranking: number
    }[]
    shiftComparison: {
      shiftId: string
      shiftName: string
      utilization: number
      efficiency: number
      cost: number
      quality: number
      ranking: number
    }[]
    employeeComparison: {
      employeeId: string
      employeeName: string
      utilization: number
      efficiency: number
      performance: number
      ranking: number
    }[]
  }
  insights: {
    keyFindings: {
      finding: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      category: 'UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY' | 'COMPLIANCE'
      description: string
      recommendation: string
    }[]
    anomalies: {
      anomalyType: 'UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY' | 'PATTERN'
      severity: 'HIGH' | 'MEDIUM' | 'LOW'
      description: string
      affectedArea: string
      possibleCauses: string[]
      suggestedActions: string[]
    }[]
    opportunities: {
      opportunityType: 'OPTIMIZATION' | 'IMPROVEMENT' | 'EXPANSION' | 'REDUCTION'
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      description: string
      potentialBenefit: number
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
    }[]
  }
  predictions: {
    utilizationForecast: {
      period: string
      predictedUtilization: number
      confidence: number
      factors: string[]
    }[]
    costForecast: {
      period: string
      predictedCost: number
      confidence: number
      factors: string[]
    }[]
    demandForecast: {
      period: string
      predictedDemand: number
      confidence: number
      factors: string[]
    }[]
  }
}

// 班次统计配置接口
export interface AttendanceShiftAnalyticsConfig {
  configId: string
  configName: string
  configType: 'STANDARD' | 'CUSTOM' | 'TEMPLATE'
  isActive: boolean
  isDefault: boolean
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  scope: {
    departmentIds: string[]
    employeeIds: string[]
    shiftIds: string[]
    positionIds: string[]
    includeSubDepartments: boolean
  }
  metrics: {
    includedMetrics: ('UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY' | 'COMPLIANCE')[]
    customMetrics: {
      metricName: string
      metricType: 'PERCENTAGE' | 'NUMBER' | 'CURRENCY' | 'TIME'
      calculation: string
      description: string
    }[]
    weightings: {
      utilization: number
      efficiency: number
      cost: number
      quality: number
      compliance: number
    }
  }
  thresholds: {
    utilizationThreshold: {
      excellent: number
      good: number
      fair: number
      poor: number
    }
    efficiencyThreshold: {
      excellent: number
      good: number
      fair: number
      poor: number
    }
    costThreshold: {
      excellent: number
      good: number
      fair: number
      poor: number
    }
    qualityThreshold: {
      excellent: number
      good: number
      fair: number
      poor: number
    }
  }
  reporting: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
    recipients: string[]
    format: 'PDF' | 'EXCEL' | 'HTML' | 'JSON'
    includeCharts: boolean
    includeTrends: boolean
    includeComparisons: boolean
    includeRecommendations: boolean
  }
  alerts: {
    enableAlerts: boolean
    thresholds: {
      utilizationAlert: number
      efficiencyAlert: number
      costAlert: number
      qualityAlert: number
    }
    recipients: string[]
    frequency: 'IMMEDIATE' | 'DAILY' | 'WEEKLY'
  }
}

// 班次统计报表接口
export interface AttendanceShiftReport {
  reportId: string
  reportName: string
  reportType: 'SUMMARY' | 'DETAILED' | 'COMPARISON' | 'TREND' | 'FORECAST'
  period: [string, string]
  scope: {
    departmentIds: string[]
    employeeIds: string[]
    shiftIds: string[]
  }
  generatedAt: string
  generatedBy: string
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'EXPIRED'
  format: 'PDF' | 'EXCEL' | 'HTML' | 'JSON'
  downloadUrl?: string
  expiryDate?: string
  sections: {
    sectionId: string
    sectionName: string
    sectionType: 'CHART' | 'TABLE' | 'SUMMARY' | 'TEXT'

    content: unknown
    order: number
  }[]
  summary: {
    totalShifts: number
    totalHours: number
    totalEmployees: number
    keyMetrics: {
      metricName: string
      value: number
      unit: string
      trend: 'UP' | 'DOWN' | 'STABLE'
      change: number
    }[]
    topPerformers: {
      category: string
      name: string
      value: number
      unit: string
    }[]
    bottomPerformers: {
      category: string
      name: string
      value: number
      unit: string
    }[]
  }
  charts: {
    chartId: string
    chartName: string
    chartType: 'LINE' | 'BAR' | 'PIE' | 'SCATTER' | 'HEATMAP'

    data: unknown

    config: unknown
  }[]
  recommendations: {
    priority: 'HIGH' | 'MEDIUM' | 'LOW'
    category: 'UTILIZATION' | 'EFFICIENCY' | 'COST' | 'QUALITY'
    description: string
    action: string
    expectedBenefit: string
    timeline: string
  }[]
}

// 班次统计导出接口
export interface AttendanceShiftAnalyticsExport {
  exportId: string
  exportName: string
  exportType: 'DATA' | 'REPORT' | 'ANALYSIS' | 'DASHBOARD'
  format: 'EXCEL' | 'CSV' | 'PDF' | 'JSON'
  scope: {
    departmentIds: string[]
    employeeIds: string[]
    shiftIds: string[]
    period: [string, string]
  }
  includeData: {
    rawData: boolean
    aggregatedData: boolean
    charts: boolean
    trends: boolean
    comparisons: boolean
    recommendations: boolean
  }
  customization: {
    fields: string[]
    filters: {
      field: string
      operator: 'EQUALS' | 'CONTAINS' | 'GREATER_THAN' | 'LESS_THAN' | 'BETWEEN'

      value: unknown
    }[]
    sorting: {
      field: string
      order: 'ASC' | 'DESC'
    }[]
    grouping: string[]
  }
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  progress: number
  requestedAt: string
  requestedBy: string
  completedAt?: string
  downloadUrl?: string
  expiryDate?: string
  fileSize?: number
  error?: string
}

// 班次统计仪表板接口
export interface AttendanceShiftDashboard {
  dashboardId: string
  dashboardName: string
  dashboardType: 'EXECUTIVE' | 'MANAGER' | 'ANALYST' | 'OPERATIONAL'
  ownerId: string
  ownerName: string
  isPublic: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
  lastViewedAt: string
  layout: {
    columns: number
    rows: number
    responsive: boolean
  }
  widgets: {
    widgetId: string
    widgetName: string
    widgetType: 'METRIC' | 'CHART' | 'TABLE' | 'GAUGE' | 'ALERT'
    position: {
      x: number
      y: number
      width: number
      height: number
    }
    configuration: {
      dataSource: string
      metrics: string[]
      filters: {
        field: string

        value: unknown
      }[]
      refreshInterval: number
      chartType?: 'LINE' | 'BAR' | 'PIE' | 'SCATTER' | 'HEATMAP'
      thresholds?: {
        warning: number
        critical: number
      }
    }

    data: unknown
    lastUpdated: string
  }[]
  filters: {
    filterId: string
    filterName: string
    filterType: 'DEPARTMENT' | 'EMPLOYEE' | 'SHIFT' | 'DATE' | 'POSITION'

    values: unknown[]
    isActive: boolean
  }[]
  sharing: {
    shareId: string
    shareType: 'VIEW' | 'EDIT' | 'ADMIN'
    sharedWith: string[]
    expiryDate?: string
    publicUrl?: string
  }[]
  alerts: {
    alertId: string
    alertName: string
    condition: string
    threshold: number
    isActive: boolean
    recipients: string[]
    lastTriggered?: string
  }[]
}

// 请假类型接口
export interface AttendanceLeaveType {
  id: string
  name: string
  code: string
  category:
    | 'ANNUAL'
    | 'SICK'
    | 'PERSONAL'
    | 'MATERNITY'
    | 'PATERNITY'
    | 'BEREAVEMENT'
    | 'STUDY'
    | 'COMPENSATORY'
    | 'UNPAID'
    | 'OTHER'
  description: string
  isActive: boolean
  isPaid: boolean
  maxDaysPerYear: number
  maxDaysPerRequest: number
  minDaysPerRequest: number
  requireApproval: boolean
  approvalLevels: number
  carryoverAllowed: boolean
  carryoverMaxDays: number
  canBeNegative: boolean
  requiredDocuments: string[]
  applicableEmployees: {
    employeeTypes: string[]
    departments: string[]
    positions: string[]
    minTenure: number
    gender?: 'MALE' | 'FEMALE'
  }
  advanceNotice: {
    minDays: number
    maxDays: number
    exceptions: string[]
  }
  restrictions: {
    blackoutPeriods: {
      startDate: string
      endDate: string
      reason: string
    }[]
    maxConsecutiveDays: number
    minGapBetweenRequests: number
    yearlyQuota: number
    monthlyQuota: number
  }
  calculations: {
    accrualMethod: 'MONTHLY' | 'ANNUAL' | 'PRORATED' | 'MANUAL'
    accrualRate: number
    accrualStartDate: 'HIRE_DATE' | 'YEAR_START' | 'CUSTOM'
    roundingRule: 'UP' | 'DOWN' | 'NEAREST'
  }
  settings: {
    allowHalfDay: boolean
    allowHourly: boolean
    workingDaysOnly: boolean
    excludeHolidays: boolean
    excludeWeekends: boolean
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假申请接口
export interface AttendanceLeaveRequest {
  id: string
  requestNo: string
  employeeId: string
  employeeName: string
  employeeNo: string
  departmentId: string
  departmentName: string
  positionId: string
  positionName: string
  leaveTypeId: string
  leaveTypeName: string
  leaveTypeCode: string
  requestType: 'FULL_DAY' | 'HALF_DAY' | 'HOURLY'
  startDate: string
  endDate: string
  startTime?: string
  endTime?: string
  totalDays: number
  totalHours?: number
  reason: string
  emergencyContact?: {
    name: string
    relationship: string
    phone: string
  }
  workHandover?: {
    handoverTo: string
    handoverDetails: string
    urgentMatters: string[]
  }
  attachments: {
    id: string
    fileName: string
    fileType: string
    fileSize: number
    uploadedAt: string
    url: string
  }[]
  status: 'DRAFT' | 'SUBMITTED' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED' | 'WITHDRAWN'
  currentStep: number
  approvalHistory: {
    stepNo: number
    approverId: string
    approverName: string
    approverRole: string
    action: 'APPROVE' | 'REJECT' | 'RETURN' | 'DELEGATE'
    actionDate: string
    comments?: string
    delegatedTo?: string
  }[]
  balanceInfo: {
    currentBalance: number
    requestedDays: number
    remainingBalance: number
    pendingRequests: number
    isOverdrawn: boolean
  }
  impact: {
    workingDays: number
    affectedProjects: string[]
    coverageArrangement: {
      coveredBy: string
      coveragePeriod: [string, string]
      tasks: string[]
    }[]
    teamImpact: 'HIGH' | 'MEDIUM' | 'LOW'
  }
  submittedAt: string
  processedAt?: string
  approvedAt?: string
  rejectedAt?: string
  withdrawnAt?: string
  cancelledAt?: string
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假申请提交数据接口
export interface AttendanceLeaveRequestSubmission {
  leaveTypeId: string
  requestType: 'FULL_DAY' | 'HALF_DAY' | 'HOURLY'
  startDate: string
  endDate: string
  startTime?: string
  endTime?: string
  reason: string
  emergencyContact?: {
    name: string
    relationship: string
    phone: string
  }
  workHandover?: {
    handoverTo: string
    handoverDetails: string
    urgentMatters: string[]
  }
  attachmentIds?: string[]
  isDraft?: boolean
  autoSubmit?: boolean
  notifyManager?: boolean
  notifyTeam?: boolean
}

// 请假申请搜索参数接口
export interface AttendanceLeaveRequestSearchParams {
  page?: number
  limit?: number
  employeeId?: string
  employeeName?: string
  employeeNo?: string
  departmentId?: string
  departmentIds?: string[]
  positionId?: string
  leaveTypeId?: string
  leaveTypeIds?: string[]
  status?: (
    | 'DRAFT'
    | 'SUBMITTED'
    | 'PENDING'
    | 'APPROVED'
    | 'REJECTED'
    | 'CANCELLED'
    | 'WITHDRAWN'
  )[]
  requestType?: ('FULL_DAY' | 'HALF_DAY' | 'HOURLY')[]
  startDate?: string
  endDate?: string
  submittedDateRange?: [string, string]
  approvedDateRange?: [string, string]
  sortBy?: 'submittedAt' | 'startDate' | 'endDate' | 'status' | 'employeeName'
  sortOrder?: 'ASC' | 'DESC'
  includeSubDepartments?: boolean
  managedByMe?: boolean
  myRequests?: boolean
  pendingApproval?: boolean
  keyword?: string
}

// 请假申请验证结果接口
export interface AttendanceLeaveRequestValidation {
  isValid: boolean
  errors: {
    field: string
    message: string
    code: string
  }[]
  warnings: {
    field: string
    message: string
    code: string
  }[]
  balanceCheck: {
    isValid: boolean
    currentBalance: number
    requestedDays: number
    remainingBalance: number
    isOverdrawn: boolean
    overdrawAmount?: number
  }
  conflictCheck: {
    hasConflicts: boolean
    conflicts: {
      conflictType: 'OVERLAP' | 'BLACKOUT' | 'QUOTA_EXCEEDED' | 'TEAM_COVERAGE'
      description: string
      suggestions: string[]
    }[]
  }
  policyCheck: {
    isCompliant: boolean
    violations: {
      rule: string
      description: string
      severity: 'ERROR' | 'WARNING'
    }[]
  }
  workflowCheck: {
    approvers: {
      stepNo: number
      approverId: string
      approverName: string
      approverRole: string
      isAvailable: boolean
      expectedDuration: number
    }[]
    estimatedProcessingTime: number
  }
  recommendations: {
    type: 'ALTERNATIVE_DATES' | 'DIFFERENT_TYPE' | 'PARTIAL_APPROVAL'
    description: string

    details: unknown
  }[]
}

// 请假申请批量操作接口
export interface AttendanceLeaveRequestBatchOperation {
  operationType: 'APPROVE' | 'REJECT' | 'CANCEL' | 'WITHDRAW' | 'EXPORT' | 'NOTIFY'
  targetIds: string[]
  criteria?: {
    departmentIds?: string[]
    leaveTypeIds?: string[]
    status?: string[]
    dateRange?: [string, string]
  }
  parameters?: {
    comments?: string
    reason?: string
    notifyEmployees?: boolean
    exportFormat?: 'EXCEL' | 'PDF' | 'CSV'
    emailTemplate?: string
  }
  executedBy: string
  executedAt: string
  batchId: string
}

// 请假申请批量操作结果接口
export interface AttendanceLeaveRequestBatchResult {
  batchId: string
  operationType: string
  status: 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'PARTIAL'
  totalCount: number
  successCount: number
  failedCount: number
  skippedCount: number
  results: {
    requestId: string
    status: 'SUCCESS' | 'FAILED' | 'SKIPPED'
    message?: string
    error?: string
  }[]
  summary: {
    processedItems: number
    successRate: number
    duration: number
    downloadUrl?: string
  }
  conflicts: {
    requestId: string
    conflictType: string
    description: string
    resolution: string
  }[]
  notifications: {
    notificationType: 'EMAIL' | 'SMS' | 'SYSTEM'
    recipient: string
    status: 'SENT' | 'FAILED' | 'PENDING'
    sentAt?: string
  }[]
  auditLog: {
    action: string
    details: string
    timestamp: string
    userId: string
  }[]
  startedAt: string
  completedAt?: string
  executedBy: string
}

// 请假审批流程接口
export interface AttendanceLeaveApprovalFlow {
  id: string
  flowName: string
  flowCode: string
  leaveTypeId: string
  leaveTypeName: string
  departmentIds: string[]
  employeeTypes: string[]
  isActive: boolean
  isDefault: boolean
  steps: {
    stepNo: number
    stepName: string
    stepType: 'DIRECT_MANAGER' | 'HR' | 'DEPARTMENT_HEAD' | 'LEADERSHIP' | 'CUSTOM'
    approverRoles: string[]
    approverIds?: string[]
    isRequired: boolean
    allowDelegate: boolean
    autoApprove: boolean
    autoApproveConditions?: {
      maxDays?: number
      maxHours?: number
      balanceThreshold?: number
      specificDates?: string[]
    }
    timeoutHours: number
    escalationAction: 'AUTO_APPROVE' | 'ESCALATE' | 'REJECT'
    escalationTo?: string
    notifications: {
      onSubmit: boolean
      onApprove: boolean
      onReject: boolean
      onTimeout: boolean
      recipients: ('APPLICANT' | 'APPROVER' | 'NEXT_APPROVER' | 'HR' | 'MANAGER')[]
    }
  }[]
  conditions: {
    minDays?: number
    maxDays?: number
    requireDocuments?: string[]
    blackoutPeriods?: {
      startDate: string
      endDate: string
      reason: string
    }[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假审批决策接口
export interface AttendanceLeaveApprovalDecision {
  requestId: string
  stepNo: number
  approverId: string
  approverName: string
  approverRole: string
  action: 'APPROVE' | 'REJECT' | 'RETURN' | 'DELEGATE' | 'REQUEST_INFO'
  decision: 'APPROVED' | 'REJECTED' | 'RETURNED' | 'DELEGATED' | 'INFO_REQUESTED'
  comments?: string
  attachments?: {
    id: string
    fileName: string
    fileType: string
    fileSize: number
    uploadedAt: string
    url: string
  }[]
  conditions?: {
    conditionalApproval: boolean
    requiredActions: string[]
    deadlineDate?: string
    followUpRequired?: boolean
  }
  delegation?: {
    delegatedTo: string
    delegatedToName: string
    delegationReason: string
    delegationPeriod?: [string, string]
  }
  infoRequest?: {
    requestedInfo: string[]
    responseDeadline: string
    responseRequired: boolean
  }
  decisionDate: string
  effectiveDate?: string
  ipAddress?: string
  userAgent?: string
  location?: string
}

// 请假审批历史接口
export interface AttendanceLeaveApprovalHistory {
  requestId: string
  requestNo: string
  employeeId: string
  employeeName: string
  leaveTypeId: string
  leaveTypeName: string
  startDate: string
  endDate: string
  totalDays: number
  currentStatus: string
  submittedAt: string
  lastActionAt: string
  timeline: {
    stepNo: number
    stepName: string
    approverId: string
    approverName: string
    action: string
    decision: string
    actionDate: string
    comments?: string
    duration: number
    isCompleted: boolean
  }[]
  flowSummary: {
    totalSteps: number
    completedSteps: number
    currentStep: number
    averageProcessingTime: number
    isOnTime: boolean
    escalationCount: number
  }
  notifications: {
    notificationId: string
    recipient: string
    type: 'EMAIL' | 'SMS' | 'SYSTEM'
    subject: string
    sentAt: string
    status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED'
  }[]
}

// 请假审批权限接口
export interface AttendanceLeaveApprovalPermission {
  userId: string
  userName: string
  roleId: string
  roleName: string
  departmentIds: string[]
  permissions: {
    canApproveLeave: boolean
    canRejectLeave: boolean
    canDelegateApproval: boolean
    canViewAllRequests: boolean
    canModifyApprovalFlow: boolean
    canOverrideDecision: boolean
    maxApprovalAmount?: number
    maxApprovalDays?: number
    leaveTypeRestrictions?: string[]
    departmentRestrictions?: string[]
  }
  approvalScope: {
    scopeType: 'DIRECT_REPORTS' | 'DEPARTMENT' | 'ORGANIZATION' | 'CUSTOM'
    includedEmployees?: string[]
    excludedEmployees?: string[]
    includeSubDepartments: boolean
  }
  delegations: {
    delegationId: string
    delegatedTo: string
    delegatedToName: string
    startDate: string
    endDate: string
    isActive: boolean
    reason: string
    permissions: string[]
  }[]
  workingHours: {
    timezone: string
    workingDays: string[]
    workingHours: [string, string]
    holidayCalendar: string
  }
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  createdAt: string
  updatedAt: string
}

// 请假审批通知接口
export interface AttendanceLeaveApprovalNotification {
  id: string
  requestId: string
  requestNo: string
  notificationType:
    | 'SUBMIT'
    | 'APPROVE'
    | 'REJECT'
    | 'RETURN'
    | 'TIMEOUT'
    | 'ESCALATION'
    | 'DELEGATE'
    | 'INFO_REQUEST'
  templateId: string
  templateName: string
  recipients: {
    recipientType: 'APPLICANT' | 'APPROVER' | 'NEXT_APPROVER' | 'HR' | 'MANAGER' | 'TEAM'
    recipientId: string
    recipientName: string
    recipientEmail?: string
    recipientPhone?: string
  }[]
  channels: ('EMAIL' | 'SMS' | 'SYSTEM' | 'PUSH' | 'WECHAT')[]
  content: {
    subject: string
    body: string
    variables: {
      [key: string]: unknown
    }
  }
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  scheduledAt?: string
  sentAt?: string
  deliveredAt?: string
  readAt?: string
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED' | 'CANCELLED'
  retryCount: number
  maxRetries: number
  error?: string
  metadata: {
    requestInfo: {
      employeeName: string
      leaveType: string
      startDate: string
      endDate: string
      totalDays: number
    }
    approvalInfo?: {
      currentStep: number
      totalSteps: number
      nextApprover?: string
      deadline?: string
    }
  }
  createdAt: string
  updatedAt: string
}

// 请假审批委托接口
export interface AttendanceLeaveApprovalDelegation {
  id: string
  delegatorId: string
  delegatorName: string
  delegateeId: string
  delegateeName: string
  delegationType: 'TEMPORARY' | 'PERMANENT' | 'PARTIAL' | 'EMERGENCY'
  scope: {
    leaveTypes?: string[]
    departments?: string[]
    employees?: string[]
    maxDays?: number
    maxAmount?: number
  }
  startDate: string
  endDate?: string
  reason: string
  status: 'ACTIVE' | 'INACTIVE' | 'EXPIRED' | 'REVOKED'
  permissions: {
    canApprove: boolean
    canReject: boolean
    canDelegate: boolean
    canViewDetails: boolean
    requiresConfirmation: boolean
  }
  conditions: {
    requiresDelegatorNotification: boolean
    requiresJustification: boolean
    autoExpiry: boolean
    maxConcurrentDelegations?: number
  }
  usage: {
    totalDelegatedRequests: number
    approvedRequests: number
    rejectedRequests: number
    lastUsedAt?: string
  }
  notifications: {
    notifyDelegator: boolean
    notifyDelegatee: boolean
    notifyOnAction: boolean
    notifyOnExpiry: boolean
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  revokedAt?: string
  revokedBy?: string
  revokedReason?: string
}

// 请假审批统计接口
export interface AttendanceLeaveApprovalStatistics {
  statisticsId: string
  period: [string, string]
  scope: {
    departmentIds?: string[]
    approverIds?: string[]
    leaveTypeIds?: string[]
  }
  overallMetrics: {
    totalRequests: number
    approvedRequests: number
    rejectedRequests: number
    pendingRequests: number
    withdrawnRequests: number
    approvalRate: number
    rejectionRate: number
    averageProcessingTime: number
    onTimeProcessingRate: number
  }
  approverMetrics: {
    approverId: string
    approverName: string
    totalRequests: number
    approvedCount: number
    rejectedCount: number
    averageProcessingTime: number
    onTimeRate: number
    escalationCount: number
    delegationCount: number
  }[]
  departmentMetrics: {
    departmentId: string
    departmentName: string
    totalRequests: number
    approvalRate: number
    averageProcessingTime: number
    escalationRate: number
  }[]
  leaveTypeMetrics: {
    leaveTypeId: string
    leaveTypeName: string
    totalRequests: number
    approvalRate: number
    averageProcessingTime: number
    complexityScore: number
  }[]
  timeAnalysis: {
    hourlyDistribution: {
      hour: number
      requestCount: number
      averageProcessingTime: number
    }[]
    dailyDistribution: {
      dayOfWeek: string
      requestCount: number
      averageProcessingTime: number
    }[]
    monthlyTrends: {
      month: string
      requestCount: number
      approvalRate: number
      processingTime: number
    }[]
  }
  bottleneckAnalysis: {
    slowestSteps: {
      stepName: string
      averageTime: number
      maxTime: number
      frequentDelays: number
    }[]
    escalationPatterns: {
      stepName: string
      escalationRate: number
      reasons: string[]
    }[]
    approverWorkload: {
      approverId: string
      approverName: string
      workloadScore: number
      peakHours: string[]
      recommendedActions: string[]
    }[]
  }
  generatedAt: string
  generatedBy: string
}

// 请假余额接口
export interface AttendanceLeaveBalance {
  id: string
  employeeId: string
  employeeName: string
  employeeNo: string
  departmentId: string
  departmentName: string
  positionId: string
  positionName: string
  leaveTypeId: string
  leaveTypeName: string
  leaveTypeCode: string
  category:
    | 'ANNUAL'
    | 'SICK'
    | 'PERSONAL'
    | 'MATERNITY'
    | 'PATERNITY'
    | 'BEREAVEMENT'
    | 'STUDY'
    | 'COMPENSATORY'
    | 'UNPAID'
    | 'OTHER'
  balanceYear: number
  currentBalance: number
  totalEntitlement: number
  usedBalance: number
  pendingBalance: number
  carryoverBalance: number
  adjustmentBalance: number
  expiryDate?: string
  lastAccrualDate: string
  nextAccrualDate?: string
  accrualRate: number
  accrualFrequency: 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY' | 'BIANNUALLY'
  maxCarryover: number
  minBalance: number
  isActive: boolean
  isOverdrawn: boolean
  overdraftLimit?: number
  rules: {
    allowNegative: boolean
    autoAccrual: boolean
    carryoverAllowed: boolean
    prorated: boolean
    roundingRule: 'UP' | 'DOWN' | 'NEAREST'
  }
  alerts: {
    lowBalanceThreshold: number
    expiryWarningDays: number
    enableAlerts: boolean
  }
  history: {
    lastUpdatedAt: string
    lastUpdatedBy: string
    lastTransactionId?: string
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假余额变更记录接口
export interface AttendanceLeaveBalanceTransaction {
  id: string
  transactionNo: string
  employeeId: string
  employeeName: string
  leaveTypeId: string
  leaveTypeName: string
  transactionType:
    | 'ACCRUAL'
    | 'USAGE'
    | 'ADJUSTMENT'
    | 'CARRYOVER'
    | 'EXPIRY'
    | 'TRANSFER'
    | 'COMPENSATION'
    | 'CORRECTION'
  transactionSource: 'SYSTEM' | 'MANUAL' | 'REQUEST' | 'APPROVAL' | 'IMPORT' | 'API' | 'BATCH'
  amount: number
  balanceBefore: number
  balanceAfter: number
  description: string
  reason?: string
  referenceId?: string
  referenceType?: 'LEAVE_REQUEST' | 'ADJUSTMENT' | 'POLICY' | 'CORRECTION' | 'TRANSFER'
  relatedRequestId?: string
  relatedTransactionId?: string
  approvedBy?: string
  approvedAt?: string
  effectiveDate: string
  expiryDate?: string
  isReversed: boolean
  reversedBy?: string
  reversedAt?: string
  reversalReason?: string
  reversalTransactionId?: string
  metadata: {
    originalBalance?: number
    calculationDetails?: {
      formula: string

      parameters: { [key: string]: unknown }
      result: number
    }
    systemContext?: {
      policyVersion: string
      ruleVersion: string
      calculationEngine: string
    }
  }
  auditInfo: {
    ipAddress?: string
    userAgent?: string
    location?: string
    sessionId?: string
  }
  createdAt: string
  createdBy: string
}

// 请假余额调整接口
export interface AttendanceLeaveBalanceAdjustment {
  id: string
  adjustmentNo: string
  adjustmentType: 'INCREASE' | 'DECREASE' | 'RESET' | 'TRANSFER'
  reason:
    | 'POLICY_CHANGE'
    | 'ERROR_CORRECTION'
    | 'MANUAL_ADJUSTMENT'
    | 'SYSTEM_MIGRATION'
    | 'SPECIAL_GRANT'
    | 'PENALTY'
    | 'OTHER'
  targetScope: {
    scopeType: 'EMPLOYEE' | 'DEPARTMENT' | 'POSITION' | 'LEAVE_TYPE' | 'BATCH'
    employeeIds?: string[]
    departmentIds?: string[]
    positionIds?: string[]
    leaveTypeIds?: string[]
    criteria?: {
      hireDate?: [string, string]
      balanceRange?: [number, number]
      category?: string[]
    }
  }
  adjustmentDetails: {
    amount?: number
    percentage?: number
    formula?: string
    newBalance?: number
    targetBalance?: number
    maxAdjustment?: number
    minAdjustment?: number
  }
  transferDetails?: {
    fromLeaveTypeId: string
    toLeaveTypeId: string
    conversionRate: number
    maxTransferAmount?: number
  }
  batchDetails?: {
    totalTargets: number
    processedCount: number
    successCount: number
    failedCount: number
    totalAmount: number
  }
  approvalRequired: boolean
  approvedBy?: string
  approvedAt?: string
  approvalComments?: string
  status:
    | 'DRAFT'
    | 'PENDING_APPROVAL'
    | 'APPROVED'
    | 'PROCESSING'
    | 'COMPLETED'
    | 'FAILED'
    | 'CANCELLED'
  scheduledAt?: string
  executedAt?: string
  completedAt?: string
  errors?: {
    employeeId: string
    employeeName: string
    error: string
    details: string
  }[]
  notifications: {
    notifyEmployees: boolean
    notifyManagers: boolean
    notifyHR: boolean
    customRecipients?: string[]
    emailTemplate?: string
  }
  auditTrail: {
    action: string
    timestamp: string
    userId: string
    details: string
  }[]
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假余额预警接口
export interface AttendanceLeaveBalanceAlert {
  id: string
  alertType:
    | 'LOW_BALANCE'
    | 'EXPIRY_WARNING'
    | 'OVERDRAFT'
    | 'UNUSUAL_USAGE'
    | 'POLICY_VIOLATION'
    | 'SYSTEM_ERROR'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  employeeId: string
  employeeName: string
  employeeNo: string
  departmentId: string
  departmentName: string
  leaveTypeId: string
  leaveTypeName: string
  currentBalance: number
  threshold?: number
  triggerValue?: number
  message: string
  description: string
  recommendations: string[]
  actionRequired: boolean
  autoActions?: {
    blockNewRequests: boolean
    notifyManager: boolean
    escalateToHR: boolean
    sendReminder: boolean
  }
  relatedRequestId?: string
  relatedTransactionId?: string
  triggeredAt: string
  acknowledgedAt?: string
  acknowledgedBy?: string
  resolvedAt?: string
  resolvedBy?: string
  resolutionNotes?: string
  status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'DISMISSED' | 'EXPIRED'
  recipients: {
    recipientType: 'EMPLOYEE' | 'MANAGER' | 'HR' | 'ADMIN'
    recipientId: string
    recipientName: string
    notified: boolean
    notifiedAt?: string
  }[]
  recurrence: {
    isRecurring: boolean
    frequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY'
    lastTriggered?: string
    nextTrigger?: string
    suppressUntil?: string
  }
  metadata: {
    ruleId: string
    ruleName: string
    ruleVersion: string

    context: { [key: string]: unknown }
  }
  createdAt: string
  updatedAt: string
}

// 请假余额计算规则接口
export interface AttendanceLeaveBalanceRule {
  id: string
  ruleName: string
  ruleCode: string
  ruleType: 'ACCRUAL' | 'CARRYOVER' | 'EXPIRY' | 'ADJUSTMENT' | 'VALIDATION'
  leaveTypeIds: string[]
  applicableScope: {
    employeeTypes: string[]
    departments: string[]
    positions: string[]
    hireDate?: [string, string]
    tenure?: [number, number]
  }
  priority: number
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  accrualRules?: {
    accrualMethod: 'MONTHLY' | 'ANNUAL' | 'PRORATED' | 'MILESTONE' | 'CUSTOM'
    accrualRate: number
    accrualCap: number
    accrualStartDate: 'HIRE_DATE' | 'YEAR_START' | 'POLICY_START' | 'CUSTOM'
    waitingPeriod?: number
    prorationRule: 'DAILY' | 'MONTHLY' | 'NONE'
    roundingRule: 'UP' | 'DOWN' | 'NEAREST'
    calendar: 'FISCAL' | 'CALENDAR' | 'POLICY'
  }
  carryoverRules?: {
    allowCarryover: boolean
    maxCarryover: number
    carryoverExpiry?: number
    carryoverConditions?: string[]
    useOrder: 'FIFO' | 'LIFO' | 'EXPIRY_FIRST'
  }
  expiryRules?: {
    expiryMethod: 'ANNUAL' | 'ROLLING' | 'FIXED_DATE' | 'NEVER'
    expiryDate?: string
    warningDays: number
    autoExpire: boolean
    gracePeriod?: number
  }
  adjustmentRules?: {
    allowNegative: boolean
    overdraftLimit?: number
    autoAdjustment: boolean
    adjustmentFrequency?: 'REAL_TIME' | 'DAILY' | 'MONTHLY'
  }
  validationRules?: {
    minBalance: number
    maxBalance: number
    requestValidation: string[]
    balanceValidation: string[]
  }
  calculationFormula: {
    formula: string
    parameters: {
      name: string
      type: 'NUMBER' | 'DATE' | 'BOOLEAN' | 'STRING'

      defaultValue: unknown
      description: string
    }[]
    conditions: {
      condition: string
      formula: string
    }[]
  }
  notifications: {
    lowBalanceAlert: boolean
    expiryWarning: boolean
    overdraftAlert: boolean
    recipients: string[]
  }
  auditSettings: {
    trackChanges: boolean
    logTransactions: boolean
    requireApproval: boolean
    approvers: string[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  version: number
  changeLog: {
    version: number
    changes: string
    changedBy: string
    changedAt: string
  }[]
}

// 请假余额统计接口
export interface AttendanceLeaveBalanceStatistics {
  statisticsId: string
  reportType: 'SUMMARY' | 'DETAILED' | 'TREND' | 'ANALYSIS' | 'FORECAST'
  period: [string, string]
  scope: {
    departmentIds?: string[]
    employeeIds?: string[]
    leaveTypeIds?: string[]
    includeInactive?: boolean
  }
  overallMetrics: {
    totalEmployees: number
    totalBalance: number
    totalEntitlement: number
    totalUsed: number
    totalCarryover: number
    utilizationRate: number
    averageBalance: number
    balanceDistribution: {
      range: string
      count: number
      percentage: number
    }[]
  }
  departmentBreakdown: {
    departmentId: string
    departmentName: string
    employeeCount: number
    totalBalance: number
    averageBalance: number
    utilizationRate: number
    ranking: number
  }[]
  leaveTypeBreakdown: {
    leaveTypeId: string
    leaveTypeName: string
    totalBalance: number
    totalUsed: number
    utilizationRate: number
    averageBalance: number
    expiryRisk: number
  }[]
  riskAnalysis: {
    lowBalanceEmployees: {
      employeeId: string
      employeeName: string
      leaveTypeName: string
      currentBalance: number
      threshold: number
      riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
    }[]
    expiryRisk: {
      employeeId: string
      employeeName: string
      leaveTypeName: string
      balance: number
      expiryDate: string
      daysUntilExpiry: number
    }[]
    overdraftEmployees: {
      employeeId: string
      employeeName: string
      leaveTypeName: string
      balance: number
      overdraftAmount: number
    }[]
  }
  trends: {
    period: string
    totalBalance: number
    totalUsed: number
    newAccruals: number
    expirations: number
    adjustments: number
  }[]
  forecasting: {
    projectedUsage: {
      month: string
      projectedUsage: number
      confidence: number
    }[]
    balanceProjection: {
      month: string
      projectedBalance: number
      confidence: number
    }[]
    recommendations: {
      type: 'POLICY_ADJUSTMENT' | 'PROCESS_IMPROVEMENT' | 'TRAINING_NEED' | 'SYSTEM_OPTIMIZATION'
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      description: string
      expectedImpact: string
    }[]
  }
  generatedAt: string
  generatedBy: string
}

// 请假统计报表接口
export interface AttendanceLeaveReport {
  id: string
  reportName: string
  reportCode: string
  reportType: 'SUMMARY' | 'DETAILED' | 'ANALYTICAL' | 'TREND' | 'COMPLIANCE' | 'CUSTOM'
  reportCategory:
    | 'EMPLOYEE'
    | 'DEPARTMENT'
    | 'LEAVE_TYPE'
    | 'PERIOD'
    | 'APPROVAL'
    | 'BALANCE'
    | 'USAGE'
  description?: string
  isActive: boolean
  isScheduled: boolean
  visibility: 'PUBLIC' | 'PRIVATE' | 'DEPARTMENT' | 'ROLE_BASED'
  scope: {
    departmentIds?: string[]
    employeeIds?: string[]
    leaveTypeIds?: string[]
    positionIds?: string[]
    includeSubDepartments: boolean
    includeInactiveEmployees: boolean
  }
  timeRange: {
    rangeType: 'FIXED' | 'RELATIVE' | 'CUSTOM'
    fixedRange?: [string, string]
    relativeRange?: {
      unit: 'DAY' | 'WEEK' | 'MONTH' | 'QUARTER' | 'YEAR'
      value: number
      direction: 'PAST' | 'FUTURE' | 'CURRENT'
    }
    customRange?: {
      startDate: string
      endDate: string
    }
  }
  dimensions: {
    groupBy: ('EMPLOYEE' | 'DEPARTMENT' | 'LEAVE_TYPE' | 'MONTH' | 'QUARTER' | 'YEAR' | 'STATUS')[]
    metrics: (
      | 'TOTAL_REQUESTS'
      | 'TOTAL_DAYS'
      | 'APPROVAL_RATE'
      | 'AVERAGE_DAYS'
      | 'BALANCE_USAGE'
      | 'COST_ANALYSIS'
    )[]
    filters: {
      field: string
      operator:
        | 'EQUALS'
        | 'NOT_EQUALS'
        | 'CONTAINS'
        | 'GREATER_THAN'
        | 'LESS_THAN'
        | 'BETWEEN'
        | 'IN'

      value: unknown
    }[]
    sorting: {
      field: string
      order: 'ASC' | 'DESC'
    }[]
  }
  visualization: {
    chartTypes: ('TABLE' | 'BAR' | 'LINE' | 'PIE' | 'SCATTER' | 'HEATMAP')[]
    layout: {
      sections: {
        sectionId: string
        sectionName: string
        sectionType: 'SUMMARY' | 'CHART' | 'TABLE' | 'KPI'
        position: number

        config: unknown
      }[]
    }
    styling: {
      theme: string
      colorScheme: string[]
      fontSize: string
      showLegend: boolean
      showDataLabels: boolean
    }
  }
  outputFormats: ('PDF' | 'EXCEL' | 'CSV' | 'HTML' | 'JSON')[]
  schedule?: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
    dayOfWeek?: number
    dayOfMonth?: number
    time: string
    timezone: string
    isEnabled: boolean
    lastRunAt?: string
    nextRunAt?: string
  }
  distribution: {
    recipients: {
      recipientType: 'EMPLOYEE' | 'MANAGER' | 'HR' | 'EMAIL'
      recipientId?: string
      recipientEmail?: string
      recipientName: string
    }[]
    emailTemplate?: string
    subject?: string
    message?: string
  }
  permissions: {
    viewPermissions: string[]
    editPermissions: string[]
    deletePermissions: string[]
    sharePermissions: string[]
  }
  metadata: {
    tags: string[]
    category: string
    priority: 'LOW' | 'MEDIUM' | 'HIGH'
    estimatedDuration: number
    dataSource: string
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  version: number
  lastGeneratedAt?: string
  lastGeneratedBy?: string
}

// 请假报表实例接口
export interface AttendanceLeaveReportInstance {
  id: string
  reportId: string
  reportName: string
  instanceName: string
  generationType: 'MANUAL' | 'SCHEDULED' | 'API'
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'EXPIRED'
  progress: number
  currentStep?: string
  parameters: {
    timeRange: [string, string]
    scope: {
      departmentIds?: string[]
      employeeIds?: string[]
      leaveTypeIds?: string[]
    }
    customFilters?: {
      field: string

      value: unknown
    }[]
  }
  results?: {
    summary: {
      totalRecords: number
      totalEmployees: number
      totalDepartments: number
      totalLeaveTypes: number
      reportPeriod: [string, string]
    }
    data: {
      sections: {
        sectionId: string
        sectionName: string
        sectionType: string

        content: unknown
      }[]

      rawData?: unknown[]

      aggregatedData?: unknown[]
    }
    insights: {
      keyFindings: string[]
      trends: string[]
      anomalies: string[]
      recommendations: string[]
    }
  }
  files: {
    fileId: string
    fileName: string
    fileType: string
    fileSize: number
    downloadUrl: string
    expiryDate: string
  }[]
  performance: {
    executionTime: number
    dataSize: number
    queryCount: number
    cacheHitRate?: number
  }
  error?: {
    errorCode: string
    errorMessage: string
    errorDetails: string
    timestamp: string
  }
  generatedAt: string
  completedAt?: string
  generatedBy: string
  expiresAt?: string
}

// 请假报表模板接口
export interface AttendanceLeaveReportTemplate {
  id: string
  templateName: string
  templateCode: string
  templateType: 'STANDARD' | 'CUSTOM' | 'INDUSTRY'
  industry?: string
  description: string
  isBuiltIn: boolean
  isActive: boolean
  configuration: {
    reportStructure: {
      sections: {
        sectionId: string
        sectionName: string
        sectionType: 'HEADER' | 'SUMMARY' | 'DETAIL' | 'CHART' | 'FOOTER'
        isRequired: boolean

        defaultConfig: unknown
      }[]
    }
    defaultParameters: {
      timeRange: {
        defaultType: 'RELATIVE' | 'FIXED'

        defaultValue: unknown
      }
      scope: {
        defaultDepartments?: string[]
        defaultLeaveTypes?: string[]
      }
      metrics: string[]
      dimensions: string[]
    }
    styling: {
      layout: string
      theme: string
      branding?: {
        logo?: string
        colors: string[]
        fonts: string[]
      }
    }
  }
  usage: {
    usageCount: number
    lastUsedAt?: string
    popularityScore: number
    userRating?: number
  }
  samples: {
    sampleId: string
    sampleName: string

    sampleData: unknown
    thumbnail?: string
  }[]
  tags: string[]
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  version: string
}

// 请假报表配置接口
export interface AttendanceLeaveReportConfig {
  id: string
  configName: string
  configType: 'GLOBAL' | 'DEPARTMENT' | 'USER' | 'ROLE'
  targetId?: string
  targetName?: string
  settings: {
    defaultTimeRange: {
      type: 'RELATIVE' | 'FIXED'

      value: unknown
    }
    defaultOutputFormat: string
    defaultFilters: {
      field: string

      value: unknown
    }[]
    maxRecordsPerReport: number
    cacheExpiration: number
    autoRefreshInterval?: number
  }
  permissions: {
    canCreateReports: boolean
    canEditReports: boolean
    canDeleteReports: boolean
    canScheduleReports: boolean
    canShareReports: boolean
    maxReportsPerUser?: number
    allowedOutputFormats: string[]
    allowedDataSources: string[]
  }
  notifications: {
    notifyOnCompletion: boolean
    notifyOnFailure: boolean
    notifyOnSchedule: boolean
    emailTemplate: string
    recipients: string[]
  }
  branding: {
    companyLogo?: string
    companyName: string
    headerColor?: string
    footerText?: string
    watermark?: string
  }
  dataRetention: {
    retentionPeriod: number
    autoCleanup: boolean
    archiveExpiredReports: boolean
  }
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假报表订阅接口
export interface AttendanceLeaveReportSubscription {
  id: string
  subscriptionName: string
  reportId: string
  reportName: string
  subscriberId: string
  subscriberName: string
  subscriberType: 'EMPLOYEE' | 'MANAGER' | 'HR' | 'ADMIN'
  subscriptionType: 'SCHEDULED' | 'EVENT_BASED' | 'ON_DEMAND'
  isActive: boolean
  schedule?: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
    time: string
    timezone: string
    dayOfWeek?: number
    dayOfMonth?: number
    weekOfMonth?: number
  }
  eventTriggers?: {
    triggerType: 'DATA_CHANGE' | 'THRESHOLD_EXCEEDED' | 'ANOMALY_DETECTED' | 'APPROVAL_PENDING'
    triggerConditions: {
      field: string
      operator: string

      value: unknown
    }[]
    cooldownPeriod?: number
  }
  customization: {
    parameters: {
      timeRange?: unknown

      scope?: unknown

      filters?: unknown[]
    }
    outputFormat: string
    includeSummary: boolean
    includeCharts: boolean
    includeRawData: boolean
  }
  delivery: {
    deliveryMethod: 'EMAIL' | 'SYSTEM' | 'API' | 'FILE_SHARE'
    emailSettings?: {
      subject: string
      body: string
      attachReport: boolean
      sendAsLink: boolean
    }
    apiSettings?: {
      webhookUrl: string
      authToken?: string
      headers?: { [key: string]: string }
    }
  }
  history: {
    lastDeliveryAt?: string
    deliveryCount: number
    successfulDeliveries: number
    failedDeliveries: number
    lastError?: string
  }
  preferences: {
    pauseUntil?: string
    maxFileSize?: number
    compressFiles: boolean
    encryptFiles: boolean
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 请假报表分析接口
export interface AttendanceLeaveReportAnalytics {
  analyticsId: string
  reportId: string
  reportName: string
  period: [string, string]
  analysisType: 'USAGE' | 'PERFORMANCE' | 'CONTENT' | 'USER_BEHAVIOR'
  metrics: {
    usageMetrics: {
      totalGenerations: number
      uniqueUsers: number
      averageGenerationTime: number
      peakUsageHours: string[]
      popularOutputFormats: {
        format: string
        count: number
        percentage: number
      }[]
    }
    performanceMetrics: {
      averageExecutionTime: number
      averageDataSize: number
      cacheHitRate: number
      errorRate: number
      timeoutRate: number
    }
    contentMetrics: {
      averageRecordsPerReport: number
      mostUsedFilters: {
        filter: string
        usageCount: number
      }[]
      mostUsedDimensions: {
        dimension: string
        usageCount: number
      }[]
      dataQualityScore: number
    }
    userBehaviorMetrics: {
      averageReportsPerUser: number
      reportRetentionRate: number
      shareRate: number
      feedbackScore?: number
      mostActiveUsers: {
        userId: string
        userName: string
        reportCount: number
      }[]
    }
  }
  trends: {
    usageTrend: {
      period: string
      generations: number
      users: number
      change: number
    }[]
    performanceTrend: {
      period: string
      avgExecutionTime: number
      errorRate: number
      change: number
    }[]
  }
  insights: {
    topInsights: {
      insight: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      recommendation: string
    }[]
    anomalies: {
      anomalyType: string
      description: string
      severity: 'HIGH' | 'MEDIUM' | 'LOW'
      detectedAt: string
    }[]
    optimizationOpportunities: {
      area: string
      description: string
      potentialBenefit: string
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
    }[]
  }
  generatedAt: string
  generatedBy: string
}
