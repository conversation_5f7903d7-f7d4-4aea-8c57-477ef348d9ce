// 机构类型枚举（根据需求文档）
import type { SpringPageData } from '@/types/common/api'
export enum OrganizationType {
  COLLEGE = 'COLLEGE', // 学院
  DEPARTMENT = 'DEPARTMENT', // 部门
  TEACHING_DEPT = 'TEACHING_DEPT', // 教学部
  RESEARCH_INST = 'RESEARCH_INST', // 科研机构
  ADMIN_DEPT = 'ADMIN_DEPT', // 行政机构
  DIRECT_UNIT = 'DIRECT_UNIT', // 直属单位
  TEMP_ORG = 'TEMP_ORG', // 临时组织
  SCHOOL = 'SCHOOL', // 学校
  OTHER = 'OTHER' // 其他
}

// 机构状态枚举
export enum OrganizationStatus {
  ACTIVE = 'ACTIVE', // 正常
  CANCELLED = 'CANCELLED', // 已撤销
  MERGED = 'MERGED', // 已合并
  DISABLED = 'DISABLED' // 已停用
}

// 组织机构接口（根据需求文档）
export interface Organization {
  institutionId: string // 机构ID
  institutionName: string // 机构名称
  institutionCode: string // 机构编码
  institutionType: OrganizationType // 机构类型
  parentInstitutionId?: string // 上级机构ID
  parentInstitutionName?: string // 上级机构名称
  establishDate?: string // 成立日期
  effectiveDate?: string // 生效日期
  withdrawDate?: string // 撤销日期
  reasonForChange?: string // 撤销/合并原因
  solidifiedDataDescription?: string // 固化数据说明
  description?: string // 职责描述
  contactPhone?: string // 联系电话
  email?: string // 邮箱
  officeAddress?: string // 办公地址
  status: OrganizationStatus // 状态
  approvalDocNumber?: string // 批文号/依据
  version: number // 版本号
  changeImpactAssessmentJSON?: string // 变更影响评估JSON
  // 扩展字段
  level?: number // 层级深度
  path?: string // 层级路径
  sortOrder?: number // 排序号
  leaderName?: string // 负责人姓名
  leaderEmployeeId?: string // 负责人ID
  employeeCount?: number // 员工数量
  establishmentCount?: number // 编制数
  actualCount?: number // 实际人数
  // 树形结构字段
  children?: Organization[]
  hasChildren?: boolean
  expanded?: boolean
  loading?: boolean
  isLeaf?: boolean
  // 时间字段
  createTime?: string
  updateTime?: string
  createBy?: string
  updateBy?: string
}

// 创建组织请求
export interface OrganizationCreateRequest {
  orgCode: string
  orgName: string
  orgShortName?: string
  orgType: OrganizationType
  parentId?: number
  sortOrder?: number
  description?: string
  leader?: string
  phone?: string
  email?: string
  address?: string
}

// 更新组织请求
export interface OrganizationUpdateRequest extends OrganizationCreateRequest {
  status?: OrganizationStatus
}

// 组织查询请求
export interface OrganizationQueryRequest {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
  keyword?: string
  orgType?: OrganizationType
  parentId?: number
  orgLevel?: number
  status?: OrganizationStatus
  leader?: string
  includeChildren?: boolean
}

// 分页响应
// 使用通用的SpringPageData类型
export type PageResponse<T> = SpringPageData<T>

// 组织类型选项
export const organizationTypeOptions = [
  { label: '学校', value: OrganizationType.SCHOOL },
  { label: '学院', value: OrganizationType.COLLEGE },
  { label: '部门', value: OrganizationType.DEPARTMENT },
  { label: '教学部', value: OrganizationType.TEACHING_DEPT },
  { label: '科研机构', value: OrganizationType.RESEARCH_INST },
  { label: '行政机构', value: OrganizationType.ADMIN_DEPT },
  { label: '直属单位', value: OrganizationType.DIRECT_UNIT },
  { label: '临时组织', value: OrganizationType.TEMP_ORG },
  { label: '其他', value: OrganizationType.OTHER }
]

// 组织状态选项
export const organizationStatusOptions = [
  { label: '正常', value: OrganizationStatus.ACTIVE },
  { label: '已撤销', value: OrganizationStatus.CANCELLED },
  { label: '已合并', value: OrganizationStatus.MERGED },
  { label: '已停用', value: OrganizationStatus.DISABLED }
]

// 树形节点数据（用于组件）
export interface TreeNode {
  key: string // 节点唯一标识
  label: string // 节点显示名称
  data: Organization // 原始数据
  level: number // 层级
  path: string // 路径
  parent?: TreeNode // 父节点引用
  children?: TreeNode[] // 子节点
  isLeaf: boolean // 是否叶子节点
  expanded: boolean // 是否展开
  loading: boolean // 是否加载中
  disabled?: boolean // 是否禁用
  selectable?: boolean // 是否可选
  checkable?: boolean // 是否可勾选
  draggable?: boolean // 是否可拖拽
  droppable?: boolean // 是否可放置
  visible?: boolean // 是否可见
  matched?: boolean // 搜索匹配
}

// 拖拽信息
export interface DragInfo {
  node: TreeNode
  parent?: TreeNode
  index: number
  event: DragEvent
}

// 视图模式
export enum ViewMode {
  Tree = 'tree', // 树形视图
  OrgChart = 'orgChart', // 组织图
  List = 'list', // 列表视图
  MindMap = 'mindMap' // 脑图视图
}

// 组织架构图相关类型定义

// 组织架构图节点
export interface OrganizationNode {
  id: string
  orgId: number
  orgCode: string
  orgName: string
  orgShortName?: string
  orgType: OrganizationType
  parentId?: string
  level: number
  position: NodePosition
  size: NodeSize
  style: NodeStyle
  children?: OrganizationNode[]
  collapsed?: boolean
  memberCount?: number
  positionCount?: number
}

// 节点位置
export interface NodePosition {
  x: number
  y: number
}

// 节点大小
export interface NodeSize {
  width: number
  height: number
}

// 节点样式
export interface NodeStyle {
  backgroundColor: string
  borderColor: string
  textColor: string
  fontSize: number
  borderWidth: number
  borderRadius: number
}

// 组织架构图边
export interface OrganizationEdge {
  id: string
  source: string
  target: string
  type: EdgeType
  style: EdgeStyle
}

// 边类型
export enum EdgeType {
  HIERARCHY = 'HIERARCHY', // 层级关系
  COOPERATION = 'COOPERATION', // 协作关系
  REPORTING = 'REPORTING' // 汇报关系
}

// 边样式
export interface EdgeStyle {
  stroke: string
  strokeWidth: number
  strokeDasharray?: string
}

// 图表布局
export interface ChartLayout {
  type: LayoutType
  direction: LayoutDirection
  spacing: LayoutSpacing
  alignment: LayoutAlignment
}

// 布局类型
export enum LayoutType {
  TREE = 'TREE', // 树形布局
  FORCE = 'FORCE', // 力导向布局
  CIRCULAR = 'CIRCULAR', // 环形布局
  GRID = 'GRID' // 网格布局
}

// 布局方向
export enum LayoutDirection {
  TOP_BOTTOM = 'TB', // 从上到下
  BOTTOM_TOP = 'BT', // 从下到上
  LEFT_RIGHT = 'LR', // 从左到右
  RIGHT_LEFT = 'RL' // 从右到左
}

// 布局间距
export interface LayoutSpacing {
  nodeSpacing: number // 节点间距
  levelSpacing: number // 层级间距
  padding: number // 边距
}

// 布局对齐
export enum LayoutAlignment {
  CENTER = 'CENTER',
  LEFT = 'LEFT',
  RIGHT = 'RIGHT',
  TOP = 'TOP',
  BOTTOM = 'BOTTOM'
}

// 组织架构图配置
export interface OrganizationChart {
  nodes: OrganizationNode[]
  edges: OrganizationEdge[]
  layout: ChartLayout
  viewport: ChartViewport
  options: ChartOptions
}

// 图表视口
export interface ChartViewport {
  width: number
  height: number
  scale: number
  center: NodePosition
}

// 图表选项
export interface ChartOptions {
  draggable: boolean // 是否可拖拽
  zoomable: boolean // 是否可缩放
  selectable: boolean // 是否可选择
  editable: boolean // 是否可编辑
  showMinimap: boolean // 是否显示缩略图
  showGrid: boolean // 是否显示网格
  animation: boolean // 是否启用动画
}

// 组织关系管理
export interface OrganizationRelation {
  id: number
  sourceOrgId: number
  sourceOrgName?: string
  targetOrgId: number
  targetOrgName?: string
  relationType: RelationType
  relationDescription?: string
  effectiveDate: string
  expiryDate?: string
  status: RelationStatus
  createTime: string
  createBy: string
}

// 关系类型
export enum RelationType {
  PARENT_CHILD = 'PARENT_CHILD', // 上下级关系
  COOPERATION = 'COOPERATION', // 协作关系
  MATRIX = 'MATRIX', // 矩阵关系
  TEMPORARY = 'TEMPORARY' // 临时关系
}

// 关系状态
export enum RelationStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING'
}

// 组织变更申请
export interface OrganizationChangeRequest {
  id?: number
  changeType: OrganizationChangeType
  organizationId: number
  changeData: OrganizationChangeData
  reason: string
  applicantId: string
  applicantName: string
  applyTime: string
  status: ChangeRequestStatus
  approvalFlow?: ApprovalFlow[]
  effectiveDate?: string
  remark?: string
}

// 变更类型
export enum OrganizationChangeType {
  CREATE = 'CREATE', // 新建
  UPDATE = 'UPDATE', // 修改
  DELETE = 'DELETE', // 删除
  MOVE = 'MOVE', // 移动
  MERGE = 'MERGE', // 合并
  SPLIT = 'SPLIT' // 拆分
}

// 变更数据
export interface OrganizationChangeData {
  before?: Partial<Organization>
  after: Partial<Organization>
  affectedOrganizations?: number[]
}

// 变更申请状态
export enum ChangeRequestStatus {
  DRAFT = 'DRAFT', // 草稿
  SUBMITTED = 'SUBMITTED', // 已提交
  REVIEWING = 'REVIEWING', // 审核中
  APPROVED = 'APPROVED', // 已批准
  REJECTED = 'REJECTED', // 已拒绝
  CANCELLED = 'CANCELLED' // 已取消
}

// 审批流程
export interface ApprovalFlow {
  stepId: number
  stepName: string
  approverId: string
  approverName: string
  approvalTime?: string
  approvalResult?: ApprovalResult
  approvalComment?: string
}

// 审批结果
export enum ApprovalResult {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

// 编制信息
export interface Establishment {
  organizationId: string
  establishmentCount: number // 编制数
  actualCount: number // 实际人数
  vacancyCount: number // 空缺数
  pendingCount: number // 待入职人数
  overStaffCount: number // 超编人数
  establishmentFile?: string // 编制文件
  effectiveDate: string // 生效日期
  remark?: string // 备注
  details?: EstablishmentDetail[] // 编制明细
}

// 编制明细
export interface EstablishmentDetail {
  positionId: string
  positionName: string
  positionType: string // 岗位类型
  establishmentCount: number // 编制数
  actualCount: number // 实际人数
  requirements?: string // 任职要求
}

// 组织统计信息
export interface OrganizationStatistics {
  totalCount: number
  activeCount: number
  inactiveCount: number
  typeDistribution: TypeDistribution[]
  levelDistribution: LevelDistribution[]
  recentChanges: number
  pendingApprovals: number
}

// 类型分布
export interface TypeDistribution {
  type: OrganizationType
  count: number
  percentage: number
}

// 层级分布
export interface LevelDistribution {
  level: number
  count: number
  percentage: number
}

// 变更类型枚举（重新定义）
export enum ChangeType {
  CREATE = 'CREATE', // 新增
  UPDATE = 'UPDATE', // 修改
  TRANSFER = 'TRANSFER', // 划转
  CANCEL = 'CANCEL', // 撤销
  MERGE = 'MERGE', // 合并
  SPLIT = 'SPLIT' // 拆分
}

// 审批状态枚举（重新定义）
export enum ApprovalStatus {
  DRAFT = 'DRAFT', // 草稿
  PENDING = 'PENDING', // 待审批
  APPROVING = 'APPROVING', // 审批中
  APPROVED = 'APPROVED', // 已通过
  REJECTED = 'REJECTED', // 已驳回
  CANCELLED = 'CANCELLED', // 已取消
  EXECUTED = 'EXECUTED' // 已执行
}

// 变更申请
export interface ChangeRequest {
  requestId: string
  changeType: ChangeType
  institutionId: string
  institutionName?: string
  title: string
  reason: string
  description?: string
  effectiveDate: string
  status: ApprovalStatus
  createTime: string
  createBy: string
  createByName?: string
  updateTime?: string

  // 变更详情（不同类型的变更有不同的详情结构）
  changeDetails: ChangeDetails

  // 影响分析结果
  impactAssessment?: ImpactAssessment

  // 审批流程信息
  approvalFlow?: ApprovalFlowInfo
}

// 变更详情（根据不同类型有不同结构）
export interface ChangeDetails {
  // 通用字段
  beforeData?: Record<string, unknown>
  afterData?: Record<string, unknown>

  // 特定类型字段
  // 合并时的源机构列表
  sourceInstitutions?: string[]
  // 划转时的目标上级机构
  targetParentId?: string
  // 拆分时的新机构列表
  newInstitutions?: Array<{
    name: string
    code: string
    type: OrganizationType
  }>
}

// 影响分析
export interface ImpactAssessment {
  assessmentId: string
  assessTime: string

  // 影响统计
  impactSummary: {
    affectedEmployees: number // 受影响员工数
    affectedPositions: number // 受影响岗位数
    affectedSubOrgs: number // 受影响下级机构数
    pendingBusiness: number // 未完成业务数
  }

  // 详细影响列表
  impactDetails: {
    employees: Array<{
      employeeId: string
      name: string
      currentOrg: string
      impact: string
    }>
    positions: Array<{
      positionId: string
      name: string
      impact: string
    }>
    subOrganizations: Array<{
      orgId: string
      name: string
      impact: string
    }>
    business: Array<{
      businessId: string
      type: string
      description: string
    }>
  }

  // 风险评估
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  riskDescription?: string

  // 处理建议
  suggestions?: string[]
}

// 审批流程信息
export interface ApprovalFlowInfo {
  flowId: string
  flowName: string
  currentStep: number
  totalSteps: number

  // 审批步骤
  steps: ApprovalStepInfo[]

  // 当前待办人
  currentApprovers?: string[]
}

// 审批步骤信息
export interface ApprovalStepInfo {
  stepId: string
  stepName: string
  stepOrder: number
  approverType: 'USER' | 'ROLE' | 'DEPT'
  approvers: string[] // 用户ID、角色ID或部门ID列表

  // 审批记录
  approvalRecord?: {
    approverId: string
    approverName: string
    action: 'APPROVE' | 'REJECT' | 'RETURN'
    comment?: string
    approvalTime: string
  }
}

// 组织架构版本
export interface OrganizationVersion {
  versionId: string
  versionNumber: string
  versionDate: string
  description?: string
  changeCount: number // 该版本的变更数量
  createdBy: string
  createdByName?: string

  // 版本快照数据（JSON格式）
  snapshotData?: string

  // 相关变更记录ID列表
  changeLogIds?: string[]
}

// 版本对比结果
export interface VersionComparison {
  version1: OrganizationVersion
  version2: OrganizationVersion

  // 变更项列表
  changes: Array<{
    type: 'ADD' | 'DELETE' | 'MODIFY' | 'MOVE'
    nodeId: string
    nodeName: string
    description: string

    before?: unknown

    after?: unknown
  }>
}

// 变更历史记录
export interface OrganizationChange {
  logId: string
  institutionId: string
  institutionName?: string
  changeType: string
  changeDescription?: string
  changeDate: string
  operator: string
  operatorName?: string
  approvalDocNumber?: string
  changeDetailsJSON?: string
}
