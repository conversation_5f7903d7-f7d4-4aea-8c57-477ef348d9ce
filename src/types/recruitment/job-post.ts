/**
 * 职位发布相关类型定义
 *
 * 本模块包含职位发布、配置、状态、模板、渠道、分析和批量操作相关的类型定义
 */

// 职位发布
export interface JobPost {
  id: number
  recruitmentNeedId?: number
  title: string // 职位标题
  departmentId: number
  departmentName: string
  positionId: number
  positionName: string
  jobType: string // 工作类型：全职/兼职/实习
  workLocation: string // 工作地点
  recruitCount: number // 招聘人数
  salaryMin?: number
  salaryMax?: number
  salaryUnit?: string // 薪资单位：月/年/时
  education: string // 学历要求
  experience: string // 经验要求
  jobDescription: string // 职位描述
  requirements: string // 任职要求
  benefits?: string // 福利待遇
  tags?: string[] // 标签
  isUrgent: boolean // 是否急聘
  isHot: boolean // 是否热门
  publishTime?: string // 发布时间
  expireTime?: string // 截止时间
  status: string // 状态：草稿/已发布/已下线/已关闭
  viewCount: number // 浏览次数
  applyCount: number // 申请人数
  createUserId: number
  createUserName: string
  createTime: string
  updateTime: string
}

// 职位发布配置接口
export interface RecruitmentJobPostConfig {
  configId: string
  configName: string
  departmentId: string
  departmentName: string
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  publishSettings: {
    autoPublish: boolean
    publishChannels: ('INTERNAL' | 'EXTERNAL' | 'SOCIAL' | 'PROFESSIONAL' | 'CAMPUS')[]
    defaultVisibility: 'PUBLIC' | 'INTERNAL' | 'DEPARTMENT' | 'CUSTOM'
    requireApproval: boolean
    approvalFlow: {
      stepId: string
      stepName: string
      approverType: 'HR' | 'DEPARTMENT_HEAD' | 'HIRING_MANAGER' | 'SYSTEM'
      approverId?: string
      approverName?: string
      isRequired: boolean
      timeout: number
    }[]
    autoExpiry: {
      enabled: boolean
      defaultDays: number
      reminderDays: number[]
      autoRenew: boolean
    }
  }
  templates: {
    defaultTemplate: string
    customTemplates: {
      templateId: string
      templateName: string
      templateType: 'DESCRIPTION' | 'REQUIREMENTS' | 'BENEFITS' | 'COMPLETE'
      content: string
      variables: string[]
    }[]
  }
  validation: {
    requiredFields: string[]
    minSalary?: number
    maxSalary?: number
    allowedEducationLevels: string[]
    allowedExperienceLevels: string[]
    customValidationRules: {
      field: string
      rule: string
      message: string
    }[]
  }
  seo: {
    enableSeo: boolean
    defaultKeywords: string[]
    metaTemplate: string
    structuredData: boolean
    urlPattern: string
  }
  analytics: {
    trackViews: boolean
    trackApplications: boolean
    trackSources: boolean
    conversionGoals: string[]
    customEvents: {
      eventName: string
      trigger: string

      properties: { [key: string]: unknown }
    }[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位发布状态接口
export interface RecruitmentJobPostStatus {
  postId: string
  postTitle: string
  currentStatus:
    | 'DRAFT'
    | 'PENDING_APPROVAL'
    | 'APPROVED'
    | 'PUBLISHED'
    | 'PAUSED'
    | 'EXPIRED'
    | 'CLOSED'
    | 'CANCELLED'
  statusHistory: {
    status: string
    timestamp: string
    operatorId: string
    operatorName: string
    reason?: string
    comments?: string
    systemGenerated: boolean
  }[]
  publishInfo: {
    publishedAt?: string
    publishedBy?: string
    scheduledPublishAt?: string
    expiryDate?: string
    actualExpiryDate?: string
    channels: {
      channelId: string
      channelName: string
      channelType: string
      publishStatus: 'PENDING' | 'PUBLISHED' | 'FAILED' | 'REMOVED'
      publishedAt?: string
      externalId?: string
      externalUrl?: string
      metrics?: {
        views: number
        applications: number
        clicks: number
        shares: number
      }
    }[]
  }
  approvalInfo: {
    currentStep?: number
    totalSteps: number
    approvalFlow: {
      stepId: string
      stepName: string
      approverType: string
      approverId?: string
      approverName?: string
      status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SKIPPED'
      processedAt?: string
      comments?: string
      attachments?: string[]
    }[]
    finalDecision?: 'APPROVED' | 'REJECTED'
    finalDecisionAt?: string
    finalDecisionBy?: string
  }
  metrics: {
    totalViews: number
    uniqueViews: number
    applicationCount: number
    qualifiedApplications: number
    averageViewTime: number
    bounceRate: number
    sourceBreakdown: {
      source: string
      views: number
      applications: number
      conversionRate: number
    }[]
  }
  alerts: {
    alertType: 'EXPIRY_WARNING' | 'LOW_APPLICATIONS' | 'HIGH_TRAFFIC' | 'APPROVAL_PENDING'
    severity: 'INFO' | 'WARNING' | 'ERROR'
    message: string
    triggeredAt: string
    isAcknowledged: boolean
  }[]
  lastModifiedAt: string
  lastModifiedBy: string
}

// 职位发布模板接口
export interface RecruitmentJobPostTemplate {
  templateId: string
  templateName: string
  templateType: 'STANDARD' | 'EXECUTIVE' | 'TECHNICAL' | 'SALES' | 'INTERN' | 'CUSTOM'
  category: string
  isPublic: boolean
  isActive: boolean
  createdBy: string
  createdAt: string
  structure: {
    sections: {
      sectionId: string
      sectionName: string
      sectionType: 'TEXT' | 'LIST' | 'TABLE' | 'MEDIA'
      order: number
      isRequired: boolean
      content: {
        title?: string
        content?: string
        placeholder?: string

        defaultValue?: unknown
        validation?: {
          minLength?: number
          maxLength?: number
          pattern?: string
          required?: boolean
        }
      }
      variables: string[]
    }[]
    style: {
      theme: string
      colors: { [key: string]: string }
      fonts: { [key: string]: string }
      layout: string
    }
  }
  usage: {
    totalUsage: number
    recentUsage: number
    successRate: number
    avgApplications: number
    topDepartments: {
      departmentName: string
      usageCount: number
    }[]
  }
  metadata: {
    tags: string[]
    description: string
    version: string
    compatibility: string[]
    lastUpdated: string
    changeLog: {
      version: string
      changes: string[]
      updatedAt: string
      updatedBy: string
    }[]
  }
}

// 职位发布渠道接口
export interface RecruitmentJobPostChannel {
  channelId: string
  channelName: string
  channelType: 'INTERNAL' | 'EXTERNAL' | 'SOCIAL' | 'PROFESSIONAL' | 'CAMPUS' | 'AGENCY'
  provider: string
  isActive: boolean
  configuration: {
    apiEndpoint?: string
    authMethod?: 'API_KEY' | 'OAUTH' | 'BASIC' | 'TOKEN'
    credentials: {
      apiKey?: string
      clientId?: string
      clientSecret?: string
      accessToken?: string
      username?: string
      password?: string
    }
    settings: {
      autoPublish: boolean
      autoRemove: boolean
      syncInterval: number
      retryAttempts: number
      timeout: number
    }
    fieldMapping: {
      localField: string
      remoteField: string
      transformation?: string
      required: boolean
    }[]
    restrictions: {
      maxPostsPerDay?: number
      allowedCategories?: string[]
      requiredFields?: string[]
      characterLimits?: { [field: string]: number }
    }
  }
  performance: {
    totalPosts: number
    successfulPosts: number
    failedPosts: number
    averageViews: number
    averageApplications: number
    conversionRate: number
    lastSyncAt?: string
    lastErrorAt?: string
    lastError?: string
  }
  costs: {
    pricingModel: 'FREE' | 'PER_POST' | 'SUBSCRIPTION' | 'PERFORMANCE'
    costPerPost?: number
    monthlyFee?: number
    conversionCost?: number
    totalSpent: number
    costPerHire?: number
  }
  compliance: {
    termsAccepted: boolean
    termsVersion: string
    privacyCompliant: boolean
    dataRetentionDays: number
    lastComplianceCheck: string
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位发布分析接口
export interface RecruitmentJobPostAnalytics {
  analyticsId: string
  postId: string
  postTitle: string
  period: [string, string]
  metrics: {
    visibility: {
      totalImpressions: number
      uniqueImpressions: number
      impressionRate: number
      reach: number
      frequency: number
      shareOfVoice: number
    }
    engagement: {
      totalViews: number
      uniqueViews: number
      avgViewDuration: number
      bounceRate: number
      pageDepth: number
      returnVisitorRate: number
    }
    applications: {
      totalApplications: number
      uniqueApplications: number
      applicationRate: number
      qualifiedApplications: number
      qualificationRate: number
      multipleApplications: number
    }
    conversion: {
      impressionToView: number
      viewToApplication: number
      applicationToQualified: number
      overallConversion: number
      funnelDropoff: {
        stage: string
        dropoffRate: number
      }[]
    }
  }
  demographics: {
    geographic: {
      country: string
      region: string
      city: string
      applicantCount: number
      percentage: number
    }[]
    professional: {
      industry: string
      experience: string
      education: string
      applicantCount: number
      percentage: number
    }[]
    temporal: {
      hourOfDay: { hour: number; views: number; applications: number }[]
      dayOfWeek: { day: string; views: number; applications: number }[]
      dateRange: { date: string; views: number; applications: number }[]
    }
  }
  sources: {
    sourceId: string
    sourceName: string
    sourceType: string
    referrals: number
    applications: number
    qualifiedApplications: number
    conversionRate: number
    cost?: number
    roi?: number
  }[]
  competitors: {
    competitorName?: string
    competitorPost?: string
    similarity: number
    rankingComparison: number
    metricComparison: {
      metric: string
      ourValue: number
      competitorValue: number
      difference: number
    }[]
  }[]
  insights: {
    performanceInsights: {
      insight: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      recommendation: string
      potentialGain?: string
    }[]
    anomalies: {
      metric: string
      description: string
      deviation: number
      detectedAt: string
    }[]
    predictions: {
      metric: string
      predictedValue: number
      confidence: number
      timeframe: string
    }[]
  }
  benchmarks: {
    industryBenchmarks: {
      metric: string
      industryAverage: number
      ourValue: number
      percentile: number
    }[]
    internalBenchmarks: {
      metric: string
      historicalAverage: number
      ourValue: number
      improvement: number
    }[]
  }
  generatedAt: string
  generatedBy: string
}

// 职位发布批量操作接口
export interface RecruitmentJobPostBatchOperation {
  operationId: string
  operationType: 'PUBLISH' | 'UNPUBLISH' | 'APPROVE' | 'REJECT' | 'UPDATE' | 'DELETE' | 'CLONE'
  operationName: string
  targetPosts: {
    postId: string
    postTitle: string
    currentStatus: string
  }[]
  parameters: {
    publishChannels?: string[]
    publishDate?: string
    expiryDate?: string

    updateFields?: { [field: string]: unknown }
    approvalComments?: string
    rejectionReason?: string
    cloneSettings?: {
      includeTemplate: boolean
      includeSettings: boolean
      updateTitle: boolean
      titleSuffix?: string
    }
  }
  validation: {
    isValid: boolean
    errors: {
      postId: string
      errorType: string
      errorMessage: string
      severity: 'ERROR' | 'WARNING'
    }[]
    warnings: {
      postId: string
      warningType: string
      warningMessage: string
    }[]
    summary: {
      totalPosts: number
      validPosts: number
      invalidPosts: number
      warningPosts: number
    }
  }
  execution: {
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
    progress: {
      completed: number
      total: number
      percentage: number
      currentPost?: string
      estimatedTimeRemaining?: number
    }
    results: {
      successful: {
        postId: string
        postTitle: string
        result: string
      }[]
      failed: {
        postId: string
        postTitle: string
        error: string
        retryable: boolean
      }[]
    }
    startedAt?: string
    completedAt?: string
    executedBy: string
  }
  notifications: {
    notifyOnCompletion: boolean
    notifyOnError: boolean
    recipients: string[]
    emailTemplate?: string
  }
  createdAt: string
  createdBy: string
}
