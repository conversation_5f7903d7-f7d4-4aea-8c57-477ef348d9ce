/**
 * 招聘需求相关类型定义
 *
 * 本模块包含招聘需求申请、审批相关的类型定义
 */

// 招聘需求
export interface RecruitmentNeed {
  id: number
  departmentId: number
  departmentName: string
  positionId: number
  positionName: string
  needCount: number // 需求人数
  currentCount: number // 现有人数
  recruitType: string // 招聘类型：社会招聘/校园招聘/内部招聘
  urgencyLevel: string // 紧急程度：高/中/低
  reason: string // 招聘原因
  requirements: string // 任职要求
  jobDescription: string // 岗位职责
  salaryRange: string // 薪资范围
  expectedDate: string // 期望到岗日期
  status: string // 状态：草稿/待审批/已通过/已拒绝
  applyUserId: number
  applyUserName: string
  applyTime: string
  approveUserId?: number
  approveUserName?: string
  approveTime?: string
  approveComment?: string
  createTime: string
  updateTime: string
}
