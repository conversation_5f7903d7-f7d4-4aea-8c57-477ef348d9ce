/**
 * 招聘计划相关类型定义
 *
 * 本模块包含招聘计划制定、执行跟踪等相关的类型定义
 */

// 招聘计划
export interface RecruitmentPlan {
  id: number
  year: number
  quarter?: number
  name: string
  departmentId?: number
  departmentName?: string
  totalCount: number // 计划招聘总数
  completedCount: number // 已完成数量
  budget?: number // 预算
  startDate: string
  endDate: string
  status: string // 状态：草稿/执行中/已完成/已取消
  details: RecruitmentPlanDetail[] // 计划明细
  createTime: string
  updateTime: string
}

// 招聘计划明细
export interface RecruitmentPlanDetail {
  positionId: number
  positionName: string
  count: number
  priority: string // 优先级
  completed: number
  requirements?: string
}
