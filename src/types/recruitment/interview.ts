/**
 * 面试管理相关类型定义
 *
 * 本模块包含面试安排、面试官管理、面试评价等相关的类型定义
 */

import type { Resume } from './resume'
import type { JobPost } from './job-post'

// 面试安排
export interface InterviewSchedule {
  id: number
  resumeId: number
  resume?: Resume
  jobPostId: number
  jobPost?: JobPost
  round: number // 面试轮次
  type: string // 面试类型：初试/复试/终试
  method: string // 面试方式：现场/电话/视频
  startTime: string
  endTime: string
  location?: string // 面试地点
  meetingUrl?: string // 视频面试链接
  interviewers: Interviewer[] // 面试官
  status: string // 状态：待面试/进行中/已完成/已取消
  result?: string // 面试结果：通过/待定/淘汰
  evaluation?: InterviewEvaluation[] // 面试评价
  notificationSent: boolean // 是否已发送通知
  createTime: string
  updateTime: string
}

// 面试官（简化版）
export interface Interviewer {
  userId: number
  userName: string
  department: string
  role: string // 面试官角色：主面试官/辅助面试官
}

// 面试评价（简化版）
export interface InterviewEvaluation {
  interviewerId: number
  interviewerName: string
  dimensions: EvaluationDimension[] // 评价维度
  overallScore: number // 总体评分
  recommendation: string // 推荐意见：强烈推荐/推荐/待定/不推荐
  strengths?: string // 优势
  weaknesses?: string // 不足
  comments?: string // 评价意见
  createTime: string
}

// 评价维度
export interface EvaluationDimension {
  name: string // 维度名称：专业能力/沟通能力/团队协作等
  score: number // 评分
  weight?: number // 权重
  comment?: string
}

// 面试安排（扩展版）
export interface InterviewArrangement {
  id: number
  candidateId: number
  candidateName: string
  candidatePhone?: string
  candidateEmail?: string
  positionId: number
  positionName: string
  departmentName: string
  round: number
  type: 'phone' | 'video' | 'onsite' | 'assessment'
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'rescheduled' | 'no_show'
  scheduledTime: string
  actualStartTime?: string
  actualEndTime?: string
  duration: number
  location?: string
  meetingUrl?: string
  roomId?: number
  roomName?: string
  interviewers: Array<{
    id: number
    name: string
    role: string
    department: string
    email: string
    phone?: string
    isPrimary: boolean
    attendance?: 'attended' | 'absent' | 'late'
  }>
  topics?: string[]
  requirements?: string
  preparations?: string
  result?: {
    decision: 'pass' | 'fail' | 'pending' | 'strong_pass' | 'strong_fail'
    nextRound?: boolean
    feedback?: string
    score?: number
  }
  notifications?: {
    candidateNotified: boolean
    candidateNotifiedAt?: string
    interviewersNotified: boolean
    interviewersNotifiedAt?: string
    remindersSent: boolean
    remindersSentAt?: string
  }
  cancellation?: {
    reason: string
    cancelledBy: string
    cancelledAt: string
  }
  rescheduling?: {
    originalTime: string
    reason: string
    rescheduledBy: string
    rescheduledAt: string
  }
  attachments?: Array<{
    id: number
    name: string
    type: string
    size: number
    url: string
  }>
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}
