/**
 * 招聘分析相关类型定义
 *
 * 本模块包含招聘统计、需求分析、技能分析、效果分析等相关的类型定义
 */

// 招聘统计
export interface RecruitmentStatistics {
  // 招聘漏斗
  funnel: {
    applied: number // 申请人数
    screened: number // 筛选通过
    interviewed: number // 面试人数
    offered: number // 发放offer
    hired: number // 入职人数
  }

  // 招聘周期
  cycle: {
    averageDays: number // 平均招聘周期
    screening: number // 筛选阶段天数
    interview: number // 面试阶段天数
    offer: number // offer阶段天数
  }

  // 渠道效果
  channel: Array<{
    source: string
    applied: number
    hired: number
    cost?: number
    roi?: number
  }>

  // 汇总数据
  summary: {
    totalPosts: number // 发布职位数
    activePosts: number // 在招职位数
    totalApplicants: number // 总申请人数
    hireRate: number // 录用率
    satisfaction: number // 满意度
  }
}

// 职位需求分析配置接口
export interface RecruitmentDemandAnalysisConfig {
  configId: string
  configName: string
  analysisScope: 'DEPARTMENT' | 'POSITION' | 'SKILL' | 'MARKET' | 'ORGANIZATION'
  isActive: boolean
  effectiveDate: string
  expiryDate?: string
  analysisSettings: {
    enablePredictiveAnalysis: boolean
    analysisFrequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
    historicalDataRange: number // 月数
    includedMetrics: (
      | 'HEADCOUNT'
      | 'TURNOVER'
      | 'HIRING_RATE'
      | 'TIME_TO_FILL'
      | 'COST_PER_HIRE'
      | 'QUALITY_OF_HIRE'
    )[]
    benchmarkSources: ('INTERNAL' | 'INDUSTRY' | 'MARKET' | 'COMPETITOR')[]
    alertThresholds: {
      criticalShortage: number // 百分比
      highTurnover: number
      longTimeToFill: number // 天数
      highCostPerHire: number
    }
  }
  dataSources: {
    internalSources: {
      hrms: boolean
      ats: boolean
      performanceSystem: boolean
      learningSystem: boolean
    }
    externalSources: {
      jobBoards: string[]
      salaryData: string[]
      marketReports: string[]
      industryBenchmarks: string[]
    }
  }
  reportingSettings: {
    autoGenerateReports: boolean
    reportSchedule: string // cron expression
    recipients: string[]
    reportFormats: ('PDF' | 'EXCEL' | 'DASHBOARD' | 'EMAIL')[]
    customDashboards: {
      dashboardId: string
      dashboardName: string
      widgets: string[]

      filters: { [key: string]: unknown }
    }[]
  }
  aiSettings: {
    enableAIInsights: boolean
    modelVersion: string
    confidenceThreshold: number
    insights: {
      demandForecasting: boolean
      skillGapAnalysis: boolean
      talentSupplyAnalysis: boolean
      competitiveAnalysis: boolean
      riskAssessment: boolean
    }
    customModels: {
      modelId: string
      modelName: string
      modelType: string

      parameters: { [key: string]: unknown }
    }[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位需求分析报告接口
export interface RecruitmentDemandAnalysisReport {
  reportId: string
  reportTitle: string
  reportType:
    | 'COMPREHENSIVE'
    | 'DEPARTMENTAL'
    | 'POSITIONAL'
    | 'SKILL_BASED'
    | 'MARKET_TRENDS'
    | 'PREDICTIVE'
  generatedAt: string
  reportPeriod: [string, string]
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'SCHEDULED'
  summary: {
    totalPositions: number
    vacantPositions: number
    urgentVacancies: number
    averageTimeToFill: number
    totalHiringCost: number
    overallSatisfactionScore: number
  }
  demandAnalysis: {
    currentDemand: {
      byDepartment: {
        departmentId: string
        departmentName: string
        totalPositions: number
        vacantPositions: number
        vacancyRate: number
        urgentVacancies: number
        avgTimeToFill: number
        priority: 'HIGH' | 'MEDIUM' | 'LOW'
      }[]
      byPosition: {
        positionId: string
        positionName: string
        currentHeadcount: number
        targetHeadcount: number
        vacancy: number
        growth: number
        difficulty: 'HIGH' | 'MEDIUM' | 'LOW'
      }[]
      bySkill: {
        skillName: string
        skillCategory: string
        demandLevel: 'HIGH' | 'MEDIUM' | 'LOW'
        supplyLevel: 'HIGH' | 'MEDIUM' | 'LOW'
        gapSeverity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW'
        marketTrend: 'INCREASING' | 'STABLE' | 'DECREASING'
      }[]
    }
    historicalTrends: {
      period: string
      totalDemand: number
      filledPositions: number
      averageTimeToFill: number
      costPerHire: number
      qualityScore: number
    }[]
    seasonalPatterns: {
      month: string
      demandIndex: number
      hiringSuccess: number
      costEfficiency: number
    }[]
  }
  supplyAnalysis: {
    internalSupply: {
      readyForPromotion: number
      crossFunctionalCandidates: number
      returningEmployees: number
      internalMobilityRate: number
    }
    externalSupply: {
      activeJobSeekers: number
      passiveCandidates: number
      talentPoolSize: number
      marketCompetitiveness: 'HIGH' | 'MEDIUM' | 'LOW'
    }
    pipelineAnalysis: {
      stage: string
      candidateCount: number
      conversionRate: number
      averageStageTime: number
      dropoffRate: number
    }[]
  }
  gapAnalysis: {
    criticalGaps: {
      gapType: 'HEADCOUNT' | 'SKILL' | 'EXPERIENCE' | 'LEADERSHIP'
      affectedArea: string
      gapSize: number
      urgency: 'IMMEDIATE' | 'SHORT_TERM' | 'MEDIUM_TERM' | 'LONG_TERM'
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      recommendedActions: string[]
    }[]
    skillGaps: {
      skillName: string
      currentLevel: number
      requiredLevel: number
      gap: number
      affectedRoles: string[]
      developmentOptions: string[]
    }[]
    capacityGaps: {
      area: string
      currentCapacity: number
      requiredCapacity: number
      gap: number
      timeline: string
      solutions: string[]
    }[]
  }
  predictiveInsights: {
    futureRecruitmentNeeds: {
      timeframe: string
      estimatedVacancies: number
      confidenceLevel: number
      drivingFactors: string[]
      recommendations: string[]
    }[]
    riskAssessment: {
      riskType: 'TURNOVER' | 'SKILL_SHORTAGE' | 'SUCCESSION' | 'COMPLIANCE'
      probability: number
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      mitigationStrategies: string[]
    }[]
    marketTrends: {
      trend: string
      impact: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL'
      timeline: string
      preparation: string[]
    }[]
  }
  competitiveAnalysis: {
    industryBenchmarks: {
      metric: string
      industryAverage: number
      ourValue: number
      percentile: number
      trend: 'IMPROVING' | 'STABLE' | 'DECLINING'
    }[]
    competitorIntelligence: {
      competitorName?: string
      hiringActivity: 'HIGH' | 'MEDIUM' | 'LOW'
      targetSkills: string[]
      compensationTrends: string
      strategicMoves: string[]
    }[]
  }
  recommendations: {
    immediate: {
      action: string
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
      resources: string[]
    }[]
    shortTerm: {
      action: string
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
      dependencies: string[]
    }[]
    longTerm: {
      action: string
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      effort: 'HIGH' | 'MEDIUM' | 'LOW'
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
      strategicAlignment: string
    }[]
  }
  appendices: {
    dataSource: string
    methodology: string
    assumptions: string[]
    limitations: string[]
    definitions: { [key: string]: string }
  }
  generatedBy: string
  approvedBy?: string
  approvedAt?: string
}

// 职位需求预测模型接口
export interface RecruitmentDemandForecastModel {
  modelId: string
  modelName: string
  modelType: 'LINEAR_REGRESSION' | 'TIME_SERIES' | 'MACHINE_LEARNING' | 'ENSEMBLE' | 'CUSTOM'
  version: string
  isActive: boolean
  trainingData: {
    startDate: string
    endDate: string
    dataPoints: number
    features: string[]
    targetVariable: string
  }
  modelParameters: {
    algorithm: string

    hyperparameters: { [key: string]: unknown }
    featureWeights: { [feature: string]: number }
    accuracy: number
    precision: number
    recall: number
    f1Score: number
  }
  validationResults: {
    testPeriod: [string, string]
    predictions: {
      predicted: number
      actual: number
      date: string
      accuracy: number
    }[]
    overallAccuracy: number
    mae: number // Mean Absolute Error
    mse: number // Mean Squared Error
    rmse: number // Root Mean Squared Error
  }
  predictionSettings: {
    forecastHorizon: number // months
    confidenceInterval: number // percentage
    updateFrequency: 'DAILY' | 'WEEKLY' | 'MONTHLY'
    alertThresholds: {
      significantChange: number // percentage
      lowConfidence: number // percentage
    }
  }
  applicableScenarios: {
    departments: string[]
    positions: string[]
    skillAreas: string[]
    businessConditions: string[]
  }
  lastTraining: {
    trainedAt: string
    trainedBy: string
    dataVersion: string
    performance: {
      trainingAccuracy: number
      validationAccuracy: number
      improvementOverPrevious: number
    }
  }
  usage: {
    totalPredictions: number
    successfulPredictions: number
    averageAccuracy: number
    lastUsed: string
    topUsers: {
      userId: string
      userName: string
      usageCount: number
    }[]
  }
  metadata: {
    description: string
    tags: string[]
    category: string
    complexity: 'LOW' | 'MEDIUM' | 'HIGH'
    maintainer: string
    documentationUrl?: string
    changeLog: {
      version: string
      changes: string[]
      date: string
      author: string
    }[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位需求分析指标接口
export interface RecruitmentDemandMetrics {
  metricsId: string
  reportId: string
  calculatedAt: string
  period: [string, string]
  demandMetrics: {
    totalDemand: number
    newDemand: number
    urgentDemand: number
    demandGrowthRate: number
    demandVolatility: number
    averageDemandDuration: number
  }
  supplyMetrics: {
    totalSupply: number
    qualifiedSupply: number
    internalSupply: number
    externalSupply: number
    supplyGrowthRate: number
    supplyQualityScore: number
  }
  balanceMetrics: {
    supplyDemandRatio: number
    vacancyRate: number
    fillRate: number
    timeToFillAverage: number
    timeToFillMedian: number
    costPerHire: number
  }
  qualityMetrics: {
    qualityOfHire: number
    retention90Days: number
    retention1Year: number
    performanceScore: number
    promotionRate: number
    culturalFit: number
  }
  efficiencyMetrics: {
    applicationToHireRatio: number
    screeningEfficiency: number
    interviewToOfferRatio: number
    offerAcceptanceRate: number
    processCompletionRate: number
    recruiterProductivity: number
  }
  costMetrics: {
    totalRecruitmentCost: number
    costPerHire: number
    costPerQualifiedCandidate: number
    agencyCosts: number
    advertisingCosts: number
    internalCosts: number
  }
  trendAnalysis: {
    metric: string
    currentValue: number
    previousValue: number
    changePercentage: number
    trend: 'IMPROVING' | 'STABLE' | 'DECLINING'
    seasonalAdjusted: number
  }[]
  benchmarkComparison: {
    metric: string
    ourValue: number
    industryBenchmark: number
    percentile: number
    gap: number
    performance: 'ABOVE' | 'AT' | 'BELOW'
  }[]
  alerts: {
    alertType: 'CRITICAL' | 'WARNING' | 'INFO'
    metric: string
    threshold: number
    currentValue: number
    description: string
    recommendedAction: string
  }[]
  insights: {
    category: 'DEMAND' | 'SUPPLY' | 'EFFICIENCY' | 'QUALITY' | 'COST'
    insight: string
    impact: 'HIGH' | 'MEDIUM' | 'LOW'
    confidence: number

    supportingData: unknown[]
  }[]
  generatedBy: string
}

// 职位需求分析任务接口
export interface RecruitmentDemandAnalysisTask {
  taskId: string
  taskName: string
  taskType: 'SCHEDULED' | 'ON_DEMAND' | 'TRIGGERED' | 'BATCH'
  analysisType: 'CURRENT_STATE' | 'TREND_ANALYSIS' | 'PREDICTIVE' | 'COMPARATIVE' | 'COMPREHENSIVE'
  priority: 'HIGH' | 'MEDIUM' | 'LOW'
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  requestedBy: string
  requestedAt: string
  parameters: {
    scope: {
      departments?: string[]
      positions?: string[]
      skills?: string[]
      locations?: string[]
    }
    timeframe: [string, string]
    granularity: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
    includePredictive: boolean
    forecastHorizon?: number
    benchmarkIndustry?: string[]

    customFilters?: { [key: string]: unknown }
  }
  dataInputs: {
    source: string
    dataType: string
    recordCount: number
    lastUpdated: string
    quality: 'HIGH' | 'MEDIUM' | 'LOW'
  }[]
  execution: {
    startedAt?: string
    completedAt?: string
    duration?: number
    progress: {
      currentStep: string
      completedSteps: number
      totalSteps: number
      percentage: number
      estimatedTimeRemaining?: number
    }
    resources: {
      cpuUsage: number
      memoryUsage: number
      dataProcessed: number
      apiCalls: number
    }
  }
  results: {
    reportId?: string
    outputFiles: {
      fileName: string
      fileType: string
      filePath: string
      fileSize: number
    }[]
    summary: {
      keyFindings: string[]
      recommendations: string[]
      dataQuality: string
      confidence: number
    }
  }
  errors: {
    errorCode: string
    errorMessage: string
    errorDetails: string
    occurredAt: string
    resolution?: string
  }[]
  notifications: {
    onCompletion: boolean
    onError: boolean
    recipients: string[]
    emailTemplate?: string
    sentAt?: string[]
  }
  schedule: {
    isRecurring: boolean
    cronExpression?: string
    nextRun?: string
    lastRun?: string
    runCount: number
  }
  dependencies: {
    dependsOn: string[]
    blocks: string[]
    triggers: string[]
  }
  createdAt: string
  updatedAt: string
  createdBy: string
}

// 职位技能需求分析接口
export interface RecruitmentSkillDemandAnalysis {
  analysisId: string
  analysisName: string
  skillCategory: string
  period: [string, string]
  skillInventory: {
    skillId: string
    skillName: string
    skillLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'
    skillType: 'TECHNICAL' | 'SOFT' | 'DOMAIN' | 'LEADERSHIP' | 'LANGUAGE'
    currentSupply: {
      internal: number
      external: number
      total: number
      availability: 'HIGH' | 'MEDIUM' | 'LOW'
    }
    demandAnalysis: {
      currentDemand: number
      projectedDemand: number
      demandGrowth: number
      urgency: 'IMMEDIATE' | 'SHORT_TERM' | 'MEDIUM_TERM' | 'LONG_TERM'
    }
    gapAnalysis: {
      quantitativeGap: number
      qualitativeGap: 'HIGH' | 'MEDIUM' | 'LOW'
      criticalityRating: number
      timeToClose: number
      closureStrategy: string[]
    }
    marketIntelligence: {
      marketDemand: 'HIGH' | 'MEDIUM' | 'LOW'
      salaryTrend: 'INCREASING' | 'STABLE' | 'DECREASING'
      competitionLevel: 'HIGH' | 'MEDIUM' | 'LOW'
      emergingTrends: string[]
    }
  }[]
  skillClusters: {
    clusterId: string
    clusterName: string
    skills: string[]
    demandScore: number
    supplyScore: number
    strategicImportance: 'HIGH' | 'MEDIUM' | 'LOW'
    investmentPriority: number
  }[]
  futureSkillNeeds: {
    timeframe: string
    emergingSkills: {
      skillName: string
      relevanceScore: number
      adoptionTimeline: string
      preparationActions: string[]
    }[]
    decliningSkills: {
      skillName: string
      declineRate: number
      replacementSkills: string[]
      transitionPlan: string[]
    }[]
  }
  developmentPathways: {
    pathway: string
    fromSkills: string[]
    toSkills: string[]
    developmentMethods: string[]
    estimatedTime: number
    cost: number
    roi: number
  }[]
  recommendations: {
    hiring: {
      prioritySkills: string[]
      sourcingStrategies: string[]
      compensationGuidance: string[]
    }
    development: {
      trainingPrograms: string[]
      certificationTargets: string[]
      mentorshipPairs: string[]
    }
    strategic: {
      partnershipsNeeded: string[]
      toolsAndTechnology: string[]
      organizationalChanges: string[]
    }
  }
  riskAssessment: {
    skillRisks: {
      skill: string
      riskType: 'SHORTAGE' | 'OBSOLESCENCE' | 'DEPENDENCY' | 'COMPETITION'
      probability: number
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      mitigation: string[]
    }[]
    businessImpact: {
      impactArea: string
      severity: 'HIGH' | 'MEDIUM' | 'LOW'
      timeline: string
      preventiveActions: string[]
    }[]
  }
  generatedAt: string
  generatedBy: string
  validUntil: string
}

// 职位效果分析相关接口定义
export interface RecruitmentJobEffectivenessAnalysis {
  analysisId: string
  jobId: string
  jobTitle: string
  department: string
  position: string
  analysisType: 'REAL_TIME' | 'PERIODIC' | 'CUSTOM' | 'COMPARATIVE' | 'PREDICTIVE'
  analysisPeriod: {
    startDate: string
    endDate: string
    periodType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY' | 'CUSTOM'
    isCompleted: boolean
  }
  performanceMetrics: {
    visibility: {
      totalViews: number
      uniqueViews: number
      viewsGrowthRate: number
      avgViewDuration: number
      bounceRate: number
      viewsBySource: Record<string, number>
      geographicDistribution: Array<{
        region: string
        viewCount: number
        percentage: number
      }>
      deviceBreakdown: {
        desktop: number
        mobile: number
        tablet: number
      }
      timeDistribution: Array<{
        hour: number
        viewCount: number
        conversionRate: number
      }>
    }
    attraction: {
      totalApplications: number
      qualifiedApplications: number
      applicationConversionRate: number
      applicationGrowthRate: number
      avgApplicationsPerDay: number
      applicationsBySource: Record<
        string,
        {
          count: number
          qualityScore: number
          conversionRate: number
        }
      >
      candidateQuality: {
        skillMatchScore: number
        experienceMatchScore: number
        educationMatchScore: number
        overallQualityScore: number
      }
      applicationTrends: Array<{
        date: string
        applications: number
        qualityScore: number
      }>
    }
    engagement: {
      interviewScheduled: number
      interviewCompleted: number
      interviewShowUpRate: number
      avgInterviewScore: number
      candidateFeedbackScore: number
      interviewerFeedbackScore: number
      engagementStages: Array<{
        stage: 'APPLICATION' | 'SCREENING' | 'INTERVIEW' | 'ASSESSMENT' | 'FINAL'
        conversionRate: number
        avgDuration: number
        dropOffRate: number
        satisfactionScore: number
      }>
    }
    conversion: {
      offersExtended: number
      offersAccepted: number
      offerAcceptanceRate: number
      finalHires: number
      overallConversionRate: number
      timeToHire: number
      costPerHire: number
      conversionFunnel: Array<{
        stage: string
        candidates: number
        conversionRate: number
        avgTimeInStage: number
      }>
      benchmarkComparison: {
        industryAvgConversionRate: number
        companyAvgConversionRate: number
        similarPositionsAvgRate: number
        performanceRank: number
      }
    }
    retention: {
      newHireRetentionRate30Days: number
      newHireRetentionRate90Days: number
      newHireRetentionRate1Year: number
      probationPassRate: number
      performanceRating: number
      retentionPredictionScore: number
      earlyWarningIndicators: Array<{
        indicator: string
        risk: 'LOW' | 'MEDIUM' | 'HIGH'
        recommendation: string
      }>
    }
  }
  costAnalysis: {
    totalRecruitmentCost: number
    costBreakdown: {
      jobPostingCosts: number
      sourcingCosts: number
      screeningCosts: number
      interviewCosts: number
      assessmentCosts: number
      offerProcessingCosts: number
      systemCosts: number
      recruiterTimeCosts: number
      managerTimeCosts: number
      travelCosts: number
      relocationCosts: number
      signingBonuses: number
      recruitmentAgencyFees: number
      backgroundCheckCosts: number
      otherCosts: number
    }
    costEfficiency: {
      costPerView: number
      costPerApplication: number
      costPerQualifiedCandidate: number
      costPerInterview: number
      costPerOffer: number
      costPerHire: number
      returnOnInvestment: number
      budgetUtilization: number
    }
    costComparison: {
      industryBenchmark: number
      companyAverage: number
      previousPeriodCost: number
      budgetVariance: number
      costTrend: 'INCREASING' | 'DECREASING' | 'STABLE'
    }
    costOptimization: Array<{
      area: string
      currentCost: number
      potentialSaving: number
      optimizationActions: string[]
      implementationEffort: 'LOW' | 'MEDIUM' | 'HIGH'
      expectedROI: number
    }>
  }
  channelEffectiveness: Array<{
    channelId: string
    channelName: string
    channelType:
      | 'JOB_BOARD'
      | 'SOCIAL_MEDIA'
      | 'COMPANY_WEBSITE'
      | 'EMPLOYEE_REFERRAL'
      | 'RECRUITMENT_AGENCY'
      | 'CAMPUS_RECRUITING'
      | 'PROFESSIONAL_NETWORK'
      | 'OTHER'
    performance: {
      views: number
      applications: number
      qualifiedCandidates: number
      interviews: number
      hires: number
      conversionRates: {
        viewToApplication: number
        applicationToQualified: number
        qualifiedToInterview: number
        interviewToHire: number
        overallConversion: number
      }
    }
    costs: {
      totalCost: number
      costPerView: number
      costPerApplication: number
      costPerHire: number
    }
    quality: {
      candidateQualityScore: number
      timeToFill: number
      retentionRate: number
      performanceRating: number
    }
    effectiveness: {
      efficiencyScore: number
      qualityScore: number
      costEffectivenessScore: number
      overallEffectivenessScore: number
      recommendation: 'INCREASE_INVESTMENT' | 'MAINTAIN' | 'REDUCE_INVESTMENT' | 'DISCONTINUE'
    }
    trends: Array<{
      period: string
      views: number
      applications: number
      hires: number
      cost: number
      effectiveness: number
    }>
  }>
  timeAnalysis: {
    recruitmentTimeline: {
      totalDuration: number
      plannedDuration: number
      timeVariance: number
      isOnSchedule: boolean
    }
    stageAnalysis: Array<{
      stage: string
      avgDuration: number
      maxDuration: number
      minDuration: number
      standardDeviation: number
      benchmarkDuration: number
      efficiency: 'EFFICIENT' | 'AVERAGE' | 'SLOW'
      bottlenecks: string[]
      improvementSuggestions: string[]
    }>
    timeToFillAnalysis: {
      actualTimeToFill: number
      targetTimeToFill: number
      industryBenchmark: number
      companyAverage: number
      percentile: number
      trend: 'IMPROVING' | 'DECLINING' | 'STABLE'
    }
    urgencyImpact: {
      urgencyLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
      impactOnTimeline: number
      impactOnQuality: number
      impactOnCost: number
      urgencyOptimization: string[]
    }
  }
  qualityAssessment: {
    candidateQuality: {
      skillAlignment: number
      experienceRelevance: number
      culturalFit: number
      motivationLevel: number
      growthPotential: number
      overallQualityScore: number
    }
    sourceQuality: Record<
      string,
      {
        qualityScore: number
        consistencyScore: number
        improvementTrend: 'IMPROVING' | 'DECLINING' | 'STABLE'
        strengthAreas: string[]
        improvementAreas: string[]
      }
    >
    hiringManagerSatisfaction: {
      satisfactionScore: number
      feedbackComments: string[]
      recommendationLikelihood: number
      processImprovement: string[]
    }
    candidateExperience: {
      applicationExperienceScore: number
      interviewExperienceScore: number
      communicationScore: number
      timelinessScore: number
      overallExperienceScore: number
      netPromoterScore: number
      feedbackThemes: Array<{
        theme: string
        frequency: number
        sentiment: 'POSITIVE' | 'NEUTRAL' | 'NEGATIVE'
      }>
    }
  }
  marketAnalysis: {
    competitivePositioning: {
      marketDemandLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH'
      competitionIntensity: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH'
      talentAvailability: 'ABUNDANT' | 'ADEQUATE' | 'LIMITED' | 'SCARCE'
      salaryCompetitiveness: number
      benefitsCompetitiveness: number
      brandAttractiveness: number
    }
    industryBenchmarks: {
      avgTimeToFill: number
      avgCostPerHire: number
      avgConversionRate: number
      avgCandidateExperience: number
      marketPosition: 'LEADER' | 'ABOVE_AVERAGE' | 'AVERAGE' | 'BELOW_AVERAGE' | 'LAGGARD'
    }
    marketTrends: Array<{
      trend: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      direction: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL'
      recommendation: string
      timeline: string
    }>
    competitorAnalysis: Array<{
      competitor: string
      advantages: string[]
      disadvantages: string[]
      differentiators: string[]
      actionableInsights: string[]
    }>
  }
  performancePrediction: {
    predictiveModels: Array<{
      modelId: string
      modelType: 'TIME_SERIES' | 'REGRESSION' | 'CLASSIFICATION' | 'ENSEMBLE'
      predictionTarget:
        | 'APPLICATION_VOLUME'
        | 'HIRE_SUCCESS'
        | 'TIME_TO_FILL'
        | 'COST_PROJECTION'
        | 'QUALITY_FORECAST'
      accuracy: number
      confidence: number
      predictions: Array<{
        period: string
        predictedValue: number
        confidenceInterval: {
          lower: number
          upper: number
        }
        factors: Array<{
          factor: string
          influence: number
        }>
      }>
    }>
    scenarioAnalysis: Array<{
      scenarioId: string
      scenarioName: string
      assumptions: Record<string, unknown>
      outcomes: {
        expectedApplications: number
        expectedHires: number
        expectedCost: number
        expectedTimeToFill: number
        expectedQuality: number
      }
      probability: number
      riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
      contingencyPlans: string[]
    }>
    recommendations: Array<{
      category: 'SOURCING' | 'PROCESS' | 'BUDGET' | 'TIMELINE' | 'QUALITY'
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      recommendation: string
      expectedImpact: string
      implementationEffort: 'LOW' | 'MEDIUM' | 'HIGH'
      timeline: string
      successMetrics: string[]
    }>
  }
  reporting: {
    executiveSummary: {
      overallEffectiveness: 'EXCELLENT' | 'GOOD' | 'AVERAGE' | 'POOR' | 'CRITICAL'
      keySuccessFactors: string[]
      majorChallenges: string[]
      strategicRecommendations: string[]
      budgetImpact: string
      timelineImpact: string
    }
    detailedFindings: Array<{
      category: string
      finding: string
      evidence: string[]
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      actionRequired: boolean
      recommendedActions: string[]
    }>
    benchmarkResults: {
      industryComparison: Record<
        string,
        {
          value: number
          benchmark: number
          variance: number
          ranking: number
        }
      >
      historicalComparison: Record<
        string,
        {
          currentValue: number
          previousValue: number
          changePercent: number
          trend: 'IMPROVING' | 'DECLINING' | 'STABLE'
        }
      >
    }
    visualizations: Array<{
      chartId: string
      chartType: 'FUNNEL' | 'TREND' | 'COMPARISON' | 'HEATMAP' | 'SCATTER' | 'GAUGE' | 'WATERFALL'
      title: string
      description: string
      dataSource: string
      configuration: Record<string, unknown>
    }>
  }
  alerts: Array<{
    alertId: string
    alertType:
      | 'PERFORMANCE_DECLINE'
      | 'COST_OVERRUN'
      | 'TIMELINE_DELAY'
      | 'QUALITY_ISSUE'
      | 'ANOMALY_DETECTED'
    severity: 'INFO' | 'WARNING' | 'CRITICAL'
    title: string
    description: string
    triggeredAt: string
    metrics: Array<{
      metric: string
      currentValue: number
      thresholdValue: number
      variance: number
    }>
    recommendations: string[]
    isResolved: boolean
  }>
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

export interface RecruitmentJobEffectivenessReport {
  reportId: string
  reportName: string
  reportType:
    | 'EXECUTIVE_SUMMARY'
    | 'DETAILED_ANALYSIS'
    | 'COMPARATIVE_STUDY'
    | 'TREND_ANALYSIS'
    | 'ROI_ANALYSIS'
    | 'CHANNEL_PERFORMANCE'
    | 'CUSTOM'
  generatedAt: string
  generatedBy: string
  reportPeriod: {
    startDate: string
    endDate: string
    periodType: 'MONTHLY' | 'QUARTERLY' | 'YEARLY' | 'CUSTOM'
  }
  scope: {
    jobIds: string[]
    departments: string[]
    positions: string[]
    locations: string[]
    recruiterIds: string[]
  }
  executiveSummary: {
    totalJobs: number
    totalApplications: number
    totalHires: number
    avgTimeToFill: number
    avgCostPerHire: number
    overallROI: number
    effectivenessScore: number
    keyInsights: string[]
    strategicRecommendations: string[]
  }
  performanceAnalysis: {
    topPerformingJobs: Array<{
      jobId: string
      jobTitle: string
      effectivenessScore: number
      keySuccessFactors: string[]
    }>
    underPerformingJobs: Array<{
      jobId: string
      jobTitle: string
      effectivenessScore: number
      identifiedIssues: string[]
      improvementRecommendations: string[]
    }>
    channelPerformance: Array<{
      channel: string
      totalCost: number
      totalHires: number
      costPerHire: number
      qualityScore: number
      effectiveness: number
      recommendation: string
    }>
    timelineAnalysis: {
      onTimeJobs: number
      delayedJobs: number
      avgDelay: number
      commonBottlenecks: string[]
      processImprovements: string[]
    }
  }
  financialAnalysis: {
    totalInvestment: number
    costBreakdown: Record<string, number>
    costTrends: Array<{
      period: string
      cost: number
      hires: number
      costPerHire: number
    }>
    budgetVariance: {
      plannedBudget: number
      actualSpend: number
      variance: number
      variancePercent: number
    }
    roi: {
      totalROI: number
      roiByChannel: Record<string, number>
      roiTrends: Array<{
        period: string
        roi: number
      }>
    }
    costOptimization: Array<{
      area: string
      currentCost: number
      optimizedCost: number
      potentialSaving: number
      implementationPlan: string[]
    }>
  }
  qualityAnalysis: {
    overallQualityScore: number
    qualityTrends: Array<{
      period: string
      qualityScore: number
      retentionRate: number
      performanceRating: number
    }>
    sourceQualityRanking: Array<{
      source: string
      qualityScore: number
      retentionRate: number
      performanceRating: number
      rank: number
    }>
    qualityFactors: Array<{
      factor: string
      impact: number
      correlation: number
      recommendation: string
    }>
  }
  benchmarkAnalysis: {
    industryComparison: Record<
      string,
      {
        ourValue: number
        industryAverage: number
        topPerformer: number
        ourRanking: number
      }
    >
    competitivePosition: {
      overallPosition: 'LEADER' | 'CHALLENGER' | 'FOLLOWER' | 'NICHE'
      strengthAreas: string[]
      improvementAreas: string[]
      competitiveAdvantages: string[]
    }
    marketInsights: Array<{
      insight: string
      relevance: 'HIGH' | 'MEDIUM' | 'LOW'
      actionability: 'IMMEDIATE' | 'SHORT_TERM' | 'LONG_TERM'
      recommendation: string
    }>
  }
  predictiveInsights: {
    forecastAccuracy: number
    predictions: Array<{
      metric: string
      currentValue: number
      predictedValue: number
      predictionPeriod: string
      confidence: number
      factors: string[]
    }>
    scenarioPlanning: Array<{
      scenario: string
      probability: number
      impact: string
      preparedness: string[]
    }>
    riskAssessment: Array<{
      risk: string
      probability: 'LOW' | 'MEDIUM' | 'HIGH'
      impact: 'LOW' | 'MEDIUM' | 'HIGH'
      mitigation: string[]
    }>
  }
  actionPlan: {
    immediateActions: Array<{
      action: string
      owner: string
      deadline: string
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      expectedOutcome: string
    }>
    shortTermInitiatives: Array<{
      initiative: string
      timeline: string
      resources: string[]
      expectedROI: number
      successMetrics: string[]
    }>
    longTermStrategy: Array<{
      strategy: string
      timeline: string
      investment: number
      expectedImpact: string
      milestones: string[]
    }>
  }
  attachments: Array<{
    fileId: string
    fileName: string
    fileType: 'CHART' | 'DATA_EXPORT' | 'PRESENTATION' | 'DETAILED_ANALYSIS'
    fileSize: number
    downloadUrl: string
  }>
  distributionList: Array<{
    recipientId: string
    recipientName: string
    role: string
    deliveryMethod: 'EMAIL' | 'DASHBOARD' | 'API'
    deliveryStatus: 'PENDING' | 'DELIVERED' | 'FAILED'
  }>
  createdAt: string
  updatedAt: string
}

export interface RecruitmentJobEffectivenessMetric {
  metricId: string
  metricName: string
  metricType:
    | 'PERFORMANCE'
    | 'COST'
    | 'QUALITY'
    | 'TIME'
    | 'ENGAGEMENT'
    | 'SATISFACTION'
    | 'RETENTION'
  category: 'PRIMARY' | 'SECONDARY' | 'DERIVED' | 'BENCHMARK'
  dataType: 'NUMERIC' | 'PERCENTAGE' | 'RATIO' | 'INDEX' | 'SCORE'
  calculationMethod: string
  formulaDefinition: string
  dataSources: string[]
  updateFrequency: 'REAL_TIME' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'MONTHLY'
  benchmarkValues: {
    industryBenchmark?: number
    companyBenchmark?: number
    targetValue?: number
    thresholds: {
      excellent: number
      good: number
      average: number
      poor: number
    }
  }
  historicalData: Array<{
    period: string
    value: number
    trend: 'UP' | 'DOWN' | 'STABLE'
    context: string
  }>
  relatedMetrics: string[]
  businessImpact: {
    impactLevel: 'HIGH' | 'MEDIUM' | 'LOW'
    impactDescription: string
    stakeholders: string[]
    decisionInfluence: string
  }
  qualityIndicators: {
    dataQuality: number
    completeness: number
    accuracy: number
    timeliness: number
    consistency: number
  }
  alertRules: Array<{
    ruleId: string
    condition: string
    threshold: number
    severity: 'WARNING' | 'CRITICAL'
    action: string
  }>
  isActive: boolean
  tags: string[]
  metadata: Record<string, unknown>
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

export interface RecruitmentJobEffectivenessBenchmark {
  benchmarkId: string
  benchmarkName: string
  benchmarkType: 'INDUSTRY' | 'COMPANY' | 'ROLE' | 'LOCATION' | 'SIZE' | 'SECTOR' | 'CUSTOM'
  scope: {
    industry?: string[]
    companySize?: 'STARTUP' | 'SMB' | 'ENTERPRISE' | 'LARGE_ENTERPRISE'
    geography?: string[]
    roles?: string[]
    timeframe: {
      startDate: string
      endDate: string
    }
  }
  metrics: Record<
    string,
    {
      value: number
      percentile25: number
      percentile50: number
      percentile75: number
      percentile90: number
      sampleSize: number
      dataSource: string
      reliability: 'HIGH' | 'MEDIUM' | 'LOW'
    }
  >
  comparisons: Array<{
    metric: string
    ourValue: number
    benchmarkValue: number
    variance: number
    percentileRank: number
    performance: 'EXCELLENT' | 'GOOD' | 'AVERAGE' | 'BELOW_AVERAGE' | 'POOR'
    interpretation: string
    improvementOpportunity: string
  }>
  insights: {
    topPerformanceAreas: string[]
    improvementAreas: string[]
    competitiveAdvantages: string[]
    marketTrends: string[]
    recommendations: Array<{
      area: string
      recommendation: string
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      expectedImpact: string
    }>
  }
  lastUpdated: string
  nextUpdate: string
  dataQuality: {
    completeness: number
    accuracy: number
    relevance: number
    timeliness: number
    consistency: number
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}
