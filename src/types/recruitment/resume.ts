/**
 * 简历管理相关类型定义
 *
 * 本模块包含简历信息、教育背景、工作经历、项目经验等相关的类型定义
 */

// 简历信息
export interface Resume {
  id: number
  applicantId: number // 应聘者ID
  jobPostId?: number // 申请职位ID
  jobPostTitle?: string
  // 基本信息
  name: string
  gender: string
  birthDate: string
  phone: string
  email: string
  idCard: string
  currentLocation: string
  expectedLocation?: string
  photo?: string

  // 教育背景
  education: EducationInfo[]

  // 工作经历
  workExperience: WorkExperience[]

  // 项目经验
  projectExperience?: ProjectExperience[]

  // 技能特长
  skills?: string[]
  certificates?: Certificate[] // 证书
  languages?: Language[] // 语言能力

  // 求职意向
  expectedPosition?: string
  expectedSalaryMin?: number
  expectedSalaryMax?: number
  jobStatus?: string // 在职状态
  availableTime?: string // 到岗时间

  // 其他信息
  selfEvaluation?: string // 自我评价
  attachments?: Attachment[] // 附件

  // AI分析结果
  aiParsed?: boolean
  aiMatchScore?: number
  aiSummary?: string
  aiTags?: string[]

  // 状态信息
  source: string // 来源：网申/内推/猎头/校招
  status: string // 状态：待筛选/初筛通过/复筛通过/面试中/已录用/已拒绝
  currentStep?: string // 当前步骤
  score?: number // 综合评分
  comments?: Comment[] // 评价记录

  createTime: string
  updateTime: string
}

// 教育信息
export interface EducationInfo {
  school: string
  major: string
  degree: string
  startDate: string
  endDate: string
  isFullTime: boolean
  achievements?: string
}

// 工作经历
export interface WorkExperience {
  company: string
  position: string
  startDate: string
  endDate: string
  isCurrent: boolean
  description: string
  achievements?: string
}

// 项目经验
export interface ProjectExperience {
  name: string
  role: string
  startDate: string
  endDate: string
  description: string
  technologies?: string[]
  achievements?: string
}

// 证书
export interface Certificate {
  name: string
  issueOrg: string
  issueDate: string
  expiryDate?: string
}

// 语言能力
export interface Language {
  language: string
  proficiency: string // 精通/熟练/一般
}

// 附件
export interface Attachment {
  name: string
  url: string
  type: string
  size: number
}

// 评论
export interface Comment {
  userId: number
  userName: string
  content: string
  time: string
}
