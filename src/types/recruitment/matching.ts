/**
 * 职位匹配相关类型定义
 *
 * 本模块包含人才匹配、职位匹配算法配置、匹配结果等相关的类型定义
 */

import type { TalentPool } from './talent-pool'

// 匹配权重配置
export interface MatchWeights {
  skills: number
  experience: number
  education: number
  salary: number
  location: number
}

// 匹配详情
export interface MatchDetails {
  skills: {
    score: number
    matched: string[]
    missing: string[]
  }
  experience: {
    score: number
    years: number
    required: number
  }
  education: {
    score: number
    level: string
    required: string
  }
  salary: {
    score: number
    expected: number
    offered: number
  }
  location: {
    score: number
    distance: number
  }
}

// 人才匹配结果
export interface TalentMatchResult {
  talent: TalentPool
  matchScore: number
  matchDetails: MatchDetails
  matchReasons: string[]
  concerns: string[]
}

// 职位匹配结果
export interface PositionMatchResult {
  position: JobPosition
  matchScore: number
  matchReasons: string[]
  benefits: string[]
  challenges: string[]
}

// 临时定义 JobPosition 接口（应该从其他模块导入）
interface JobPosition {
  id: number
  name: string
  departmentId: number
  departmentName: string
}

// 匹配配置
export interface MatchConfig {
  defaultWeights: MatchWeights
  scoreThresholds: {
    excellent: number
    good: number
    acceptable: number
  }
  rules: Array<{
    id: number
    name: string
    condition: string
    action: string
    priority: number
    enabled: boolean
  }>
}

// 匹配历史记录
export interface MatchHistory {
  id: number
  matchType: string
  positionId: number
  positionName: string
  talentId: number
  talentName: string
  matchScore: number
  outcome?: string
  feedback?: string
  createTime: string
}

// 匹配优化建议
export interface MatchOptimizationSuggestion {
  type: 'requirement' | 'skill' | 'salary' | 'description'
  priority: 'high' | 'medium' | 'low'
  suggestion: string
  expectedImprovement: number
  implementation: string
}

// 职位匹配算法配置接口
export interface RecruitmentJobMatchingConfig {
  configId: string
  configName: string
  algorithmType: 'COMPREHENSIVE' | 'SKILL_BASED' | 'EXPERIENCE_BASED' | 'EDUCATION_BASED' | 'CUSTOM'
  isActive: boolean
  version: string
  weights: {
    skills: number
    experience: number
    education: number
    salary: number
    location: number
    industry: number
    culture: number
    additional?: { [key: string]: number }
  }
  thresholds: {
    minimumMatchScore: number
    recommendationThreshold: number
    autoScreeningThreshold: number
    excellentMatchThreshold: number
  }
  matchingRules: {
    skillMatching: {
      exactMatchWeight: number
      similarSkillWeight: number
      skillLevelWeight: number
      skillCategoryWeight: number
      requiredSkillsMode: 'ALL' | 'MAJORITY' | 'ANY'
    }
    experienceMatching: {
      yearExactMatchWeight: number
      yearRangeWeight: number
      industryExperienceWeight: number
      roleLevelWeight: number
      leadershipExperienceWeight: number
    }
    educationMatching: {
      degreeWeight: number
      majorWeight: number
      schoolTierWeight: number
      certificationWeight: number
      additionalQualificationWeight: number
    }
    compensationMatching: {
      salaryRangeWeight: number
      benefitsWeight: number
      equityWeight: number
      bonusWeight: number
      flexibilityWeight: number
    }
  }
  aiSettings: {
    enableMLMatching: boolean
    modelVersion: string
    confidenceThreshold: number
    learningEnabled: boolean
    feedbackWeight: number
    biasDetection: boolean
    explainabilityLevel: 'HIGH' | 'MEDIUM' | 'LOW'
  }
  candidateSources: {
    internalCandidates: boolean
    talentPool: boolean
    activeApplications: boolean
    passiveCandidates: boolean
    referrals: boolean
    externalDatabases: string[]
  }
  outputSettings: {
    maxCandidatesPerJob: number
    includeExplanation: boolean
    includeConfidenceScore: boolean
    includeAlternativeJobs: boolean
    rankingMethod: 'SCORE' | 'WEIGHTED' | 'ML_RANKING'
  }
  qualityControls: {
    diversityRequirements: {
      enableDiversityFilters: boolean
      genderBalance: boolean
      ageDistribution: boolean
      backgroundDiversity: boolean
    }
    fairnessChecks: {
      biasDetection: boolean
      equalOpportunity: boolean
      demographicParity: boolean
    }
  }
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// 职位匹配结果接口
export interface RecruitmentJobMatchingResult {
  matchingId: string
  jobId: string
  jobTitle: string
  candidateId: string
  candidateName: string
  executedAt: string
  algorithmVersion: string
  overallMatchScore: number
  confidence: number
  recommendation: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'NOT_SUITABLE'
  detailedScores: {
    skillMatch: {
      score: number
      breakdown: {
        exactMatches: string[]
        similarMatches: { skill: string; similarity: number }[]
        missingSkills: string[]
        additionalSkills: string[]
      }
      explanation: string
    }
    experienceMatch: {
      score: number
      breakdown: {
        yearsMatch: number
        industryMatch: number
        roleMatch: number
        leadershipMatch: number
        projectMatch: number
      }
      explanation: string
    }
    educationMatch: {
      score: number
      breakdown: {
        degreeMatch: number
        majorMatch: number
        schoolMatch: number
        certificationMatch: number
        additionalMatch: number
      }
      explanation: string
    }
    compensationMatch: {
      score: number
      breakdown: {
        salaryFit: number
        benefitsFit: number
        locationFit: number
        flexibilityFit: number
      }
      explanation: string
    }
    culturalFit: {
      score: number
      breakdown: {
        valueAlignment: number
        workStyleMatch: number
        teamFit: number
        managementStyleFit: number
      }
      explanation: string
    }
  }
  strengthsAndWeaknesses: {
    strengths: Array<{
      aspect: string
      description: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
    }>
    weaknesses: Array<{
      aspect: string
      description: string
      impact: 'HIGH' | 'MEDIUM' | 'LOW'
      mitigation?: string
    }>
    uniqueQualities: string[]
  }
  recommendedActions: {
    immediate: string[]
    followUp: string[]
    longTerm: string[]
  }
  alternativePositions?: Array<{
    jobId: string
    jobTitle: string
    matchScore: number
    keyDifferences: string[]
  }>
  risks: Array<{
    riskType:
      | 'RETENTION'
      | 'PERFORMANCE'
      | 'CULTURAL_FIT'
      | 'OVERQUALIFICATION'
      | 'UNDERQUALIFICATION'
    severity: 'HIGH' | 'MEDIUM' | 'LOW'
    description: string
    mitigation: string
  }>
  metadata: {
    processingTime: number
    dataCompleteness: number
    confidenceFactors: Record<string, number>

    debugInfo?: unknown
  }
}

// 职位匹配批量操作接口
export interface RecruitmentJobMatchingBatch {
  batchId: string
  batchName: string
  batchType: 'JOB_TO_CANDIDATES' | 'CANDIDATE_TO_JOBS' | 'BULK_MATCHING' | 'PERIODIC_MATCHING'
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  createdAt: string
  startedAt?: string
  completedAt?: string
  createdBy: string
  parameters: {
    jobIds?: string[]
    candidateIds?: string[]
    filters?: {
      skills?: string[]
      experience?: { min: number; max: number }
      education?: string[]
      location?: string[]
      salary?: { min: number; max: number }
    }
    configOverrides?: Partial<RecruitmentJobMatchingConfig>
    outputFormat: 'DETAILED' | 'SUMMARY' | 'MATRIX'
    notificationSettings: {
      notifyOnCompletion: boolean
      notifyOnHighMatch: boolean
      recipients: string[]
    }
  }
  progress: {
    totalItems: number
    processedItems: number
    successfulMatches: number
    failedMatches: number
    averageMatchScore: number
    processingRate: number // items per minute
    estimatedTimeRemaining?: number // minutes
  }
  results: {
    summary: {
      totalMatches: number
      excellentMatches: number
      goodMatches: number
      fairMatches: number
      poorMatches: number
      averageScore: number
      highestScore: number
      lowestScore: number
    }
    matches: RecruitmentJobMatchingResult[]
    errors: Array<{
      itemId: string
      errorType: string
      errorMessage: string
      timestamp: string
    }>
  }
  performance: {
    totalProcessingTime: number
    averageProcessingTimePerItem: number
    resourceUsage: {
      cpuUsage: number
      memoryUsage: number
      apiCalls: number
    }
  }
}

// 职位匹配性能分析接口
export interface RecruitmentJobMatchingPerformance {
  performanceId: string
  analysisDate: string
  analysisPeriod: [string, string]
  scope: {
    totalJobs: number
    totalCandidates: number
    totalMatches: number
    departmentsIncluded: string[]
    positionsIncluded: string[]
  }
  matchingEffectiveness: {
    overallAccuracy: number
    precisionScore: number
    recallScore: number
    f1Score: number
    falsePositiveRate: number
    falseNegativeRate: number
  }
  outcomeAnalysis: {
    matchesToHires: {
      excellent: { matched: number; hired: number; conversionRate: number }
      good: { matched: number; hired: number; conversionRate: number }
      fair: { matched: number; hired: number; conversionRate: number }
      poor: { matched: number; hired: number; conversionRate: number }
    }
    hiringManagerFeedback: {
      averageRating: number
      satisfactionRate: number
      feedbackCount: number
      commonThemes: string[]
    }
    candidateFeedback: {
      averageRating: number
      relevanceScore: number
      feedbackCount: number
      commonThemes: string[]
    }
  }
  performanceByFactor: {
    skills: { weight: number; accuracy: number; impact: number }
    experience: { weight: number; accuracy: number; impact: number }
    education: { weight: number; accuracy: number; impact: number }
    compensation: { weight: number; accuracy: number; impact: number }
    culture: { weight: number; accuracy: number; impact: number }
  }
  algorithmPerformance: {
    modelVersion: string
    trainingMetrics: {
      dataSize: number
      features: number
      accuracy: number
      loss: number
    }
    predictionQuality: {
      meanAbsoluteError: number
      rootMeanSquareError: number
      r2Score: number
    }
    biasAnalysis: {
      detectedBiases: Array<{
        biasType: string
        affectedAttribute: string
        severity: 'HIGH' | 'MEDIUM' | 'LOW'
        mitigation: string
      }>
      fairnessMetrics: {
        demographicParity: number
        equalOpportunity: number
        equalizedOdds: number
      }
    }
  }
  improvementRecommendations: Array<{
    area: string
    currentPerformance: number
    targetPerformance: number
    recommendation: string
    expectedImpact: string
    implementationEffort: 'HIGH' | 'MEDIUM' | 'LOW'
  }>
  comparisons: {
    vsLastPeriod: {
      accuracyChange: number
      efficiencyChange: number
      satisfactionChange: number
    }
    vsBenchmark: {
      industryBenchmark: number
      ourPerformance: number
      gap: number
      ranking: string
    }
  }
}

// 候选人档案接口（用于匹配）
export interface RecruitmentCandidateProfile {
  profileId: string
  candidateId: string
  lastUpdated: string
  completeness: number
  basicInfo: {
    name: string
    email: string
    phone: string
    location: string
    currentTitle?: string
    currentCompany?: string
    yearsOfExperience: number
    noticePeriod?: string
    availability:
      | 'IMMEDIATE'
      | 'WITHIN_2_WEEKS'
      | 'WITHIN_1_MONTH'
      | 'WITHIN_3_MONTHS'
      | 'NOT_LOOKING'
  }
  professionalProfile: {
    summary: string
    coreCompetencies: string[]
    industries: Array<{
      industry: string
      yearsOfExperience: number
      level: 'ENTRY' | 'MID' | 'SENIOR' | 'EXPERT'
    }>
    functionalAreas: Array<{
      area: string
      proficiency: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'
    }>
  }
  skills: Array<{
    skillId: string
    skillName: string
    category: 'TECHNICAL' | 'SOFT' | 'DOMAIN' | 'TOOL' | 'LANGUAGE'
    proficiencyLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'
    yearsOfExperience: number
    lastUsed?: string
    verified: boolean
    endorsements?: number
  }>
  experience: Array<{
    company: string
    title: string
    startDate: string
    endDate?: string
    isCurrent: boolean
    description: string
    keyAchievements: string[]
    technologies: string[]
    teamSize?: number
    reportingTo?: string
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    startDate: string
    endDate: string
    grade?: string
    achievements?: string[]
  }>
  certifications: Array<{
    name: string
    issuingOrganization: string
    issueDate: string
    expiryDate?: string
    credentialId?: string
    url?: string
  }>
  preferences: {
    jobTypes: Array<'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'FREELANCE' | 'INTERNSHIP'>
    workModes: Array<'ONSITE' | 'HYBRID' | 'REMOTE'>
    preferredLocations: string[]
    willingToRelocate: boolean
    travelWillingness: 'NO' | 'OCCASIONAL' | 'FREQUENT'
    compensationExpectations: {
      currency: string
      minSalary: number
      maxSalary: number
      bonusExpectation?: number
      equityInterest: boolean
    }
    industryPreferences: string[]
    companySizePreferences: Array<'STARTUP' | 'SMB' | 'MID_SIZE' | 'ENTERPRISE'>
    culturalPreferences: {
      workLifeBalance: number // 1-5
      innovation: number // 1-5
      teamCollaboration: number // 1-5
      careerGrowth: number // 1-5
      stability: number // 1-5
    }
  }
  assessments: Array<{
    assessmentId: string
    assessmentType: string
    assessmentName: string
    score: number
    percentile?: number
    completedAt: string
    validity: string
  }>
  aiInsights: {
    careerTrajectory: 'UPWARD' | 'LATERAL' | 'SPECIALIZED' | 'DIVERSE'
    stabilityScore: number
    growthPotential: number
    leadershipPotential: number
    keyStrengths: string[]
    developmentAreas: string[]
    bestFitRoles: string[]
    riskFactors: string[]
  }
  matchingMetadata: {
    lastMatchedAt?: string
    matchingFrequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'ON_DEMAND'
    exclusions: string[] // Job IDs to exclude
    tags: string[]
    notes: string
  }
}

// 职位匹配模型接口
export interface RecruitmentJobMatchingModel {
  modelId: string
  modelName: string
  modelType: 'RULE_BASED' | 'ML_CLASSIFICATION' | 'ML_REGRESSION' | 'DEEP_LEARNING' | 'HYBRID'
  version: string
  status: 'TRAINING' | 'VALIDATING' | 'ACTIVE' | 'DEPRECATED' | 'FAILED'
  createdAt: string
  lastTrainedAt: string
  nextTrainingScheduled?: string
  trainingConfiguration: {
    algorithm: string
    hyperparameters: Record<string, unknown>
    features: Array<{
      featureName: string
      featureType: 'NUMERIC' | 'CATEGORICAL' | 'TEXT' | 'BOOLEAN'
      importance: number
      preprocessing: string
    }>
    targetVariable: string
    trainTestSplit: number
    validationMethod: 'HOLD_OUT' | 'CROSS_VALIDATION' | 'TIME_SERIES_SPLIT'
    optimizationMetric: string
  }
  trainingData: {
    totalSamples: number
    positiveSamples: number
    negativeSamples: number
    features: number
    dataQuality: number
    lastUpdated: string
    sources: string[]
  }
  performance: {
    trainingMetrics: {
      accuracy: number
      precision: number
      recall: number
      f1Score: number
      aucRoc: number
      logLoss?: number
    }
    validationMetrics: {
      accuracy: number
      precision: number
      recall: number
      f1Score: number
      aucRoc: number
      confusionMatrix: number[][]
    }
    productionMetrics?: {
      predictionCount: number
      averageConfidence: number
      driftScore: number
      performanceTrend: 'IMPROVING' | 'STABLE' | 'DEGRADING'
    }
  }
  featureImportance: Array<{
    feature: string
    importance: number
    category: string
    description: string
  }>
  biasAnalysis: {
    analyzedAttributes: string[]
    detectedBiases: Array<{
      attribute: string
      biasType: string
      severity: number
      affectedGroups: string[]
      mitigation: string
    }>
    fairnessScore: number
    recommendations: string[]
  }
  deployment: {
    endpoint?: string
    scalingConfig: {
      minInstances: number
      maxInstances: number
      targetLatency: number
    }
    monitoring: {
      enabledMetrics: string[]
      alertThresholds: Record<string, number>
      logLevel: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR'
    }
  }
  governance: {
    owner: string
    approvers: string[]
    complianceChecks: Array<{
      checkType: string
      status: 'PASSED' | 'FAILED' | 'PENDING'
      lastChecked: string
    }>
    dataPrivacy: {
      piiHandling: string
      dataRetention: string
      encryptionEnabled: boolean
    }
  }
  changelog: Array<{
    version: string
    date: string
    changes: string[]
    performanceImpact: string
    deployedBy: string
  }>
}
