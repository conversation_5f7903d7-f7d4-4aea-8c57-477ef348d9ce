/**
 * 人才库管理相关类型定义
 *
 * 本模块包含人才库、标签管理、跟踪计划、评估等相关的类型定义
 */

import type { Resume } from './resume'

// 人才库
export interface TalentPool {
  id: number
  resumeId: number
  resume?: Resume
  category: string // 分类：技术类/管理类/营销类等
  level: string // 级别：高级/中级/初级
  tags: string[] // 标签
  source: string // 来源
  status: string // 状态：活跃/储备/已入职
  lastContactTime?: string // 最后联系时间
  nextContactTime?: string // 下次联系时间
  contactRecords?: ContactRecord[] // 联系记录
  notes?: string // 备注
  createUserId: number
  createUserName: string
  createTime: string
  updateTime: string
}

// 人才标签
export interface TalentTag {
  id: number
  name: string
  category: string // 技能类/性格类/行业类/资质类等
  color: string // 标签颜色
  description?: string // 标签描述
  useCount: number // 使用次数
  status: 'active' | 'inactive' // 状态
  createTime: string
  updateTime: string
}

// 标签分类
export interface TagCategory {
  category: string
  name: string // 分类显示名称
  count: number // 该分类下的标签数量
  tags: Array<{
    id: number
    name: string
    color: string
  }>
}

// 标签统计
export interface TagStatistics {
  totalTags: number
  activeTags: number
  topUsedTags: Array<{
    id: number
    name: string
    category: string
    useCount: number
    percentage: number
  }>
  categoryDistribution: Array<{
    category: string
    count: number
    percentage: number
  }>
  usageTrend: Array<{
    date: string
    count: number
  }>
}

// 标签推荐
export interface TagRecommendation {
  id: number
  name: string
  category: string
  score: number // 推荐分数 0-100
  reason: string // 推荐理由
}

// 标签云项
export interface TagCloudItem {
  text: string
  value: number // 权重值
  color: string
  category: string
}

// 联系记录
export interface ContactRecord {
  time: string
  type: string // 电话/邮件/面谈
  content: string
  result?: string
  nextAction?: string
  userId: number
  userName: string
}

// 跟踪计划
export interface TrackingPlan {
  id: number
  talentId: number
  talentName?: string
  planName: string
  trackingType: 'regular' | 'important' | 'potential'
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  objectives: string[]
  startDate: string
  endDate?: string
  progress: number
  lastTrackTime?: string
  nextTrackTime?: string
  status: 'active' | 'paused' | 'completed'
  reminders?: Array<{
    type: 'email' | 'system' | 'sms'
    advanceDays: number
  }>
  createTime: string
  updateTime: string
}

// 跟踪活动记录
export interface TrackingActivity {
  id: number
  planId: number
  planName: string
  talentId: number
  talentName: string
  trackingMethod: 'phone' | 'email' | 'meeting' | 'wechat' | 'other'
  content: string
  result: 'positive' | 'neutral' | 'negative'
  keyFindings: string[]
  nextAction?: string
  attachments?: string[]
  followUpRequired: boolean
  followUpDate?: string
  trackingUserId: number
  trackingUserName: string
  trackingTime: string
  trackingScore?: number
}

// 跟踪任务
export interface TrackingTask {
  id: number
  talentId: number
  talentName: string
  taskType: 'greeting' | 'birthday' | 'holiday' | 'career' | 'custom'
  taskName: string
  description?: string
  dueDate: string
  status: 'pending' | 'completed' | 'overdue'
  priority: 'high' | 'medium' | 'low'
  assigneeId: number
  assigneeName: string
  completedTime?: string
  completionNotes?: string
}

// 跟踪提醒
export interface TrackingReminder {
  id: number
  type: 'plan' | 'task' | 'follow-up'
  relatedId: number
  talentId: number
  talentName: string
  message: string
  dueDate: string
  priority: string
  acknowledged: boolean
}

// 跟踪模板
export interface TrackingTemplate {
  id: number
  type: string
  name: string
  content: string
  variables: string[]
  useCount: number
  createTime: string
  updateTime: string
}

// 评估维度
export interface EvaluationDimension {
  professional: {
    score: number
    details: {
      skills: number
      experience: number
      education: number
      certifications: number
      achievements: number
    }
  }
  potential: {
    score: number
    details: {
      learningAbility: number
      adaptability: number
      leadership: number
      innovation: number
      stability: number
    }
  }
  market: {
    score: number
    details: {
      demandLevel: number
      scarcity: number
      salaryExpectation: number
      competitiveness: number
    }
  }
  cultural: {
    score: number
    details: {
      values: number
      teamwork: number
      communication: number
      workStyle: number
    }
  }
}

// 评估洞察
export interface EvaluationInsight {
  type: 'strength' | 'weakness' | 'opportunity' | 'risk'
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
}

// 评估结果
export interface EvaluationResult {
  talentInfo: {
    id: number
    name: string
    currentPosition?: string
    experience: number
    education: string
  }
  overallScore: number
  level: 'S' | 'A' | 'B' | 'C' | 'D'
  dimensions: EvaluationDimension
  insights: EvaluationInsight[]
  recommendations: {
    hiringDecision: 'highly_recommend' | 'recommend' | 'consider' | 'not_recommend'
    suggestedPositions: string[]
    developmentPlan?: string[]
    retentionStrategy?: string[]
  }
}

// 评估历史记录
export interface EvaluationHistory {
  id: number
  evaluationTime: string
  evaluationType: string
  overallScore: number
  level: string
  evaluatorId: number
  evaluatorName: string
  keyFindings: string[]
  scoreChange?: number
}

// 潜力预测
export interface PotentialPrediction {
  year: number
  predictedLevel: string
  predictedScore: number
  confidence: number
  keyMilestones: string[]
}
