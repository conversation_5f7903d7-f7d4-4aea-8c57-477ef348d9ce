/**
 * 招聘模块类型定义汇总导出
 *
 * 本文件汇总导出所有招聘相关的类型定义，方便统一引用
 */

// 导出招聘需求相关类型
export type { RecruitmentNeed } from './needs'

// 导出职位发布相关类型
export type {
  JobPost,
  RecruitmentJobPostConfig,
  RecruitmentJobPostStatus,
  RecruitmentJobPostTemplate,
  RecruitmentJobPostChannel,
  RecruitmentJobPostAnalytics,
  RecruitmentJobPostBatchOperation
} from './job-post'

// 导出简历相关类型
export type {
  Resume,
  EducationInfo,
  WorkExperience,
  ProjectExperience,
  Certificate,
  Language,
  Attachment,
  Comment
} from './resume'

// 导出人才库相关类型
export type {
  TalentPool,
  TalentTag,
  TagCategory,
  TagStatistics,
  TagRecommendation,
  TagCloudItem,
  ContactRecord,
  TrackingPlan,
  TrackingActivity,
  TrackingTask,
  TrackingReminder,
  TrackingTemplate,
  EvaluationDimension,
  EvaluationInsight,
  EvaluationResult,
  EvaluationHistory,
  PotentialPrediction
} from './talent-pool'

// 导出面试相关类型
export type {
  InterviewSchedule,
  Interviewer,
  InterviewEvaluation,
  EvaluationDimension as InterviewEvaluationDimension,
  InterviewArrangement
} from './interview'

// 导出招聘计划相关类型
export type { RecruitmentPlan, RecruitmentPlanDetail } from './plan'

// 导出分析相关类型
export type {
  RecruitmentStatistics,
  RecruitmentDemandAnalysisConfig,
  RecruitmentDemandAnalysisReport,
  RecruitmentDemandForecastModel,
  RecruitmentDemandMetrics,
  RecruitmentDemandAnalysisTask,
  RecruitmentSkillDemandAnalysis,
  RecruitmentJobEffectivenessAnalysis,
  RecruitmentJobEffectivenessReport,
  RecruitmentJobEffectivenessMetric,
  RecruitmentJobEffectivenessBenchmark
} from './analytics'

// 导出匹配相关类型
export type {
  MatchWeights,
  MatchDetails,
  TalentMatchResult,
  PositionMatchResult,
  MatchConfig,
  MatchHistory,
  MatchOptimizationSuggestion,
  RecruitmentJobMatchingConfig,
  RecruitmentJobMatchingResult,
  RecruitmentJobMatchingBatch,
  RecruitmentJobMatchingPerformance,
  RecruitmentCandidateProfile,
  RecruitmentJobMatchingModel
} from './matching'

// 导出渠道相关类型
export type { RecruitmentChannel, ChannelStatistics } from './channel'

// 导出基础类型
export type {
  RecruitmentProcess,
  ProcessStep,
  RecruitmentJobStatusTracker,
  RecruitmentJobStatusChangeLog,
  RecruitmentJobStatusDashboard,
  RecruitmentJobStatusReport,
  RecruitmentJobStatusNotification
} from './base'
