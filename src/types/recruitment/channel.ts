/**
 * 招聘渠道相关类型定义
 *
 * 本模块包含招聘渠道管理、渠道统计等相关的类型定义
 */

// 招聘渠道
export interface RecruitmentChannel {
  id: number
  name: string
  type: string
  url?: string
  contactInfo?: {
    name: string
    phone: string
    email: string
    address?: string
  }
  contractInfo?: {
    contractNumber: string
    startDate: string
    endDate: string
    cost: number
    paymentMethod: string
  }
  config?: {
    autoPost: boolean
    postTemplate?: string
    refreshFrequency?: number
    keywords?: string[]
  }
  performance?: {
    totalPosts: number
    totalViews: number
    totalApplications: number
    totalHires: number
    conversionRate: number
    averageCostPerHire: number
  }
  status: 'active' | 'inactive' | 'suspended'
  notes?: string
  createUserId: number
  createUserName: string
  createTime: string
  updateTime: string
}

// 渠道统计
export interface ChannelStatistics {
  channelId: number
  channelName: string
  period: {
    startDate: string
    endDate: string
  }
  metrics: {
    posts: {
      total: number
      active: number
      expired: number
      avgDuration: number
    }
    traffic: {
      views: number
      uniqueViews: number
      avgViewTime: number
      bounceRate: number
    }
    applications: {
      total: number
      qualified: number
      inProcess: number
      rejected: number
    }
    hiring: {
      interviews: number
      offers: number
      hires: number
      avgTimeToHire: number
    }
    cost: {
      totalCost: number
      costPerView: number
      costPerApplication: number
      costPerInterview: number
      costPerHire: number
      roi: number
    }
  }
  comparison?: {
    previousPeriod: {
      views: number
      applications: number
      hires: number
      cost: number
    }
    yearOverYear: {
      views: number
      applications: number
      hires: number
      cost: number
    }
  }
  trends: Array<{
    date: string
    views: number
    applications: number
    hires: number
    cost: number
  }>
}
