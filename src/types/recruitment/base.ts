/**
 * 招聘基础类型定义
 *
 * 本模块包含招聘流程、状态跟踪等基础类型定义
 */

// 招聘流程配置
export interface RecruitmentProcess {
  id: number
  name: string
  steps: ProcessStep[]
  isDefault: boolean
  createTime: string
}

// 流程步骤
export interface ProcessStep {
  id: string
  name: string
  type: string // 筛选/面试/审批/其他
  order: number

  config?: unknown // 步骤配置

  conditions?: unknown // 流转条件
}

// 职位状态跟踪器接口
export interface RecruitmentJobStatusTracker {
  trackerId: string
  jobId: string
  jobTitle: string
  department: string
  currentStatus:
    | 'DRAFT'
    | 'PENDING_APPROVAL'
    | 'APPROVED'
    | 'PUBLISHED'
    | 'ACTIVE'
    | 'PAUSED'
    | 'CLOSED'
    | 'CANCELLED'
    | 'ARCHIVED'
  statusDetails: {
    stage:
      | 'PREPARATION'
      | 'SOURCING'
      | 'SCREENING'
      | 'INTERVIEWING'
      | 'DECISION'
      | 'OFFER'
      | 'ONBOARDING'
      | 'COMPLETED'
    subStatus?: string
    progress: number // 0-100
    isStuck: boolean
    stuckDuration?: number // days
    bottlenecks?: string[]
  }
  timeline: {
    createdAt: string
    approvedAt?: string
    publishedAt?: string
    firstApplicationAt?: string
    firstInterviewAt?: string
    firstOfferAt?: string
    closedAt?: string
    totalDuration?: number // days
  }
  metrics: {
    targetHires: number
    actualHires: number
    progressRate: number
    applications: {
      total: number
      qualified: number
      inProcess: number
      rejected: number
    }
    interviews: {
      scheduled: number
      completed: number
      pending: number
      cancelled: number
    }
    offers: {
      extended: number
      accepted: number
      declined: number
      pending: number
    }
  }
  stakeholders: {
    hiringManager: {
      id: string
      name: string
      satisfaction?: number
      lastActivity?: string
    }
    recruiters: Array<{
      id: string
      name: string
      role: 'PRIMARY' | 'SECONDARY' | 'SUPPORT'
      workload: number
    }>
    interviewers: Array<{
      id: string
      name: string
      interviewsCompleted: number
      availability: 'HIGH' | 'MEDIUM' | 'LOW'
    }>
  }
  healthScore: {
    overall: number // 0-100
    factors: {
      pipelineHealth: number
      timeEfficiency: number
      qualityOfCandidates: number
      stakeholderEngagement: number
      processCompliance: number
    }
    risks: Array<{
      riskType: string
      severity: 'HIGH' | 'MEDIUM' | 'LOW'
      description: string
      mitigation: string
    }>
  }
  nextActions: Array<{
    action: string
    owner: string
    dueDate: string
    priority: 'HIGH' | 'MEDIUM' | 'LOW'
    status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'OVERDUE'
  }>
  alerts: Array<{
    alertType: 'DEADLINE' | 'BOTTLENECK' | 'QUALITY' | 'COMPLIANCE' | 'RESOURCE'
    message: string
    severity: 'INFO' | 'WARNING' | 'CRITICAL'
    createdAt: string
    acknowledged: boolean
  }>
  lastUpdated: string
  updatedBy: string
}

// 职位状态变更日志接口
export interface RecruitmentJobStatusChangeLog {
  logId: string
  jobId: string
  timestamp: string
  previousStatus: string
  newStatus: string
  changedBy: {
    userId: string
    userName: string
    role: string
  }
  changeReason: string
  changeDetails?: {
    trigger: 'MANUAL' | 'AUTOMATIC' | 'SCHEDULED' | 'RULE_BASED'
    comments?: string
    approvals?: Array<{
      approverId: string
      approverName: string
      approvedAt: string
      comments?: string
    }>
  }
  impact: {
    affectedCandidates: number
    affectedInterviews: number
    notifications: Array<{
      recipientType: string
      recipientCount: number
      notificationType: string
    }>
  }
  metadata?: Record<string, unknown>
}

// 职位状态仪表板接口
export interface RecruitmentJobStatusDashboard {
  dashboardId: string
  dashboardName: string
  scope: {
    departments?: string[]
    positions?: string[]
    locations?: string[]
    dateRange: [string, string]
  }
  summary: {
    totalJobs: number
    activeJobs: number
    completedJobs: number
    onHoldJobs: number
    cancelledJobs: number
    successRate: number
  }
  statusDistribution: Array<{
    status: string
    count: number
    percentage: number
    trend: 'INCREASING' | 'STABLE' | 'DECREASING'
  }>
  stageAnalysis: Array<{
    stage: string
    jobCount: number
    avgDuration: number
    bottleneckRate: number
    successRate: number
  }>
  performanceMetrics: {
    avgTimeToFill: number
    avgApplicationsPerJob: number
    avgInterviewsPerHire: number
    offerAcceptanceRate: number
    firstYearRetentionRate: number
  }
  trendAnalysis: {
    timeSeriesData: Array<{
      date: string
      metrics: {
        newJobs: number
        activeJobs: number
        completedJobs: number
        applications: number
        hires: number
      }
    }>
    seasonalPatterns: Array<{
      period: string
      pattern: string
      impact: string
    }>
  }
  topPerformers: {
    departments: Array<{
      name: string
      completionRate: number
      avgTimeToFill: number
      qualityScore: number
    }>
    recruiters: Array<{
      name: string
      jobsHandled: number
      successRate: number
      avgTimeToFill: number
    }>
  }
  alerts: {
    critical: number
    warning: number
    info: number
    recentAlerts: Array<{
      alertId: string
      type: string
      message: string
      timestamp: string
      status: 'NEW' | 'ACKNOWLEDGED' | 'RESOLVED'
    }>
  }
  lastRefreshed: string
  refreshFrequency: 'REAL_TIME' | 'HOURLY' | 'DAILY' | 'WEEKLY'
}

// 职位状态报告接口
export interface RecruitmentJobStatusReport {
  reportId: string
  reportName: string
  reportType: 'SUMMARY' | 'DETAILED' | 'EXECUTIVE' | 'OPERATIONAL' | 'CUSTOM'
  generatedAt: string
  generatedBy: string
  reportPeriod: [string, string]
  filters: {
    statuses?: string[]
    departments?: string[]
    positions?: string[]
    recruiters?: string[]
  }
  content: {
    executiveSummary: {
      keyFindings: string[]
      achievements: string[]
      challenges: string[]
      recommendations: string[]
    }
    statusOverview: {
      statusBreakdown: Array<{
        status: string
        count: number
        percentage: number
        changeFromLastPeriod: number
      }>
      flowAnalysis: {
        transitions: Array<{
          from: string
          to: string
          count: number
          avgDuration: number
        }>
        bottlenecks: Array<{
          location: string
          severity: 'HIGH' | 'MEDIUM' | 'LOW'
          impact: string
          recommendation: string
        }>
      }
    }
    performanceAnalysis: {
      kpis: Array<{
        metric: string
        value: number
        target: number
        variance: number
        trend: 'UP' | 'DOWN' | 'STABLE'
      }>
      benchmarks: Array<{
        metric: string
        internal: number
        industry: number
        percentile: number
      }>
    }
    detailedBreakdown: {
      byDepartment: Array<{
        department: string
        metrics: Record<string, number>
        insights: string[]
      }>
      byPosition: Array<{
        position: string
        metrics: Record<string, number>
        insights: string[]
      }>
      byRecruiter: Array<{
        recruiter: string
        metrics: Record<string, number>
        insights: string[]
      }>
    }
    timeAnalysis: {
      averageDurations: Record<string, number>
      distributions: Array<{
        metric: string
        distribution: Array<{
          range: string
          count: number
          percentage: number
        }>
      }>
      trends: Array<{
        metric: string
        dataPoints: Array<{
          date: string
          value: number
        }>
      }>
    }
    qualityMetrics: {
      candidateQuality: number
      hiringManagerSatisfaction: number
      candidateExperience: number
      diversityMetrics: Record<string, number>
    }
    actionItems: Array<{
      priority: 'HIGH' | 'MEDIUM' | 'LOW'
      action: string
      owner: string
      deadline: string
      expectedOutcome: string
    }>
  }
  distribution: Array<{
    recipient: string
    role: string
    deliveryMethod: 'EMAIL' | 'DASHBOARD' | 'DOWNLOAD'
    deliveredAt?: string
  }>
  metadata: {
    dataCompleteness: number
    confidenceLevel: number
    assumptions: string[]
    limitations: string[]
  }
}

// 职位状态通知接口
export interface RecruitmentJobStatusNotification {
  notificationId: string
  jobId: string
  jobTitle: string
  notificationType:
    | 'STATUS_CHANGE'
    | 'DEADLINE_APPROACHING'
    | 'MILESTONE_REACHED'
    | 'ALERT'
    | 'REMINDER'
    | 'ESCALATION'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  triggerEvent: {
    eventType: string
    eventDetails: Record<string, unknown>
    triggeredAt: string
    triggeredBy: 'SYSTEM' | 'USER' | 'SCHEDULE' | 'RULE'
  }
  content: {
    subject: string
    message: string
    actionRequired?: string
    actionUrl?: string
    deadline?: string
    additionalInfo?: Record<string, unknown>
  }
  recipients: Array<{
    userId: string
    userName: string
    role: string
    notificationMethod: 'EMAIL' | 'SMS' | 'IN_APP' | 'SLACK' | 'TEAMS'
    deliveryStatus: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED' | 'BOUNCED'
    deliveredAt?: string
    readAt?: string
    acknowledgedAt?: string
  }>
  escalation?: {
    escalationLevel: number
    escalationPath: Array<{
      level: number
      recipients: string[]
      triggerTime: string
    }>
    currentLevel: number
    nextEscalationAt?: string
  }
  responseTracking: {
    expectedResponse?: 'ACKNOWLEDGE' | 'ACTION' | 'APPROVAL'
    responseDeadline?: string
    responses: Array<{
      userId: string
      responseType: string
      responseTime: string

      responseDetails?: unknown
    }>
  }
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'ACKNOWLEDGED' | 'ACTIONED' | 'EXPIRED' | 'CANCELLED'
  expiresAt?: string
  createdAt: string
  updatedAt: string
}
