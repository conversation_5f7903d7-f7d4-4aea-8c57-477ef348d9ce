/**
 * Type definitions for mock request handlers
 */

/**
 * Mock request options interface
 * Used to type the parameters passed to mock response handlers
 */
export interface MockRequestOptions {
  /** URL parameters from path segments (e.g., /api/users/:id) */
  params?: Record<string, any>
  /** Query string parameters */
  query: Record<string, any>
  /** Request body data */
  body: Record<string, any>
  /** Request headers */
  headers: Record<string, any>
  /** URL object containing parsed URL information */
  url: Record<string, any>
}

/**
 * Typed response function for mock handlers
 */
export type MockResponseFunction = (options: MockRequestOptions) => any