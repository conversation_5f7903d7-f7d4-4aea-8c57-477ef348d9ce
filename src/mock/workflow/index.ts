 
/**
 * 工作流Mock数据
 * @module mock/workflow
 */

import type {
  ProcessDefinition,
  ProcessInstance,
  Task,
  Model,
  ProcessStatistics,
  ProcessHistory
} from '@/workflow/types'

// 流程定义Mock数据
export const mockProcessDefinitions: ProcessDefinition[] = [
  {
    id: 'process-onboarding-v1',
    key: 'employee_onboarding',
    name: '员工入职流程',
    version: 1,
    category: 'hr',
    description: '新员工入职审批流程，包括部门审批、人事审批等环节',
    xml: '', // 实际使用时从 /src/mock/workflow/processes/onboarding.bpmn 加载
    deployTime: '2025-01-10 10:00:00',
    deploymentId: 'deployment-001',
    suspended: false,
    tenantId: 'hky'
  },
  {
    id: 'process-leave-v2',
    key: 'leave_application',
    name: '请假审批流程',
    version: 2,
    category: 'hr',
    description: '员工请假申请流程，根据请假天数自动判断审批层级',
    xml: '', // 实际使用时从 /src/mock/workflow/processes/leave.bpmn 加载
    deployTime: '2025-01-08 14:30:00',
    deploymentId: 'deployment-002',
    suspended: false,
    tenantId: 'hky'
  },
  {
    id: 'process-transfer-v1',
    key: 'position_transfer',
    name: '调岗申请流程',
    version: 1,
    category: 'hr',
    description: '员工岗位调动申请流程，需要原部门和新部门审批',
    xml: '', // 实际使用时从 /src/mock/workflow/processes/transfer.bpmn 加载
    deployTime: '2025-01-05 09:00:00',
    deploymentId: 'deployment-003',
    suspended: false,
    tenantId: 'hky'
  },
  {
    id: 'process-resignation-v1',
    key: 'employee_resignation',
    name: '离职申请流程',
    version: 1,
    category: 'hr',
    description: '员工离职申请流程，包括工作交接、资产归还等环节',
    xml: '', // 实际使用时从 /src/mock/workflow/processes/resignation.bpmn 加载
    deployTime: '2025-01-03 11:20:00',
    deploymentId: 'deployment-004',
    suspended: false,
    tenantId: 'hky'
  }
]

// 流程实例Mock数据
export const mockProcessInstances: ProcessInstance[] = [
  {
    id: 'instance-001',
    processDefinitionId: 'process-onboarding-v1',
    processDefinitionKey: 'employee_onboarding',
    processDefinitionName: '员工入职流程',
    businessKey: 'EMP2025001',
    startTime: '2025-01-11 09:00:00',
    startUserId: 'user-hr-001',
    startUserName: '张三',
    status: 'RUNNING',
    variables: {
      employeeName: '李四',
      department: '技术部',
      position: '前端工程师',
      entryDate: '2025-01-15'
    }
  },
  {
    id: 'instance-002',
    processDefinitionId: 'process-leave-v2',
    processDefinitionKey: 'leave_application',
    processDefinitionName: '请假审批流程',
    businessKey: 'LEAVE2025001',
    startTime: '2025-01-10 14:30:00',
    endTime: '2025-01-10 16:00:00',
    startUserId: 'user-001',
    startUserName: '王五',
    status: 'COMPLETED',
    variables: {
      leaveType: 'annual',
      startDate: '2025-01-12',
      endDate: '2025-01-14',
      days: 3,
      reason: '家庭事务'
    }
  },
  {
    id: 'instance-003',
    processDefinitionId: 'process-leave-v2',
    processDefinitionKey: 'leave_application',
    processDefinitionName: '请假审批流程',
    businessKey: 'LEAVE2025002',
    startTime: '2025-01-11 10:00:00',
    startUserId: 'user-002',
    startUserName: '赵六',
    status: 'RUNNING',
    variables: {
      leaveType: 'sick',
      startDate: '2025-01-11',
      endDate: '2025-01-11',
      days: 1,
      reason: '身体不适'
    }
  },
  {
    id: 'instance-004',
    processDefinitionId: 'process-transfer-v1',
    processDefinitionKey: 'position_transfer',
    processDefinitionName: '调岗申请流程',
    businessKey: 'TRANSFER2025001',
    startTime: '2025-01-09 15:00:00',
    startUserId: 'user-003',
    startUserName: '钱七',
    status: 'SUSPENDED',
    variables: {
      fromDepartment: '市场部',
      toDepartment: '销售部',
      fromPosition: '市场专员',
      toPosition: '销售经理',
      reason: '个人发展需要'
    }
  }
]

// 任务Mock数据
export const mockTasks: Task[] = [
  {
    id: 'task-001',
    name: '部门负责人审批',
    description: '请审批李四的入职申请',
    assignee: 'user-dept-001',
    candidateUsers: [],
    candidateGroups: ['dept_manager'],
    owner: null,
    processInstanceId: 'instance-001',
    processDefinitionId: 'process-onboarding-v1',
    taskDefinitionKey: 'dept_approval',
    createTime: '2025-01-11 09:05:00',
    dueDate: '2025-01-12 18:00:00',
    priority: 50,
    category: 'hr',
    formKey: 'onboarding_form',
    tenantId: 'hky',
    variables: {
      employeeName: '李四',
      department: '技术部',
      position: '前端工程师'
    }
  },
  {
    id: 'task-002',
    name: '直属领导审批',
    description: '请审批赵六的病假申请',
    assignee: 'user-leader-001',
    candidateUsers: [],
    candidateGroups: ['team_leader'],
    owner: null,
    processInstanceId: 'instance-003',
    processDefinitionId: 'process-leave-v2',
    taskDefinitionKey: 'leader_approval',
    createTime: '2025-01-11 10:05:00',
    dueDate: '2025-01-11 18:00:00',
    priority: 60,
    category: 'hr',
    formKey: 'leave_form',
    tenantId: 'hky',
    variables: {
      applicant: '赵六',
      leaveType: 'sick',
      days: 1
    }
  },
  {
    id: 'task-003',
    name: '新部门审批',
    description: '请审批钱七的调岗申请',
    assignee: null,
    candidateUsers: ['user-sales-001', 'user-sales-002'],
    candidateGroups: ['sales_manager'],
    owner: null,
    processInstanceId: 'instance-004',
    processDefinitionId: 'process-transfer-v1',
    taskDefinitionKey: 'new_dept_approval',
    createTime: '2025-01-09 16:00:00',
    dueDate: '2025-01-13 18:00:00',
    priority: 40,
    category: 'hr',
    formKey: 'transfer_form',
    tenantId: 'hky',
    variables: {
      applicant: '钱七',
      toDepartment: '销售部',
      toPosition: '销售经理'
    }
  }
]

// 流程模型Mock数据
export const mockModels: Model[] = [
  {
    id: 'model-001',
    name: '员工入职流程',
    key: 'employee_onboarding',
    category: 'hr',
    version: 1,
    metaInfo: JSON.stringify({
      description: '新员工入职审批流程模型',
      author: 'admin',
      createTime: '2025-01-01 10:00:00'
    }),
    deploymentId: 'deployment-001',
    createTime: '2025-01-01 10:00:00',
    lastUpdateTime: '2025-01-10 09:30:00'
  },
  {
    id: 'model-002',
    name: '请假审批流程',
    key: 'leave_application',
    category: 'hr',
    version: 2,
    metaInfo: JSON.stringify({
      description: '员工请假申请流程模型',
      author: 'admin',
      createTime: '2025-01-02 11:00:00'
    }),
    deploymentId: 'deployment-002',
    createTime: '2025-01-02 11:00:00',
    lastUpdateTime: '2025-01-08 14:00:00'
  },
  {
    id: 'model-003',
    name: '采购审批流程',
    key: 'purchase_approval',
    category: 'finance',
    version: 1,
    metaInfo: JSON.stringify({
      description: '采购申请审批流程模型（开发中）',
      author: 'admin',
      createTime: '2025-01-05 14:00:00'
    }),
    createTime: '2025-01-05 14:00:00',
    lastUpdateTime: '2025-01-10 16:00:00'
  }
]

// 流程统计Mock数据
export const mockProcessStatistics: ProcessStatistics = {
  total: 156,
  running: 42,
  completed: 98,
  suspended: 6,
  terminated: 10,
  todayStarted: 12,
  todayCompleted: 8,
  averageDuration: 3.5 * 24 * 60 * 60 * 1000, // 3.5天
  categories: [
    { category: 'hr', count: 120, percentage: 76.9 },
    { category: 'finance', count: 25, percentage: 16.0 },
    { category: 'admin', count: 11, percentage: 7.1 }
  ],
  trends: [
    { date: '2025-01-05', started: 8, completed: 6 },
    { date: '2025-01-06', started: 10, completed: 8 },
    { date: '2025-01-07', started: 12, completed: 10 },
    { date: '2025-01-08', started: 15, completed: 12 },
    { date: '2025-01-09', started: 11, completed: 9 },
    { date: '2025-01-10', started: 14, completed: 11 },
    { date: '2025-01-11', started: 12, completed: 8 }
  ]
}

// 流程历史Mock数据
export const mockProcessHistory: ProcessHistory[] = [
  {
    id: 'history-001',
    processInstanceId: 'instance-002',
    activityId: 'startEvent',
    activityName: '开始',
    activityType: 'startEvent',
    startTime: '2025-01-10 14:30:00',
    endTime: '2025-01-10 14:30:01',
    duration: 1000,
    assignee: 'user-001'
  },
  {
    id: 'history-002',
    processInstanceId: 'instance-002',
    activityId: 'userTask_leader',
    activityName: '直属领导审批',
    activityType: 'userTask',
    startTime: '2025-01-10 14:30:01',
    endTime: '2025-01-10 15:00:00',
    duration: 30 * 60 * 1000,
    assignee: 'user-leader-002'
  },
  {
    id: 'history-003',
    processInstanceId: 'instance-002',
    activityId: 'userTask_hr',
    activityName: '人事审批',
    activityType: 'userTask',
    startTime: '2025-01-10 15:00:00',
    endTime: '2025-01-10 16:00:00',
    duration: 60 * 60 * 1000,
    assignee: 'user-hr-002'
  },
  {
    id: 'history-004',
    processInstanceId: 'instance-002',
    activityId: 'endEvent',
    activityName: '结束',
    activityType: 'endEvent',
    startTime: '2025-01-10 16:00:00',
    endTime: '2025-01-10 16:00:01',
    duration: 1000,
    assignee: null
  }
]

// Mock数据生成函数
export function generateMockTasks(count: number): Task[] {
  const tasks: Task[] = []
  const taskNames = ['部门审批', '领导审批', '人事审批', '财务审批', '总经理审批']
  const assignees = ['user-001', 'user-002', 'user-003', 'user-004', 'user-005']
  
  for (let i = 0; i < count; i++) {
    tasks.push({
      id: `task-${Date.now()}-${i}`,
      name: taskNames[i % taskNames.length],
      description: `任务描述 ${i + 1}`,
      assignee: assignees[i % assignees.length],
      candidateUsers: [],
      candidateGroups: [],
      owner: null,
      processInstanceId: `instance-${Math.floor(i / 3)}`,
      processDefinitionId: mockProcessDefinitions[i % mockProcessDefinitions.length].id,
      taskDefinitionKey: `task_${i}`,
      createTime: new Date(Date.now() - i * 60 * 60 * 1000).toISOString(),
      dueDate: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000).toISOString(),
      priority: 50,
      category: 'hr',
      formKey: 'task_form',
      tenantId: 'hky',
      variables: {}
    })
  }
  
  return tasks
}

// Mock API响应
export const mockWorkflowResponses = {
  // 获取流程定义列表
  getProcessDefinitions: () => ({
    code: 0,
    message: 'success',
    data: {
      total: mockProcessDefinitions.length,
      page: 1,
      size: 20,
      list: mockProcessDefinitions
    },
    timestamp: Date.now()
  }),
  
  // 获取流程实例列表
  getProcessInstances: () => ({
    code: 0,
    message: 'success',
    data: {
      total: mockProcessInstances.length,
      page: 1,
      size: 20,
      list: mockProcessInstances
    },
    timestamp: Date.now()
  }),
  
  // 获取任务列表
  getTasks: () => ({
    code: 0,
    message: 'success',
    data: {
      total: mockTasks.length,
      page: 1,
      size: 20,
      list: mockTasks
    },
    timestamp: Date.now()
  }),
  
  // 获取流程统计
  getProcessStatistics: () => ({
    code: 0,
    message: 'success',
    data: mockProcessStatistics,
    timestamp: Date.now()
  }),
  
  // 启动流程
  startProcess: (key: string) => ({
    code: 0,
    message: 'success',
    data: {
      id: `instance-${Date.now()}`,
      processDefinitionId: mockProcessDefinitions.find(d => d.key === key)?.id,
      processDefinitionKey: key,
      processDefinitionName: mockProcessDefinitions.find(d => d.key === key)?.name,
      businessKey: `BK${Date.now()}`,
      startTime: new Date().toISOString(),
      startUserId: 'current-user',
      startUserName: '当前用户',
      status: 'RUNNING' as const,
      variables: {}
    },
    timestamp: Date.now()
  }),
  
  // 完成任务
  completeTask: (taskId: string) => ({
    code: 0,
    message: 'success',
    data: null,
    timestamp: Date.now()
  })
}