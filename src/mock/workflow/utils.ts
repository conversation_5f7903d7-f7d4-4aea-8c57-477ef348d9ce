/**
 * 工作流Mock数据工具函数
 * @module mock/workflow/utils
 */

import type { ProcessDefinition } from '@/workflow/types'

// BPMN文件内容映射
const bpmnXmlMap: Record<string, string> = {}

/**
 * 加载BPMN XML内容
 * @param key - 流程定义key
 * @returns Promise<string>
 */
export async function loadBpmnXml(key: string): Promise<string> {
  // 在实际应用中，这里会从文件系统或服务器加载BPMN文件
  // 由于浏览器环境限制，这里使用静态字符串模拟

  if (bpmnXmlMap[key]) {
    return bpmnXmlMap[key]
  }

  // 模拟异步加载
  return new Promise(__resolve => {
    setTimeout(() => {
      // 返回简化的BPMN XML（实际使用时应从文件加载）
      const xml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  id="Definitions_${key}" 
                  targetNamespace="http://hr.hky.edu.cn/bpmn">
  <bpmn:process id="${key}" name="${key}" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" />
  </bpmn:process>
</bpmn:definitions>`

      bpmnXmlMap[key] = xml
      resolve(xml)
    }, 100)
  })
}

/**
 * 获取带有XML内容的流程定义
 * @param definitions - 流程定义列表
 * @returns Promise<ProcessDefinition[]>
 */
export async function getProcessDefinitionsWithXml(
  definitions: ProcessDefinition[]
): Promise<ProcessDefinition[]> {
  const promises = definitions.map(async _def => {
    const xml = await loadBpmnXml(def.key)
    return { ...def, xml }
  })

  return Promise.all(promises)
}

/**
 * 生成流程实例ID
 * @returns string
 */
export function generateProcessInstanceId(): string {
  return `instance-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 生成任务ID
 * @returns string
 */
export function generateTaskId(): string {
  return `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 生成业务键
 * @param prefix - 业务键前缀
 * @returns string
 */
export function generateBusinessKey(prefix: string): string {
  const year = new Date().getFullYear()
  const sequence = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0')
  return `${prefix}${year}${sequence}`
}

/**
 * 格式化流程持续时间
 * @param startTime - 开始时间
 * @param endTime - 结束时间（可选）
 * @returns string
 */
export function formatDuration(startTime: string, endTime?: string): string {
  const start = new Date(startTime).getTime()
  const end = endTime ? new Date(endTime).getTime() : Date.now()
  const duration = end - start

  const days = Math.floor(duration / (1000 * 60 * 60 * 24))
  const hours = Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) {
    return `${days}天${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

/**
 * 模拟流程执行进度
 * @param processInstanceId - 流程实例ID
 * @returns number (0-100)
 */
export function getProcessProgress(processInstanceId: string): number {
  // 模拟计算进度
  const hash = processInstanceId.split('').reduce((acc, char) => {
    return acc + char.charCodeAt(0)
  }, 0)

  return hash % 101
}

/**
 * 获取流程状态颜色
 * @param status - 流程状态
 * @returns string
 */
export function getProcessStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    RUNNING: '#409EFF', // 蓝色
    COMPLETED: '#67C23A', // 绿色
    SUSPENDED: '#E6A23C', // 橙色
    TERMINATED: '#F56C6C', // 红色
    CANCELLED: '#909399' // 灰色
  }

  return colorMap[status] || '#606266'
}

/**
 * 获取任务优先级标签
 * @param priority - 优先级（0-100）
 * @returns object
 */
export function getTaskPriorityLabel(priority: number): {
  text: string
  type: 'danger' | 'warning' | 'success' | 'info'
} {
  if (priority >= 75) {
    return { text: '紧急', type: 'danger' }
  } else if (priority >= 50) {
    return { text: '高', type: 'warning' }
  } else if (priority >= 25) {
    return { text: '中', type: 'success' }
  } else {
    return { text: '低', type: 'info' }
  }
}
