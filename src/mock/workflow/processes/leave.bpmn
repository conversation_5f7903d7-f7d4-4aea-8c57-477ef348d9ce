<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                  xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  xmlns:hr="http://hr.hky.edu.cn/schema/bpmn"
                  id="Definitions_leave" 
                  targetNamespace="http://hr.hky.edu.cn/bpmn">
  
  <bpmn:process id="leave_application" name="请假审批流程" isExecutable="true" hr:category="hr">
    
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_start_to_apply</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:userTask id="Task_apply" name="填写请假申请" hr:assigneeType="initiator" hr:formKey="leave_form">
      <bpmn:incoming>Flow_start_to_apply</bpmn:incoming>
      <bpmn:outgoing>Flow_apply_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_days" name="请假天数判断">
      <bpmn:incoming>Flow_apply_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_less_than_3</bpmn:outgoing>
      <bpmn:outgoing>Flow_3_to_7</bpmn:outgoing>
      <bpmn:outgoing>Flow_more_than_7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 小于3天：只需直属领导审批 -->
    <bpmn:userTask id="Task_leader_approval" name="直属领导审批" hr:assigneeType="directLeader" hr:formKey="approval_form">
      <bpmn:incoming>Flow_less_than_3</bpmn:incoming>
      <bpmn:outgoing>Flow_leader_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_leader_result" name="直属领导审批结果">
      <bpmn:incoming>Flow_leader_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_leader_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_leader_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 3-7天：需要部门负责人审批 -->
    <bpmn:userTask id="Task_dept_approval" name="部门负责人审批" hr:assigneeType="department" hr:formKey="approval_form">
      <bpmn:incoming>Flow_3_to_7</bpmn:incoming>
      <bpmn:incoming>Flow_leader_approved</bpmn:incoming>
      <bpmn:outgoing>Flow_dept_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_dept_result" name="部门审批结果">
      <bpmn:incoming>Flow_dept_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_dept_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_dept_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 大于7天：需要分管领导审批 -->
    <bpmn:userTask id="Task_vp_approval" name="分管领导审批" hr:assigneeType="role" hr:candidateGroups="vp" hr:formKey="approval_form">
      <bpmn:incoming>Flow_more_than_7</bpmn:incoming>
      <bpmn:incoming>Flow_dept_approved_long</bpmn:incoming>
      <bpmn:outgoing>Flow_vp_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_vp_result" name="分管领导审批结果">
      <bpmn:incoming>Flow_vp_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_vp_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_vp_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 人事部门备案 -->
    <bpmn:userTask id="Task_hr_record" name="人事备案" hr:assigneeType="role" hr:candidateGroups="hr_staff" hr:formKey="record_form">
      <bpmn:incoming>Flow_leader_approved_short</bpmn:incoming>
      <bpmn:incoming>Flow_dept_approved_medium</bpmn:incoming>
      <bpmn:incoming>Flow_vp_approved</bpmn:incoming>
      <bpmn:outgoing>Flow_hr_to_notify</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 发送通知 -->
    <bpmn:serviceTask id="Task_notify" name="发送请假通知">
      <bpmn:incoming>Flow_hr_to_notify</bpmn:incoming>
      <bpmn:outgoing>Flow_notify_to_end</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:endEvent id="EndEvent_success" name="请假成功">
      <bpmn:incoming>Flow_notify_to_end</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:endEvent id="EndEvent_rejected" name="申请驳回">
      <bpmn:incoming>Flow_leader_rejected</bpmn:incoming>
      <bpmn:incoming>Flow_dept_rejected</bpmn:incoming>
      <bpmn:incoming>Flow_vp_rejected</bpmn:incoming>
    </bpmn:endEvent>
    
    <!-- 流程连线 -->
    <bpmn:sequenceFlow id="Flow_start_to_apply" sourceRef="StartEvent_1" targetRef="Task_apply" />
    <bpmn:sequenceFlow id="Flow_apply_to_gateway" sourceRef="Task_apply" targetRef="Gateway_days" />
    
    <!-- 小于3天流程 -->
    <bpmn:sequenceFlow id="Flow_less_than_3" name="小于3天" sourceRef="Gateway_days" targetRef="Task_leader_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${days &lt; 3}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_leader_to_gateway" sourceRef="Task_leader_approval" targetRef="Gateway_leader_result" />
    <bpmn:sequenceFlow id="Flow_leader_approved" name="同意" sourceRef="Gateway_leader_result" targetRef="Task_dept_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true &amp;&amp; days &gt;= 3}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_leader_approved_short" name="同意" sourceRef="Gateway_leader_result" targetRef="Task_hr_record">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true &amp;&amp; days &lt; 3}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_leader_rejected" name="驳回" sourceRef="Gateway_leader_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 3-7天流程 -->
    <bpmn:sequenceFlow id="Flow_3_to_7" name="3-7天" sourceRef="Gateway_days" targetRef="Task_dept_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${days &gt;= 3 &amp;&amp; days &lt;= 7}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_dept_to_gateway" sourceRef="Task_dept_approval" targetRef="Gateway_dept_result" />
    <bpmn:sequenceFlow id="Flow_dept_approved" name="同意" sourceRef="Gateway_dept_result" targetRef="Task_vp_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true &amp;&amp; days &gt; 7}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_dept_approved_medium" name="同意" sourceRef="Gateway_dept_result" targetRef="Task_hr_record">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true &amp;&amp; days &lt;= 7}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_dept_approved_long" name="同意" sourceRef="Gateway_dept_result" targetRef="Task_vp_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true &amp;&amp; days &gt; 7}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_dept_rejected" name="驳回" sourceRef="Gateway_dept_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 大于7天流程 -->
    <bpmn:sequenceFlow id="Flow_more_than_7" name="大于7天" sourceRef="Gateway_days" targetRef="Task_vp_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${days &gt; 7}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_vp_to_gateway" sourceRef="Task_vp_approval" targetRef="Gateway_vp_result" />
    <bpmn:sequenceFlow id="Flow_vp_approved" name="同意" sourceRef="Gateway_vp_result" targetRef="Task_hr_record">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_vp_rejected" name="驳回" sourceRef="Gateway_vp_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <bpmn:sequenceFlow id="Flow_hr_to_notify" sourceRef="Task_hr_record" targetRef="Task_notify" />
    <bpmn:sequenceFlow id="Flow_notify_to_end" sourceRef="Task_notify" targetRef="EndEvent_success" />
    
  </bpmn:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="leave_application">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_1" bpmnElement="StartEvent_1">
        <dc:Bounds x="170" y="200" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="177" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_apply_di" bpmnElement="Task_apply">
        <dc:Bounds x="270" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_days_di" bpmnElement="Gateway_days" isMarkerVisible="true">
        <dc:Bounds x="435" y="193" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="423" y="163" width="74" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_leader_approval_di" bpmnElement="Task_leader_approval">
        <dc:Bounds x="550" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_dept_approval_di" bpmnElement="Task_dept_approval">
        <dc:Bounds x="550" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_vp_approval_di" bpmnElement="Task_vp_approval">
        <dc:Bounds x="550" y="300" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_leader_result_di" bpmnElement="Gateway_leader_result" isMarkerVisible="true">
        <dc:Bounds x="715" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="697" y="65" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_dept_result_di" bpmnElement="Gateway_dept_result" isMarkerVisible="true">
        <dc:Bounds x="715" y="193" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="702" y="250" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_vp_result_di" bpmnElement="Gateway_vp_result" isMarkerVisible="true">
        <dc:Bounds x="715" y="315" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="697" y="372" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_hr_record_di" bpmnElement="Task_hr_record">
        <dc:Bounds x="890" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_notify_di" bpmnElement="Task_notify">
        <dc:Bounds x="1050" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_success_di" bpmnElement="EndEvent_success">
        <dc:Bounds x="1212" y="200" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1206" y="243" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_rejected_di" bpmnElement="EndEvent_rejected">
        <dc:Bounds x="892" y="422" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="886" y="465" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>