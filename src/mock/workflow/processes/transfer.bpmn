<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                  xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  xmlns:hr="http://hr.hky.edu.cn/schema/bpmn"
                  id="Definitions_transfer" 
                  targetNamespace="http://hr.hky.edu.cn/bpmn">
  
  <bpmn:process id="position_transfer" name="调岗申请流程" isExecutable="true" hr:category="hr">
    
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_start_to_apply</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:userTask id="Task_apply" name="填写调岗申请" hr:assigneeType="initiator" hr:formKey="transfer_form">
      <bpmn:incoming>Flow_start_to_apply</bpmn:incoming>
      <bpmn:outgoing>Flow_apply_to_current_dept</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 原部门审批 -->
    <bpmn:userTask id="Task_current_dept_approval" name="原部门负责人审批" hr:assigneeType="currentDepartment" hr:formKey="approval_form">
      <bpmn:incoming>Flow_apply_to_current_dept</bpmn:incoming>
      <bpmn:outgoing>Flow_current_dept_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_current_dept_result" name="原部门审批结果">
      <bpmn:incoming>Flow_current_dept_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_current_dept_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_current_dept_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 新部门审批 -->
    <bpmn:userTask id="Task_new_dept_approval" name="新部门负责人审批" hr:assigneeType="targetDepartment" hr:formKey="approval_form">
      <bpmn:incoming>Flow_current_dept_approved</bpmn:incoming>
      <bpmn:outgoing>Flow_new_dept_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_new_dept_result" name="新部门审批结果">
      <bpmn:incoming>Flow_new_dept_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_new_dept_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_new_dept_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 人事审批 -->
    <bpmn:userTask id="Task_hr_approval" name="人事部门审批" hr:assigneeType="role" hr:candidateGroups="hr_manager" hr:formKey="hr_approval_form">
      <bpmn:incoming>Flow_new_dept_approved</bpmn:incoming>
      <bpmn:outgoing>Flow_hr_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_hr_result" name="人事审批结果">
      <bpmn:incoming>Flow_hr_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_hr_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_hr_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 分管领导审批（如果需要） -->
    <bpmn:exclusiveGateway id="Gateway_need_vp" name="是否需要分管领导审批">
      <bpmn:incoming>Flow_hr_approved</bpmn:incoming>
      <bpmn:outgoing>Flow_need_vp</bpmn:outgoing>
      <bpmn:outgoing>Flow_no_need_vp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <bpmn:userTask id="Task_vp_approval" name="分管领导审批" hr:assigneeType="role" hr:candidateGroups="vp" hr:formKey="approval_form">
      <bpmn:incoming>Flow_need_vp</bpmn:incoming>
      <bpmn:outgoing>Flow_vp_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_vp_result" name="分管领导审批结果">
      <bpmn:incoming>Flow_vp_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_vp_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_vp_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 工作交接 -->
    <bpmn:userTask id="Task_handover" name="工作交接" hr:assigneeType="initiator" hr:formKey="handover_form">
      <bpmn:incoming>Flow_vp_approved</bpmn:incoming>
      <bpmn:incoming>Flow_no_need_vp</bpmn:incoming>
      <bpmn:outgoing>Flow_handover_to_confirm</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 交接确认 -->
    <bpmn:userTask id="Task_handover_confirm" name="交接确认" hr:assigneeType="currentDepartment" hr:formKey="handover_confirm_form">
      <bpmn:incoming>Flow_handover_to_confirm</bpmn:incoming>
      <bpmn:outgoing>Flow_confirm_to_update</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 更新系统信息 -->
    <bpmn:serviceTask id="Task_update_system" name="更新系统信息">
      <bpmn:incoming>Flow_confirm_to_update</bpmn:incoming>
      <bpmn:outgoing>Flow_update_to_notify</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <!-- 发送通知 -->
    <bpmn:serviceTask id="Task_notify" name="发送调岗通知">
      <bpmn:incoming>Flow_update_to_notify</bpmn:incoming>
      <bpmn:outgoing>Flow_notify_to_end</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:endEvent id="EndEvent_success" name="调岗成功">
      <bpmn:incoming>Flow_notify_to_end</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:endEvent id="EndEvent_rejected" name="申请驳回">
      <bpmn:incoming>Flow_current_dept_rejected</bpmn:incoming>
      <bpmn:incoming>Flow_new_dept_rejected</bpmn:incoming>
      <bpmn:incoming>Flow_hr_rejected</bpmn:incoming>
      <bpmn:incoming>Flow_vp_rejected</bpmn:incoming>
    </bpmn:endEvent>
    
    <!-- 流程连线 -->
    <bpmn:sequenceFlow id="Flow_start_to_apply" sourceRef="StartEvent_1" targetRef="Task_apply" />
    <bpmn:sequenceFlow id="Flow_apply_to_current_dept" sourceRef="Task_apply" targetRef="Task_current_dept_approval" />
    
    <!-- 原部门审批流程 -->
    <bpmn:sequenceFlow id="Flow_current_dept_to_gateway" sourceRef="Task_current_dept_approval" targetRef="Gateway_current_dept_result" />
    <bpmn:sequenceFlow id="Flow_current_dept_approved" name="同意" sourceRef="Gateway_current_dept_result" targetRef="Task_new_dept_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_current_dept_rejected" name="驳回" sourceRef="Gateway_current_dept_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 新部门审批流程 -->
    <bpmn:sequenceFlow id="Flow_new_dept_to_gateway" sourceRef="Task_new_dept_approval" targetRef="Gateway_new_dept_result" />
    <bpmn:sequenceFlow id="Flow_new_dept_approved" name="同意" sourceRef="Gateway_new_dept_result" targetRef="Task_hr_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_new_dept_rejected" name="驳回" sourceRef="Gateway_new_dept_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 人事审批流程 -->
    <bpmn:sequenceFlow id="Flow_hr_to_gateway" sourceRef="Task_hr_approval" targetRef="Gateway_hr_result" />
    <bpmn:sequenceFlow id="Flow_hr_approved" name="同意" sourceRef="Gateway_hr_result" targetRef="Gateway_need_vp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_hr_rejected" name="驳回" sourceRef="Gateway_hr_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 分管领导审批判断 -->
    <bpmn:sequenceFlow id="Flow_need_vp" name="需要" sourceRef="Gateway_need_vp" targetRef="Task_vp_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${crossDivision == true || positionLevel &gt;= 5}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_no_need_vp" name="不需要" sourceRef="Gateway_need_vp" targetRef="Task_handover">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${crossDivision == false &amp;&amp; positionLevel &lt; 5}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 分管领导审批流程 -->
    <bpmn:sequenceFlow id="Flow_vp_to_gateway" sourceRef="Task_vp_approval" targetRef="Gateway_vp_result" />
    <bpmn:sequenceFlow id="Flow_vp_approved" name="同意" sourceRef="Gateway_vp_result" targetRef="Task_handover">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_vp_rejected" name="驳回" sourceRef="Gateway_vp_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 工作交接流程 -->
    <bpmn:sequenceFlow id="Flow_handover_to_confirm" sourceRef="Task_handover" targetRef="Task_handover_confirm" />
    <bpmn:sequenceFlow id="Flow_confirm_to_update" sourceRef="Task_handover_confirm" targetRef="Task_update_system" />
    <bpmn:sequenceFlow id="Flow_update_to_notify" sourceRef="Task_update_system" targetRef="Task_notify" />
    <bpmn:sequenceFlow id="Flow_notify_to_end" sourceRef="Task_notify" targetRef="EndEvent_success" />
    
  </bpmn:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="position_transfer">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_1" bpmnElement="StartEvent_1">
        <dc:Bounds x="170" y="300" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="177" y="343" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_apply_di" bpmnElement="Task_apply">
        <dc:Bounds x="270" y="278" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_current_dept_approval_di" bpmnElement="Task_current_dept_approval">
        <dc:Bounds x="430" y="278" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_current_dept_result_di" bpmnElement="Gateway_current_dept_result" isMarkerVisible="true">
        <dc:Bounds x="595" y="293" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="578" y="263" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_new_dept_approval_di" bpmnElement="Task_new_dept_approval">
        <dc:Bounds x="710" y="278" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_new_dept_result_di" bpmnElement="Gateway_new_dept_result" isMarkerVisible="true">
        <dc:Bounds x="875" y="293" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="858" y="263" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_hr_approval_di" bpmnElement="Task_hr_approval">
        <dc:Bounds x="990" y="278" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_hr_result_di" bpmnElement="Gateway_hr_result" isMarkerVisible="true">
        <dc:Bounds x="1155" y="293" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1145" y="263" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_need_vp_di" bpmnElement="Gateway_need_vp" isMarkerVisible="true">
        <dc:Bounds x="1270" y="293" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1252" y="253" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_vp_approval_di" bpmnElement="Task_vp_approval">
        <dc:Bounds x="1385" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_vp_result_di" bpmnElement="Gateway_vp_result" isMarkerVisible="true">
        <dc:Bounds x="1550" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1532" y="165" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_handover_di" bpmnElement="Task_handover">
        <dc:Bounds x="1665" y="278" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_handover_confirm_di" bpmnElement="Task_handover_confirm">
        <dc:Bounds x="1830" y="278" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_update_system_di" bpmnElement="Task_update_system">
        <dc:Bounds x="1995" y="278" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_notify_di" bpmnElement="Task_notify">
        <dc:Bounds x="2160" y="278" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_success_di" bpmnElement="EndEvent_success">
        <dc:Bounds x="2322" y="300" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2316" y="343" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_rejected_di" bpmnElement="EndEvent_rejected">
        <dc:Bounds x="1012" y="512" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1006" y="555" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>