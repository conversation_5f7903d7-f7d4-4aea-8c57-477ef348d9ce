<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                  xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  xmlns:hr="http://hr.hky.edu.cn/schema/bpmn"
                  id="Definitions_resignation" 
                  targetNamespace="http://hr.hky.edu.cn/bpmn">
  
  <bpmn:process id="employee_resignation" name="离职申请流程" isExecutable="true" hr:category="hr">
    
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_start_to_apply</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:userTask id="Task_apply" name="填写离职申请" hr:assigneeType="initiator" hr:formKey="resignation_form">
      <bpmn:incoming>Flow_start_to_apply</bpmn:incoming>
      <bpmn:outgoing>Flow_apply_to_dept</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 部门负责人审批 -->
    <bpmn:userTask id="Task_dept_approval" name="部门负责人审批" hr:assigneeType="department" hr:formKey="approval_form">
      <bpmn:incoming>Flow_apply_to_dept</bpmn:incoming>
      <bpmn:outgoing>Flow_dept_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_dept_result" name="部门审批结果">
      <bpmn:incoming>Flow_dept_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_dept_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_dept_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 工作交接 -->
    <bpmn:userTask id="Task_handover" name="工作交接" hr:assigneeType="initiator" hr:formKey="handover_form">
      <bpmn:incoming>Flow_dept_approved</bpmn:incoming>
      <bpmn:outgoing>Flow_handover_to_parallel</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 并行网关：开始多个并行任务 -->
    <bpmn:parallelGateway id="Gateway_parallel_start" name="并行任务开始">
      <bpmn:incoming>Flow_handover_to_parallel</bpmn:incoming>
      <bpmn:outgoing>Flow_parallel_to_finance</bpmn:outgoing>
      <bpmn:outgoing>Flow_parallel_to_it</bpmn:outgoing>
      <bpmn:outgoing>Flow_parallel_to_admin</bpmn:outgoing>
    </bpmn:parallelGateway>
    
    <!-- 财务结算 -->
    <bpmn:userTask id="Task_finance_settle" name="财务结算" hr:assigneeType="role" hr:candidateGroups="finance_staff" hr:formKey="finance_settle_form">
      <bpmn:incoming>Flow_parallel_to_finance</bpmn:incoming>
      <bpmn:outgoing>Flow_finance_to_parallel_end</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- IT资产回收 -->
    <bpmn:userTask id="Task_it_asset" name="IT资产回收" hr:assigneeType="role" hr:candidateGroups="it_staff" hr:formKey="it_asset_form">
      <bpmn:incoming>Flow_parallel_to_it</bpmn:incoming>
      <bpmn:outgoing>Flow_it_to_parallel_end</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 行政资产回收 -->
    <bpmn:userTask id="Task_admin_asset" name="行政资产回收" hr:assigneeType="role" hr:candidateGroups="admin_staff" hr:formKey="admin_asset_form">
      <bpmn:incoming>Flow_parallel_to_admin</bpmn:incoming>
      <bpmn:outgoing>Flow_admin_to_parallel_end</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 并行网关：结束多个并行任务 -->
    <bpmn:parallelGateway id="Gateway_parallel_end" name="并行任务结束">
      <bpmn:incoming>Flow_finance_to_parallel_end</bpmn:incoming>
      <bpmn:incoming>Flow_it_to_parallel_end</bpmn:incoming>
      <bpmn:incoming>Flow_admin_to_parallel_end</bpmn:incoming>
      <bpmn:outgoing>Flow_parallel_end_to_hr</bpmn:outgoing>
    </bpmn:parallelGateway>
    
    <!-- 人事审批 -->
    <bpmn:userTask id="Task_hr_approval" name="人事最终审批" hr:assigneeType="role" hr:candidateGroups="hr_manager" hr:formKey="hr_final_approval_form">
      <bpmn:incoming>Flow_parallel_end_to_hr</bpmn:incoming>
      <bpmn:outgoing>Flow_hr_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_hr_result" name="人事审批结果">
      <bpmn:incoming>Flow_hr_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_hr_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_hr_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 办理离职手续 -->
    <bpmn:serviceTask id="Task_process_resignation" name="办理离职手续">
      <bpmn:incoming>Flow_hr_approved</bpmn:incoming>
      <bpmn:outgoing>Flow_process_to_disable</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <!-- 禁用系统账号 -->
    <bpmn:serviceTask id="Task_disable_account" name="禁用系统账号">
      <bpmn:incoming>Flow_process_to_disable</bpmn:incoming>
      <bpmn:outgoing>Flow_disable_to_archive</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <!-- 归档员工信息 -->
    <bpmn:serviceTask id="Task_archive_info" name="归档员工信息">
      <bpmn:incoming>Flow_disable_to_archive</bpmn:incoming>
      <bpmn:outgoing>Flow_archive_to_notify</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <!-- 发送离职通知 -->
    <bpmn:serviceTask id="Task_notify" name="发送离职通知">
      <bpmn:incoming>Flow_archive_to_notify</bpmn:incoming>
      <bpmn:outgoing>Flow_notify_to_end</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:endEvent id="EndEvent_success" name="离职完成">
      <bpmn:incoming>Flow_notify_to_end</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:endEvent id="EndEvent_rejected" name="申请驳回">
      <bpmn:incoming>Flow_dept_rejected</bpmn:incoming>
      <bpmn:incoming>Flow_hr_rejected</bpmn:incoming>
    </bpmn:endEvent>
    
    <!-- 流程连线 -->
    <bpmn:sequenceFlow id="Flow_start_to_apply" sourceRef="StartEvent_1" targetRef="Task_apply" />
    <bpmn:sequenceFlow id="Flow_apply_to_dept" sourceRef="Task_apply" targetRef="Task_dept_approval" />
    
    <!-- 部门审批流程 -->
    <bpmn:sequenceFlow id="Flow_dept_to_gateway" sourceRef="Task_dept_approval" targetRef="Gateway_dept_result" />
    <bpmn:sequenceFlow id="Flow_dept_approved" name="同意" sourceRef="Gateway_dept_result" targetRef="Task_handover">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_dept_rejected" name="驳回" sourceRef="Gateway_dept_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 工作交接和并行任务 -->
    <bpmn:sequenceFlow id="Flow_handover_to_parallel" sourceRef="Task_handover" targetRef="Gateway_parallel_start" />
    <bpmn:sequenceFlow id="Flow_parallel_to_finance" sourceRef="Gateway_parallel_start" targetRef="Task_finance_settle" />
    <bpmn:sequenceFlow id="Flow_parallel_to_it" sourceRef="Gateway_parallel_start" targetRef="Task_it_asset" />
    <bpmn:sequenceFlow id="Flow_parallel_to_admin" sourceRef="Gateway_parallel_start" targetRef="Task_admin_asset" />
    <bpmn:sequenceFlow id="Flow_finance_to_parallel_end" sourceRef="Task_finance_settle" targetRef="Gateway_parallel_end" />
    <bpmn:sequenceFlow id="Flow_it_to_parallel_end" sourceRef="Task_it_asset" targetRef="Gateway_parallel_end" />
    <bpmn:sequenceFlow id="Flow_admin_to_parallel_end" sourceRef="Task_admin_asset" targetRef="Gateway_parallel_end" />
    <bpmn:sequenceFlow id="Flow_parallel_end_to_hr" sourceRef="Gateway_parallel_end" targetRef="Task_hr_approval" />
    
    <!-- 人事审批流程 -->
    <bpmn:sequenceFlow id="Flow_hr_to_gateway" sourceRef="Task_hr_approval" targetRef="Gateway_hr_result" />
    <bpmn:sequenceFlow id="Flow_hr_approved" name="同意" sourceRef="Gateway_hr_result" targetRef="Task_process_resignation">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_hr_rejected" name="驳回" sourceRef="Gateway_hr_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 后续处理流程 -->
    <bpmn:sequenceFlow id="Flow_process_to_disable" sourceRef="Task_process_resignation" targetRef="Task_disable_account" />
    <bpmn:sequenceFlow id="Flow_disable_to_archive" sourceRef="Task_disable_account" targetRef="Task_archive_info" />
    <bpmn:sequenceFlow id="Flow_archive_to_notify" sourceRef="Task_archive_info" targetRef="Task_notify" />
    <bpmn:sequenceFlow id="Flow_notify_to_end" sourceRef="Task_notify" targetRef="EndEvent_success" />
    
  </bpmn:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="employee_resignation">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_1" bpmnElement="StartEvent_1">
        <dc:Bounds x="170" y="350" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="177" y="393" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_apply_di" bpmnElement="Task_apply">
        <dc:Bounds x="270" y="328" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_dept_approval_di" bpmnElement="Task_dept_approval">
        <dc:Bounds x="430" y="328" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_dept_result_di" bpmnElement="Gateway_dept_result" isMarkerVisible="true">
        <dc:Bounds x="595" y="343" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="582" y="313" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_handover_di" bpmnElement="Task_handover">
        <dc:Bounds x="710" y="328" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_parallel_start_di" bpmnElement="Gateway_parallel_start">
        <dc:Bounds x="875" y="343" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="865" y="313" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_finance_settle_di" bpmnElement="Task_finance_settle">
        <dc:Bounds x="990" y="200" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_it_asset_di" bpmnElement="Task_it_asset">
        <dc:Bounds x="990" y="328" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_admin_asset_di" bpmnElement="Task_admin_asset">
        <dc:Bounds x="990" y="456" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_parallel_end_di" bpmnElement="Gateway_parallel_end">
        <dc:Bounds x="1155" y="343" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1145" y="400" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_hr_approval_di" bpmnElement="Task_hr_approval">
        <dc:Bounds x="1270" y="328" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_hr_result_di" bpmnElement="Gateway_hr_result" isMarkerVisible="true">
        <dc:Bounds x="1435" y="343" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1425" y="313" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_process_resignation_di" bpmnElement="Task_process_resignation">
        <dc:Bounds x="1550" y="328" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_disable_account_di" bpmnElement="Task_disable_account">
        <dc:Bounds x="1710" y="328" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_archive_info_di" bpmnElement="Task_archive_info">
        <dc:Bounds x="1870" y="328" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_notify_di" bpmnElement="Task_notify">
        <dc:Bounds x="2030" y="328" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_success_di" bpmnElement="EndEvent_success">
        <dc:Bounds x="2192" y="350" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2186" y="393" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_rejected_di" bpmnElement="EndEvent_rejected">
        <dc:Bounds x="1012" y="592" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1006" y="635" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>