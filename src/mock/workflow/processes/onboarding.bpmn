<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                  xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  xmlns:hr="http://hr.hky.edu.cn/schema/bpmn"
                  id="Definitions_onboarding" 
                  targetNamespace="http://hr.hky.edu.cn/bpmn">
  
  <bpmn:process id="employee_onboarding" name="员工入职流程" isExecutable="true" hr:category="hr">
    
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_start_to_apply</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:userTask id="Task_apply" name="填写入职申请" hr:assigneeType="initiator" hr:formKey="onboarding_form">
      <bpmn:incoming>Flow_start_to_apply</bpmn:incoming>
      <bpmn:outgoing>Flow_apply_to_dept</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:userTask id="Task_dept_approval" name="部门负责人审批" hr:assigneeType="department" hr:formKey="approval_form">
      <bpmn:incoming>Flow_apply_to_dept</bpmn:incoming>
      <bpmn:outgoing>Flow_dept_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_dept_result" name="部门审批结果">
      <bpmn:incoming>Flow_dept_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_dept_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_dept_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <bpmn:userTask id="Task_hr_approval" name="人事审批" hr:assigneeType="role" hr:candidateGroups="hr_manager" hr:formKey="hr_approval_form">
      <bpmn:incoming>Flow_dept_approved</bpmn:incoming>
      <bpmn:outgoing>Flow_hr_to_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_hr_result" name="人事审批结果">
      <bpmn:incoming>Flow_hr_to_gateway</bpmn:incoming>
      <bpmn:outgoing>Flow_hr_approved</bpmn:outgoing>
      <bpmn:outgoing>Flow_hr_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <bpmn:serviceTask id="Task_generate_empno" name="生成工号">
      <bpmn:incoming>Flow_hr_approved</bpmn:incoming>
      <bpmn:outgoing>Flow_empno_to_account</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:serviceTask id="Task_create_account" name="创建系统账号">
      <bpmn:incoming>Flow_empno_to_account</bpmn:incoming>
      <bpmn:outgoing>Flow_account_to_notify</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:serviceTask id="Task_notify" name="发送入职通知">
      <bpmn:incoming>Flow_account_to_notify</bpmn:incoming>
      <bpmn:outgoing>Flow_notify_to_end</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:endEvent id="EndEvent_success" name="入职成功">
      <bpmn:incoming>Flow_notify_to_end</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:endEvent id="EndEvent_rejected" name="申请驳回">
      <bpmn:incoming>Flow_dept_rejected</bpmn:incoming>
      <bpmn:incoming>Flow_hr_rejected</bpmn:incoming>
    </bpmn:endEvent>
    
    <!-- 流程连线 -->
    <bpmn:sequenceFlow id="Flow_start_to_apply" sourceRef="StartEvent_1" targetRef="Task_apply" />
    <bpmn:sequenceFlow id="Flow_apply_to_dept" sourceRef="Task_apply" targetRef="Task_dept_approval" />
    <bpmn:sequenceFlow id="Flow_dept_to_gateway" sourceRef="Task_dept_approval" targetRef="Gateway_dept_result" />
    <bpmn:sequenceFlow id="Flow_dept_approved" name="同意" sourceRef="Gateway_dept_result" targetRef="Task_hr_approval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_dept_rejected" name="驳回" sourceRef="Gateway_dept_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_hr_to_gateway" sourceRef="Task_hr_approval" targetRef="Gateway_hr_result" />
    <bpmn:sequenceFlow id="Flow_hr_approved" name="同意" sourceRef="Gateway_hr_result" targetRef="Task_generate_empno">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_hr_rejected" name="驳回" sourceRef="Gateway_hr_result" targetRef="EndEvent_rejected">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_empno_to_account" sourceRef="Task_generate_empno" targetRef="Task_create_account" />
    <bpmn:sequenceFlow id="Flow_account_to_notify" sourceRef="Task_create_account" targetRef="Task_notify" />
    <bpmn:sequenceFlow id="Flow_notify_to_end" sourceRef="Task_notify" targetRef="EndEvent_success" />
    
  </bpmn:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="employee_onboarding">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_1" bpmnElement="StartEvent_1">
        <dc:Bounds x="170" y="200" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="177" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_apply_di" bpmnElement="Task_apply">
        <dc:Bounds x="270" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_dept_approval_di" bpmnElement="Task_dept_approval">
        <dc:Bounds x="430" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_dept_result_di" bpmnElement="Gateway_dept_result" isMarkerVisible="true">
        <dc:Bounds x="595" y="193" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="582" y="163" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_hr_approval_di" bpmnElement="Task_hr_approval">
        <dc:Bounds x="710" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_hr_result_di" bpmnElement="Gateway_hr_result" isMarkerVisible="true">
        <dc:Bounds x="875" y="193" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="865" y="163" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_generate_empno_di" bpmnElement="Task_generate_empno">
        <dc:Bounds x="990" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_create_account_di" bpmnElement="Task_create_account">
        <dc:Bounds x="1150" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_notify_di" bpmnElement="Task_notify">
        <dc:Bounds x="1310" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_success_di" bpmnElement="EndEvent_success">
        <dc:Bounds x="1472" y="200" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1466" y="243" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_rejected_di" bpmnElement="EndEvent_rejected">
        <dc:Bounds x="752" y="332" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="746" y="375" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <!-- 连线 -->
      <bpmndi:BPMNEdge id="Flow_start_to_apply_di" bpmnElement="Flow_start_to_apply">
        <di:waypoint x="206" y="218" />
        <di:waypoint x="270" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_apply_to_dept_di" bpmnElement="Flow_apply_to_dept">
        <di:waypoint x="370" y="218" />
        <di:waypoint x="430" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_dept_to_gateway_di" bpmnElement="Flow_dept_to_gateway">
        <di:waypoint x="530" y="218" />
        <di:waypoint x="595" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_dept_approved_di" bpmnElement="Flow_dept_approved">
        <di:waypoint x="645" y="218" />
        <di:waypoint x="710" y="218" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="667" y="200" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_dept_rejected_di" bpmnElement="Flow_dept_rejected">
        <di:waypoint x="620" y="243" />
        <di:waypoint x="620" y="350" />
        <di:waypoint x="752" y="350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="624" y="293" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_hr_to_gateway_di" bpmnElement="Flow_hr_to_gateway">
        <di:waypoint x="810" y="218" />
        <di:waypoint x="875" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_hr_approved_di" bpmnElement="Flow_hr_approved">
        <di:waypoint x="925" y="218" />
        <di:waypoint x="990" y="218" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="947" y="200" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_hr_rejected_di" bpmnElement="Flow_hr_rejected">
        <di:waypoint x="900" y="243" />
        <di:waypoint x="900" y="350" />
        <di:waypoint x="788" y="350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="904" y="293" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_empno_to_account_di" bpmnElement="Flow_empno_to_account">
        <di:waypoint x="1090" y="218" />
        <di:waypoint x="1150" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_account_to_notify_di" bpmnElement="Flow_account_to_notify">
        <di:waypoint x="1250" y="218" />
        <di:waypoint x="1310" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_notify_to_end_di" bpmnElement="Flow_notify_to_end">
        <di:waypoint x="1410" y="218" />
        <di:waypoint x="1472" y="218" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>