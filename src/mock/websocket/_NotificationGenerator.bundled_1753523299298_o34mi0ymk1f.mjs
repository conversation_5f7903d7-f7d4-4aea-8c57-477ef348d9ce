// src/mock/websocket/NotificationGenerator.ts
import { faker as faker2 } from "@faker-js/faker/locale/zh_CN";

// src/mock/utils/index.ts
import { faker } from "@faker-js/faker/locale/zh_CN";

// src/mock/utils/errorHandler.ts
var MockError = class extends Error {
  /** 错误类型 */
  type;
  /** HTTP状态码 */
  statusCode;
  /** 错误详情 */
  details;
  /** 错误发生时间 */
  timestamp;
  constructor(message, type = "SYSTEM_ERROR" /* SYSTEM_ERROR */, statusCode = 500, details) {
    super(message);
    this.name = "MockError";
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = (/* @__PURE__ */ new Date()).toISOString();
  }
};
var ErrorCollector = class {
  errors = [];
  /**
   * 收集错误
   */
  collect(error, context) {
    this.errors.push({
      error,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      context
    });
  }
  /**
   * 获取所有错误
   */
  getErrors() {
    return [...this.errors];
  }
  /**
   * 清空错误
   */
  clear() {
    this.errors = [];
  }
  /**
   * 获取错误统计
   */
  getStatistics() {
    const stats = {};
    this.errors.forEach(({ error }) => {
      const type = error instanceof MockError ? error.type : "UNKNOWN";
      stats[type] = (stats[type] || 0) + 1;
    });
    return stats;
  }
};
var globalErrorCollector = new ErrorCollector();

// src/mock/utils/index.ts
function generateId() {
  return faker.string.uuid();
}
var isBrowser = typeof window !== "undefined" && typeof window.localStorage !== "undefined";

// src/mock/websocket/NotificationGenerator.ts
var NotificationType = /* @__PURE__ */ ((NotificationType2) => {
  NotificationType2["SYSTEM"] = "system";
  NotificationType2["APPROVAL"] = "approval";
  NotificationType2["CONTRACT"] = "contract";
  NotificationType2["ATTENDANCE"] = "attendance";
  NotificationType2["SALARY"] = "salary";
  NotificationType2["PERFORMANCE"] = "performance";
  NotificationType2["RECRUITMENT"] = "recruitment";
  NotificationType2["WORKFLOW"] = "workflow";
  return NotificationType2;
})(NotificationType || {});
var NotificationLevel = /* @__PURE__ */ ((NotificationLevel2) => {
  NotificationLevel2["INFO"] = "info";
  NotificationLevel2["SUCCESS"] = "success";
  NotificationLevel2["WARNING"] = "warning";
  NotificationLevel2["ERROR"] = "error";
  return NotificationLevel2;
})(NotificationLevel || {});
var NotificationGenerator = class {
  // 通知模板库
  static templates = [
    // 审批通知
    {
      type: "approval" /* APPROVAL */,
      level: "info" /* INFO */,
      title: "\u5F85\u5BA1\u6279\u63D0\u9192",
      contentTemplate: "\u60A8\u6709\u4E00\u4E2A{processName}\u5F85\u5BA1\u6279\uFF0C\u7533\u8BF7\u4EBA\uFF1A{applicant}",
      category: "\u5BA1\u6279\u4E8B\u9879",
      linkTemplate: "/workflow/task/{taskId}",
      actions: [
        { label: "\u7ACB\u5373\u5904\u7406", action: "approve" },
        { label: "\u7A0D\u540E\u5904\u7406", action: "later" }
      ]
    },
    {
      type: "approval" /* APPROVAL */,
      level: "success" /* SUCCESS */,
      title: "\u5BA1\u6279\u901A\u8FC7",
      contentTemplate: "\u60A8\u7684{processName}\u5DF2\u901A\u8FC7\u5BA1\u6279",
      category: "\u5BA1\u6279\u4E8B\u9879",
      linkTemplate: "/workflow/instance/{instanceId}"
    },
    {
      type: "approval" /* APPROVAL */,
      level: "error" /* ERROR */,
      title: "\u5BA1\u6279\u9A73\u56DE",
      contentTemplate: "\u60A8\u7684{processName}\u88AB\u9A73\u56DE\uFF0C\u539F\u56E0\uFF1A{reason}",
      category: "\u5BA1\u6279\u4E8B\u9879",
      linkTemplate: "/workflow/instance/{instanceId}",
      actions: [
        { label: "\u67E5\u770B\u8BE6\u60C5", action: "view" },
        { label: "\u91CD\u65B0\u7533\u8BF7", action: "reapply" }
      ]
    },
    // 合同通知
    {
      type: "contract" /* CONTRACT */,
      level: "warning" /* WARNING */,
      title: "\u5408\u540C\u5230\u671F\u63D0\u9192",
      contentTemplate: "\u60A8\u7684{contractType}\u5C06\u4E8E{days}\u5929\u540E\u5230\u671F\uFF0C\u8BF7\u53CA\u65F6\u529E\u7406\u7EED\u7B7E\u624B\u7EED",
      category: "\u5408\u540C\u7BA1\u7406",
      linkTemplate: "/contract/detail/{contractId}",
      actions: [
        { label: "\u7533\u8BF7\u7EED\u7B7E", action: "renew" },
        { label: "\u67E5\u770B\u5408\u540C", action: "view" }
      ]
    },
    {
      type: "contract" /* CONTRACT */,
      level: "info" /* INFO */,
      title: "\u5408\u540C\u7B7E\u7F72\u63D0\u9192",
      contentTemplate: "\u60A8\u6709\u4E00\u4EFD{contractType}\u5F85\u7B7E\u7F72",
      category: "\u5408\u540C\u7BA1\u7406",
      linkTemplate: "/contract/sign/{contractId}",
      actions: [{ label: "\u7ACB\u5373\u7B7E\u7F72", action: "sign" }]
    },
    // 考勤通知
    {
      type: "attendance" /* ATTENDANCE */,
      level: "warning" /* WARNING */,
      title: "\u6253\u5361\u5F02\u5E38\u63D0\u9192",
      contentTemplate: "{date}\u60A8\u7684\u8003\u52E4\u5F02\u5E38\uFF1A{abnormalType}",
      category: "\u8003\u52E4\u7BA1\u7406",
      linkTemplate: "/attendance/my-attendance",
      actions: [
        { label: "\u7533\u8BF7\u8865\u5361", action: "supplement" },
        { label: "\u67E5\u770B\u8BE6\u60C5", action: "view" }
      ]
    },
    {
      type: "attendance" /* ATTENDANCE */,
      level: "success" /* SUCCESS */,
      title: "\u8BF7\u5047\u5BA1\u6279\u901A\u8FC7",
      contentTemplate: "\u60A8\u7684{leaveType}\u7533\u8BF7\u5DF2\u901A\u8FC7\uFF0C\u8BF7\u5047\u65F6\u95F4\uFF1A{startDate}\u81F3{endDate}",
      category: "\u8003\u52E4\u7BA1\u7406",
      linkTemplate: "/attendance/leave/detail/{leaveId}"
    },
    // 薪资通知
    {
      type: "salary" /* SALARY */,
      level: "info" /* INFO */,
      title: "\u5DE5\u8D44\u53D1\u653E\u901A\u77E5",
      contentTemplate: "\u60A8\u7684{month}\u6708\u5DE5\u8D44\u5DF2\u53D1\u653E\uFF0C\u5B9E\u53D1\u91D1\u989D\uFF1A\xA5{amount}",
      category: "\u85AA\u8D44\u798F\u5229",
      linkTemplate: "/salary/payslip/{payslipId}",
      actions: [{ label: "\u67E5\u770B\u5DE5\u8D44\u6761", action: "view" }]
    },
    // 绩效通知
    {
      type: "performance" /* PERFORMANCE */,
      level: "info" /* INFO */,
      title: "\u7EE9\u6548\u8003\u6838\u901A\u77E5",
      contentTemplate: "{year}\u5E74\u5EA6\u7EE9\u6548\u8003\u6838\u5DF2\u5F00\u59CB\uFF0C\u8BF7\u53CA\u65F6\u5B8C\u6210\u81EA\u8BC4",
      category: "\u7EE9\u6548\u8003\u6838",
      linkTemplate: "/performance/assessment/{assessmentId}",
      actions: [{ label: "\u5F00\u59CB\u81EA\u8BC4", action: "start" }]
    },
    {
      type: "performance" /* PERFORMANCE */,
      level: "success" /* SUCCESS */,
      title: "\u7EE9\u6548\u7ED3\u679C\u901A\u77E5",
      contentTemplate: "\u60A8\u7684{period}\u7EE9\u6548\u8003\u6838\u7ED3\u679C\u5DF2\u51FA\uFF1A{grade}",
      category: "\u7EE9\u6548\u8003\u6838",
      linkTemplate: "/performance/result/{resultId}"
    },
    // 招聘通知
    {
      type: "recruitment" /* RECRUITMENT */,
      level: "info" /* INFO */,
      title: "\u9762\u8BD5\u5B89\u6392\u901A\u77E5",
      contentTemplate: "\u60A8\u6709\u4E00\u573A\u9762\u8BD5\u5B89\u6392\u5728{date} {time}\uFF0C\u5019\u9009\u4EBA\uFF1A{candidate}",
      category: "\u62DB\u8058\u7BA1\u7406",
      linkTemplate: "/recruitment/interview/{interviewId}",
      actions: [
        { label: "\u67E5\u770B\u8BE6\u60C5", action: "view" },
        { label: "\u8C03\u6574\u65F6\u95F4", action: "reschedule" }
      ]
    },
    // 系统通知
    {
      type: "system" /* SYSTEM */,
      level: "info" /* INFO */,
      title: "\u7CFB\u7EDF\u7EF4\u62A4\u901A\u77E5",
      contentTemplate: "\u7CFB\u7EDF\u5C06\u4E8E{date} {time}\u8FDB\u884C\u7EF4\u62A4\uFF0C\u9884\u8BA1\u6301\u7EED{duration}\u5C0F\u65F6",
      category: "\u7CFB\u7EDF\u6D88\u606F"
    },
    {
      type: "system" /* SYSTEM */,
      level: "warning" /* WARNING */,
      title: "\u5BC6\u7801\u5230\u671F\u63D0\u9192",
      contentTemplate: "\u60A8\u7684\u5BC6\u7801\u5C06\u4E8E{days}\u5929\u540E\u8FC7\u671F\uFF0C\u8BF7\u53CA\u65F6\u4FEE\u6539",
      category: "\u7CFB\u7EDF\u6D88\u606F",
      linkTemplate: "/profile/security",
      actions: [{ label: "\u7ACB\u5373\u4FEE\u6539", action: "change" }]
    }
  ];
  /**
   * 生成随机通知
   */
  static generateRandomNotification(userId) {
    const template = this.templates[Math.floor(Math.random() * this.templates.length)];
    return this.generateNotificationFromTemplate(template, userId);
  }
  /**
   * 根据模板生成通知
   */
  static generateNotificationFromTemplate(template, userId) {
    const variables = this.generateTemplateVariables(template.type);
    let content = template.contentTemplate;
    let link = template.linkTemplate;
    Object.entries(variables).forEach(([key, value]) => {
      content = content.replace(`{${key}}`, value);
      if (link) {
        link = link.replace(`{${key}}`, value);
      }
    });
    return {
      id: generateId(),
      userId: userId || `U${faker2.number.int({ min: 10001, max: 10100 }).toString().padStart(5, "0")}`,
      type: template.level,
      title: template.title,
      content,
      category: template.category,
      link,
      actions: template.actions,
      isRead: false,
      createdAt: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
  /**
   * 生成模板变量
   */
  static generateTemplateVariables(type) {
    const commonVars = {
      date: faker2.date.recent().toLocaleDateString("zh-CN"),
      time: faker2.date.recent().toLocaleTimeString("zh-CN", { hour: "2-digit", minute: "2-digit" }),
      days: faker2.number.int({ min: 1, max: 30 }),
      month: (/* @__PURE__ */ new Date()).getMonth() + 1,
      year: (/* @__PURE__ */ new Date()).getFullYear(),
      amount: faker2.number.int({ min: 5e3, max: 3e4 }).toFixed(2)
    };
    const typeSpecificVars = {
      ["approval" /* APPROVAL */]: {
        processName: faker2.helpers.arrayElement(["\u8BF7\u5047\u7533\u8BF7", "\u62A5\u9500\u7533\u8BF7", "\u51FA\u5DEE\u7533\u8BF7", "\u91C7\u8D2D\u7533\u8BF7"]),
        applicant: faker2.person.fullName(),
        reason: faker2.helpers.arrayElement(["\u6750\u6599\u4E0D\u9F50\u5168", "\u91D1\u989D\u8D85\u9650", "\u4E0D\u7B26\u5408\u89C4\u5B9A"]),
        taskId: generateId(),
        instanceId: generateId()
      },
      ["contract" /* CONTRACT */]: {
        contractType: faker2.helpers.arrayElement(["\u52B3\u52A8\u5408\u540C", "\u8058\u7528\u5408\u540C", "\u52B3\u52A1\u534F\u8BAE"]),
        contractId: generateId()
      },
      ["attendance" /* ATTENDANCE */]: {
        abnormalType: faker2.helpers.arrayElement(["\u7F3A\u5361", "\u8FDF\u5230", "\u65E9\u9000", "\u65F7\u5DE5"]),
        leaveType: faker2.helpers.arrayElement(["\u4E8B\u5047", "\u75C5\u5047", "\u5E74\u5047", "\u5A5A\u5047"]),
        startDate: faker2.date.soon().toLocaleDateString("zh-CN"),
        endDate: faker2.date.soon({ days: 5 }).toLocaleDateString("zh-CN"),
        leaveId: generateId()
      },
      ["salary" /* SALARY */]: {
        payslipId: generateId()
      },
      ["performance" /* PERFORMANCE */]: {
        period: faker2.helpers.arrayElement(["Q1", "Q2", "Q3", "Q4", "\u5E74\u5EA6"]),
        grade: faker2.helpers.arrayElement(["\u4F18\u79C0", "\u826F\u597D", "\u5408\u683C", "\u5F85\u6539\u8FDB"]),
        assessmentId: generateId(),
        resultId: generateId()
      },
      ["recruitment" /* RECRUITMENT */]: {
        candidate: faker2.person.fullName(),
        interviewId: generateId()
      },
      ["system" /* SYSTEM */]: {
        duration: faker2.number.int({ min: 1, max: 4 })
      },
      ["workflow" /* WORKFLOW */]: {}
    };
    return { ...commonVars, ...typeSpecificVars[type] };
  }
  /**
   * 生成批量通知（模拟不同场景）
   */
  static generateScenarioNotifications() {
    const scenarios = [
      // 合同到期批量提醒
      {
        name: "\u6708\u5EA6\u5408\u540C\u5230\u671F\u63D0\u9192",
        notifications: Array.from({ length: 5 }, () => {
          const template = this.templates.find(
            (t) => t.type === "contract" /* CONTRACT */ && t.title === "\u5408\u540C\u5230\u671F\u63D0\u9192"
          );
          return this.generateNotificationFromTemplate(template);
        })
      },
      // 考勤异常批量通知
      {
        name: "\u65E5\u5E38\u8003\u52E4\u5F02\u5E38",
        notifications: Array.from({ length: 8 }, () => {
          const template = this.templates.find(
            (t) => t.type === "attendance" /* ATTENDANCE */ && t.title === "\u6253\u5361\u5F02\u5E38\u63D0\u9192"
          );
          return this.generateNotificationFromTemplate(template);
        })
      },
      // 审批待办批量推送
      {
        name: "\u5F85\u529E\u4E8B\u9879\u63A8\u9001",
        notifications: Array.from({ length: 10 }, () => {
          const template = this.templates.find(
            (t) => t.type === "approval" /* APPROVAL */ && t.title === "\u5F85\u5BA1\u6279\u63D0\u9192"
          );
          return this.generateNotificationFromTemplate(template);
        })
      },
      // 工资发放通知
      {
        name: "\u6708\u5EA6\u5DE5\u8D44\u53D1\u653E",
        notifications: Array.from({ length: 50 }, () => {
          const template = this.templates.find(
            (t) => t.type === "salary" /* SALARY */ && t.title === "\u5DE5\u8D44\u53D1\u653E\u901A\u77E5"
          );
          return this.generateNotificationFromTemplate(template);
        })
      }
    ];
    return scenarios;
  }
  /**
   * 生成未读通知统计
   */
  static generateNotificationStats(userId) {
    return {
      total: faker2.number.int({ min: 0, max: 50 }),
      unread: faker2.number.int({ min: 0, max: 20 }),
      byCategory: {
        \u5BA1\u6279\u4E8B\u9879: faker2.number.int({ min: 0, max: 10 }),
        \u5408\u540C\u7BA1\u7406: faker2.number.int({ min: 0, max: 5 }),
        \u8003\u52E4\u7BA1\u7406: faker2.number.int({ min: 0, max: 8 }),
        \u85AA\u8D44\u798F\u5229: faker2.number.int({ min: 0, max: 3 }),
        \u7EE9\u6548\u8003\u6838: faker2.number.int({ min: 0, max: 2 }),
        \u7CFB\u7EDF\u6D88\u606F: faker2.number.int({ min: 0, max: 5 })
      },
      recent: Array.from({ length: 5 }, () => this.generateRandomNotification(userId))
    };
  }
};
export {
  NotificationGenerator,
  NotificationLevel,
  NotificationType
};
//# sourceMappingURL=data:application/json;base64,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
