/**
 * @name 通知消息生成器
 * @description 生成各类系统通知消息
 * <AUTHOR>
 * @since 2025-01-24
 */

import { faker } from '@faker-js/faker/locale/zh_CN'
import { generateId } from '../utils'

// 通知类型
export enum NotificationType {
  SYSTEM = 'system',
  APPROVAL = 'approval',
  CONTRACT = 'contract',
  ATTENDANCE = 'attendance',
  SALARY = 'salary',
  PERFORMANCE = 'performance',
  RECRUITMENT = 'recruitment',
  WORKFLOW = 'workflow'
}

// 通知级别
export enum NotificationLevel {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error'
}

// 通知模板
interface NotificationTemplate {
  type: NotificationType
  level: NotificationLevel
  title: string
  contentTemplate: string
  category: string
  linkTemplate?: string
  actions?: Array<{ label: string; action: string }>
}

export class NotificationGenerator {
  // 通知模板库
  private static templates: NotificationTemplate[] = [
    // 审批通知
    {
      type: NotificationType.APPROVAL,
      level: NotificationLevel.INFO,
      title: '待审批提醒',
      contentTemplate: '您有一个{processName}待审批，申请人：{applicant}',
      category: '审批事项',
      linkTemplate: '/workflow/task/{taskId}',
      actions: [
        { label: '立即处理', action: 'approve' },
        { label: '稍后处理', action: 'later' }
      ]
    },
    {
      type: NotificationType.APPROVAL,
      level: NotificationLevel.SUCCESS,
      title: '审批通过',
      contentTemplate: '您的{processName}已通过审批',
      category: '审批事项',
      linkTemplate: '/workflow/instance/{instanceId}'
    },
    {
      type: NotificationType.APPROVAL,
      level: NotificationLevel.ERROR,
      title: '审批驳回',
      contentTemplate: '您的{processName}被驳回，原因：{reason}',
      category: '审批事项',
      linkTemplate: '/workflow/instance/{instanceId}',
      actions: [
        { label: '查看详情', action: 'view' },
        { label: '重新申请', action: 'reapply' }
      ]
    },

    // 合同通知
    {
      type: NotificationType.CONTRACT,
      level: NotificationLevel.WARNING,
      title: '合同到期提醒',
      contentTemplate: '您的{contractType}将于{days}天后到期，请及时办理续签手续',
      category: '合同管理',
      linkTemplate: '/contract/detail/{contractId}',
      actions: [
        { label: '申请续签', action: 'renew' },
        { label: '查看合同', action: 'view' }
      ]
    },
    {
      type: NotificationType.CONTRACT,
      level: NotificationLevel.INFO,
      title: '合同签署提醒',
      contentTemplate: '您有一份{contractType}待签署',
      category: '合同管理',
      linkTemplate: '/contract/sign/{contractId}',
      actions: [{ label: '立即签署', action: 'sign' }]
    },

    // 考勤通知
    {
      type: NotificationType.ATTENDANCE,
      level: NotificationLevel.WARNING,
      title: '打卡异常提醒',
      contentTemplate: '{date}您的考勤异常：{abnormalType}',
      category: '考勤管理',
      linkTemplate: '/attendance/my-attendance',
      actions: [
        { label: '申请补卡', action: 'supplement' },
        { label: '查看详情', action: 'view' }
      ]
    },
    {
      type: NotificationType.ATTENDANCE,
      level: NotificationLevel.SUCCESS,
      title: '请假审批通过',
      contentTemplate: '您的{leaveType}申请已通过，请假时间：{startDate}至{endDate}',
      category: '考勤管理',
      linkTemplate: '/attendance/leave/detail/{leaveId}'
    },

    // 薪资通知
    {
      type: NotificationType.SALARY,
      level: NotificationLevel.INFO,
      title: '工资发放通知',
      contentTemplate: '您的{month}月工资已发放，实发金额：¥{amount}',
      category: '薪资福利',
      linkTemplate: '/salary/payslip/{payslipId}',
      actions: [{ label: '查看工资条', action: 'view' }]
    },

    // 绩效通知
    {
      type: NotificationType.PERFORMANCE,
      level: NotificationLevel.INFO,
      title: '绩效考核通知',
      contentTemplate: '{year}年度绩效考核已开始，请及时完成自评',
      category: '绩效考核',
      linkTemplate: '/performance/assessment/{assessmentId}',
      actions: [{ label: '开始自评', action: 'start' }]
    },
    {
      type: NotificationType.PERFORMANCE,
      level: NotificationLevel.SUCCESS,
      title: '绩效结果通知',
      contentTemplate: '您的{period}绩效考核结果已出：{grade}',
      category: '绩效考核',
      linkTemplate: '/performance/result/{resultId}'
    },

    // 招聘通知
    {
      type: NotificationType.RECRUITMENT,
      level: NotificationLevel.INFO,
      title: '面试安排通知',
      contentTemplate: '您有一场面试安排在{date} {time}，候选人：{candidate}',
      category: '招聘管理',
      linkTemplate: '/recruitment/interview/{interviewId}',
      actions: [
        { label: '查看详情', action: 'view' },
        { label: '调整时间', action: 'reschedule' }
      ]
    },

    // 系统通知
    {
      type: NotificationType.SYSTEM,
      level: NotificationLevel.INFO,
      title: '系统维护通知',
      contentTemplate: '系统将于{date} {time}进行维护，预计持续{duration}小时',
      category: '系统消息'
    },
    {
      type: NotificationType.SYSTEM,
      level: NotificationLevel.WARNING,
      title: '密码到期提醒',
      contentTemplate: '您的密码将于{days}天后过期，请及时修改',
      category: '系统消息',
      linkTemplate: '/profile/security',
      actions: [{ label: '立即修改', action: 'change' }]
    }
  ]

  /**
   * 生成随机通知
   */
  static generateRandomNotification(userId?: string) {
    const template = this.templates[Math.floor(Math.random() * this.templates.length)]
    return this.generateNotificationFromTemplate(template, userId)
  }

  /**
   * 根据模板生成通知
   */
  static generateNotificationFromTemplate(template: NotificationTemplate, userId?: string) {
    const variables = this.generateTemplateVariables(template.type)

    // 替换模板变量
    let content = template.contentTemplate
    let link = template.linkTemplate

    Object.entries(variables).forEach(([key, value]) => {
      content = content.replace(`{${key}}`, value as string)
      if (link) {
        link = link.replace(`{${key}}`, value as string)
      }
    })

    return {
      id: generateId(),
      userId:
        userId || `U${faker.number.int({ min: 10001, max: 10100 }).toString().padStart(5, '0')}`,
      type: template.level,
      title: template.title,
      content,
      category: template.category,
      link,
      actions: template.actions,
      isRead: false,
      createdAt: new Date().toISOString()
    }
  }

  /**
   * 生成模板变量
   */
  private static generateTemplateVariables(type: NotificationType): Record<string, any> {
    const commonVars = {
      date: faker.date.recent().toLocaleDateString('zh-CN'),
      time: faker.date.recent().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      days: faker.number.int({ min: 1, max: 30 }),
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      amount: faker.number.int({ min: 5000, max: 30000 }).toFixed(2)
    }

    const typeSpecificVars: Record<NotificationType, any> = {
      [NotificationType.APPROVAL]: {
        processName: faker.helpers.arrayElement(['请假申请', '报销申请', '出差申请', '采购申请']),
        applicant: faker.person.fullName(),
        reason: faker.helpers.arrayElement(['材料不齐全', '金额超限', '不符合规定']),
        taskId: generateId(),
        instanceId: generateId()
      },
      [NotificationType.CONTRACT]: {
        contractType: faker.helpers.arrayElement(['劳动合同', '聘用合同', '劳务协议']),
        contractId: generateId()
      },
      [NotificationType.ATTENDANCE]: {
        abnormalType: faker.helpers.arrayElement(['缺卡', '迟到', '早退', '旷工']),
        leaveType: faker.helpers.arrayElement(['事假', '病假', '年假', '婚假']),
        startDate: faker.date.soon().toLocaleDateString('zh-CN'),
        endDate: faker.date.soon({ days: 5 }).toLocaleDateString('zh-CN'),
        leaveId: generateId()
      },
      [NotificationType.SALARY]: {
        payslipId: generateId()
      },
      [NotificationType.PERFORMANCE]: {
        period: faker.helpers.arrayElement(['Q1', 'Q2', 'Q3', 'Q4', '年度']),
        grade: faker.helpers.arrayElement(['优秀', '良好', '合格', '待改进']),
        assessmentId: generateId(),
        resultId: generateId()
      },
      [NotificationType.RECRUITMENT]: {
        candidate: faker.person.fullName(),
        interviewId: generateId()
      },
      [NotificationType.SYSTEM]: {
        duration: faker.number.int({ min: 1, max: 4 })
      },
      [NotificationType.WORKFLOW]: {}
    }

    return { ...commonVars, ...typeSpecificVars[type] }
  }

  /**
   * 生成批量通知（模拟不同场景）
   */
  static generateScenarioNotifications() {
    const scenarios = [
      // 合同到期批量提醒
      {
        name: '月度合同到期提醒',
        notifications: Array.from({ length: 5 }, () => {
          const template = this.templates.find(
            t => t.type === NotificationType.CONTRACT && t.title === '合同到期提醒'
          )!
          return this.generateNotificationFromTemplate(template)
        })
      },

      // 考勤异常批量通知
      {
        name: '日常考勤异常',
        notifications: Array.from({ length: 8 }, () => {
          const template = this.templates.find(
            t => t.type === NotificationType.ATTENDANCE && t.title === '打卡异常提醒'
          )!
          return this.generateNotificationFromTemplate(template)
        })
      },

      // 审批待办批量推送
      {
        name: '待办事项推送',
        notifications: Array.from({ length: 10 }, () => {
          const template = this.templates.find(
            t => t.type === NotificationType.APPROVAL && t.title === '待审批提醒'
          )!
          return this.generateNotificationFromTemplate(template)
        })
      },

      // 工资发放通知
      {
        name: '月度工资发放',
        notifications: Array.from({ length: 50 }, () => {
          const template = this.templates.find(
            t => t.type === NotificationType.SALARY && t.title === '工资发放通知'
          )!
          return this.generateNotificationFromTemplate(template)
        })
      }
    ]

    return scenarios
  }

  /**
   * 生成未读通知统计
   */
  static generateNotificationStats(userId: string) {
    return {
      total: faker.number.int({ min: 0, max: 50 }),
      unread: faker.number.int({ min: 0, max: 20 }),
      byCategory: {
        审批事项: faker.number.int({ min: 0, max: 10 }),
        合同管理: faker.number.int({ min: 0, max: 5 }),
        考勤管理: faker.number.int({ min: 0, max: 8 }),
        薪资福利: faker.number.int({ min: 0, max: 3 }),
        绩效考核: faker.number.int({ min: 0, max: 2 }),
        系统消息: faker.number.int({ min: 0, max: 5 })
      },
      recent: Array.from({ length: 5 }, () => this.generateRandomNotification(userId))
    }
  }
}
