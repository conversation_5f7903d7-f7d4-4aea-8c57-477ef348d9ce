/**
 * @name Mock WebSocket服务器
 * @description 模拟WebSocket连接和实时消息推送
 * <AUTHOR>
 * @since 2025-01-24
 */

import { generateId } from '../utils'

// 消息类型枚举
export enum MessageType {
  CONNECTION = 'connection',
  NOTIFICATION = 'notification',
  USER_STATUS = 'user_status',
  HEARTBEAT = 'heartbeat',
  BROADCAST = 'broadcast',
  SYSTEM = 'system',
  APPROVAL = 'approval',
  CONTRACT_REMINDER = 'contract_reminder',
  ATTENDANCE = 'attendance',
  SALARY = 'salary',
  ERROR = 'error'
}

// 用户状态枚举
export enum UserStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  AWAY = 'away',
  BUSY = 'busy'
}

// WebSocket连接信息
interface WebSocketConnection {
  id: string
  userId: string
  userName: string
  status: UserStatus
  connectedAt: string
  lastHeartbeat: string
  clientInfo: {
    userAgent: string
    ip: string
  }
}

// 消息结构
interface WebSocketMessage {
  id: string
  type: MessageType
  from?: string
  to?: string | string[]
  data: any
  timestamp: string
}

// Mock WebSocket服务器类
export class MockWebSocketServer {
  private static connections: Map<string, WebSocketConnection> = new Map()
  private static messageHistory: WebSocketMessage[] = []
  private static messageHandlers: Map<MessageType, Function[]> = new Map()
  private static heartbeatInterval: any = null

  /**
   * 初始化WebSocket服务
   */
  static initialize() {
    // 设置心跳检查
    this.startHeartbeatCheck()

    // 注册默认消息处理器
    this.registerDefaultHandlers()

    console.log('Mock WebSocket Server initialized')
  }

  /**
   * 模拟客户端连接
   */
  static connect(userId: string, userName: string, clientInfo: any = {}): string {
    const connectionId = generateId()
    const connection: WebSocketConnection = {
      id: connectionId,
      userId,
      userName,
      status: UserStatus.ONLINE,
      connectedAt: new Date().toISOString(),
      lastHeartbeat: new Date().toISOString(),
      clientInfo: {
        userAgent: clientInfo.userAgent || 'MockClient/1.0',
        ip: clientInfo.ip || '127.0.0.1'
      }
    }

    this.connections.set(connectionId, connection)

    // 发送连接成功消息
    this.sendMessage({
      id: generateId(),
      type: MessageType.CONNECTION,
      data: {
        connectionId,
        status: 'connected',
        message: '连接成功'
      },
      timestamp: new Date().toISOString()
    })

    // 广播用户上线通知
    this.broadcastUserStatus(userId, userName, UserStatus.ONLINE)

    return connectionId
  }

  /**
   * 模拟客户端断开连接
   */
  static disconnect(connectionId: string) {
    const connection = this.connections.get(connectionId)
    if (connection) {
      // 广播用户下线通知
      this.broadcastUserStatus(connection.userId, connection.userName, UserStatus.OFFLINE)

      // 移除连接
      this.connections.delete(connectionId)

      console.log(`Connection ${connectionId} disconnected`)
    }
  }

  /**
   * 发送消息
   */
  static sendMessage(message: WebSocketMessage, connectionId?: string) {
    // 保存到历史记录
    this.messageHistory.push(message)

    // 限制历史记录数量
    if (this.messageHistory.length > 1000) {
      this.messageHistory.shift()
    }

    // 触发消息处理器
    const handlers = this.messageHandlers.get(message.type) || []
    handlers.forEach(handler => handler(message))

    // 如果指定了连接ID，模拟发送到特定客户端
    if (connectionId) {
      const connection = this.connections.get(connectionId)
      if (connection) {
        console.log(`Message sent to ${connection.userName}:`, message)
      }
    } else {
      console.log('Message broadcasted:', message)
    }

    return message
  }

  /**
   * 广播消息到所有连接
   */
  static broadcast(type: MessageType, data: any, excludeConnectionId?: string) {
    const message: WebSocketMessage = {
      id: generateId(),
      type,
      data,
      timestamp: new Date().toISOString()
    }

    this.connections.forEach((connection, connectionId) => {
      if (connectionId !== excludeConnectionId) {
        this.sendMessage(message, connectionId)
      }
    })
  }

  /**
   * 发送通知消息
   */
  static sendNotification(
    userId: string,
    notification: {
      title: string
      content: string
      type: 'info' | 'success' | 'warning' | 'error'
      category: string
      link?: string
      actions?: Array<{ label: string; action: string }>
    }
  ) {
    const connectionId = this.findConnectionByUserId(userId)
    if (connectionId) {
      this.sendMessage(
        {
          id: generateId(),
          type: MessageType.NOTIFICATION,
          to: userId,
          data: notification,
          timestamp: new Date().toISOString()
        },
        connectionId
      )
    }

    // 保存到通知历史
    this.saveNotificationHistory(userId, notification)
  }

  /**
   * 广播用户状态变化
   */
  private static broadcastUserStatus(userId: string, userName: string, status: UserStatus) {
    this.broadcast(MessageType.USER_STATUS, {
      userId,
      userName,
      status,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 心跳检查
   */
  private static startHeartbeatCheck() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    this.heartbeatInterval = setInterval(() => {
      const now = new Date()
      const timeout = 60000 // 60秒超时

      this.connections.forEach((connection, connectionId) => {
        const lastHeartbeat = new Date(connection.lastHeartbeat)
        if (now.getTime() - lastHeartbeat.getTime() > timeout) {
          console.log(`Connection ${connectionId} timeout, disconnecting...`)
          this.disconnect(connectionId)
        }
      })
    }, 30000) // 每30秒检查一次
  }

  /**
   * 处理心跳消息
   */
  static handleHeartbeat(connectionId: string) {
    const connection = this.connections.get(connectionId)
    if (connection) {
      connection.lastHeartbeat = new Date().toISOString()

      // 响应心跳
      this.sendMessage(
        {
          id: generateId(),
          type: MessageType.HEARTBEAT,
          data: { status: 'pong' },
          timestamp: new Date().toISOString()
        },
        connectionId
      )
    }
  }

  /**
   * 注册消息处理器
   */
  static registerHandler(type: MessageType, handler: Function) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, [])
    }
    this.messageHandlers.get(type)!.push(handler)
  }

  /**
   * 注册默认处理器
   */
  private static registerDefaultHandlers() {
    // 系统消息处理
    this.registerHandler(MessageType.SYSTEM, (message: WebSocketMessage) => {
      console.log('System message:', message.data)
    })

    // 通知消息处理
    this.registerHandler(MessageType.NOTIFICATION, (message: WebSocketMessage) => {
      console.log('Notification:', message.data)
    })
  }

  /**
   * 根据用户ID查找连接
   */
  private static findConnectionByUserId(userId: string): string | null {
    const connectionsArray = Array.from(this.connections.entries())
    for (const [connectionId, connection] of connectionsArray) {
      if (connection.userId === userId) {
        return connectionId
      }
    }
    return null
  }

  /**
   * 保存通知历史
   */
  private static saveNotificationHistory(userId: string, notification: any) {
    const historyKey = `ws_notification_history_${userId}`
    const history = JSON.parse(localStorage.getItem(historyKey) || '[]')

    history.unshift({
      ...notification,
      id: generateId(),
      userId,
      createdAt: new Date().toISOString(),
      isRead: false
    })

    // 只保留最近100条
    if (history.length > 100) {
      history.length = 100
    }

    localStorage.setItem(historyKey, JSON.stringify(history))
  }

  /**
   * 获取连接状态
   */
  static getConnectionStatus() {
    const connections = Array.from(this.connections.values())
    return {
      total: connections.length,
      online: connections.filter(c => c.status === UserStatus.ONLINE).length,
      away: connections.filter(c => c.status === UserStatus.AWAY).length,
      busy: connections.filter(c => c.status === UserStatus.BUSY).length,
      connections: connections.map(c => ({
        userId: c.userId,
        userName: c.userName,
        status: c.status,
        connectedAt: c.connectedAt,
        duration: this.calculateDuration(c.connectedAt)
      }))
    }
  }

  /**
   * 获取消息历史
   */
  static getMessageHistory(filter?: { type?: MessageType; userId?: string; limit?: number }) {
    let history = [...this.messageHistory]

    if (filter?.type) {
      history = history.filter(m => m.type === filter.type)
    }

    if (filter?.userId) {
      history = history.filter(m => m.from === filter.userId || m.to === filter.userId)
    }

    if (filter?.limit) {
      history = history.slice(-filter.limit)
    }

    return history
  }

  /**
   * 计算连接时长
   */
  private static calculateDuration(connectedAt: string): string {
    const now = new Date()
    const connected = new Date(connectedAt)
    const duration = now.getTime() - connected.getTime()

    const hours = Math.floor(duration / 3600000)
    const minutes = Math.floor((duration % 3600000) / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds}秒`
    } else {
      return `${seconds}秒`
    }
  }

  /**
   * 清理资源
   */
  static cleanup() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    this.connections.clear()
    this.messageHistory = []
    this.messageHandlers.clear()
  }
}

// 初始化服务
MockWebSocketServer.initialize()
