// src/mock/utils/index.ts
import { faker } from "@faker-js/faker/locale/zh_CN";

// src/mock/utils/errorHandler.ts
var MockError = class extends Error {
  /** 错误类型 */
  type;
  /** HTTP状态码 */
  statusCode;
  /** 错误详情 */
  details;
  /** 错误发生时间 */
  timestamp;
  constructor(message, type = "SYSTEM_ERROR" /* SYSTEM_ERROR */, statusCode = 500, details) {
    super(message);
    this.name = "MockError";
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = (/* @__PURE__ */ new Date()).toISOString();
  }
};
var ErrorCollector = class {
  errors = [];
  /**
   * 收集错误
   */
  collect(error, context) {
    this.errors.push({
      error,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      context
    });
  }
  /**
   * 获取所有错误
   */
  getErrors() {
    return [...this.errors];
  }
  /**
   * 清空错误
   */
  clear() {
    this.errors = [];
  }
  /**
   * 获取错误统计
   */
  getStatistics() {
    const stats = {};
    this.errors.forEach(({ error }) => {
      const type = error instanceof MockError ? error.type : "UNKNOWN";
      stats[type] = (stats[type] || 0) + 1;
    });
    return stats;
  }
};
var globalErrorCollector = new ErrorCollector();

// src/mock/utils/index.ts
function generateId() {
  return faker.string.uuid();
}
var isBrowser = typeof window !== "undefined" && typeof window.localStorage !== "undefined";

// src/mock/websocket/MockWebSocketServer.ts
var MessageType = /* @__PURE__ */ ((MessageType2) => {
  MessageType2["CONNECTION"] = "connection";
  MessageType2["NOTIFICATION"] = "notification";
  MessageType2["USER_STATUS"] = "user_status";
  MessageType2["HEARTBEAT"] = "heartbeat";
  MessageType2["BROADCAST"] = "broadcast";
  MessageType2["SYSTEM"] = "system";
  MessageType2["APPROVAL"] = "approval";
  MessageType2["CONTRACT_REMINDER"] = "contract_reminder";
  MessageType2["ATTENDANCE"] = "attendance";
  MessageType2["SALARY"] = "salary";
  MessageType2["ERROR"] = "error";
  return MessageType2;
})(MessageType || {});
var UserStatus = /* @__PURE__ */ ((UserStatus2) => {
  UserStatus2["ONLINE"] = "online";
  UserStatus2["OFFLINE"] = "offline";
  UserStatus2["AWAY"] = "away";
  UserStatus2["BUSY"] = "busy";
  return UserStatus2;
})(UserStatus || {});
var MockWebSocketServer = class {
  static connections = /* @__PURE__ */ new Map();
  static messageHistory = [];
  static messageHandlers = /* @__PURE__ */ new Map();
  static heartbeatInterval = null;
  /**
   * 初始化WebSocket服务
   */
  static initialize() {
    this.startHeartbeatCheck();
    this.registerDefaultHandlers();
    console.log("Mock WebSocket Server initialized");
  }
  /**
   * 模拟客户端连接
   */
  static connect(userId, userName, clientInfo = {}) {
    const connectionId = generateId();
    const connection = {
      id: connectionId,
      userId,
      userName,
      status: "online" /* ONLINE */,
      connectedAt: (/* @__PURE__ */ new Date()).toISOString(),
      lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
      clientInfo: {
        userAgent: clientInfo.userAgent || "MockClient/1.0",
        ip: clientInfo.ip || "127.0.0.1"
      }
    };
    this.connections.set(connectionId, connection);
    this.sendMessage({
      id: generateId(),
      type: "connection" /* CONNECTION */,
      data: {
        connectionId,
        status: "connected",
        message: "\u8FDE\u63A5\u6210\u529F"
      },
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    });
    this.broadcastUserStatus(userId, userName, "online" /* ONLINE */);
    return connectionId;
  }
  /**
   * 模拟客户端断开连接
   */
  static disconnect(connectionId) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      this.broadcastUserStatus(connection.userId, connection.userName, "offline" /* OFFLINE */);
      this.connections.delete(connectionId);
      console.log(`Connection ${connectionId} disconnected`);
    }
  }
  /**
   * 发送消息
   */
  static sendMessage(message, connectionId) {
    this.messageHistory.push(message);
    if (this.messageHistory.length > 1e3) {
      this.messageHistory.shift();
    }
    const handlers = this.messageHandlers.get(message.type) || [];
    handlers.forEach((handler) => handler(message));
    if (connectionId) {
      const connection = this.connections.get(connectionId);
      if (connection) {
        console.log(`Message sent to ${connection.userName}:`, message);
      }
    } else {
      console.log("Message broadcasted:", message);
    }
    return message;
  }
  /**
   * 广播消息到所有连接
   */
  static broadcast(type, data, excludeConnectionId) {
    const message = {
      id: generateId(),
      type,
      data,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
    this.connections.forEach((connection, connectionId) => {
      if (connectionId !== excludeConnectionId) {
        this.sendMessage(message, connectionId);
      }
    });
  }
  /**
   * 发送通知消息
   */
  static sendNotification(userId, notification) {
    const connectionId = this.findConnectionByUserId(userId);
    if (connectionId) {
      this.sendMessage(
        {
          id: generateId(),
          type: "notification" /* NOTIFICATION */,
          to: userId,
          data: notification,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        },
        connectionId
      );
    }
    this.saveNotificationHistory(userId, notification);
  }
  /**
   * 广播用户状态变化
   */
  static broadcastUserStatus(userId, userName, status) {
    this.broadcast("user_status" /* USER_STATUS */, {
      userId,
      userName,
      status,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    });
  }
  /**
   * 心跳检查
   */
  static startHeartbeatCheck() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    this.heartbeatInterval = setInterval(() => {
      const now = /* @__PURE__ */ new Date();
      const timeout = 6e4;
      this.connections.forEach((connection, connectionId) => {
        const lastHeartbeat = new Date(connection.lastHeartbeat);
        if (now.getTime() - lastHeartbeat.getTime() > timeout) {
          console.log(`Connection ${connectionId} timeout, disconnecting...`);
          this.disconnect(connectionId);
        }
      });
    }, 3e4);
  }
  /**
   * 处理心跳消息
   */
  static handleHeartbeat(connectionId) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastHeartbeat = (/* @__PURE__ */ new Date()).toISOString();
      this.sendMessage(
        {
          id: generateId(),
          type: "heartbeat" /* HEARTBEAT */,
          data: { status: "pong" },
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        },
        connectionId
      );
    }
  }
  /**
   * 注册消息处理器
   */
  static registerHandler(type, handler) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type).push(handler);
  }
  /**
   * 注册默认处理器
   */
  static registerDefaultHandlers() {
    this.registerHandler("system" /* SYSTEM */, (message) => {
      console.log("System message:", message.data);
    });
    this.registerHandler("notification" /* NOTIFICATION */, (message) => {
      console.log("Notification:", message.data);
    });
  }
  /**
   * 根据用户ID查找连接
   */
  static findConnectionByUserId(userId) {
    const connectionsArray = Array.from(this.connections.entries());
    for (const [connectionId, connection] of connectionsArray) {
      if (connection.userId === userId) {
        return connectionId;
      }
    }
    return null;
  }
  /**
   * 保存通知历史
   */
  static saveNotificationHistory(userId, notification) {
    const historyKey = `ws_notification_history_${userId}`;
    const history = JSON.parse(localStorage.getItem(historyKey) || "[]");
    history.unshift({
      ...notification,
      id: generateId(),
      userId,
      createdAt: (/* @__PURE__ */ new Date()).toISOString(),
      isRead: false
    });
    if (history.length > 100) {
      history.length = 100;
    }
    localStorage.setItem(historyKey, JSON.stringify(history));
  }
  /**
   * 获取连接状态
   */
  static getConnectionStatus() {
    const connections = Array.from(this.connections.values());
    return {
      total: connections.length,
      online: connections.filter((c) => c.status === "online" /* ONLINE */).length,
      away: connections.filter((c) => c.status === "away" /* AWAY */).length,
      busy: connections.filter((c) => c.status === "busy" /* BUSY */).length,
      connections: connections.map((c) => ({
        userId: c.userId,
        userName: c.userName,
        status: c.status,
        connectedAt: c.connectedAt,
        duration: this.calculateDuration(c.connectedAt)
      }))
    };
  }
  /**
   * 获取消息历史
   */
  static getMessageHistory(filter) {
    let history = [...this.messageHistory];
    if (filter?.type) {
      history = history.filter((m) => m.type === filter.type);
    }
    if (filter?.userId) {
      history = history.filter((m) => m.from === filter.userId || m.to === filter.userId);
    }
    if (filter?.limit) {
      history = history.slice(-filter.limit);
    }
    return history;
  }
  /**
   * 计算连接时长
   */
  static calculateDuration(connectedAt) {
    const now = /* @__PURE__ */ new Date();
    const connected = new Date(connectedAt);
    const duration = now.getTime() - connected.getTime();
    const hours = Math.floor(duration / 36e5);
    const minutes = Math.floor(duration % 36e5 / 6e4);
    const seconds = Math.floor(duration % 6e4 / 1e3);
    if (hours > 0) {
      return `${hours}\u5C0F\u65F6${minutes}\u5206\u949F`;
    } else if (minutes > 0) {
      return `${minutes}\u5206\u949F${seconds}\u79D2`;
    } else {
      return `${seconds}\u79D2`;
    }
  }
  /**
   * 清理资源
   */
  static cleanup() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    this.connections.clear();
    this.messageHistory = [];
    this.messageHandlers.clear();
  }
};
MockWebSocketServer.initialize();
export {
  MessageType,
  MockWebSocketServer,
  UserStatus
};
//# sourceMappingURL=data:application/json;base64,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
