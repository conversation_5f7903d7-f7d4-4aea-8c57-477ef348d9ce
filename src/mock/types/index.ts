/**
 * @name Mock服务类型定义
 * @description 提供Mock服务相关的TypeScript类型定义
 * <AUTHOR>
 * @since 2025-01-24
 */

/**
 * Mock请求方法类型
 */
export type MockMethod = 'get' | 'post' | 'put' | 'delete' | 'patch' | 'head' | 'options'

/**
 * Mock请求头类型
 */
export interface MockHeaders extends Record<string, string | string[] | undefined> {
  'content-type'?: string
  authorization?: string
  'x-user-id'?: string
  'x-user-permissions'?: string
  'x-request-id'?: string
}

/**
 * Mock请求参数
 */
export interface MockRequestParams {
  /** URL路径参数 */
  params?: Record<string, string>
  /** 查询参数 */
  query?: Record<string, any>
  /** 请求体 */
  body?: any
  /** 请求头 */
  headers?: MockHeaders
}

/**
 * Mock请求选项（扩展版）
 */
export interface MockRequestOptions extends MockRequestParams {
  /** 请求URL */
  url: string
  /** 请求方法 */
  method: MockMethod
  /** 原始请求对象 */
  rawRequest?: any
}

/**
 * Mock响应选项
 */
export interface MockResponseOptions {
  /** HTTP状态码 */
  statusCode?: number
  /** 响应头 */
  headers?: Record<string, string>
  /** 响应延迟(ms) */
  delay?: number
}

/**
 * Mock配置项
 */
export interface MockConfig {
  /** 是否启用Mock */
  enabled: boolean
  /** Mock延迟配置 */
  delayEnabled: boolean
  delayRange: {
    min: number
    max: number
  }
  /** 响应格式配置 */
  responseFormat: {
    codeKey: string
    messageKey: string
    dataKey: string
    successKey: string
    successCode: number
  }
  /** 分页配置 */
  pagination: {
    defaultPage: number
    defaultSize: number
    maxSize: number
  }
  /** 持久化配置 */
  persistence: {
    enabled: boolean
    storage: 'localStorage' | 'sessionStorage'
    prefix: string
    expires: number // 小时
  }
  /** 日志配置 */
  logger: {
    enabled: boolean
    request: boolean
    response: boolean
    error: boolean
  }
  /** 错误模拟配置 */
  errorSimulation: {
    enabled: boolean
    rate: number
    codes: number[]
  }
  /** API版本 */
  version?: string
}

/**
 * Mock服务接口
 */
export interface IMockService<T> {
  /** 服务名称 */
  readonly name: string

  /** 创建数据 */
  create(data: T): boolean

  /** 读取单条数据 */
  read(id: string): T | undefined

  /** 读取所有数据 */
  readAll(): T[]

  /** 更新数据 */
  update(id: string, data: Partial<T>): boolean

  /** 删除数据 */
  delete(id: string): boolean

  /** 清空所有数据 */
  clearAll(): void

  /** 按条件查找 */
  findBy(field: string, value: any): T[]

  /** 按条件查找单条 */
  findOneBy(field: string, value: any): T | undefined

  /** 检查是否存在 */
  exists(id: string): boolean

  /** 获取数据总数 */
  count(): number
}

/**
 * 增强的Mock服务接口
 */
export interface IEnhancedMockService<T> extends IMockService<T> {
  /** 综合查询 */
  query(params: any): any

  /** 批量创建 */
  createBatch(items: T[]): T[]

  /** 批量更新 */
  updateBatch(updates: Array<{ id: string } & Partial<T>>): { success: string[]; failed: string[] }

  /** 批量删除 */
  deleteBatch(ids: string[]): { success: string[]; failed: string[] }

  /** 导出数据 */
  export(ids?: string[]): T[]

  /** 导入数据 */
  import(items: T[], mode: 'merge' | 'replace'): { success: number; failed: number; errors: any[] }

  /** 获取统计信息 */
  getStatistics(): Record<string, any>
}

/**
 * 基础实体接口
 */
export interface BaseEntity {
  /** 唯一标识 */
  id?: string
  /** 创建时间 */
  createdAt?: string
  /** 更新时间 */
  updatedAt?: string
  /** 创建人ID */
  createdBy?: string
  /** 更新人ID */
  updatedBy?: string
}

/**
 * 可排序实体接口
 */
export interface SortableEntity extends BaseEntity {
  /** 排序值 */
  sortOrder?: number
}

/**
 * 可启用/禁用实体接口
 */
export interface ToggleableEntity extends BaseEntity {
  /** 是否启用 */
  enabled?: boolean
  /** 状态 */
  status?: 'active' | 'inactive' | 'pending'
}

/**
 * 树形结构实体接口
 */
export interface TreeEntity extends BaseEntity {
  /** 父级ID */
  parentId?: string | null
  /** 子级列表 */
  children?: TreeEntity[]
  /** 层级 */
  level?: number
  /** 路径 */
  path?: string
}

/**
 * Mock数据生成器接口
 */
export interface IMockGenerator<T> {
  /** 生成单条数据 */
  generate(): T

  /** 生成多条数据 */
  generateBatch(count: number): T[]

  /** 生成部分数据 */
  generatePartial(): Partial<T>
}

/**
 * Mock场景接口
 */
export interface IMockScenario {
  /** 场景名称 */
  name: string

  /** 场景描述 */
  description?: string

  /** 初始化场景 */
  setup(): void

  /** 清理场景 */
  teardown(): void

  /** 验证场景 */
  validate?(): boolean
}

/**
 * Mock中间件接口
 */
export interface IMockMiddleware {
  /** 请求前处理 */
  beforeRequest?(options: MockRequestOptions): MockRequestOptions | Promise<MockRequestOptions>

  /** 响应前处理 */
  beforeResponse?(response: any, options: MockRequestOptions): any | Promise<any>

  /** 错误处理 */
  onError?(error: Error, options: MockRequestOptions): any | Promise<any>
}

/**
 * 文件信息接口
 */
export interface FileInfo {
  /** 文件ID */
  id: string
  /** 文件名 */
  filename: string
  /** 原始文件名 */
  originalName: string
  /** MIME类型 */
  mimeType: string
  /** 文件大小(字节) */
  size: number
  /** 文件URL */
  url: string
  /** 上传时间 */
  uploadTime: string
  /** 上传者ID */
  uploaderId?: string
}

/**
 * 批量操作结果
 */
export interface BatchResult {
  /** 成功数量 */
  success: number
  /** 失败数量 */
  failed: number
  /** 总数 */
  total: number
  /** 错误详情 */
  errors?: Array<{
    index?: number
    id?: string
    message: string
    details?: any
  }>
}

/**
 * 导入导出选项
 */
export interface ImportExportOptions {
  /** 文件格式 */
  format: 'json' | 'csv' | 'excel'
  /** 字段映射 */
  fieldMapping?: Record<string, string>
  /** 是否包含表头 */
  includeHeaders?: boolean
  /** 编码 */
  encoding?: string
  /** 分隔符(CSV) */
  delimiter?: string
  /** 工作表名(Excel) */
  sheetName?: string
}

/**
 * WebSocket消息类型
 */
export interface WSMessage<T = any> {
  /** 消息类型 */
  type: string
  /** 消息数据 */
  data: T
  /** 消息ID */
  id?: string
  /** 时间戳 */
  timestamp: number
}

/**
 * 统计数据接口
 */
export interface Statistics {
  /** 统计项名称 */
  name: string
  /** 统计值 */
  value: number
  /** 变化趋势 */
  trend?: 'up' | 'down' | 'stable'
  /** 变化率 */
  changeRate?: number
  /** 统计时间 */
  time?: string
}

/**
 * 审计日志接口
 */
export interface AuditLog {
  /** 日志ID */
  id: string
  /** 操作类型 */
  action: 'create' | 'read' | 'update' | 'delete' | 'import' | 'export'
  /** 资源类型 */
  resourceType: string
  /** 资源ID */
  resourceId?: string
  /** 操作者ID */
  userId: string
  /** 操作者名称 */
  userName?: string
  /** 操作时间 */
  timestamp: string
  /** 操作详情 */
  details?: Record<string, any>
  /** IP地址 */
  ip?: string
  /** 用户代理 */
  userAgent?: string
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  /** 是否启用缓存 */
  enabled: boolean
  /** 缓存时间(秒) */
  ttl: number
  /** 最大缓存数量 */
  maxSize?: number
  /** 缓存策略 */
  strategy?: 'lru' | 'fifo' | 'lfu'
}

/**
 * 类型守卫辅助函数
 */
export const TypeGuards = {
  isBaseEntity(obj: any): obj is BaseEntity {
    return obj && typeof obj.id === 'string'
  },

  isSortableEntity(obj: any): obj is SortableEntity {
    return this.isBaseEntity(obj) && typeof (obj as SortableEntity).sortOrder === 'number'
  },

  isToggleableEntity(obj: any): obj is ToggleableEntity {
    return this.isBaseEntity(obj) && typeof (obj as ToggleableEntity).enabled === 'boolean'
  },

  isTreeEntity(obj: any): obj is TreeEntity {
    return this.isBaseEntity(obj) && 'parentId' in obj
  }
}
