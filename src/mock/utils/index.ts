/**
 * @name Mock工具函数库
 * @description 提供Mock数据生成和处理的工具函数
 * <AUTHOR>
 * @since 2025-01-24
 */

import { faker } from '@faker-js/faker/locale/zh_CN'
import mockConfig from '../config'

/**
 * 生成UUID
 */
export function generateId(): string {
  return faker.string.uuid()
}

/**
 * 生成指定范围的随机数
 */
export function randomNumber(min: number, max: number): number {
  return faker.number.int({ min, max })
}

/**
 * 别名：生成指定范围的随机数
 */
export function getRandomNumber(min: number, max: number): number {
  return randomNumber(min, max)
}

/**
 * 从数组中随机获取一个元素
 */
export function getRandomItem<T>(array: T[]): T {
  const index = randomNumber(0, array.length - 1)
  return array[index]
}

/**
 * mockResponse 对象形式
 */
export const mockResponse = {
  success: success,
  error: error
}

/**
 * 生成随机延迟时间
 */
export function getRandomDelay(): number {
  const { min, max } = mockConfig.delayRange
  return randomNumber(min, max)
}

/**
 * 模拟网络延迟
 */
export function delay(ms?: number): Promise<void> {
  const delayTime = ms || getRandomDelay()
  return new Promise(resolve => setTimeout(resolve, delayTime))
}

// 导入新的响应处理工具
import { response as newResponse } from './response'

/**
 * 统一成功响应格式（保留向后兼容）
 */
export function success<T>(data: T, message = '操作成功'): any {
  return newResponse.success(data, message)
}

/**
 * 统一错误响应格式（保留向后兼容）
 */
export function error(message: string, code = 500, data: any = null): any {
  return newResponse.error(message, code, data)
}

/**
 * 分页数据处理
 */
export function paginate<T>(
  list: T[],
  page: number,
  size: number
): {
  list: T[]
  total: number
  page: number
  size: number
  pages: number
} {
  const { defaultPage, defaultSize, maxSize } = mockConfig.pagination

  // 参数验证和默认值
  const currentPage = Math.max(1, page || defaultPage)
  const pageSize = Math.min(Math.max(1, size || defaultSize), maxSize)

  // 计算分页
  const total = list.length
  const pages = Math.ceil(total / pageSize)
  const start = (currentPage - 1) * pageSize
  const end = start + pageSize

  return {
    list: list.slice(start, end),
    total,
    page: currentPage,
    size: pageSize,
    pages
  }
}

/**
 * 分页响应格式
 */
export function pageResult<T>(
  list: T[],
  total: number,
  page: number,
  size: number,
  message = '获取成功'
): any {
  const pages = Math.ceil(total / size)

  return success(
    {
      list,
      total,
      page,
      size,
      pages
    },
    message
  )
}

/**
 * 检查是否在浏览器环境
 */
const isBrowser = typeof window !== 'undefined' && typeof window.localStorage !== 'undefined'

/**
 * 内存存储（用于Node环境）
 */
const memoryStorage: { [key: string]: string } = {}

/**
 * 本地存储管理
 */
export const storage = {
  /**
   * 获取存储key
   */
  getKey(key: string): string {
    return `${mockConfig.persistence.prefix}${key}`
  },

  /**
   * 保存数据
   */
  set(key: string, value: any): void {
    if (!mockConfig.persistence.enabled) return

    const storageKey = this.getKey(key)
    const data = {
      value,
      expires: Date.now() + mockConfig.persistence.expires * 3600 * 1000,
      timestamp: Date.now()
    }

    try {
      if (!isBrowser) {
        // Node环境使用内存存储
        memoryStorage[storageKey] = JSON.stringify(data)
        return
      }

      if (mockConfig.persistence.storage === 'localStorage') {
        localStorage.setItem(storageKey, JSON.stringify(data))
      } else {
        sessionStorage.setItem(storageKey, JSON.stringify(data))
      }
    } catch (e) {
      console.error('Mock数据存储失败:', e)
    }
  },

  /**
   * 获取数据
   */
  get<T>(key: string, defaultValue?: T): T | undefined {
    if (!mockConfig.persistence.enabled) return defaultValue

    const storageKey = this.getKey(key)

    try {
      if (!isBrowser) {
        // Node环境从内存存储读取
        const item = memoryStorage[storageKey]
        if (!item) return defaultValue

        const data = JSON.parse(item)

        // 检查是否过期
        if (data.expires && data.expires < Date.now()) {
          delete memoryStorage[storageKey]
          return defaultValue
        }

        return data.value as T
      }

      const storage =
        mockConfig.persistence.storage === 'localStorage' ? localStorage : sessionStorage

      const item = storage.getItem(storageKey)
      if (!item) return defaultValue

      const data = JSON.parse(item)

      // 检查是否过期
      if (data.expires && data.expires < Date.now()) {
        storage.removeItem(storageKey)
        return defaultValue
      }

      return data.value as T
    } catch (e) {
      console.error('Mock数据读取失败:', e)
      return defaultValue
    }
  },

  /**
   * 删除数据
   */
  remove(key: string): void {
    if (!mockConfig.persistence.enabled) return

    const storageKey = this.getKey(key)

    try {
      if (!isBrowser) {
        // Node环境从内存存储删除
        delete memoryStorage[storageKey]
        return
      }

      if (mockConfig.persistence.storage === 'localStorage') {
        localStorage.removeItem(storageKey)
      } else {
        sessionStorage.removeItem(storageKey)
      }
    } catch (e) {
      console.error('Mock数据删除失败:', e)
    }
  },

  /**
   * 清空所有Mock数据
   */
  clear(): void {
    if (!mockConfig.persistence.enabled) return

    const prefix = mockConfig.persistence.prefix

    if (!isBrowser) {
      // Node环境清空内存存储
      Object.keys(memoryStorage).forEach(key => {
        if (key.startsWith(prefix)) {
          delete memoryStorage[key]
        }
      })
      return
    }

    const storage =
      mockConfig.persistence.storage === 'localStorage' ? localStorage : sessionStorage

    const keys = Object.keys(storage)

    keys.forEach(key => {
      if (key.startsWith(prefix)) {
        storage.removeItem(key)
      }
    })
  }
}

/**
 * 日志工具
 */
export const logger = {
  /**
   * 请求日志
   */
  request(url: string, method: string, params?: any): void {
    if (!mockConfig.logger.enabled || !mockConfig.logger.request) return

    console.group(`🔵 [Mock Request] ${method} ${url}`)
    if (params) {
      console.log('Parameters:', params)
    }
    console.groupEnd()
  },

  /**
   * 响应日志
   */
  response(url: string, data: any, duration?: number): void {
    if (!mockConfig.logger.enabled || !mockConfig.logger.response) return

    console.group(`🟢 [Mock Response] ${url}`)
    console.log('Response:', data)
    if (duration) {
      console.log(`Duration: ${duration}ms`)
    }
    console.groupEnd()
  },

  /**
   * 错误日志
   */
  error(message: string, error?: any): void {
    if (!mockConfig.logger.enabled || !mockConfig.logger.error) return

    console.group(`🔴 [Mock Error] ${message}`)
    if (error) {
      console.error('Error:', error)
    }
    console.groupEnd()
  }
}

/**
 * 模拟错误（用于测试错误处理）
 */
export function simulateError(): boolean {
  if (!mockConfig.errorSimulation.enabled) return false

  return Math.random() < mockConfig.errorSimulation.rate
}

/**
 * 获取随机错误码
 */
export function getRandomErrorCode(): number {
  const codes = mockConfig.errorSimulation.codes
  return codes[randomNumber(0, codes.length - 1)]
}

/**
 * 生成中文姓名
 */
export function generateChineseName(): string {
  return faker.person.fullName()
}

/**
 * 生成手机号
 */
export function generatePhoneNumber(): string {
  // 生成中国大陆手机号格式：1开头的11位数字
  const prefixes = [
    '130',
    '131',
    '132',
    '133',
    '134',
    '135',
    '136',
    '137',
    '138',
    '139',
    '150',
    '151',
    '152',
    '153',
    '155',
    '156',
    '157',
    '158',
    '159',
    '180',
    '181',
    '182',
    '183',
    '184',
    '185',
    '186',
    '187',
    '188',
    '189'
  ]
  const prefix = faker.helpers.arrayElement(prefixes)
  const suffix = faker.string.numeric(8)
  return prefix + suffix
}

/**
 * 生成邮箱
 */
export function generateEmail(): string {
  return faker.internet.email()
}

/**
 * 生成部门名称
 */
export function generateDepartment(): string {
  const departments = [
    '技术部',
    '产品部',
    '运营部',
    '市场部',
    '销售部',
    '人事部',
    '财务部',
    '行政部',
    '法务部',
    '采购部'
  ]
  return faker.helpers.arrayElement(departments)
}

/**
 * 生成职位名称
 */
export function generatePosition(): string {
  const positions = [
    '前端工程师',
    '后端工程师',
    '产品经理',
    '设计师',
    '测试工程师',
    '运营专员',
    '市场专员',
    '销售代表',
    '人事专员',
    '财务专员'
  ]
  return faker.helpers.arrayElement(positions)
}

/**
 * 生成随机布尔值
 */
export function randomBoolean(probability = 0.5): boolean {
  return Math.random() < probability
}

/**
 * 生成时间范围内的随机日期
 */
export function randomDate(start: Date, end: Date): Date {
  return faker.date.between({ from: start, to: end })
}

/**
 * 深拷贝对象
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj))
}

/**
 * 合并对象（用于更新数据）
 */
export function mergeObjects<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  return { ...target, ...source }
}

// 导出错误处理工具
export * from './errorHandler'

// 导出数据处理工具
export * from './dataHelpers'

// 导出响应处理工具
export * from './response'

// 导出Mock包装器
export * from './mockWrapper'
