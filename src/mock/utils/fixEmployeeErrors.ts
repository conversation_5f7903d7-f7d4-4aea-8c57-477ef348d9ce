/**
 * @name Employee模块错误修复方案
 * @description 修复employee.ts中的类型错误
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { Employee } from '../../types/employee'
import { MockServiceManager } from '../service'

// 1. 创建适配器类型，让Employee类型兼容SimpleMockService
export type EmployeeWithId = Employee & { id?: string }

// 2. 创建类型适配器函数
export function adaptEmployee(employee: Employee): EmployeeWithId {
  return {
    ...employee,
    id: employee.employeeId // 使用employeeId作为id
  }
}

// 3. 创建反向适配器
export function extractEmployee(data: EmployeeWithId): Employee {
  const { id, ...employee } = data
  return employee as Employee
}

// 4. 为ValidationError创建类型定义
export interface ValidationError {
  id?: string
  field?: string
  message: string
}

// 5. 修复MockRequestOptions的params问题
export interface SafeMockRequestOptions {
  url: string
  method: string
  params: Record<string, string> // 确保params总是存在
  query?: Record<string, any>
  body?: any
  headers?: Record<string, string>
}

// 6. 创建安全的参数提取函数
export function getSafeParams(options: any): Record<string, string> {
  return options?.params || {}
}

// 7. 创建类型安全的服务包装器
export function createTypedEmployeeService() {
  const baseService = MockServiceManager.getService<EmployeeWithId>('employees')

  return {
    create(employee: Employee): Employee {
      const adapted = adaptEmployee(employee)
      const result = baseService.create(adapted)
      return extractEmployee(result)
    },

    read(id: string): Employee | undefined {
      const result = baseService.read(id)
      return result ? extractEmployee(result) : undefined
    },

    readAll(): Employee[] {
      return baseService.readAll().map(extractEmployee)
    },

    update(id: string, data: Partial<Employee>): Employee | undefined {
      const result = baseService.update(id, data)
      return result ? extractEmployee(result) : undefined
    },

    delete(id: string): boolean {
      return baseService.delete(id)
    },

    findBy(field: keyof Employee, value: any): Employee[] {
      return baseService.findBy(field as string, value).map(extractEmployee)
    },

    findOneBy(field: keyof Employee, value: any): Employee | undefined {
      const result = baseService.findOneBy(field as string, value)
      return result ? extractEmployee(result) : undefined
    },

    createBatch(employees: Employee[]): void {
      const adapted = employees.map(adaptEmployee)
      adapted.forEach(emp => baseService.create(emp))
    }
  }
}

// 8. 类型安全的数组方法包装器
export function typedFilter<T>(
  array: T[],
  predicate: (item: T, index: number, array: T[]) => boolean
): T[] {
  return array.filter(predicate)
}

export function typedMap<T, R>(array: T[], mapper: (item: T, index: number, array: T[]) => R): R[] {
  return array.map(mapper)
}

export function typedForEach<T>(
  array: T[],
  callback: (item: T, index: number, array: T[]) => void
): void {
  array.forEach(callback)
}

// 9. 创建类型断言辅助函数
export function isEmployee(obj: any): obj is Employee {
  return (
    obj &&
    typeof obj.employeeId === 'string' &&
    typeof obj.employeeNumber === 'string' &&
    typeof obj.fullName === 'string'
  )
}

export function assertEmployee(obj: any): Employee {
  if (!isEmployee(obj)) {
    throw new Error('Invalid employee data')
  }
  return obj
}
