/**
 * @name Mock工具函数测试
 * @description 测试Mock工具函数的功能
 * <AUTHOR>
 * @since 2025-01-24
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  generateId,
  randomNumber,
  delay,
  success,
  error,
  paginate,
  pageResult,
  storage,
  generateChineseName,
  generatePhoneNumber,
  generateEmail,
  deepClone,
  mergeObjects
} from '../index'

describe('Mock工具函数测试', () => {
  describe('generateId', () => {
    it('应该生成有效的UUID', () => {
      const id = generateId()
      expect(id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)
    })

    it('每次生成的ID应该不同', () => {
      const id1 = generateId()
      const id2 = generateId()
      expect(id1).not.toBe(id2)
    })
  })

  describe('randomNumber', () => {
    it('应该生成指定范围内的随机数', () => {
      const min = 10
      const max = 20
      const num = randomNumber(min, max)
      expect(num).toBeGreaterThanOrEqual(min)
      expect(num).toBeLessThanOrEqual(max)
    })
  })

  describe('delay', () => {
    it('应该延迟指定时间', async () => {
      const start = Date.now()
      await delay(100)
      const end = Date.now()
      expect(end - start).toBeGreaterThanOrEqual(90) // 允许10ms误差
    })
  })

  describe('success', () => {
    it('应该返回成功响应格式', () => {
      const data = { id: 1, name: 'test' }
      const result = success(data)

      expect(result.code).toBe(200)
      expect(result.success).toBe(true)
      expect(result.message).toBe('操作成功')
      expect(result.data).toEqual(data)
      expect(result.timestamp).toBeDefined()
    })

    it('应该支持自定义消息', () => {
      const result = success({}, '自定义消息')
      expect(result.message).toBe('自定义消息')
    })
  })

  describe('error', () => {
    it('应该返回错误响应格式', () => {
      const result = error('错误信息')

      expect(result.code).toBe(500)
      expect(result.success).toBe(false)
      expect(result.message).toBe('错误信息')
      expect(result.data).toBeNull()
      expect(result.timestamp).toBeDefined()
    })

    it('应该支持自定义错误码', () => {
      const result = error('未授权', 401)
      expect(result.code).toBe(401)
    })
  })

  describe('paginate', () => {
    const testData = Array.from({ length: 25 }, (_, i) => ({ id: i + 1, name: `Item ${i + 1}` }))

    it('应该正确分页数据', () => {
      const result = paginate(testData, 2, 10)

      expect(result.list.length).toBe(10)
      expect(result.list[0].id).toBe(11)
      expect(result.total).toBe(25)
      expect(result.page).toBe(2)
      expect(result.size).toBe(10)
      expect(result.pages).toBe(3)
    })

    it('应该处理最后一页', () => {
      const result = paginate(testData, 3, 10)

      expect(result.list.length).toBe(5)
      expect(result.list[0].id).toBe(21)
    })

    it('应该处理无效参数', () => {
      const result = paginate(testData, -1, 0)

      expect(result.page).toBe(1)
      expect(result.size).toBeGreaterThan(0)
    })
  })

  describe('storage', () => {
    beforeEach(() => {
      localStorage.clear()
      sessionStorage.clear()
    })

    it('应该保存和获取数据', () => {
      const testData = { id: 1, name: 'test' }
      storage.set('test', testData)

      const retrieved = storage.get<typeof testData>('test')
      expect(retrieved).toEqual(testData)
    })

    it('应该返回默认值当数据不存在时', () => {
      const defaultValue = { id: 0, name: 'default' }
      const result = storage.get('nonexistent', defaultValue)
      expect(result).toEqual(defaultValue)
    })

    it('应该删除数据', () => {
      storage.set('test', { id: 1 })
      storage.remove('test')

      const result = storage.get('test')
      expect(result).toBeUndefined()
    })

    it('应该清空所有Mock数据', () => {
      storage.set('test1', { id: 1 })
      storage.set('test2', { id: 2 })
      storage.clear()

      expect(storage.get('test1')).toBeUndefined()
      expect(storage.get('test2')).toBeUndefined()
    })
  })

  describe('数据生成函数', () => {
    it('应该生成有效的中文姓名', () => {
      const name = generateChineseName()
      expect(name).toBeTruthy()
      expect(name.length).toBeGreaterThan(0)
    })

    it('应该生成有效的手机号', () => {
      const phone = generatePhoneNumber()
      expect(phone).toMatch(/^1\d{10}$/)
    })

    it('应该生成有效的邮箱', () => {
      const email = generateEmail()
      expect(email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
    })
  })

  describe('工具函数', () => {
    it('deepClone应该深拷贝对象', () => {
      const original = { a: 1, b: { c: 2 } }
      const cloned = deepClone(original)

      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned.b).not.toBe(original.b)
    })

    it('mergeObjects应该正确合并对象', () => {
      const target = { a: 1, b: 2 }
      const source = { b: 3, c: 4 }
      const result = mergeObjects(target, source)

      expect(result).toEqual({ a: 1, b: 3, c: 4 })
      expect(result).not.toBe(target)
    })
  })
})
