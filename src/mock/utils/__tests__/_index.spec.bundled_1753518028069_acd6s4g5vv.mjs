// src/mock/utils/__tests__/index.spec.ts
import { describe, it, expect, beforeEach } from "vitest";

// src/mock/utils/index.ts
import { faker } from "@faker-js/faker/locale/zh_CN";

// src/mock/config.ts
var mockConfig = {
  // API前缀
  apiPrefix: "/api",
  // 是否启用响应延迟
  delayEnabled: true,
  // 默认响应延迟（模拟网络延迟）
  defaultDelay: 200,
  // 响应延迟范围
  delayRange: {
    min: 100,
    max: 500
  },
  // 分页默认配置
  pagination: {
    defaultPage: 1,
    defaultSize: 10,
    maxSize: 100
  },
  // 日志配置
  logger: {
    enabled: true,
    request: true,
    response: true,
    error: true
  },
  // 数据持久化配置
  persistence: {
    enabled: true,
    storage: "localStorage",
    prefix: "hr_mock_",
    // 过期时间（小时）
    expires: 24
  },
  // 错误模拟配置
  errorSimulation: {
    enabled: false,
    // 错误率（0-1）
    rate: 0.1,
    // 错误码范围
    codes: [400, 401, 403, 404, 500]
  },
  // 默认响应格式
  responseFormat: {
    successCode: 200,
    errorCode: 500,
    messageKey: "message",
    dataKey: "data",
    codeKey: "code",
    successKey: "success"
  }
};
var config_default = mockConfig;

// src/mock/utils/response.ts
var ResponseBuilder = class {
  startTime;
  requestId;
  constructor(requestId) {
    this.startTime = Date.now();
    this.requestId = requestId;
  }
  /**
   * 构建成功响应
   */
  success(data, message = "\u64CD\u4F5C\u6210\u529F") {
    const { successCode, messageKey, dataKey, codeKey, successKey } = config_default.responseFormat;
    const response2 = {
      [codeKey]: successCode,
      [successKey]: true,
      [messageKey]: message,
      [dataKey]: data,
      meta: this.buildMeta()
    };
    return response2;
  }
  /**
   * 构建错误响应
   */
  error(message, code = 500, errors) {
    const { messageKey, dataKey, codeKey, successKey } = config_default.responseFormat;
    const response2 = {
      [codeKey]: code,
      [successKey]: false,
      [messageKey]: message,
      [dataKey]: null,
      errors,
      meta: this.buildMeta()
    };
    return response2;
  }
  /**
   * 构建分页响应
   */
  page(result, message = "\u83B7\u53D6\u6210\u529F") {
    return this.success(result, message);
  }
  /**
   * 构建列表响应
   */
  list(items, message = "\u83B7\u53D6\u6210\u529F") {
    return this.success(items, message);
  }
  /**
   * 构建单项响应
   */
  item(item, message) {
    if (item === null || item === void 0) {
      return this.error("\u6570\u636E\u4E0D\u5B58\u5728", 404);
    }
    return this.success(item, message || "\u83B7\u53D6\u6210\u529F");
  }
  /**
   * 构建创建响应
   */
  created(data, message = "\u521B\u5EFA\u6210\u529F") {
    const response2 = this.success(data, message);
    response2.code = 201;
    return response2;
  }
  /**
   * 构建更新响应
   */
  updated(data, message = "\u66F4\u65B0\u6210\u529F") {
    return this.success(data, message);
  }
  /**
   * 构建删除响应
   */
  deleted(message = "\u5220\u9664\u6210\u529F") {
    return this.success(null, message);
  }
  /**
   * 构建批量操作响应
   */
  batch(result, message = "\u6279\u91CF\u64CD\u4F5C\u5B8C\u6210") {
    return this.success(result, message);
  }
  /**
   * 构建文件上传响应
   */
  upload(fileInfo, message = "\u4E0A\u4F20\u6210\u529F") {
    return this.success(fileInfo, message);
  }
  /**
   * 构建导出响应
   */
  export(data, message = "\u5BFC\u51FA\u6210\u529F") {
    return this.success(data, message);
  }
  /**
   * 构建验证错误响应
   */
  validationError(errors, message = "\u9A8C\u8BC1\u5931\u8D25") {
    return this.error(message, 400, errors);
  }
  /**
   * 构建权限错误响应
   */
  unauthorized(message = "\u672A\u6388\u6743") {
    return this.error(message, 401);
  }
  /**
   * 构建禁止访问响应
   */
  forbidden(message = "\u7981\u6B62\u8BBF\u95EE") {
    return this.error(message, 403);
  }
  /**
   * 构建未找到响应
   */
  notFound(message = "\u8D44\u6E90\u4E0D\u5B58\u5728") {
    return this.error(message, 404);
  }
  /**
   * 构建服务器错误响应
   */
  serverError(message = "\u670D\u52A1\u5668\u9519\u8BEF", errors) {
    return this.error(message, 500, errors);
  }
  /**
   * 构建自定义响应
   */
  custom(data, code, message, success2 = true) {
    const { messageKey, dataKey, codeKey, successKey } = config_default.responseFormat;
    return {
      [codeKey]: code,
      [successKey]: success2,
      [messageKey]: message,
      [dataKey]: data,
      meta: this.buildMeta()
    };
  }
  /**
   * 构建响应元数据
   */
  buildMeta() {
    return {
      requestId: this.requestId,
      timestamp: Date.now(),
      duration: Date.now() - this.startTime,
      version: config_default.version ?? "1.0.0"
      // 提供默认版本号，避免TS错误
    };
  }
};
var response = {
  /**
   * 成功响应
   */
  success(data, message = "\u64CD\u4F5C\u6210\u529F") {
    return new ResponseBuilder().success(data, message);
  },
  /**
   * 错误响应
   */
  error(message, code = 500, errors) {
    return new ResponseBuilder().error(message, code, errors);
  },
  /**
   * 分页响应
   */
  page(result, message = "\u83B7\u53D6\u6210\u529F") {
    return new ResponseBuilder().page(result, message);
  },
  /**
   * 列表响应
   */
  list(items, message = "\u83B7\u53D6\u6210\u529F") {
    return new ResponseBuilder().list(items, message);
  },
  /**
   * 单项响应
   */
  item(item, message) {
    return new ResponseBuilder().item(item, message);
  },
  /**
   * 创建响应
   */
  created(data, message = "\u521B\u5EFA\u6210\u529F") {
    return new ResponseBuilder().created(data, message);
  },
  /**
   * 更新响应
   */
  updated(data, message = "\u66F4\u65B0\u6210\u529F") {
    return new ResponseBuilder().updated(data, message);
  },
  /**
   * 删除响应
   */
  deleted(message = "\u5220\u9664\u6210\u529F") {
    return new ResponseBuilder().deleted(message);
  },
  /**
   * 批量操作响应
   */
  batch(result, message = "\u6279\u91CF\u64CD\u4F5C\u5B8C\u6210") {
    return new ResponseBuilder().batch(result, message);
  },
  /**
   * 验证错误
   */
  validationError(errors, message = "\u9A8C\u8BC1\u5931\u8D25") {
    return new ResponseBuilder().validationError(errors, message);
  },
  /**
   * 未授权
   */
  unauthorized(message = "\u672A\u6388\u6743") {
    return new ResponseBuilder().unauthorized(message);
  },
  /**
   * 禁止访问
   */
  forbidden(message = "\u7981\u6B62\u8BBF\u95EE") {
    return new ResponseBuilder().forbidden(message);
  },
  /**
   * 未找到
   */
  notFound(message = "\u8D44\u6E90\u4E0D\u5B58\u5728") {
    return new ResponseBuilder().notFound(message);
  },
  /**
   * 服务器错误
   */
  serverError(message = "\u670D\u52A1\u5668\u9519\u8BEF", errors) {
    return new ResponseBuilder().serverError(message, errors);
  }
};

// src/mock/utils/errorHandler.ts
var MockError = class extends Error {
  /** 错误类型 */
  type;
  /** HTTP状态码 */
  statusCode;
  /** 错误详情 */
  details;
  /** 错误发生时间 */
  timestamp;
  constructor(message, type = "SYSTEM_ERROR" /* SYSTEM_ERROR */, statusCode = 500, details) {
    super(message);
    this.name = "MockError";
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = (/* @__PURE__ */ new Date()).toISOString();
  }
};
var ErrorCollector = class {
  errors = [];
  /**
   * 收集错误
   */
  collect(error2, context) {
    this.errors.push({
      error: error2,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      context
    });
  }
  /**
   * 获取所有错误
   */
  getErrors() {
    return [...this.errors];
  }
  /**
   * 清空错误
   */
  clear() {
    this.errors = [];
  }
  /**
   * 获取错误统计
   */
  getStatistics() {
    const stats = {};
    this.errors.forEach(({ error: error2 }) => {
      const type = error2 instanceof MockError ? error2.type : "UNKNOWN";
      stats[type] = (stats[type] || 0) + 1;
    });
    return stats;
  }
};
var globalErrorCollector = new ErrorCollector();

// src/mock/utils/index.ts
function generateId() {
  return faker.string.uuid();
}
function randomNumber(min, max) {
  return faker.number.int({ min, max });
}
function getRandomDelay() {
  const { min, max } = config_default.delayRange;
  return randomNumber(min, max);
}
function delay(ms) {
  const delayTime = ms || getRandomDelay();
  return new Promise((resolve) => setTimeout(resolve, delayTime));
}
function success(data, message = "\u64CD\u4F5C\u6210\u529F") {
  return response.success(data, message);
}
function error(message, code = 500, data = null) {
  return response.error(message, code, data);
}
function paginate(list, page, size) {
  const { defaultPage, defaultSize, maxSize } = config_default.pagination;
  const currentPage = Math.max(1, page || defaultPage);
  const pageSize = Math.min(Math.max(1, size || defaultSize), maxSize);
  const total = list.length;
  const pages = Math.ceil(total / pageSize);
  const start = (currentPage - 1) * pageSize;
  const end = start + pageSize;
  return {
    list: list.slice(start, end),
    total,
    page: currentPage,
    size: pageSize,
    pages
  };
}
var isBrowser = typeof window !== "undefined" && typeof window.localStorage !== "undefined";
var memoryStorage = {};
var storage = {
  /**
   * 获取存储key
   */
  getKey(key) {
    return `${config_default.persistence.prefix}${key}`;
  },
  /**
   * 保存数据
   */
  set(key, value) {
    if (!config_default.persistence.enabled) return;
    const storageKey = this.getKey(key);
    const data = {
      value,
      expires: Date.now() + config_default.persistence.expires * 3600 * 1e3,
      timestamp: Date.now()
    };
    try {
      if (!isBrowser) {
        memoryStorage[storageKey] = JSON.stringify(data);
        return;
      }
      if (config_default.persistence.storage === "localStorage") {
        localStorage.setItem(storageKey, JSON.stringify(data));
      } else {
        sessionStorage.setItem(storageKey, JSON.stringify(data));
      }
    } catch (e) {
      console.error("Mock\u6570\u636E\u5B58\u50A8\u5931\u8D25:", e);
    }
  },
  /**
   * 获取数据
   */
  get(key, defaultValue) {
    if (!config_default.persistence.enabled) return defaultValue;
    const storageKey = this.getKey(key);
    try {
      if (!isBrowser) {
        const item2 = memoryStorage[storageKey];
        if (!item2) return defaultValue;
        const data2 = JSON.parse(item2);
        if (data2.expires && data2.expires < Date.now()) {
          delete memoryStorage[storageKey];
          return defaultValue;
        }
        return data2.value;
      }
      const storage2 = config_default.persistence.storage === "localStorage" ? localStorage : sessionStorage;
      const item = storage2.getItem(storageKey);
      if (!item) return defaultValue;
      const data = JSON.parse(item);
      if (data.expires && data.expires < Date.now()) {
        storage2.removeItem(storageKey);
        return defaultValue;
      }
      return data.value;
    } catch (e) {
      console.error("Mock\u6570\u636E\u8BFB\u53D6\u5931\u8D25:", e);
      return defaultValue;
    }
  },
  /**
   * 删除数据
   */
  remove(key) {
    if (!config_default.persistence.enabled) return;
    const storageKey = this.getKey(key);
    try {
      if (!isBrowser) {
        delete memoryStorage[storageKey];
        return;
      }
      if (config_default.persistence.storage === "localStorage") {
        localStorage.removeItem(storageKey);
      } else {
        sessionStorage.removeItem(storageKey);
      }
    } catch (e) {
      console.error("Mock\u6570\u636E\u5220\u9664\u5931\u8D25:", e);
    }
  },
  /**
   * 清空所有Mock数据
   */
  clear() {
    if (!config_default.persistence.enabled) return;
    const prefix = config_default.persistence.prefix;
    if (!isBrowser) {
      Object.keys(memoryStorage).forEach((key) => {
        if (key.startsWith(prefix)) {
          delete memoryStorage[key];
        }
      });
      return;
    }
    const storage2 = config_default.persistence.storage === "localStorage" ? localStorage : sessionStorage;
    const keys = Object.keys(storage2);
    keys.forEach((key) => {
      if (key.startsWith(prefix)) {
        storage2.removeItem(key);
      }
    });
  }
};
function generateChineseName() {
  return faker.person.fullName();
}
function generatePhoneNumber() {
  const prefixes = [
    "130",
    "131",
    "132",
    "133",
    "134",
    "135",
    "136",
    "137",
    "138",
    "139",
    "150",
    "151",
    "152",
    "153",
    "155",
    "156",
    "157",
    "158",
    "159",
    "180",
    "181",
    "182",
    "183",
    "184",
    "185",
    "186",
    "187",
    "188",
    "189"
  ];
  const prefix = faker.helpers.arrayElement(prefixes);
  const suffix = faker.string.numeric(8);
  return prefix + suffix;
}
function generateEmail() {
  return faker.internet.email();
}
function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj));
}
function mergeObjects(target, source) {
  return { ...target, ...source };
}

// src/mock/utils/__tests__/index.spec.ts
describe("Mock\u5DE5\u5177\u51FD\u6570\u6D4B\u8BD5", () => {
  describe("generateId", () => {
    it("\u5E94\u8BE5\u751F\u6210\u6709\u6548\u7684UUID", () => {
      const id = generateId();
      expect(id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);
    });
    it("\u6BCF\u6B21\u751F\u6210\u7684ID\u5E94\u8BE5\u4E0D\u540C", () => {
      const id1 = generateId();
      const id2 = generateId();
      expect(id1).not.toBe(id2);
    });
  });
  describe("randomNumber", () => {
    it("\u5E94\u8BE5\u751F\u6210\u6307\u5B9A\u8303\u56F4\u5185\u7684\u968F\u673A\u6570", () => {
      const min = 10;
      const max = 20;
      const num = randomNumber(min, max);
      expect(num).toBeGreaterThanOrEqual(min);
      expect(num).toBeLessThanOrEqual(max);
    });
  });
  describe("delay", () => {
    it("\u5E94\u8BE5\u5EF6\u8FDF\u6307\u5B9A\u65F6\u95F4", async () => {
      const start = Date.now();
      await delay(100);
      const end = Date.now();
      expect(end - start).toBeGreaterThanOrEqual(90);
    });
  });
  describe("success", () => {
    it("\u5E94\u8BE5\u8FD4\u56DE\u6210\u529F\u54CD\u5E94\u683C\u5F0F", () => {
      const data = { id: 1, name: "test" };
      const result = success(data);
      expect(result.code).toBe(200);
      expect(result.success).toBe(true);
      expect(result.message).toBe("\u64CD\u4F5C\u6210\u529F");
      expect(result.data).toEqual(data);
      expect(result.timestamp).toBeDefined();
    });
    it("\u5E94\u8BE5\u652F\u6301\u81EA\u5B9A\u4E49\u6D88\u606F", () => {
      const result = success({}, "\u81EA\u5B9A\u4E49\u6D88\u606F");
      expect(result.message).toBe("\u81EA\u5B9A\u4E49\u6D88\u606F");
    });
  });
  describe("error", () => {
    it("\u5E94\u8BE5\u8FD4\u56DE\u9519\u8BEF\u54CD\u5E94\u683C\u5F0F", () => {
      const result = error("\u9519\u8BEF\u4FE1\u606F");
      expect(result.code).toBe(500);
      expect(result.success).toBe(false);
      expect(result.message).toBe("\u9519\u8BEF\u4FE1\u606F");
      expect(result.data).toBeNull();
      expect(result.timestamp).toBeDefined();
    });
    it("\u5E94\u8BE5\u652F\u6301\u81EA\u5B9A\u4E49\u9519\u8BEF\u7801", () => {
      const result = error("\u672A\u6388\u6743", 401);
      expect(result.code).toBe(401);
    });
  });
  describe("paginate", () => {
    const testData = Array.from({ length: 25 }, (_, i) => ({ id: i + 1, name: `Item ${i + 1}` }));
    it("\u5E94\u8BE5\u6B63\u786E\u5206\u9875\u6570\u636E", () => {
      const result = paginate(testData, 2, 10);
      expect(result.list.length).toBe(10);
      expect(result.list[0].id).toBe(11);
      expect(result.total).toBe(25);
      expect(result.page).toBe(2);
      expect(result.size).toBe(10);
      expect(result.pages).toBe(3);
    });
    it("\u5E94\u8BE5\u5904\u7406\u6700\u540E\u4E00\u9875", () => {
      const result = paginate(testData, 3, 10);
      expect(result.list.length).toBe(5);
      expect(result.list[0].id).toBe(21);
    });
    it("\u5E94\u8BE5\u5904\u7406\u65E0\u6548\u53C2\u6570", () => {
      const result = paginate(testData, -1, 0);
      expect(result.page).toBe(1);
      expect(result.size).toBeGreaterThan(0);
    });
  });
  describe("storage", () => {
    beforeEach(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    it("\u5E94\u8BE5\u4FDD\u5B58\u548C\u83B7\u53D6\u6570\u636E", () => {
      const testData = { id: 1, name: "test" };
      storage.set("test", testData);
      const retrieved = storage.get("test");
      expect(retrieved).toEqual(testData);
    });
    it("\u5E94\u8BE5\u8FD4\u56DE\u9ED8\u8BA4\u503C\u5F53\u6570\u636E\u4E0D\u5B58\u5728\u65F6", () => {
      const defaultValue = { id: 0, name: "default" };
      const result = storage.get("nonexistent", defaultValue);
      expect(result).toEqual(defaultValue);
    });
    it("\u5E94\u8BE5\u5220\u9664\u6570\u636E", () => {
      storage.set("test", { id: 1 });
      storage.remove("test");
      const result = storage.get("test");
      expect(result).toBeUndefined();
    });
    it("\u5E94\u8BE5\u6E05\u7A7A\u6240\u6709Mock\u6570\u636E", () => {
      storage.set("test1", { id: 1 });
      storage.set("test2", { id: 2 });
      storage.clear();
      expect(storage.get("test1")).toBeUndefined();
      expect(storage.get("test2")).toBeUndefined();
    });
  });
  describe("\u6570\u636E\u751F\u6210\u51FD\u6570", () => {
    it("\u5E94\u8BE5\u751F\u6210\u6709\u6548\u7684\u4E2D\u6587\u59D3\u540D", () => {
      const name = generateChineseName();
      expect(name).toBeTruthy();
      expect(name.length).toBeGreaterThan(0);
    });
    it("\u5E94\u8BE5\u751F\u6210\u6709\u6548\u7684\u624B\u673A\u53F7", () => {
      const phone = generatePhoneNumber();
      expect(phone).toMatch(/^1\d{10}$/);
    });
    it("\u5E94\u8BE5\u751F\u6210\u6709\u6548\u7684\u90AE\u7BB1", () => {
      const email = generateEmail();
      expect(email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
    });
  });
  describe("\u5DE5\u5177\u51FD\u6570", () => {
    it("deepClone\u5E94\u8BE5\u6DF1\u62F7\u8D1D\u5BF9\u8C61", () => {
      const original = { a: 1, b: { c: 2 } };
      const cloned = deepClone(original);
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.b).not.toBe(original.b);
    });
    it("mergeObjects\u5E94\u8BE5\u6B63\u786E\u5408\u5E76\u5BF9\u8C61", () => {
      const target = { a: 1, b: 2 };
      const source = { b: 3, c: 4 };
      const result = mergeObjects(target, source);
      expect(result).toEqual({ a: 1, b: 3, c: 4 });
      expect(result).not.toBe(target);
    });
  });
});
//# sourceMappingURL=data:application/json;base64,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
