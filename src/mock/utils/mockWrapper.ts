/**
 * @name Mock接口包装器
 * @description 为Mock接口提供统一的错误处理、日志记录和延迟模拟
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import { handleError, MockError, globalErrorCollector } from './errorHandler'
import { logger, delay, getRandomDelay } from './index'
import mockConfig from '../config'
// Force TypeScript refresh - config includes delayEnabled property

/**
 * Mock响应函数类型
 */
type MockResponseFunction = (params: any) => any | Promise<any>

/**
 * Mock包装器配置
 */
interface MockWrapperConfig {
  /** 是否启用延迟 */
  enableDelay?: boolean
  /** 自定义延迟时间(ms) */
  customDelay?: number
  /** 是否记录日志 */
  enableLogging?: boolean
  /** 是否收集错误 */
  collectErrors?: boolean
  /** 自定义错误消息 */
  errorMessage?: string
}

/**
 * 包装Mock响应函数，添加错误处理、日志和延迟
 */
export function wrapMockResponse(
  responseFn: MockResponseFunction,
  config: MockWrapperConfig = {}
): MockResponseFunction {
  return async (params: any) => {
    const {
      enableDelay = (mockConfig as any).delayEnabled ?? true, // 使用配置的延迟开关，类型断言解决TS问题
      customDelay,
      enableLogging = mockConfig.logger.enabled,
      collectErrors = true,
      errorMessage
    } = config

    const startTime = Date.now()
    const { url = '', method = '', query, body, headers } = params

    try {
      // 记录请求日志
      if (enableLogging) {
        logger.request(url, method, { query, body, headers })
      }

      // 模拟延迟
      if (enableDelay) {
        const delayTime = customDelay || getRandomDelay()
        await delay(delayTime)
      }

      // 执行实际的响应函数
      const response = await responseFn(params)

      // 记录响应日志
      if (enableLogging) {
        const duration = Date.now() - startTime
        logger.response(url, response, duration)
      }

      return response
    } catch (error) {
      // 收集错误
      if (collectErrors && (error instanceof Error || error instanceof MockError)) {
        globalErrorCollector.collect(error, {
          url,
          method,
          params: { query, body }
        })
      }

      // 记录错误日志
      if (enableLogging) {
        logger.error(`${method} ${url}`, error)
      }

      // 返回错误响应
      return handleError(error, {
        logError: false, // 已经记录过了
        customMessage: errorMessage
      })
    }
  }
}

/**
 * 包装整个Mock方法
 */
export function wrapMockMethod(mockMethod: MockMethod, config?: MockWrapperConfig): MockMethod {
  const originalResponse = mockMethod.response

  return {
    ...mockMethod,
    response:
      typeof originalResponse === 'function'
        ? wrapMockResponse(originalResponse, config)
        : originalResponse
  }
}

/**
 * 批量包装Mock方法
 */
export function wrapMockMethods(
  mockMethods: MockMethod[],
  config?: MockWrapperConfig
): MockMethod[] {
  return mockMethods.map(method => wrapMockMethod(method, config))
}

/**
 * 创建带错误处理的Mock方法
 */
export function createSafeMockMethod(
  url: string,
  method: 'get' | 'post' | 'put' | 'delete' | 'patch',
  responseFn: MockResponseFunction,
  config?: MockWrapperConfig
): MockMethod {
  return {
    url,
    method,
    response: wrapMockResponse(responseFn, config)
  }
}

/**
 * 创建CRUD Mock方法集合
 */
export function createCRUDMockMethods(
  resourcePath: string,
  handlers: {
    list?: MockResponseFunction
    get?: MockResponseFunction
    create?: MockResponseFunction
    update?: MockResponseFunction
    delete?: MockResponseFunction
    batchDelete?: MockResponseFunction
    batchUpdate?: MockResponseFunction
  },
  config?: MockWrapperConfig
): MockMethod[] {
  const methods: MockMethod[] = []

  // 列表查询
  if (handlers.list) {
    methods.push(createSafeMockMethod(`${resourcePath}`, 'get', handlers.list, config))
  }

  // 获取单个资源
  if (handlers.get) {
    methods.push(createSafeMockMethod(`${resourcePath}/:id`, 'get', handlers.get, config))
  }

  // 创建资源
  if (handlers.create) {
    methods.push(createSafeMockMethod(`${resourcePath}`, 'post', handlers.create, config))
  }

  // 更新资源
  if (handlers.update) {
    methods.push(createSafeMockMethod(`${resourcePath}/:id`, 'put', handlers.update, config))
  }

  // 删除资源
  if (handlers.delete) {
    methods.push(createSafeMockMethod(`${resourcePath}/:id`, 'delete', handlers.delete, config))
  }

  // 批量删除
  if (handlers.batchDelete) {
    methods.push(
      createSafeMockMethod(`${resourcePath}/batch`, 'delete', handlers.batchDelete, config)
    )
  }

  // 批量更新
  if (handlers.batchUpdate) {
    methods.push(createSafeMockMethod(`${resourcePath}/batch`, 'put', handlers.batchUpdate, config))
  }

  return methods
}

/**
 * 验证装饰器 - 用于验证请求参数
 */
export function withValidation<T extends Record<string, any>>(
  validator: (params: T) => void | string | MockError
): (responseFn: MockResponseFunction) => MockResponseFunction {
  return (responseFn: MockResponseFunction) => {
    return async (params: any) => {
      try {
        const validationResult = validator(params)

        if (typeof validationResult === 'string') {
          throw new MockError(validationResult, MockError.prototype.type, 400)
        }

        if (validationResult instanceof MockError) {
          throw validationResult
        }
      } catch (error) {
        if (error instanceof MockError) {
          throw error
        }
        throw new MockError('参数验证失败', MockError.prototype.type, 400, { originalError: error })
      }

      return responseFn(params)
    }
  }
}

/**
 * 权限装饰器 - 用于检查权限
 */
export function withPermission(
  requiredPermission: string | ((params: any) => boolean)
): (responseFn: MockResponseFunction) => MockResponseFunction {
  return (responseFn: MockResponseFunction) => {
    return async (params: any) => {
      const hasPermission =
        typeof requiredPermission === 'function'
          ? requiredPermission(params)
          : checkPermission(params, requiredPermission)

      if (!hasPermission) {
        throw new MockError('无权限执行此操作', MockError.prototype.type, 403)
      }

      return responseFn(params)
    }
  }
}

/**
 * 简单的权限检查函数（实际项目中应该更复杂）
 */
function checkPermission(params: any, permission: string): boolean {
  // 这里只是示例，实际应该从用户会话中获取权限
  const userPermissions = params.headers?.['x-user-permissions']?.split(',') || []
  return userPermissions.includes(permission) || userPermissions.includes('*')
}
