/**
 * @name 统一响应格式处理
 * @description 提供一致的API响应格式，支持成功、错误、分页等多种响应类型
 * <AUTHOR>
 * @since 2025-01-24
 */

import mockConfig from '../config'
import type { PaginatedResult } from '../service/EnhancedMockService'

/**
 * 响应元数据
 */
export interface ResponseMeta {
  /** 请求ID */
  requestId?: string
  /** 响应时间戳 */
  timestamp: number
  /** 处理耗时(ms) */
  duration?: number
  /** API版本 */
  version?: string
}

/**
 * 基础响应结构
 */
export interface BaseResponse<T = any> {
  [key: string]: any // 支持动态键名
  data?: T
  meta?: ResponseMeta
}

/**
 * 成功响应结构
 */
export interface SuccessResponse<T = any> extends BaseResponse<T> {
  success: true
  code: number
  message: string
  data: T
}

/**
 * 错误响应结构
 */
export interface ErrorResponse extends BaseResponse<null> {
  success: false
  code: number
  message: string
  errors?: any
}

/**
 * 分页响应数据结构
 */
export interface PageData<T> {
  list: T[]
  total: number
  page: number
  size: number
  pages: number
}

/**
 * 响应构建器类
 */
export class ResponseBuilder {
  private startTime: number
  private requestId?: string

  constructor(requestId?: string) {
    this.startTime = Date.now()
    this.requestId = requestId
  }

  /**
   * 构建成功响应
   */
  success<T>(data: T, message = '操作成功'): SuccessResponse<T> {
    const { successCode, messageKey, dataKey, codeKey, successKey } = mockConfig.responseFormat

    const response = {
      [codeKey]: successCode,
      [successKey]: true,
      [messageKey]: message,
      [dataKey]: data,
      meta: this.buildMeta()
    } as any

    return response
  }

  /**
   * 构建错误响应
   */
  error(message: string, code = 500, errors?: any): ErrorResponse {
    const { messageKey, dataKey, codeKey, successKey } = mockConfig.responseFormat

    const response = {
      [codeKey]: code,
      [successKey]: false,
      [messageKey]: message,
      [dataKey]: null,
      errors,
      meta: this.buildMeta()
    } as any

    return response
  }

  /**
   * 构建分页响应
   */
  page<T>(result: PaginatedResult<T>, message = '获取成功'): SuccessResponse<PageData<T>> {
    return this.success(result, message)
  }

  /**
   * 构建列表响应
   */
  list<T>(items: T[], message = '获取成功'): SuccessResponse<T[]> {
    return this.success(items, message)
  }

  /**
   * 构建单项响应
   */
  item<T>(item: T | null, message?: string): SuccessResponse<T> | ErrorResponse {
    if (item === null || item === undefined) {
      return this.error('数据不存在', 404)
    }
    return this.success(item, message || '获取成功')
  }

  /**
   * 构建创建响应
   */
  created<T>(data: T, message = '创建成功'): SuccessResponse<T> {
    const response = this.success(data, message)
    response.code = 201
    return response
  }

  /**
   * 构建更新响应
   */
  updated<T>(data: T, message = '更新成功'): SuccessResponse<T> {
    return this.success(data, message)
  }

  /**
   * 构建删除响应
   */
  deleted(message = '删除成功'): SuccessResponse<null> {
    return this.success(null, message)
  }

  /**
   * 构建批量操作响应
   */
  batch(
    result: { success: number; failed: number; errors?: any[] },
    message = '批量操作完成'
  ): SuccessResponse<typeof result> {
    return this.success(result, message)
  }

  /**
   * 构建文件上传响应
   */
  upload(
    fileInfo: { url: string; filename: string; size: number },
    message = '上传成功'
  ): SuccessResponse<typeof fileInfo> {
    return this.success(fileInfo, message)
  }

  /**
   * 构建导出响应
   */
  export(
    data: { url?: string; content?: any; filename: string },
    message = '导出成功'
  ): SuccessResponse<typeof data> {
    return this.success(data, message)
  }

  /**
   * 构建验证错误响应
   */
  validationError(
    errors: Array<{ field: string; message: string }>,
    message = '验证失败'
  ): ErrorResponse {
    return this.error(message, 400, errors)
  }

  /**
   * 构建权限错误响应
   */
  unauthorized(message = '未授权'): ErrorResponse {
    return this.error(message, 401)
  }

  /**
   * 构建禁止访问响应
   */
  forbidden(message = '禁止访问'): ErrorResponse {
    return this.error(message, 403)
  }

  /**
   * 构建未找到响应
   */
  notFound(message = '资源不存在'): ErrorResponse {
    return this.error(message, 404)
  }

  /**
   * 构建服务器错误响应
   */
  serverError(message = '服务器错误', errors?: any): ErrorResponse {
    return this.error(message, 500, errors)
  }

  /**
   * 构建自定义响应
   */
  custom<T>(data: T, code: number, message: string, success = true): BaseResponse<T> {
    const { messageKey, dataKey, codeKey, successKey } = mockConfig.responseFormat

    return {
      [codeKey]: code,
      [successKey]: success,
      [messageKey]: message,
      [dataKey]: data,
      meta: this.buildMeta()
    } as any
  }

  /**
   * 构建响应元数据
   */
  private buildMeta(): ResponseMeta {
    return {
      requestId: this.requestId,
      timestamp: Date.now(),
      duration: Date.now() - this.startTime,
      version: (mockConfig as any).version ?? '1.0.0' // 提供默认版本号，避免TS错误
    }
  }
}

/**
 * 快捷响应函数
 */
export const response = {
  /**
   * 成功响应
   */
  success<T>(data: T, message = '操作成功'): SuccessResponse<T> {
    return new ResponseBuilder().success(data, message)
  },

  /**
   * 错误响应
   */
  error(message: string, code = 500, errors?: any): ErrorResponse {
    return new ResponseBuilder().error(message, code, errors)
  },

  /**
   * 分页响应
   */
  page<T>(result: PaginatedResult<T>, message = '获取成功'): SuccessResponse<PageData<T>> {
    return new ResponseBuilder().page(result, message)
  },

  /**
   * 列表响应
   */
  list<T>(items: T[], message = '获取成功'): SuccessResponse<T[]> {
    return new ResponseBuilder().list(items, message)
  },

  /**
   * 单项响应
   */
  item<T>(item: T | null, message?: string): SuccessResponse<T> | ErrorResponse {
    return new ResponseBuilder().item(item, message)
  },

  /**
   * 创建响应
   */
  created<T>(data: T, message = '创建成功'): SuccessResponse<T> {
    return new ResponseBuilder().created(data, message)
  },

  /**
   * 更新响应
   */
  updated<T>(data: T, message = '更新成功'): SuccessResponse<T> {
    return new ResponseBuilder().updated(data, message)
  },

  /**
   * 删除响应
   */
  deleted(message = '删除成功'): SuccessResponse<null> {
    return new ResponseBuilder().deleted(message)
  },

  /**
   * 批量操作响应
   */
  batch(
    result: { success: number; failed: number; errors?: any[] },
    message = '批量操作完成'
  ): SuccessResponse<typeof result> {
    return new ResponseBuilder().batch(result, message)
  },

  /**
   * 验证错误
   */
  validationError(
    errors: Array<{ field: string; message: string }>,
    message = '验证失败'
  ): ErrorResponse {
    return new ResponseBuilder().validationError(errors, message)
  },

  /**
   * 未授权
   */
  unauthorized(message = '未授权'): ErrorResponse {
    return new ResponseBuilder().unauthorized(message)
  },

  /**
   * 禁止访问
   */
  forbidden(message = '禁止访问'): ErrorResponse {
    return new ResponseBuilder().forbidden(message)
  },

  /**
   * 未找到
   */
  notFound(message = '资源不存在'): ErrorResponse {
    return new ResponseBuilder().notFound(message)
  },

  /**
   * 服务器错误
   */
  serverError(message = '服务器错误', errors?: any): ErrorResponse {
    return new ResponseBuilder().serverError(message, errors)
  }
}

/**
 * 响应状态码常量
 */
export const StatusCodes = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const

/**
 * 响应消息常量
 */
export const Messages = {
  SUCCESS: '操作成功',
  CREATED: '创建成功',
  UPDATED: '更新成功',
  DELETED: '删除成功',
  NOT_FOUND: '数据不存在',
  UNAUTHORIZED: '未授权',
  FORBIDDEN: '无权限',
  VALIDATION_ERROR: '验证失败',
  SERVER_ERROR: '服务器错误'
} as const
