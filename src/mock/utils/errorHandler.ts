/**
 * @name Mock错误处理工具
 * @description 统一的错误处理机制，包含错误类型定义、错误日志和错误响应
 * <AUTHOR>
 * @since 2025-01-24
 */

// 避免循环导入，直接定义logger和errorResponse
const logger = {
  error(message: string, data?: any): void {
    console.group(`🔴 [Mock Error] ${message}`)
    if (data) {
      console.error('Error:', data)
    }
    console.groupEnd()
  }
}

// 直接定义 errorResponse 函数以避免导入问题
const errorResponse = (message: string, code = 500, errors?: any): any => {
  const { messageKey, dataKey, codeKey, successKey } = {
    codeKey: 'code',
    successKey: 'success',
    messageKey: 'message',
    dataKey: 'data'
  }

  return {
    [codeKey]: code,
    [successKey]: false,
    [messageKey]: message,
    [dataKey]: null,
    errors,
    meta: {
      timestamp: Date.now()
    }
  }
}

/**
 * Mock错误类型枚举
 */
export enum MockErrorType {
  /** 数据验证错误 */
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  /** 业务逻辑错误 */
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  /** 数据不存在错误 */
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  /** 权限错误 */
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  /** 系统错误 */
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR'
}

/**
 * Mock错误类
 */
export class MockError extends Error {
  /** 错误类型 */
  type: MockErrorType
  /** HTTP状态码 */
  statusCode: number
  /** 错误详情 */
  details?: Record<string, unknown>
  /** 错误发生时间 */
  timestamp: string

  constructor(
    message: string,
    type: MockErrorType = MockErrorType.SYSTEM_ERROR,
    statusCode: number = 500,
    details?: Record<string, unknown>
  ) {
    super(message)
    this.name = 'MockError'
    this.type = type
    this.statusCode = statusCode
    this.details = details
    this.timestamp = new Date().toISOString()
  }
}

/**
 * 错误处理配置
 */
interface ErrorHandlerConfig {
  /** 是否记录错误日志 */
  logError?: boolean
  /** 是否在响应中包含错误详情（仅开发环境） */
  includeDetails?: boolean
  /** 自定义错误消息 */
  customMessage?: string
}

/**
 * 统一错误处理函数
 */
export function handleError(
  error: unknown,
  config: ErrorHandlerConfig = {}
): ReturnType<typeof errorResponse> {
  const {
    logError = true,
    includeDetails = process.env.NODE_ENV === 'development',
    customMessage
  } = config

  // 处理MockError类型
  if (error instanceof MockError) {
    if (logError) {
      logger.error('MockError:', {
        type: error.type,
        message: error.message,
        statusCode: error.statusCode,
        details: error.details,
        timestamp: error.timestamp,
        stack: error.stack
      })
    }

    return errorResponse(
      customMessage || error.message,
      error.statusCode,
      includeDetails ? error.details : undefined
    )
  }

  // 处理标准Error类型
  if (error instanceof Error) {
    if (logError) {
      logger.error('Error:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      })
    }

    // 根据错误消息判断错误类型
    let statusCode = 500
    let errorType = MockErrorType.SYSTEM_ERROR

    if (error.message.includes('not found') || error.message.includes('不存在')) {
      statusCode = 404
      errorType = MockErrorType.NOT_FOUND_ERROR
    } else if (error.message.includes('unauthorized') || error.message.includes('权限')) {
      statusCode = 403
      errorType = MockErrorType.PERMISSION_ERROR
    } else if (error.message.includes('validation') || error.message.includes('验证')) {
      statusCode = 400
      errorType = MockErrorType.VALIDATION_ERROR
    }

    return errorResponse(
      customMessage || error.message,
      statusCode,
      includeDetails ? { type: errorType, originalError: error.message } : undefined
    )
  }

  // 处理未知错误类型
  if (logError) {
    logger.error('Unknown error:', error)
  }

  return errorResponse(
    customMessage || '服务器内部错误',
    500,
    includeDetails ? { originalError: String(error) } : undefined
  )
}

/**
 * 异步错误包装器
 * 自动捕获异步函数中的错误并处理
 */
export function asyncErrorWrapper<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  config?: ErrorHandlerConfig
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args)
    } catch (error) {
      return handleError(error, config)
    }
  }) as T
}

/**
 * 验证错误辅助函数
 */
export function validationError(field: string, message: string, value?: unknown): MockError {
  return new MockError(`${field}: ${message}`, MockErrorType.VALIDATION_ERROR, 400, {
    field,
    value
  })
}

/**
 * 业务错误辅助函数
 */
export function businessError(message: string, details?: Record<string, unknown>): MockError {
  return new MockError(message, MockErrorType.BUSINESS_ERROR, 400, details)
}

/**
 * 未找到错误辅助函数
 */
export function notFoundError(resource: string, id?: string | number): MockError {
  return new MockError(
    `${resource}不存在${id ? `（ID: ${id}）` : ''}`,
    MockErrorType.NOT_FOUND_ERROR,
    404,
    { resource, id }
  )
}

/**
 * 权限错误辅助函数
 */
export function permissionError(action: string, resource?: string): MockError {
  return new MockError(
    `无权限执行${action}${resource ? `（资源: ${resource}）` : ''}`,
    MockErrorType.PERMISSION_ERROR,
    403,
    { action, resource }
  )
}

/**
 * 错误日志收集器
 */
export class ErrorCollector {
  private errors: Array<{
    error: MockError | Error
    timestamp: string
    context?: Record<string, unknown>
  }> = []

  /**
   * 收集错误
   */
  collect(error: MockError | Error, context?: Record<string, unknown>): void {
    this.errors.push({
      error,
      timestamp: new Date().toISOString(),
      context
    })
  }

  /**
   * 获取所有错误
   */
  getErrors(): typeof this.errors {
    return [...this.errors]
  }

  /**
   * 清空错误
   */
  clear(): void {
    this.errors = []
  }

  /**
   * 获取错误统计
   */
  getStatistics(): Record<string, number> {
    const stats: Record<string, number> = {}

    this.errors.forEach(({ error }) => {
      const type = error instanceof MockError ? error.type : 'UNKNOWN'
      stats[type] = (stats[type] || 0) + 1
    })

    return stats
  }
}

// 全局错误收集器实例
export const globalErrorCollector = new ErrorCollector()
