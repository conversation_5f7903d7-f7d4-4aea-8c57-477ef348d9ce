/**
 * @name 数据处理辅助函数
 * @description 提供通用的数据过滤、排序、搜索等工具函数
 * <AUTHOR>
 * @since 2025-01-24
 */

/**
 * 比较运算符类型
 */
export type CompareOperator =
  | '='
  | '!='
  | '>'
  | '<'
  | '>='
  | '<='
  | 'in'
  | 'not-in'
  | 'like'
  | 'between'

/**
 * 过滤条件接口
 */
export interface FilterCondition {
  field: string
  operator: CompareOperator
  value: any
}

/**
 * 高级过滤选项
 */
export interface AdvancedFilterOptions {
  conditions: FilterCondition[]
  logic?: 'and' | 'or'
}

/**
 * 排序选项
 */
export interface SortOption {
  field: string
  order: 'asc' | 'desc'
}

/**
 * 搜索选项
 */
export interface SearchOptions {
  keyword: string
  fields: string[]
  caseSensitive?: boolean
  exactMatch?: boolean
}

/**
 * 通用比较函数
 */
export function compare(value1: any, operator: CompareOperator, value2: any): boolean {
  switch (operator) {
    case '=':
      return value1 === value2
    case '!=':
      return value1 !== value2
    case '>':
      return value1 > value2
    case '<':
      return value1 < value2
    case '>=':
      return value1 >= value2
    case '<=':
      return value1 <= value2
    case 'in':
      return Array.isArray(value2) && value2.includes(value1)
    case 'not-in':
      return Array.isArray(value2) && !value2.includes(value1)
    case 'like':
      const pattern = String(value2).toLowerCase()
      const text = String(value1).toLowerCase()
      return text.includes(pattern)
    case 'between':
      if (Array.isArray(value2) && value2.length === 2) {
        return value1 >= value2[0] && value1 <= value2[1]
      }
      return false
    default:
      return false
  }
}

/**
 * 获取嵌套对象的值
 * @param obj 对象
 * @param path 属性路径，支持点号分隔的嵌套路径
 */
export function getNestedValue(obj: any, path: string): any {
  const keys = path.split('.')
  let value = obj

  for (const key of keys) {
    if (value == null) return undefined
    value = value[key]
  }

  return value
}

/**
 * 设置嵌套对象的值
 */
export function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.')
  const lastKey = keys.pop()!

  let current = obj
  for (const key of keys) {
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }

  current[lastKey] = value
}

/**
 * 单条件过滤
 */
export function filterByCondition<T>(items: T[], condition: FilterCondition): T[] {
  return items.filter(item => {
    const value = getNestedValue(item, condition.field)
    return compare(value, condition.operator, condition.value)
  })
}

/**
 * 多条件过滤
 */
export function filterByConditions<T>(items: T[], options: AdvancedFilterOptions): T[] {
  const { conditions, logic = 'and' } = options

  return items.filter(item => {
    if (logic === 'and') {
      return conditions.every(condition => {
        const value = getNestedValue(item, condition.field)
        return compare(value, condition.operator, condition.value)
      })
    } else {
      return conditions.some(condition => {
        const value = getNestedValue(item, condition.field)
        return compare(value, condition.operator, condition.value)
      })
    }
  })
}

/**
 * 全文搜索
 */
export function searchItems<T>(items: T[], options: SearchOptions): T[] {
  const { keyword, fields, caseSensitive = false, exactMatch = false } = options

  if (!keyword || keyword.trim() === '') {
    return items
  }

  const searchKeyword = caseSensitive ? keyword.trim() : keyword.trim().toLowerCase()

  return items.filter(item => {
    return fields.some(field => {
      const value = getNestedValue(item, field)
      if (value == null) return false

      const textValue = caseSensitive ? String(value) : String(value).toLowerCase()

      if (exactMatch) {
        return textValue === searchKeyword
      } else {
        return textValue.includes(searchKeyword)
      }
    })
  })
}

/**
 * 多字段排序
 */
export function sortByFields<T>(items: T[], sortOptions: SortOption[]): T[] {
  if (!sortOptions || sortOptions.length === 0) {
    return items
  }

  return [...items].sort((a, b) => {
    for (const option of sortOptions) {
      const aVal = getNestedValue(a, option.field)
      const bVal = getNestedValue(b, option.field)

      // 处理null/undefined
      if (aVal == null && bVal == null) continue
      if (aVal == null) return 1
      if (bVal == null) return -1

      let comparison = 0

      // 数字比较
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        comparison = aVal - bVal
      }
      // 日期比较
      else if (aVal instanceof Date && bVal instanceof Date) {
        comparison = aVal.getTime() - bVal.getTime()
      }
      // 字符串比较
      else {
        comparison = String(aVal).localeCompare(String(bVal), 'zh-CN')
      }

      if (comparison !== 0) {
        return option.order === 'asc' ? comparison : -comparison
      }
    }

    return 0
  })
}

/**
 * 分组
 */
export function groupBy<T>(items: T[], keyGetter: (item: T) => string): Record<string, T[]> {
  const groups: Record<string, T[]> = {}

  for (const item of items) {
    const key = keyGetter(item)
    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(item)
  }

  return groups
}

/**
 * 聚合统计
 */
export interface AggregateOptions {
  groupBy?: string
  aggregations: Array<{
    field: string
    operation: 'sum' | 'avg' | 'min' | 'max' | 'count' | 'distinct'
    alias?: string
  }>
}

export function aggregate<T>(items: T[], options: AggregateOptions): any[] {
  if (options.groupBy) {
    const groups = groupBy(items, item => String(getNestedValue(item, options.groupBy!)))

    return Object.entries(groups).map(([key, groupItems]) => {
      const result: any = { [options.groupBy!]: key }

      for (const agg of options.aggregations) {
        const alias = agg.alias || `${agg.operation}_${agg.field}`
        result[alias] = calculateAggregation(groupItems, agg.field, agg.operation)
      }

      return result
    })
  } else {
    const result: any = {}

    for (const agg of options.aggregations) {
      const alias = agg.alias || `${agg.operation}_${agg.field}`
      result[alias] = calculateAggregation(items, agg.field, agg.operation)
    }

    return [result]
  }
}

/**
 * 计算聚合值
 */
function calculateAggregation<T>(
  items: T[],
  field: string,
  operation: 'sum' | 'avg' | 'min' | 'max' | 'count' | 'distinct'
): number {
  const values = items.map(item => getNestedValue(item, field)).filter(v => v != null)

  switch (operation) {
    case 'sum':
      return values.reduce((sum, val) => sum + Number(val), 0)
    case 'avg':
      if (values.length === 0) return 0
      return values.reduce((sum, val) => sum + Number(val), 0) / values.length
    case 'min':
      return values.length > 0 ? Math.min(...values.map(Number)) : 0
    case 'max':
      return values.length > 0 ? Math.max(...values.map(Number)) : 0
    case 'count':
      return values.length
    case 'distinct':
      return new Set(values).size
    default:
      return 0
  }
}

/**
 * 数据转换
 */
export function transformData<T, R>(items: T[], transformer: (item: T, index: number) => R): R[] {
  return items.map(transformer)
}

/**
 * 数据验证
 */
export interface ValidationRule {
  field: string
  rules: Array<{
    type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
    value?: any
    message: string
    validator?: (value: any) => boolean
  }>
}

export function validateData<T>(
  data: T,
  rules: ValidationRule[]
): { valid: boolean; errors: Array<{ field: string; message: string }> } {
  const errors: Array<{ field: string; message: string }> = []

  for (const rule of rules) {
    const value = getNestedValue(data, rule.field)

    for (const check of rule.rules) {
      let valid = true

      switch (check.type) {
        case 'required':
          valid = value != null && value !== ''
          break
        case 'min':
          valid = value >= check.value
          break
        case 'max':
          valid = value <= check.value
          break
        case 'pattern':
          valid = new RegExp(check.value).test(String(value))
          break
        case 'custom':
          valid = check.validator ? check.validator(value) : true
          break
      }

      if (!valid) {
        errors.push({
          field: rule.field,
          message: check.message
        })
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 数据去重
 */
export function uniqueBy<T>(items: T[], keyGetter: (item: T) => any): T[] {
  const seen = new Set()
  const result: T[] = []

  for (const item of items) {
    const key = keyGetter(item)
    if (!seen.has(key)) {
      seen.add(key)
      result.push(item)
    }
  }

  return result
}

/**
 * 树形数据处理
 */
export interface TreeNode {
  id: string | number
  parentId?: string | number | null
  children?: TreeNode[]
  [key: string]: any
}

export function buildTree<T extends TreeNode>(
  items: T[],
  options: {
    idField?: string
    parentField?: string
    childrenField?: string
  } = {}
): T[] {
  const { idField = 'id', parentField = 'parentId', childrenField = 'children' } = options

  const itemMap = new Map<string | number, T>()
  const roots: T[] = []

  // 创建映射
  items.forEach(item => {
    itemMap.set(item[idField], { ...item, [childrenField]: [] })
  })

  // 构建树
  items.forEach(item => {
    const parentId = item[parentField]
    const currentItem = itemMap.get(item[idField])!

    if (parentId == null || !itemMap.has(parentId)) {
      roots.push(currentItem)
    } else {
      const parent = itemMap.get(parentId)!
      parent[childrenField].push(currentItem)
    }
  })

  return roots
}

/**
 * 扁平化树形数据
 */
export function flattenTree<T extends TreeNode>(tree: T[], childrenField = 'children'): T[] {
  const result: T[] = []

  function traverse(nodes: T[]) {
    for (const node of nodes) {
      const { [childrenField]: children, ...rest } = node
      result.push(rest as T)

      if (children && children.length > 0) {
        traverse(children)
      }
    }
  }

  traverse(tree)
  return result
}
