/**
 * @name 导入导出Mock模块
 * @description 提供Excel/PDF导入导出、批量数据处理等Mock API接口
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import { mockResponse, generateId, delay } from '../utils'
import { faker } from '@faker-js/faker/locale/zh_CN'

// 导入任务存储
const importTasks = new Map<string, any>()

// 导出任务存储
const exportTasks = new Map<string, any>()

// 批处理任务存储
const batchTasks = new Map<string, any>()

export default [
  // ==================== Excel导入接口 ====================
  
  // 创建Excel导入任务
  {
    url: '/api/import/excel/create',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      const { type, file } = body
      
      if (!type || !file) {
        return mockResponse.error('参数不完整', 400)
      }
      
      const task = {
        id: generateId(),
        type,
        fileName: file.name || 'import.xlsx',
        fileSize: file.size || faker.number.int({ min: 10240, max: 5242880 }),
        totalRows: faker.number.int({ min: 100, max: 5000 }),
        processedRows: 0,
        successRows: 0,
        failedRows: 0,
        status: 'pending',
        progress: 0,
        errors: [],
        createdAt: new Date().toISOString(),
        createdBy: 'admin'
      }
      
      importTasks.set(task.id, task)
      
      // 模拟异步处理
      setTimeout(() => processImportTask(task.id), 1000)
      
      return mockResponse.success({
        taskId: task.id,
        message: '导入任务已创建，正在处理中...'
      })
    }
  },
  
  // 获取导入任务状态
  {
    url: '/api/import/excel/status/:taskId',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      const task = importTasks.get(params.taskId)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      return mockResponse.success(task)
    }
  },
  
  // 下载导入模板
  {
    url: '/api/import/excel/template/:type',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      const templates = {
        employee: {
          name: '员工信息导入模板',
          columns: ['工号', '姓名', '性别', '部门', '职位', '入职日期', '手机号', '邮箱']
        },
        salary: {
          name: '薪资数据导入模板',
          columns: ['工号', '姓名', '基本工资', '绩效工资', '津贴', '扣款']
        },
        attendance: {
          name: '考勤数据导入模板',
          columns: ['工号', '姓名', '日期', '上班时间', '下班时间', '加班时长']
        },
        contract: {
          name: '合同信息导入模板',
          columns: ['工号', '姓名', '合同编号', '合同类型', '开始日期', '结束日期']
        }
      }
      
      const template = templates[params.type as keyof typeof templates]
      
      if (!template) {
        return mockResponse.error('模板类型不存在', 404)
      }
      
      return mockResponse.success({
        type: params.type,
        name: template.name,
        downloadUrl: `/downloads/templates/${params.type}_template.xlsx`,
        columns: template.columns,
        sampleData: generateSampleData(params.type)
      })
    }
  },
  
  // 取消导入任务
  {
    url: '/api/import/excel/cancel/:taskId',
    method: 'post',
    response: ({ params }: { params: Record<string, any> }) => {
      const task = importTasks.get(params.taskId)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      if (task.status === 'completed' || task.status === 'failed') {
        return mockResponse.error('任务已结束，无法取消', 400)
      }
      
      task.status = 'cancelled'
      task.cancelledAt = new Date().toISOString()
      
      return mockResponse.success(task, '任务已取消')
    }
  },
  
  // 获取导入错误详情
  {
    url: '/api/import/excel/errors/:taskId',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      const task = importTasks.get(params.taskId)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      return mockResponse.success({
        taskId: params.taskId,
        totalErrors: task.errors.length,
        errors: task.errors,
        downloadUrl: task.errors.length > 0 ? `/downloads/errors/${params.taskId}_errors.xlsx` : null
      })
    }
  },
  
  // ==================== PDF导出接口 ====================
  
  // 创建PDF导出任务
  {
    url: '/api/export/pdf/create',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      const { type, title, data, options = {} } = body
      
      const task = {
        id: generateId(),
        type,
        title: title || '数据导出',
        format: 'pdf',
        pageCount: faker.number.int({ min: 1, max: 50 }),
        status: 'processing',
        progress: 0,
        options: {
          orientation: options.orientation || 'portrait',
          pageSize: options.pageSize || 'A4',
          margins: options.margins || { top: 20, right: 20, bottom: 20, left: 20 },
          header: options.header || true,
          footer: options.footer || true,
          watermark: options.watermark || null
        },
        createdAt: new Date().toISOString(),
        createdBy: 'admin'
      }
      
      exportTasks.set(task.id, task)
      
      // 模拟异步处理
      setTimeout(() => processExportTask(task.id), 2000)
      
      return mockResponse.success({
        taskId: task.id,
        message: 'PDF导出任务已创建，正在生成中...'
      })
    }
  },
  
  // 创建Excel导出任务
  {
    url: '/api/export/excel/create',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      const { type, title, data, sheets = [] } = body
      
      const task = {
        id: generateId(),
        type,
        title: title || '数据导出',
        format: 'excel',
        sheets: sheets.length || 1,
        totalRows: faker.number.int({ min: 100, max: 10000 }),
        status: 'processing',
        progress: 0,
        options: {
          includeFormulas: body.includeFormulas || false,
          autoFilter: body.autoFilter || true,
          freezePanes: body.freezePanes || true
        },
        createdAt: new Date().toISOString(),
        createdBy: 'admin'
      }
      
      exportTasks.set(task.id, task)
      
      // 模拟异步处理
      setTimeout(() => processExportTask(task.id), 1500)
      
      return mockResponse.success({
        taskId: task.id,
        message: 'Excel导出任务已创建，正在生成中...'
      })
    }
  },
  
  // 获取导出任务状态
  {
    url: '/api/export/status/:taskId',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      const task = exportTasks.get(params.taskId)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      return mockResponse.success(task)
    }
  },
  
  // 下载导出文件
  {
    url: '/api/export/download/:taskId',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      const task = exportTasks.get(params.taskId)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      if (task.status !== 'completed') {
        return mockResponse.error('文件尚未生成完成', 400)
      }
      
      const extension = task.format === 'pdf' ? 'pdf' : 'xlsx'
      
      return mockResponse.success({
        taskId: params.taskId,
        fileName: `${task.title}_${new Date().toISOString().split('T')[0]}.${extension}`,
        downloadUrl: `/downloads/exports/${params.taskId}.${extension}`,
        fileSize: faker.number.int({ min: 102400, max: 10485760 }),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      })
    }
  },
  
  // ==================== 批量处理接口 ====================
  
  // 创建批量处理任务
  {
    url: '/api/batch/create',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      const { operation, entityType, ids, data } = body
      
      if (!operation || !entityType || !ids || ids.length === 0) {
        return mockResponse.error('参数不完整', 400)
      }
      
      const task = {
        id: generateId(),
        operation,
        entityType,
        totalCount: ids.length,
        processedCount: 0,
        successCount: 0,
        failedCount: 0,
        status: 'queued',
        progress: 0,
        results: [],
        createdAt: new Date().toISOString(),
        startedAt: null,
        completedAt: null,
        estimatedTime: ids.length * 100 // 每条100ms
      }
      
      batchTasks.set(task.id, task)
      
      // 模拟异步处理
      setTimeout(() => processBatchTask(task.id, ids, data), 500)
      
      return mockResponse.success({
        taskId: task.id,
        message: `批量${operation}任务已创建，共${ids.length}条记录`
      })
    }
  },
  
  // 获取批量任务状态
  {
    url: '/api/batch/status/:taskId',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      const task = batchTasks.get(params.taskId)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      return mockResponse.success(task)
    }
  },
  
  // 获取批量任务结果
  {
    url: '/api/batch/results/:taskId',
    method: 'get',
    response: ({ params, query }: { params: Record<string, any>, query: Record<string, any> }) => {
      const task = batchTasks.get(params.taskId)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      let results = [...task.results]
      
      // 过滤成功/失败
      if (query.status === 'success') {
        results = results.filter(r => r.success)
      } else if (query.status === 'failed') {
        results = results.filter(r => !r.success)
      }
      
      // 分页
      const page = Number(query.page) || 1
      const pageSize = Number(query.pageSize) || 20
      const start = (page - 1) * pageSize
      const end = start + pageSize
      
      return mockResponse.success({
        taskId: params.taskId,
        total: results.length,
        page,
        pageSize,
        results: results.slice(start, end)
      })
    }
  },
  
  // ==================== 进度查询接口 ====================
  
  // 获取所有进行中的任务
  {
    url: '/api/tasks/in-progress',
    method: 'get',
    response: () => {
      const inProgressTasks: any[] = []
      
      // 收集所有进行中的任务
      importTasks.forEach(task => {
        if (task.status === 'processing') {
          inProgressTasks.push({
            id: task.id,
            type: 'import',
            name: task.fileName,
            progress: task.progress,
            status: task.status
          })
        }
      })
      
      exportTasks.forEach(task => {
        if (task.status === 'processing') {
          inProgressTasks.push({
            id: task.id,
            type: 'export',
            name: task.title,
            progress: task.progress,
            status: task.status
          })
        }
      })
      
      batchTasks.forEach(task => {
        if (task.status === 'processing') {
          inProgressTasks.push({
            id: task.id,
            type: 'batch',
            name: `${task.operation} ${task.entityType}`,
            progress: task.progress,
            status: task.status
          })
        }
      })
      
      return mockResponse.success({
        total: inProgressTasks.length,
        tasks: inProgressTasks
      })
    }
  },
  
  // WebSocket订阅任务进度
  {
    url: '/api/tasks/subscribe',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      const { taskIds = [] } = body
      
      return mockResponse.success({
        subscriptionId: generateId(),
        wsUrl: `ws://localhost:3000/tasks/progress/${generateId()}`,
        subscribedTasks: taskIds,
        interval: 1000 // 1秒更新一次
      })
    }
  }
] as MockMethod[]

// 处理导入任务的辅助函数
function processImportTask(taskId: string) {
  const task = importTasks.get(taskId)
  if (!task || task.status !== 'pending') return
  
  task.status = 'processing'
  task.startedAt = new Date().toISOString()
  
  // 模拟逐步处理
  const interval = setInterval(() => {
    if (task.status !== 'processing') {
      clearInterval(interval)
      return
    }
    
    const batchSize = faker.number.int({ min: 50, max: 200 })
    task.processedRows = Math.min(task.processedRows + batchSize, task.totalRows)
    task.progress = Math.floor((task.processedRows / task.totalRows) * 100)
    
    // 模拟一些错误
    if (Math.random() > 0.9) {
      const errorRow = faker.number.int({ min: 1, max: task.processedRows })
      task.errors.push({
        row: errorRow,
        column: faker.helpers.arrayElement(['姓名', '工号', '部门', '邮箱']),
        value: faker.lorem.word(),
        error: faker.helpers.arrayElement(['格式错误', '数据重复', '必填项为空', '长度超限'])
      })
      task.failedRows++
    } else {
      task.successRows++
    }
    
    // 处理完成
    if (task.processedRows >= task.totalRows) {
      task.status = 'completed'
      task.completedAt = new Date().toISOString()
      clearInterval(interval)
    }
  }, 500)
}

// 处理导出任务的辅助函数
function processExportTask(taskId: string) {
  const task = exportTasks.get(taskId)
  if (!task || task.status !== 'processing') return
  
  task.startedAt = new Date().toISOString()
  
  // 模拟逐步处理
  const interval = setInterval(() => {
    task.progress = Math.min(task.progress + faker.number.int({ min: 10, max: 30 }), 100)
    
    if (task.progress >= 100) {
      task.status = 'completed'
      task.completedAt = new Date().toISOString()
      task.downloadUrl = `/downloads/exports/${task.id}.${task.format === 'pdf' ? 'pdf' : 'xlsx'}`
      task.fileSize = faker.number.int({ min: 102400, max: 10485760 })
      clearInterval(interval)
    }
  }, 500)
}

// 处理批量任务的辅助函数
function processBatchTask(taskId: string, ids: string[], data: any) {
  const task = batchTasks.get(taskId)
  if (!task) return
  
  task.status = 'processing'
  task.startedAt = new Date().toISOString()
  
  // 模拟逐个处理
  let index = 0
  const interval = setInterval(() => {
    if (task.status !== 'processing' || index >= ids.length) {
      task.status = 'completed'
      task.completedAt = new Date().toISOString()
      clearInterval(interval)
      return
    }
    
    const batchSize = faker.number.int({ min: 1, max: 5 })
    for (let i = 0; i < batchSize && index < ids.length; i++) {
      const id = ids[index]
      const success = Math.random() > 0.1
      
      task.results.push({
        id,
        success,
        message: success ? '操作成功' : faker.helpers.arrayElement(['权限不足', '数据不存在', '状态不允许操作']),
        processedAt: new Date().toISOString()
      })
      
      if (success) {
        task.successCount++
      } else {
        task.failedCount++
      }
      
      task.processedCount++
      task.progress = Math.floor((task.processedCount / task.totalCount) * 100)
      index++
    }
  }, 200)
}

// 生成示例数据的辅助函数
function generateSampleData(type: string) {
  switch (type) {
    case 'employee':
      return [
        ['E00001', '张三', '男', '信息工程学院', '教师', '2024-01-15', '13800138000', '<EMAIL>'],
        ['E00002', '李四', '女', '人事处', '行政专员', '2024-02-01', '13900139000', '<EMAIL>']
      ]
    case 'salary':
      return [
        ['E00001', '张三', '10000', '3000', '2000', '500'],
        ['E00002', '李四', '8000', '2000', '1500', '300']
      ]
    default:
      return []
  }
}