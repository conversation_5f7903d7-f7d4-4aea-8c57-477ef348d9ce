/**
 * @name 组织架构管理Mock模块
 * @description 提供组织架构管理相关的Mock接口，包括组织树、部门CRUD、岗位管理、编制管理等功能
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import type { MockRequestOptions } from '../types'
// import { faker } from '@faker-js/faker/locale/zh_CN'
import { success, error, paginate, delay, generateId } from '../utils'
// import { MockServiceManager } from '../service/MockServiceManager'
import { createSimpleMockService } from '../service/SimpleMockService'
import type {
  Organization,
  OrganizationType,
  OrganizationStatus,
  OrganizationQueryRequest,
  Establishment,
  // EstablishmentDetail,
  ChangeRequest,
  // ChangeType,
  ApprovalStatus,
  OrganizationChange
} from '../../types/organization'

// 创建持久化服务
const organizationService = createSimpleMockService<any>('organizations')
const establishmentService = createSimpleMockService<any>('establishments')
const changeRequestService = createSimpleMockService<any>('organizationChanges')
const changeLogService = createSimpleMockService<any>('organizationLogs')

// 初始化标志
let isInitialized = false

// 确保数据初始化
const ensureInitialized = () => {
  if (!isInitialized) {
    try {
      initOrganizationData()
      initEstablishmentData()
      isInitialized = true
      console.log('组织架构Mock数据初始化成功')
    } catch (error) {
      console.error('组织架构Mock数据初始化失败:', error)
    }
  }
}

// 初始化组织架构数据
const initOrganizationData = () => {
  const existingData = organizationService.readAll()
  if (existingData.length > 0) {
    console.log('组织架构数据已存在，跳过初始化')
    return
  }

  const organizations: Organization[] = [
    // 学校根节点
    {
      institutionId: '1',
      institutionName: '杭州科技职业技术学院',
      institutionCode: 'HZVTC',
      institutionType: 'SCHOOL' as OrganizationType,
      status: 'ACTIVE' as OrganizationStatus,
      level: 0,
      path: '1',
      sortOrder: 1,
      establishDate: '1999-01-01',
      effectiveDate: '1999-01-01',
      description: '杭州科技职业技术学院是杭州市人民政府主办的一所全日制普通高等职业院校',
      contactPhone: '0571-88888888',
      email: '<EMAIL>',
      officeAddress: '杭州市富阳区高科路198号',
      approvalDocNumber: '浙教高〔1999〕1号',
      version: 1,
      leaderName: '张伟',
      leaderEmployeeId: '10001',
      employeeCount: 1200,
      establishmentCount: 1500,
      actualCount: 1200,
      hasChildren: true,
      children: [],
      createTime: '1999-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 行政部门
    {
      institutionId: '2',
      institutionName: '党政办公室',
      institutionCode: 'DZOFFICE',
      institutionType: 'ADMIN_DEPT' as OrganizationType,
      parentInstitutionId: '1',
      parentInstitutionName: '杭州科技职业技术学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 1,
      path: '1/2',
      sortOrder: 1,
      establishDate: '1999-01-01',
      effectiveDate: '1999-01-01',
      description: '负责学校党务、行政综合协调管理工作',
      contactPhone: '0571-88888801',
      email: '<EMAIL>',
      officeAddress: '行政楼301室',
      version: 1,
      leaderName: '李明',
      leaderEmployeeId: '10002',
      employeeCount: 25,
      establishmentCount: 30,
      actualCount: 25,
      hasChildren: false,
      createTime: '1999-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    {
      institutionId: '3',
      institutionName: '人事处',
      institutionCode: 'HR',
      institutionType: 'ADMIN_DEPT' as OrganizationType,
      parentInstitutionId: '1',
      parentInstitutionName: '杭州科技职业技术学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 1,
      path: '1/3',
      sortOrder: 2,
      establishDate: '1999-01-01',
      effectiveDate: '1999-01-01',
      description: '负责学校人事管理、师资队伍建设等工作',
      contactPhone: '0571-88888802',
      email: '<EMAIL>',
      officeAddress: '行政楼401室',
      version: 1,
      leaderName: '王芳',
      leaderEmployeeId: '10003',
      employeeCount: 18,
      establishmentCount: 20,
      actualCount: 18,
      hasChildren: true,
      createTime: '1999-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 人事处下属科室
    {
      institutionId: '31',
      institutionName: '人事科',
      institutionCode: 'HR-RS',
      institutionType: 'DEPARTMENT' as OrganizationType,
      parentInstitutionId: '3',
      parentInstitutionName: '人事处',
      status: 'ACTIVE' as OrganizationStatus,
      level: 2,
      path: '1/3/31',
      sortOrder: 1,
      establishDate: '1999-01-01',
      effectiveDate: '1999-01-01',
      description: '负责人事管理具体工作',
      version: 1,
      leaderName: '赵敏',
      leaderEmployeeId: '10031',
      employeeCount: 8,
      establishmentCount: 10,
      actualCount: 8,
      hasChildren: false,
      createTime: '1999-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    {
      institutionId: '32',
      institutionName: '劳资科',
      institutionCode: 'HR-LZ',
      institutionType: 'DEPARTMENT' as OrganizationType,
      parentInstitutionId: '3',
      parentInstitutionName: '人事处',
      status: 'ACTIVE' as OrganizationStatus,
      level: 2,
      path: '1/3/32',
      sortOrder: 2,
      establishDate: '1999-01-01',
      effectiveDate: '1999-01-01',
      description: '负责薪资福利管理工作',
      version: 1,
      leaderName: '钱伟',
      leaderEmployeeId: '10032',
      employeeCount: 6,
      establishmentCount: 8,
      actualCount: 6,
      hasChildren: false,
      createTime: '1999-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 教务处
    {
      institutionId: '4',
      institutionName: '教务处',
      institutionCode: 'TEACH',
      institutionType: 'ADMIN_DEPT' as OrganizationType,
      parentInstitutionId: '1',
      parentInstitutionName: '杭州科技职业技术学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 1,
      path: '1/4',
      sortOrder: 3,
      establishDate: '1999-01-01',
      effectiveDate: '1999-01-01',
      description: '负责学校教学管理工作',
      contactPhone: '0571-88888803',
      email: '<EMAIL>',
      officeAddress: '教学楼201室',
      version: 1,
      leaderName: '孙杰',
      leaderEmployeeId: '10004',
      employeeCount: 20,
      establishmentCount: 25,
      actualCount: 20,
      hasChildren: false,
      createTime: '1999-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 二级学院
    {
      institutionId: '5',
      institutionName: '信息工程学院',
      institutionCode: 'IT',
      institutionType: 'COLLEGE' as OrganizationType,
      parentInstitutionId: '1',
      parentInstitutionName: '杭州科技职业技术学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 1,
      path: '1/5',
      sortOrder: 4,
      establishDate: '2001-09-01',
      effectiveDate: '2001-09-01',
      description: '培养信息技术专业人才的二级学院',
      contactPhone: '0571-88888805',
      email: '<EMAIL>',
      officeAddress: '信息楼',
      version: 1,
      leaderName: '刘强',
      leaderEmployeeId: '10005',
      employeeCount: 150,
      establishmentCount: 180,
      actualCount: 150,
      hasChildren: true,
      createTime: '2001-09-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 信息工程学院下属系部
    {
      institutionId: '51',
      institutionName: '计算机系',
      institutionCode: 'IT-CS',
      institutionType: 'TEACHING_DEPT' as OrganizationType,
      parentInstitutionId: '5',
      parentInstitutionName: '信息工程学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 2,
      path: '1/5/51',
      sortOrder: 1,
      establishDate: '2001-09-01',
      effectiveDate: '2001-09-01',
      description: '计算机相关专业教学部门',
      version: 1,
      leaderName: '周敏',
      leaderEmployeeId: '10051',
      employeeCount: 60,
      establishmentCount: 70,
      actualCount: 60,
      hasChildren: false,
      createTime: '2001-09-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    {
      institutionId: '52',
      institutionName: '软件工程系',
      institutionCode: 'IT-SE',
      institutionType: 'TEACHING_DEPT' as OrganizationType,
      parentInstitutionId: '5',
      parentInstitutionName: '信息工程学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 2,
      path: '1/5/52',
      sortOrder: 2,
      establishDate: '2005-09-01',
      effectiveDate: '2005-09-01',
      description: '软件工程专业教学部门',
      version: 1,
      leaderName: '吴华',
      leaderEmployeeId: '10052',
      employeeCount: 50,
      establishmentCount: 60,
      actualCount: 50,
      hasChildren: false,
      createTime: '2005-09-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    {
      institutionId: '53',
      institutionName: '网络工程系',
      institutionCode: 'IT-NE',
      institutionType: 'TEACHING_DEPT' as OrganizationType,
      parentInstitutionId: '5',
      parentInstitutionName: '信息工程学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 2,
      path: '1/5/53',
      sortOrder: 3,
      establishDate: '2008-09-01',
      effectiveDate: '2008-09-01',
      description: '网络工程专业教学部门',
      version: 1,
      leaderName: '郑军',
      leaderEmployeeId: '10053',
      employeeCount: 40,
      establishmentCount: 50,
      actualCount: 40,
      hasChildren: false,
      createTime: '2008-09-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 其他二级学院
    {
      institutionId: '6',
      institutionName: '机电工程学院',
      institutionCode: 'ME',
      institutionType: 'COLLEGE' as OrganizationType,
      parentInstitutionId: '1',
      parentInstitutionName: '杭州科技职业技术学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 1,
      path: '1/6',
      sortOrder: 5,
      establishDate: '2000-09-01',
      effectiveDate: '2000-09-01',
      description: '培养机电工程专业人才的二级学院',
      contactPhone: '0571-88888806',
      email: '<EMAIL>',
      officeAddress: '机电楼',
      version: 1,
      leaderName: '陈晓',
      leaderEmployeeId: '10006',
      employeeCount: 120,
      establishmentCount: 150,
      actualCount: 120,
      hasChildren: true,
      createTime: '2000-09-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    {
      institutionId: '7',
      institutionName: '商贸旅游学院',
      institutionCode: 'BT',
      institutionType: 'COLLEGE' as OrganizationType,
      parentInstitutionId: '1',
      parentInstitutionName: '杭州科技职业技术学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 1,
      path: '1/7',
      sortOrder: 6,
      establishDate: '2002-09-01',
      effectiveDate: '2002-09-01',
      description: '培养商贸旅游专业人才的二级学院',
      contactPhone: '0571-88888807',
      email: '<EMAIL>',
      officeAddress: '商贸楼',
      version: 1,
      leaderName: '黄丽',
      leaderEmployeeId: '10007',
      employeeCount: 100,
      establishmentCount: 120,
      actualCount: 100,
      hasChildren: true,
      createTime: '2002-09-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 直属单位
    {
      institutionId: '8',
      institutionName: '图书馆',
      institutionCode: 'LIBRARY',
      institutionType: 'DIRECT_UNIT' as OrganizationType,
      parentInstitutionId: '1',
      parentInstitutionName: '杭州科技职业技术学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 1,
      path: '1/8',
      sortOrder: 7,
      establishDate: '1999-01-01',
      effectiveDate: '1999-01-01',
      description: '学校图书文献信息中心',
      contactPhone: '0571-88888808',
      email: '<EMAIL>',
      officeAddress: '图书馆大楼',
      version: 1,
      leaderName: '马勇',
      leaderEmployeeId: '10008',
      employeeCount: 30,
      establishmentCount: 35,
      actualCount: 30,
      hasChildren: false,
      createTime: '1999-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 科研机构
    {
      institutionId: '9',
      institutionName: '智能制造研究所',
      institutionCode: 'IMRI',
      institutionType: 'RESEARCH_INST' as OrganizationType,
      parentInstitutionId: '1',
      parentInstitutionName: '杭州科技职业技术学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 1,
      path: '1/9',
      sortOrder: 8,
      establishDate: '2015-06-01',
      effectiveDate: '2015-06-01',
      description: '智能制造技术研究与应用',
      contactPhone: '0571-88888809',
      email: '<EMAIL>',
      officeAddress: '科研楼301室',
      version: 1,
      leaderName: '杨帆',
      leaderEmployeeId: '10009',
      employeeCount: 15,
      establishmentCount: 20,
      actualCount: 15,
      hasChildren: false,
      createTime: '2015-06-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 临时组织
    {
      institutionId: '10',
      institutionName: '教学质量评估委员会',
      institutionCode: 'TQAC',
      institutionType: 'TEMP_ORG' as OrganizationType,
      parentInstitutionId: '1',
      parentInstitutionName: '杭州科技职业技术学院',
      status: 'ACTIVE' as OrganizationStatus,
      level: 1,
      path: '1/10',
      sortOrder: 9,
      establishDate: '2023-01-01',
      effectiveDate: '2023-01-01',
      withdrawDate: '2025-12-31',
      description: '负责教学质量评估工作的临时组织',
      version: 1,
      leaderName: '林静',
      leaderEmployeeId: '10010',
      employeeCount: 10,
      establishmentCount: 10,
      actualCount: 10,
      hasChildren: false,
      createTime: '2023-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    }
  ]

  // 批量保存
  organizations.forEach(org => organizationService.create(org))
  console.log(`初始化${organizations.length}条组织架构数据`)
}

// 初始化编制数据
const initEstablishmentData = () => {
  const existingData = establishmentService.readAll()
  if (existingData.length > 0) {
    console.log('编制数据已存在，跳过初始化')
    return
  }

  const establishments: Establishment[] = [
    {
      organizationId: '1',
      establishmentCount: 1500,
      actualCount: 1200,
      vacancyCount: 300,
      pendingCount: 50,
      overStaffCount: 0,
      effectiveDate: '2024-01-01',
      establishmentFile: '浙人社〔2024〕1号',
      remark: '学校总编制',
      details: [
        {
          positionId: 'P001',
          positionName: '教授',
          positionType: '教学岗',
          establishmentCount: 100,
          actualCount: 85,
          requirements: '博士学位，副高以上职称'
        },
        {
          positionId: 'P002',
          positionName: '副教授',
          positionType: '教学岗',
          establishmentCount: 200,
          actualCount: 180,
          requirements: '硕士以上学位，中级以上职称'
        },
        {
          positionId: 'P003',
          positionName: '讲师',
          positionType: '教学岗',
          establishmentCount: 400,
          actualCount: 350,
          requirements: '硕士以上学位'
        },
        {
          positionId: 'P004',
          positionName: '管理人员',
          positionType: '管理岗',
          establishmentCount: 300,
          actualCount: 280,
          requirements: '本科以上学历'
        }
      ]
    },
    {
      organizationId: '3',
      establishmentCount: 20,
      actualCount: 18,
      vacancyCount: 2,
      pendingCount: 1,
      overStaffCount: 0,
      effectiveDate: '2024-01-01',
      remark: '人事处编制',
      details: [
        {
          positionId: 'P101',
          positionName: '处长',
          positionType: '管理岗',
          establishmentCount: 1,
          actualCount: 1,
          requirements: '正处级，硕士以上学位'
        },
        {
          positionId: 'P102',
          positionName: '副处长',
          positionType: '管理岗',
          establishmentCount: 2,
          actualCount: 2,
          requirements: '副处级，本科以上学历'
        },
        {
          positionId: 'P103',
          positionName: '科长',
          positionType: '管理岗',
          establishmentCount: 4,
          actualCount: 4,
          requirements: '正科级，本科以上学历'
        },
        {
          positionId: 'P104',
          positionName: '科员',
          positionType: '管理岗',
          establishmentCount: 13,
          actualCount: 11,
          requirements: '本科以上学历'
        }
      ]
    },
    {
      organizationId: '5',
      establishmentCount: 180,
      actualCount: 150,
      vacancyCount: 30,
      pendingCount: 10,
      overStaffCount: 0,
      effectiveDate: '2024-01-01',
      remark: '信息工程学院编制',
      details: [
        {
          positionId: 'P201',
          positionName: '院长',
          positionType: '管理岗',
          establishmentCount: 1,
          actualCount: 1,
          requirements: '正高职称，博士学位'
        },
        {
          positionId: 'P202',
          positionName: '副院长',
          positionType: '管理岗',
          establishmentCount: 3,
          actualCount: 3,
          requirements: '副高以上职称，硕士以上学位'
        },
        {
          positionId: 'P203',
          positionName: '系主任',
          positionType: '教学岗',
          establishmentCount: 6,
          actualCount: 5,
          requirements: '副高以上职称'
        },
        {
          positionId: 'P204',
          positionName: '专任教师',
          positionType: '教学岗',
          establishmentCount: 150,
          actualCount: 130,
          requirements: '硕士以上学位'
        },
        {
          positionId: 'P205',
          positionName: '实验员',
          positionType: '教辅岗',
          establishmentCount: 20,
          actualCount: 11,
          requirements: '本科以上学历'
        }
      ]
    }
  ]

  // 批量保存
  establishments.forEach(est => establishmentService.create(est))
  console.log(`初始化${establishments.length}条编制数据`)
}

// 构建组织树
const buildOrganizationTree = (organizations: Organization[]): Organization[] => {
  const orgMap = new Map<string, Organization>()
  const roots: Organization[] = []

  // 创建映射
  organizations.forEach(org => {
    orgMap.set(org.institutionId, { ...org, children: [] })
  })

  // 构建树
  organizations.forEach(org => {
    const node = orgMap.get(org.institutionId)!
    if (org.parentInstitutionId) {
      const parent = orgMap.get(org.parentInstitutionId)
      if (parent) {
        parent.children = parent.children || []
        parent.children.push(node)
      }
    } else {
      roots.push(node)
    }
  })

  return roots
}

// 扁平化组织树
const flattenOrganizationTree = (tree: Organization[]): Organization[] => {
  const result: Organization[] = []

  const traverse = (nodes: Organization[]) => {
    nodes.forEach(node => {
      result.push(node)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }

  traverse(tree)
  return result
}

// 查找组织及其所有子组织ID
const findOrganizationWithChildren = (orgId: string): string[] => {
  const organizations = organizationService.readAll()
  const result: string[] = [orgId]

  const findChildren = (parentId: string) => {
    organizations
      .filter(org => org.parentInstitutionId === parentId)
      .forEach(child => {
        result.push(child.institutionId)
        findChildren(child.institutionId)
      })
  }

  findChildren(orgId)
  return result
}

// 验证组织数据
const validateOrganization = (data: Partial<Organization>): string | null => {
  if (!data.institutionName || data.institutionName.trim() === '') {
    return '机构名称不能为空'
  }

  if (!data.institutionCode || data.institutionCode.trim() === '') {
    return '机构编码不能为空'
  }

  if (!data.institutionType) {
    return '机构类型不能为空'
  }

  // 检查编码唯一性
  const organizations = organizationService.readAll()
  const exists = organizations.some(
    org => org.institutionCode === data.institutionCode && org.institutionId !== data.institutionId
  )

  if (exists) {
    return '机构编码已存在'
  }

  return null
}

export default [
  // 获取组织架构树
  {
    url: '/api/organization/tree',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        let organizations = organizationService.readAll()

        // 根据查询参数过滤
        if (query?.status) {
          organizations = organizations.filter(org => org.status === query.status)
        }

        if (query?.institutionType) {
          organizations = organizations.filter(org => org.institutionType === query.institutionType)
        }

        // 构建树形结构
        const tree = buildOrganizationTree(organizations)

        const response = success(tree, '获取组织架构树成功')
        console.log('Response for /api/organization/tree:', response)
        return response
      } catch (err: any) {
        console.error('/api/organization/tree', err)
        return error('获取组织架构树失败', 500)
      }
    }
  },

  // 获取组织列表（分页）
  {
    url: '/api/organization/list',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const {
          page = 1,
          size = 10,
          keyword = '',
          orgType,
          parentId,
          status,
          includeChildren = false
        } = query as OrganizationQueryRequest

        let organizations = organizationService.readAll()

        // 关键词搜索
        if (keyword) {
          organizations = organizations.filter(
            org =>
              org.institutionName.includes(keyword) ||
              org.institutionCode.includes(keyword) ||
              org.leaderName?.includes(keyword)
          )
        }

        // 类型筛选
        if (orgType) {
          organizations = organizations.filter(org => org.institutionType === orgType)
        }

        // 状态筛选
        if (status) {
          organizations = organizations.filter(org => org.status === status)
        }

        // 父级筛选
        if (parentId) {
          if (String(includeChildren) === 'true') {
            const targetIds = findOrganizationWithChildren(String(parentId))
            organizations = organizations.filter(org => targetIds.includes(org.institutionId))
          } else {
            organizations = organizations.filter(org => org.parentInstitutionId === parentId)
          }
        }

        // 分页处理
        const result = paginate(organizations, Number(page), Number(size))

        const response = success(result, '获取组织列表成功')
        console.log('Response for /api/organization/list:', response)
        return response
      } catch (err: any) {
        console.error('/api/organization/list', err)
        return error('获取组织列表失败', 500)
      }
    }
  },

  // 获取组织详情
  {
    url: '/api/organization/:id',
    method: 'get',
    response: async ({ params }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const organizations = organizationService.readAll()
        const organization = organizations.find(org => org.institutionId === params?.id)

        if (!organization) {
          return error('组织不存在', 404)
        }

        // 获取编制信息
        const establishments = establishmentService.readAll()
        const establishment = establishments.find(est => est.organizationId === params?.id)

        const result = {
          ...organization,
          establishment
        }

        const response = success(result, '获取组织详情成功')
        console.log(`Response for /api/organization/${params?.id}:`, response)
        return response
      } catch (err: any) {
        console.error(`Error in /api/organization/${params?.id}:`, error)
        return error('获取组织详情失败', 500)
      }
    }
  },

  // 创建组织
  {
    url: '/api/organization',
    method: 'post',
    response: async ({ body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        // 验证数据
        const validationError = validateOrganization(body)
        if (validationError) {
          return error(validationError, 400)
        }

        // 获取父组织信息
        let parentOrg = null
        let level = 0
        let path = ''

        if (body.parentInstitutionId) {
          const organizations = organizationService.readAll()
          parentOrg = organizations.find(org => org.institutionId === body.parentInstitutionId)

          if (!parentOrg) {
            return error('父级组织不存在', 400)
          }

          level = (parentOrg.level || 0) + 1
          path = `${parentOrg.path}/${generateId()}`
        } else {
          path = generateId()
        }

        // 创建新组织
        const newOrganization: Organization = {
          institutionId: generateId(),
          institutionName: body.institutionName,
          institutionCode: body.institutionCode,
          institutionType: body.institutionType,
          parentInstitutionId: body.parentInstitutionId,
          parentInstitutionName: parentOrg?.institutionName,
          status: 'ACTIVE' as OrganizationStatus,
          level,
          path,
          sortOrder: body.sortOrder || 999,
          establishDate: body.establishDate || new Date().toISOString().split('T')[0],
          effectiveDate: body.effectiveDate || new Date().toISOString().split('T')[0],
          description: body.description,
          contactPhone: body.contactPhone,
          email: body.email,
          officeAddress: body.officeAddress,
          version: 1,
          leaderName: body.leaderName,
          leaderEmployeeId: body.leaderEmployeeId,
          employeeCount: 0,
          establishmentCount: 0,
          actualCount: 0,
          hasChildren: false,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString(),
          createBy: 'admin',
          updateBy: 'admin'
        }

        const created = organizationService.create(newOrganization)

        // 更新父组织的hasChildren标志
        if (parentOrg) {
          organizationService.update(parentOrg.institutionId, { hasChildren: true })
        }

        // 记录变更日志
        const changeLog: OrganizationChange = {
          logId: generateId(),
          institutionId: created.institutionId,
          institutionName: created.institutionName,
          changeType: 'CREATE',
          changeDescription: `创建机构：${created.institutionName}`,
          changeDate: new Date().toISOString(),
          operator: 'admin',
          operatorName: '系统管理员',
          changeDetailsJSON: JSON.stringify({
            after: created
          })
        }
        changeLogService.create(changeLog)

        const response = success(created, '创建组织成功')
        console.log('Response for /api/organization:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/organization:', error)
        return error('创建组织失败', 500)
      }
    }
  },

  // 更新组织
  {
    url: '/api/organization/:id',
    method: 'put',
    response: async ({ params, body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const organizations = organizationService.readAll()
        const oldOrganization = organizations.find(org => org.institutionId === params?.id)

        if (!oldOrganization) {
          return error('组织不存在', 404)
        }

        // 验证数据
        const validationError = validateOrganization({ ...body, institutionId: params?.id || '' })
        if (validationError) {
          return error(validationError, 400)
        }

        // 更新组织
        const updateData = {
          ...body,
          updateTime: new Date().toISOString(),
          updateBy: 'admin',
          version: oldOrganization.version + 1
        }

        const updated = organizationService.update(params?.id || '', updateData)

        if (!updated) {
          return error('更新失败', 500)
        }

        // 记录变更日志
        const changeLog: OrganizationChange = {
          logId: generateId(),
          institutionId: params?.id,
          institutionName: updated.institutionName,
          changeType: 'UPDATE',
          changeDescription: `更新机构信息：${updated.institutionName}`,
          changeDate: new Date().toISOString(),
          operator: 'admin',
          operatorName: '系统管理员',
          changeDetailsJSON: JSON.stringify({
            before: oldOrganization,
            after: updated
          })
        }
        changeLogService.create(changeLog)

        const response = success(updated, '更新组织成功')
        console.log(`Response for /api/organization/${params?.id}:`, response)
        return response
      } catch (err: any) {
        console.error(`Error in /api/organization/${params?.id}:`, error)
        return error('更新组织失败', 500)
      }
    }
  },

  // 删除组织
  {
    url: '/api/organization/:id',
    method: 'delete',
    response: async ({ params }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const organizations = organizationService.readAll()
        const organization = organizations.find(org => org.institutionId === params?.id)

        if (!organization) {
          return error('组织不存在', 404)
        }

        // 检查是否有子组织
        const hasChildren = organizations.some(org => org.parentInstitutionId === params?.id)
        if (hasChildren) {
          return error('该组织下还有子组织，无法删除', 400)
        }

        // 检查是否有员工
        if (organization.employeeCount && organization.employeeCount > 0) {
          return error('该组织下还有员工，无法删除', 400)
        }

        // 执行删除（实际是标记为已撤销）
        const updated = organizationService.update(params?.id || '', {
          status: 'CANCELLED' as OrganizationStatus,
          withdrawDate: new Date().toISOString().split('T')[0],
          updateTime: new Date().toISOString(),
          updateBy: 'admin'
        })

        // 记录变更日志
        const changeLog: OrganizationChange = {
          logId: generateId(),
          institutionId: params?.id,
          institutionName: organization.institutionName,
          changeType: 'DELETE',
          changeDescription: `撤销机构：${organization.institutionName}`,
          changeDate: new Date().toISOString(),
          operator: 'admin',
          operatorName: '系统管理员',
          changeDetailsJSON: JSON.stringify({
            before: organization,
            after: updated
          })
        }
        changeLogService.create(changeLog)

        const response = success(null, '删除组织成功')
        console.log(`Response for /api/organization/${params?.id}:`, response)
        return response
      } catch (err: any) {
        console.error(`Error in /api/organization/${params?.id}:`, error)
        return error('删除组织失败', 500)
      }
    }
  },

  // 移动组织
  {
    url: '/api/organization/:id/move',
    method: 'post',
    response: async ({ params, body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { targetParentId } = body

        const organizations = organizationService.readAll()
        const organization = organizations.find(org => org.institutionId === params?.id)

        if (!organization) {
          return error('组织不存在', 404)
        }

        // 检查目标父组织
        let targetParent = null
        if (targetParentId) {
          targetParent = organizations.find(org => org.institutionId === targetParentId)
          if (!targetParent) {
            return error('目标父组织不存在', 404)
          }

          // 防止移动到自己的子组织
          const childrenIds = findOrganizationWithChildren(params?.id)
          if (childrenIds.includes(targetParentId)) {
            return error('不能将组织移动到其子组织下', 400)
          }
        }

        // 更新组织信息
        const oldParentId = organization.parentInstitutionId
        const updateData = {
          parentInstitutionId: targetParentId || undefined,
          parentInstitutionName: targetParent?.institutionName,
          level: targetParent ? (targetParent.level || 0) + 1 : 0,
          path: targetParent
            ? `${targetParent.path}/${organization.institutionId}`
            : organization.institutionId,
          updateTime: new Date().toISOString(),
          updateBy: 'admin',
          version: organization.version + 1
        }

        const updated = organizationService.update(params?.id || '', updateData)

        // 更新所有子组织的level和path
        const updateChildrenLevelAndPath = (
          orgId: string,
          parentLevel: number,
          parentPath: string
        ) => {
          const children = organizations.filter(org => org.parentInstitutionId === orgId)
          children.forEach(child => {
            organizationService.update(child.institutionId, {
              level: parentLevel + 1,
              path: `${parentPath}/${child.institutionId}`
            })
            updateChildrenLevelAndPath(
              child.institutionId,
              parentLevel + 1,
              `${parentPath}/${child.institutionId}`
            )
          })
        }

        updateChildrenLevelAndPath(params?.id, updateData.level!, updateData.path!)

        // 更新原父组织的hasChildren标志
        if (oldParentId) {
          const oldParentChildren = organizations.filter(
            org => org.parentInstitutionId === oldParentId && org.institutionId !== params?.id
          )
          if (oldParentChildren.length === 0) {
            organizationService.update(oldParentId, { hasChildren: false })
          }
        }

        // 更新新父组织的hasChildren标志
        if (targetParentId) {
          organizationService.update(targetParentId, { hasChildren: true })
        }

        // 记录变更日志
        const changeLog: OrganizationChange = {
          logId: generateId(),
          institutionId: params?.id,
          institutionName: organization.institutionName,
          changeType: 'MOVE',
          changeDescription: `移动机构：${organization.institutionName} 从 ${organization.parentInstitutionName || '顶级'} 到 ${targetParent?.institutionName || '顶级'}`,
          changeDate: new Date().toISOString(),
          operator: 'admin',
          operatorName: '系统管理员',
          changeDetailsJSON: JSON.stringify({
            before: { parentId: oldParentId },
            after: { parentId: targetParentId }
          })
        }
        changeLogService.create(changeLog)

        const response = success(updated, '移动组织成功')
        console.log(`Response for /api/organization/${params?.id}/move:`, response)
        return response
      } catch (err: any) {
        console.error(`Error in /api/organization/${params?.id}/move:`, error)
        return error('移动组织失败', 500)
      }
    }
  },

  // 获取编制信息
  {
    url: '/api/organization/:id/establishment',
    method: 'get',
    response: async ({ params }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const establishments = establishmentService.readAll()
        const establishment = establishments.find(est => est.organizationId === params?.id)

        if (!establishment) {
          // 返回默认编制信息
          const defaultEstablishment: Establishment = {
            organizationId: params?.id,
            establishmentCount: 0,
            actualCount: 0,
            vacancyCount: 0,
            pendingCount: 0,
            overStaffCount: 0,
            effectiveDate: new Date().toISOString().split('T')[0],
            details: []
          }
          return success(defaultEstablishment, '获取编制信息成功')
        }

        const response = success(establishment, '获取编制信息成功')
        console.log(`Response for /api/organization/${params?.id}/establishment:`, response)
        return response
      } catch (err: any) {
        console.error(`Error in /api/organization/${params?.id}/establishment:`, error)
        return error('获取编制信息失败', 500)
      }
    }
  },

  // 更新编制信息
  {
    url: '/api/organization/:id/establishment',
    method: 'put',
    response: async ({ params, body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const establishments = establishmentService.readAll()
        const existingIndex = establishments.findIndex(est => est.organizationId === params?.id)

        const establishmentData: Establishment = {
          organizationId: params?.id || '',
          establishmentCount: body.establishmentCount || 0,
          actualCount: body.actualCount || 0,
          vacancyCount: (body.establishmentCount || 0) - (body.actualCount || 0),
          pendingCount: body.pendingCount || 0,
          overStaffCount: Math.max(0, (body.actualCount || 0) - (body.establishmentCount || 0)),
          effectiveDate: body.effectiveDate || new Date().toISOString().split('T')[0],
          establishmentFile: body.establishmentFile,
          remark: body.remark,
          details: body.details || []
        }

        let result
        if (existingIndex >= 0) {
          // 更新现有编制
          result = establishmentService.update(params?.id, establishmentData)
        } else {
          // 创建新编制
          result = establishmentService.create(establishmentData)
        }

        // 更新组织的编制数量
        organizationService.update(params?.id, {
          establishmentCount: establishmentData.establishmentCount,
          actualCount: establishmentData.actualCount
        })

        const response = success(result, '更新编制信息成功')
        console.log(`Response for /api/organization/${params?.id}/establishment:`, response)
        return response
      } catch (err: any) {
        console.error(`Error in /api/organization/${params?.id}/establishment:`, error)
        return error('更新编制信息失败', 500)
      }
    }
  },

  // 获取组织变更历史
  {
    url: '/api/organization/:id/changes',
    method: 'get',
    response: async ({ params, query }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { page = 1, size = 10 } = query || {}

        let changes = changeLogService.readAll()

        // 筛选特定组织的变更历史
        changes = changes.filter(log => log.institutionId === params?.id)

        // 按时间倒序排序
        changes.sort((a, b) => new Date(b.changeDate).getTime() - new Date(a.changeDate).getTime())

        // 分页处理
        const result = paginate(changes, Number(page), Number(size))

        const response = success(result, '获取变更历史成功')
        console.log(`Response for /api/organization/${params?.id}/changes:`, response)
        return response
      } catch (err: any) {
        console.error(`Error in /api/organization/${params?.id}/changes:`, error)
        return error('获取变更历史失败', 500)
      }
    }
  },

  // 提交组织变更申请
  {
    url: '/api/organization/change-request',
    method: 'post',
    response: async ({ body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const changeRequest: ChangeRequest = {
          requestId: generateId(),
          changeType: body.changeType,
          institutionId: body.institutionId,
          institutionName: body.institutionName,
          title: body.title,
          reason: body.reason,
          description: body.description,
          effectiveDate: body.effectiveDate,
          status: 'DRAFT' as ApprovalStatus,
          createTime: new Date().toISOString(),
          createBy: 'admin',
          createByName: '系统管理员',
          changeDetails: body.changeDetails || {},
          approvalFlow: {
            flowId: generateId(),
            flowName: '组织变更审批流程',
            currentStep: 1,
            totalSteps: 3,
            steps: [
              {
                stepId: '1',
                stepName: '部门负责人审批',
                stepOrder: 1,
                approverType: 'USER',
                approvers: ['10003'] // 人事处负责人
              },
              {
                stepId: '2',
                stepName: '分管领导审批',
                stepOrder: 2,
                approverType: 'USER',
                approvers: ['10002']
              },
              {
                stepId: '3',
                stepName: '校长审批',
                stepOrder: 3,
                approverType: 'USER',
                approvers: ['10001']
              }
            ]
          }
        }

        const created = changeRequestService.create(changeRequest)

        const response = success(created, '提交变更申请成功')
        console.log('Response for /api/organization/change-request:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/organization/change-request:', error)
        return error('提交变更申请失败', 500)
      }
    }
  },

  // 获取组织统计信息
  {
    url: '/api/organization/statistics',
    method: 'get',
    response: async () => {
      await delay()
      ensureInitialized()

      try {
        const organizations = organizationService.readAll()
        const changeRequests = changeRequestService.readAll()

        // 按类型统计
        const typeStats = new Map<string, number>()
        organizations.forEach(org => {
          typeStats.set(org.institutionType, (typeStats.get(org.institutionType) || 0) + 1)
        })

        const typeDistribution = Array.from(typeStats.entries()).map(([type, count]) => ({
          type,
          count,
          percentage: Math.round((count / organizations.length) * 100)
        }))

        // 按层级统计
        const levelStats = new Map<number, number>()
        organizations.forEach(org => {
          const level = org.level || 0
          levelStats.set(level, (levelStats.get(level) || 0) + 1)
        })

        const levelDistribution = Array.from(levelStats.entries()).map(([level, count]) => ({
          level,
          count,
          percentage: Math.round((count / organizations.length) * 100)
        }))

        // 统计待审批数
        const pendingApprovals = changeRequests.filter(
          req => req.status === 'PENDING' || req.status === 'APPROVING'
        ).length

        const statistics = {
          totalCount: organizations.length,
          activeCount: organizations.filter(org => org.status === 'ACTIVE').length,
          inactiveCount: organizations.filter(org => org.status !== 'ACTIVE').length,
          typeDistribution,
          levelDistribution,
          recentChanges: changeLogService.readAll().length,
          pendingApprovals
        }

        const response = success(statistics, '获取统计信息成功')
        console.log('Response for /api/organization/statistics:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/organization/statistics:', error)
        return error('获取统计信息失败', 500)
      }
    }
  },

  // 批量导入组织
  {
    url: '/api/organization/import',
    method: 'post',
    response: async ({ body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { data } = body

        if (!Array.isArray(data) || data.length === 0) {
          return error('导入数据不能为空', 400)
        }

        const results = {
          success: 0,
          failed: 0,
          errors: [] as Array<{ row: number; field?: string; message: string }>
        }

        for (let i = 0; i < data.length; i++) {
          const row = data[i]

          // 验证必填字段
          if (!row.institutionName || !row.institutionCode || !row.institutionType) {
            results.failed++
            results.errors.push({
              row: i + 1,
              message: '缺少必填字段'
            })
            continue
          }

          // 查找父组织
          let parentOrg = null
          if (row.parentCode) {
            const organizations = organizationService.readAll()
            parentOrg = organizations.find(org => org.institutionCode === row.parentCode)

            if (!parentOrg) {
              results.failed++
              results.errors.push({
                row: i + 1,
                message: `父组织编码 ${row.parentCode} 不存在`
              })
              continue
            }
          }

          // 创建组织
          const newOrg: Organization = {
            institutionId: generateId(),
            institutionName: row.institutionName,
            institutionCode: row.institutionCode,
            institutionType: row.institutionType,
            parentInstitutionId: parentOrg?.institutionId,
            parentInstitutionName: parentOrg?.institutionName,
            status: 'ACTIVE' as OrganizationStatus,
            level: parentOrg ? (parentOrg.level || 0) + 1 : 0,
            path: parentOrg ? `${parentOrg.path}/${generateId()}` : generateId(),
            sortOrder: row.sortOrder || 999,
            establishDate: row.establishDate || new Date().toISOString().split('T')[0],
            effectiveDate: row.effectiveDate || new Date().toISOString().split('T')[0],
            description: row.description,
            contactPhone: row.contactPhone,
            email: row.email,
            officeAddress: row.officeAddress,
            version: 1,
            leaderName: row.leaderName,
            employeeCount: 0,
            establishmentCount: row.establishmentCount || 0,
            actualCount: 0,
            hasChildren: false,
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          }

          organizationService.create(newOrg)
          results.success++
        }

        const response = success(
          results,
          `导入完成：成功${results.success}条，失败${results.failed}条`
        )
        console.log('Response for /api/organization/import:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/organization/import:', error)
        return error('导入失败', 500)
      }
    }
  },

  // 导出组织架构
  {
    url: '/api/organization/export',
    method: 'post',
    response: async ({ body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { format = 'excel', includeChildren = true } = body

        let organizations = organizationService.readAll()

        // 如果需要层级结构
        if (includeChildren) {
          const tree = buildOrganizationTree(organizations)
          organizations = flattenOrganizationTree(tree)
        }

        // 模拟文件生成
        const fileUrl = `/downloads/organization_export_${Date.now()}.${format === 'excel' ? 'xlsx' : 'csv'}`

        const response = success(
          {
            fileUrl,
            fileName: `组织架构_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : 'csv'}`,
            fileSize: Math.floor(Math.random() * 1000000) + 100000,
            recordCount: organizations.length
          },
          '导出成功'
        )

        console.log('Response for /api/organization/export:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/organization/export:', error)
        return error('导出失败', 500)
      }
    }
  },

  // 获取总体统计数据
  {
    url: '/api/organization/statistics/overall',
    method: 'get',
    response: async () => {
      await delay()
      ensureInitialized()

      try {
        const organizations = organizationService.readAll()

        // 计算总体统计
        let totalStaff = 0
        let totalApproved = 0

        organizations.forEach(org => {
          totalStaff += org.actualCount || 0
          totalApproved += org.establishmentCount || 0
        })

        const usageRate = totalApproved > 0 ? (totalStaff / totalApproved) * 100 : 0

        const response = success(
          {
            total: organizations.length,
            totalStaff,
            totalApproved,
            usageRate: Math.round(usageRate * 10) / 10, // 保留一位小数
            activeCount: organizations.filter(org => org.status === 'ACTIVE').length,
            disabledCount: organizations.filter(org => org.status === 'DISABLED').length
          },
          '获取总体统计成功'
        )

        console.log('Response for /api/organization/statistics/overall:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/organization/statistics/overall:', err)
        return error('获取统计数据失败', 500)
      }
    }
  },

  // 获取部门统计数据（按类型分布）
  {
    url: '/api/organization/statistics/department',
    method: 'get',
    response: async () => {
      await delay()
      ensureInitialized()

      try {
        const organizations = organizationService.readAll()

        // 按类型统计
        const typeDistribution = [
          { type: 'college', name: '学院', count: 0 },
          { type: 'department', name: '部门', count: 0 },
          { type: 'office', name: '处室', count: 0 },
          { type: 'center', name: '中心', count: 0 },
          { type: 'institute', name: '研究所', count: 0 }
        ]

        organizations.forEach(org => {
          switch (org.institutionType) {
            case 'COLLEGE':
              typeDistribution[0].count++
              break
            case 'DEPARTMENT':
            case 'ADMIN_DEPT':
            case 'TEACHING_DEPT':
              typeDistribution[1].count++
              break
            case 'OFFICE':
              typeDistribution[2].count++
              break
            case 'CENTER':
              typeDistribution[3].count++
              break
            case 'INSTITUTE':
              typeDistribution[4].count++
              break
          }
        })

        const response = success(
          {
            typeDistribution,
            totalTypes: typeDistribution.filter(t => t.count > 0).length,
            lastUpdateTime: new Date().toISOString()
          },
          '获取部门统计成功'
        )

        console.log('Response for /api/organization/statistics/department:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/organization/statistics/department:', err)
        return error('获取部门统计失败', 500)
      }
    }
  },

  // 获取编制统计数据
  {
    url: '/api/organization/statistics/establishment',
    method: 'get',
    response: async () => {
      await delay()
      ensureInitialized()

      try {
        const organizations = organizationService.readAll()

        // 获取主要部门的编制情况
        const departments = organizations
          .filter(org => org.institutionType === 'COLLEGE' || org.level === 1)
          .slice(0, 10) // 取前10个
          .map(org => ({
            id: org.institutionId,
            name: org.institutionName,
            approved: org.establishmentCount || 0,
            current: org.actualCount || 0,
            vacancy: Math.max(0, (org.establishmentCount || 0) - (org.actualCount || 0)),
            usageRate: org.establishmentCount
              ? Math.round(((org.actualCount || 0) / org.establishmentCount) * 100)
              : 0
          }))
          .sort((a, b) => b.approved - a.approved) // 按核定编制降序

        // 计算汇总数据
        const summary = {
          totalDepartments: departments.length,
          totalApproved: departments.reduce((sum, d) => sum + d.approved, 0),
          totalCurrent: departments.reduce((sum, d) => sum + d.current, 0),
          totalVacancy: departments.reduce((sum, d) => sum + d.vacancy, 0),
          overStaffedCount: departments.filter(d => d.current > d.approved).length,
          fullyStaffedCount: departments.filter(d => d.current === d.approved).length,
          underStaffedCount: departments.filter(d => d.current < d.approved).length
        }

        const response = success(
          {
            departments,
            summary,
            lastUpdateTime: new Date().toISOString()
          },
          '获取编制统计成功'
        )

        console.log('Response for /api/organization/statistics/establishment:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/organization/statistics/establishment:', err)
        return error('获取编制统计失败', 500)
      }
    }
  },

  // 获取部门列表（简化接口，用于下拉选择等）
  {
    url: '/api/department/list',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { page = 1, size = 20, name, status = 'ACTIVE' } = query || {}

        let organizations = organizationService.readAll()

        // 过滤部门类型的组织
        organizations = organizations.filter(
          org =>
            org.institutionType === 'DEPARTMENT' ||
            org.institutionType === 'ADMIN_DEPT' ||
            org.institutionType === 'TEACHING_DEPT' ||
            org.institutionType === 'COLLEGE'
        )

        // 按状态过滤
        if (status) {
          organizations = organizations.filter(org => org.status === status)
        }

        // 按名称搜索
        if (name) {
          organizations = organizations.filter(org => org.institutionName.includes(name))
        }

        // 转换为简化格式
        const departments = organizations.map(org => ({
          id: org.institutionId,
          code: org.institutionCode,
          name: org.institutionName,
          parentId: org.parentInstitutionId,
          parentName: org.parentInstitutionName,
          type: org.institutionType,
          status: org.status,
          level: org.level,
          leaderName: org.leaderName,
          employeeCount: org.actualCount || 0,
          establishmentCount: org.establishmentCount || 0
        }))

        // 分页处理
        const total = departments.length
        const startIndex = (page - 1) * size
        const endIndex = startIndex + size
        const list = departments.slice(startIndex, endIndex)

        const response = success(
          {
            list,
            total,
            page: Number(page),
            size: Number(size),
            pages: Math.ceil(total / size)
          },
          '获取部门列表成功'
        )

        console.log('Response for /api/department/list:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/department/list:', err)
        return error('获取部门列表失败', 500)
      }
    }
  }
] as MockMethod[]
