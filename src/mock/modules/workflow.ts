/**
 * @name 工作流Mock模块
 * @description 提供工作流引擎相关的Mock接口
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import { MockServiceManager } from '../service'
import { mockResponse, paginate } from '../utils'
import { WorkflowGenerator } from '../generators/WorkflowGenerator'

// 定义请求参数接口，使用防御性编程
interface MockRequestOptions {
  query?: Record<string, any>
  body?: Record<string, any>
  params?: Record<string, any>
  headers?: Record<string, any>
}

// 初始化Mock服务
const processDefinitionService = MockServiceManager.getService('workflow_process_definitions')
const processInstanceService = MockServiceManager.getService('workflow_process_instances')
const taskService = MockServiceManager.getService('workflow_tasks')
const approvalHistoryService = MockServiceManager.getService('workflow_approval_history')
const activityHistoryService = MockServiceManager.getService('workflow_activity_history')

// 检查是否需要初始化数据
let testData: ReturnType<typeof WorkflowGenerator.generateTestDataset>
if (processDefinitionService.count() === 0) {
  // 生成初始数据
  testData = WorkflowGenerator.generateTestDataset()
  
  // 初始化流程定义
  testData.processDefinitions.forEach(def => processDefinitionService.create(def))
  
  // 初始化流程实例
  testData.processInstances.forEach(instance => processInstanceService.create(instance))
  
  // 初始化任务
  testData.tasks.forEach(task => taskService.create(task))
  
  // 初始化审批历史
  testData.approvalHistories.forEach(history => approvalHistoryService.create(history))
  
  // 初始化活动历史
  testData.activityHistories.forEach(activity => activityHistoryService.create(activity))
} else {
  // 如果数据已存在，创建空的testData结构
  testData = {
    processDefinitions: [],
    processInstances: [],
    tasks: [],
    approvalHistories: [],
    activityHistories: [],
    processNodes: {}
  }
}

const workflowMock: MockMethod[] = [
  // ==================== 流程定义管理 ====================
  
  // 获取流程定义列表
  {
    url: '/api/workflow/definitions',
    method: 'get',
    timeout: 500,
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, category, key, name, isActive } = query || {}
      
      let definitions = processDefinitionService.readAll()
      
      // 过滤
      if (category) {
        definitions = definitions.filter((d: any) => d.category === category)
      }
      if (key) {
        definitions = definitions.filter((d: any) => d.key.includes(key))
      }
      if (name) {
        definitions = definitions.filter((d: any) => d.name.includes(name))
      }
      if (isActive !== undefined) {
        definitions = definitions.filter((d: any) => d.isActive === (isActive === 'true'))
      }
      
      // 排序 - 按版本降序
      definitions.sort((a: any, b: any) => b.version - a.version)
      
      const result = paginate(definitions, Number(page), Number(size))
      return mockResponse.success(result)
    }
  },
  
  // 获取流程定义详情
  {
    url: '/api/workflow/definitions/:id',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      const { id } = params || {}
      const definition = processDefinitionService.read(id)
      
      if (definition) {
        // 添加流程节点信息
        const nodes = testData.processNodes[definition.key] || WorkflowGenerator.generateProcessNodes(definition.key)
        return mockResponse.success({
          ...definition,
          nodes
        })
      }
      
      return mockResponse.error('流程定义不存在', 404)
    }
  },
  
  // 部署流程定义
  {
    url: '/api/workflow/definitions/deploy',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { name, key, category, bpmnXml } = body || {}
      
      if (!name || !key || !bpmnXml) {
        return mockResponse.error('流程名称、标识和BPMN文件不能为空')
      }
      
      // 查找该key的最高版本
      const existingDefs = processDefinitionService.findBy('key', key)
      const maxVersion = Math.max(0, ...existingDefs.map((d: any) => d.version))
      
      const newDefinition = {
        ...WorkflowGenerator.generateProcessDefinition(),
        name,
        key,
        category: category || '其他',
        version: maxVersion + 1,
        isActive: true
      }
      
      // 将旧版本设为非激活
      existingDefs.forEach((def: any) => {
        processDefinitionService.update(def.id, { isActive: false })
      })
      
      const created = processDefinitionService.create(newDefinition)
      return mockResponse.success(created, '流程部署成功')
    }
  },
  
  // 激活/暂停流程定义
  {
    url: '/api/workflow/definitions/:id/toggle',
    method: 'put',
    response: ({ params }: MockRequestOptions) => {
      const { id } = params || {}
      const definition = processDefinitionService.read(id)
      
      if (!definition) {
        return mockResponse.error('流程定义不存在', 404)
      }
      
      const updated = processDefinitionService.update(id, {
        suspensionState: definition.suspensionState === 1 ? 2 : 1
      })
      
      return mockResponse.success(updated, definition.suspensionState === 1 ? '流程已暂停' : '流程已激活')
    }
  },
  
  // ==================== 流程实例管理 ====================
  
  // 启动流程实例
  {
    url: '/api/workflow/instances/start',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { processDefinitionKey, businessKey, variables } = body || {}
      
      if (!processDefinitionKey) {
        return mockResponse.error('流程定义标识不能为空')
      }
      
      // 查找激活的流程定义
      const definition = processDefinitionService.find({
        filters: { key: processDefinitionKey, isActive: true }
      })[0]
      
      if (!definition) {
        return mockResponse.error('未找到激活的流程定义', 404)
      }
      
      const instance = WorkflowGenerator.generateProcessInstance(definition.id, processDefinitionKey)
      instance.businessKey = businessKey || instance.businessKey
      instance.variables = { ...instance.variables, ...variables }
      instance.status = '运行中'
      
      const created = processInstanceService.create(instance)
      
      // 创建第一个任务
      const firstTask = WorkflowGenerator.generateTask(created.id, processDefinitionKey, 1)
      firstTask.status = '待处理'
      taskService.create(firstTask)
      
      return mockResponse.success({
        instance: created,
        firstTask
      }, '流程启动成功')
    }
  },
  
  // 获取流程实例列表
  {
    url: '/api/workflow/instances',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, status, processDefinitionKey, startUserId } = query || {}
      
      let instances = processInstanceService.readAll()
      
      // 过滤
      if (status) {
        instances = instances.filter((i: any) => i.status === status)
      }
      if (processDefinitionKey) {
        instances = instances.filter((i: any) => i.processDefinitionKey === processDefinitionKey)
      }
      if (startUserId) {
        instances = instances.filter((i: any) => i.startUserId === startUserId)
      }
      
      // 排序 - 按开始时间降序
      instances.sort((a: any, b: any) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())
      
      const result = paginate(instances, Number(page), Number(size))
      return mockResponse.success(result)
    }
  },
  
  // 获取流程实例详情
  {
    url: '/api/workflow/instances/:id',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      const { id } = params || {}
      const instance = processInstanceService.read(id)
      
      if (!instance) {
        return mockResponse.error('流程实例不存在', 404)
      }
      
      // 获取相关数据
      const tasks = taskService.findBy('processInstanceId', id)
      const activities = activityHistoryService.findBy('processInstanceId', id)
      const approvals = approvalHistoryService.findBy('processInstanceId', id)
      
      return mockResponse.success({
        ...instance,
        tasks,
        activities,
        approvals
      })
    }
  },
  
  // 暂停/恢复流程实例
  {
    url: '/api/workflow/instances/:id/toggle',
    method: 'put',
    response: ({ params }: MockRequestOptions) => {
      const { id } = params || {}
      const instance = processInstanceService.read(id)
      
      if (!instance) {
        return mockResponse.error('流程实例不存在', 404)
      }
      
      if (instance.status === '已完成' || instance.status === '已终止') {
        return mockResponse.error('已结束的流程无法操作')
      }
      
      const newStatus = instance.status === '运行中' ? '已暂停' : '运行中'
      const updated = processInstanceService.update(id, { status: newStatus })
      
      return mockResponse.success(updated, newStatus === '已暂停' ? '流程已暂停' : '流程已恢复')
    }
  },
  
  // 终止流程实例
  {
    url: '/api/workflow/instances/:id/terminate',
    method: 'delete',
    response: ({ params, body }: MockRequestOptions) => {
      const { id } = params || {}
      const { reason } = body || {}
      const instance = processInstanceService.read(id)
      
      if (!instance) {
        return mockResponse.error('流程实例不存在', 404)
      }
      
      if (instance.status === '已完成' || instance.status === '已终止') {
        return mockResponse.error('流程已经结束')
      }
      
      const updated = processInstanceService.update(id, {
        status: '已终止',
        endTime: new Date().toISOString(),
        deleteReason: reason || '手动终止'
      })
      
      // 取消所有待办任务
      const tasks = taskService.findBy('processInstanceId', id)
      tasks.forEach((task: any) => {
        if (task.status === '待处理' || task.status === '处理中') {
          taskService.update(task.id, { status: '已超时' })
        }
      })
      
      return mockResponse.success(updated, '流程已终止')
    }
  },
  
  // ==================== 任务管理 ====================
  
  // 获取待办任务列表
  {
    url: '/api/workflow/tasks/todo',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, assignee, processDefinitionKey } = query || {}
      
      let tasks = taskService.find({
        filters: { status: '待处理' }
      })
      
      // 如果指定了处理人，还要包含已认领的任务
      if (assignee) {
        const assignedTasks = taskService.find({
          filters: { assignee, status: '处理中' }
        })
        tasks = [...tasks, ...assignedTasks]
      }
      
      if (processDefinitionKey) {
        tasks = tasks.filter((t: any) => t.processDefinitionKey === processDefinitionKey)
      }
      
      // 排序 - 按创建时间升序（先进先出）
      tasks.sort((a: any, b: any) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime())
      
      const result = paginate(tasks, Number(page), Number(size))
      return mockResponse.success(result)
    }
  },
  
  // 获取已办任务列表
  {
    url: '/api/workflow/tasks/done',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, assignee } = query || {}
      
      let tasks = taskService.find({
        filters: { status: '已完成' }
      })
      
      if (assignee) {
        tasks = tasks.filter((t: any) => t.assignee === assignee)
      }
      
      // 排序 - 按完成时间降序
      tasks.sort((a: any, b: any) => new Date(b.completeTime || 0).getTime() - new Date(a.completeTime || 0).getTime())
      
      const result = paginate(tasks, Number(page), Number(size))
      return mockResponse.success(result)
    }
  },
  
  // 认领任务
  {
    url: '/api/workflow/tasks/:id/claim',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const { id } = params || {}
      const { userId, userName } = body || {}
      const task = taskService.read(id)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      if (task.status !== '待处理') {
        return mockResponse.error('任务已被处理')
      }
      
      const updated = taskService.update(id, {
        status: '处理中',
        assignee: userId,
        assigneeName: userName,
        claimTime: new Date().toISOString()
      })
      
      return mockResponse.success(updated, '任务认领成功')
    }
  },
  
  // 完成任务
  {
    url: '/api/workflow/tasks/:id/complete',
    method: 'post',
    response: ({ params, body }: MockRequestOptions) => {
      const { id } = params || {}
      const { action = '通过', comment, variables, userId, userName } = body || {}
      const task = taskService.read(id)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      if (task.status === '已完成') {
        return mockResponse.error('任务已完成')
      }
      
      // 更新任务状态
      const updated = taskService.update(id, {
        status: '已完成',
        completeTime: new Date().toISOString(),
        assignee: task.assignee || userId,
        assigneeName: task.assigneeName || userName
      })
      
      // 记录审批历史
      const approval = {
        ...WorkflowGenerator.generateApprovalHistory(task.processInstanceId, id),
        action,
        comment: comment || '已处理',
        userId: userId || task.assignee,
        userName: userName || task.assigneeName
      }
      approvalHistoryService.create(approval)
      
      // 更新流程实例变量
      if (variables) {
        const instance = processInstanceService.read(task.processInstanceId)
        if (instance) {
          processInstanceService.update(instance.id, {
            variables: { ...instance.variables, ...variables }
          })
        }
      }
      
      // 如果是最后一个任务，结束流程
      const remainingTasks = taskService.find({
        filters: { processInstanceId: task.processInstanceId, status: '待处理' }
      })
      
      if (remainingTasks.length === 0) {
        processInstanceService.update(task.processInstanceId, {
          status: '已完成',
          endTime: new Date().toISOString()
        })
      } else if (action === '通过') {
        // 创建下一个任务
        const nextTask = WorkflowGenerator.generateTask(
          task.processInstanceId,
          task.processDefinitionKey,
          parseInt(task.taskDefinitionKey.split('_').pop() || '1') + 1
        )
        nextTask.status = '待处理'
        taskService.create(nextTask)
      }
      
      return mockResponse.success({
        task: updated,
        approval,
        nextTask: action === '通过' ? taskService.findBy('processInstanceId', task.processInstanceId).find((t: any) => t.status === '待处理') : null
      }, '任务处理成功')
    }
  },
  
  // 转办任务
  {
    url: '/api/workflow/tasks/:id/delegate',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const { id } = params || {}
      const { targetUserId, targetUserName, comment } = body || {}
      const task = taskService.read(id)
      
      if (!task) {
        return mockResponse.error('任务不存在', 404)
      }
      
      if (task.status === '已完成') {
        return mockResponse.error('已完成的任务无法转办')
      }
      
      const updated = taskService.update(id, {
        assignee: targetUserId,
        assigneeName: targetUserName,
        delegationState: 'PENDING'
      })
      
      // 记录转办历史
      const approval = {
        ...WorkflowGenerator.generateApprovalHistory(task.processInstanceId, id),
        action: '转办',
        comment: comment || `已转办给${targetUserName}`
      }
      approvalHistoryService.create(approval)
      
      return mockResponse.success(updated, '任务转办成功')
    }
  },
  
  // ==================== 流程跟踪 ====================
  
  // 获取流程图
  {
    url: '/api/workflow/instances/:id/diagram',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      const { id } = params || {}
      const instance = processInstanceService.read(id)
      
      if (!instance) {
        return mockResponse.error('流程实例不存在', 404)
      }
      
      const definition = processDefinitionService.read(instance.processDefinitionId)
      const nodes = testData.processNodes[instance.processDefinitionKey] || WorkflowGenerator.generateProcessNodes(instance.processDefinitionKey)
      const activities = activityHistoryService.findBy('processInstanceId', id)
      
      // 标记已完成和当前节点
      const completedNodeIds = activities.map((a: any) => a.activityId)
      const currentNodeId = instance.currentActivityId
      
      return mockResponse.success({
        definition,
        nodes: nodes.map(node => ({
          ...node,
          status: completedNodeIds.includes(node.id) ? 'completed' : 
                  node.id === currentNodeId ? 'active' : 'pending'
        })),
        flows: nodes.slice(0, -1).map((node, index) => ({
          id: `flow_${index}`,
          source: node.id,
          target: nodes[index + 1].id,
          type: 'sequenceFlow'
        }))
      })
    }
  },
  
  // 获取流程统计
  {
    url: '/api/workflow/statistics',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { startDate, endDate } = query || {}
      let instances = processInstanceService.readAll()
      
      // 日期过滤
      if (startDate) {
        instances = instances.filter((i: any) => new Date(i.startTime) >= new Date(startDate))
      }
      if (endDate) {
        instances = instances.filter((i: any) => new Date(i.startTime) <= new Date(endDate))
      }
      
      // 按流程类型统计
      const byProcess = instances.reduce((acc: any, instance: any) => {
        const key = instance.processDefinitionKey
        if (!acc[key]) {
          acc[key] = {
            name: instance.processDefinitionName,
            total: 0,
            completed: 0,
            running: 0,
            terminated: 0
          }
        }
        acc[key].total++
        if (instance.status === '已完成') acc[key].completed++
        if (instance.status === '运行中') acc[key].running++
        if (instance.status === '已终止') acc[key].terminated++
        return acc
      }, {} as Record<string, any>)
      
      // 按部门统计
      interface DepartmentStats {
        total: number
        avgDuration: number
      }
      const byDepartment = instances.reduce((acc: any, instance: any) => {
        const dept = instance.variables?.starterDepartment || '未知部门'
        if (!acc[dept]) {
          acc[dept] = {
            total: 0,
            avgDuration: 0
          }
        }
        acc[dept].total++
        if (instance.duration) {
          acc[dept].avgDuration = (acc[dept].avgDuration * (acc[dept].total - 1) + instance.duration) / acc[dept].total
        }
        return acc
      }, {} as Record<string, DepartmentStats>)
      
      // 任务统计
      const tasks = taskService.readAll()
      const taskStats = {
        total: tasks.length,
        pending: tasks.filter((t: any) => t.status === '待处理').length,
        processing: tasks.filter((t: any) => t.status === '处理中').length,
        completed: tasks.filter((t: any) => t.status === '已完成').length,
        overdue: tasks.filter((t: any) => t.status === '已超时').length
      }
      
      return mockResponse.success({
        overview: {
          totalInstances: instances.length,
          runningInstances: instances.filter((i: any) => i.status === '运行中').length,
          completedInstances: instances.filter((i: any) => i.status === '已完成').length,
          avgDuration: instances
            .filter((i: any) => i.duration)
            .reduce((sum: any, i: any) => sum + (i.duration || 0), 0) / instances.filter((i: any) => i.duration).length || 0
        },
        byProcess: Object.values(byProcess),
        byDepartment: Object.keys(byDepartment).map(name => ({ 
          name, 
          total: byDepartment[name].total,
          avgDuration: byDepartment[name].avgDuration
        })),
        taskStats
      })
    }
  }
]

export default workflowMock