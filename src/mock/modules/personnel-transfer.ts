/**
 * @name 人员调动管理Mock模块
 * @description 提供人员调动管理相关的Mock接口，包括调动申请、审批、影响分析、薪酬调整、工作交接等功能
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import type { MockRequestOptions } from '../types'
import { faker } from '@faker-js/faker/locale/zh_CN'
import { success, error, paginate, delay, generateId, logger } from '../utils'
import { createSimpleMockService } from '../service/SimpleMockService'

// 定义人员调动相关类型
interface TransferApplication {
  id?: string
  adjustmentId: string
  employeeId: string
  employeeName: string
  oldInstitutionId: string
  oldInstitutionName: string
  oldPositionId: string
  oldPositionName: string
  newInstitutionId: string
  newInstitutionName: string
  newPositionId: string
  newPositionName: string
  adjustmentReason: string
  applicationStatus: string
  currentApproverRole: string
  // 调出单位审核
  outgoingUnitReviewOpinion?: string
  outgoingUnitReviewerId?: string
  outgoingUnitReviewerName?: string
  outgoingUnitReviewTime?: string
  // 调入单位审核
  incomingUnitReviewOpinion?: string
  incomingUnitReviewerId?: string
  incomingUnitReviewerName?: string
  incomingUnitReviewTime?: string
  // 人事处审核
  hrReviewOpinion?: string
  hrReviewerId?: string
  hrReviewerName?: string
  hrReviewTime?: string
  // 申请信息
  applicationDate: string
  attachmentUrls?: string[]
  // 扩展信息
  impactAnalysis?: TransferImpactAnalysis
  positionMatchAssessment?: PositionMatchAssessment
  salaryAdjustment?: SalaryAdjustment
  workHandover?: WorkHandover
}

// 调动影响分析
interface TransferImpactAnalysis {
  id?: string
  analysisId: string
  applicationId: string
  analysisTime: string
  // 影响维度
  departmentImpact: {
    oldDepartment: {
      projectsAffected: number
      tasksToReassign: number
      estimatedDelay: number // 天数
      riskLevel: 'HIGH' | 'MEDIUM' | 'LOW'
    }
    newDepartment: {
      workloadIncrease: number // 百分比
      teamSizeChange: number
      onboardingTime: number // 天数
    }
  }
  personalImpact: {
    salaryChange: number // 金额
    careerPathAlignment: 'POSITIVE' | 'NEUTRAL' | 'NEGATIVE'
    skillDevelopment: string[]
  }
  businessImpact: {
    contractsAffected: number
    permissionsToUpdate: number
    trainingRequired: boolean
  }
  overallRiskLevel: 'HIGH' | 'MEDIUM' | 'LOW'
  recommendations: string[]
}

// 岗位匹配度评估
interface PositionMatchAssessment {
  id?: string
  assessmentId: string
  applicationId: string
  assessmentTime: string
  // 匹配度评分
  overallScore: number // 0-100
  dimensions: {
    education: { required: string; actual: string; score: number }
    experience: { required: number; actual: number; score: number }
    skills: { required: string[]; matched: string[]; score: number }
    certificates: { required: string[]; matched: string[]; score: number }
  }
  strengths: string[]
  gaps: string[]
  recommendations: string[]
  assessorId: string
  assessorName: string
}

// 薪酬调整
interface SalaryAdjustment {
  id?: string
  adjustmentId: string
  applicationId: string
  // 调整前
  oldSalary: {
    basic: number
    allowance: number
    bonus: number
    total: number
  }
  // 调整后
  newSalary: {
    basic: number
    allowance: number
    bonus: number
    total: number
  }
  adjustmentAmount: number
  adjustmentPercentage: number
  adjustmentReason: string
  effectiveDate: string
  approvalStatus: string
  approvedBy?: string
  approvedDate?: string
}

// 工作交接
interface WorkHandover {
  id?: string
  handoverId: string
  applicationId: string
  // 交接清单
  checklist: HandoverItem[]
  overallProgress: number // 0-100
  status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'VERIFIED'
  // 评估
  qualityAssessment?: {
    score: number // 1-5
    feedback: string
    assessedBy: string
    assessedDate: string
  }
  // 知识资产
  knowledgeAssets?: {
    documents: number
    uploaded: number
    critical: string[]
  }
  completedDate?: string
}

// 交接项
interface HandoverItem {
  itemId: string
  category: 'DOCUMENT' | 'TASK' | 'PROJECT' | 'ASSET' | 'PERMISSION' | 'KNOWLEDGE'
  name: string
  description: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'VERIFIED'
  assignee: string
  dueDate: string
  completedDate?: string
  notes?: string
}

// 创建持久化服务
const transferService = createSimpleMockService<TransferApplication>('transferApplications')
const impactAnalysisService =
  createSimpleMockService<TransferImpactAnalysis>('transferImpactAnalysis')
const matchAssessmentService = createSimpleMockService<PositionMatchAssessment>(
  'positionMatchAssessments'
)
const salaryAdjustmentService = createSimpleMockService<SalaryAdjustment>('salaryAdjustments')
const workHandoverService = createSimpleMockService<WorkHandover>('workHandovers')

// 初始化标志
let isInitialized = false

// 确保数据初始化
const ensureInitialized = () => {
  if (!isInitialized) {
    try {
      initTransferData()
      isInitialized = true
      console.log('人员调动Mock数据初始化成功')
    } catch (error) {
      console.error('人员调动Mock数据初始化失败:', error)
    }
  }
}

// 初始化示例数据
const initTransferData = () => {
  const existingData = transferService.readAll()
  if (existingData.length > 0) {
    console.log('人员调动数据已存在，跳过初始化')
    return
  }

  // 创建一些示例调动申请
  const applications: TransferApplication[] = [
    {
      adjustmentId: '1',
      employeeId: '10051',
      employeeName: '周敏',
      oldInstitutionId: '51',
      oldInstitutionName: '计算机系',
      oldPositionId: 'P203',
      oldPositionName: '系主任',
      newInstitutionId: '52',
      newInstitutionName: '软件工程系',
      newPositionId: 'P203',
      newPositionName: '系主任',
      adjustmentReason: '响应学院发展需要，充分发挥个人专业特长',
      applicationStatus: '已通过',
      currentApproverRole: '',
      outgoingUnitReviewOpinion: '同意调动，该同志工作认真负责，望在新岗位继续发挥作用',
      outgoingUnitReviewerId: '10005',
      outgoingUnitReviewerName: '刘强',
      outgoingUnitReviewTime: '2024-12-01T10:30:00.000Z',
      incomingUnitReviewOpinion: '欢迎周敏同志加入我系，相信能为系部发展做出贡献',
      incomingUnitReviewerId: '10052',
      incomingUnitReviewerName: '吴华',
      incomingUnitReviewTime: '2024-12-02T14:20:00.000Z',
      hrReviewOpinion: '经审核，符合调动条件，同意调动',
      hrReviewerId: '10003',
      hrReviewerName: '王芳',
      hrReviewTime: '2024-12-03T09:00:00.000Z',
      applicationDate: '2024-11-28T08:00:00.000Z'
    },
    {
      adjustmentId: '2',
      employeeId: '20001',
      employeeName: '李娜',
      oldInstitutionId: '4',
      oldInstitutionName: '教务处',
      oldPositionId: 'P104',
      oldPositionName: '科员',
      newInstitutionId: '3',
      newInstitutionName: '人事处',
      newPositionId: 'P104',
      newPositionName: '科员',
      adjustmentReason: '个人职业发展需要，希望在人事管理领域深入发展',
      applicationStatus: '待人事处审核',
      currentApproverRole: '人事处管理员',
      outgoingUnitReviewOpinion: '该同志工作踏实，同意其调动申请',
      outgoingUnitReviewerId: '10004',
      outgoingUnitReviewerName: '孙杰',
      outgoingUnitReviewTime: '2025-01-20T15:30:00.000Z',
      incomingUnitReviewOpinion: '经考察，该同志符合我处用人需求，同意接收',
      incomingUnitReviewerId: '10003',
      incomingUnitReviewerName: '王芳',
      incomingUnitReviewTime: '2025-01-22T10:00:00.000Z',
      applicationDate: '2025-01-18T09:00:00.000Z'
    },
    {
      adjustmentId: '3',
      employeeId: '30001',
      employeeName: '张磊',
      oldInstitutionId: '6',
      oldInstitutionName: '机电工程学院',
      oldPositionId: 'P204',
      oldPositionName: '专任教师',
      newInstitutionId: '5',
      newInstitutionName: '信息工程学院',
      newPositionId: 'P204',
      newPositionName: '专任教师',
      adjustmentReason: '跨学科发展，结合机电与信息技术开展教学科研',
      applicationStatus: '待调入单位审核',
      currentApproverRole: '调入单位负责人',
      outgoingUnitReviewOpinion: '支持教师跨学科发展，同意调动',
      outgoingUnitReviewerId: '10006',
      outgoingUnitReviewerName: '陈晓',
      outgoingUnitReviewTime: '2025-01-23T16:00:00.000Z',
      applicationDate: '2025-01-22T14:00:00.000Z'
    }
  ]

  // 保存申请数据
  applications.forEach(app => transferService.create(app))

  // 为已完成的申请创建相关数据
  const completedApp = applications[0]

  // 创建影响分析
  const impact: TransferImpactAnalysis = {
    analysisId: generateId(),
    applicationId: completedApp.adjustmentId,
    analysisTime: '2024-11-29T10:00:00.000Z',
    departmentImpact: {
      oldDepartment: {
        projectsAffected: 3,
        tasksToReassign: 12,
        estimatedDelay: 7,
        riskLevel: 'MEDIUM'
      },
      newDepartment: {
        workloadIncrease: 15,
        teamSizeChange: 1,
        onboardingTime: 14
      }
    },
    personalImpact: {
      salaryChange: 0,
      careerPathAlignment: 'POSITIVE',
      skillDevelopment: ['软件工程管理', '团队建设', '项目管理']
    },
    businessImpact: {
      contractsAffected: 0,
      permissionsToUpdate: 5,
      trainingRequired: true
    },
    overallRiskLevel: 'MEDIUM',
    recommendations: [
      '建议提前一个月进行工作交接',
      '安排系主任岗位培训',
      '与原部门协调项目过渡方案'
    ]
  }
  impactAnalysisService.create(impact)

  // 创建岗位匹配度评估
  const assessment: PositionMatchAssessment = {
    assessmentId: generateId(),
    applicationId: completedApp.adjustmentId,
    assessmentTime: '2024-11-29T14:00:00.000Z',
    overallScore: 85,
    dimensions: {
      education: {
        required: '博士',
        actual: '博士',
        score: 100
      },
      experience: {
        required: 5,
        actual: 8,
        score: 100
      },
      skills: {
        required: ['教学管理', '科研管理', '团队管理'],
        matched: ['教学管理', '科研管理'],
        score: 67
      },
      certificates: {
        required: ['高校教师资格证'],
        matched: ['高校教师资格证'],
        score: 100
      }
    },
    strengths: ['丰富的教学经验', '较强的科研能力', '良好的沟通协调能力'],
    gaps: ['团队管理经验需要加强'],
    recommendations: ['建议参加管理能力提升培训', '安排经验丰富的领导进行指导'],
    assessorId: '10003',
    assessorName: '王芳'
  }
  matchAssessmentService.create(assessment)

  // 创建工作交接记录
  const handover: WorkHandover = {
    handoverId: generateId(),
    applicationId: completedApp.adjustmentId,
    checklist: [
      {
        itemId: '1',
        category: 'DOCUMENT',
        name: '教学文档',
        description: '课程大纲、教案、考试材料等',
        status: 'COMPLETED',
        assignee: '周敏',
        dueDate: '2024-12-10',
        completedDate: '2024-12-08'
      },
      {
        itemId: '2',
        category: 'PROJECT',
        name: '在研项目',
        description: '省级科研项目2项，校级项目1项',
        status: 'COMPLETED',
        assignee: '周敏',
        dueDate: '2024-12-15',
        completedDate: '2024-12-12'
      },
      {
        itemId: '3',
        category: 'TASK',
        name: '日常管理工作',
        description: '系部会议记录、学生管理、教师考核等',
        status: 'COMPLETED',
        assignee: '周敏',
        dueDate: '2024-12-20',
        completedDate: '2024-12-18'
      },
      {
        itemId: '4',
        category: 'PERMISSION',
        name: '系统权限',
        description: '教务系统、OA系统等权限移交',
        status: 'COMPLETED',
        assignee: 'IT部门',
        dueDate: '2024-12-25',
        completedDate: '2024-12-23'
      }
    ],
    overallProgress: 100,
    status: 'VERIFIED',
    qualityAssessment: {
      score: 4.5,
      feedback: '交接工作完成良好，文档齐全，过渡平稳',
      assessedBy: '刘强',
      assessedDate: '2024-12-25'
    },
    knowledgeAssets: {
      documents: 45,
      uploaded: 45,
      critical: ['系部发展规划', '教学质量报告', '科研项目申报书']
    },
    completedDate: '2024-12-25'
  }
  workHandoverService.create(handover)

  console.log(`初始化${applications.length}条人员调动申请数据`)
}

// 生成影响分析
const generateImpactAnalysis = (application: TransferApplication): TransferImpactAnalysis => {
  return {
    analysisId: generateId(),
    applicationId: application.adjustmentId,
    analysisTime: new Date().toISOString(),
    departmentImpact: {
      oldDepartment: {
        projectsAffected: faker.number.int({ min: 0, max: 10 }),
        tasksToReassign: faker.number.int({ min: 5, max: 30 }),
        estimatedDelay: faker.number.int({ min: 0, max: 30 }),
        riskLevel: faker.helpers.arrayElement(['HIGH', 'MEDIUM', 'LOW'])
      },
      newDepartment: {
        workloadIncrease: faker.number.int({ min: 5, max: 30 }),
        teamSizeChange: 1,
        onboardingTime: faker.number.int({ min: 7, max: 30 })
      }
    },
    personalImpact: {
      salaryChange: faker.number.int({ min: -5000, max: 10000 }),
      careerPathAlignment: faker.helpers.arrayElement(['POSITIVE', 'NEUTRAL', 'NEGATIVE']),
      skillDevelopment: faker.helpers.arrayElements(
        ['领导力', '项目管理', '跨部门协作', '专业技能', '沟通能力'],
        { min: 1, max: 3 }
      )
    },
    businessImpact: {
      contractsAffected: faker.number.int({ min: 0, max: 5 }),
      permissionsToUpdate: faker.number.int({ min: 3, max: 20 }),
      trainingRequired: faker.datatype.boolean()
    },
    overallRiskLevel: faker.helpers.arrayElement(['HIGH', 'MEDIUM', 'LOW']),
    recommendations: ['建议提前进行工作交接', '安排必要的岗位培训', '与相关部门协调过渡方案']
  }
}

// 生成岗位匹配度评估
const generateMatchAssessment = (application: TransferApplication): PositionMatchAssessment => {
  const educationScore = faker.number.int({ min: 70, max: 100 })
  const experienceScore = faker.number.int({ min: 60, max: 100 })
  const skillsScore = faker.number.int({ min: 50, max: 100 })
  const certificatesScore = faker.number.int({ min: 80, max: 100 })

  const overallScore = Math.round(
    (educationScore + experienceScore + skillsScore + certificatesScore) / 4
  )

  return {
    assessmentId: generateId(),
    applicationId: application.adjustmentId,
    assessmentTime: new Date().toISOString(),
    overallScore,
    dimensions: {
      education: {
        required: faker.helpers.arrayElement(['博士', '硕士', '本科']),
        actual: faker.helpers.arrayElement(['博士', '硕士', '本科']),
        score: educationScore
      },
      experience: {
        required: faker.number.int({ min: 3, max: 10 }),
        actual: faker.number.int({ min: 1, max: 15 }),
        score: experienceScore
      },
      skills: {
        required: ['专业技能', '管理能力', '沟通协调'],
        matched: ['专业技能', '沟通协调'],
        score: skillsScore
      },
      certificates: {
        required: ['相关资格证书'],
        matched: ['相关资格证书'],
        score: certificatesScore
      }
    },
    strengths: faker.helpers.arrayElements(
      ['丰富的工作经验', '较强的专业能力', '良好的团队合作精神', '优秀的学习能力'],
      { min: 2, max: 3 }
    ),
    gaps: overallScore < 80 ? ['需要加强相关技能培训'] : [],
    recommendations: overallScore >= 80 ? ['建议直接上岗'] : ['建议参加岗位培训', '安排导师指导'],
    assessorId: '10003',
    assessorName: '王芳'
  }
}

// 生成薪酬调整方案
const generateSalaryAdjustment = (application: TransferApplication): SalaryAdjustment => {
  const oldBasic = faker.number.int({ min: 8000, max: 20000 })
  const oldAllowance = faker.number.int({ min: 1000, max: 5000 })
  const oldBonus = faker.number.int({ min: 2000, max: 10000 })
  const oldTotal = oldBasic + oldAllowance + oldBonus

  // 根据岗位变化计算薪酬调整
  const adjustmentPercentage = faker.number.float({ min: -10, max: 20 })
  const adjustmentAmount = Math.round((oldTotal * adjustmentPercentage) / 100)

  const newBasic = oldBasic + Math.round(adjustmentAmount * 0.7)
  const newAllowance = oldAllowance + Math.round(adjustmentAmount * 0.2)
  const newBonus = oldBonus + Math.round(adjustmentAmount * 0.1)
  const newTotal = newBasic + newAllowance + newBonus

  return {
    adjustmentId: generateId(),
    applicationId: application.adjustmentId,
    oldSalary: {
      basic: oldBasic,
      allowance: oldAllowance,
      bonus: oldBonus,
      total: oldTotal
    },
    newSalary: {
      basic: newBasic,
      allowance: newAllowance,
      bonus: newBonus,
      total: newTotal
    },
    adjustmentAmount: newTotal - oldTotal,
    adjustmentPercentage,
    adjustmentReason: '岗位调整相应薪酬调整',
    effectiveDate: faker.date.future({ years: 1 }).toISOString().split('T')[0],
    approvalStatus: '待审批'
  }
}

// 生成工作交接清单
const generateWorkHandover = (application: TransferApplication): WorkHandover => {
  const categories: Array<HandoverItem['category']> = [
    'DOCUMENT',
    'TASK',
    'PROJECT',
    'ASSET',
    'PERMISSION',
    'KNOWLEDGE'
  ]
  const items: HandoverItem[] = categories.map((category, index) => ({
    itemId: String(index + 1),
    category,
    name:
      {
        DOCUMENT: '工作文档',
        TASK: '待办任务',
        PROJECT: '在研项目',
        ASSET: '固定资产',
        PERMISSION: '系统权限',
        KNOWLEDGE: '知识文档'
      }[category] || category,
    description: faker.lorem.sentence(),
    status: 'PENDING',
    assignee: application.employeeName,
    dueDate: faker.date.future({ years: 1 }).toISOString().split('T')[0]
  }))

  return {
    handoverId: generateId(),
    applicationId: application.adjustmentId,
    checklist: items,
    overallProgress: 0,
    status: 'NOT_STARTED',
    knowledgeAssets: {
      documents: faker.number.int({ min: 10, max: 100 }),
      uploaded: 0,
      critical: []
    }
  }
}

export default [
  // 获取人员调动申请列表
  {
    url: '/api/personnel-transfer/list',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { page = 1, size = 10, status, employeeName } = query || {}

        let applications = transferService.readAll()

        // 状态筛选
        if (status) {
          applications = applications.filter(app => app.applicationStatus === status)
        }

        // 姓名搜索
        if (employeeName) {
          applications = applications.filter(app => app.employeeName.includes(employeeName))
        }

        // 按申请日期倒序
        applications.sort(
          (a, b) => new Date(b.applicationDate).getTime() - new Date(a.applicationDate).getTime()
        )

        // 分页处理
        const result = paginate(applications, Number(page), Number(size))

        const response = success(result, '获取人员调动列表成功')
        logger.response('/api/personnel-transfer/list', response)
        return response
      } catch (err: any) {
        logger.error('/api/personnel-transfer/list', err)
        return error('获取人员调动列表失败', 500)
      }
    }
  },

  // 获取调动申请详情
  {
    url: '/api/personnel-transfer/:id',
    method: 'get',
    response: async ({ params }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const application = transferService.readAll().find(app => app.adjustmentId === params?.id)

        if (!application) {
          return error('调动申请不存在', 404)
        }

        // 获取相关数据
        const impactAnalysis = impactAnalysisService
          .readAll()
          .find(analysis => analysis.applicationId === params?.id)
        const matchAssessment = matchAssessmentService
          .readAll()
          .find(assessment => assessment.applicationId === params?.id)
        const salaryAdjustment = salaryAdjustmentService
          .readAll()
          .find(adjustment => adjustment.applicationId === params?.id)
        const workHandover = workHandoverService
          .readAll()
          .find(handover => handover.applicationId === params?.id)

        const result = {
          ...application,
          impactAnalysis,
          positionMatchAssessment: matchAssessment,
          salaryAdjustment,
          workHandover
        }

        const response = success(result, '获取调动申请详情成功')
        logger.response(`/api/personnel-transfer/${params?.id}`, response)
        return response
      } catch (err: any) {
        logger.error(`/api/personnel-transfer/${params?.id}`, err)
        return error('获取调动申请详情失败', 500)
      }
    }
  },

  // 创建人员调动申请
  {
    url: '/api/personnel-transfer',
    method: 'post',
    response: async ({ body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        // 验证必填字段
        const required = [
          'employeeId',
          'employeeName',
          'oldInstitutionId',
          'newInstitutionId',
          'adjustmentReason'
        ]
        for (const field of required) {
          if (!body[field]) {
            return error(`${field} 不能为空`, 400)
          }
        }

        // 创建申请
        const application: TransferApplication = {
          adjustmentId: generateId(),
          employeeId: body.employeeId,
          employeeName: body.employeeName,
          oldInstitutionId: body.oldInstitutionId,
          oldInstitutionName: body.oldInstitutionName,
          oldPositionId: body.oldPositionId,
          oldPositionName: body.oldPositionName,
          newInstitutionId: body.newInstitutionId,
          newInstitutionName: body.newInstitutionName,
          newPositionId: body.newPositionId,
          newPositionName: body.newPositionName,
          adjustmentReason: body.adjustmentReason,
          applicationStatus: '待调出单位审核',
          currentApproverRole: '调出单位负责人',
          applicationDate: new Date().toISOString(),
          attachmentUrls: body.attachmentUrls
        }

        const created = transferService.create(application)

        // 自动生成影响分析
        const impactAnalysis = generateImpactAnalysis(created)
        impactAnalysisService.create(impactAnalysis)

        // 自动生成岗位匹配度评估
        const matchAssessment = generateMatchAssessment(created)
        matchAssessmentService.create(matchAssessment)

        const response = success(created, '创建调动申请成功')
        logger.response('/api/personnel-transfer', response)
        return response
      } catch (err: any) {
        logger.error('/api/personnel-transfer', err)
        return error('创建调动申请失败', 500)
      }
    }
  },

  // 审批人员调动申请
  {
    url: '/api/personnel-transfer/:id/approve',
    method: 'post',
    response: async ({ params, body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const application = transferService.readAll().find(app => app.adjustmentId === params?.id)

        if (!application) {
          return error('调动申请不存在', 404)
        }

        const { action, opinion, reviewerId, reviewerName } = body

        if (!action || !opinion) {
          return error('审批动作和意见不能为空', 400)
        }

        const now = new Date().toISOString()
        let updateData: Partial<TransferApplication> = {}

        // 根据当前状态处理审批
        switch (application.applicationStatus) {
          case '待调出单位审核':
            updateData = {
              outgoingUnitReviewOpinion: opinion,
              outgoingUnitReviewerId: reviewerId,
              outgoingUnitReviewerName: reviewerName,
              outgoingUnitReviewTime: now
            }

            if (action === 'approve') {
              updateData.applicationStatus = '待调入单位审核'
              updateData.currentApproverRole = '调入单位负责人'
            } else {
              updateData.applicationStatus = '已驳回'
              updateData.currentApproverRole = ''
            }
            break

          case '待调入单位审核':
            updateData = {
              incomingUnitReviewOpinion: opinion,
              incomingUnitReviewerId: reviewerId,
              incomingUnitReviewerName: reviewerName,
              incomingUnitReviewTime: now
            }

            if (action === 'approve') {
              updateData.applicationStatus = '待人事处审核'
              updateData.currentApproverRole = '人事处管理员'

              // 生成薪酬调整方案
              const salaryAdjustment = generateSalaryAdjustment(application)
              salaryAdjustmentService.create(salaryAdjustment)
            } else {
              updateData.applicationStatus = '已驳回'
              updateData.currentApproverRole = ''
            }
            break

          case '待人事处审核':
            updateData = {
              hrReviewOpinion: opinion,
              hrReviewerId: reviewerId,
              hrReviewerName: reviewerName,
              hrReviewTime: now
            }

            if (action === 'approve') {
              updateData.applicationStatus = '已通过'
              updateData.currentApproverRole = ''

              // 生成工作交接清单
              const workHandover = generateWorkHandover(application)
              workHandoverService.create(workHandover)

              // TODO: 这里应该调用员工信息更新接口，更新员工的部门和岗位信息
            } else {
              updateData.applicationStatus = '已驳回'
              updateData.currentApproverRole = ''
            }
            break

          default:
            return error('当前状态不允许审批', 400)
        }

        const id = params?.id
        if (!id) {
          return error('缺少调动ID', 400)
        }
        const updated = transferService.update(id, updateData)

        const response = success(updated, '审批成功')
        logger.response(`/api/personnel-transfer/${params?.id}/approve`, response)
        return response
      } catch (err: any) {
        logger.error(`/api/personnel-transfer/${params?.id}/approve`, err)
        return error('审批失败', 500)
      }
    }
  },

  // 获取调动影响分析
  {
    url: '/api/personnel-transfer/:id/impact-analysis',
    method: 'get',
    response: async ({ params }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const analysis = impactAnalysisService.readAll().find(a => a.applicationId === params?.id)

        if (!analysis) {
          return error('影响分析不存在', 404)
        }

        const response = success(analysis, '获取影响分析成功')
        logger.response(`/api/personnel-transfer/${params?.id}/impact-analysis`, response)
        return response
      } catch (err: any) {
        logger.error(`/api/personnel-transfer/${params?.id}/impact-analysis`, err)
        return error('获取影响分析失败', 500)
      }
    }
  },

  // 获取岗位匹配度评估
  {
    url: '/api/personnel-transfer/:id/match-assessment',
    method: 'get',
    response: async ({ params }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const assessment = matchAssessmentService
          .readAll()
          .find(a => a.applicationId === params?.id)

        if (!assessment) {
          return error('岗位匹配度评估不存在', 404)
        }

        const response = success(assessment, '获取岗位匹配度评估成功')
        logger.response(`/api/personnel-transfer/${params?.id}/match-assessment`, response)
        return response
      } catch (err: any) {
        logger.error(`/api/personnel-transfer/${params?.id}/match-assessment`, err)
        return error('获取岗位匹配度评估失败', 500)
      }
    }
  },

  // 获取薪酬调整方案
  {
    url: '/api/personnel-transfer/:id/salary-adjustment',
    method: 'get',
    response: async ({ params }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const adjustment = salaryAdjustmentService
          .readAll()
          .find(a => a.applicationId === params?.id)

        if (!adjustment) {
          return error('薪酬调整方案不存在', 404)
        }

        const response = success(adjustment, '获取薪酬调整方案成功')
        logger.response(`/api/personnel-transfer/${params?.id}/salary-adjustment`, response)
        return response
      } catch (err: any) {
        logger.error(`/api/personnel-transfer/${params?.id}/salary-adjustment`, err)
        return error('获取薪酬调整方案失败', 500)
      }
    }
  },

  // 审批薪酬调整
  {
    url: '/api/personnel-transfer/salary-adjustment/:id/approve',
    method: 'post',
    response: async ({ params, body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const adjustments = salaryAdjustmentService.readAll()
        const adjustment = adjustments.find(a => a.adjustmentId === params?.id)

        if (!adjustment) {
          return error('薪酬调整方案不存在', 404)
        }

        const { action, approvedBy } = body

        const updateData: Partial<SalaryAdjustment> = {
          approvalStatus: action === 'approve' ? '已批准' : '已拒绝',
          approvedBy,
          approvedDate: new Date().toISOString()
        }

        const id = params?.id
        if (!id) {
          return error('缺少薪酬调整ID', 400)
        }
        const updated = salaryAdjustmentService.update(id, updateData)

        const response = success(updated, '薪酬调整审批成功')
        logger.response(`/api/personnel-transfer/salary-adjustment/${params?.id}/approve`, response)
        return response
      } catch (err: any) {
        logger.error(`/api/personnel-transfer/salary-adjustment/${params?.id}/approve`, err)
        return error('薪酬调整审批失败', 500)
      }
    }
  },

  // 获取工作交接清单
  {
    url: '/api/personnel-transfer/:id/work-handover',
    method: 'get',
    response: async ({ params }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const handover = workHandoverService.readAll().find(h => h.applicationId === params?.id)

        if (!handover) {
          return error('工作交接清单不存在', 404)
        }

        const response = success(handover, '获取工作交接清单成功')
        logger.response(`/api/personnel-transfer/${params?.id}/work-handover`, response)
        return response
      } catch (err: any) {
        logger.error(`/api/personnel-transfer/${params?.id}/work-handover`, err)
        return error('获取工作交接清单失败', 500)
      }
    }
  },

  // 更新交接项状态
  {
    url: '/api/personnel-transfer/handover-item/:id/update',
    method: 'put',
    response: async ({ params, body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { handoverId, itemId, status, notes } = body

        const handovers = workHandoverService.readAll()
        const handover = handovers.find(h => h.handoverId === handoverId)

        if (!handover) {
          return error('工作交接清单不存在', 404)
        }

        // 更新交接项状态
        const item = handover.checklist.find(i => i.itemId === itemId)
        if (!item) {
          return error('交接项不存在', 404)
        }

        item.status = status
        if (notes) item.notes = notes
        if (status === 'COMPLETED') {
          item.completedDate = new Date().toISOString().split('T')[0]
        }

        // 计算整体进度
        const completedCount = handover.checklist.filter(i => i.status === 'COMPLETED').length
        handover.overallProgress = Math.round((completedCount / handover.checklist.length) * 100)

        // 更新整体状态
        if (handover.overallProgress === 100) {
          handover.status = 'COMPLETED'
          handover.completedDate = new Date().toISOString().split('T')[0]
        } else if (handover.overallProgress > 0) {
          handover.status = 'IN_PROGRESS'
        }

        const updated = workHandoverService.update(handoverId, handover)

        const response = success(updated, '更新交接项状态成功')
        logger.response(`/api/personnel-transfer/handover-item/${params?.id}/update`, response)
        return response
      } catch (err: any) {
        logger.error(`/api/personnel-transfer/handover-item/${params?.id}/update`, err)
        return error('更新交接项状态失败', 500)
      }
    }
  },

  // 工作交接质量评估
  {
    url: '/api/personnel-transfer/work-handover/:id/assess',
    method: 'post',
    response: async ({ params, body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const handovers = workHandoverService.readAll()
        const handover = handovers.find(h => h.handoverId === params?.id)

        if (!handover) {
          return error('工作交接清单不存在', 404)
        }

        if (handover.status !== 'COMPLETED') {
          return error('交接尚未完成，无法评估', 400)
        }

        const { score, feedback, assessedBy } = body

        handover.qualityAssessment = {
          score,
          feedback,
          assessedBy,
          assessedDate: new Date().toISOString().split('T')[0]
        }
        handover.status = 'VERIFIED'

        const id = params?.id
        if (!id) {
          return error('缺少工作交接ID', 400)
        }
        const updated = workHandoverService.update(id, handover)

        const response = success(updated, '工作交接质量评估成功')
        logger.response(`/api/personnel-transfer/work-handover/${params?.id}/assess`, response)
        return response
      } catch (err: any) {
        logger.error(`/api/personnel-transfer/work-handover/${params?.id}/assess`, err)
        return error('工作交接质量评估失败', 500)
      }
    }
  },

  // 获取待审批的调动申请（当前用户）
  {
    url: '/api/personnel-transfer/pending',
    method: 'get',
    response: async () => {
      await delay()
      ensureInitialized()

      try {
        // const { userId } = query || {} // 暂时未使用

        // 这里应该根据用户角色和所属部门筛选待审批的申请
        // 简化处理，返回所有待审批的申请
        const applications = transferService
          .readAll()
          .filter(app =>
            ['待调出单位审核', '待调入单位审核', '待人事处审核'].includes(app.applicationStatus)
          )

        const response = success(
          {
            list: applications,
            total: applications.length
          },
          '获取待审批申请成功'
        )

        logger.response('/api/personnel-transfer/pending', response)
        return response
      } catch (err: any) {
        logger.error('/api/personnel-transfer/pending', err)
        return error('获取待审批申请失败', 500)
      }
    }
  },

  // 批量导入人员调动
  {
    url: '/api/personnel-transfer/import',
    method: 'post',
    response: async ({ body }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { data } = body

        if (!Array.isArray(data) || data.length === 0) {
          return error('导入数据不能为空', 400)
        }

        const results = {
          success: 0,
          failed: 0,
          errors: [] as any[]
        }

        for (let i = 0; i < data.length; i++) {
          const row = data[i]

          // 验证必填字段
          if (
            !row.employeeId ||
            !row.employeeName ||
            !row.oldInstitutionId ||
            !row.newInstitutionId
          ) {
            results.failed++
            results.errors.push({
              row: i + 1,
              message: '缺少必填字段'
            })
            continue
          }

          // 创建调动申请
          const application: TransferApplication = {
            adjustmentId: generateId(),
            employeeId: row.employeeId,
            employeeName: row.employeeName,
            oldInstitutionId: row.oldInstitutionId,
            oldInstitutionName: row.oldInstitutionName || '',
            oldPositionId: row.oldPositionId || '',
            oldPositionName: row.oldPositionName || '',
            newInstitutionId: row.newInstitutionId,
            newInstitutionName: row.newInstitutionName || '',
            newPositionId: row.newPositionId || '',
            newPositionName: row.newPositionName || '',
            adjustmentReason: row.adjustmentReason || '批量导入',
            applicationStatus: '待调出单位审核',
            currentApproverRole: '调出单位负责人',
            applicationDate: new Date().toISOString()
          }

          transferService.create(application)
          results.success++
        }

        const response = success(
          results,
          `导入完成：成功${results.success}条，失败${results.failed}条`
        )
        logger.response('/api/personnel-transfer/import', response)
        return response
      } catch (err: any) {
        logger.error('/api/personnel-transfer/import', err)
        return error('导入失败', 500)
      }
    }
  }
] as MockMethod[]
