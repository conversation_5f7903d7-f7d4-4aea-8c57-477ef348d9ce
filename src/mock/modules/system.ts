/**
 * @name 系统配置Mock模块
 * @description 提供系统参数、字典数据、角色权限等相关接口
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import type { MockRequestOptions } from '../types'
import { success, error, delay, logger, pageResult, generateId } from '../utils'
import { MockServiceManager } from '../service'

// 定义字典类型
interface DictItem {
  id?: string
  type: string
  label: string
  value: string
  sort?: number
  status?: 'active' | 'inactive'
  description?: string
  createTime?: string
  updateTime?: string
}

// 定义系统配置类型
interface SystemConfig {
  id?: string
  key: string
  value: any
  name: string
  description?: string
  type: 'string' | 'number' | 'boolean' | 'json'
  category?: string
  createTime?: string
  updateTime?: string
}

// 定义角色类型
interface Role {
  id?: string
  code: string
  name: string
  description?: string
  permissions: string[]
  status: 'active' | 'inactive'
  createTime?: string
  updateTime?: string
}

// 定义通知类型
interface Notice {
  id?: string
  title: string
  content: string
  type: 'system' | 'feature' | 'warning' | 'error'
  level: 'low' | 'medium' | 'high'
  read?: boolean
  userId?: string
  createTime?: string
}

// 获取服务实例
const dictService = MockServiceManager.getService<DictItem>('dicts')
const configService = MockServiceManager.getService<SystemConfig>('configs')
const roleService = MockServiceManager.getService<Role>('roles')
const noticeService = MockServiceManager.getService<Notice>('notices')

// 初始化字典数据
const initDictData = () => {
  const dicts = dictService.readAll()
  if (dicts.length === 0) {
    const dictTypes = [
      // 性别
      { type: 'gender', items: [
        { label: '男', value: 'male', sort: 1 },
        { label: '女', value: 'female', sort: 2 },
        { label: '未知', value: 'unknown', sort: 3 }
      ]},
      // 学历
      { type: 'education', items: [
        { label: '博士研究生', value: 'doctor', sort: 1 },
        { label: '硕士研究生', value: 'master', sort: 2 },
        { label: '大学本科', value: 'bachelor', sort: 3 },
        { label: '大学专科', value: 'college', sort: 4 },
        { label: '高中', value: 'high_school', sort: 5 },
        { label: '初中及以下', value: 'junior', sort: 6 }
      ]},
      // 员工状态
      { type: 'employeeStatus', items: [
        { label: '在职', value: 'active', sort: 1 },
        { label: '试用期', value: 'probation', sort: 2 },
        { label: '休假', value: 'vacation', sort: 3 },
        { label: '离职', value: 'resigned', sort: 4 },
        { label: '退休', value: 'retired', sort: 5 }
      ]},
      // 合同类型
      { type: 'contractType', items: [
        { label: '正式员工', value: 'formal', sort: 1 },
        { label: '劳务合同', value: 'contract', sort: 2 },
        { label: '临时工', value: 'temporary', sort: 3 },
        { label: '实习生', value: 'intern', sort: 4 },
        { label: '外包人员', value: 'outsource', sort: 5 }
      ]},
      // 政治面貌
      { type: 'politicalStatus', items: [
        { label: '中共党员', value: 'party_member', sort: 1 },
        { label: '中共预备党员', value: 'probationary_party', sort: 2 },
        { label: '共青团员', value: 'youth_league', sort: 3 },
        { label: '民主党派', value: 'democratic_party', sort: 4 },
        { label: '群众', value: 'masses', sort: 5 }
      ]},
      // 婚姻状况
      { type: 'maritalStatus', items: [
        { label: '未婚', value: 'single', sort: 1 },
        { label: '已婚', value: 'married', sort: 2 },
        { label: '离异', value: 'divorced', sort: 3 },
        { label: '丧偶', value: 'widowed', sort: 4 }
      ]},
      // 职称等级
      { type: 'titleLevel', items: [
        { label: '正高级', value: 'senior_professor', sort: 1 },
        { label: '副高级', value: 'associate_professor', sort: 2 },
        { label: '中级', value: 'intermediate', sort: 3 },
        { label: '初级', value: 'junior', sort: 4 },
        { label: '无职称', value: 'none', sort: 5 }
      ]},
      // 请假类型
      { type: 'leaveType', items: [
        { label: '事假', value: 'personal', sort: 1 },
        { label: '病假', value: 'sick', sort: 2 },
        { label: '年假', value: 'annual', sort: 3 },
        { label: '婚假', value: 'marriage', sort: 4 },
        { label: '产假', value: 'maternity', sort: 5 },
        { label: '陪产假', value: 'paternity', sort: 6 },
        { label: '丧假', value: 'bereavement', sort: 7 },
        { label: '调休', value: 'compensatory', sort: 8 }
      ]}
    ]
    
    // 批量创建字典数据
    const allDicts: DictItem[] = []
    dictTypes.forEach(({ type, items }) => {
      items.forEach(item => {
        allDicts.push({
          type,
          ...item,
          status: 'active',
          description: `${type} - ${item.label}`
        })
      })
    })
    
    dictService.createBatch(allDicts)
  }
}

// 初始化系统配置
const initSystemConfig = () => {
  const configs = configService.readAll()
  if (configs.length === 0) {
    configService.createBatch([
      {
        key: 'system.name',
        value: '杭科院人事综合管理系统',
        name: '系统名称',
        type: 'string',
        category: 'basic',
        description: '系统显示名称'
      },
      {
        key: 'system.version',
        value: '1.0.0',
        name: '系统版本',
        type: 'string',
        category: 'basic',
        description: '当前系统版本号'
      },
      {
        key: 'system.copyright',
        value: '© 2025 杭州科技职业技术学院',
        name: '版权信息',
        type: 'string',
        category: 'basic',
        description: '系统版权声明'
      },
      {
        key: 'system.logo',
        value: '/hr-web/logo.png',
        name: '系统Logo',
        type: 'string',
        category: 'basic',
        description: '系统Logo路径'
      },
      {
        key: 'theme.primaryColor',
        value: '#409eff',
        name: '主题色',
        type: 'string',
        category: 'theme',
        description: '系统主题颜色'
      },
      {
        key: 'theme.layout',
        value: 'side-menu',
        name: '布局方式',
        type: 'string',
        category: 'theme',
        description: '系统布局模式'
      },
      {
        key: 'security.passwordMinLength',
        value: 8,
        name: '密码最小长度',
        type: 'number',
        category: 'security',
        description: '用户密码最小长度要求'
      },
      {
        key: 'security.sessionTimeout',
        value: 30,
        name: '会话超时时间',
        type: 'number',
        category: 'security',
        description: '用户会话超时时间（分钟）'
      },
      {
        key: 'security.maxLoginAttempts',
        value: 5,
        name: '最大登录尝试次数',
        type: 'number',
        category: 'security',
        description: '用户最大登录失败次数'
      },
      {
        key: 'attendance.workStartTime',
        value: '09:00',
        name: '上班时间',
        type: 'string',
        category: 'attendance',
        description: '标准上班时间'
      },
      {
        key: 'attendance.workEndTime',
        value: '18:00',
        name: '下班时间',
        type: 'string',
        category: 'attendance',
        description: '标准下班时间'
      },
      {
        key: 'attendance.lateThreshold',
        value: 15,
        name: '迟到阈值',
        type: 'number',
        category: 'attendance',
        description: '迟到判定时间（分钟）'
      },
      {
        key: 'notification.emailEnabled',
        value: true,
        name: '邮件通知',
        type: 'boolean',
        category: 'notification',
        description: '是否启用邮件通知'
      },
      {
        key: 'notification.smsEnabled',
        value: false,
        name: '短信通知',
        type: 'boolean',
        category: 'notification',
        description: '是否启用短信通知'
      }
    ])
  }
}

// 初始化角色数据
const initRoles = () => {
  const roles = roleService.readAll()
  if (roles.length === 0) {
    roleService.createBatch([
      {
        code: 'admin',
        name: '系统管理员',
        description: '系统最高权限管理员',
        permissions: ['*:*:*'],
        status: 'active'
      },
      {
        code: 'hr_manager',
        name: '人事主管',
        description: '人事部门主管，拥有人事管理全部权限',
        permissions: [
          'employee:*',
          'organization:*',
          'salary:*',
          'attendance:*',
          'recruitment:*',
          'contract:*',
          'performance:*'
        ],
        status: 'active'
      },
      {
        code: 'hr_staff',
        name: '人事专员',
        description: '人事部门工作人员，拥有人事管理基础权限',
        permissions: [
          'employee:view',
          'employee:create',
          'employee:update',
          'organization:view',
          'salary:view',
          'attendance:*',
          'recruitment:*',
          'contract:*'
        ],
        status: 'active'
      },
      {
        code: 'dept_manager',
        name: '部门主管',
        description: '部门负责人，可管理本部门员工',
        permissions: [
          'employee:view',
          'attendance:view',
          'attendance:approve',
          'performance:view',
          'performance:evaluate'
        ],
        status: 'active'
      },
      {
        code: 'employee',
        name: '普通员工',
        description: '普通员工，仅可查看个人信息',
        permissions: [
          'employee:self',
          'salary:self',
          'attendance:self',
          'contract:self'
        ],
        status: 'active'
      }
    ])
  }
}

// 初始化通知数据
const initNotices = () => {
  const notices = noticeService.readAll()
  if (notices.length === 0) {
    noticeService.createBatch([
      {
        title: '系统上线通知',
        content: '人事综合管理系统正式上线，欢迎使用！如有问题请联系信息中心。',
        type: 'system',
        level: 'high'
      },
      {
        title: '功能更新：考勤管理优化',
        content: '考勤管理模块已完成优化升级，新增移动打卡、智能排班等功能。',
        type: 'feature',
        level: 'medium'
      },
      {
        title: '系统维护通知',
        content: '系统将于本周日凌晨2:00-4:00进行例行维护，期间服务可能暂时不可用。',
        type: 'warning',
        level: 'medium'
      },
      {
        title: '安全提醒',
        content: '请定期修改密码，不要使用简单密码，确保账号安全。',
        type: 'warning',
        level: 'low'
      }
    ])
  }
}

// 标记是否已初始化
let isInitialized = false

// 延迟初始化函数
const ensureInitialized = () => {
  if (!isInitialized) {
    try {
      initDictData()
      initSystemConfig()
      initRoles()
      initNotices()
      isInitialized = true
    } catch (error) {
      console.error('Mock数据初始化失败:', error)
    }
  }
}

export default [
  // 获取字典列表
  {
    url: '/api/system/dict/list',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      logger.request('/api/system/dict/list', 'GET', query)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const { type, status } = query
      
      let result = dictService.find({
        filters: {
          ...(type && { type }),
          ...(status && { status })
        },
        sortBy: 'sort',
        sortOrder: 'asc'
      })
      
      // 按类型分组
      if (!type) {
        const grouped: Record<string, DictItem[]> = {}
        result.forEach((item: any) => {
          if (!grouped[item.type]) {
            grouped[item.type] = []
          }
          grouped[item.type].push(item)
        })
        
        const responseData = success(grouped, '获取成功')
        logger.response('/api/system/dict/list', responseData)
        return responseData
      }
      
      const responseData = success(result, '获取成功')
      logger.response('/api/system/dict/list', responseData)
      return responseData
    }
  },
  
  // 获取字典类型列表
  {
    url: '/api/system/dict/types',
    method: 'get',
    response: async () => {
      logger.request('/api/system/dict/types', 'GET')
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const allDicts = dictService.readAll()
      const types = new Set(allDicts.map((d: any) => d.type))
      
      const typeList = Array.from(types).map(type => {
        const items = allDicts.filter((d: any) => d.type === type)
        return {
          type,
          name: items[0]?.description?.split(' - ')[0] || type,
          count: items.length
        }
      })
      
      const responseData = success(typeList, '获取成功')
      logger.response('/api/system/dict/types', responseData)
      return responseData
    }
  },
  
  // 创建字典项
  {
    url: '/api/system/dict/create',
    method: 'post',
    response: async ({ body }: MockRequestOptions) => {
      logger.request('/api/system/dict/create', 'POST', body)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const { type, label, value } = body
      
      if (!type || !label || !value) {
        return error('请填写完整信息', 400)
      }
      
      // 检查是否已存在
      const existing = dictService.find({
        filters: { type, value }
      })
      
      if (existing.length > 0) {
        return error('字典值已存在', 400)
      }
      
      const dict = dictService.create({
        ...body,
        status: body.status || 'active'
      })
      
      const responseData = success(dict, '创建成功')
      logger.response('/api/system/dict/create', responseData)
      return responseData
    }
  },
  
  // 更新字典项
  {
    url: '/api/system/dict/update/:id',
    method: 'put',
    response: async ({ params, body }: MockRequestOptions) => {
      logger.request(`/api/system/dict/update/${params?.id}`, 'PUT', body)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const updated = dictService.update(params?.id, body)
      
      if (!updated) {
        return error('字典项不存在', 404)
      }
      
      const responseData = success(updated, '更新成功')
      logger.response(`/api/system/dict/update/${params?.id}`, responseData)
      return responseData
    }
  },
  
  // 删除字典项
  {
    url: '/api/system/dict/delete/:id',
    method: 'delete',
    response: async ({ params }: MockRequestOptions) => {
      logger.request(`/api/system/dict/delete/${params?.id}`, 'DELETE')
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const result = dictService.delete(params?.id)
      
      if (!result) {
        return error('字典项不存在', 404)
      }
      
      const responseData = success(null, '删除成功')
      logger.response(`/api/system/dict/delete/${params?.id}`, responseData)
      return responseData
    }
  },
  
  // 获取系统配置
  {
    url: '/api/system/config',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      logger.request('/api/system/config', 'GET', query)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const { category, key } = query
      
      if (key) {
        const config = configService.findOneBy('key', key)
        if (!config) {
          return error('配置项不存在', 404)
        }
        
        const responseData = success(config, '获取成功')
        logger.response('/api/system/config', responseData)
        return responseData
      }
      
      const configs = configService.find({
        filters: category ? { category } : {}
      })
      
      // 转换为对象格式
      const configMap: Record<string, any> = {}
      configs.forEach((config: any) => {
        const keys = config.key.split('.')
        let current = configMap
        
        keys.forEach((k: any, index: any) => {
          if (index === keys.length - 1) {
            current[k] = config.value
          } else {
            if (!current[k]) {
              current[k] = {}
            }
            current = current[k]
          }
        })
      })
      
      const responseData = success(configMap, '获取成功')
      logger.response('/api/system/config', responseData)
      return responseData
    }
  },
  
  // 获取配置列表（带分页）
  {
    url: '/api/system/config/list',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      logger.request('/api/system/config/list', 'GET', query)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const result = configService.paginate({
        ...query,
        sortBy: 'category'
      })
      
      const responseData = pageResult(result.list, result.total, result.page, result.size)
      logger.response('/api/system/config/list', responseData)
      return responseData
    }
  },
  
  // 更新系统配置
  {
    url: '/api/system/config/update',
    method: 'put',
    response: async ({ body }: MockRequestOptions) => {
      logger.request('/api/system/config/update', 'PUT', body)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const { key, value } = body
      
      if (!key) {
        return error('配置键不能为空', 400)
      }
      
      const config = configService.findOneBy('key', key)
      
      if (!config) {
        return error('配置项不存在', 404)
      }
      
      const updated = configService.update(config.id!, { value })
      
      const responseData = success(updated, '更新成功')
      logger.response('/api/system/config/update', responseData)
      return responseData
    }
  },
  
  // 批量更新配置
  {
    url: '/api/system/config/batch-update',
    method: 'put',
    response: async ({ body }: MockRequestOptions) => {
      logger.request('/api/system/config/batch-update', 'PUT', body)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const { configs } = body
      
      if (!Array.isArray(configs)) {
        return error('参数格式错误', 400)
      }
      
      const results = []
      for (const { key, value } of configs) {
        const config = configService.findOneBy('key', key)
        if (config) {
          const updated = configService.update(config.id!, { value })
          results.push(updated)
        }
      }
      
      const responseData = success(results, '批量更新成功')
      logger.response('/api/system/config/batch-update', responseData)
      return responseData
    }
  },
  
  // 获取角色列表
  {
    url: '/api/system/roles',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      logger.request('/api/system/roles', 'GET', query)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const result = roleService.paginate({
        ...query,
        sortBy: 'createTime',
        sortOrder: 'desc'
      })
      
      const responseData = pageResult(result.list, result.total, result.page, result.size)
      logger.response('/api/system/roles', responseData)
      return responseData
    }
  },
  
  // 获取所有角色（不分页）
  {
    url: '/api/system/roles/all',
    method: 'get',
    response: async () => {
      logger.request('/api/system/roles/all', 'GET')
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const roles = roleService.find({
        filters: { status: 'active' }
      })
      
      const responseData = success(roles, '获取成功')
      logger.response('/api/system/roles/all', responseData)
      return responseData
    }
  },
  
  // 创建角色
  {
    url: '/api/system/role/create',
    method: 'post',
    response: async ({ body }: MockRequestOptions) => {
      logger.request('/api/system/role/create', 'POST', body)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const { code, name } = body
      
      if (!code || !name) {
        return error('角色编码和名称不能为空', 400)
      }
      
      // 检查编码是否已存在
      const existing = roleService.findOneBy('code', code)
      if (existing) {
        return error('角色编码已存在', 400)
      }
      
      const role = roleService.create({
        ...body,
        status: body.status || 'active',
        permissions: body.permissions || []
      })
      
      const responseData = success(role, '创建成功')
      logger.response('/api/system/role/create', responseData)
      return responseData
    }
  },
  
  // 更新角色
  {
    url: '/api/system/role/update/:id',
    method: 'put',
    response: async ({ params, body }: MockRequestOptions) => {
      logger.request(`/api/system/role/update/${params?.id}`, 'PUT', body)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const updated = roleService.update(params?.id, body)
      
      if (!updated) {
        return error('角色不存在', 404)
      }
      
      const responseData = success(updated, '更新成功')
      logger.response(`/api/system/role/update/${params?.id}`, responseData)
      return responseData
    }
  },
  
  // 删除角色
  {
    url: '/api/system/role/delete/:id',
    method: 'delete',
    response: async ({ params }: MockRequestOptions) => {
      logger.request(`/api/system/role/delete/${params?.id}`, 'DELETE')
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const result = roleService.delete(params?.id)
      
      if (!result) {
        return error('角色不存在', 404)
      }
      
      const responseData = success(null, '删除成功')
      logger.response(`/api/system/role/delete/${params?.id}`, responseData)
      return responseData
    }
  },
  
  // 获取系统通知
  {
    url: '/api/system/notices',
    method: 'get',
    response: async ({ query, headers }: MockRequestOptions) => {
      logger.request('/api/system/notices', 'GET', query)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const token = headers.authorization?.replace('Bearer ', '')
      const userId = token ? token.split('-')[2] : null
      
      // 如果有用户ID，标记部分通知为已读
      if (userId) {
        const notices = noticeService.readAll()
        notices.forEach((notice: any, index: any) => {
          if (index % 2 === 0) {
            notice.read = true
            notice.userId = userId
          }
        })
      }
      
      const result = noticeService.paginate({
        ...query,
        sortBy: 'createTime',
        sortOrder: 'desc'
      })
      
      const responseData = pageResult(result.list, result.total, result.page, result.size)
      logger.response('/api/system/notices', responseData)
      return responseData
    }
  },
  
  // 标记通知已读
  {
    url: '/api/system/notice/read/:id',
    method: 'put',
    response: async ({ params }: MockRequestOptions) => {
      logger.request(`/api/system/notice/read/${params?.id}`, 'PUT')
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const updated = noticeService.update(params?.id, { read: true })
      
      if (!updated) {
        return error('通知不存在', 404)
      }
      
      const responseData = success(updated, '标记成功')
      logger.response(`/api/system/notice/read/${params?.id}`, responseData)
      return responseData
    }
  },
  
  // 获取未读通知数量
  {
    url: '/api/system/notices/unread-count',
    method: 'get',
    response: async () => {
      logger.request('/api/system/notices/unread-count', 'GET')
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const unreadCount = noticeService.count({
        filters: { read: false }
      })
      
      const responseData = success({ count: unreadCount }, '获取成功')
      logger.response('/api/system/notices/unread-count', responseData)
      return responseData
    }
  },
  
  // 获取操作日志
  {
    url: '/api/system/logs',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      logger.request('/api/system/logs', 'GET', query)
      await delay()
      
      // 确保数据已初始化
      ensureInitialized()
      
      const logs = [
        {
          id: generateId(),
          user: 'admin',
          action: '登录系统',
          module: '认证模块',
          ip: '*************',
          result: 'success',
          createTime: new Date().toISOString()
        },
        {
          id: generateId(),
          user: 'hr001',
          action: '创建员工',
          module: '员工管理',
          ip: '*************',
          result: 'success',
          createTime: new Date(Date.now() - 3600000).toISOString()
        },
        {
          id: generateId(),
          user: 'admin',
          action: '修改系统配置',
          module: '系统管理',
          ip: '*************',
          result: 'success',
          createTime: new Date(Date.now() - 7200000).toISOString()
        }
      ]
      
      const responseData = pageResult(logs, logs.length, 1, 10)
      logger.response('/api/system/logs', responseData)
      return responseData
    }
  }
] as MockMethod[]