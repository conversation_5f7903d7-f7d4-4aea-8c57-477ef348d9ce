/**
 * @name 场景化数据Mock模块
 * @description 提供完整业务流程的场景化测试数据
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import { mockResponse } from '../utils'
import { ScenarioGenerator } from '../generators/ScenarioGenerator'

export default [
  // 获取所有场景数据
  {
    url: '/api/scenarios/all',
    method: 'get',
    response: () => {
      const scenarios = ScenarioGenerator.generateAllScenarios()
      return mockResponse.success(scenarios, '获取成功')
    }
  },

  // 获取入职流程场景数据
  {
    url: '/api/scenarios/onboarding',
    method: 'get',
    response: () => {
      const scenario = ScenarioGenerator.generateOnboardingScenario()
      return mockResponse.success(scenario, '获取成功')
    }
  },

  // 获取请假流程场景数据
  {
    url: '/api/scenarios/leave',
    method: 'get',
    response: () => {
      const scenario = ScenarioGenerator.generateLeaveScenario()
      return mockResponse.success(scenario, '获取成功')
    }
  },

  // 获取薪资发放场景数据
  {
    url: '/api/scenarios/salary',
    method: 'get',
    response: () => {
      const scenario = ScenarioGenerator.generateSalaryScenario()
      return mockResponse.success(scenario, '获取成功')
    }
  },

  // 获取年度考核场景数据
  {
    url: '/api/scenarios/annual-assessment',
    method: 'get',
    response: () => {
      const scenario = ScenarioGenerator.generateAnnualAssessmentScenario()
      return mockResponse.success(scenario, '获取成功')
    }
  },

  // 生成并保存入职场景数据（用于测试）
  {
    url: '/api/scenarios/onboarding/generate',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const count = body.count || 1
      const scenarios = []
      
      for (let i = 0; i < count; i++) {
        scenarios.push(ScenarioGenerator.generateOnboardingScenario())
      }
      
      return mockResponse.success({
        count: scenarios.length,
        scenarios
      }, '生成成功')
    }
  },

  // 生成并保存请假场景数据（用于测试）
  {
    url: '/api/scenarios/leave/generate',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const count = body.count || 1
      const scenarios = []
      
      for (let i = 0; i < count; i++) {
        scenarios.push(ScenarioGenerator.generateLeaveScenario())
      }
      
      return mockResponse.success({
        count: scenarios.length,
        scenarios
      }, '生成成功')
    }
  },

  // 生成并保存薪资场景数据（用于测试）
  {
    url: '/api/scenarios/salary/generate',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const month = body.month || new Date().toISOString().substring(0, 7)
      const scenario = ScenarioGenerator.generateSalaryScenario()
      
      // 设置指定月份
      scenario.salaryBatch.month = month
      
      return mockResponse.success(scenario, '生成成功')
    }
  },

  // 生成并保存年度考核场景数据（用于测试）
  {
    url: '/api/scenarios/assessment/generate',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const year = body.year || new Date().getFullYear()
      const scenario = ScenarioGenerator.generateAnnualAssessmentScenario()
      
      // 设置指定年份
      scenario.assessmentPlan.assessmentYear = year
      
      return mockResponse.success(scenario, '生成成功')
    }
  },

  // 获取场景执行进度
  {
    url: '/api/scenarios/:type/progress',
    method: 'get',
    response: ({ params }: { params: any }) => {
      const progressMap: Record<string, any> = {
        onboarding: {
          total: 10,
          completed: 4,
          current: '人事处审批',
          percentage: 40,
          steps: [
            { name: '发放录用通知', status: 'completed' },
            { name: '背景调查', status: 'completed' },
            { name: '体检', status: 'completed' },
            { name: '提交入职申请', status: 'completed' },
            { name: '部门审批', status: 'completed' },
            { name: '人事处审批', status: 'in_progress' },
            { name: '账号开通', status: 'pending' },
            { name: '合同签订', status: 'pending' },
            { name: '入职培训', status: 'pending' },
            { name: '试用期考核', status: 'pending' }
          ]
        },
        leave: {
          total: 5,
          completed: 2,
          current: '部门负责人审批',
          percentage: 40,
          steps: [
            { name: '提交请假申请', status: 'completed' },
            { name: '直属上级审批', status: 'completed' },
            { name: '部门负责人审批', status: 'in_progress' },
            { name: '人事处备案', status: 'pending' },
            { name: '考勤数据更新', status: 'pending' }
          ]
        },
        salary: {
          total: 6,
          completed: 3,
          current: '财务审核',
          percentage: 50,
          steps: [
            { name: '薪资核算', status: 'completed' },
            { name: '部门复核', status: 'completed' },
            { name: '异常处理', status: 'completed' },
            { name: '财务审核', status: 'in_progress' },
            { name: '领导审批', status: 'pending' },
            { name: '薪资发放', status: 'pending' }
          ]
        },
        assessment: {
          total: 8,
          completed: 5,
          current: '人事处审核',
          percentage: 62.5,
          steps: [
            { name: '考核方案发布', status: 'completed' },
            { name: '任务分配', status: 'completed' },
            { name: '个人自评', status: 'completed' },
            { name: '360度评价', status: 'completed' },
            { name: '部门初审', status: 'completed' },
            { name: '人事处审核', status: 'in_progress' },
            { name: '结果公示', status: 'pending' },
            { name: '申诉处理', status: 'pending' }
          ]
        }
      }
      
      const progress = progressMap[params.type]
      
      if (!progress) {
        return mockResponse.error('场景类型不存在', 404)
      }
      
      return mockResponse.success(progress, '获取成功')
    }
  },

  // 获取场景统计数据
  {
    url: '/api/scenarios/statistics',
    method: 'get',
    response: () => {
      const statistics = {
        totalScenarios: 4,
        activeScenarios: {
          onboarding: 12,
          leave: 35,
          salary: 1,
          assessment: 1
        },
        completedToday: {
          onboarding: 3,
          leave: 15,
          salary: 0,
          assessment: 0
        },
        pendingActions: {
          approvals: 28,
          signatures: 5,
          reviews: 12
        },
        monthlyTrends: [
          { month: '2025-01', onboarding: 8, leave: 120, salary: 1, assessment: 0 },
          { month: '2024-12', onboarding: 10, leave: 135, salary: 1, assessment: 1 },
          { month: '2024-11', onboarding: 6, leave: 110, salary: 1, assessment: 0 },
          { month: '2024-10', onboarding: 12, leave: 128, salary: 1, assessment: 0 }
        ]
      }
      
      return mockResponse.success(statistics, '获取成功')
    }
  }
] as MockMethod[]