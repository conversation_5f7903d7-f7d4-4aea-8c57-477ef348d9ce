/**
 * @name 薪资管理Mock模块
 * @description 提供薪资管理相关的Mock接口
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import { MockServiceManager } from '../service'
import { mockResponse, paginate, generateId } from '../utils'
import { SalaryGenerator } from '../generators/SalaryGenerator'

// 定义请求参数接口
interface MockRequestOptions {
  query?: Record<string, any>
  body?: Record<string, any>
  params?: Record<string, any>
  headers?: Record<string, any>
}

// 初始化Mock服务
const salaryDetailService = MockServiceManager.getService('salary_details')
const salaryStructureService = MockServiceManager.getService('salary_structures')
const salaryExceptionService = MockServiceManager.getService('salary_exceptions')
const budgetMonitoringService = MockServiceManager.getService('budget_monitoring')

// 检查是否需要初始化数据
if (salaryDetailService.count() === 0) {
  const testData = SalaryGenerator.generateTestDataset()
  
  // 初始化薪资结构
  testData.salaryStructures.forEach(structure => salaryStructureService.create(structure))
  
  // 初始化工资明细
  testData.salaryDetails.forEach(detail => salaryDetailService.create(detail))
  
  // 初始化薪资异常
  testData.salaryExceptions.forEach(exception => salaryExceptionService.create(exception))
  
  // 初始化预算监控
  testData.budgetMonitoring.forEach(budget => budgetMonitoringService.create(budget))
}

const salaryMock: MockMethod[] = [
  // ========== 薪资结构管理 ==========
  
  // 获取薪资结构列表
  {
    url: '/api/salary/structures',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, status } = query || {}
      let structures = salaryStructureService.findAll()
      
      if (status) {
        structures = structures.filter((s: any) => s.status === status)
      }
      
      return mockResponse.success(paginate(structures, Number(page), Number(size)))
    }
  },
  
  // 获取薪资结构详情
  {
    url: '/api/salary/structures/:id',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      const structure = salaryStructureService.findById(params?.id)
      if (!structure) {
        return mockResponse.error('薪资结构不存在', 404)
      }
      return mockResponse.success(structure)
    }
  },
  
  // 创建薪资结构
  {
    url: '/api/salary/structures',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const structure = {
        ...body,
        id: generateId(),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      salaryStructureService.create(structure)
      return mockResponse.success(structure, '创建成功')
    }
  },
  
  // 更新薪资结构
  {
    url: '/api/salary/structures/:id',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const updated = salaryStructureService.update(params?.id, {
        ...body,
        updateTime: new Date().toISOString()
      })
      if (!updated) {
        return mockResponse.error('薪资结构不存在', 404)
      }
      return mockResponse.success(updated)
    }
  },
  
  // 删除薪资结构
  {
    url: '/api/salary/structures/:id',
    method: 'delete',
    response: ({ params }: MockRequestOptions) => {
      const deleted = salaryStructureService.delete(params?.id)
      if (!deleted) {
        return mockResponse.error('薪资结构不存在', 404)
      }
      return mockResponse.success(null, '删除成功')
    }
  },
  
  // ========== 工资明细管理 ==========
  
  // 获取工资明细列表
  {
    url: '/api/salary/details',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, month, employeeId, department } = query || {}
      let details = salaryDetailService.findAll()
      
      if (month) {
        details = details.filter((d: any) => d.salaryMonth === month)
      }
      if (employeeId) {
        details = details.filter((d: any) => d.employeeId === employeeId)
      }
      if (department) {
        details = details.filter((d: any) => d.department === department)
      }
      
      // 数据脱敏处理
      const maskedDetails = details.map((detail: any) => ({
        ...detail,
        baseSalary: detail.baseSalary > 10000 ? '****' : detail.baseSalary,
        netSalary: detail.netSalary > 10000 ? '****' : detail.netSalary
      }))
      
      return mockResponse.success(paginate(maskedDetails, Number(page), Number(size)))
    }
  },
  
  // 获取工资明细详情（个人查询）
  {
    url: '/api/salary/details/:id',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      const detail = salaryDetailService.findById(params?.id)
      if (!detail) {
        return mockResponse.error('工资明细不存在', 404)
      }
      return mockResponse.success(detail)
    }
  },
  
  // 计算员工月度工资
  {
    url: '/api/salary/calculate',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { employeeId, month } = body || {}
      if (!employeeId || !month) {
        return mockResponse.error('参数错误', 400)
      }
      
      const salaryDetail = SalaryGenerator.generateSalaryDetail(employeeId, month)
      salaryDetailService.create(salaryDetail)
      
      return mockResponse.success(salaryDetail, '计算成功')
    }
  },
  
  // 批量计算部门工资
  {
    url: '/api/salary/calculate-batch',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { department, month } = body || {}
      if (!department || !month) {
        return mockResponse.error('参数错误', 400)
      }
      
      // 模拟批量计算
      const taskId = generateId()
      const employeeCount = Math.floor(Math.random() * 50) + 20
      
      return mockResponse.success({
        taskId,
        status: 'processing',
        department,
        month,
        totalEmployees: employeeCount,
        processedEmployees: 0,
        estimatedTime: employeeCount * 0.1 // 秒
      })
    }
  },
  
  // 获取工资条（员工自查）
  {
    url: '/api/salary/payslips',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, year } = query || {}
      const currentYear = year || new Date().getFullYear()
      
      // 获取当前用户的工资条（模拟）
      const employeeId = 'E00001' // 从token中获取
      let details = salaryDetailService.findAll().filter((d: any) => 
        d.employeeId === employeeId && d.salaryMonth.startsWith(String(currentYear))
      )
      
      return mockResponse.success(paginate(details, Number(page), Number(size)))
    }
  },
  
  // 确认工资条
  {
    url: '/api/salary/payslips/:id/confirm',
    method: 'post',
    response: ({ params }: MockRequestOptions) => {
      const detail = salaryDetailService.findById(params?.id)
      if (!detail) {
        return mockResponse.error('工资条不存在', 404)
      }
      
      const updated = salaryDetailService.update(params?.id, {
        ...detail,
        confirmStatus: '已确认',
        confirmTime: new Date().toISOString()
      })
      
      return mockResponse.success(updated)
    }
  },
  
  // ========== 薪资统计分析 ==========
  
  // 获取薪资统计数据
  {
    url: '/api/salary/statistics',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { month, type = 'month' } = query || {}
      
      if (!month) {
        return mockResponse.error('请指定统计月份', 400)
      }
      
      const statistics = SalaryGenerator.generateSalaryStatistics(month)
      return mockResponse.success(statistics)
    }
  },
  
  // 获取部门薪资对比
  {
    url: '/api/salary/statistics/department-compare',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { year = new Date().getFullYear() } = query || {}
      
      const departments = [
        '信息工程学院', '机电工程学院', '商贸旅游学院', 
        '艺术设计学院', '建筑工程学院'
      ]
      
      const data = departments.map(dept => ({
        department: dept,
        months: Array.from({ length: 12 }, (_, i) => ({
          month: `${year}-${String(i + 1).padStart(2, '0')}`,
          avgSalary: 8000 + Math.random() * 4000,
          totalSalary: 500000 + Math.random() * 1000000
        }))
      }))
      
      return mockResponse.success({ year, data })
    }
  },
  
  // 获取薪资趋势分析
  {
    url: '/api/salary/statistics/trend',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { startMonth, endMonth } = query || {}
      
      if (!startMonth || !endMonth) {
        return mockResponse.error('请指定起止月份', 400)
      }
      
      // 生成趋势数据
      const start = new Date(startMonth)
      const end = new Date(endMonth)
      const months: string[] = []
      
      while (start <= end) {
        months.push(start.toISOString().slice(0, 7))
        start.setMonth(start.getMonth() + 1)
      }
      
      const trend = months.map(month => ({
        month,
        totalSalary: 8000000 + Math.random() * 2000000,
        avgSalary: 8000 + Math.random() * 2000,
        employeeCount: 1000 + Math.floor(Math.random() * 200),
        socialInsurance: 1000000 + Math.random() * 500000,
        housingFund: 800000 + Math.random() * 400000
      }))
      
      return mockResponse.success({ period: { startMonth, endMonth }, trend })
    }
  },
  
  // 获取薪资结构分析
  {
    url: '/api/salary/statistics/structure-analysis',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { month } = query || {}
      
      const analysis = {
        month,
        components: [
          { name: '基本工资', amount: 4000000, percentage: 40 },
          { name: '岗位工资', amount: 2000000, percentage: 20 },
          { name: '绩效工资', amount: 2000000, percentage: 20 },
          { name: '津贴补贴', amount: 1000000, percentage: 10 },
          { name: '奖金', amount: 1000000, percentage: 10 }
        ],
        deductions: [
          { name: '养老保险', amount: 800000, percentage: 8 },
          { name: '医疗保险', amount: 200000, percentage: 2 },
          { name: '失业保险', amount: 50000, percentage: 0.5 },
          { name: '住房公积金', amount: 1200000, percentage: 12 },
          { name: '个人所得税', amount: 500000, percentage: 5 }
        ]
      }
      
      return mockResponse.success(analysis)
    }
  },
  
  // ========== 薪资异常管理 ==========
  
  // 获取薪资异常列表
  {
    url: '/api/salary/exceptions',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, status, month } = query || {}
      let exceptions = salaryExceptionService.findAll()
      
      if (status) {
        exceptions = exceptions.filter((e: any) => e.processStatus === status)
      }
      if (month) {
        exceptions = exceptions.filter((e: any) => e.salaryMonth === month)
      }
      
      return mockResponse.success(paginate(exceptions, Number(page), Number(size)))
    }
  },
  
  // 创建薪资异常
  {
    url: '/api/salary/exceptions',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const exception = {
        ...body,
        id: generateId(),
        discoveryTime: new Date().toISOString(),
        processStatus: '待处理'
      }
      salaryExceptionService.create(exception)
      return mockResponse.success(exception, '异常已记录')
    }
  },
  
  // 处理薪资异常
  {
    url: '/api/salary/exceptions/:id/process',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const exception = salaryExceptionService.findById(params?.id)
      if (!exception) {
        return mockResponse.error('异常记录不存在', 404)
      }
      
      const updated = salaryExceptionService.update(params?.id, {
        ...exception,
        processStatus: '已处理',
        processorId: 'U00001', // 从token获取
        processorName: '张三',
        processTime: new Date().toISOString(),
        solution: body?.solution
      })
      
      return mockResponse.success(updated)
    }
  },
  
  // ========== 预算监控 ==========
  
  // 获取预算监控数据
  {
    url: '/api/salary/budget-monitoring',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { year = new Date().getFullYear() } = query || {}
      let budgets = budgetMonitoringService.findAll().filter((b: any) => b.budgetYear === String(year))
      
      if (budgets.length === 0) {
        // 生成当年预算数据
        budgets = SalaryGenerator.generateBudgetMonitoring(String(year))
        budgets.forEach((budget: any) => budgetMonitoringService.create(budget))
      }
      
      return mockResponse.success(budgets)
    }
  },
  
  // 创建预算计划
  {
    url: '/api/salary/budget-monitoring',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const budget = {
        ...body,
        id: generateId(),
        actualExpense: 0,
        difference: body?.budgetAmount || 0,
        executionRate: 0,
        warningStatus: '正常',
        updateTime: new Date().toISOString()
      }
      budgetMonitoringService.create(budget)
      return mockResponse.success(budget, '预算计划已创建')
    }
  },
  
  // 更新预算执行情况
  {
    url: '/api/salary/budget-monitoring/:id',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const budget = budgetMonitoringService.findById(params?.id)
      if (!budget) {
        return mockResponse.error('预算项不存在', 404)
      }
      
      const actualExpense = body?.actualExpense || budget.actualExpense
      const difference = budget.budgetAmount - actualExpense
      const executionRate = Number(((actualExpense / budget.budgetAmount) * 100).toFixed(2))
      const warningStatus = actualExpense > budget.budgetAmount ? '超预算' : 
                           actualExpense > budget.budgetAmount * 0.9 ? '预警' : '正常'
      
      const updated = budgetMonitoringService.update(params?.id, {
        ...budget,
        ...body,
        actualExpense,
        difference,
        executionRate,
        warningStatus,
        updateTime: new Date().toISOString()
      })
      
      return mockResponse.success(updated)
    }
  },
  
  // ========== 税务管理 ==========
  
  // 获取个税计算
  {
    url: '/api/salary/tax/calculate',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { grossSalary, deductions = {} } = body || {}
      
      if (!grossSalary) {
        return mockResponse.error('请输入应发工资', 400)
      }
      
      // 计算专项扣除总额
      const specialDeductions = Object.values(deductions).reduce((sum: number, val: any) => sum + (val || 0), 0)
      
      // 计算应纳税所得额
      const taxableIncome = Math.max(0, grossSalary - 5000 - specialDeductions)
      
      // 计算个税
      let tax = 0
      if (taxableIncome > 0) {
        if (taxableIncome <= 3000) tax = taxableIncome * 0.03
        else if (taxableIncome <= 12000) tax = taxableIncome * 0.1 - 210
        else if (taxableIncome <= 25000) tax = taxableIncome * 0.2 - 1410
        else if (taxableIncome <= 35000) tax = taxableIncome * 0.25 - 2660
        else if (taxableIncome <= 55000) tax = taxableIncome * 0.3 - 4410
        else if (taxableIncome <= 80000) tax = taxableIncome * 0.35 - 7160
        else tax = taxableIncome * 0.45 - 15160
      }
      
      return mockResponse.success({
        grossSalary,
        specialDeductions,
        taxableIncome,
        taxAmount: Math.round(tax),
        netSalary: grossSalary - Math.round(tax),
        taxRate: taxableIncome > 80000 ? 45 : 
                taxableIncome > 55000 ? 35 :
                taxableIncome > 35000 ? 30 :
                taxableIncome > 25000 ? 25 :
                taxableIncome > 12000 ? 20 :
                taxableIncome > 3000 ? 10 : 3
      })
    }
  },
  
  // 获取员工专项扣除
  {
    url: '/api/salary/tax/deductions/:employeeId',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      // 模拟员工专项扣除数据
      return mockResponse.success({
        employeeId: params?.employeeId,
        childEducation: Math.random() > 0.5 ? 1000 : 0,
        continuingEducation: Math.random() > 0.7 ? 400 : 0,
        seriousIllness: 0,
        housingLoan: Math.random() > 0.4 ? 1000 : 0,
        housingRent: Math.random() > 0.3 ? 1500 : 0,
        elderlySupport: Math.random() > 0.6 ? 2000 : 0,
        totalDeductions: 0
      })
    }
  },
  
  // 更新员工专项扣除
  {
    url: '/api/salary/tax/deductions/:employeeId',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const deductions = {
        employeeId: params?.employeeId,
        ...body,
        totalDeductions: Object.values(body || {}).reduce((sum: number, val: any) => {
          return sum + (typeof val === 'number' ? val : 0)
        }, 0),
        updateTime: new Date().toISOString()
      }
      
      return mockResponse.success(deductions)
    }
  },
  
  // ========== 工资发放 ==========
  
  // 批量发放工资
  {
    url: '/api/salary/batch-pay',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { month, departmentIds = [] } = body || {}
      
      if (!month) {
        return mockResponse.error('请指定发放月份', 400)
      }
      
      const taskId = generateId()
      const totalEmployees = departmentIds.length > 0 ? departmentIds.length * 50 : 1200
      const totalAmount = totalEmployees * 8500
      
      return mockResponse.success({
        taskId,
        month,
        departments: departmentIds,
        totalEmployees,
        totalAmount,
        status: 'processing',
        createTime: new Date().toISOString(),
        estimatedTime: Math.ceil(totalEmployees / 100) * 60 // 秒
      })
    }
  },
  
  // 查询发放进度
  {
    url: '/api/salary/batch-pay/:taskId',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      // 模拟发放进度
      const progress = Math.min(100, Math.floor(Math.random() * 100) + 20)
      
      return mockResponse.success({
        taskId: params?.taskId,
        progress,
        processedCount: Math.floor(1200 * progress / 100),
        totalCount: 1200,
        status: progress === 100 ? 'completed' : 'processing',
        completedTime: progress === 100 ? new Date().toISOString() : null
      })
    }
  },
  
  // 导出工资单
  {
    url: '/api/salary/export',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { month, format = 'excel', departmentIds = [] } = body || {}
      
      return mockResponse.success({
        downloadUrl: `/download/salary_${month}_${Date.now()}.${format === 'pdf' ? 'pdf' : 'xlsx'}`,
        fileName: `工资单_${month}.${format === 'pdf' ? 'pdf' : 'xlsx'}`,
        fileSize: Math.floor(Math.random() * 1000000) + 500000,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      })
    }
  }
] as MockMethod[]

export default salaryMock