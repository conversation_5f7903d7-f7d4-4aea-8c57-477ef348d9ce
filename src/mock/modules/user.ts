/**
 * @name 用户认证Mock模块
 * @description 提供用户登录、权限、菜单等相关接口
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import type { MockRequestOptions } from '../types'
import { success, error, delay, generateId, logger } from '../utils'
import { MockServiceManager } from '../service'

// 定义用户类型
interface User {
  id?: string
  username: string
  password: string
  role: string
  name: string
  userId: string
  department: string
  position: string
  phone: string
  email: string
  status?: 'active' | 'inactive'
  createTime?: string
  updateTime?: string
}

// 定义Token信息类型
interface TokenInfo {
  id?: string
  userId: string
  username: string
  role: string
  loginTime: string
  expiresAt: string
  [key: string]: any
}

// 获取用户服务实例
const userService = MockServiceManager.getService<User>('users')
const tokenService = MockServiceManager.getService<TokenInfo>('tokens')

// 初始化默认用户数据
const initUsers = () => {
  const users = userService.readAll()
  if (users.length === 0) {
    userService.createBatch([
      {
        username: 'admin',
        password: 'admin123',
        role: 'admin',
        name: '系统管理员',
        userId: '100001',
        department: '信息中心',
        position: '系统管理员',
        phone: '13800138000',
        email: '<EMAIL>',
        status: 'active'
      },
      {
        username: 'hr001',
        password: 'hr123',
        role: 'hr',
        name: '人事专员',
        userId: '100002',
        department: '人事处',
        position: '人事专员',
        phone: '13800138001',
        email: '<EMAIL>',
        status: 'active'
      },
      {
        username: 'teacher001',
        password: 'teacher123',
        role: 'teacher',
        name: '张三',
        userId: '2024001',
        department: '信息工程学院',
        position: '讲师',
        phone: '13800138002',
        email: '<EMAIL>',
        status: 'active'
      }
    ])
  }
}

// 初始化数据
initUsers()

export default [
  // 用户登录
  {
    url: '/api/auth/login',
    method: 'post',

    response: async ({ body }: MockRequestOptions) => {
      logger.request('/api/auth/login', 'POST', body)
      await delay()
      
      const { username, password } = body

      // 参数验证
      if (!username || !password) {
        return error('用户名和密码不能为空', 400)
      }

      // 查找用户
      const user = userService.findOneBy('username', username)

      if (user && user.password === password) {
        // 检查用户状态
        if (user.status === 'inactive') {
          return error('用户已被禁用', 403)
        }

        const token = 'mock-token-' + user.userId
        const refreshToken = 'mock-refresh-' + user.userId
        const loginTime = new Date().toISOString()
        const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()

        // 存储Token信息
        tokenService.create({
          id: token,
          userId: user.userId,
          username: user.username,
          role: user.role,
          name: user.name,
          department: user.department,
          position: user.position,
          phone: user.phone,
          email: user.email,
          loginTime,
          expiresAt
        })

        const responseData = success({
          token,
          refreshToken,
          expiresIn: 86400, // 24小时
          userInfo: {
            userId: user.userId,
            username: user.username,
            name: user.name,
            role: user.role,
            department: user.department,
            position: user.position,
            phone: user.phone,
            email: user.email,
            avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.userId}`
          }
        }, '登录成功')
        
        logger.response('/api/auth/login', responseData)
        return responseData
      } else {
        const errorResponse = error('用户名或密码错误', 401)
        logger.error('/api/auth/login', errorResponse)
        return errorResponse
      }
    }
  },

  // 获取用户信息
  {
    url: '/api/user/info',
    method: 'get',

    response: async ({ headers }: MockRequestOptions) => {
      logger.request('/api/user/info', 'GET', { headers })
      await delay()
      
      const token = headers.authorization?.replace('Bearer ', '')

      if (!token) {
        return error('未提供认证令牌', 401)
      }

      const tokenInfo = tokenService.read(token)

      if (tokenInfo) {
        // 检查是否过期
        if (new Date(tokenInfo.expiresAt) < new Date()) {
          tokenService.delete(token)
          return error('Token已过期', 401)
        }

        // 根据角色分配权限
        const permissions =
          tokenInfo.role === 'admin'
            ? ['*:*:*']
            : tokenInfo.role === 'hr'
              ? ['employee:*', 'salary:*', 'attendance:*', 'recruitment:*', 'contract:*']
              : ['employee:view', 'salary:view', 'attendance:view']

        const responseData = success({
          userId: tokenInfo.userId,
          username: tokenInfo.username,
          name: tokenInfo.name,
          role: tokenInfo.role,
          department: tokenInfo.department,
          position: tokenInfo.position,
          phone: tokenInfo.phone,
          email: tokenInfo.email,
          avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${tokenInfo.userId}`,
          permissions
        })
        
        logger.response('/api/user/info', responseData)
        return responseData
      } else {
        return error('无效的Token', 401)
      }
    }
  },

  // 退出登录
  {
    url: '/api/auth/logout',
    method: 'post',
    response: async ({ headers }: MockRequestOptions) => {
      logger.request('/api/auth/logout', 'POST', { headers })
      await delay()
      
      const token = headers.authorization?.replace('Bearer ', '')
      if (token) {
        tokenService.delete(token)
      }
      
      const responseData = success(null, '退出成功')
      logger.response('/api/auth/logout', responseData)
      return responseData
    }
  },

  // 获取用户菜单
  {
    url: '/api/user/menus',
    method: 'get',
    response: async ({ headers }: MockRequestOptions) => {
      logger.request('/api/user/menus', 'GET', { headers })
      await delay()
      
      const token = headers.authorization?.replace('Bearer ', '')

      if (!token) {
        return error('未提供认证令牌', 401)
      }

      const tokenInfo = tokenService.read(token)
      
      if (!tokenInfo) {
        return error('无效的Token', 401)
      }

      // 检查是否过期
      if (new Date(tokenInfo.expiresAt) < new Date()) {
        tokenService.delete(token)
        return error('Token已过期', 401)
      }

      // 根据角色返回不同的菜单
      let menus = []

      if (tokenInfo.role === 'admin') {
        menus = [
          {
            id: '1',
            name: '工作台',
            path: '/dashboard',
            icon: 'Dashboard',
            component: 'dashboard/index'
          },
          {
            id: '2',
            name: '员工管理',
            path: '/employee',
            icon: 'User',
            children: [
              {
                id: '2-1',
                name: '员工列表',
                path: '/employee/list',
                component: 'employee/list'
              },
              {
                id: '2-2',
                name: '员工信息',
                path: '/employee/info',
                component: 'employee/info'
              }
            ]
          },
          {
            id: '3',
            name: '组织管理',
            path: '/organization',
            icon: 'OfficeBuilding',
            children: [
              {
                id: '3-1',
                name: '组织架构',
                path: '/organization/structure',
                component: 'organization/structure'
              },
              {
                id: '3-2',
                name: '部门管理',
                path: '/organization/department',
                component: 'organization/department'
              },
              {
                id: '3-3',
                name: '岗位管理',
                path: '/organization/position',
                component: 'organization/position'
              }
            ]
          },
          {
            id: '4',
            name: '系统管理',
            path: '/system',
            icon: 'Setting',
            children: [
              {
                id: '4-1',
                name: '用户管理',
                path: '/system/user',
                component: 'system/user'
              },
              {
                id: '4-2',
                name: '角色管理',
                path: '/system/role',
                component: 'system/role'
              },
              {
                id: '4-3',
                name: '权限管理',
                path: '/system/permission',
                component: 'system/permission'
              }
            ]
          }
        ]
      } else if (tokenInfo.role === 'hr') {
        menus = [
          {
            id: '1',
            name: '工作台',
            path: '/dashboard',
            icon: 'Dashboard',
            component: 'dashboard/index'
          },
          {
            id: '2',
            name: '员工管理',
            path: '/employee',
            icon: 'User',
            children: [
              {
                id: '2-1',
                name: '员工列表',
                path: '/employee/list',
                component: 'employee/list'
              }
            ]
          }
        ]
      } else {
        menus = [
          {
            id: '1',
            name: '个人中心',
            path: '/personal',
            icon: 'UserFilled',
            component: 'personal/index'
          }
        ]
      }

      const responseData = success(menus, '获取成功')
      logger.response('/api/user/menus', responseData)
      return responseData
    }
  },

  // 获取用户权限
  {
    url: '/api/user/permissions',
    method: 'get',
    response: async ({ headers }: MockRequestOptions) => {
      logger.request('/api/user/permissions', 'GET', { headers })
      await delay()
      
      const token = headers.authorization?.replace('Bearer ', '')

      if (!token) {
        return error('未提供认证令牌', 401)
      }

      const tokenInfo = tokenService.read(token)
      
      if (!tokenInfo) {
        return error('无效的Token', 401)
      }

      // 检查是否过期
      if (new Date(tokenInfo.expiresAt) < new Date()) {
        tokenService.delete(token)
        return error('Token已过期', 401)
      }

      // 根据角色分配权限
      const permissions =
        tokenInfo.role === 'admin'
          ? ['*:*:*']
          : tokenInfo.role === 'hr'
            ? ['employee:*', 'salary:*', 'attendance:*', 'recruitment:*', 'contract:*']
            : ['employee:view', 'salary:view', 'attendance:view']

      const responseData = success(permissions, '获取成功')
      logger.response('/api/user/permissions', responseData)
      return responseData
    }
  },

  // 修改密码
  {
    url: '/api/auth/change-password',
    method: 'post',
    response: async ({ body, headers }: MockRequestOptions) => {
      logger.request('/api/auth/change-password', 'POST', { body, headers })
      await delay()
      
      const token = headers.authorization?.replace('Bearer ', '')

      if (!token) {
        return error('未提供认证令牌', 401)
      }

      const tokenInfo = tokenService.read(token)
      
      if (!tokenInfo) {
        return error('无效的Token', 401)
      }

      // 检查是否过期
      if (new Date(tokenInfo.expiresAt) < new Date()) {
        tokenService.delete(token)
        return error('Token已过期', 401)
      }

      const { oldPassword, newPassword, confirmPassword } = body

      if (!oldPassword || !newPassword || !confirmPassword) {
        return error('请填写完整信息', 400)
      }

      if (newPassword !== confirmPassword) {
        return error('两次密码不一致', 400)
      }

      // 查找用户并验证旧密码
      const user = userService.findOneBy('userId', tokenInfo.userId)
      
      if (!user) {
        return error('用户不存在', 404)
      }

      if (user.password !== oldPassword) {
        return error('旧密码不正确', 400)
      }

      // 更新密码
      userService.update(user.id!, { password: newPassword })

      const responseData = success(null, '密码修改成功')
      logger.response('/api/auth/change-password', responseData)
      return responseData
    }
  },

  // 更新用户信息
  {
    url: '/api/user/info',
    method: 'put',
    response: async ({ body, headers }: MockRequestOptions) => {
      logger.request('/api/user/info', 'PUT', { body, headers })
      await delay()
      
      const token = headers.authorization?.replace('Bearer ', '')

      if (!token) {
        return error('未提供认证令牌', 401)
      }

      const tokenInfo = tokenService.read(token)
      
      if (!tokenInfo) {
        return error('无效的Token', 401)
      }

      // 检查是否过期
      if (new Date(tokenInfo.expiresAt) < new Date()) {
        tokenService.delete(token)
        return error('Token已过期', 401)
      }

      // 查找用户
      const user = userService.findOneBy('userId', tokenInfo.userId)
      
      if (!user) {
        return error('用户不存在', 404)
      }

      // 更新用户信息
      const updatedUser = userService.update(user.id!, body)
      
      if (!updatedUser) {
        return error('更新失败', 500)
      }

      // 更新Token中的用户信息
      tokenService.update(token, {
        name: updatedUser.name,
        department: updatedUser.department,
        position: updatedUser.position,
        phone: updatedUser.phone,
        email: updatedUser.email
      })

      const responseData = success({
        userId: updatedUser.userId,
        username: updatedUser.username,
        name: updatedUser.name,
        role: updatedUser.role,
        department: updatedUser.department,
        position: updatedUser.position,
        phone: updatedUser.phone,
        email: updatedUser.email,
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${updatedUser.userId}`
      }, '更新成功')
      
      logger.response('/api/user/info', responseData)
      return responseData
    }
  },
  // Token刷新
  {
    url: '/api/auth/refresh',
    method: 'post',
    response: async ({ body }: MockRequestOptions) => {
      logger.request('/api/auth/refresh', 'POST', body)
      await delay()
      
      const { refreshToken } = body
      
      if (!refreshToken) {
        return error('未提供RefreshToken', 400)
      }
      
      // 简化处理：根据refreshToken掾式之前的userId
      const userIdMatch = refreshToken.match(/mock-refresh-(\d+)/)
      if (!userIdMatch) {
        return error('无效的RefreshToken', 401)
      }
      
      const userId = userIdMatch[1]
      const user = userService.findOneBy('userId', userId)
      
      if (!user) {
        return error('用户不存在', 404)
      }
      
      // 生成新的Token
      const newToken = 'mock-token-' + user.userId + '-' + Date.now()
      const newRefreshToken = 'mock-refresh-' + user.userId + '-' + Date.now()
      const loginTime = new Date().toISOString()
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      
      // 存储新Token
      tokenService.create({
        id: newToken,
        userId: user.userId,
        username: user.username,
        role: user.role,
        name: user.name,
        department: user.department,
        position: user.position,
        phone: user.phone,
        email: user.email,
        loginTime,
        expiresAt
      })
      
      const responseData = success({
        token: newToken,
        refreshToken: newRefreshToken,
        expiresIn: 86400 // 24小时
      }, 'Token刷新成功')
      
      logger.response('/api/auth/refresh', responseData)
      return responseData
    }
  }
] as MockMethod[]
