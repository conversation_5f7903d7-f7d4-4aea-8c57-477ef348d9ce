/**
 * @name WebSocket Mock模块
 * @description 提供WebSocket相关的Mock API接口
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import { mockResponse, generateId } from '../utils'
import { MockWebSocketServer, MessageType } from '../websocket/MockWebSocketServer'
import { NotificationGenerator } from '../websocket/NotificationGenerator'
import { faker } from '@faker-js/faker/locale/zh_CN'

export default [
  // WebSocket连接接口
  {
    url: '/api/websocket/connect',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const { userId, userName } = body

      if (!userId || !userName) {
        return mockResponse.error('用户信息不能为空', 400)
      }

      // 模拟WebSocket连接
      const connectionId = MockWebSocketServer.connect(userId, userName, {
        userAgent: body.userAgent || 'Browser',
        ip: body.ip || faker.internet.ip()
      })

      return mockResponse.success(
        {
          connectionId,
          wsUrl: `ws://localhost:3000/ws/${connectionId}`,
          token: generateId(),
          protocols: ['v1.websocket.hr']
        },
        '连接成功'
      )
    }
  },

  // 断开连接接口
  {
    url: '/api/websocket/disconnect',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const { connectionId } = body

      if (!connectionId) {
        return mockResponse.error('连接ID不能为空', 400)
      }

      MockWebSocketServer.disconnect(connectionId)

      return mockResponse.success({ status: 'disconnected' }, '断开成功')
    }
  },

  // 心跳接口
  {
    url: '/api/websocket/heartbeat',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const { connectionId } = body

      if (!connectionId) {
        return mockResponse.error('连接ID不能为空', 400)
      }

      MockWebSocketServer.handleHeartbeat(connectionId)

      return mockResponse.success({
        status: 'alive',
        serverTime: new Date().toISOString()
      })
    }
  },

  // 获取连接状态
  {
    url: '/api/websocket/status',
    method: 'get',
    response: () => {
      const status = MockWebSocketServer.getConnectionStatus()
      return mockResponse.success(status)
    }
  },

  // 获取在线用户列表
  {
    url: '/api/websocket/online-users',
    method: 'get',
    response: () => {
      const status = MockWebSocketServer.getConnectionStatus()
      const onlineUsers = status.connections.map(conn => ({
        userId: conn.userId,
        userName: conn.userName,
        status: conn.status,
        onlineTime: conn.duration,
        department: faker.helpers.arrayElement(['信息工程学院', '人事处', '财务处', '教务处']),
        avatar: `/avatars/${conn.userId}.jpg`
      }))

      return mockResponse.success({
        total: onlineUsers.length,
        users: onlineUsers
      })
    }
  },

  // 发送消息（测试用）
  {
    url: '/api/websocket/send-message',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const { type, to, data } = body

      const message = MockWebSocketServer.sendMessage({
        id: generateId(),
        type: type || MessageType.NOTIFICATION,
        to,
        data,
        timestamp: new Date().toISOString()
      })

      return mockResponse.success(message, '消息发送成功')
    }
  },

  // 广播消息（测试用）
  {
    url: '/api/websocket/broadcast',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const { type, data } = body

      MockWebSocketServer.broadcast(type || MessageType.BROADCAST, data)

      return mockResponse.success({ status: 'broadcasted' }, '广播成功')
    }
  },

  // 获取消息历史
  {
    url: '/api/websocket/message-history',
    method: 'get',
    response: ({ query }: { query: any }) => {
      const history = MockWebSocketServer.getMessageHistory({
        type: query.type as MessageType,
        userId: query.userId,
        limit: query.limit ? Number(query.limit) : 100
      })

      return mockResponse.success({
        total: history.length,
        messages: history
      })
    }
  },

  // ==================== 通知相关接口 ====================

  // 获取用户通知列表
  {
    url: '/api/notifications/list',
    method: 'get',
    response: ({ query }: { query: any }) => {
      const userId = query.userId || 'U00001'
      const historyKey = `ws_notification_history_${userId}`
      const history = JSON.parse(localStorage.getItem(historyKey) || '[]')

      // 过滤条件
      let filtered = [...history]
      if (query.category) {
        filtered = filtered.filter((n: any) => n.category === query.category)
      }
      if (query.isRead !== undefined) {
        filtered = filtered.filter((n: any) => n.isRead === (query.isRead === 'true'))
      }
      if (query.type) {
        filtered = filtered.filter((n: any) => n.type === query.type)
      }

      // 分页
      const page = Number(query.page) || 1
      const pageSize = Number(query.pageSize) || 20
      const start = (page - 1) * pageSize
      const end = start + pageSize

      return mockResponse.success({
        total: filtered.length,
        page,
        pageSize,
        list: filtered.slice(start, end)
      })
    }
  },

  // 获取通知统计
  {
    url: '/api/notifications/stats',
    method: 'get',
    response: ({ query }: { query: any }) => {
      const userId = query.userId || 'U00001'
      const stats = NotificationGenerator.generateNotificationStats(userId)

      return mockResponse.success(stats)
    }
  },

  // 标记通知为已读
  {
    url: '/api/notifications/read/:id',
    method: 'put',
    response: ({ params, body }: { params: any; body: any }) => {
      const userId = body.userId || 'U00001'
      const historyKey = `ws_notification_history_${userId}`
      const history = JSON.parse(localStorage.getItem(historyKey) || '[]')

      const notification = history.find((n: any) => n.id === params.id)
      if (notification) {
        notification.isRead = true
        notification.readAt = new Date().toISOString()
        localStorage.setItem(historyKey, JSON.stringify(history))

        return mockResponse.success(notification, '标记成功')
      }

      return mockResponse.error('通知不存在', 404)
    }
  },

  // 批量标记已读
  {
    url: '/api/notifications/read-batch',
    method: 'put',
    response: ({ body }: { body: any }) => {
      const { userId, notificationIds } = body
      const historyKey = `ws_notification_history_${userId || 'U00001'}`
      const history = JSON.parse(localStorage.getItem(historyKey) || '[]')

      let count = 0
      history.forEach((notification: any) => {
        if (notificationIds.includes(notification.id) && !notification.isRead) {
          notification.isRead = true
          notification.readAt = new Date().toISOString()
          count++
        }
      })

      localStorage.setItem(historyKey, JSON.stringify(history))

      return mockResponse.success({ count }, `标记${count}条通知为已读`)
    }
  },

  // 删除通知
  {
    url: '/api/notifications/delete/:id',
    method: 'delete',
    response: ({ params, body }: { params: any; body: any }) => {
      const userId = body.userId || 'U00001'
      const historyKey = `ws_notification_history_${userId}`
      const history = JSON.parse(localStorage.getItem(historyKey) || '[]')

      const index = history.findIndex((n: any) => n.id === params.id)
      if (index > -1) {
        history.splice(index, 1)
        localStorage.setItem(historyKey, JSON.stringify(history))

        return mockResponse.success(null, '删除成功')
      }

      return mockResponse.error('通知不存在', 404)
    }
  },

  // 清空通知
  {
    url: '/api/notifications/clear',
    method: 'delete',
    response: ({ body }: { body: any }) => {
      const userId = body.userId || 'U00001'
      const historyKey = `ws_notification_history_${userId}`

      if (body.onlyRead) {
        // 只清空已读通知
        const history = JSON.parse(localStorage.getItem(historyKey) || '[]')
        const unread = history.filter((n: any) => !n.isRead)
        localStorage.setItem(historyKey, JSON.stringify(unread))

        return mockResponse.success(
          {
            deleted: history.length - unread.length
          },
          '清空已读通知成功'
        )
      } else {
        // 清空所有通知
        localStorage.setItem(historyKey, '[]')
        return mockResponse.success(null, '清空所有通知成功')
      }
    }
  },

  // 生成测试通知
  {
    url: '/api/notifications/generate-test',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const { userId, count = 1 } = body
      const notifications = []

      for (let i = 0; i < count; i++) {
        const notification = NotificationGenerator.generateRandomNotification(userId)

        // 通过WebSocket推送
        MockWebSocketServer.sendNotification(userId, {
          title: notification.title,
          content: notification.content,
          type: notification.type as any,
          category: notification.category,
          link: notification.link,
          actions: notification.actions
        })

        notifications.push(notification)
      }

      return mockResponse.success(
        {
          count: notifications.length,
          notifications
        },
        '生成成功'
      )
    }
  },

  // 模拟场景化通知推送
  {
    url: '/api/notifications/simulate-scenario',
    method: 'post',
    response: ({ body }: { body: any }) => {
      const { scenario } = body
      const scenarios = NotificationGenerator.generateScenarioNotifications()

      const selectedScenario = scenario
        ? scenarios.find((s: any) => s.name === scenario)
        : scenarios[Math.floor(Math.random() * scenarios.length)]

      if (!selectedScenario) {
        return mockResponse.error('场景不存在', 404)
      }

      // 模拟批量推送
      selectedScenario.notifications.forEach((notification: any) => {
        MockWebSocketServer.sendNotification(notification.userId, {
          title: notification.title,
          content: notification.content,
          type: notification.type as any,
          category: notification.category,
          link: notification.link,
          actions: notification.actions
        })
      })

      return mockResponse.success(
        {
          scenario: selectedScenario.name,
          count: selectedScenario.notifications.length
        },
        '场景模拟成功'
      )
    }
  }
] as MockMethod[]
