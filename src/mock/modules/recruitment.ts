/**
 * @name 招聘管理Mock模块
 * @description 提供招聘管理相关的Mock接口
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import { MockServiceManager } from '../service'
import { mockResponse, paginate, generateId } from '../utils'
import { RecruitmentGenerator } from '../generators/RecruitmentGenerator'

// 定义请求参数接口
interface MockRequestOptions {
  query?: Record<string, any>
  body?: Record<string, any>
  params?: Record<string, any>
  headers?: Record<string, any>
}

// 初始化Mock服务
const recruitmentDemandService = MockServiceManager.getService('recruitment_demands')
const recruitmentPlanService = MockServiceManager.getService('recruitment_plans')
const resumeService = MockServiceManager.getService('resumes')
const jobApplicationService = MockServiceManager.getService('job_applications')
const interviewScheduleService = MockServiceManager.getService('interview_schedules')
const talentPoolService = MockServiceManager.getService('talent_pool')

// 检查是否需要初始化数据
if (recruitmentDemandService.count() === 0) {
  const testData = RecruitmentGenerator.generateTestDataset()
  
  // 初始化招聘需求
  testData.recruitmentDemands.forEach(demand => recruitmentDemandService.create(demand))
  
  // 初始化招聘计划
  testData.recruitmentPlans.forEach(plan => recruitmentPlanService.create(plan))
  
  // 初始化简历
  testData.resumes.forEach(resume => resumeService.create(resume))
  
  // 初始化岗位申请
  testData.jobApplications.forEach(application => jobApplicationService.create(application))
  
  // 初始化面试安排
  testData.interviewSchedules.forEach(interview => interviewScheduleService.create(interview))
  
  // 初始化人才库
  testData.talentPool.forEach(talent => talentPoolService.create(talent))
}

const recruitmentMock: MockMethod[] = [
  // ========== 招聘需求管理 ==========
  
  // 获取招聘需求列表
  {
    url: '/api/recruitment/demands',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, department, status, urgencyLevel } = query || {}
      let demands = recruitmentDemandService.findAll()
      
      if (department) {
        demands = demands.filter((d: any) => d.department === department)
      }
      if (status) {
        demands = demands.filter((d: any) => d.status === status)
      }
      if (urgencyLevel) {
        demands = demands.filter((d: any) => d.urgencyLevel === urgencyLevel)
      }
      
      // 按创建时间倒序
      demands.sort((a: any, b: any) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
      
      return mockResponse.success(paginate(demands, Number(page), Number(size)))
    }
  },
  
  // 获取招聘需求详情
  {
    url: '/api/recruitment/demands/:id',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      const demand = recruitmentDemandService.findById(params?.id)
      if (!demand) {
        return mockResponse.error('招聘需求不存在', 404)
      }
      return mockResponse.success(demand)
    }
  },
  
  // 创建招聘需求
  {
    url: '/api/recruitment/demands',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const demand = {
        ...body,
        id: generateId(),
        demandNo: `RD${new Date().getFullYear()}${String(Math.floor(Math.random() * 999) + 1).padStart(3, '0')}`,
        status: '待审核',
        createTime: new Date().toISOString(),
        creatorId: 'U00001', // 从token获取
        creatorName: '张三'
      }
      recruitmentDemandService.create(demand)
      return mockResponse.success(demand, '创建成功')
    }
  },
  
  // 更新招聘需求
  {
    url: '/api/recruitment/demands/:id',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const updated = recruitmentDemandService.update(params?.id, {
        ...body,
        updateTime: new Date().toISOString()
      })
      if (!updated) {
        return mockResponse.error('招聘需求不存在', 404)
      }
      return mockResponse.success(updated)
    }
  },
  
  // 审核招聘需求
  {
    url: '/api/recruitment/demands/:id/approve',
    method: 'post',
    response: ({ params, body }: MockRequestOptions) => {
      const demand = recruitmentDemandService.findById(params?.id)
      if (!demand) {
        return mockResponse.error('招聘需求不存垆', 404)
      }
      
      const updated = recruitmentDemandService.update(params?.id, {
        ...demand,
        status: body?.approved ? '已审核' : '已拒绝',
        approveTime: new Date().toISOString(),
        approverId: 'U00002',
        approverName: '李四',
        approveComment: body?.comment
      })
      
      return mockResponse.success(updated)
    }
  },
  
  // ========== 招聘计划管理 ==========
  
  // 获取招聘计划列表
  {
    url: '/api/recruitment/plans',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, year, status } = query || {}
      let plans = recruitmentPlanService.findAll()
      
      if (year) {
        plans = plans.filter((p: any) => p.year === Number(year))
      }
      if (status) {
        plans = plans.filter((p: any) => p.status === status)
      }
      
      plans.sort((a: any, b: any) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
      
      return mockResponse.success(paginate(plans, Number(page), Number(size)))
    }
  },
  
  // 获取招聘计划详情
  {
    url: '/api/recruitment/plans/:id',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      const plan = recruitmentPlanService.findById(params?.id)
      if (!plan) {
        return mockResponse.error('招聘计划不存在', 404)
      }
      
      // 附加关联的招聘需求
      const demands = recruitmentDemandService.findAll().filter((d: any) => 
        d.status === '已审核' && new Date(d.expectedOnboardDate).getFullYear() === plan.year
      )
      
      return mockResponse.success({
        ...plan,
        demands: demands.slice(0, 10) // 只返回前10个
      })
    }
  },
  
  // 创建招聘计划
  {
    url: '/api/recruitment/plans',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const year = new Date().getFullYear()
      const quarter = Math.ceil((new Date().getMonth() + 1) / 3)
      
      const plan = {
        ...body,
        id: generateId(),
        planNo: `RP${year}Q${quarter}${String(Math.floor(Math.random() * 99) + 1).padStart(2, '0')}`,
        status: '草稿',
        createTime: new Date().toISOString(),
        creatorId: 'U00001',
        creatorName: '张三'
      }
      recruitmentPlanService.create(plan)
      return mockResponse.success(plan, '创建成功')
    }
  },
  
  // 发布招聘计划
  {
    url: '/api/recruitment/plans/:id/publish',
    method: 'post',
    response: ({ params }: MockRequestOptions) => {
      const plan = recruitmentPlanService.findById(params?.id)
      if (!plan) {
        return mockResponse.error('招聘计划不存在', 404)
      }
      
      const updated = recruitmentPlanService.update(params?.id, {
        ...plan,
        status: '已发布',
        publishDate: new Date().toISOString()
      })
      
      return mockResponse.success(updated)
    }
  },
  
  // ========== 招聘岗位管理 ==========
  
  // 获取招聘岗位列表
  {
    url: '/api/recruitment/positions',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, status, department } = query || {}
      let demands = recruitmentDemandService.findAll().filter((d: any) => d.status === '已发布' || d.status === '招聘中')
      
      if (status) {
        demands = demands.filter((d: any) => d.status === status)
      }
      if (department) {
        demands = demands.filter((d: any) => d.department === department)
      }
      
      // 转换为岗位格式
      const positions = demands.map((demand: any) => ({
        id: demand.id,
        planId: demand.planId || null,
        name: demand.positionName,
        department: demand.department,
        type: demand.positionType,
        level: demand.educationRequirement,
        count: demand.recruitNumber,
        salary: `${demand.salary.min / 1000}k-${demand.salary.max / 1000}k`,
        requirements: {
          education: demand.educationRequirement,
          major: demand.majorRequirement,
          experience: `${demand.workExperienceRequirement}年以上相关工作经验`,
          skills: demand.skillRequirements,
          other: '具有良好的沟通能力和团队协作精神'
        },
        duties: demand.jobDescription.split('\n'),
        publishDate: demand.createTime.split('T')[0],
        deadline: demand.expectedOnboardDate,
        status: demand.status,
        applicantCount: jobApplicationService.findAll().filter((a: any) => a.positionId === demand.id).length,
        viewCount: Math.floor(Math.random() * 500) + 100
      }))
      
      return mockResponse.success(paginate(positions, Number(page), Number(size)))
    }
  },
  
  // ========== 简历管理 ==========
  
  // 获取简历列表
  {
    url: '/api/recruitment/resumes',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, keyword, education, recruitmentChannel } = query || {}
      let resumes = resumeService.findAll()
      
      if (keyword) {
        resumes = resumes.filter((r: any) => 
          r.name.includes(keyword) || 
          r.phoneNumber.includes(keyword) ||
          r.email.includes(keyword)
        )
      }
      if (education) {
        resumes = resumes.filter((r: any) => 
          r.educationHistory.some((e: any) => e.degree === education)
        )
      }
      if (recruitmentChannel) {
        resumes = resumes.filter((r: any) => r.recruitmentChannel === recruitmentChannel)
      }
      
      resumes.sort((a: any, b: any) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
      
      return mockResponse.success(paginate(resumes, Number(page), Number(size)))
    }
  },
  
  // 获取简历详情
  {
    url: '/api/recruitment/resumes/:id',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      const resume = resumeService.findById(params?.id)
      if (!resume) {
        return mockResponse.error('简历不存在', 404)
      }
      
      // 附加申请记录
      const applications = jobApplicationService.findAll().filter((a: any) => a.resumeId === resume.id)
      
      return mockResponse.success({
        ...resume,
        applications
      })
    }
  },
  
  // 解析简历（AI功能模拟）
  {
    url: '/api/recruitment/resume/parse',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      // 模拟AI解析简历
      const parsedData = {
        name: '王五',
        phoneNumber: '13800138000',
        email: '<EMAIL>',
        education: '硕士',
        workYears: 5,
        skills: ['Java', 'Spring Boot', 'MySQL'],
        lastCompany: '某科技公司',
        lastPosition: '高级软件工程师'
      }
      
      return mockResponse.success({
        success: true,
        parsedData,
        confidence: 0.95
      })
    }
  },
  
  // ========== 岗位申请管理 ==========
  
  // 获取应聘者列表
  {
    url: '/api/recruitment/applicants',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, positionId, status } = query || {}
      let applications = jobApplicationService.findAll()
      
      if (positionId) {
        applications = applications.filter((a: any) => a.positionId === positionId)
      }
      if (status) {
        applications = applications.filter((a: any) => a.status === status)
      }
      
      // 关联简历信息
      const applicants = applications.map((app: any) => {
        const resume = resumeService.findById(app.resumeId)
        const position = recruitmentDemandService.findById(app.positionId)
        
        return {
          id: app.id,
          positionId: app.positionId,
          positionName: app.positionName,
          
          // 基本信息
          name: resume?.name || '未知',
          gender: resume?.gender || '',
          age: resume?.age || 0,
          phone: resume?.phoneNumber || '',
          email: resume?.email || '',
          
          // 教育背景
          education: resume?.educationHistory?.[0]?.degree || '',
          university: resume?.educationHistory?.[0]?.school || '',
          major: resume?.educationHistory?.[0]?.major || '',
          graduationDate: resume?.educationHistory?.[0]?.endDate || '',
          
          // 工作经验
          workYears: new Date().getFullYear() - new Date(resume?.workHistory?.[0]?.startDate || new Date()).getFullYear(),
          currentCompany: resume?.workHistory?.[0]?.company || '应届毕业生',
          currentPosition: resume?.workHistory?.[0]?.position || '',
          
          // 期望
          expectedSalary: `${(resume?.expectedSalary || 0) / 1000}k`,
          
          // 申请信息
          applyDate: app.applicationTime,
          status: app.status,
          resumeUrl: `/resumes/resume_${app.resumeId}.pdf`,
          attachments: [`/attachments/cert_${app.resumeId}.pdf`],
          
          // 评价
          score: app.screeningResult?.result === '通过' ? 80 + Math.floor(Math.random() * 20) : null,
          evaluation: app.screeningResult?.comment || null,
          
          // AI分析结果
          aiScore: app.matchScore,
          aiMatchRate: app.matchScore,
          aiTags: ['技术扎实', '经验丰富', '学历匹配', '薪资合理'].filter(() => Math.random() > 0.5)
        }
      })
      
      applicants.sort((a: any, b: any) => new Date(b.applyDate).getTime() - new Date(a.applyDate).getTime())
      
      return mockResponse.success(paginate(applicants, Number(page), Number(size)))
    }
  },
  
  // 获取岗位申请列表
  {
    url: '/api/recruitment/applications',
    method: 'get',  
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, positionId, status, department } = query || {}
      let applications = jobApplicationService.findAll()
      
      if (positionId) {
        applications = applications.filter((a: any) => a.positionId === positionId)
      }
      if (status) {
        applications = applications.filter((a: any) => a.status === status)
      }
      if (department) {
        applications = applications.filter((a: any) => a.department === department)
      }
      
      // 关联简历信息
      applications = applications.map((app: any) => {
        const resume = resumeService.findById(app.resumeId)
        return {
          ...app,
          applicantName: resume?.name || '未知',
          applicantPhone: resume?.phoneNumber || '',
          applicantEmail: resume?.email || '',
          education: resume?.educationHistory?.[0]?.degree || ''
        }
      })
      
      applications.sort((a: any, b: any) => new Date(b.applicationTime).getTime() - new Date(a.applicationTime).getTime())
      
      return mockResponse.success(paginate(applications, Number(page), Number(size)))
    }
  },
  
  // 获取岗位申请详情
  {
    url: '/api/recruitment/applications/:id',
    method: 'get',
    response: ({ params }: MockRequestOptions) => {
      const application = jobApplicationService.findById(params?.id)
      if (!application) {
        return mockResponse.error('申请记录不存在', 404)
      }
      
      const resume = resumeService.findById(application.resumeId)
      
      return mockResponse.success({
        ...application,
        resume
      })
    }
  },
  
  // 更新申请状态
  {
    url: '/api/recruitment/applications/:id/status',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const application = jobApplicationService.findById(params?.id)
      if (!application) {
        return mockResponse.error('申请记录不存在', 404)
      }
      
      const updated = jobApplicationService.update(params?.id, {
        ...application,
        status: body?.status,
        currentStep: body?.currentStep || application.currentStep,
        processLogs: [
          ...application.processLogs,
          {
            action: `状态更新为${body?.status}`,
            time: new Date().toISOString(),
            operator: '张三',
            remark: body?.remark || ''
          }
        ]
      })
      
      return mockResponse.success(updated)
    }
  },
  
  // AI简历筛选
  {
    url: '/api/recruitment/applications/ai-screening',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { positionId, applicationIds } = body || {}
      
      if (!positionId || !applicationIds?.length) {
        return mockResponse.error('参数错误', 400)
      }
      
      // 模拟AI筛选结果
      const results = applicationIds.map((id: string) => ({
        applicationId: id,
        matchScore: Math.floor(Math.random() * 40) + 60,
        recommendation: Math.random() > 0.3 ? '推荐' : '不推荐',
        analysis: {
          education: Math.random() > 0.5 ? '符合' : '不符合',
          experience: Math.random() > 0.4 ? '符合' : '不符合',
          skills: Math.random() > 0.3 ? '匹配' : '部分匹配',
          overall: '综合评估结果'
        }
      }))
      
      return mockResponse.success({
        positionId,
        screeningTime: new Date().toISOString(),
        results
      })
    }
  },
  
  // ========== 面试管理 ==========
  
  // 获取面试安排列表
  {
    url: '/api/recruitment/interviews',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, date, status, interviewType } = query || {}
      let interviews = interviewScheduleService.findAll()
      
      if (date) {
        interviews = interviews.filter((i: any) => i.interviewDate === date)
      }
      if (status) {
        interviews = interviews.filter((i: any) => i.status === status)
      }
      if (interviewType) {
        interviews = interviews.filter((i: any) => i.interviewType === interviewType)
      }
      
      // 关联申请和简历信息
      interviews = interviews.map((interview: any) => {
        const application = jobApplicationService.findById(interview.applicationId)
        const resume = application ? resumeService.findById(application.resumeId) : null
        
        return {
          ...interview,
          applicantId: application?.id || '',
          applicantName: resume?.name || '未知',
          positionName: application?.positionName || '',
          round: interview.interviewType,
          type: interview.interviewForm === '现场面试' ? '技术面试' : interview.interviewForm,
          date: interview.interviewDate,
          time: interview.interviewTime,
          result: interview.status === '已完成' ? (Math.random() > 0.3 ? '通过' : '未通过') : null,
          feedback: interview.status === '已完成' ? '面试表现良好' : null,
          confirmStatus: interview.status === '待确认' ? '待确认' : '已确认'
        }
      })
      
      interviews.sort((a: any, b: any) => 
        new Date(b.interviewDate + ' ' + b.interviewTime).getTime() - 
        new Date(a.interviewDate + ' ' + a.interviewTime).getTime()
      )
      
      return mockResponse.success(paginate(interviews, Number(page), Number(size)))
    }
  },
  
  // 创建面试安排
  {
    url: '/api/recruitment/interviews',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const interview = {
        ...body,
        id: generateId(),
        status: '待确认',
        notificationStatus: '未通知',
        createTime: new Date().toISOString()
      }
      interviewScheduleService.create(interview)
      
      // 更新申请状态
      const application = jobApplicationService.findById(body?.applicationId)
      if (application) {
        jobApplicationService.update(application.id, {
          ...application,
          status: '待面试',
          currentStep: '面试安排'
        })
      }
      
      return mockResponse.success(interview, '面试安排创建成功')
    }
  },
  
  // 更新面试结果
  {
    url: '/api/recruitment/interviews/:id/result',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const interview = interviewScheduleService.findById(params?.id)
      if (!interview) {
        return mockResponse.error('面试安排不存在', 404)
      }
      
      const updated = interviewScheduleService.update(params?.id, {
        ...interview,
        status: '已完成',
        result: body?.result,
        feedback: body?.feedback,
        score: body?.score
      })
      
      // 更新申请状态
      const application = jobApplicationService.findById(interview.applicationId)
      if (application) {
        const newStatus = body?.result === '通过' ? '面试通过' : '面试未通过'
        jobApplicationService.update(application.id, {
          ...application,
          status: newStatus,
          currentStep: body?.result === '通过' ? 'Offer准备' : '流程结束',
          interviews: [
            ...(application.interviews || []),
            {
              round: application.interviews?.length + 1 || 1,
              type: interview.interviewType,
              date: new Date().toISOString(),
              result: body?.result,
              score: body?.score,
              comment: body?.feedback,
              interviewers: interview.interviewers.map((i: any) => i.name)
            }
          ]
        })
      }
      
      return mockResponse.success(updated)
    }
  },
  
  // 视频面试房间管理（模拟）
  {
    url: '/api/recruitment/interviews/:id/video-room',
    method: 'post',
    response: ({ params }: MockRequestOptions) => {
      return mockResponse.success({
        interviewId: params?.id,
        roomId: generateId(),
        roomUrl: `https://video.example.com/room/${generateId()}`,
        accessToken: generateId(),
        expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString() // 2小时后过期
      })
    }
  },
  
  // ========== Offer管理 ==========
  
  // 发送Offer
  {
    url: '/api/recruitment/offers',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const application = jobApplicationService.findById(body?.applicationId)
      if (!application) {
        return mockResponse.error('申请记录不存在', 404)
      }
      
      const offerInfo = {
        ...body,
        offerId: generateId(),
        offerDate: new Date().toISOString(),
        offerStatus: '已发送',
        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7天有效期
      }
      
      // 更新申请记录
      jobApplicationService.update(application.id, {
        ...application,
        status: '待录用',
        currentStep: 'Offer发放',
        offerInfo
      })
      
      return mockResponse.success(offerInfo, 'Offer发送成功')
    }
  },
  
  // 处理Offer响应
  {
    url: '/api/recruitment/offers/:applicationId/response',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const application = jobApplicationService.findById(params?.applicationId)
      if (!application || !application.offerInfo) {
        return mockResponse.error('Offer不存在', 404)
      }
      
      const newStatus = body?.accepted ? '已录用' : '已拒绝'
      const updated = jobApplicationService.update(application.id, {
        ...application,
        status: newStatus,
        currentStep: body?.accepted ? '入职准备' : '流程结束',
        offerInfo: {
          ...application.offerInfo,
          offerStatus: body?.accepted ? '已接受' : '已拒绝',
          responseDate: new Date().toISOString(),
          responseRemark: body?.remark
        }
      })
      
      return mockResponse.success(updated)
    }
  },
  
  // ========== 人才库管理 ==========
  
  // 获取人才库列表
  {
    url: '/api/recruitment/talent-pool',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { page = 1, size = 10, category, tags, isActive } = query || {}
      let talents = talentPoolService.findAll()
      
      if (category) {
        talents = talents.filter((t: any) => t.category === category)
      }
      if (tags) {
        const tagArray = Array.isArray(tags) ? tags : [tags]
        talents = talents.filter((t: any) => 
          tagArray.some(tag => t.tags.includes(tag))
        )
      }
      if (isActive !== undefined) {
        talents = talents.filter((t: any) => t.isActive === (isActive === 'true'))
      }
      
      // 关联简历信息
      talents = talents.map((talent: any) => {
        const resume = resumeService.findById(talent.resumeId)
        return {
          ...talent,
          name: resume?.name || '未知',
          phoneNumber: resume?.phoneNumber || '',
          email: resume?.email || '',
          currentPosition: resume?.workHistory?.[0]?.position || '',
          education: resume?.educationHistory?.[0]?.degree || ''
        }
      })
      
      return mockResponse.success(paginate(talents, Number(page), Number(size)))
    }
  },
  
  // 添加到人才库
  {
    url: '/api/recruitment/talent-pool',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const talent = {
        ...body,
        id: generateId(),
        isActive: true,
        addTime: new Date().toISOString(),
        addedBy: '张三'
      }
      talentPoolService.create(talent)
      return mockResponse.success(talent, '已添加到人才库')
    }
  },
  
  // 更新人才库记录
  {
    url: '/api/recruitment/talent-pool/:id',
    method: 'put',
    response: ({ params, body }: MockRequestOptions) => {
      const updated = talentPoolService.update(params?.id, {
        ...body,
        updateTime: new Date().toISOString()
      })
      if (!updated) {
        return mockResponse.error('人才记录不存在', 404)
      }
      return mockResponse.success(updated)
    }
  },
  
  // 人才推荐（AI功能模拟）
  {
    url: '/api/recruitment/talent-pool/recommend',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { positionId } = body || {}
      
      if (!positionId) {
        return mockResponse.error('请指定职位', 400)
      }
      
      // 模拟AI推荐
      const talents = talentPoolService.findAll().filter((t: any) => t.isActive)
      const recommendations = talents
        .map((talent: any) => {
          const resume = resumeService.findById(talent.resumeId)
          return {
            talentId: talent.id,
            name: resume?.name || '未知',
            matchScore: Math.floor(Math.random() * 40) + 60,
            matchReasons: [
              '专业背景匹配',
              '工作经验符合要求',
              '技能匹配度高'
            ].filter(() => Math.random() > 0.3),
            tags: talent.tags,
            category: talent.category
          }
        })
        .sort((a: any, b: any) => b.matchScore - a.matchScore)
        .slice(0, 10)
      
      return mockResponse.success({
        positionId,
        recommendTime: new Date().toISOString(),
        recommendations
      })
    }
  },
  
  // ========== 招聘统计分析 ==========
  
  // 获取招聘统计
  {
    url: '/api/recruitment/statistics',
    method: 'get',
    response: () => {
      const applications = jobApplicationService.findAll()
      const interviews = interviewScheduleService.findAll()
      const demands = recruitmentDemandService.findAll()
      
      // 总体统计
      const overview = {
        activePositions: demands.filter((d: any) => d.status === '招聘中').length,
        totalApplicants: applications.length,
        interviewScheduled: interviews.length,
        offersGiven: applications.filter((a: any) => a.offerInfo).length,
        hired: applications.filter((a: any) => a.status === '已录用').length,
        conversionRate: applications.length > 0 ? 
          ((applications.filter((a: any) => a.status === '已录用').length / applications.length) * 100).toFixed(1) : 0
      }
      
      // 各阶段统计
      const funnelStats = [
        { stage: '投递简历', count: applications.length },
        { stage: '简历筛选', count: applications.filter((a: any) => a.screeningResult).length },
        { stage: '初试', count: applications.filter((a: any) => a.interviews?.length >= 1).length },
        { stage: '复试', count: applications.filter((a: any) => a.interviews?.length >= 2).length },
        { stage: '终试', count: applications.filter((a: any) => a.interviews?.length >= 3).length },
        { stage: '发放Offer', count: applications.filter((a: any) => a.offerInfo).length },
        { stage: '成功入职', count: applications.filter((a: any) => a.status === '已录用').length }
      ]
      
      // 部门需求统计
      const departmentGroups = demands.reduce((acc: any, d: any) => {
        if (!acc[d.department]) {
          acc[d.department] = {
            department: d.department,
            positions: 0,
            applicants: 0,
            hired: 0
          }
        }
        acc[d.department].positions += 1
        return acc
      }, {} as Record<string, any>)
      
      applications.forEach((app: any) => {
        const demand = demands.find((d: any) => d.id === app.positionId)
        if (demand && departmentGroups[demand.department]) {
          departmentGroups[demand.department].applicants += 1
          if (app.status === '已录用') {
            departmentGroups[demand.department].hired += 1
          }
        }
      })
      
      const departmentStats = Object.values(departmentGroups)
      
      // 招聘渠道统计
      const channelGroups = applications.reduce((acc: any, app: any) => {
        const resume = resumeService.findById(app.resumeId)
        const channel = resume?.recruitmentChannel || '其他'
        
        if (!acc[channel]) {
          acc[channel] = {
            channel,
            applicants: 0,
            quality: 0,
            hired: 0
          }
        }
        
        acc[channel].applicants += 1
        acc[channel].quality += app.matchScore || 0
        if (app.status === '已录用') {
          acc[channel].hired += 1
        }
        
        return acc
      }, {} as Record<string, any>)
      
      const channelStats = Object.values(channelGroups).map((stat: any) => ({
        ...stat,
        quality: Math.round(stat.quality / stat.applicants)
      }))
      
      // 时间趋势
      const monthlyTrend = Array.from({ length: 6 }, (_, i) => {
        const date = new Date()
        date.setMonth(date.getMonth() - (5 - i))
        const month = date.toISOString().slice(0, 7)
        
        const monthApplications = applications.filter((a: any) => 
          a.applicationTime.startsWith(month)
        )
        
        return {
          month,
          positions: demands.filter((d: any) => d.createTime.startsWith(month)).length,
          applicants: monthApplications.length,
          hired: monthApplications.filter((a: any) => a.status === '已录用').length
        }
      })
      
      return mockResponse.success({
        overview,
        funnelStats,
        departmentStats,
        channelStats,
        monthlyTrend
      })
    }
  },
  
  // 获取招聘概览数据
  {
    url: '/api/recruitment/statistics/overview',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { startDate, endDate } = query || {}
      
      const applications = jobApplicationService.findAll()
      const interviews = interviewScheduleService.findAll()
      
      return mockResponse.success({
        period: { startDate, endDate },
        summary: {
          totalApplications: applications.length,
          totalInterviews: interviews.length,
          totalOffers: applications.filter((a: any) => a.offerInfo).length,
          totalHired: applications.filter((a: any) => a.status === '已录用').length,
          activePositions: recruitmentDemandService.findAll().filter((d: any) => d.status === '招聘中').length
        },
        conversionRate: {
          applicationToInterview: 0.45,
          interviewToOffer: 0.30,
          offerToHire: 0.85
        },
        channelDistribution: [
          { channel: '智联招聘', count: 120, percentage: 30 },
          { channel: '学校官网', count: 80, percentage: 20 },
          { channel: 'BOSS直聘', count: 60, percentage: 15 },
          { channel: '内部推荐', count: 40, percentage: 10 },
          { channel: '其他', count: 100, percentage: 25 }
        ]
      })
    }
  },
  
  // 获取招聘漏斗分析
  {
    url: '/api/recruitment/statistics/funnel',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      const { positionId, department } = query || {}
      
      let applications = jobApplicationService.findAll()
      
      if (positionId) {
        applications = applications.filter((a: any) => a.positionId === positionId)
      }
      if (department) {
        applications = applications.filter((a: any) => a.department === department)
      }
      
      const total = applications.length
      const screened = applications.filter((a: any) => a.screeningResult).length
      const interviewed = applications.filter((a: any) => a.interviews?.length > 0).length
      const offered = applications.filter((a: any) => a.offerInfo).length
      const hired = applications.filter((a: any) => a.status === '已录用').length
      
      return mockResponse.success({
        stages: [
          { stage: '简历投递', count: total, rate: 100 },
          { stage: '简历筛选', count: screened, rate: total > 0 ? (screened / total * 100).toFixed(2) : 0 },
          { stage: '面试', count: interviewed, rate: total > 0 ? (interviewed / total * 100).toFixed(2) : 0 },
          { stage: 'Offer', count: offered, rate: total > 0 ? (offered / total * 100).toFixed(2) : 0 },
          { stage: '入职', count: hired, rate: total > 0 ? (hired / total * 100).toFixed(2) : 0 }
        ]
      })
    }
  },
  
  // 导出招聘数据
  {
    url: '/api/recruitment/export',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      const { type, format = 'excel' } = body || {}
      
      return mockResponse.success({
        downloadUrl: `/download/recruitment_${type}_${Date.now()}.${format === 'pdf' ? 'pdf' : 'xlsx'}`,
        fileName: `招聘${type}报表_${new Date().toISOString().split('T')[0]}.${format === 'pdf' ? 'pdf' : 'xlsx'}`,
        fileSize: Math.floor(Math.random() * 2000000) + 500000,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      })
    }
  }
] as MockMethod[]

export default recruitmentMock