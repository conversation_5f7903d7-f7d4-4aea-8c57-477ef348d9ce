/**
 * @name 绩效考核Mock模块
 * @description 提供绩效考核相关的Mock API接口
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import { mockResponse, paginate } from '../utils'
import { MockServiceManager } from '../service'
import { PerformanceGenerator } from '../generators/PerformanceGenerator'

// 获取服务实例
const templateService = MockServiceManager.getService('assessmentTemplates')
const planService = MockServiceManager.getService('assessmentPlans')
const taskService = MockServiceManager.getService('assessmentTasks')
const evaluationService = MockServiceManager.getService('evaluations360')
const appealService = MockServiceManager.getService('assessmentAppeals')
const statisticsService = MockServiceManager.getService('assessmentStatistics')
const gradeService = MockServiceManager.getService('assessmentGrades')

// 初始化绩效考核数据
const initPerformanceData = () => {
  if (templateService.readAll().length === 0) {
    const dataset = PerformanceGenerator.generateTestDataset()
    dataset.assessmentTemplates.forEach((t: any) => templateService.create(t))
    dataset.assessmentPlans.forEach((p: any) => planService.create(p))
    dataset.assessmentTasks.forEach((t: any) => taskService.create(t))
    dataset.evaluations360.forEach((e: any) => evaluationService.create(e))
    dataset.assessmentAppeals.forEach((a: any) => appealService.create(a))
    dataset.assessmentStatistics.forEach((s: any) => statisticsService.create(s))
    dataset.assessmentGrades.forEach(g => gradeService.create(g))
  }
}

export default [
  // ==================== 考核模板管理接口 ====================
  
  {
    url: '/api/performance/template/list',
    method: 'get',
    response: ({ query }: { query: Record<string, any> }) => {
      initPerformanceData()
      const templates = templateService.readAll() || []
      
      // 过滤条件
      let filtered = [...templates]
      if (query.assessmentType) {
        filtered = filtered.filter((t: any) => t.assessmentType === query.assessmentType)
      }
      if (query.status) {
        filtered = filtered.filter((t: any) => t.status === query.status)
      }
      if (query.keyword) {
        filtered = filtered.filter((t: any) => 
          t.name.includes(query.keyword) ||
          t.assessmentType.includes(query.keyword)
        )
      }
      
      // 分页处理
      const result = paginate(filtered, Number(query.page) || 1, Number(query.pageSize) || 10)
      
      return mockResponse.success(result)
    }
  },
  
  {
    url: '/api/performance/template/detail/:id',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      initPerformanceData()
      const templates = templateService.readAll() || []
      const template = templates.find((t: any) => t.id === params.id)
      
      if (!template) {
        return mockResponse.error('模板不存在', 404)
      }
      
      return mockResponse.success(template)
    }
  },
  
  {
    url: '/api/performance/template/create',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      initPerformanceData()
      
      const newTemplate = {
        ...PerformanceGenerator.generateAssessmentTemplate(),
        ...body,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      
      templateService.create(newTemplate)
      
      return mockResponse.success(newTemplate, '创建成功')
    }
  },
  
  {
    url: '/api/performance/template/update/:id',
    method: 'put',
    response: ({ params, body }: { params: Record<string, any>, body: Record<string, any> }) => {
      initPerformanceData()
      const templates = templateService.readAll() || []
      const index = templates.findIndex((t: any) => t.id === params.id)
      
      if (index === -1) {
        return mockResponse.error('模板不存在', 404)
      }
      
      const updated = templateService.update(params.id, {
        ...body,
        updateTime: new Date().toISOString()
      })
      return mockResponse.success(updated, '更新成功')
    }
  },
  
  {
    url: '/api/performance/template/delete/:id',
    method: 'delete',
    response: ({ params }: { params: Record<string, any> }) => {
      initPerformanceData()
      const templates = templateService.readAll() || []
      const index = templates.findIndex((t: any) => t.id === params.id)
      
      if (index === -1) {
        return mockResponse.error('模板不存在', 404)
      }
      
      // 检查是否有方案在使用此模板
      const plans = planService.readAll() || []
      const isUsed = plans.some((p: any) => p.templateId === params.id)
      
      if (isUsed) {
        return mockResponse.error('该模板正在被考核方案使用，无法删除', 400)
      }
      
      templateService.delete(params.id)
      
      return mockResponse.error( '删除成功')
    }
  },
  
  // ==================== 考核方案管理接口 ====================
  
  {
    url: '/api/performance/plan/list',
    method: 'get',
    response: ({ query }: { query: Record<string, any> }) => {
      initPerformanceData()
      const plans = planService.readAll() || []
      
      // 过滤条件
      let filtered = [...plans]
      if (query.assessmentType) {
        filtered = filtered.filter((p: any) => p.assessmentType === query.assessmentType)
      }
      if (query.status) {
        filtered = filtered.filter((p: any) => p.status === query.status)
      }
      if (query.year) {
        filtered = filtered.filter((p: any) => 
          p.assessmentYear === Number(query.year) ||
          (p.assessmentPeriod && p.assessmentPeriod.includes(query.year))
        )
      }
      if (query.keyword) {
        filtered = filtered.filter((p: any) => 
          p.name.includes(query.keyword) ||
          p.assessmentType.includes(query.keyword)
        )
      }
      
      // 分页处理
      const result = paginate(filtered, Number(query.page) || 1, Number(query.pageSize) || 10)
      
      return mockResponse.success(result)
    }
  },
  
  {
    url: '/api/performance/plan/detail/:id',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      initPerformanceData()
      const plans = planService.readAll() || []
      const plan = plans.find((p: any) => p.id === params.id)
      
      if (!plan) {
        return mockResponse.error('方案不存在', 404)
      }
      
      // 添加关联信息
      const templates = templateService.readAll() || []
      const template = templates.find((t: any) => t.id === plan.templateId)
      
      return mockResponse.success({
        ...plan,
        template
      })
    }
  },
  
  {
    url: '/api/performance/plan/create',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      initPerformanceData()
      
      const newPlan = {
        ...PerformanceGenerator.generateAssessmentPlan(),
        ...body,
        status: '草稿',
        createTime: new Date().toISOString()
      }
      
      planService.create(newPlan)
      
      return mockResponse.success(newPlan, '创建成功')
    }
  },
  
  {
    url: '/api/performance/plan/update/:id',
    method: 'put',
    response: ({ params, body }: { params: Record<string, any>, body: Record<string, any> }) => {
      initPerformanceData()
      const plans = planService.readAll() || []
      const index = plans.findIndex((p: any) => p.id === params.id)
      
      if (index === -1) {
        return mockResponse.error( '方案不存在', 404)
      }
      
      // 已发布的方案只能修改部分字段
      if (plans[index].status !== '草稿' && body.templateId !== plans[index].templateId) {
        return mockResponse.error( '已发布的方案不能修改模板', 400)
      }
      
      const updated = planService.update(params.id, {
        ...body,
        updateTime: new Date().toISOString()
      })
      
      return mockResponse.success(updated, '更新成功')
    }
  },
  
  {
    url: '/api/performance/plan/publish/:id',
    method: 'post',
    response: ({ params }: { params: Record<string, any> }) => {
      initPerformanceData()
      const plans = planService.readAll() || []
      const index = plans.findIndex((p: any) => p.id === params.id)
      
      if (index === -1) {
        return mockResponse.error( '方案不存在', 404)
      }
      
      if (plans[index].status !== '草稿') {
        return mockResponse.error( '只有草稿状态的方案可以发布', 400)
      }
      
      const updated = planService.update(params.id, {
        status: '已发布',
        publishTime: new Date().toISOString()
      })
      
      return mockResponse.success(updated, '发布成功')
    }
  },
  
  {
    url: '/api/performance/plan/start/:id',
    method: 'post',
    response: ({ params }: { params: Record<string, any> }) => {
      initPerformanceData()
      const plans = planService.readAll() || []
      const tasks = taskService.readAll() || []
      const index = plans.findIndex((p: any) => p.id === params.id)
      
      if (index === -1) {
        return mockResponse.error( '方案不存在', 404)
      }
      
      if (plans[index].status !== '已发布') {
        return mockResponse.error( '只有已发布的方案可以启动', 400)
      }
      
      // 更新方案状态
      const updatedPlan = planService.update(params.id, {
        status: '进行中',
        actualStartTime: new Date().toISOString()
      })
      
      // 生成考核任务
      const employees = MockServiceManager.getService('employees').readAll() || []
      const scopeDepts = plans[index].personnelScope.includeConditions.departments
      const scopeEmployees = employees.filter((e: any) => 
        scopeDepts.includes(e.department) &&
        e.status === '在职'
      )
      
      // 为每个符合条件的员工创建考核任务
      const newTasks = scopeEmployees.map((emp: any) => 
        PerformanceGenerator.generateAssessmentTask(params.id, emp.id)
      )
      
      newTasks.forEach((task: any) => taskService.create(task))
      
      return mockResponse.success({
        plan: updatedPlan,
        taskCount: newTasks.length
      }, '考核启动成功')
    }
  },
  
  {
    url: '/api/performance/plan/monitor/:id',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      initPerformanceData()
      const statistics = statisticsService.readAll() || []
      const stat = statistics.find((s: any) => s.planId === params.id)
      
      if (!stat) {
        // 如果没有统计数据，生成一份
        const newStat = PerformanceGenerator.generateAssessmentStatistics(params.id)
        statisticsService.create(newStat)
        return mockResponse.success(newStat)
      }
      
      return mockResponse.success(stat)
    }
  },
  
  {
    url: '/api/performance/plan/delete/:id',
    method: 'delete',
    response: ({ params }: { params: Record<string, any> }) => {
      initPerformanceData()
      const plans = planService.readAll() || []
      const index = plans.findIndex((p: any) => p.id === params.id)
      
      if (index === -1) {
        return mockResponse.error( '方案不存在', 404)
      }
      
      if (plans[index].status !== '草稿') {
        return mockResponse.error( '只能删除草稿状态的方案', 400)
      }
      
      planService.delete(params.id)
      
      return mockResponse.error( '删除成功')
    }
  },
  
  // ==================== 考核任务管理接口 ====================
  
  {
    url: '/api/performance/task/list',
    method: 'get',
    response: ({ query }: { query: Record<string, any> }) => {
      initPerformanceData()
      const tasks = taskService.readAll() || []
      
      // 过滤条件
      let filtered = [...tasks]
      if (query.planId) {
        filtered = filtered.filter((t: any) => t.planId === query.planId)
      }
      if (query.employeeId) {
        filtered = filtered.filter((t: any) => t.employeeId === query.employeeId)
      }
      if (query.department) {
        filtered = filtered.filter((t: any) => t.department === query.department)
      }
      if (query.currentStep) {
        filtered = filtered.filter((t: any) => t.currentStep === query.currentStep)
      }
      if (query.status) {
        filtered = filtered.filter((t: any) => t.status === query.status)
      }
      if (query.keyword) {
        filtered = filtered.filter((t: any) => 
          t.employeeName.includes(query.keyword) ||
          t.department.includes(query.keyword)
        )
      }
      
      // 分页处理
      const result = paginate(filtered, Number(query.page) || 1, Number(query.pageSize) || 10)
      
      return mockResponse.success(result)
    }
  },
  
  {
    url: '/api/performance/task/detail/:id',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      initPerformanceData()
      const tasks = taskService.readAll() || []
      const task = tasks.find((t: any) => t.id === params.id)
      
      if (!task) {
        return mockResponse.error( '任务不存在', 404)
      }
      
      // 获取360度评价
      const evaluations = evaluationService.readAll() || []
      const taskEvaluations = evaluations.filter((e: any) => e.taskId === params.id)
      
      // 获取申诉信息
      const appeals = appealService.readAll() || []
      const taskAppeals = appeals.filter((a: any) => a.taskId === params.id)
      
      return mockResponse.success({
        ...task,
        evaluations360: taskEvaluations,
        appeals: taskAppeals
      })
    }
  },
  
  {
    url: '/api/performance/task/submit/:id',
    method: 'post',
    response: ({ params, body }: { params: Record<string, any>, body: Record<string, any> }) => {
      initPerformanceData()
      const tasks = taskService.readAll() || []
      const index = tasks.findIndex((t: any) => t.id === params.id)
      
      if (index === -1) {
        return mockResponse.error( '任务不存在', 404)
      }
      
      if (tasks[index].currentStep !== '个人填报') {
        return mockResponse.error( '当前步骤不允许提交', 400)
      }
      
      // 更新任务
      const updated = taskService.update(params.id, {
        formInstance: {
          ...tasks[index].formInstance,
          ...body.formInstance,
          signature: {
            name: body.employeeName || tasks[index].employeeName,
            time: new Date().toISOString(),
            signatureData: 'data:image/png;base64,signature_mock'
          }
        },
        currentStep: '部门初审',
        updateTime: new Date().toISOString()
      })
      
      return mockResponse.success(updated, '提交成功')
    }
  },
  
  {
    url: '/api/performance/task/review/:id',
    method: 'post',
    response: ({ params, body }: { params: Record<string, any>, body: Record<string, any> }) => {
      initPerformanceData()
      const tasks = taskService.readAll() || []
      const index = tasks.findIndex((t: any) => t.id === params.id)
      
      if (index === -1) {
        return mockResponse.error( '任务不存在', 404)
      }
      
      const task = tasks[index]
      const currentStep = task.currentStep
      
      // 确定下一步骤
      const stepFlow = ['个人填报', '部门初审', '人事处审核', '组织部审核', '校领导审定', '公示', '已完成']
      const currentIndex = stepFlow.indexOf(currentStep)
      let nextStep = stepFlow[currentIndex + 1]
      
      // 跳过组织部审核（非中层干部）
      if (nextStep === '组织部审核' && task.positionType !== '中层干部') {
        nextStep = stepFlow[currentIndex + 2]
      }
      
      // 更新审核记录
      const reviewRecord = {
        step: currentStep,
        reviewerId: body.reviewerId,
        reviewerName: body.reviewerName,
        reviewTime: new Date().toISOString(),
        opinion: body.opinion,
        comment: body.comment,
        signature: {
          name: body.reviewerName,
          time: new Date().toISOString(),
          signatureData: 'data:image/png;base64,signature_mock'
        }
      }
      
      task.reviewRecords.push(reviewRecord)
      task.currentStep = nextStep
      task.status = nextStep === '已完成' ? '已完成' : '进行中'
      
      // 如果完成，生成考核结果
      if (task.status === '已完成') {
        task.assessmentResult = {
          grade: body.grade || '合格',
          score: body.score || 85,
          ranking: Math.floor(Math.random() * 100) + 1,
          comment: body.comment || '工作表现良好，圆满完成年度任务'
        }
      }
      
      task.updateTime = new Date().toISOString()
      
      const updated = taskService.update(params.id, task)
      return mockResponse.success(updated, '审核成功')
    }
  },
  
  {
    url: '/api/performance/task/batch-review',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      initPerformanceData()
      const tasks = taskService.readAll() || []
      const { taskIds, ...reviewData } = body
      
      let successCount = 0
      const errors: string[] = []
      
      taskIds.forEach((taskId: any) => {
        const index = tasks.findIndex((t: any) => t.id === taskId)
        if (index === -1) {
          errors.push(`任务 ${taskId} 不存在`)
          return
        }
        
        const task = tasks[index]
        const currentStep = task.currentStep
        
        // 确定下一步骤
        const stepFlow = ['个人填报', '部门初审', '人事处审核', '组织部审核', '校领导审定', '公示', '已完成']
        const currentIndex = stepFlow.indexOf(currentStep)
        let nextStep = stepFlow[currentIndex + 1]
        
        // 跳过组织部审核（非中层干部）
        if (nextStep === '组织部审核' && task.positionType !== '中层干部') {
          nextStep = stepFlow[currentIndex + 2]
        }
        
        // 更新审核记录
        const reviewRecord = {
          step: currentStep,
          reviewerId: reviewData.reviewerId,
          reviewerName: reviewData.reviewerName,
          reviewTime: new Date().toISOString(),
          opinion: reviewData.opinion,
          comment: reviewData.comment,
          signature: {
            name: reviewData.reviewerName,
            time: new Date().toISOString(),
            signatureData: 'data:image/png;base64,signature_mock'
          }
        }
        
        task.reviewRecords.push(reviewRecord)
        task.currentStep = nextStep
        task.status = nextStep === '已完成' ? '已完成' : '进行中'
        task.updateTime = new Date().toISOString()
        
        taskService.update(taskId, task)
        successCount++
      })
      
      return mockResponse.success({
        successCount,
        failCount: errors.length,
        errors
      }, `批量审核完成，成功 ${successCount} 条`)
    }
  },
  
  // ==================== 360度评价接口 ====================
  
  {
    url: '/api/performance/evaluation/360/list',
    method: 'get',
    response: ({ query }: { query: Record<string, any> }) => {
      initPerformanceData()
      const evaluations = evaluationService.readAll() || []
      
      // 过滤条件
      let filtered = [...evaluations]
      if (query.taskId) {
        filtered = filtered.filter((e: any) => e.taskId === query.taskId)
      }
      if (query.evaluatorId) {
        filtered = filtered.filter((e: any) => e.evaluatorId === query.evaluatorId)
      }
      if (query.evaluationType) {
        filtered = filtered.filter((e: any) => e.evaluationType === query.evaluationType)
      }
      if (query.status) {
        filtered = filtered.filter((e: any) => e.status === query.status)
      }
      
      // 分页处理
      const result = paginate(filtered, Number(query.page) || 1, Number(query.pageSize) || 10)
      
      return mockResponse.success(result)
    }
  },
  
  {
    url: '/api/performance/evaluation/360/submit',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      initPerformanceData()
      const evaluations = evaluationService.readAll() || []
      
      const newEvaluation = {
        ...PerformanceGenerator.generate360Evaluation(body.taskId)[0],
        ...body,
        evaluationTime: new Date().toISOString(),
        status: '已提交'
      }
      
      evaluationService.create(newEvaluation)
      
      return mockResponse.success(newEvaluation, '评价提交成功')
    }
  },
  
  {
    url: '/api/performance/evaluation/360/update/:id',
    method: 'put',
    response: ({ params, body }: { params: Record<string, any>, body: Record<string, any> }) => {
      initPerformanceData()
      const evaluations = evaluationService.readAll() || []
      const index = evaluations.findIndex((e: any) => e.id === params.id)
      
      if (index === -1) {
        return mockResponse.error( '评价不存在', 404)
      }
      
      if (evaluations[index].status === '已提交') {
        return mockResponse.error( '已提交的评价不能修改', 400)
      }
      
      const updated = evaluationService.update(params.id, {
        ...body,
        evaluationTime: new Date().toISOString()
      })
      
      return mockResponse.success(updated, '更新成功')
    }
  },
  
  // ==================== 考核统计分析接口 ====================
  
  {
    url: '/api/performance/statistics/overview',
    method: 'get',
    response: ({ query }: { query: Record<string, any> }) => {
      initPerformanceData()
      const statistics = statisticsService.readAll() || []
      
      if (query.planId) {
        const stat = statistics.find((s: any) => s.planId === query.planId)
        if (!stat) {
          const newStat = PerformanceGenerator.generateAssessmentStatistics(query.planId)
          statisticsService.create(newStat)
          return mockResponse.success(newStat)
        }
        return mockResponse.success(stat)
      }
      
      // 返回所有统计数据
      return mockResponse.success(statistics)
    }
  },
  
  {
    url: '/api/performance/statistics/department',
    method: 'get',
    response: ({ query }: { query: Record<string, any> }) => {
      initPerformanceData()
      const statistics = statisticsService.readAll() || []
      
      if (!query.planId) {
        return mockResponse.error( '请指定考核方案', 400)
      }
      
      const stat = statistics.find((s: any) => s.planId === query.planId)
      if (!stat) {
        return mockResponse.success([], '暂无统计数据')
      }
      
      // 按部门筛选
      let deptStats = stat.departmentStats
      if (query.department) {
        deptStats = deptStats.filter((d: any) => d.department === query.department)
      }
      
      return mockResponse.success(deptStats)
    }
  },
  
  {
    url: '/api/performance/statistics/grade-distribution',
    method: 'get',
    response: ({ query }: { query: Record<string, any> }) => {
      initPerformanceData()
      const statistics = statisticsService.readAll() || []
      
      if (!query.planId) {
        return mockResponse.error( '请指定考核方案', 400)
      }
      
      const stat = statistics.find((s: any) => s.planId === query.planId)
      if (!stat) {
        return mockResponse.error( '暂无统计数据')
      }
      
      return mockResponse.success({
        gradeDistribution: stat.gradeDistribution,
        scoreDistribution: stat.scoreDistribution
      })
    }
  },
  
  {
    url: '/api/performance/statistics/export',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      return mockResponse.success({
        fileUrl: '/downloads/performance_statistics_' + Date.now() + '.xlsx',
        fileName: '绩效考核统计报表.xlsx'
      }, '导出成功')
    }
  },
  
  // ==================== 考核申诉管理接口 ====================
  
  {
    url: '/api/performance/appeal/list',
    method: 'get',
    response: ({ query }: { query: Record<string, any> }) => {
      initPerformanceData()
      const appeals = appealService.readAll() || []
      
      // 过滤条件
      let filtered = [...appeals]
      if (query.taskId) {
        filtered = filtered.filter((a: any) => a.taskId === query.taskId)
      }
      if (query.appealType) {
        filtered = filtered.filter((a: any) => a.appealType === query.appealType)
      }
      if (query.status) {
        filtered = filtered.filter((a: any) => a.status === query.status)
      }
      
      // 分页处理
      const result = paginate(filtered, Number(query.page) || 1, Number(query.pageSize) || 10)
      
      return mockResponse.success(result)
    }
  },
  
  {
    url: '/api/performance/appeal/create',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      initPerformanceData()
      const appeals = appealService.readAll() || []
      
      const newAppeal = {
        ...PerformanceGenerator.generateAppeal(body.taskId),
        ...body,
        status: '待处理',
        submitTime: new Date().toISOString()
      }
      
      appealService.create(newAppeal)
      
      return mockResponse.success(newAppeal, '申诉提交成功')
    }
  },
  
  {
    url: '/api/performance/appeal/handle/:id',
    method: 'post',
    response: ({ params, body }: { params: Record<string, any>, body: Record<string, any> }) => {
      initPerformanceData()
      const appeals = appealService.readAll() || []
      const index = appeals.findIndex((a: any) => a.id === params.id)
      
      if (index === -1) {
        return mockResponse.error( '申诉不存在', 404)
      }
      
      const appeal = appeals[index]
      
      // 添加处理记录
      appeal.handleRecords.push({
        handlerId: body.handlerId,
        handlerName: body.handlerName,
        handleTime: new Date().toISOString(),
        action: body.action,
        opinion: body.opinion
      })
      
      // 更新状态
      if (body.action === '受理') {
        appeal.status = '处理中'
      } else if (body.action === '处理完成') {
        appeal.status = '已处理'
        appeal.result = {
          decision: body.decision,
          reason: body.reason,
          notificationTime: new Date().toISOString()
        }
      }
      
      appealService.update(params.id, appeal)
      
      return mockResponse.success(appeal, '处理成功')
    }
  },
  
  {
    url: '/api/performance/appeal/withdraw/:id',
    method: 'post',
    response: ({ params }: { params: Record<string, any> }) => {
      initPerformanceData()
      const appeals = appealService.readAll() || []
      const index = appeals.findIndex((a: any) => a.id === params.id)
      
      if (index === -1) {
        return mockResponse.error( '申诉不存在', 404)
      }
      
      if (appeals[index].status === '已处理') {
        return mockResponse.error( '已处理的申诉不能撤回', 400)
      }
      
      const updated = appealService.update(params.id, {
        status: '已撤回',
        withdrawTime: new Date().toISOString()
      })
      
      return mockResponse.success(updated, '撤回成功')
    }
  }
  
] as MockMethod[]