/**
 * @name 文件处理Mock模块
 * @description 提供文件上传、下载、预览等Mock API接口
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import { mockResponse, generateId, delay } from '../utils'
import { faker } from '@faker-js/faker/locale/zh_CN'

// 文件类型配置
const FILE_TYPES = {
  image: {
    extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'],
    maxSize: 10 * 1024 * 1024 // 10MB
  },
  document: {
    extensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
    mimeTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ],
    maxSize: 50 * 1024 * 1024 // 50MB
  },
  video: {
    extensions: ['mp4', 'avi', 'mov', 'wmv', 'flv'],
    mimeTypes: ['video/mp4', 'video/x-msvideo', 'video/quicktime', 'video/x-ms-wmv', 'video/x-flv'],
    maxSize: 200 * 1024 * 1024 // 200MB
  },
  archive: {
    extensions: ['zip', 'rar', '7z', 'tar', 'gz'],
    mimeTypes: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'],
    maxSize: 100 * 1024 * 1024 // 100MB
  }
}

// 文件存储（模拟）
const fileStorage = new Map<string, any>()

// 上传进度存储
const uploadProgress = new Map<string, any>()

export default [
  // ==================== 文件上传接口 ====================
  
  // 单文件上传
  {
    url: '/api/file/upload',
    method: 'post',
    response: async ({ body }: { body: Record<string, any> }) => {
      // 模拟文件上传延迟
      await delay(1000)
      
      const { filename, size, type, category } = body
      
      if (!filename) {
        return mockResponse.error( '文件名不能为空', 400)
      }
      
      // 检查文件类型
      const extension = filename.split('.').pop()?.toLowerCase() || ''
      let fileCategory = category
      
      if (!fileCategory) {
        // 自动识别文件类型
        for (const [cat, config] of Object.entries(FILE_TYPES)) {
          if (config.extensions.includes(extension)) {
            fileCategory = cat
            break
          }
        }
      }
      
      // 生成文件信息
      const fileInfo = {
        id: generateId(),
        filename,
        originalName: filename,
        size: size || faker.number.int({ min: 1024, max: 10485760 }),
        type: type || `application/${extension}`,
        category: fileCategory || 'other',
        extension,
        url: `/uploads/${fileCategory}/${generateId()}.${extension}`,
        thumbnailUrl: fileCategory === 'image' ? `/thumbnails/${generateId()}.jpg` : null,
        uploadTime: new Date().toISOString(),
        uploadBy: 'U00001',
        uploadByName: faker.person.fullName(),
        status: 'success',
        md5: faker.string.alphanumeric(32)
      }
      
      // 存储文件信息
      fileStorage.set(fileInfo.id, fileInfo)
      
      return mockResponse.success(fileInfo, '上传成功')
    }
  },
  
  // 多文件上传
  {
    url: '/api/file/upload-multiple',
    method: 'post',
    response: async ({ body }: { body: Record<string, any> }) => {
      const { files } = body
      
      if (!files || !Array.isArray(files)) {
        return mockResponse.error( '文件列表不能为空', 400)
      }
      
      const uploadedFiles = []
      
      for (const file of files) {
        await delay(500) // 模拟每个文件的上传延迟
        
        const fileInfo = {
          id: generateId(),
          filename: file.filename,
          originalName: file.filename,
          size: file.size || faker.number.int({ min: 1024, max: 10485760 }),
          type: file.type,
          extension: file.filename.split('.').pop()?.toLowerCase() || '',
          url: `/uploads/${generateId()}.${file.filename.split('.').pop()}`,
          uploadTime: new Date().toISOString(),
          status: 'success'
        }
        
        fileStorage.set(fileInfo.id, fileInfo)
        uploadedFiles.push(fileInfo)
      }
      
      return mockResponse.success({
        total: uploadedFiles.length,
        success: uploadedFiles.length,
        failed: 0,
        files: uploadedFiles
      }, '批量上传成功')
    }
  },
  
  // 分片上传初始化
  {
    url: '/api/file/upload/init',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      const { filename, size, chunkSize = 1024 * 1024 } = body // 默认1MB分片
      
      if (!filename || !size) {
        return mockResponse.error( '文件信息不完整', 400)
      }
      
      const uploadId = generateId()
      const totalChunks = Math.ceil(size / chunkSize)
      
      const uploadInfo = {
        uploadId,
        filename,
        size,
        chunkSize,
        totalChunks,
        uploadedChunks: [],
        status: 'initialized',
        createdAt: new Date().toISOString()
      }
      
      uploadProgress.set(uploadId, uploadInfo)
      
      return mockResponse.success({
        uploadId,
        chunkSize,
        totalChunks
      }, '初始化成功')
    }
  },
  
  // 分片上传
  {
    url: '/api/file/upload/chunk',
    method: 'post',
    response: async ({ body }: { body: Record<string, any> }) => {
      const { uploadId, chunkIndex, chunkData } = body
      
      const uploadInfo = uploadProgress.get(uploadId)
      if (!uploadInfo) {
        return mockResponse.error( '上传任务不存在', 404)
      }
      
      // 模拟上传延迟
      await delay(200)
      
      // 记录已上传分片
      if (!uploadInfo.uploadedChunks.includes(chunkIndex)) {
        uploadInfo.uploadedChunks.push(chunkIndex)
      }
      
      const progress = (uploadInfo.uploadedChunks.length / uploadInfo.totalChunks) * 100
      
      return mockResponse.success({
        uploadId,
        chunkIndex,
        uploadedChunks: uploadInfo.uploadedChunks.length,
        totalChunks: uploadInfo.totalChunks,
        progress: progress.toFixed(2)
      })
    }
  },
  
  // 分片上传完成
  {
    url: '/api/file/upload/complete',
    method: 'post',
    response: ({ body }: { body: Record<string, any> }) => {
      const { uploadId } = body
      
      const uploadInfo = uploadProgress.get(uploadId)
      if (!uploadInfo) {
        return mockResponse.error( '上传任务不存在', 404)
      }
      
      // 检查是否所有分片都已上传
      if (uploadInfo.uploadedChunks.length < uploadInfo.totalChunks) {
        return mockResponse.error( '还有分片未上传完成', 400)
      }
      
      // 生成最终文件信息
      const extension = uploadInfo.filename.split('.').pop()?.toLowerCase() || ''
      const fileInfo = {
        id: generateId(),
        filename: uploadInfo.filename,
        size: uploadInfo.size,
        extension,
        url: `/uploads/large/${generateId()}.${extension}`,
        uploadTime: new Date().toISOString(),
        status: 'success'
      }
      
      fileStorage.set(fileInfo.id, fileInfo)
      uploadProgress.delete(uploadId)
      
      return mockResponse.success(fileInfo, '文件合并成功')
    }
  },
  
  // 获取上传进度
  {
    url: '/api/file/upload/progress/:uploadId',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      const uploadInfo = uploadProgress.get(params.uploadId)
      
      if (!uploadInfo) {
        return mockResponse.error( '上传任务不存在', 404)
      }
      
      const progress = (uploadInfo.uploadedChunks.length / uploadInfo.totalChunks) * 100
      
      return mockResponse.success({
        uploadId: params.uploadId,
        filename: uploadInfo.filename,
        size: uploadInfo.size,
        uploadedChunks: uploadInfo.uploadedChunks.length,
        totalChunks: uploadInfo.totalChunks,
        progress: progress.toFixed(2),
        status: progress === 100 ? 'completed' : 'uploading'
      })
    }
  },
  
  // ==================== 文件下载接口 ====================
  
  // 文件下载
  {
    url: '/api/file/download/:id',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      const fileInfo = fileStorage.get(params.id)
      
      if (!fileInfo) {
        return mockResponse.error( '文件不存在', 404)
      }
      
      // 返回文件下载信息
      return mockResponse.success({
        ...fileInfo,
        downloadUrl: `/downloads/${fileInfo.id}/${fileInfo.filename}`,
        expires: new Date(Date.now() + 3600000).toISOString() // 1小时后过期
      })
    }
  },
  
  // 批量下载（生成压缩包）
  {
    url: '/api/file/download-batch',
    method: 'post',
    response: async ({ body }: { body: Record<string, any> }) => {
      const { fileIds } = body
      
      if (!fileIds || !Array.isArray(fileIds)) {
        return mockResponse.error( '文件ID列表不能为空', 400)
      }
      
      // 模拟压缩处理
      await delay(2000)
      
      const zipInfo = {
        id: generateId(),
        filename: `批量下载_${new Date().toISOString().split('T')[0]}.zip`,
        size: faker.number.int({ min: 1048576, max: 104857600 }), // 1MB-100MB
        fileCount: fileIds.length,
        downloadUrl: `/downloads/batch/${generateId()}.zip`,
        expires: new Date(Date.now() + 3600000).toISOString(),
        status: 'ready'
      }
      
      return mockResponse.success(zipInfo, '压缩包生成成功')
    }
  },
  
  // ==================== 文件预览接口 ====================
  
  // 文件预览
  {
    url: '/api/file/preview/:id',
    method: 'get',
    response: ({ params }: { params: Record<string, any> }) => {
      const fileInfo = fileStorage.get(params.id)
      
      if (!fileInfo) {
        return mockResponse.error( '文件不存在', 404)
      }
      
      // 根据文件类型返回不同的预览信息
      let previewData: any = {
        id: fileInfo.id,
        filename: fileInfo.filename,
        type: fileInfo.type,
        canPreview: false
      }
      
      // 图片文件
      if (fileInfo.category === 'image') {
        previewData = {
          ...previewData,
          canPreview: true,
          previewType: 'image',
          previewUrl: fileInfo.url,
          thumbnailUrl: fileInfo.thumbnailUrl
        }
      }
      // PDF文件
      else if (fileInfo.extension === 'pdf') {
        previewData = {
          ...previewData,
          canPreview: true,
          previewType: 'pdf',
          previewUrl: `/preview/pdf/${fileInfo.id}`,
          pageCount: faker.number.int({ min: 1, max: 50 })
        }
      }
      // Office文档
      else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileInfo.extension)) {
        previewData = {
          ...previewData,
          canPreview: true,
          previewType: 'office',
          previewUrl: `/preview/office/${fileInfo.id}`,
          convertedPdfUrl: `/preview/pdf/${fileInfo.id}.pdf`
        }
      }
      // 文本文件
      else if (['txt', 'md', 'json', 'xml', 'log'].includes(fileInfo.extension)) {
        previewData = {
          ...previewData,
          canPreview: true,
          previewType: 'text',
          content: faker.lorem.paragraphs(5)
        }
      }
      
      return mockResponse.success(previewData)
    }
  },
  
  // ==================== 文件管理接口 ====================
  
  // 获取文件列表
  {
    url: '/api/file/list',
    method: 'get',
    response: ({ query }: { query: Record<string, any> }) => {
      let files = Array.from(fileStorage.values())
      
      // 过滤
      if (query.category) {
        files = files.filter(f => f.category === query.category)
      }
      if (query.uploadBy) {
        files = files.filter(f => f.uploadBy === query.uploadBy)
      }
      if (query.keyword) {
        files = files.filter(f => f.filename.includes(query.keyword))
      }
      
      // 排序
      files.sort((a, b) => new Date(b.uploadTime).getTime() - new Date(a.uploadTime).getTime())
      
      // 分页
      const page = Number(query.page) || 1
      const pageSize = Number(query.pageSize) || 20
      const start = (page - 1) * pageSize
      const end = start + pageSize
      
      return mockResponse.success({
        total: files.length,
        page,
        pageSize,
        list: files.slice(start, end)
      })
    }
  },
  
  // 删除文件
  {
    url: '/api/file/delete/:id',
    method: 'delete',
    response: ({ params }: { params: Record<string, any> }) => {
      if (fileStorage.has(params.id)) {
        fileStorage.delete(params.id)
        return mockResponse.success(null, '删除成功')
      }
      
      return mockResponse.error( '文件不存在', 404)
    }
  },
  
  // 批量删除文件
  {
    url: '/api/file/delete-batch',
    method: 'delete',
    response: ({ body }: { body: Record<string, any> }) => {
      const { fileIds } = body
      
      if (!fileIds || !Array.isArray(fileIds)) {
        return mockResponse.error( '文件ID列表不能为空', 400)
      }
      
      let deletedCount = 0
      fileIds.forEach(id => {
        if (fileStorage.has(id)) {
          fileStorage.delete(id)
          deletedCount++
        }
      })
      
      return mockResponse.success({ 
        deletedCount,
        failedCount: fileIds.length - deletedCount
      }, '批量删除完成')
    }
  },
  
  // 获取存储统计
  {
    url: '/api/file/storage-stats',
    method: 'get',
    response: () => {
      const files = Array.from(fileStorage.values())
      const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0)
      
      const statsByCategory: Record<string, { count: number; size: number }> = {}
      Object.keys(FILE_TYPES).forEach(category => {
        const categoryFiles = files.filter(f => f.category === category)
        statsByCategory[category] = {
          count: categoryFiles.length,
          size: categoryFiles.reduce((sum, file) => sum + (file.size || 0), 0)
        }
      })
      
      return mockResponse.success({
        totalFiles: files.length,
        totalSize,
        usedSpace: totalSize,
        totalSpace: 10 * 1024 * 1024 * 1024, // 10GB
        freeSpace: 10 * 1024 * 1024 * 1024 - totalSize,
        usagePercent: ((totalSize / (10 * 1024 * 1024 * 1024)) * 100).toFixed(2),
        byCategory: statsByCategory,
        recentUploads: files.slice(0, 10)
      })
    }
  }
  
] as MockMethod[]