/**
 * @name 岗位管理Mock模块
 * @description 提供岗位管理相关的Mock接口，包括岗位CRUD、岗位序列、岗位族群等功能
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import type { MockRequestOptions } from '../types'
import { success, error, paginate, delay, generateId } from '../utils'
import { createSimpleMockService } from '../service/SimpleMockService'

// 创建持久化服务
const positionService = createSimpleMockService<any>('positions')
const sequenceService = createSimpleMockService<any>('positionSequences')
const familyService = createSimpleMockService<any>('positionFamilies')

// 初始化标志
let isInitialized = false

// 确保数据初始化
const ensureInitialized = () => {
  if (!isInitialized) {
    try {
      initPositionData()
      isInitialized = true
      console.log('岗位Mock数据初始化成功')
    } catch (error) {
      console.error('岗位Mock数据初始化失败:', error)
    }
  }
}

// 初始化岗位数据
const initPositionData = () => {
  const existingData = positionService.readAll()
  if (existingData.length > 0) {
    console.log('岗位数据已存在，跳过初始化')
    return
  }

  const positions = [
    // 教学岗位
    {
      id: 'POS001',
      code: 'PROF001',
      name: '教授',
      level: 'P1',
      category: 'TEACHING',
      type: 'PROFESSIONAL',
      departmentId: '4',
      departmentName: '教务处',
      sequenceId: 'SEQ001',
      sequenceName: '教学序列',
      familyId: 'FAM001',
      familyName: '高等教育',
      description: '从事高等教育教学和科研工作的高级专业技术人员',
      requirements: '博士学位，副教授以上职称，具有丰富的教学和科研经验',
      responsibilities: '承担本科生、研究生教学任务；开展科学研究；指导青年教师',
      qualifications: ['博士学位', '副教授以上职称', '5年以上教学经验'],
      skills: ['教学能力', '科研能力', '学术领导力'],
      status: 'ACTIVE',
      establishmentCount: 50,
      actualCount: 45,
      vacancyCount: 5,
      salaryRange: { min: 15000, max: 25000 },
      createTime: '2024-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    {
      id: 'POS002',
      code: 'ASSOC001',
      name: '副教授',
      level: 'P2',
      category: 'TEACHING',
      type: 'PROFESSIONAL',
      departmentId: '4',
      departmentName: '教务处',
      sequenceId: 'SEQ001',
      sequenceName: '教学序列',
      familyId: 'FAM001',
      familyName: '高等教育',
      description: '从事高等教育教学和科研工作的中级专业技术人员',
      requirements: '硕士以上学位，讲师以上职称，具有一定的教学和科研经验',
      responsibilities: '承担本科生教学任务；参与科学研究；协助指导研究生',
      qualifications: ['硕士以上学位', '讲师以上职称', '3年以上教学经验'],
      skills: ['教学能力', '科研能力', '团队协作'],
      status: 'ACTIVE',
      establishmentCount: 80,
      actualCount: 75,
      vacancyCount: 5,
      salaryRange: { min: 10000, max: 18000 },
      createTime: '2024-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    {
      id: 'POS003',
      code: 'LECT001',
      name: '讲师',
      level: 'P3',
      category: 'TEACHING',
      type: 'PROFESSIONAL',
      departmentId: '4',
      departmentName: '教务处',
      sequenceId: 'SEQ001',
      sequenceName: '教学序列',
      familyId: 'FAM001',
      familyName: '高等教育',
      description: '从事高等教育教学工作的初级专业技术人员',
      requirements: '本科以上学位，具有教学能力和发展潜力',
      responsibilities: '承担本科生教学任务；参与教学改革；提升专业能力',
      qualifications: ['本科以上学位', '教师资格证', '1年以上相关经验'],
      skills: ['教学能力', '沟通能力', '学习能力'],
      status: 'ACTIVE',
      establishmentCount: 120,
      actualCount: 110,
      vacancyCount: 10,
      salaryRange: { min: 6000, max: 12000 },
      createTime: '2024-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 管理岗位
    {
      id: 'POS004',
      code: 'MGR001',
      name: '处长',
      level: 'M1',
      category: 'MANAGEMENT',
      type: 'LEADERSHIP',
      departmentId: '3',
      departmentName: '人事处',
      sequenceId: 'SEQ002',
      sequenceName: '管理序列',
      familyId: 'FAM002',
      familyName: '行政管理',
      description: '负责部门整体管理工作的高级管理人员',
      requirements: '本科以上学位，具有丰富的管理经验和领导能力',
      responsibilities: '制定部门发展规划；统筹部门工作；协调各方关系',
      qualifications: ['本科以上学位', '5年以上管理经验', '领导能力'],
      skills: ['管理能力', '决策能力', '沟通协调'],
      status: 'ACTIVE',
      establishmentCount: 15,
      actualCount: 14,
      vacancyCount: 1,
      salaryRange: { min: 12000, max: 20000 },
      createTime: '2024-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    {
      id: 'POS005',
      code: 'MGR002',
      name: '副处长',
      level: 'M2',
      category: 'MANAGEMENT',
      type: 'LEADERSHIP',
      departmentId: '3',
      departmentName: '人事处',
      sequenceId: 'SEQ002',
      sequenceName: '管理序列',
      familyId: 'FAM002',
      familyName: '行政管理',
      description: '协助处长工作的中级管理人员',
      requirements: '本科以上学位，具有一定的管理经验',
      responsibilities: '协助处长工作；分管具体业务；指导下属工作',
      qualifications: ['本科以上学位', '3年以上管理经验', '专业能力'],
      skills: ['管理能力', '执行能力', '专业技能'],
      status: 'ACTIVE',
      establishmentCount: 25,
      actualCount: 23,
      vacancyCount: 2,
      salaryRange: { min: 8000, max: 15000 },
      createTime: '2024-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    },
    // 技术岗位
    {
      id: 'POS006',
      code: 'TECH001',
      name: '高级工程师',
      level: 'T1',
      category: 'TECHNICAL',
      type: 'PROFESSIONAL',
      departmentId: '5',
      departmentName: '信息中心',
      sequenceId: 'SEQ003',
      sequenceName: '技术序列',
      familyId: 'FAM003',
      familyName: '信息技术',
      description: '从事信息技术工作的高级专业技术人员',
      requirements: '本科以上学位，具有丰富的技术经验',
      responsibilities: '负责技术架构设计；解决技术难题；指导技术团队',
      qualifications: ['本科以上学位', '高级工程师职称', '5年以上技术经验'],
      skills: ['技术能力', '架构设计', '团队管理'],
      status: 'ACTIVE',
      establishmentCount: 20,
      actualCount: 18,
      vacancyCount: 2,
      salaryRange: { min: 10000, max: 18000 },
      createTime: '2024-01-01T00:00:00.000Z',
      updateTime: new Date().toISOString()
    }
  ]

  // 保存岗位数据
  positions.forEach(position => {
    positionService.create(position)
  })

  // 初始化岗位序列数据
  const sequences = [
    {
      id: 'SEQ001',
      code: 'TEACH',
      name: '教学序列',
      description: '从事教学和科研工作的专业技术序列',
      category: 'TEACHING',
      levels: ['P1', 'P2', 'P3', 'P4'],
      status: 'ACTIVE'
    },
    {
      id: 'SEQ002',
      code: 'MGMT',
      name: '管理序列',
      description: '从事管理工作的行政管理序列',
      category: 'MANAGEMENT',
      levels: ['M1', 'M2', 'M3', 'M4'],
      status: 'ACTIVE'
    },
    {
      id: 'SEQ003',
      code: 'TECH',
      name: '技术序列',
      description: '从事技术工作的专业技术序列',
      category: 'TECHNICAL',
      levels: ['T1', 'T2', 'T3', 'T4'],
      status: 'ACTIVE'
    }
  ]

  sequences.forEach(sequence => {
    sequenceService.create(sequence)
  })

  // 初始化岗位族群数据
  const families = [
    {
      id: 'FAM001',
      code: 'EDU',
      name: '高等教育',
      description: '高等教育教学和科研相关岗位族群',
      sequenceId: 'SEQ001',
      status: 'ACTIVE'
    },
    {
      id: 'FAM002',
      code: 'ADMIN',
      name: '行政管理',
      description: '行政管理相关岗位族群',
      sequenceId: 'SEQ002',
      status: 'ACTIVE'
    },
    {
      id: 'FAM003',
      code: 'IT',
      name: '信息技术',
      description: '信息技术相关岗位族群',
      sequenceId: 'SEQ003',
      status: 'ACTIVE'
    }
  ]

  families.forEach(family => {
    familyService.create(family)
  })
}

export default [
  // 获取岗位列表
  {
    url: '/api/position/list',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const {
          page = 1,
          size = 20,
          name,
          code,
          category,
          type,
          status = 'ACTIVE',
          departmentId,
          sequenceId,
          familyId
        } = query || {}

        let positions = positionService.readAll()

        // 按状态过滤
        if (status) {
          positions = positions.filter(pos => pos.status === status)
        }

        // 按名称搜索
        if (name) {
          positions = positions.filter(pos => pos.name.includes(name))
        }

        // 按编码搜索
        if (code) {
          positions = positions.filter(pos => pos.code.includes(code))
        }

        // 按类别过滤
        if (category) {
          positions = positions.filter(pos => pos.category === category)
        }

        // 按类型过滤
        if (type) {
          positions = positions.filter(pos => pos.type === type)
        }

        // 按部门过滤
        if (departmentId) {
          positions = positions.filter(pos => pos.departmentId === departmentId)
        }

        // 按序列过滤
        if (sequenceId) {
          positions = positions.filter(pos => pos.sequenceId === sequenceId)
        }

        // 按族群过滤
        if (familyId) {
          positions = positions.filter(pos => pos.familyId === familyId)
        }

        // 分页处理
        const total = positions.length
        const startIndex = (page - 1) * size
        const endIndex = startIndex + size
        const list = positions.slice(startIndex, endIndex)

        const response = success(
          {
            list,
            total,
            page: Number(page),
            size: Number(size),
            pages: Math.ceil(total / size)
          },
          '获取岗位列表成功'
        )

        console.log('Response for /api/position/list:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/position/list:', err)
        return error('获取岗位列表失败', 500)
      }
    }
  },

  // 获取岗位详情
  {
    url: '/api/position/:id',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { id } = query || {}

        if (!id) {
          return error('岗位ID不能为空', 400)
        }

        const position = positionService.read(id)

        if (!position) {
          return error('岗位不存在', 404)
        }

        const response = success(position, '获取岗位详情成功')
        console.log('Response for /api/position/:id:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/position/:id:', err)
        return error('获取岗位详情失败', 500)
      }
    }
  },

  // 获取岗位选项（用于下拉选择）
  {
    url: '/api/position/options',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { category, type, status = 'ACTIVE' } = query || {}

        let positions = positionService.readAll()

        // 过滤条件
        if (status) {
          positions = positions.filter(pos => pos.status === status)
        }
        if (category) {
          positions = positions.filter(pos => pos.category === category)
        }
        if (type) {
          positions = positions.filter(pos => pos.type === type)
        }

        // 转换为选项格式
        const options = positions.map(pos => ({
          label: pos.name,
          value: pos.id,
          code: pos.code,
          category: pos.category,
          type: pos.type,
          departmentName: pos.departmentName
        }))

        const response = success(options, '获取岗位选项成功')
        console.log('Response for /api/position/options:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/position/options:', err)
        return error('获取岗位选项失败', 500)
      }
    }
  },

  // 获取岗位序列列表
  {
    url: '/api/position/sequences',
    method: 'get',
    response: async () => {
      await delay()
      ensureInitialized()

      try {
        const sequences = sequenceService.readAll()
        const response = success(sequences, '获取岗位序列成功')
        console.log('Response for /api/position/sequences:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/position/sequences:', err)
        return error('获取岗位序列失败', 500)
      }
    }
  },

  // 获取岗位族群列表
  {
    url: '/api/position/families',
    method: 'get',
    response: async ({ query }: MockRequestOptions) => {
      await delay()
      ensureInitialized()

      try {
        const { sequenceId } = query || {}

        let families = familyService.readAll()

        if (sequenceId) {
          families = families.filter(family => family.sequenceId === sequenceId)
        }

        const response = success(families, '获取岗位族群成功')
        console.log('Response for /api/position/families:', response)
        return response
      } catch (err: any) {
        console.error('Error in /api/position/families:', err)
        return error('获取岗位族群失败', 500)
      }
    }
  }
] as MockMethod[]
