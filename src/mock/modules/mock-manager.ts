/**
 * @name Mock数据管理API模块
 * @description 提供Mock数据管理界面所需的API接口，桥接Mock服务和管理界面
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'
import type { MockRequestOptions } from '../types'
import { success, error, delay, logger } from '../utils'
import { MockServiceManager } from '../service'
// import { EmployeeGenerator } from '../generators/EmployeeGenerator'

// 集合配置映射
const COLLECTION_SERVICE_MAP = {
  users: 'users',
  tokens: 'tokens',
  dicts: 'dicts',
  configs: 'configs',
  roles: 'roles',
  notices: 'notices',
  employees: 'employees',
  departments: 'organizations',
  positions: 'positions'
}

// 数据生成器映射
const DATA_GENERATORS: Record<string, () => any[]> = {
  employees: () => {
    const names = ['张三', '李四', '王五', '赵六', '陈七', '刘八', '杨九', '黄十']
    const departments = ['信息工程学院', '机电工程学院', '商贸旅游学院', '人事处', '教务处']
    const positions = ['教授', '副教授', '讲师', '助教', '处长', '科长', '科员']

    return Array.from({ length: 50 }, (_, i) => ({
      id: `emp_${i + 1}`,
      employeeId: `E${String(i + 1).padStart(4, '0')}`,
      employeeNumber: `${2020 + Math.floor(i / 10)}${String(i + 1).padStart(4, '0')}`,
      fullName: `${names[i % names.length]}${i + 1}`,
      gender: i % 2 === 0 ? 'male' : 'female',
      dateOfBirth: `${1980 + (i % 25)}-${String((i % 12) + 1).padStart(2, '0')}-15`,
      departmentId: `dept_${(i % departments.length) + 1}`,
      departmentName: departments[i % departments.length],
      positionName: positions[i % positions.length],
      hireDate: `${2015 + (i % 8)}-09-01`,
      status: 'ACTIVE',
      createTime: new Date().toISOString()
    }))
  },
  departments: () => {
    const deptNames = [
      '信息工程学院',
      '机电工程学院',
      '商贸旅游学院',
      '人文社科学院',
      '教务处',
      '人事处',
      '财务处',
      '学生处',
      '科研处',
      '国际交流处'
    ]
    return Array.from({ length: deptNames.length }, (_, i) => ({
      id: `dept_${i + 1}`,
      institutionId: `${1000 + i}`,
      institutionName: deptNames[i],
      institutionCode: `D${String(i + 1).padStart(3, '0')}`,
      institutionType: i < 4 ? 'COLLEGE' : 'ADMIN_DEPT',
      status: 'ACTIVE',
      level: 1,
      employeeCount: 30 + Math.floor(Math.random() * 50),
      createTime: new Date().toISOString()
    }))
  },
  users: () => {
    return Array.from({ length: 30 }, (_, i) => ({
      id: `user_${i + 1}`,
      username: `user${i + 1}`,
      fullName: `用户${i + 1}`,
      email: `user${i + 1}@example.com`,
      status: 'active',
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    }))
  },
  tokens: () => {
    return Array.from({ length: 10 }, (_, i) => ({
      id: `token_${i + 1}`,
      userId: `user_${(i % 30) + 1}`,
      token: `tok_${Date.now()}_${i}`,
      type: 'access',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      createTime: new Date().toISOString()
    }))
  },
  dicts: () => {
    return [
      { id: 'dict_1', type: 'gender', label: '男', value: 'male', sort: 1 },
      { id: 'dict_2', type: 'gender', label: '女', value: 'female', sort: 2 },
      { id: 'dict_3', type: 'education', label: '博士', value: 'phd', sort: 1 },
      { id: 'dict_4', type: 'education', label: '硕士', value: 'master', sort: 2 },
      { id: 'dict_5', type: 'education', label: '学士', value: 'bachelor', sort: 3 }
    ]
  },
  configs: () => {
    return [
      { id: 'config_1', key: 'system.title', value: '杭科院人事系统', category: 'system' },
      { id: 'config_2', key: 'system.version', value: '1.0.0', category: 'system' },
      { id: 'config_3', key: 'system.debug', value: 'false', category: 'system' }
    ]
  },
  roles: () => {
    return [
      { id: 'admin', name: '系统管理员', description: '拥有所有权限' },
      { id: 'hr', name: '人事专员', description: '负责人事管理' },
      { id: 'teacher', name: '教师', description: '教学人员' },
      { id: 'student', name: '学生', description: '在校学生' }
    ]
  },
  notices: () => {
    return Array.from({ length: 15 }, (_, i) => ({
      id: `notice_${i + 1}`,
      title: `通知公告${i + 1}`,
      content: `这是第${i + 1}条通知的内容`,
      type: ['info', 'warning', 'success'][i % 3],
      publishTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
    }))
  },
  positions: () => {
    return Array.from({ length: 25 }, (_, i) => ({
      id: `pos_${i + 1}`,
      name: `岗位${i + 1}`,
      code: `P${String(i + 1).padStart(3, '0')}`,
      type: ['教学岗', '管理岗', '教辅岗'][i % 3],
      level: ['高级', '中级', '初级'][i % 3],
      departmentId: `dept_${(i % 10) + 1}`,
      description: `这是岗位${i + 1}的描述`,
      createTime: new Date().toISOString()
    }))
  }
}

/**
 * 获取集合统计信息
 */
const getCollectionStats = () => {
  const stats = Object.keys(COLLECTION_SERVICE_MAP).map(collectionName => {
    const serviceName =
      COLLECTION_SERVICE_MAP[collectionName as keyof typeof COLLECTION_SERVICE_MAP]

    try {
      const service = MockServiceManager.getService(serviceName)
      let count = service.count()

      // 如果数据为空，使用生成器初始化数据
      if (count === 0 && DATA_GENERATORS[collectionName]) {
        try {
          const generatedData = DATA_GENERATORS[collectionName]()
          generatedData.forEach(item => {
            try {
              service.create(item)
            } catch (createErr) {
              // 静默处理单个数据创建错误
            }
          })
          count = service.count()
        } catch (genErr) {
          // 静默处理数据生成错误
        }
      }

      return {
        name: collectionName,
        count
      }
    } catch (err) {
      // 如果服务不存在，返回0
      return {
        name: collectionName,
        count: 0
      }
    }
  })

  return stats
}

/**
 * 获取指定集合的数据
 */
const getCollectionData = (collectionName: string, page = 1, size = 20, keyword = '') => {
  const serviceName = COLLECTION_SERVICE_MAP[collectionName as keyof typeof COLLECTION_SERVICE_MAP]

  if (!serviceName) {
    throw new Error(`未知的集合: ${collectionName}`)
  }

  try {
    const service = MockServiceManager.getService(serviceName)
    let allData = service.readAll()

    // 如果没有数据，尝试生成初始数据
    if (allData.length === 0 && DATA_GENERATORS[collectionName]) {
      const generatedData = DATA_GENERATORS[collectionName]()
      generatedData.forEach(item => service.create(item))
      allData = service.readAll()
    }

    // 关键字搜索
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase()
      allData = allData.filter((item: any) => {
        return Object.values(item).some(value => String(value).toLowerCase().includes(lowerKeyword))
      })
    }

    // 分页
    const total = allData.length
    const start = (page - 1) * size
    const end = start + size
    const list = allData.slice(start, end)

    return {
      list,
      total,
      page,
      size,
      pages: Math.ceil(total / size)
    }
  } catch (err) {
    throw new Error(`获取集合数据失败: ${err}`)
  }
}

/**
 * 初始化所有Mock数据
 */
const initializeAllData = () => {
  const results: Record<string, number> = {}

  Object.keys(DATA_GENERATORS).forEach(collectionName => {
    const serviceName =
      COLLECTION_SERVICE_MAP[collectionName as keyof typeof COLLECTION_SERVICE_MAP]

    if (serviceName) {
      try {
        const service = MockServiceManager.getService(serviceName)
        const existingCount = service.readAll().length

        if (existingCount === 0) {
          const generatedData = DATA_GENERATORS[collectionName]()
          generatedData.forEach(item => service.create(item))
          results[collectionName] = generatedData.length
        } else {
          results[collectionName] = existingCount
        }
      } catch (err) {
        console.warn(`初始化 ${collectionName} 数据失败:`, err)
        results[collectionName] = 0
      }
    }
  })

  return results
}

// Mock API定义
const mockMethods: MockMethod[] = [
  // 获取集合统计
  {
    url: '/api/mock-manager/collections/stats',
    method: 'get',
    response: ({ query }: MockRequestOptions) => {
      logger.request('/api/mock-manager/collections/stats', 'GET', query)

      try {
        const stats = getCollectionStats()
        return success(stats, '获取统计信息成功')
      } catch (err) {
        return error('获取统计信息失败', 500)
      }
    }
  },

  // 获取集合数据
  {
    url: '/api/mock-manager/collections/:name/data',
    method: 'get',
    response: ({ query, url }: MockRequestOptions) => {
      const collectionName = url.split('/').slice(-2)[0]
      logger.request(`/api/mock-manager/collections/${collectionName}/data`, 'GET', query)

      try {
        const { page = 1, size = 20, keyword = '' } = query || {}
        const result = getCollectionData(
          collectionName,
          Number(page),
          Number(size),
          String(keyword)
        )
        return success(result, '获取数据成功')
      } catch (err) {
        return error(String(err), 500)
      }
    }
  },

  // 初始化所有数据
  {
    url: '/api/mock-manager/initialize',
    method: 'post',
    response: ({ body }: MockRequestOptions) => {
      logger.request('/api/mock-manager/initialize', 'POST', body)

      try {
        const results = initializeAllData()
        return {
          success: true,
          code: 200,
          message: '初始化数据成功',
          data: results
        }
      } catch (err) {
        return {
          success: false,
          code: 500,
          message: '初始化数据失败',
          data: null
        }
      }
    }
  },

  // 清空指定集合数据
  {
    url: '/api/mock-manager/collections/:name/clear',
    method: 'delete',
    response: async ({ url }: MockRequestOptions) => {
      const collectionName = url.split('/').slice(-2)[0]
      logger.request(`/api/mock-manager/collections/${collectionName}/clear`, 'DELETE')
      await delay()

      try {
        const serviceName =
          COLLECTION_SERVICE_MAP[collectionName as keyof typeof COLLECTION_SERVICE_MAP]
        if (!serviceName) {
          return error(`未知的集合: ${collectionName}`, 400)
        }

        const service = MockServiceManager.getService(serviceName)
        const allData = service.readAll()
        const deletedCount = allData.length

        // 清空所有数据
        allData.forEach((item: any) => service.delete(item.id))

        return success({ deletedCount }, '清空数据成功')
      } catch (err) {
        return error('清空数据失败', 500)
      }
    }
  },

  // 重新生成指定集合数据
  {
    url: '/api/mock-manager/collections/:name/regenerate',
    method: 'post',
    response: async ({ url, body }: MockRequestOptions) => {
      const collectionName = url.split('/').slice(-2)[0]
      logger.request(`/api/mock-manager/collections/${collectionName}/regenerate`, 'POST', body)
      await delay()

      try {
        const { count } = body || {}
        const serviceName =
          COLLECTION_SERVICE_MAP[collectionName as keyof typeof COLLECTION_SERVICE_MAP]

        if (!serviceName) {
          return error(`未知的集合: ${collectionName}`, 400)
        }

        if (!DATA_GENERATORS[collectionName]) {
          return error(`该集合不支持自动生成: ${collectionName}`, 400)
        }

        const service = MockServiceManager.getService(serviceName)

        // 清空现有数据
        const existingData = service.readAll()
        existingData.forEach((item: any) => service.delete(item.id))

        // 生成新数据
        const generatedData = DATA_GENERATORS[collectionName]()
        const actualCount = count ? Math.min(count, generatedData.length) : generatedData.length
        const dataToCreate = generatedData.slice(0, actualCount)

        dataToCreate.forEach(item => service.create(item))

        return success(
          {
            generatedCount: actualCount,
            collectionName
          },
          '重新生成数据成功'
        )
      } catch (err) {
        return error('重新生成数据失败', 500)
      }
    }
  }
]

export default mockMethods
