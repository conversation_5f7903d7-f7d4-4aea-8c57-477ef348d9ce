/**
 * @name Mock服务入口
 * @description 统一管理Mock服务的初始化和配置
 * <AUTHOR>
 * @since 2025-01-24
 */

import type { MockMethod } from 'vite-plugin-mock'

// 导入各模块的 mock 数据
import userMock from './modules/user'
import organizationMock from './modules/organization'
import positionMock from './modules/position'
import employeeMock from './modules/employee'
import workflowMock from './modules/workflow'
import dashboardMock from './modules/dashboard'
import attendanceMock from './modules/attendance'
import salaryMock from './modules/salary'
import recruitmentMock from './modules/recruitment'
import systemMock from './modules/system'
import personnelTransferMock from './modules/personnel-transfer'
import performanceMock from './modules/performance'
import contractMock from './modules/contract'
import scenariosMock from './modules/scenarios'
import websocketMock from './modules/websocket'
import fileMock from './modules/file'
import analyticsMock from './modules/analytics'
import importExportMock from './modules/import-export'
import errorSimulationMock from './modules/error-simulation'
import mockManagerMock from './modules/mock-manager'

// 合并所有 mock 接口
const mockModules: MockMethod[] = [
  ...userMock,
  ...organizationMock,
  ...positionMock,
  ...employeeMock,
  ...workflowMock,
  ...dashboardMock,
  ...attendanceMock,
  ...salaryMock,
  ...recruitmentMock,
  ...systemMock,
  ...personnelTransferMock,
  ...performanceMock,
  ...contractMock,
  ...scenariosMock,
  ...websocketMock,
  ...fileMock,
  ...analyticsMock,
  ...importExportMock,
  ...errorSimulationMock,
  ...mockManagerMock
]

// 生产环境Mock设置（可选）
export function setupProdMockServer() {
  if (import.meta.env.PROD && String(import.meta.env.VITE_ENABLE_MOCK) === 'true') {
    console.warn('⚠️ 生产环境使用Mock数据，请谨慎！')
    // 这里可以添加生产环境的Mock逻辑
  }
}

// Mock服务配置
export const mockConfig = {
  // 延迟响应时间（毫秒）
  delay: 200,
  // 是否打印请求日志
  logger: true,
  // 数据持久化配置
  persistence: {
    enabled: true,
    storage: 'localStorage',
    prefix: 'hr_mock_'
  }
}

export default mockModules
