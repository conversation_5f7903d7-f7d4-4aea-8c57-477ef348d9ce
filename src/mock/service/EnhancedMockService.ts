/**
 * @name 增强的Mock服务基类
 * @description 提供分页、过滤、排序等通用功能的Mock服务基类
 * <AUTHOR>
 * @since 2025-01-24
 */

import { createSimpleMockService } from './SimpleMockService'
import { generateId } from '../utils'
import mockConfig from '../config'

/**
 * 分页参数接口
 */
export interface PaginationParams {
  page?: number
  size?: number
  pageSize?: number // 兼容不同的参数名
  current?: number // 兼容不同的参数名
}

/**
 * 排序参数接口
 */
export interface SortParams {
  sortField?: string
  sortOrder?: 'asc' | 'desc' | 'ascend' | 'descend'
  orderBy?: string // 兼容不同的参数名
  order?: string // 兼容不同的参数名
}

/**
 * 过滤参数接口
 */
export interface FilterParams {
  [key: string]: any
}

/**
 * 查询参数接口
 */
export interface QueryParams extends PaginationParams, SortParams {
  keyword?: string
  search?: string // 兼容不同的参数名
  filters?: FilterParams
  dateRanges?: Record<string, { start: string; end: string }>
}

/**
 * 分页结果接口
 */
export interface PaginatedResult<T> {
  list: T[]
  total: number
  page: number
  size: number
  pages: number
}

/**
 * 增强的Mock服务类
 */
export class EnhancedMockService<T extends { id?: string }> {
  protected service: ReturnType<typeof createSimpleMockService<T>>

  constructor(name: string) {
    this.service = createSimpleMockService<T>(name)
  }
  /**
   * 获取标准化的分页参数
   */
  protected normalizePaginationParams(params: PaginationParams): { page: number; size: number } {
    const { defaultPage, defaultSize, maxSize } = mockConfig.pagination

    const page = Math.max(1, params.page || params.current || defaultPage)
    const size = Math.min(Math.max(1, params.size || params.pageSize || defaultSize), maxSize)

    return { page, size }
  }

  /**
   * 获取标准化的排序参数
   */
  protected normalizeSortParams(params: SortParams): { field?: string; order: 'asc' | 'desc' } {
    const field = params.sortField || params.orderBy
    const orderValue = params.sortOrder || params.order || 'asc'

    // 统一排序顺序值
    const order = orderValue === 'descend' || orderValue === 'desc' ? 'desc' : 'asc'

    return { field, order }
  }

  /**
   * 文本搜索过滤
   */
  protected filterByKeyword<K extends keyof T>(
    items: T[],
    keyword: string,
    searchFields: K[]
  ): T[] {
    if (!keyword || !keyword.trim()) {
      return items
    }

    const lowerKeyword = keyword.toLowerCase().trim()

    return items.filter(item => {
      return searchFields.some(field => {
        const value = item[field]
        if (value == null) return false

        const stringValue = String(value).toLowerCase()
        return stringValue.includes(lowerKeyword)
      })
    })
  }

  /**
   * 精确字段过滤
   */
  protected filterByFields(items: T[], filters: FilterParams): T[] {
    if (!filters || Object.keys(filters).length === 0) {
      return items
    }

    return items.filter(item => {
      return Object.entries(filters).every(([key, value]) => {
        if (value == null || value === '') return true

        const itemValue = (item as any)[key]

        // 数组类型的过滤（如多选）
        if (Array.isArray(value)) {
          return value.includes(itemValue)
        }

        // 普通值比较
        return itemValue === value
      })
    })
  }

  /**
   * 日期范围过滤
   */
  protected filterByDateRanges(
    items: T[],
    dateRanges: Record<string, { start: string; end: string }>
  ): T[] {
    if (!dateRanges || Object.keys(dateRanges).length === 0) {
      return items
    }

    return items.filter(item => {
      return Object.entries(dateRanges).every(([field, range]) => {
        if (!range || (!range.start && !range.end)) return true

        const fieldValue = (item as any)[field]
        if (!fieldValue) return false

        if (range.start && fieldValue < range.start) return false
        if (range.end && fieldValue > range.end) return false

        return true
      })
    })
  }

  /**
   * 排序
   */
  protected sortItems(items: T[], sortParams: { field?: string; order: 'asc' | 'desc' }): T[] {
    if (!sortParams.field) {
      return items
    }

    return [...items].sort((a, b) => {
      const aVal = (a as any)[sortParams.field!]
      const bVal = (b as any)[sortParams.field!]

      // 处理null/undefined
      if (aVal == null && bVal == null) return 0
      if (aVal == null) return 1
      if (bVal == null) return -1

      // 数字比较
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return sortParams.order === 'asc' ? aVal - bVal : bVal - aVal
      }

      // 字符串比较
      const aStr = String(aVal)
      const bStr = String(bVal)

      if (sortParams.order === 'asc') {
        return aStr.localeCompare(bStr, 'zh-CN')
      } else {
        return bStr.localeCompare(aStr, 'zh-CN')
      }
    })
  }

  /**
   * 分页处理
   */
  protected paginate(items: T[], page: number, size: number): PaginatedResult<T> {
    const total = items.length
    const pages = Math.ceil(total / size)
    const start = (page - 1) * size
    const end = start + size

    return {
      list: items.slice(start, end),
      total,
      page,
      size,
      pages
    }
  }

  /**
   * 综合查询方法
   */
  query(params: QueryParams & { searchFields?: (keyof T)[] }): PaginatedResult<T> {
    let results = this.service.readAll()

    // 1. 关键词搜索
    const keyword = params.keyword || params.search
    if (keyword && params.searchFields) {
      results = this.filterByKeyword(results, keyword, params.searchFields)
    }

    // 2. 字段过滤
    if (params.filters) {
      results = this.filterByFields(results, params.filters)
    }

    // 3. 日期范围过滤
    if (params.dateRanges) {
      results = this.filterByDateRanges(results, params.dateRanges)
    }

    // 4. 排序
    const sortParams = this.normalizeSortParams(params)
    if (sortParams.field) {
      results = this.sortItems(results, sortParams)
    }

    // 5. 分页
    const { page, size } = this.normalizePaginationParams(params)
    return this.paginate(results, page, size)
  }

  /**
   * 批量创建
   */
  createBatch(items: Omit<T, 'id'>[]): T[] {
    const created: T[] = []

    for (const item of items) {
      const newItem = {
        ...item,
        id: generateId()
      } as T

      const result = this.service.create(newItem)
      if (result) {
        created.push(result)
      }
    }

    return created
  }

  /**
   * 批量更新
   */
  updateBatch(updates: Array<{ id: string } & Partial<T>>): {
    success: string[]
    failed: string[]
  } {
    const result = {
      success: [] as string[],
      failed: [] as string[]
    }

    for (const update of updates) {
      const { id, ...data } = update

      if (this.service.update(id, data as Partial<T>)) {
        result.success.push(id)
      } else {
        result.failed.push(id)
      }
    }

    return result
  }

  /**
   * 批量删除
   */
  deleteBatch(ids: string[]): { success: string[]; failed: string[] } {
    const result = {
      success: [] as string[],
      failed: [] as string[]
    }

    for (const id of ids) {
      if (this.service.delete(id)) {
        result.success.push(id)
      } else {
        result.failed.push(id)
      }
    }

    return result
  }

  /**
   * 按条件删除
   */
  deleteBy(predicate: (item: T) => boolean): number {
    const items = this.service.readAll()
    let deletedCount = 0

    for (const item of items) {
      if (predicate(item) && item.id) {
        if (this.service.delete(item.id)) {
          deletedCount++
        }
      }
    }

    return deletedCount
  }

  /**
   * 导出数据
   */
  export(ids?: string[]): T[] {
    if (!ids || ids.length === 0) {
      return this.service.readAll()
    }

    return ids.map(id => this.service.read(id)).filter((item): item is T => item !== undefined)
  }

  /**
   * 导入数据
   */
  import(
    items: T[],
    mode: 'merge' | 'replace' = 'merge'
  ): { success: number; failed: number; errors: any[] } {
    const result = {
      success: 0,
      failed: 0,
      errors: [] as any[]
    }

    if (mode === 'replace') {
      this.service.clear()
    }

    for (let i = 0; i < items.length; i++) {
      try {
        const item = { ...items[i] }

        if (!item.id) {
          item.id = generateId()
        }

        const created = this.service.create(item)
        if (created) {
          result.success++
        } else {
          result.failed++
          result.errors.push({
            row: i + 1,
            message: '创建失败'
          })
        }
      } catch (error) {
        result.failed++
        result.errors.push({
          row: i + 1,
          message: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    return result
  }

  /**
   * 获取统计信息
   */
  getStatistics(): Record<string, any> {
    const items = this.service.readAll()

    return {
      total: items.length,
      createdToday: items.filter((item: any) => {
        const createdAt = item.createdAt
        if (!createdAt) return false

        const today = new Date().toISOString().split('T')[0]
        return createdAt.startsWith(today)
      }).length
    }
  }

  /**
   * 代理基础服务方法
   */
  create(item: T): T {
    return this.service.create(item)
  }

  read(id: string): T | undefined {
    return this.service.read(id)
  }

  readAll(): T[] {
    return this.service.readAll()
  }

  update(id: string, updates: Partial<T>): T | undefined {
    return this.service.update(id, updates)
  }

  delete(id: string): boolean {
    return this.service.delete(id)
  }

  clear(): void {
    return this.service.clear()
  }

  count(): number {
    return this.service.count()
  }

  find(options?: { filters?: Partial<T>; sortBy?: keyof T; sortOrder?: 'asc' | 'desc' }): T[] {
    return this.service.find(options)
  }

  findBy<K extends keyof T>(field: K, value: T[K]): T[] {
    return this.service.findBy(field, value)
  }

  findOneBy<K extends keyof T>(field: K, value: T[K]): T | undefined {
    return this.service.findOneBy(field, value)
  }
}

/**
 * 创建增强的Mock服务
 */
export function createEnhancedMockService<T extends { id?: string }>(
  name: string,
  initialData?: T[]
): EnhancedMockService<T> {
  const service = new EnhancedMockService<T>(name)

  if (initialData) {
    initialData.forEach(item => {
      if (!item.id) {
        item.id = generateId()
      }
      service.create(item)
    })
  }

  return service
}
