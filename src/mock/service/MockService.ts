/**
 * @name MockService核心服务类
 * @description 提供Mock数据的CRUD操作和持久化管理
 * <AUTHOR>
 * @since 2025-01-24
 */

import { storage, generateId, deepClone } from '../utils'

export interface QueryOptions {
  // 分页参数
  page?: number
  size?: number
  // 排序参数
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  // 搜索参数
  keyword?: string
  // 筛选参数
  filters?: Record<string, any>
}

export interface BatchOperation<T> {
  ids: string[]
  operation: 'delete' | 'update'
  data?: Partial<T>
}

// 使用函数式实现替代类，避免构造函数问题
export function MockService<T extends { id?: string }>(collection: string) {
  const data = new Map<string, T>()
  const indexes = new Map<string, Set<string>>()

  // 恢复数据
  function restore() {
    try {
      const saved = storage.get<Array<[string, T]>>(collection)
      if (saved && Array.isArray(saved)) {
        saved.forEach(([id, item]) => {
          data.set(id, item)
          updateIndexes(id, item)
        })
      }
    } catch (error) {
      console.warn(`Failed to restore data for collection ${collection}:`, error)
    }
  }

  // 持久化数据
  function persist() {
    try {
      const dataArray = Array.from(data.entries())
      storage.set(collection, dataArray)
    } catch (error) {
      console.warn(`Failed to persist data for collection ${collection}:`, error)
    }
  }

  // 更新索引
  function updateIndexes(id: string, item: T) {
    Object.entries(item).forEach(([key, value]) => {
      if (typeof value === 'string' || typeof value === 'number') {
        const indexKey = `${key}:${value}`
        if (!indexes.has(indexKey)) {
          indexes.set(indexKey, new Set())
        }
        indexes.get(indexKey)!.add(id)
      }
    })
  }

  // 从索引中移除
  function removeFromIndexes(id: string) {
    indexes.forEach(idSet => {
      idSet.delete(id)
    })
  }

  // 初始化
  restore()

  const service = {
    /**
     * 创建数据
     */
    create(item: T): T {
      const id = item.id || generateId()
      const newData = { ...item, id } as T

      // 添加时间戳
      const timestamp = new Date().toISOString()
      if (!('createTime' in newData)) {
        ;(newData as any).createTime = timestamp
      }
      if (!('updateTime' in newData)) {
        ;(newData as any).updateTime = timestamp
      }

      data.set(id, newData)
      updateIndexes(id, newData)
      persist()

      return newData
    },

    /**
     * 批量创建
     */
    createBatch(items: T[]): T[] {
      const results = items.map(item => {
        const id = item.id || generateId()
        const newData = { ...item, id } as T

        const timestamp = new Date().toISOString()
        if (!('createTime' in newData)) {
          ;(newData as any).createTime = timestamp
        }
        if (!('updateTime' in newData)) {
          ;(newData as any).updateTime = timestamp
        }

        data.set(id, newData)
        updateIndexes(id, newData)

        return newData
      })

      persist()
      return results
    },

    /**
     * 读取单条数据
     */
    read(id: string): T | undefined {
      return data.get(id)
    },

    /**
     * 读取所有数据
     */
    readAll(): T[] {
      return Array.from(data.values())
    },

    /**
     * 更新数据
     */
    update(id: string, updates: Partial<T>): T | undefined {
      const existing = data.get(id)
      if (!existing) return undefined

      const updated = {
        ...existing,
        ...updates,
        id, // 确保ID不被覆盖
        updateTime: new Date().toISOString()
      } as T

      data.set(id, updated)
      updateIndexes(id, updated)
      persist()

      return updated
    },

    /**
     * 删除数据
     */
    delete(id: string): boolean {
      const result = data.delete(id)
      if (result) {
        removeFromIndexes(id)
        persist()
      }
      return result
    },

    /**
     * 批量删除
     */
    deleteBatch(ids: string[]): number {
      let count = 0
      ids.forEach(id => {
        if (data.delete(id)) {
          removeFromIndexes(id)
          count++
        }
      })

      if (count > 0) {
        persist()
      }

      return count
    },

    /**
     * 查询数据
     */
    find(query: QueryOptions = {}): T[] {
      let results = Array.from(data.values())

      // 关键词搜索
      if (query.keyword) {
        const keyword = query.keyword.toLowerCase()
        results = results.filter(item => {
          return Object.values(item).some(value => {
            if (typeof value === 'string') {
              return value.toLowerCase().includes(keyword)
            }
            return false
          })
        })
      }

      // 筛选条件
      if (query.filters) {
        Object.entries(query.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            results = results.filter(item => {
              const itemValue = (item as any)[key]

              // 数组包含判断
              if (Array.isArray(value)) {
                return value.includes(itemValue)
              }

              // 范围查询
              if (typeof value === 'object' && value !== null) {
                if ('min' in value && 'max' in value) {
                  return itemValue >= value.min && itemValue <= value.max
                }
                if ('gt' in value) {
                  return itemValue > value.gt
                }
                if ('gte' in value) {
                  return itemValue >= value.gte
                }
                if ('lt' in value) {
                  return itemValue < value.lt
                }
                if ('lte' in value) {
                  return itemValue <= value.lte
                }
              }

              // 精确匹配
              return itemValue === value
            })
          }
        })
      }

      // 排序
      if (query.sortBy) {
        results.sort((a, b) => {
          const aVal = (a as any)[query.sortBy!]
          const bVal = (b as any)[query.sortBy!]

          if (aVal === bVal) return 0

          const comparison = aVal > bVal ? 1 : -1
          return query.sortOrder === 'desc' ? -comparison : comparison
        })
      }

      // 分页
      if (query.page && query.size) {
        const start = (query.page - 1) * query.size
        const end = start + query.size
        results = results.slice(start, end)
      }

      return results
    },

    /**
     * 按字段查找
     */
    findBy<K extends keyof T>(field: K, value: T[K]): T[] {
      return Array.from(data.values()).filter(item => item[field] === value)
    },

    /**
     * 按字段查找第一个
     */
    findOneBy<K extends keyof T>(field: K, value: T[K]): T | undefined {
      return service.findBy(field, value)[0]
    },

    /**
     * 计数
     */
    count(query: QueryOptions = {}): number {
      return service.find(query).length
    },

    /**
     * 清空数据
     */
    clear(): void {
      data.clear()
      indexes.clear()
      persist()
    },

    /**
     * 批量操作
     */
    batch(operation: BatchOperation<T>): number {
      if (operation.operation === 'delete') {
        return service.deleteBatch(operation.ids)
      } else if (operation.operation === 'update' && operation.data) {
        let count = 0
        operation.ids.forEach(id => {
          if (service.update(id, operation.data!)) {
            count++
          }
        })
        return count
      }
      return 0
    },

    /**
     * 检查是否存在
     */
    exists(id: string): boolean {
      return data.has(id)
    },

    /**
     * 获取统计信息
     */
    getStats() {
      return {
        total: data.size,
        collection,
        lastUpdated: new Date().toISOString()
      }
    }
  }

  return service
}

// 全局Mock服务管理器
export class MockServiceManager {
  private static services: Map<string, any> = new Map()

  /**
   * 获取或创建服务实例
   */
  static getService<T extends { id?: string }>(collection: string) {
    if (!this.services.has(collection)) {
      try {
        const service = MockService<T>(collection)
        this.services.set(collection, service)
      } catch (error) {
        console.error(`Failed to create MockService for collection ${collection}:`, error)
        // 返回一个基础的模拟服务
        const fallbackService = {
          create: () => ({}),
          read: () => undefined,
          readAll: () => [],
          update: () => undefined,
          delete: () => false,
          find: () => [],
          count: () => 0,
          clear: () => {},
          exists: () => false,
          getStats: () => ({ total: 0, collection, lastUpdated: new Date().toISOString() })
        }
        this.services.set(collection, fallbackService)
      }
    }
    return this.services.get(collection)!
  }

  /**
   * 清空所有服务
   */
  static clearAll(): void {
    this.services.forEach(service => {
      if (service.clear) {
        service.clear()
      }
    })
    this.services.clear()
  }

  /**
   * 获取所有服务统计
   */
  static getAllStats(): Array<{ collection: string; count: number }> {
    return Array.from(this.services.entries()).map(([collection, service]) => ({
      collection,
      count: service.count ? service.count() : 0
    }))
  }
}

// 导出一个工厂函数，避免类构造器问题
export function createMockService<T extends { id?: string }>(collection: string) {
  return MockService<T>(collection)
}

export default MockService
