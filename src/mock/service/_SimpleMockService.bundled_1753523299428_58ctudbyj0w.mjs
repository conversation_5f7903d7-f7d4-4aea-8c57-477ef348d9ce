// src/mock/utils/index.ts
import { faker } from "@faker-js/faker/locale/zh_CN";

// src/mock/config.ts
var mockConfig = {
  // API前缀
  apiPrefix: "/api",
  // 是否启用响应延迟
  delayEnabled: true,
  // 默认响应延迟（模拟网络延迟）
  defaultDelay: 200,
  // 响应延迟范围
  delayRange: {
    min: 100,
    max: 500
  },
  // 分页默认配置
  pagination: {
    defaultPage: 1,
    defaultSize: 10,
    maxSize: 100
  },
  // 日志配置
  logger: {
    enabled: true,
    request: true,
    response: true,
    error: true
  },
  // 数据持久化配置
  persistence: {
    enabled: true,
    storage: "localStorage",
    prefix: "hr_mock_",
    // 过期时间（小时）
    expires: 24
  },
  // 错误模拟配置
  errorSimulation: {
    enabled: false,
    // 错误率（0-1）
    rate: 0.1,
    // 错误码范围
    codes: [400, 401, 403, 404, 500]
  },
  // 默认响应格式
  responseFormat: {
    successCode: 200,
    errorCode: 500,
    messageKey: "message",
    dataKey: "data",
    codeKey: "code",
    successKey: "success"
  }
};
var config_default = mockConfig;

// src/mock/utils/errorHandler.ts
var MockError = class extends Error {
  /** 错误类型 */
  type;
  /** HTTP状态码 */
  statusCode;
  /** 错误详情 */
  details;
  /** 错误发生时间 */
  timestamp;
  constructor(message, type = "SYSTEM_ERROR" /* SYSTEM_ERROR */, statusCode = 500, details) {
    super(message);
    this.name = "MockError";
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = (/* @__PURE__ */ new Date()).toISOString();
  }
};
var ErrorCollector = class {
  errors = [];
  /**
   * 收集错误
   */
  collect(error, context) {
    this.errors.push({
      error,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      context
    });
  }
  /**
   * 获取所有错误
   */
  getErrors() {
    return [...this.errors];
  }
  /**
   * 清空错误
   */
  clear() {
    this.errors = [];
  }
  /**
   * 获取错误统计
   */
  getStatistics() {
    const stats = {};
    this.errors.forEach(({ error }) => {
      const type = error instanceof MockError ? error.type : "UNKNOWN";
      stats[type] = (stats[type] || 0) + 1;
    });
    return stats;
  }
};
var globalErrorCollector = new ErrorCollector();

// src/mock/utils/index.ts
function generateId() {
  return faker.string.uuid();
}
var isBrowser = typeof window !== "undefined" && typeof window.localStorage !== "undefined";
var memoryStorage = {};
var storage = {
  /**
   * 获取存储key
   */
  getKey(key) {
    return `${config_default.persistence.prefix}${key}`;
  },
  /**
   * 保存数据
   */
  set(key, value) {
    if (!config_default.persistence.enabled) return;
    const storageKey = this.getKey(key);
    const data = {
      value,
      expires: Date.now() + config_default.persistence.expires * 3600 * 1e3,
      timestamp: Date.now()
    };
    try {
      if (!isBrowser) {
        memoryStorage[storageKey] = JSON.stringify(data);
        return;
      }
      if (config_default.persistence.storage === "localStorage") {
        localStorage.setItem(storageKey, JSON.stringify(data));
      } else {
        sessionStorage.setItem(storageKey, JSON.stringify(data));
      }
    } catch (e) {
      console.error("Mock\u6570\u636E\u5B58\u50A8\u5931\u8D25:", e);
    }
  },
  /**
   * 获取数据
   */
  get(key, defaultValue) {
    if (!config_default.persistence.enabled) return defaultValue;
    const storageKey = this.getKey(key);
    try {
      if (!isBrowser) {
        const item2 = memoryStorage[storageKey];
        if (!item2) return defaultValue;
        const data2 = JSON.parse(item2);
        if (data2.expires && data2.expires < Date.now()) {
          delete memoryStorage[storageKey];
          return defaultValue;
        }
        return data2.value;
      }
      const storage2 = config_default.persistence.storage === "localStorage" ? localStorage : sessionStorage;
      const item = storage2.getItem(storageKey);
      if (!item) return defaultValue;
      const data = JSON.parse(item);
      if (data.expires && data.expires < Date.now()) {
        storage2.removeItem(storageKey);
        return defaultValue;
      }
      return data.value;
    } catch (e) {
      console.error("Mock\u6570\u636E\u8BFB\u53D6\u5931\u8D25:", e);
      return defaultValue;
    }
  },
  /**
   * 删除数据
   */
  remove(key) {
    if (!config_default.persistence.enabled) return;
    const storageKey = this.getKey(key);
    try {
      if (!isBrowser) {
        delete memoryStorage[storageKey];
        return;
      }
      if (config_default.persistence.storage === "localStorage") {
        localStorage.removeItem(storageKey);
      } else {
        sessionStorage.removeItem(storageKey);
      }
    } catch (e) {
      console.error("Mock\u6570\u636E\u5220\u9664\u5931\u8D25:", e);
    }
  },
  /**
   * 清空所有Mock数据
   */
  clear() {
    if (!config_default.persistence.enabled) return;
    const prefix = config_default.persistence.prefix;
    if (!isBrowser) {
      Object.keys(memoryStorage).forEach((key) => {
        if (key.startsWith(prefix)) {
          delete memoryStorage[key];
        }
      });
      return;
    }
    const storage2 = config_default.persistence.storage === "localStorage" ? localStorage : sessionStorage;
    const keys = Object.keys(storage2);
    keys.forEach((key) => {
      if (key.startsWith(prefix)) {
        storage2.removeItem(key);
      }
    });
  }
};

// src/mock/service/SimpleMockService.ts
var dataStores = /* @__PURE__ */ new Map();
function getDataStore(collection) {
  if (!dataStores.has(collection)) {
    const store = /* @__PURE__ */ new Map();
    const saved = storage.get(collection);
    if (saved && Array.isArray(saved)) {
      saved.forEach(([id, item]) => store.set(id, item));
    }
    dataStores.set(collection, store);
  }
  return dataStores.get(collection);
}
function persistData(collection) {
  const store = getDataStore(collection);
  const dataArray = Array.from(store.entries());
  storage.set(collection, dataArray);
}
function createSimpleMockService(collection) {
  const store = getDataStore(collection);
  return {
    // 创建
    create(item) {
      const id = item.id || generateId();
      const newItem = {
        ...item,
        id,
        createTime: (/* @__PURE__ */ new Date()).toISOString(),
        updateTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      store.set(id, newItem);
      persistData(collection);
      return newItem;
    },
    // 批量创建
    createBatch(items) {
      const created = items.map((item) => {
        const id = item.id || generateId();
        const newItem = {
          ...item,
          id,
          createTime: (/* @__PURE__ */ new Date()).toISOString(),
          updateTime: (/* @__PURE__ */ new Date()).toISOString()
        };
        store.set(id, newItem);
        return newItem;
      });
      persistData(collection);
      return created;
    },
    // 读取单个
    read(id) {
      return store.get(id);
    },
    // 读取所有
    readAll() {
      return Array.from(store.values());
    },
    // 更新
    update(id, updates) {
      const existing = store.get(id);
      if (!existing) return void 0;
      const updated = {
        ...existing,
        ...updates,
        id,
        // 保持ID不变
        updateTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      store.set(id, updated);
      persistData(collection);
      return updated;
    },
    // 删除
    delete(id) {
      const result = store.delete(id);
      if (result) {
        persistData(collection);
      }
      return result;
    },
    // 清空
    clear() {
      store.clear();
      persistData(collection);
    },
    // 计数
    count() {
      return store.size;
    },
    // 查找
    find(options = {}) {
      let results = this.readAll();
      if (options.filters) {
        results = results.filter((item) => {
          return Object.entries(options.filters).every(([key, value]) => {
            return item[key] === value;
          });
        });
      }
      if (options.sortBy) {
        results.sort((a, b) => {
          const aVal = a[options.sortBy];
          const bVal = b[options.sortBy];
          if (options.sortOrder === "desc") {
            return bVal > aVal ? 1 : -1;
          }
          return aVal > bVal ? 1 : -1;
        });
      }
      return results;
    },
    // 按字段查找
    findBy(field, value) {
      return this.readAll().filter((item) => item[field] === value);
    },
    // 按字段查找第一个
    findOneBy(field, value) {
      return this.findBy(field, value)[0];
    }
  };
}
var SimpleMockServiceManager = {
  services: /* @__PURE__ */ new Map(),
  getService(collection) {
    if (!this.services.has(collection)) {
      this.services.set(collection, createSimpleMockService(collection));
    }
    return this.services.get(collection);
  },
  createService(collection, options) {
    const service = createSimpleMockService(collection);
    if (options?.initialData) {
      service.clear();
      const data = options.initialData();
      data.forEach((item) => service.create(item));
    }
    this.services.set(collection, service);
    return service;
  },
  clearAll() {
    this.services.forEach((service) => service.clear());
    this.services.clear();
    dataStores.clear();
  }
};
export {
  SimpleMockServiceManager,
  createSimpleMockService
};
//# sourceMappingURL=data:application/json;base64,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
