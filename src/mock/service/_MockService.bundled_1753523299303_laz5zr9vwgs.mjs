// src/mock/utils/index.ts
import { faker } from "@faker-js/faker/locale/zh_CN";

// src/mock/config.ts
var mockConfig = {
  // API前缀
  apiPrefix: "/api",
  // 是否启用响应延迟
  delayEnabled: true,
  // 默认响应延迟（模拟网络延迟）
  defaultDelay: 200,
  // 响应延迟范围
  delayRange: {
    min: 100,
    max: 500
  },
  // 分页默认配置
  pagination: {
    defaultPage: 1,
    defaultSize: 10,
    maxSize: 100
  },
  // 日志配置
  logger: {
    enabled: true,
    request: true,
    response: true,
    error: true
  },
  // 数据持久化配置
  persistence: {
    enabled: true,
    storage: "localStorage",
    prefix: "hr_mock_",
    // 过期时间（小时）
    expires: 24
  },
  // 错误模拟配置
  errorSimulation: {
    enabled: false,
    // 错误率（0-1）
    rate: 0.1,
    // 错误码范围
    codes: [400, 401, 403, 404, 500]
  },
  // 默认响应格式
  responseFormat: {
    successCode: 200,
    errorCode: 500,
    messageKey: "message",
    dataKey: "data",
    codeKey: "code",
    successKey: "success"
  }
};
var config_default = mockConfig;

// src/mock/utils/errorHandler.ts
var MockError = class extends Error {
  /** 错误类型 */
  type;
  /** HTTP状态码 */
  statusCode;
  /** 错误详情 */
  details;
  /** 错误发生时间 */
  timestamp;
  constructor(message, type = "SYSTEM_ERROR" /* SYSTEM_ERROR */, statusCode = 500, details) {
    super(message);
    this.name = "MockError";
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = (/* @__PURE__ */ new Date()).toISOString();
  }
};
var ErrorCollector = class {
  errors = [];
  /**
   * 收集错误
   */
  collect(error, context) {
    this.errors.push({
      error,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      context
    });
  }
  /**
   * 获取所有错误
   */
  getErrors() {
    return [...this.errors];
  }
  /**
   * 清空错误
   */
  clear() {
    this.errors = [];
  }
  /**
   * 获取错误统计
   */
  getStatistics() {
    const stats = {};
    this.errors.forEach(({ error }) => {
      const type = error instanceof MockError ? error.type : "UNKNOWN";
      stats[type] = (stats[type] || 0) + 1;
    });
    return stats;
  }
};
var globalErrorCollector = new ErrorCollector();

// src/mock/utils/index.ts
function generateId() {
  return faker.string.uuid();
}
var isBrowser = typeof window !== "undefined" && typeof window.localStorage !== "undefined";
var memoryStorage = {};
var storage = {
  /**
   * 获取存储key
   */
  getKey(key) {
    return `${config_default.persistence.prefix}${key}`;
  },
  /**
   * 保存数据
   */
  set(key, value) {
    if (!config_default.persistence.enabled) return;
    const storageKey = this.getKey(key);
    const data = {
      value,
      expires: Date.now() + config_default.persistence.expires * 3600 * 1e3,
      timestamp: Date.now()
    };
    try {
      if (!isBrowser) {
        memoryStorage[storageKey] = JSON.stringify(data);
        return;
      }
      if (config_default.persistence.storage === "localStorage") {
        localStorage.setItem(storageKey, JSON.stringify(data));
      } else {
        sessionStorage.setItem(storageKey, JSON.stringify(data));
      }
    } catch (e) {
      console.error("Mock\u6570\u636E\u5B58\u50A8\u5931\u8D25:", e);
    }
  },
  /**
   * 获取数据
   */
  get(key, defaultValue) {
    if (!config_default.persistence.enabled) return defaultValue;
    const storageKey = this.getKey(key);
    try {
      if (!isBrowser) {
        const item2 = memoryStorage[storageKey];
        if (!item2) return defaultValue;
        const data2 = JSON.parse(item2);
        if (data2.expires && data2.expires < Date.now()) {
          delete memoryStorage[storageKey];
          return defaultValue;
        }
        return data2.value;
      }
      const storage2 = config_default.persistence.storage === "localStorage" ? localStorage : sessionStorage;
      const item = storage2.getItem(storageKey);
      if (!item) return defaultValue;
      const data = JSON.parse(item);
      if (data.expires && data.expires < Date.now()) {
        storage2.removeItem(storageKey);
        return defaultValue;
      }
      return data.value;
    } catch (e) {
      console.error("Mock\u6570\u636E\u8BFB\u53D6\u5931\u8D25:", e);
      return defaultValue;
    }
  },
  /**
   * 删除数据
   */
  remove(key) {
    if (!config_default.persistence.enabled) return;
    const storageKey = this.getKey(key);
    try {
      if (!isBrowser) {
        delete memoryStorage[storageKey];
        return;
      }
      if (config_default.persistence.storage === "localStorage") {
        localStorage.removeItem(storageKey);
      } else {
        sessionStorage.removeItem(storageKey);
      }
    } catch (e) {
      console.error("Mock\u6570\u636E\u5220\u9664\u5931\u8D25:", e);
    }
  },
  /**
   * 清空所有Mock数据
   */
  clear() {
    if (!config_default.persistence.enabled) return;
    const prefix = config_default.persistence.prefix;
    if (!isBrowser) {
      Object.keys(memoryStorage).forEach((key) => {
        if (key.startsWith(prefix)) {
          delete memoryStorage[key];
        }
      });
      return;
    }
    const storage2 = config_default.persistence.storage === "localStorage" ? localStorage : sessionStorage;
    const keys = Object.keys(storage2);
    keys.forEach((key) => {
      if (key.startsWith(prefix)) {
        storage2.removeItem(key);
      }
    });
  }
};

// src/mock/service/MockService.ts
function MockService(collection) {
  const data = /* @__PURE__ */ new Map();
  const indexes = /* @__PURE__ */ new Map();
  function restore() {
    try {
      const saved = storage.get(collection);
      if (saved && Array.isArray(saved)) {
        saved.forEach(([id, item]) => {
          data.set(id, item);
          updateIndexes(id, item);
        });
      }
    } catch (error) {
      console.warn(`Failed to restore data for collection ${collection}:`, error);
    }
  }
  function persist() {
    try {
      const dataArray = Array.from(data.entries());
      storage.set(collection, dataArray);
    } catch (error) {
      console.warn(`Failed to persist data for collection ${collection}:`, error);
    }
  }
  function updateIndexes(id, item) {
    Object.entries(item).forEach(([key, value]) => {
      if (typeof value === "string" || typeof value === "number") {
        const indexKey = `${key}:${value}`;
        if (!indexes.has(indexKey)) {
          indexes.set(indexKey, /* @__PURE__ */ new Set());
        }
        indexes.get(indexKey).add(id);
      }
    });
  }
  function removeFromIndexes(id) {
    indexes.forEach((idSet) => {
      idSet.delete(id);
    });
  }
  restore();
  const service = {
    /**
     * 创建数据
     */
    create(item) {
      const id = item.id || generateId();
      const newData = { ...item, id };
      const timestamp = (/* @__PURE__ */ new Date()).toISOString();
      if (!("createTime" in newData)) {
        ;
        newData.createTime = timestamp;
      }
      if (!("updateTime" in newData)) {
        ;
        newData.updateTime = timestamp;
      }
      data.set(id, newData);
      updateIndexes(id, newData);
      persist();
      return newData;
    },
    /**
     * 批量创建
     */
    createBatch(items) {
      const results = items.map((item) => {
        const id = item.id || generateId();
        const newData = { ...item, id };
        const timestamp = (/* @__PURE__ */ new Date()).toISOString();
        if (!("createTime" in newData)) {
          ;
          newData.createTime = timestamp;
        }
        if (!("updateTime" in newData)) {
          ;
          newData.updateTime = timestamp;
        }
        data.set(id, newData);
        updateIndexes(id, newData);
        return newData;
      });
      persist();
      return results;
    },
    /**
     * 读取单条数据
     */
    read(id) {
      return data.get(id);
    },
    /**
     * 读取所有数据
     */
    readAll() {
      return Array.from(data.values());
    },
    /**
     * 更新数据
     */
    update(id, updates) {
      const existing = data.get(id);
      if (!existing) return void 0;
      const updated = {
        ...existing,
        ...updates,
        id,
        // 确保ID不被覆盖
        updateTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      data.set(id, updated);
      updateIndexes(id, updated);
      persist();
      return updated;
    },
    /**
     * 删除数据
     */
    delete(id) {
      const result = data.delete(id);
      if (result) {
        removeFromIndexes(id);
        persist();
      }
      return result;
    },
    /**
     * 批量删除
     */
    deleteBatch(ids) {
      let count = 0;
      ids.forEach((id) => {
        if (data.delete(id)) {
          removeFromIndexes(id);
          count++;
        }
      });
      if (count > 0) {
        persist();
      }
      return count;
    },
    /**
     * 查询数据
     */
    find(query = {}) {
      let results = Array.from(data.values());
      if (query.keyword) {
        const keyword = query.keyword.toLowerCase();
        results = results.filter((item) => {
          return Object.values(item).some((value) => {
            if (typeof value === "string") {
              return value.toLowerCase().includes(keyword);
            }
            return false;
          });
        });
      }
      if (query.filters) {
        Object.entries(query.filters).forEach(([key, value]) => {
          if (value !== void 0 && value !== null && value !== "") {
            results = results.filter((item) => {
              const itemValue = item[key];
              if (Array.isArray(value)) {
                return value.includes(itemValue);
              }
              if (typeof value === "object" && value !== null) {
                if ("min" in value && "max" in value) {
                  return itemValue >= value.min && itemValue <= value.max;
                }
                if ("gt" in value) {
                  return itemValue > value.gt;
                }
                if ("gte" in value) {
                  return itemValue >= value.gte;
                }
                if ("lt" in value) {
                  return itemValue < value.lt;
                }
                if ("lte" in value) {
                  return itemValue <= value.lte;
                }
              }
              return itemValue === value;
            });
          }
        });
      }
      if (query.sortBy) {
        results.sort((a, b) => {
          const aVal = a[query.sortBy];
          const bVal = b[query.sortBy];
          if (aVal === bVal) return 0;
          const comparison = aVal > bVal ? 1 : -1;
          return query.sortOrder === "desc" ? -comparison : comparison;
        });
      }
      if (query.page && query.size) {
        const start = (query.page - 1) * query.size;
        const end = start + query.size;
        results = results.slice(start, end);
      }
      return results;
    },
    /**
     * 按字段查找
     */
    findBy(field, value) {
      return Array.from(data.values()).filter((item) => item[field] === value);
    },
    /**
     * 按字段查找第一个
     */
    findOneBy(field, value) {
      return service.findBy(field, value)[0];
    },
    /**
     * 计数
     */
    count(query = {}) {
      return service.find(query).length;
    },
    /**
     * 清空数据
     */
    clear() {
      data.clear();
      indexes.clear();
      persist();
    },
    /**
     * 批量操作
     */
    batch(operation) {
      if (operation.operation === "delete") {
        return service.deleteBatch(operation.ids);
      } else if (operation.operation === "update" && operation.data) {
        let count = 0;
        operation.ids.forEach((id) => {
          if (service.update(id, operation.data)) {
            count++;
          }
        });
        return count;
      }
      return 0;
    },
    /**
     * 检查是否存在
     */
    exists(id) {
      return data.has(id);
    },
    /**
     * 获取统计信息
     */
    getStats() {
      return {
        total: data.size,
        collection,
        lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
      };
    }
  };
  return service;
}
var MockServiceManager = class {
  static services = /* @__PURE__ */ new Map();
  /**
   * 获取或创建服务实例
   */
  static getService(collection) {
    if (!this.services.has(collection)) {
      try {
        const service = MockService(collection);
        this.services.set(collection, service);
      } catch (error) {
        console.error(`Failed to create MockService for collection ${collection}:`, error);
        const fallbackService = {
          create: () => ({}),
          read: () => void 0,
          readAll: () => [],
          update: () => void 0,
          delete: () => false,
          find: () => [],
          count: () => 0,
          clear: () => {
          },
          exists: () => false,
          getStats: () => ({ total: 0, collection, lastUpdated: (/* @__PURE__ */ new Date()).toISOString() })
        };
        this.services.set(collection, fallbackService);
      }
    }
    return this.services.get(collection);
  }
  /**
   * 清空所有服务
   */
  static clearAll() {
    this.services.forEach((service) => {
      if (service.clear) {
        service.clear();
      }
    });
    this.services.clear();
  }
  /**
   * 获取所有服务统计
   */
  static getAllStats() {
    return Array.from(this.services.entries()).map(([collection, service]) => ({
      collection,
      count: service.count ? service.count() : 0
    }));
  }
};
function createMockService(collection) {
  return MockService(collection);
}
var MockService_default = MockService;
export {
  MockService,
  MockServiceManager,
  createMockService,
  MockService_default as default
};
//# sourceMappingURL=data:application/json;base64,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
