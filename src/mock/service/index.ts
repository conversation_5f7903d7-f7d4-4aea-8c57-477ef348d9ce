/**
 * @name Mock服务导出
 * @description 统一导出Mock服务相关功能
 * <AUTHOR>
 * @since 2025-01-24
 */

// 使用简化版Mock服务避免构造器问题
export { SimpleMockServiceManager as MockServiceManager } from './SimpleMockService'
export { createSimpleMockService as createMockService } from './SimpleMockService'

// 导出类型定义
export type { QueryOptions, BatchOperation } from './MockService'
export type { PaginatedResult } from './EnhancedMockService'
