// src/mock/utils/index.ts
import { faker } from "@faker-js/faker/locale/zh_CN";

// src/mock/config.ts
var mockConfig = {
  // API前缀
  apiPrefix: "/api",
  // 是否启用响应延迟
  delayEnabled: true,
  // 默认响应延迟（模拟网络延迟）
  defaultDelay: 200,
  // 响应延迟范围
  delayRange: {
    min: 100,
    max: 500
  },
  // 分页默认配置
  pagination: {
    defaultPage: 1,
    defaultSize: 10,
    maxSize: 100
  },
  // 日志配置
  logger: {
    enabled: true,
    request: true,
    response: true,
    error: true
  },
  // 数据持久化配置
  persistence: {
    enabled: true,
    storage: "localStorage",
    prefix: "hr_mock_",
    // 过期时间（小时）
    expires: 24
  },
  // 错误模拟配置
  errorSimulation: {
    enabled: false,
    // 错误率（0-1）
    rate: 0.1,
    // 错误码范围
    codes: [400, 401, 403, 404, 500]
  },
  // 默认响应格式
  responseFormat: {
    successCode: 200,
    errorCode: 500,
    messageKey: "message",
    dataKey: "data",
    codeKey: "code",
    successKey: "success"
  }
};
var config_default = mockConfig;

// src/mock/utils/errorHandler.ts
var MockError = class extends Error {
  /** 错误类型 */
  type;
  /** HTTP状态码 */
  statusCode;
  /** 错误详情 */
  details;
  /** 错误发生时间 */
  timestamp;
  constructor(message, type = "SYSTEM_ERROR" /* SYSTEM_ERROR */, statusCode = 500, details) {
    super(message);
    this.name = "MockError";
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = (/* @__PURE__ */ new Date()).toISOString();
  }
};
var ErrorCollector = class {
  errors = [];
  /**
   * 收集错误
   */
  collect(error, context) {
    this.errors.push({
      error,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      context
    });
  }
  /**
   * 获取所有错误
   */
  getErrors() {
    return [...this.errors];
  }
  /**
   * 清空错误
   */
  clear() {
    this.errors = [];
  }
  /**
   * 获取错误统计
   */
  getStatistics() {
    const stats = {};
    this.errors.forEach(({ error }) => {
      const type = error instanceof MockError ? error.type : "UNKNOWN";
      stats[type] = (stats[type] || 0) + 1;
    });
    return stats;
  }
};
var globalErrorCollector = new ErrorCollector();

// src/mock/utils/index.ts
function generateId() {
  return faker.string.uuid();
}
var isBrowser = typeof window !== "undefined" && typeof window.localStorage !== "undefined";
var memoryStorage = {};
var storage = {
  /**
   * 获取存储key
   */
  getKey(key) {
    return `${config_default.persistence.prefix}${key}`;
  },
  /**
   * 保存数据
   */
  set(key, value) {
    if (!config_default.persistence.enabled) return;
    const storageKey = this.getKey(key);
    const data = {
      value,
      expires: Date.now() + config_default.persistence.expires * 3600 * 1e3,
      timestamp: Date.now()
    };
    try {
      if (!isBrowser) {
        memoryStorage[storageKey] = JSON.stringify(data);
        return;
      }
      if (config_default.persistence.storage === "localStorage") {
        localStorage.setItem(storageKey, JSON.stringify(data));
      } else {
        sessionStorage.setItem(storageKey, JSON.stringify(data));
      }
    } catch (e) {
      console.error("Mock\u6570\u636E\u5B58\u50A8\u5931\u8D25:", e);
    }
  },
  /**
   * 获取数据
   */
  get(key, defaultValue) {
    if (!config_default.persistence.enabled) return defaultValue;
    const storageKey = this.getKey(key);
    try {
      if (!isBrowser) {
        const item2 = memoryStorage[storageKey];
        if (!item2) return defaultValue;
        const data2 = JSON.parse(item2);
        if (data2.expires && data2.expires < Date.now()) {
          delete memoryStorage[storageKey];
          return defaultValue;
        }
        return data2.value;
      }
      const storage2 = config_default.persistence.storage === "localStorage" ? localStorage : sessionStorage;
      const item = storage2.getItem(storageKey);
      if (!item) return defaultValue;
      const data = JSON.parse(item);
      if (data.expires && data.expires < Date.now()) {
        storage2.removeItem(storageKey);
        return defaultValue;
      }
      return data.value;
    } catch (e) {
      console.error("Mock\u6570\u636E\u8BFB\u53D6\u5931\u8D25:", e);
      return defaultValue;
    }
  },
  /**
   * 删除数据
   */
  remove(key) {
    if (!config_default.persistence.enabled) return;
    const storageKey = this.getKey(key);
    try {
      if (!isBrowser) {
        delete memoryStorage[storageKey];
        return;
      }
      if (config_default.persistence.storage === "localStorage") {
        localStorage.removeItem(storageKey);
      } else {
        sessionStorage.removeItem(storageKey);
      }
    } catch (e) {
      console.error("Mock\u6570\u636E\u5220\u9664\u5931\u8D25:", e);
    }
  },
  /**
   * 清空所有Mock数据
   */
  clear() {
    if (!config_default.persistence.enabled) return;
    const prefix = config_default.persistence.prefix;
    if (!isBrowser) {
      Object.keys(memoryStorage).forEach((key) => {
        if (key.startsWith(prefix)) {
          delete memoryStorage[key];
        }
      });
      return;
    }
    const storage2 = config_default.persistence.storage === "localStorage" ? localStorage : sessionStorage;
    const keys = Object.keys(storage2);
    keys.forEach((key) => {
      if (key.startsWith(prefix)) {
        storage2.removeItem(key);
      }
    });
  }
};

// src/mock/service/SimpleMockService.ts
var dataStores = /* @__PURE__ */ new Map();
function getDataStore(collection) {
  if (!dataStores.has(collection)) {
    const store = /* @__PURE__ */ new Map();
    const saved = storage.get(collection);
    if (saved && Array.isArray(saved)) {
      saved.forEach(([id, item]) => store.set(id, item));
    }
    dataStores.set(collection, store);
  }
  return dataStores.get(collection);
}
function persistData(collection) {
  const store = getDataStore(collection);
  const dataArray = Array.from(store.entries());
  storage.set(collection, dataArray);
}
function createSimpleMockService(collection) {
  const store = getDataStore(collection);
  return {
    // 创建
    create(item) {
      const id = item.id || generateId();
      const newItem = {
        ...item,
        id,
        createTime: (/* @__PURE__ */ new Date()).toISOString(),
        updateTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      store.set(id, newItem);
      persistData(collection);
      return newItem;
    },
    // 批量创建
    createBatch(items) {
      const created = items.map((item) => {
        const id = item.id || generateId();
        const newItem = {
          ...item,
          id,
          createTime: (/* @__PURE__ */ new Date()).toISOString(),
          updateTime: (/* @__PURE__ */ new Date()).toISOString()
        };
        store.set(id, newItem);
        return newItem;
      });
      persistData(collection);
      return created;
    },
    // 读取单个
    read(id) {
      return store.get(id);
    },
    // 读取所有
    readAll() {
      return Array.from(store.values());
    },
    // 更新
    update(id, updates) {
      const existing = store.get(id);
      if (!existing) return void 0;
      const updated = {
        ...existing,
        ...updates,
        id,
        // 保持ID不变
        updateTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      store.set(id, updated);
      persistData(collection);
      return updated;
    },
    // 删除
    delete(id) {
      const result = store.delete(id);
      if (result) {
        persistData(collection);
      }
      return result;
    },
    // 清空
    clear() {
      store.clear();
      persistData(collection);
    },
    // 计数
    count() {
      return store.size;
    },
    // 查找
    find(options = {}) {
      let results = this.readAll();
      if (options.filters) {
        results = results.filter((item) => {
          return Object.entries(options.filters).every(([key, value]) => {
            return item[key] === value;
          });
        });
      }
      if (options.sortBy) {
        results.sort((a, b) => {
          const aVal = a[options.sortBy];
          const bVal = b[options.sortBy];
          if (options.sortOrder === "desc") {
            return bVal > aVal ? 1 : -1;
          }
          return aVal > bVal ? 1 : -1;
        });
      }
      return results;
    },
    // 按字段查找
    findBy(field, value) {
      return this.readAll().filter((item) => item[field] === value);
    },
    // 按字段查找第一个
    findOneBy(field, value) {
      return this.findBy(field, value)[0];
    }
  };
}

// src/mock/service/EnhancedMockService.ts
var EnhancedMockService = class {
  service;
  constructor(name) {
    this.service = createSimpleMockService(name);
  }
  /**
   * 获取标准化的分页参数
   */
  normalizePaginationParams(params) {
    const { defaultPage, defaultSize, maxSize } = config_default.pagination;
    const page = Math.max(1, params.page || params.current || defaultPage);
    const size = Math.min(Math.max(1, params.size || params.pageSize || defaultSize), maxSize);
    return { page, size };
  }
  /**
   * 获取标准化的排序参数
   */
  normalizeSortParams(params) {
    const field = params.sortField || params.orderBy;
    const orderValue = params.sortOrder || params.order || "asc";
    const order = orderValue === "descend" || orderValue === "desc" ? "desc" : "asc";
    return { field, order };
  }
  /**
   * 文本搜索过滤
   */
  filterByKeyword(items, keyword, searchFields) {
    if (!keyword || !keyword.trim()) {
      return items;
    }
    const lowerKeyword = keyword.toLowerCase().trim();
    return items.filter((item) => {
      return searchFields.some((field) => {
        const value = item[field];
        if (value == null) return false;
        const stringValue = String(value).toLowerCase();
        return stringValue.includes(lowerKeyword);
      });
    });
  }
  /**
   * 精确字段过滤
   */
  filterByFields(items, filters) {
    if (!filters || Object.keys(filters).length === 0) {
      return items;
    }
    return items.filter((item) => {
      return Object.entries(filters).every(([key, value]) => {
        if (value == null || value === "") return true;
        const itemValue = item[key];
        if (Array.isArray(value)) {
          return value.includes(itemValue);
        }
        return itemValue === value;
      });
    });
  }
  /**
   * 日期范围过滤
   */
  filterByDateRanges(items, dateRanges) {
    if (!dateRanges || Object.keys(dateRanges).length === 0) {
      return items;
    }
    return items.filter((item) => {
      return Object.entries(dateRanges).every(([field, range]) => {
        if (!range || !range.start && !range.end) return true;
        const fieldValue = item[field];
        if (!fieldValue) return false;
        if (range.start && fieldValue < range.start) return false;
        if (range.end && fieldValue > range.end) return false;
        return true;
      });
    });
  }
  /**
   * 排序
   */
  sortItems(items, sortParams) {
    if (!sortParams.field) {
      return items;
    }
    return [...items].sort((a, b) => {
      const aVal = a[sortParams.field];
      const bVal = b[sortParams.field];
      if (aVal == null && bVal == null) return 0;
      if (aVal == null) return 1;
      if (bVal == null) return -1;
      if (typeof aVal === "number" && typeof bVal === "number") {
        return sortParams.order === "asc" ? aVal - bVal : bVal - aVal;
      }
      const aStr = String(aVal);
      const bStr = String(bVal);
      if (sortParams.order === "asc") {
        return aStr.localeCompare(bStr, "zh-CN");
      } else {
        return bStr.localeCompare(aStr, "zh-CN");
      }
    });
  }
  /**
   * 分页处理
   */
  paginate(items, page, size) {
    const total = items.length;
    const pages = Math.ceil(total / size);
    const start = (page - 1) * size;
    const end = start + size;
    return {
      list: items.slice(start, end),
      total,
      page,
      size,
      pages
    };
  }
  /**
   * 综合查询方法
   */
  query(params) {
    let results = this.service.readAll();
    const keyword = params.keyword || params.search;
    if (keyword && params.searchFields) {
      results = this.filterByKeyword(results, keyword, params.searchFields);
    }
    if (params.filters) {
      results = this.filterByFields(results, params.filters);
    }
    if (params.dateRanges) {
      results = this.filterByDateRanges(results, params.dateRanges);
    }
    const sortParams = this.normalizeSortParams(params);
    if (sortParams.field) {
      results = this.sortItems(results, sortParams);
    }
    const { page, size } = this.normalizePaginationParams(params);
    return this.paginate(results, page, size);
  }
  /**
   * 批量创建
   */
  createBatch(items) {
    const created = [];
    for (const item of items) {
      const newItem = {
        ...item,
        id: generateId()
      };
      const result = this.service.create(newItem);
      if (result) {
        created.push(result);
      }
    }
    return created;
  }
  /**
   * 批量更新
   */
  updateBatch(updates) {
    const result = {
      success: [],
      failed: []
    };
    for (const update of updates) {
      const { id, ...data } = update;
      if (this.service.update(id, data)) {
        result.success.push(id);
      } else {
        result.failed.push(id);
      }
    }
    return result;
  }
  /**
   * 批量删除
   */
  deleteBatch(ids) {
    const result = {
      success: [],
      failed: []
    };
    for (const id of ids) {
      if (this.service.delete(id)) {
        result.success.push(id);
      } else {
        result.failed.push(id);
      }
    }
    return result;
  }
  /**
   * 按条件删除
   */
  deleteBy(predicate) {
    const items = this.service.readAll();
    let deletedCount = 0;
    for (const item of items) {
      if (predicate(item) && item.id) {
        if (this.service.delete(item.id)) {
          deletedCount++;
        }
      }
    }
    return deletedCount;
  }
  /**
   * 导出数据
   */
  export(ids) {
    if (!ids || ids.length === 0) {
      return this.service.readAll();
    }
    return ids.map((id) => this.service.read(id)).filter((item) => item !== void 0);
  }
  /**
   * 导入数据
   */
  import(items, mode = "merge") {
    const result = {
      success: 0,
      failed: 0,
      errors: []
    };
    if (mode === "replace") {
      this.service.clear();
    }
    for (let i = 0; i < items.length; i++) {
      try {
        const item = { ...items[i] };
        if (!item.id) {
          item.id = generateId();
        }
        const created = this.service.create(item);
        if (created) {
          result.success++;
        } else {
          result.failed++;
          result.errors.push({
            row: i + 1,
            message: "\u521B\u5EFA\u5931\u8D25"
          });
        }
      } catch (error) {
        result.failed++;
        result.errors.push({
          row: i + 1,
          message: error instanceof Error ? error.message : "\u672A\u77E5\u9519\u8BEF"
        });
      }
    }
    return result;
  }
  /**
   * 获取统计信息
   */
  getStatistics() {
    const items = this.service.readAll();
    return {
      total: items.length,
      createdToday: items.filter((item) => {
        const createdAt = item.createdAt;
        if (!createdAt) return false;
        const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
        return createdAt.startsWith(today);
      }).length
    };
  }
  /**
   * 代理基础服务方法
   */
  create(item) {
    return this.service.create(item);
  }
  read(id) {
    return this.service.read(id);
  }
  readAll() {
    return this.service.readAll();
  }
  update(id, updates) {
    return this.service.update(id, updates);
  }
  delete(id) {
    return this.service.delete(id);
  }
  clear() {
    return this.service.clear();
  }
  count() {
    return this.service.count();
  }
  find(options) {
    return this.service.find(options);
  }
  findBy(field, value) {
    return this.service.findBy(field, value);
  }
  findOneBy(field, value) {
    return this.service.findOneBy(field, value);
  }
};
function createEnhancedMockService(name, initialData) {
  const service = new EnhancedMockService(name);
  if (initialData) {
    initialData.forEach((item) => {
      if (!item.id) {
        item.id = generateId();
      }
      service.create(item);
    });
  }
  return service;
}
export {
  EnhancedMockService,
  createEnhancedMockService
};
//# sourceMappingURL=data:application/json;base64,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
