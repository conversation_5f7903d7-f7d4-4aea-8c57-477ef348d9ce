/**
 * @name 简化版Mock服务
 * @description 提供基础的Mock数据管理功能，避免类构造器问题
 * <AUTHOR>
 * @since 2025-01-24
 */

import { generateId, storage } from '../utils'

// 内存数据存储
const dataStores = new Map<string, Map<string, any>>()

/**
 * 获取或创建数据存储
 */
function getDataStore<T>(collection: string): Map<string, T> {
  if (!dataStores.has(collection)) {
    const store = new Map<string, T>()
    // 尝试从持久化存储恢复数据
    const saved = storage.get<Array<[string, T]>>(collection)
    if (saved && Array.isArray(saved)) {
      saved.forEach(([id, item]) => store.set(id, item))
    }
    dataStores.set(collection, store)
  }
  return dataStores.get(collection)!
}

/**
 * 持久化数据
 */
function persistData<T>(collection: string): void {
  const store = getDataStore<T>(collection)
  const dataArray = Array.from(store.entries())
  storage.set(collection, dataArray)
}

/**
 * 创建Mock服务对象
 */
export function createSimpleMockService<T extends { id?: string }>(collection: string) {
  const store = getDataStore<T>(collection)

  return {
    // 创建
    create(item: T): T {
      const id = item.id || generateId()
      const newItem = {
        ...item,
        id,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      store.set(id, newItem)
      persistData<T>(collection)
      return newItem
    },

    // 批量创建
    createBatch(items: T[]): T[] {
      const created = items.map(item => {
        const id = item.id || generateId()
        const newItem = {
          ...item,
          id,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        }
        store.set(id, newItem)
        return newItem
      })
      persistData<T>(collection)
      return created
    },

    // 读取单个
    read(id: string): T | undefined {
      return store.get(id)
    },

    // 读取所有
    readAll(): T[] {
      return Array.from(store.values())
    },

    // 更新
    update(id: string, updates: Partial<T>): T | undefined {
      const existing = store.get(id)
      if (!existing) return undefined

      const updated = {
        ...existing,
        ...updates,
        id, // 保持ID不变
        updateTime: new Date().toISOString()
      }
      store.set(id, updated)
      persistData<T>(collection)
      return updated
    },

    // 删除
    delete(id: string): boolean {
      const result = store.delete(id)
      if (result) {
        persistData<T>(collection)
      }
      return result
    },

    // 清空
    clear(): void {
      store.clear()
      persistData<T>(collection)
    },

    // 计数
    count(): number {
      return store.size
    },

    // 查找
    find(
      options: {
        filters?: Partial<T>
        sortBy?: keyof T
        sortOrder?: 'asc' | 'desc'
      } = {}
    ): T[] {
      let results = this.readAll()

      // 过滤
      if (options.filters) {
        results = results.filter(item => {
          return Object.entries(options.filters!).every(([key, value]) => {
            return (item as any)[key] === value
          })
        })
      }

      // 排序
      if (options.sortBy) {
        results.sort((a, b) => {
          const aVal = (a as any)[options.sortBy!]
          const bVal = (b as any)[options.sortBy!]
          if (options.sortOrder === 'desc') {
            return bVal > aVal ? 1 : -1
          }
          return aVal > bVal ? 1 : -1
        })
      }

      return results
    },

    // 按字段查找
    findBy<K extends keyof T>(field: K, value: T[K]): T[] {
      return this.readAll().filter(item => item[field] === value)
    },

    // 按字段查找第一个
    findOneBy<K extends keyof T>(field: K, value: T[K]): T | undefined {
      return this.findBy(field, value)[0]
    }
  }
}

/**
 * 简化版Mock服务管理器
 */
export const SimpleMockServiceManager = {
  services: new Map<string, any>(),

  getService<T extends { id?: string }>(collection: string) {
    if (!this.services.has(collection)) {
      this.services.set(collection, createSimpleMockService<T>(collection))
    }
    return this.services.get(collection)!
  },

  createService<T extends { id?: string }>(
    collection: string,
    options?: {
      initialData?: () => T[]
    }
  ) {
    const service = createSimpleMockService<T>(collection)

    // 如果提供了初始数据，先清空再添加
    if (options?.initialData) {
      service.clear()
      const data = options.initialData()
      data.forEach(item => service.create(item))
    }

    this.services.set(collection, service)
    return service
  },

  clearAll() {
    this.services.forEach(service => service.clear())
    this.services.clear()
    dataStores.clear()
  }
}
