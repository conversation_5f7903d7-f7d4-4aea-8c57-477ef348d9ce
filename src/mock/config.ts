/**
 * @name Mock配置
 * @description Mock服务的全局配置
 * <AUTHOR>
 * @since 2025-01-24
 */

export const mockConfig = {
  // API前缀
  apiPrefix: '/api',

  // 是否启用响应延迟
  delayEnabled: true,

  // 默认响应延迟（模拟网络延迟）
  defaultDelay: 200,

  // 响应延迟范围
  delayRange: {
    min: 100,
    max: 500
  },

  // 分页默认配置
  pagination: {
    defaultPage: 1,
    defaultSize: 10,
    maxSize: 100
  },

  // 日志配置
  logger: {
    enabled: true,
    request: true,
    response: true,
    error: true
  },

  // 数据持久化配置
  persistence: {
    enabled: true,
    storage: 'localStorage',
    prefix: 'hr_mock_',
    // 过期时间（小时）
    expires: 24
  },

  // 错误模拟配置
  errorSimulation: {
    enabled: false,
    // 错误率（0-1）
    rate: 0.1,
    // 错误码范围
    codes: [400, 401, 403, 404, 500]
  },

  // 默认响应格式
  responseFormat: {
    successCode: 200,
    errorCode: 500,
    messageKey: 'message',
    dataKey: 'data',
    codeKey: 'code',
    successKey: 'success'
  }
}

export default mockConfig
