/**
 * @name 合同管理数据生成器
 * @description 生成真实的合同管理相关测试数据
 * <AUTHOR>
 * @since 2025-01-24
 */

import { faker } from '@faker-js/faker/locale/zh_CN'
import { generateId } from '../utils'

// 避免导入问题，本地定义工具函数
function getRandomNumber(min: number, max: number): number {
  return faker.number.int({ min, max })
}

function getRandomItem<T>(array: T[]): T {
  const index = getRandomNumber(0, array.length - 1)
  return array[index]
}

export class ContractGenerator {
  // 合同类型
  private static contractTypes = [
    { code: 'FORMAL', name: '事业编制聘用合同' },
    { code: 'LABOR', name: '劳务派遣合同' },
    { code: 'SERVICE', name: '劳务协议' },
    { code: 'PARTTIME', name: '兼职聘用合同' },
    { code: 'PROBATION', name: '试用期合同' }
  ]

  // 岗位类别
  private static positionCategories = [
    '管理岗位',
    '专业技术岗位',
    '工勤技能岗位',
    '教师岗位',
    '科研岗位',
    '辅导员岗位'
  ]

  // 合同状态
  private static contractStatuses = [
    { code: 'DRAFT', name: '草稿' },
    { code: 'PENDING', name: '待审批' },
    { code: 'APPROVED', name: '已审批' },
    { code: 'SIGNING', name: '签署中' },
    { code: 'ACTIVE', name: '履行中' },
    { code: 'EXPIRED', name: '已到期' },
    { code: 'TERMINATED', name: '已终止' },
    { code: 'CHANGED', name: '已变更' }
  ]

  // 终止原因
  private static terminationReasons = [
    '合同期满',
    '员工主动辞职',
    '双方协商一致',
    '退休',
    '试用期不合格',
    '严重违纪',
    '组织调整',
    '健康原因'
  ]

  // 变更类型
  private static changeTypes = [
    '岗位调整',
    '薪酬调整',
    '工作地点变更',
    '合同期限变更',
    '职务变更',
    '部门调整'
  ]

  /**
   * 生成合同模板
   */
  static generateContractTemplate() {
    const contractType = getRandomItem(this.contractTypes)
    
    return {
      id: generateId(),
      templateName: `${contractType.name}模板-${new Date().getFullYear()}版`,
      templateType: contractType.code,
      templateContent: this.generateTemplateContent(contractType.code),
      versionNumber: `V${getRandomNumber(1, 3)}.${getRandomNumber(0, 9)}`,
      status: getRandomItem(['草稿', '已发布', '已废弃']),
      fieldDefinitions: this.generateFieldDefinitions(contractType.code),
      createTime: faker.date.recent({ days: 365 }).toISOString(),
      updateTime: faker.date.recent({ days: 30 }).toISOString(),
      creatorId: generateId(),
      creatorName: faker.person.fullName()
    }
  }

  /**
   * 生成合同记录
   */
  static generateContract(employeeId: string, employeeName: string) {
    const contractType = getRandomItem(this.contractTypes)
    const startDate = faker.date.recent({ days: 730 }) // 过去2年内
    const duration = getRandomItem([1, 2, 3, 5]) // 合同年限
    const endDate = new Date(startDate)
    endDate.setFullYear(endDate.getFullYear() + duration)
    
    const hasProbation = contractType.code === 'FORMAL' || contractType.code === 'PROBATION'
    const probationMonths = hasProbation ? getRandomItem([3, 6]) : 0
    
    return {
      id: generateId(),
      employeeId,
      employeeName,
      contractNumber: `HKY-${new Date().getFullYear()}-${String(getRandomNumber(1000, 9999)).padStart(4, '0')}`,
      contractType: contractType.code,
      contractTypeName: contractType.name,
      positionCategory: getRandomItem(this.positionCategories),
      employerUnit: '杭州科技职业技术学院',
      signDate: startDate.toISOString().split('T')[0],
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      probationStartDate: hasProbation ? startDate.toISOString().split('T')[0] : null,
      probationEndDate: hasProbation ? new Date(startDate.getTime() + probationMonths * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
      contractStatus: this.getContractStatus(startDate, endDate),
      contractDocumentURL: `/contracts/signed/${generateId()}.pdf`,
      // 电子签名相关
      employerSignatureURL: `/signatures/employer/${generateId()}.png`,
      employeeSignatureURL: `/signatures/employee/${generateId()}.png`,
      cloudSealData: {
        sealId: generateId(),
        sealPosition: { x: 500, y: 700 },
        sealImageURL: '/seals/school-seal.png',
        timestamp: faker.date.recent({ days: 7 }).toISOString()
      },
      // 审批信息
      approvalProcessInstanceId: generateId(),
      approvalStatus: '已通过',
      legalValidityProofURL: `/legal-proof/${generateId()}.pdf`,
      executionStatus: this.getExecutionStatus(startDate, endDate),
      // 合同条款
      contractTerms: this.generateContractTerms(contractType.code),
      createTime: faker.date.recent({ days: 730 }).toISOString(),
      creatorId: generateId(),
      creatorName: faker.person.fullName()
    }
  }

  /**
   * 生成合同变更记录
   */
  static generateContractChange(contractId: string) {
    const changeType = getRandomItem(this.changeTypes)
    
    return {
      id: generateId(),
      contractId,
      changeType,
      changeReason: `因${faker.lorem.sentence()}需要进行${changeType}`,
      changeContent: this.generateChangeContent(changeType),
      changeAgreementURL: `/change-agreements/${generateId()}.pdf`,
      changeTime: faker.date.recent({ days: 180 }).toISOString(),
      operatorId: generateId(),
      operatorName: faker.person.fullName(),
      approvalStatus: getRandomItem(['待审批', '已通过', '已拒绝']),
      approvalOpinion: faker.lorem.sentence(),
      // 变更前后对比
      beforeChange: this.generateChangeComparison(changeType).before,
      afterChange: this.generateChangeComparison(changeType).after,
      effectiveDate: faker.date.soon({ days: 30 }).toISOString().split('T')[0]
    }
  }

  /**
   * 生成续签记录
   */
  static generateRenewalRecord(contractId: string, employeeId: string) {
    return {
      id: generateId(),
      originalContractId: contractId,
      employeeId,
      renewalType: getRandomItem(['主动续签', '自动续签', '协商续签']),
      proposedStartDate: faker.date.soon({ days: 60 }).toISOString().split('T')[0],
      proposedEndDate: faker.date.soon({ days: 365 * 3 }).toISOString().split('T')[0],
      proposedTerms: {
        salary: getRandomNumber(8000, 25000),
        position: getRandomItem(this.positionCategories),
        workLocation: '杭州市'
      },
      applicationTime: faker.date.recent({ days: 30 }).toISOString(),
      // 审批流程
      departmentOpinion: {
        opinion: getRandomItem(['同意续签', '建议调整条款后续签', '不建议续签']),
        comment: faker.lorem.sentence(),
        reviewerId: generateId(),
        reviewerName: faker.person.fullName(),
        reviewTime: faker.date.recent({ days: 7 }).toISOString()
      },
      hrOpinion: {
        opinion: '同意续签',
        comment: '该员工表现优秀，建议续签',
        reviewerId: generateId(),
        reviewerName: faker.person.fullName(),
        reviewTime: faker.date.recent({ days: 3 }).toISOString()
      },
      status: getRandomItem(['待部门审核', '待人事审核', '待签署', '已完成', '已拒绝']),
      newContractId: null
    }
  }

  /**
   * 生成合同提醒
   */
  static generateContractReminder(contractId: string, employeeId: string) {
    const reminderTypes = ['到期提醒', '续签提醒', '试用期结束提醒', '年度评估提醒']
    const reminderType = getRandomItem(reminderTypes)
    
    return {
      id: generateId(),
      contractId,
      employeeId,
      reminderType,
      reminderTitle: this.getReminderTitle(reminderType),
      reminderContent: this.getReminderContent(reminderType),
      reminderTime: faker.date.soon({ days: 90 }).toISOString(),
      reminderLevel: getRandomItem(['普通', '重要', '紧急']),
      reminderDays: getRandomItem([90, 60, 30, 15, 7]),
      isRead: faker.datatype.boolean(),
      readTime: faker.datatype.boolean() ? faker.date.recent({ days: 7 }).toISOString() : null,
      isHandled: faker.datatype.boolean(),
      handleTime: faker.datatype.boolean() ? faker.date.recent({ days: 3 }).toISOString() : null,
      handleResult: faker.datatype.boolean() ? '已启动续签流程' : null
    }
  }

  /**
   * 生成合同统计数据
   */
  static generateContractStatistics() {
    return {
      // 总体统计
      totalContracts: getRandomNumber(800, 1200),
      activeContracts: getRandomNumber(700, 1000),
      expiredContracts: getRandomNumber(50, 100),
      terminatedContracts: getRandomNumber(20, 50),
      
      // 按类型统计
      byType: this.contractTypes.map(type => ({
        type: type.name,
        count: getRandomNumber(100, 300),
        percentage: Math.random() * 30 + 10
      })),
      
      // 按部门统计
      byDepartment: [
        '信息工程学院',
        '机电工程学院',
        '商贸旅游学院',
        '艺术设计学院',
        '行政部门'
      ].map(dept => ({
        department: dept,
        count: getRandomNumber(50, 150),
        activeCount: getRandomNumber(40, 130),
        expiringCount: getRandomNumber(5, 20)
      })),
      
      // 到期预警
      expiringIn30Days: getRandomNumber(10, 30),
      expiringIn60Days: getRandomNumber(20, 50),
      expiringIn90Days: getRandomNumber(30, 80),
      
      // 续签统计
      renewalRate: 85 + Math.random() * 10,
      pendingRenewals: getRandomNumber(5, 20),
      completedRenewals: getRandomNumber(50, 100),
      
      updateTime: new Date().toISOString()
    }
  }

  // 辅助方法

  private static generateTemplateContent(contractType: string) {
    const baseContent = `
      <h1>${this.contractTypes.find(t => t.code === contractType)?.name}</h1>
      <p>甲方：杭州科技职业技术学院</p>
      <p>乙方：{{employeeName}}</p>
      <p>身份证号：{{idNumber}}</p>
      
      <h2>第一条 合同期限</h2>
      <p>本合同期限自{{startDate}}起至{{endDate}}止。</p>
      
      <h2>第二条 工作内容和工作地点</h2>
      <p>乙方同意在{{department}}从事{{position}}工作。</p>
      <p>工作地点：{{workLocation}}</p>
      
      <h2>第三条 工作时间和休息休假</h2>
      <p>执行标准工时制度，每周工作40小时。</p>
      
      <h2>第四条 劳动报酬</h2>
      <p>乙方月工资为{{salary}}元。</p>
    `
    
    return baseContent
  }

  private static generateFieldDefinitions(contractType: string) {
    return [
      { fieldName: 'employeeName', fieldLabel: '员工姓名', fieldType: 'text', required: true },
      { fieldName: 'idNumber', fieldLabel: '身份证号', fieldType: 'text', required: true },
      { fieldName: 'department', fieldLabel: '部门', fieldType: 'select', required: true },
      { fieldName: 'position', fieldLabel: '岗位', fieldType: 'text', required: true },
      { fieldName: 'startDate', fieldLabel: '开始日期', fieldType: 'date', required: true },
      { fieldName: 'endDate', fieldLabel: '结束日期', fieldType: 'date', required: true },
      { fieldName: 'salary', fieldLabel: '月薪', fieldType: 'number', required: true },
      { fieldName: 'workLocation', fieldLabel: '工作地点', fieldType: 'text', required: true }
    ]
  }

  private static getContractStatus(startDate: Date, endDate: Date): string {
    const now = new Date()
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
    
    if (now < startDate) {
      return '待生效'
    } else if (now >= startDate && now <= endDate) {
      if (endDate <= thirtyDaysFromNow) {
        return '即将到期'
      }
      return '履行中'
    } else {
      return '已到期'
    }
  }

  private static getExecutionStatus(startDate: Date, endDate: Date): string {
    const now = new Date()
    
    if (now < startDate) {
      return '未开始'
    } else if (now >= startDate && now <= endDate) {
      return '进行中'
    } else {
      return '已完成'
    }
  }

  private static generateContractTerms(contractType: string) {
    return {
      salary: getRandomNumber(5000, 30000),
      benefits: ['五险一金', '带薪年假', '节日福利', '培训机会'],
      workingHours: '周一至周五 8:30-17:30',
      probationSalaryRatio: contractType === 'FORMAL' ? 0.8 : 1,
      annualLeave: getRandomItem([5, 10, 15]),
      overtime: '按国家规定支付加班费',
      confidentiality: '离职后2年内保密义务',
      competition: contractType === 'FORMAL' ? '离职后6个月竞业限制' : '无'
    }
  }

  private static generateChangeContent(changeType: string) {
    const changeContents: Record<string, any> = {
      '岗位调整': {
        oldPosition: getRandomItem(this.positionCategories),
        newPosition: getRandomItem(this.positionCategories),
        reason: '组织架构调整'
      },
      '薪酬调整': {
        oldSalary: getRandomNumber(8000, 15000),
        newSalary: getRandomNumber(10000, 20000),
        adjustmentRatio: '+' + getRandomNumber(10, 30) + '%'
      },
      '工作地点变更': {
        oldLocation: '杭州市西湖区',
        newLocation: '杭州市滨江区',
        allowance: getRandomNumber(500, 2000)
      }
    }
    
    return changeContents[changeType] || {}
  }

  private static generateChangeComparison(changeType: string) {
    const comparisons: Record<string, any> = {
      '岗位调整': {
        before: { position: '专业技术岗位', level: '中级' },
        after: { position: '管理岗位', level: '副处级' }
      },
      '薪酬调整': {
        before: { basicSalary: 10000, allowance: 2000 },
        after: { basicSalary: 12000, allowance: 3000 }
      }
    }
    
    return comparisons[changeType] || { before: {}, after: {} }
  }

  private static getReminderTitle(reminderType: string) {
    const titles: Record<string, string> = {
      '到期提醒': '您的劳动合同即将到期',
      '续签提醒': '请及时办理合同续签手续',
      '试用期结束提醒': '试用期即将结束，请准备转正材料',
      '年度评估提醒': '年度合同履行评估即将开始'
    }
    
    return titles[reminderType] || '合同提醒'
  }

  private static getReminderContent(reminderType: string) {
    const contents: Record<string, string> = {
      '到期提醒': '您的劳动合同将于{days}天后到期，请及时联系人事处办理续签或终止手续。',
      '续签提醒': '您已符合续签条件，请登录系统提交续签申请，或联系所在部门人事联络员。',
      '试用期结束提醒': '您的试用期将于{days}天后结束，请准备试用期工作总结并提交转正申请。',
      '年度评估提醒': '根据合同约定，需要进行年度履行情况评估，请配合完成相关工作。'
    }
    
    return contents[reminderType] || '请及时处理相关事宜。'
  }

  /**
   * 生成完整的合同管理测试数据集
   */
  static generateTestDataset() {
    // 生成合同模板
    const templates = Array.from({ length: 5 }, () => this.generateContractTemplate())
    
    // 获取员工数据（假设从员工服务获取）
    const employees = Array.from({ length: 100 }, (_, i) => ({
      id: `E${String(1000 + i).padStart(5, '0')}`,
      name: faker.person.fullName()
    }))
    
    // 为每个员工生成合同
    const contracts: any[] = []
    const contractChanges: any[] = []
    const renewalRecords: any[] = []
    const reminders: any[] = []
    
    employees.forEach(emp => {
      const contract = this.generateContract(emp.id, emp.name)
      contracts.push(contract)
      
      // 20%的合同有变更记录
      if (Math.random() > 0.8) {
        const change = this.generateContractChange(contract.id)
        contractChanges.push(change)
      }
      
      // 即将到期的合同生成续签记录
      if (contract.contractStatus === '即将到期' && Math.random() > 0.5) {
        const renewal = this.generateRenewalRecord(contract.id, emp.id)
        renewalRecords.push(renewal)
      }
      
      // 生成提醒
      if (contract.contractStatus === '即将到期' || contract.contractStatus === '履行中') {
        const reminder = this.generateContractReminder(contract.id, emp.id)
        reminders.push(reminder)
      }
    })
    
    // 生成统计数据
    const statistics = this.generateContractStatistics()
    
    return {
      contractTemplates: templates,
      contracts,
      contractChanges,
      renewalRecords,
      contractReminders: reminders,
      contractStatistics: statistics
    }
  }
}