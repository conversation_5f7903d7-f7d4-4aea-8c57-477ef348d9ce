import { faker } from '@faker-js/faker'

/**
 * 生成器选项接口
 */
export interface GeneratorOptions {
  count?: number
  includeTimestamps?: boolean
  dateRange?: {
    start: Date
    end: Date
  }
  customFields?: Record<string, any>
}

/**
 * 基础数据生成器接口
 */
export interface BaseGeneratorInterface<T extends Record<string, any>> {
  generateOne(index: number): T
  generate(): T[]
  generateMany(count: number): T[]
  generateRandomDate(startDate?: Date): Date
  generateId(): string
  generateSequentialId(prefix: string, index: number): string
  randomChoice<U>(array: U[]): U
  randomChoices<U>(array: U[], min: number, max: number): U[]
  generatePhoneNumber(): string
  generateEmail(name?: string): string
  generateIdCard(): string
  generateBankCard(): string
  generateAddress(): {
    province: string
    city: string
    district: string
    street: string
    fullAddress: string
  }
  generateChineseName(): string
  generateAmount(min: number, max: number, decimals?: number): number
  generatePercentage(min?: number, max?: number): number
  generateRating(min?: number, max?: number, step?: number): number
  generateTags(tagPool: string[], min?: number, max?: number): string[]
  generateStatus<S>(statusMap: Record<string, number>): S
}

/**
 * 基础生成器上下文
 */
export interface BaseGeneratorContext {
  faker: typeof faker
  options: GeneratorOptions
}

/**
 * 创建基础数据生成器
 */
export function createBaseGenerator<T extends Record<string, any>>(
  generateOneImpl: (index: number, context: BaseGeneratorContext) => T,
  options: GeneratorOptions = {}
): BaseGeneratorInterface<T> {
  const actualOptions: GeneratorOptions = {
    count: 10,
    includeTimestamps: true,
    dateRange: {
      start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // 一年前
      end: new Date()
    },
    ...options
  }

  const context: BaseGeneratorContext = {
    faker,
    options: actualOptions
  }

  // 辅助函数
  function randomBoolean(probability: number = 0.5): boolean {
    return Math.random() < probability
  }

  return {
    generateOne(index: number): T {
      return generateOneImpl(index, context)
    },

    generate(): T[] {
      const results: T[] = []
      const count = actualOptions.count || 10

      for (let i = 0; i < count; i++) {
        const item = this.generateOne(i)

        // 添加时间戳
        if (actualOptions.includeTimestamps) {
          const createTime = this.generateRandomDate()
          const updateTime = randomBoolean(0.3) ? this.generateRandomDate(createTime) : createTime

          Object.assign(item, {
            createTime: createTime.toISOString(),
            updateTime: updateTime.toISOString()
          })
        }

        // 添加自定义字段
        if (actualOptions.customFields) {
          Object.assign(item, actualOptions.customFields)
        }

        results.push(item)
      }

      return results
    },

    generateMany(count: number): T[] {
      const oldCount = actualOptions.count
      actualOptions.count = count
      const results = this.generate()
      actualOptions.count = oldCount
      return results
    },

    /**
     * 生成随机日期
     */
    generateRandomDate(startDate?: Date): Date {
      const start =
        startDate ||
        actualOptions.dateRange?.start ||
        new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
      const end = actualOptions.dateRange?.end || new Date()
      return faker.date.between({ from: start, to: end })
    },

    /**
     * 生成唯一ID
     */
    generateId(): string {
      return faker.string.uuid()
    },

    /**
     * 生成序列ID
     */
    generateSequentialId(prefix: string, index: number): string {
      return `${prefix}_${String(index + 1).padStart(6, '0')}`
    },

    /**
     * 随机选择一个元素
     */
    randomChoice<U>(array: U[]): U {
      return faker.helpers.arrayElement(array)
    },

    /**
     * 随机选择多个元素
     */
    randomChoices<U>(array: U[], min: number, max: number): U[] {
      const count = faker.number.int({ min, max })
      return faker.helpers.arrayElements(array, count)
    },

    /**
     * 生成手机号码
     */
    generatePhoneNumber(): string {
      const prefixes = [
        '130',
        '131',
        '132',
        '133',
        '134',
        '135',
        '136',
        '137',
        '138',
        '139',
        '150',
        '151',
        '152',
        '153',
        '155',
        '156',
        '157',
        '158',
        '159',
        '180',
        '181',
        '182',
        '183',
        '184',
        '185',
        '186',
        '187',
        '188',
        '189'
      ]
      const prefix = faker.helpers.arrayElement(prefixes)
      const suffix = faker.number.int({ min: 10000000, max: 99999999 })
      return `${prefix}${suffix}`
    },

    /**
     * 生成邮箱地址
     */
    generateEmail(name?: string): string {
      const username = name || faker.internet.userName()
      const domains = ['gmail.com', 'qq.com', '163.com', 'sina.com', 'hky.edu.cn']
      const domain = faker.helpers.arrayElement(domains)
      return `${username}@${domain}`
    },

    /**
     * 生成身份证号码
     */
    generateIdCard(): string {
      const areaCode = faker.helpers.arrayElement(['330100', '330200', '330300', '330400'])
      const birthDate = faker.date.birthdate({ min: 18, max: 65, mode: 'age' })
      const year = birthDate.getFullYear()
      const month = String(birthDate.getMonth() + 1).padStart(2, '0')
      const day = String(birthDate.getDate()).padStart(2, '0')
      const sequence = faker.number.int({ min: 100, max: 999 })
      const checksum = faker.number.int({ min: 0, max: 9 })
      return `${areaCode}${year}${month}${day}${sequence}${checksum}`
    },

    /**
     * 生成银行卡号
     */
    generateBankCard(): string {
      const prefix = faker.helpers.arrayElement(['6225', '6227', '6228', '6229'])
      const middle = faker.number.int({ min: ************, max: ************ })
      return `${prefix}${middle}`
    },

    /**
     * 生成地址信息
     */
    generateAddress(): {
      province: string
      city: string
      district: string
      street: string
      fullAddress: string
    } {
      const provinces = ['浙江省', '江苏省', '上海市', '安徽省', '福建省']
      const cities = [
        '杭州市',
        '宁波市',
        '温州市',
        '嘉兴市',
        '湖州市',
        '绍兴市',
        '金华市',
        '衢州市',
        '舟山市',
        '台州市',
        '丽水市'
      ]
      const districts = [
        '西湖区',
        '拱墅区',
        '江干区',
        '下城区',
        '上城区',
        '滨江区',
        '余杭区',
        '萧山区'
      ]
      const streets = [
        '文一路',
        '文二路',
        '文三路',
        '学院路',
        '教工路',
        '天目山路',
        '古墩路',
        '莫干山路'
      ]

      const province = faker.helpers.arrayElement(provinces)
      const city = faker.helpers.arrayElement(cities)
      const district = faker.helpers.arrayElement(districts)
      const street = faker.helpers.arrayElement(streets)
      const number = faker.number.int({ min: 1, max: 999 })

      return {
        province,
        city,
        district,
        street,
        fullAddress: `${province}${city}${district}${street}${number}号`
      }
    },

    /**
     * 生成中文姓名
     */
    generateChineseName(): string {
      const surnames = [
        '王',
        '李',
        '张',
        '刘',
        '陈',
        '杨',
        '赵',
        '黄',
        '周',
        '吴',
        '徐',
        '孙',
        '胡',
        '朱',
        '高',
        '林',
        '何',
        '郭',
        '马',
        '罗'
      ]
      const givenNames = [
        '伟',
        '芳',
        '娜',
        '秀英',
        '敏',
        '静',
        '丽',
        '强',
        '磊',
        '军',
        '洋',
        '勇',
        '艳',
        '杰',
        '娟',
        '涛',
        '明',
        '超',
        '秀兰',
        '霞'
      ]

      const surname = faker.helpers.arrayElement(surnames)
      const givenName = faker.helpers.arrayElement(givenNames)
      return `${surname}${givenName}`
    },

    /**
     * 生成金额
     */
    generateAmount(min: number, max: number, decimals: number = 2): number {
      const amount = faker.number.int({ min: min * 100, max: max * 100 }) / 100
      return Number(amount.toFixed(decimals))
    },

    /**
     * 生成百分比
     */
    generatePercentage(min: number = 0, max: number = 100): number {
      return faker.number.int({ min, max })
    },

    /**
     * 生成评分
     */
    generateRating(min: number = 1, max: number = 5, step: number = 0.1): number {
      const range = max - min
      const steps = Math.floor(range / step)
      const randomStep = faker.number.int({ min: 0, max: steps })
      return Number((min + randomStep * step).toFixed(1))
    },

    /**
     * 生成标签
     */
    generateTags(tagPool: string[], min: number = 1, max: number = 3): string[] {
      const count = faker.number.int({ min, max })
      return faker.helpers.arrayElements(tagPool, count)
    },

    /**
     * 根据权重生成状态
     */
    generateStatus<S>(statusMap: Record<string, number>): S {
      const statuses = Object.keys(statusMap)
      const weights = Object.values(statusMap)
      const totalWeight = weights.reduce((sum, weight) => sum + weight, 0)

      let random = Math.random() * totalWeight
      for (let i = 0; i < statuses.length; i++) {
        random -= weights[i]
        if (random <= 0) {
          return statuses[i] as S
        }
      }

      return statuses[statuses.length - 1] as S
    }
  }
}

// 为了向后兼容，保留原有的抽象类定义
export abstract class BaseGenerator<T extends Record<string, any>> {
  protected faker = faker
  protected options: GeneratorOptions

  constructor(options: GeneratorOptions = {}) {
    this.options = {
      count: 10,
      includeTimestamps: true,
      dateRange: {
        start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // 一年前
        end: new Date()
      },
      ...options
    }
  }

  /**
   * 生成单条数据（子类必须实现）
   */
  abstract generateOne(index: number): T

  /**
   * 生成多条数据
   */
  generate(): T[] {
    const results: T[] = []
    const count = this.options.count || 10

    for (let i = 0; i < count; i++) {
      const item = this.generateOne(i)

      // 添加时间戳
      if (this.options.includeTimestamps) {
        const createTime = this.generateRandomDate()
        const updateTime = this.randomBoolean(0.3)
          ? this.generateRandomDate(createTime)
          : createTime

        Object.assign(item, {
          createTime: createTime.toISOString(),
          updateTime: updateTime.toISOString()
        })
      }

      // 添加自定义字段
      if (this.options.customFields) {
        Object.assign(item, this.options.customFields)
      }

      results.push(item)
    }

    return results
  }

  /**
   * 生成指定数量的数据
   */
  generateMany(count: number): T[] {
    const oldCount = this.options.count
    this.options.count = count
    const results = this.generate()
    this.options.count = oldCount
    return results
  }

  /**
   * 生成随机日期
   */
  generateRandomDate(startDate?: Date): Date {
    const start =
      startDate || this.options.dateRange?.start || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
    const end = this.options.dateRange?.end || new Date()
    return faker.date.between({ from: start, to: end })
  }

  /**
   * 生成唯一ID
   */
  generateId(): string {
    return faker.string.uuid()
  }

  /**
   * 生成序列ID
   */
  generateSequentialId(prefix: string, index: number): string {
    return `${prefix}_${String(index + 1).padStart(6, '0')}`
  }

  /**
   * 随机选择一个元素
   */
  randomChoice<U>(array: U[]): U {
    return faker.helpers.arrayElement(array)
  }

  /**
   * 随机选择多个元素
   */
  randomChoices<U>(array: U[], min: number, max: number): U[] {
    const count = faker.number.int({ min, max })
    return faker.helpers.arrayElements(array, count)
  }

  /**
   * 生成手机号码
   */
  generatePhoneNumber(): string {
    const prefixes = [
      '130',
      '131',
      '132',
      '133',
      '134',
      '135',
      '136',
      '137',
      '138',
      '139',
      '150',
      '151',
      '152',
      '153',
      '155',
      '156',
      '157',
      '158',
      '159',
      '180',
      '181',
      '182',
      '183',
      '184',
      '185',
      '186',
      '187',
      '188',
      '189'
    ]
    const prefix = faker.helpers.arrayElement(prefixes)
    const suffix = faker.number.int({ min: 10000000, max: 99999999 })
    return `${prefix}${suffix}`
  }

  /**
   * 生成邮箱地址
   */
  generateEmail(name?: string): string {
    const username = name || faker.internet.userName()
    const domains = ['gmail.com', 'qq.com', '163.com', 'sina.com', 'hky.edu.cn']
    const domain = faker.helpers.arrayElement(domains)
    return `${username}@${domain}`
  }

  /**
   * 生成身份证号码
   */
  generateIdCard(): string {
    const areaCode = faker.helpers.arrayElement(['330100', '330200', '330300', '330400'])
    const birthDate = faker.date.birthdate({ min: 18, max: 65, mode: 'age' })
    const year = birthDate.getFullYear()
    const month = String(birthDate.getMonth() + 1).padStart(2, '0')
    const day = String(birthDate.getDate()).padStart(2, '0')
    const sequence = faker.number.int({ min: 100, max: 999 })
    const checksum = faker.number.int({ min: 0, max: 9 })
    return `${areaCode}${year}${month}${day}${sequence}${checksum}`
  }

  /**
   * 生成银行卡号
   */
  generateBankCard(): string {
    const prefix = faker.helpers.arrayElement(['6225', '6227', '6228', '6229'])
    const middle = faker.number.int({ min: ************, max: ************ })
    return `${prefix}${middle}`
  }

  /**
   * 随机布尔值
   */
  protected randomBoolean(probability: number = 0.5): boolean {
    return Math.random() < probability
  }
}
