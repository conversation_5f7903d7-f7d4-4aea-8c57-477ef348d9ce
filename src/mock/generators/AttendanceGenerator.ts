/**
 * @name 考勤数据生成器
 * @description 生成真实的考勤相关测试数据
 * <AUTHOR>
 * @since 2025-01-24
 */

import { faker } from '@faker-js/faker/locale/zh_CN'
import { generateId } from '../utils'

// 临时定义工具函数，避免导入问题
function getRandomNumber(min: number, max: number): number {
  return faker.number.int({ min, max })
}

function getRandomItem<T>(array: T[]): T {
  const index = getRandomNumber(0, array.length - 1)
  return array[index]
}

export class AttendanceGenerator {
  // 部门列表
  private static departments = [
    '信息工程学院',
    '机电工程学院',
    '商贸旅游学院',
    '艺术设计学院',
    '建筑工程学院',
    '人事处',
    '教务处',
    '财务处',
    '学生处',
    '后勤处'
  ]

  // 考勤状态 - 在 getAttendanceStatus 方法中使用
  private static attendanceStatuses = ['正常', '迟到', '早退', '缺勤', '请假', '出差', '外出']
  
  // 请假类型
  private static leaveTypes = ['事假', '病假', '年假', '婚假', '产假', '陪产假', '丧假', '探亲假', '调休']
  
  // 数据来源
  private static dataSources = ['考勤机', '移动APP', '人脸识别', '门禁系统', '补录']
  
  // 异常类型
  private static exceptionTypes = ['迟到', '早退', '旷工', '忘记打卡', '设备故障']

  /**
   * 生成员工考勤记录
   */
  static generateAttendanceRecord(employeeId: string, date: Date) {
    const dateStr = date.toISOString().split('T')[0]
    const isWeekend = date.getDay() % 6 === 0
    const status = this.getAttendanceStatus(isWeekend)
    
    // 根据状态生成打卡时间
    const times = this.generateCheckTimes(status, isWeekend)
    
    return {
      id: generateId(),
      employeeId,
      employeeName: faker.person.fullName(),
      department: getRandomItem(this.departments),
      date: dateStr,
      checkInTime: times.checkInTime,
      checkOutTime: times.checkOutTime,
      attendanceType: isWeekend ? '加班' : '正常工作日',
      status,
      workHours: times.workHours,
      overtimeHours: isWeekend ? times.workHours : 0,
      dataSource: getRandomItem(this.dataSources),
      isNonWorkdayAttendance: isWeekend,
      remarks: this.generateRemarks(status),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * 生成批量考勤记录
   */
  static generateBatchAttendanceRecords(employeeIds: string[], startDate: Date, endDate: Date) {
    const records: ReturnType<typeof AttendanceGenerator.generateAttendanceRecord>[] = []
    const currentDate = new Date(startDate)
    
    while (currentDate <= endDate) {
      employeeIds.forEach(employeeId => {
        // 90%的概率有考勤记录
        if (Math.random() > 0.1) {
          records.push(this.generateAttendanceRecord(employeeId, new Date(currentDate)))
        }
      })
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    return records
  }

  /**
   * 生成请假申请
   */
  static generateLeaveApplication(employeeId: string) {
    const leaveType = getRandomItem(this.leaveTypes)
    const days = this.getLeaveDefaultDays(leaveType)
    const startDate = faker.date.between({ 
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 
      to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) 
    })
    const endDate = new Date(startDate.getTime() + days * 24 * 60 * 60 * 1000)
    
    return {
      id: generateId(),
      employeeId,
      employeeName: faker.person.fullName(),
      department: getRandomItem(this.departments),
      leaveType,
      reason: this.generateLeaveReason(leaveType),
      startTime: startDate.toISOString(),
      endTime: endDate.toISOString(),
      calculatedDays: days,
      isOverseasTravel: leaveType === '探亲假' && Math.random() > 0.7,
      courseAdjustmentInfo: Math.random() > 0.5 ? '已安排代课教师' : '',
      status: '待审批',
      currentApproverId: generateId(),
      approvalHistory: [],
      attachmentUrls: this.generateAttachmentUrls(leaveType),
      submissionTime: new Date().toISOString()
    }
  }

  /**
   * 生成异常考勤记录
   */
  static generateAttendanceException(employeeId: string) {
    const exceptionType = getRandomItem(this.exceptionTypes)
    
    return {
      id: generateId(),
      employeeId,
      employeeName: faker.person.fullName(),
      department: getRandomItem(this.departments),
      attendanceDate: faker.date.recent({ days: 30 }).toISOString().split('T')[0],
      exceptionType,
      description: this.generateExceptionDescription(exceptionType),
      declarationTime: new Date().toISOString(),
      declarerId: Math.random() > 0.5 ? employeeId : generateId(),
      declarerName: faker.person.fullName(),
      processStatus: '待处理',
      processingOpinion: null,
      processorId: null,
      processorName: null,
      processTime: null
    }
  }

  /**
   * 生成加班记录
   */
  static generateOvertimeRecord(employeeId: string) {
    const overtimeDate = faker.date.recent({ days: 30 })
    const isWeekend = overtimeDate.getDay() % 6 === 0
    const hours = isWeekend ? getRandomNumber(4, 8) : getRandomNumber(2, 4)
    
    return {
      id: generateId(),
      employeeId,
      employeeName: faker.person.fullName(),
      department: getRandomItem(this.departments),
      overtimeDate: overtimeDate.toISOString().split('T')[0],
      overtimeType: isWeekend ? '周末加班' : '工作日加班',
      startTime: isWeekend ? '09:00:00' : '18:30:00',
      endTime: isWeekend ? `${9 + hours}:00:00` : `${18.5 + hours}:30:00`,
      hours,
      reason: this.generateOvertimeReason(),
      approvalStatus: '已批准',
      compensationType: Math.random() > 0.5 ? '调休' : '加班费',
      createdAt: new Date().toISOString()
    }
  }

  /**
   * 生成月度考勤汇总
   */
  static generateMonthlyAttendanceSummary(employeeId: string, month: string) {
    const workDays = 22
    const actualDays = getRandomNumber(18, 22)
    const lateDays = getRandomNumber(0, 3)
    const earlyLeaveDays = getRandomNumber(0, 2)
    const leaveDays = workDays - actualDays
    
    return {
      employeeId,
      employeeName: faker.person.fullName(),
      department: getRandomItem(this.departments),
      month,
      workDays,
      actualDays,
      lateDays,
      earlyLeaveDays,
      absentDays: Math.max(0, workDays - actualDays - leaveDays),
      leaveDays,
      overtimeHours: getRandomNumber(0, 40),
      attendanceRate: Number(((actualDays / workDays) * 100).toFixed(1))
    }
  }

  // 辅助方法

  private static getAttendanceStatus(isWeekend: boolean): string {
    if (isWeekend) {
      return Math.random() > 0.5 ? '正常' : '加班'
    }
    
    // 使用 attendanceStatuses 数组，根据概率分布返回状态
    const random = Math.random()
    if (random < 0.7) return this.attendanceStatuses[0] // 正常
    if (random < 0.8) return this.attendanceStatuses[1] // 迟到
    if (random < 0.85) return this.attendanceStatuses[2] // 早退
    if (random < 0.9) return this.attendanceStatuses[4] // 请假
    if (random < 0.95) return this.attendanceStatuses[5] // 出差
    return this.attendanceStatuses[3] // 缺勤
  }

  private static generateCheckTimes(status: string, isWeekend: boolean) {
    let checkInTime: string | null = null
    let checkOutTime: string | null = null
    let workHours = 0
    
    // 周末加班时间可能不同
    const baseStartHour = isWeekend ? 9 : 8
    const baseEndHour = isWeekend ? 17 : 18
    
    switch (status) {
      case '正常':
      case '加班':
        checkInTime = `${baseStartHour.toString().padStart(2, '0')}:${getRandomNumber(30, 55)}:${getRandomNumber(10, 59).toString().padStart(2, '0')}`
        checkOutTime = `${baseEndHour}:${getRandomNumber(0, 30)}:${getRandomNumber(10, 59).toString().padStart(2, '0')}`
        workHours = isWeekend ? 8 : 8.5
        break
      case '迟到':
        checkInTime = `${(baseStartHour + 1).toString().padStart(2, '0')}:${getRandomNumber(10, 30)}:${getRandomNumber(10, 59).toString().padStart(2, '0')}`
        checkOutTime = `${baseEndHour}:${getRandomNumber(0, 30)}:${getRandomNumber(10, 59).toString().padStart(2, '0')}`
        workHours = 7.5
        break
      case '早退':
        checkInTime = `${baseStartHour.toString().padStart(2, '0')}:${getRandomNumber(30, 55)}:${getRandomNumber(10, 59).toString().padStart(2, '0')}`
        checkOutTime = `${baseEndHour - 1}:${getRandomNumber(0, 30)}:${getRandomNumber(10, 59).toString().padStart(2, '0')}`
        workHours = 7.5
        break
      case '出差':
        workHours = 8
        break
    }
    
    return { checkInTime, checkOutTime, workHours }
  }

  private static generateRemarks(status: string): string {
    const remarks: Record<string, string[]> = {
      '缺勤': ['未请假缺勤', '无故缺勤'],
      '出差': ['参加培训', '客户拜访', '项目实施'],
      '外出': ['外出办事', '参加会议']
    }
    
    return remarks[status] ? getRandomItem(remarks[status]) : ''
  }

  private static getLeaveDefaultDays(leaveType: string): number {
    const daysMap: Record<string, [number, number]> = {
      '事假': [1, 5],
      '病假': [1, 7],
      '年假': [3, 10],
      '婚假': [13, 13],
      '产假': [158, 188],
      '陪产假': [15, 15],
      '丧假': [1, 3],
      '探亲假': [10, 20],
      '调休': [1, 3]
    }
    
    const range = daysMap[leaveType] || [1, 3]
    return getRandomNumber(range[0], range[1])
  }

  private static generateLeaveReason(leaveType: string): string {
    const reasons: Record<string, string[]> = {
      '事假': ['家中有急事需要处理', '办理个人重要事务', '参加朋友婚礼', '处理房产相关事宜'],
      '病假': ['身体不适，需要休息', '感冒发烧，医生建议休息', '医院检查和治疗', '手术后恢复'],
      '年假': ['计划外出旅游', '回老家探望父母', '处理家庭事务', '个人休整'],
      '婚假': ['本人结婚', '筹备婚礼事宜'],
      '产假': ['预产期临近', '产后休养'],
      '陪产假': ['妻子生产，需要陪护'],
      '丧假': ['家中亲人去世', '参加亲属葬礼'],
      '探亲假': ['探望异地配偶', '探望父母'],
      '调休': ['之前加班较多，申请调休', '项目结束后调休']
    }
    
    return getRandomItem(reasons[leaveType] || ['个人原因'])
  }

  private static generateAttachmentUrls(leaveType: string): string[] {
    const attachments: Record<string, string[]> = {
      '病假': ['/mock-files/medical-certificate.pdf', '/mock-files/hospital-record.jpg'],
      '婚假': ['/mock-files/marriage-certificate.pdf'],
      '产假': ['/mock-files/birth-certificate.pdf', '/mock-files/medical-record.pdf'],
      '丧假': ['/mock-files/death-certificate.pdf'],
      '探亲假': ['/mock-files/household-register.pdf', '/mock-files/relationship-proof.pdf']
    }
    
    return attachments[leaveType] || []
  }

  private static generateExceptionDescription(exceptionType: string): string {
    const descriptions: Record<string, string[]> = {
      '迟到': ['路上堵车严重', '家中有急事耽误', '公交车晚点', '天气原因导致出行困难'],
      '早退': ['需要去医院看病', '家中有急事需要处理', '参加重要会议'],
      '旷工': ['未及时请假', '通讯故障未能联系', '个人原因'],
      '忘记打卡': ['工作太忙忘记打卡', '考勤机故障', '赶着开会忘记打卡'],
      '设备故障': ['考勤机无法识别', '系统显示异常', '刷卡无反应']
    }
    
    return getRandomItem(descriptions[exceptionType] || ['异常情况'])
  }

  private static generateOvertimeReason(): string {
    const reasons = [
      '项目紧急需求',
      '系统升级维护',
      '客户需求变更',
      '月度报表制作',
      '年终总结准备',
      '紧急会议筹备',
      '重要文件处理'
    ]
    
    return getRandomItem(reasons)
  }

  /**
   * 生成考勤测试数据集
   */
  static generateTestDataset() {
    const employeeIds = Array.from({ length: 50 }, (_, i) => `E${String(1000 + i).padStart(5, '0')}`)
    const startDate = new Date()
    startDate.setMonth(startDate.getMonth() - 3) // 3个月前
    const endDate = new Date()
    
    return {
      // 考勤记录
      attendanceRecords: this.generateBatchAttendanceRecords(
        employeeIds.slice(0, 20), 
        startDate, 
        endDate
      ),
      
      // 请假申请
      leaveApplications: employeeIds.slice(0, 30).map(id => 
        this.generateLeaveApplication(id)
      ),
      
      // 异常考勤
      attendanceExceptions: Array.from({ length: 50 }, () => 
        this.generateAttendanceException(getRandomItem(employeeIds))
      ),
      
      // 加班记录
      overtimeRecords: Array.from({ length: 100 }, () => 
        this.generateOvertimeRecord(getRandomItem(employeeIds))
      ),
      
      // 月度汇总
      monthlySummaries: employeeIds.map(id => 
        this.generateMonthlyAttendanceSummary(
          id, 
          new Date().toISOString().slice(0, 7)
        )
      )
    }
  }
}