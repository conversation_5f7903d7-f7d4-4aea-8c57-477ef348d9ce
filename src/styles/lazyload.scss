/**
 * 图片懒加载样式
 */

// 加载中状态
.lazy-loading {
  opacity: 0;
  transition: opacity 0.3s;

  // 加载中动画
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--el-color-primary);
    border-radius: 50%;
    animation: lazy-spin 1s linear infinite;
  }

  // 背景颜色
  background-color: #f5f7fa;
}

// 加载完成状态
.lazy-loaded {
  opacity: 1;
  transition: opacity 0.3s;
  animation: lazy-fade-in 0.5s;
}

// 加载失败状态
.lazy-error {
  opacity: 1;
  position: relative;
  background-color: #f5f7fa;

  // 错误图标
  &::after {
    content: '⚠';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #f56c6c;
    font-size: 24px;
  }
}

// 加载动画
@keyframes lazy-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 淡入动画
@keyframes lazy-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式图片容器
.lazy-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f5f7fa;

  // 保持宽高比
  &[data-aspect-ratio='16:9'] {
    padding-bottom: 56.25%;
  }

  &[data-aspect-ratio='4:3'] {
    padding-bottom: 75%;
  }

  &[data-aspect-ratio='1:1'] {
    padding-bottom: 100%;
  }

  // 图片绝对定位
  img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 模糊效果
.lazy-blur {
  filter: blur(20px);
  transition: filter 0.3s;

  &.lazy-loaded {
    filter: blur(0);
  }
}

// 渐进式加载
.lazy-progressive {
  background-size: cover;
  background-position: center;
  filter: blur(5px);
  transition: filter 0.5s;

  &.lazy-loaded {
    filter: blur(0);
  }
}

// 骨架屏效果
.lazy-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: lazy-skeleton 1.5s infinite;
}

@keyframes lazy-skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 暗色主题适配
.dark {
  .lazy-loading {
    background-color: #1f2937;

    &::before {
      border-color: #374151;
      border-top-color: var(--el-color-primary);
    }
  }

  .lazy-error {
    background-color: #1f2937;
  }

  .lazy-image-container,
  .lazy-skeleton {
    background-color: #1f2937;
  }

  .lazy-skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }
}

// 移动端优化
@media (max-width: 768px) {
  // 减小加载动画尺寸
  .lazy-loading::before {
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
  }

  // 减少动画时间
  .lazy-loaded {
    animation-duration: 0.3s;
  }
}

// 打印样式
@media print {
  .lazy-loading,
  .lazy-error {
    display: none;
  }
}
