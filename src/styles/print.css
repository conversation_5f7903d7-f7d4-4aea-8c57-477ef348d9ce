/* 打印样式优化 - HR管理系统 */

/* 基础打印样式重置 */
@media print {
  /* 页面设置 */
  @page {
    size: A4;
    margin: 2cm;
    background: white;

    /* 页眉页脚 */
    @top-left {
      content: '杭科院人事综合管理系统';
      font-size: 10pt;
      color: #666;
    }

    @top-right {
      content: '打印时间: ' counter(page);
      font-size: 10pt;
      color: #666;
    }

    @bottom-center {
      content: '第 ' counter(page) ' 页，共 ' counter(pages) ' 页';
      font-size: 10pt;
      color: #666;
    }
  }

  /* 针对特殊尺寸的页面设置 */
  @page :first {
    margin-top: 3cm;
  }

  @page landscape {
    size: A4 landscape;
    margin: 1.5cm;
  }

  @page legal {
    size: legal;
    margin: 2.5cm;
  }

  /* 全局打印样式 */
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  html {
    font-size: 12pt;
    line-height: 1.4;
  }

  body {
    margin: 0;
    padding: 0;
    background: white !important;
    color: black !important;
    font-family: 'SimSun', '宋体', serif;
  }

  /* 隐藏不需要打印的元素 */
  .no-print,
  .print-hidden,
  .sidebar,
  .navbar,
  .toolbar,
  .floating-button,
  .action-buttons,
  .pagination,
  .el-pagination,
  .el-drawer,
  .el-dialog,
  .el-message,
  .el-loading-mask,
  .back-to-top,
  nav,
  .nav,
  header,
  footer,
  .header,
  .footer,
  .menu,
  .breadcrumb,
  button:not(.print-button),
  .el-button:not(.print-button),
  .operation-column,
  .action-column,
  input[type='file'],
  .upload-area,
  .filter-section,
  .search-section:not(.print-search) {
    display: none !important;
  }

  /* 主要内容区域 */
  .main-content,
  .print-content,
  .content,
  .page-content {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    background: white !important;
    box-shadow: none !important;
    border: none !important;
  }

  /* 标题样式 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: black !important;
    page-break-after: avoid;
    margin-top: 20pt;
    margin-bottom: 10pt;
    font-weight: bold;
  }

  h1 {
    font-size: 18pt;
    text-align: center;
    border-bottom: 2pt solid black;
    padding-bottom: 8pt;
  }

  h2 {
    font-size: 16pt;
    border-bottom: 1pt solid #666;
    padding-bottom: 4pt;
  }

  h3 {
    font-size: 14pt;
  }

  h4,
  h5,
  h6 {
    font-size: 12pt;
  }

  /* 段落和文本 */
  p {
    margin: 6pt 0;
    text-align: justify;
    text-indent: 2em;
  }

  .no-indent {
    text-indent: 0 !important;
  }

  /* 表格样式 */
  table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin: 10pt 0 !important;
    background: white !important;
    font-size: 10pt !important;
  }

  .el-table,
  .el-table__inner-wrapper {
    background: white !important;
  }

  .el-table .el-table__header-wrapper {
    background: #f5f5f5 !important;
  }

  th,
  td,
  .el-table th,
  .el-table td {
    border: 1pt solid black !important;
    padding: 6pt 8pt !important;
    background: white !important;
    color: black !important;
    text-align: left !important;
    vertical-align: top !important;
    word-wrap: break-word !important;
    font-size: 9pt !important;
    line-height: 1.3 !important;
  }

  th,
  .el-table th {
    background: #f0f0f0 !important;
    font-weight: bold !important;
    text-align: center !important;
  }

  /* 表格分页处理 */
  .el-table .el-table__body-wrapper {
    overflow: visible !important;
  }

  tr {
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tfoot {
    display: table-footer-group;
  }

  /* 列表样式 */
  ul,
  ol {
    margin: 8pt 0;
    padding-left: 20pt;
  }

  li {
    margin: 2pt 0;
  }

  /* 表单样式 */
  .form-container,
  .el-form {
    background: white !important;
    border: none !important;
    box-shadow: none !important;
  }

  .form-item,
  .el-form-item {
    margin-bottom: 8pt !important;
    page-break-inside: avoid;
  }

  .form-label,
  .el-form-item__label {
    font-weight: bold !important;
    color: black !important;
    width: auto !important;
    margin-right: 10pt !important;
  }

  .form-content,
  .el-form-item__content {
    color: black !important;
  }

  /* 输入框在打印时显示为文本 */
  input,
  textarea,
  select,
  .el-input__inner,
  .el-textarea__inner,
  .el-select {
    border: none !important;
    background: transparent !important;
    color: black !important;
    font-size: 10pt !important;
    padding: 0 !important;
  }

  /* 选择框显示选中值 */
  .el-select .el-input__inner::after {
    content: attr(value);
  }

  /* 卡片和面板 */
  .card,
  .panel,
  .el-card,
  .el-collapse {
    border: 1pt solid #ccc !important;
    background: white !important;
    margin: 8pt 0 !important;
    page-break-inside: avoid;
  }

  .card-header,
  .panel-header,
  .el-card__header {
    background: #f5f5f5 !important;
    border-bottom: 1pt solid #ccc !important;
    font-weight: bold !important;
    padding: 8pt !important;
  }

  .card-body,
  .panel-body,
  .el-card__body {
    padding: 10pt !important;
  }

  /* 图表处理 */
  .chart-container,
  .echarts-container,
  canvas {
    max-width: 100% !important;
    height: auto !important;
    page-break-inside: avoid;
  }

  /* 分页控制 */
  .page-break-before {
    page-break-before: always;
  }

  .page-break-after {
    page-break-after: always;
  }

  .page-break-inside-avoid {
    page-break-inside: avoid;
  }

  .keep-together {
    page-break-inside: avoid;
  }

  /* 员工信息打印样式 */
  .employee-profile {
    display: flex;
    flex-direction: column;
    gap: 15pt;
  }

  .employee-header {
    text-align: center;
    border-bottom: 2pt solid black;
    padding-bottom: 10pt;
    margin-bottom: 15pt;
  }

  .employee-photo {
    width: 2cm !important;
    height: 2.5cm !important;
    border: 1pt solid black;
    float: right;
    margin-left: 10pt;
  }

  .employee-basic-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8pt;
  }

  .info-row {
    display: flex;
    align-items: center;
  }

  .info-label {
    width: 80pt;
    font-weight: bold;
  }

  .info-value {
    flex: 1;
    border-bottom: 1pt dotted #666;
    padding-bottom: 2pt;
  }

  /* 考勤记录打印样式 */
  .attendance-summary {
    background: #f9f9f9 !important;
    border: 1pt solid black;
    padding: 10pt;
    margin-bottom: 15pt;
  }

  .attendance-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10pt;
    text-align: center;
  }

  .stat-item {
    border-right: 1pt solid #ccc;
  }

  .stat-item:last-child {
    border-right: none;
  }

  .stat-value {
    font-size: 14pt;
    font-weight: bold;
    color: black !important;
  }

  .stat-label {
    font-size: 8pt;
    color: #666 !important;
  }

  /* 薪资明细打印样式 */
  .salary-header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15pt;
    margin-bottom: 20pt;
    padding: 10pt;
    border: 1pt solid black;
  }

  .salary-period {
    text-align: center;
    font-size: 14pt;
    font-weight: bold;
  }

  .salary-breakdown {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20pt;
  }

  .income-section,
  .deduction-section {
    border: 1pt solid black;
    padding: 10pt;
  }

  .section-title {
    text-align: center;
    font-weight: bold;
    border-bottom: 1pt solid black;
    padding-bottom: 5pt;
    margin-bottom: 10pt;
  }

  .salary-total {
    margin-top: 15pt;
    padding: 10pt;
    border: 2pt solid black;
    text-align: center;
    font-size: 16pt;
    font-weight: bold;
  }

  /* 报表打印样式 */
  .report-header {
    text-align: center;
    margin-bottom: 20pt;
  }

  .report-title {
    font-size: 20pt;
    font-weight: bold;
    margin-bottom: 8pt;
  }

  .report-subtitle {
    font-size: 12pt;
    color: #666 !important;
  }

  .report-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15pt;
    font-size: 10pt;
  }

  .report-summary {
    background: #f5f5f5 !important;
    border: 1pt solid black;
    padding: 15pt;
    margin-bottom: 20pt;
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15pt;
  }

  .summary-item {
    text-align: center;
  }

  .summary-number {
    font-size: 18pt;
    font-weight: bold;
    color: black !important;
  }

  .summary-text {
    font-size: 10pt;
    color: #666 !important;
  }

  /* 合同文档打印样式 */
  .contract-document {
    line-height: 1.6;
  }

  .contract-header {
    text-align: center;
    margin-bottom: 30pt;
  }

  .contract-title {
    font-size: 22pt;
    font-weight: bold;
    letter-spacing: 2pt;
  }

  .contract-number {
    margin-top: 10pt;
    font-size: 12pt;
  }

  .contract-parties {
    margin-bottom: 20pt;
  }

  .party-info {
    margin-bottom: 15pt;
    padding: 10pt;
    border: 1pt solid #ccc;
  }

  .party-title {
    font-weight: bold;
    margin-bottom: 8pt;
  }

  .contract-terms {
    counter-reset: article;
  }

  .contract-article {
    margin-bottom: 15pt;
    page-break-inside: avoid;
  }

  .contract-article::before {
    counter-increment: article;
    content: '第' counter(article, cjk-ideographic) '条';
    font-weight: bold;
    display: block;
    margin-bottom: 5pt;
  }

  .contract-signature {
    margin-top: 40pt;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40pt;
  }

  .signature-section {
    text-align: center;
  }

  .signature-title {
    font-weight: bold;
    margin-bottom: 20pt;
  }

  .signature-line {
    border-bottom: 1pt solid black;
    width: 80%;
    margin: 15pt auto;
    height: 20pt;
  }

  .signature-date {
    margin-top: 10pt;
  }

  /* 二维码和条形码 */
  .qrcode,
  .barcode {
    max-width: 2cm !important;
    height: auto !important;
  }

  /* 公章样式 */
  .official-seal {
    width: 3cm !important;
    height: 3cm !important;
    border: 2pt solid red !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: red !important;
    font-weight: bold !important;
    font-size: 8pt !important;
    text-align: center !important;
    line-height: 1.2 !important;
  }

  /* 水印效果 */
  .watermark {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 48pt;
    color: #f0f0f0 !important;
    z-index: -1;
    pointer-events: none;
    opacity: 0.3;
  }

  /* 页面边距调整 */
  .narrow-margins {
    margin: 1cm !important;
  }

  .wide-margins {
    margin: 3cm !important;
  }

  /* 字体优化 */
  .print-font-serif {
    font-family: 'SimSun', '宋体', serif !important;
  }

  .print-font-sans {
    font-family: 'Microsoft YaHei', '微软雅黑', sans-serif !important;
  }

  .print-font-mono {
    font-family: 'Courier New', '新宋体', monospace !important;
  }

  /* 颜色转换为灰度 */
  .grayscale * {
    filter: grayscale(100%) !important;
  }

  /* 强制分页 */
  .new-page {
    page-break-before: always !important;
  }

  .avoid-break {
    page-break-inside: avoid !important;
  }

  .keep-with-next {
    page-break-after: avoid !important;
  }

  /* 列表格式化 */
  .numbered-list {
    counter-reset: item;
  }

  .numbered-list li {
    counter-increment: item;
  }

  .numbered-list li::before {
    content: counter(item) '. ';
    font-weight: bold;
  }

  /* 特殊格式 */
  .highlight-print {
    background: #ffff99 !important;
    color: black !important;
    padding: 2pt 4pt !important;
  }

  .important-print {
    border: 2pt solid red !important;
    padding: 10pt !important;
    background: #fff5f5 !important;
  }

  .note-print {
    border-left: 4pt solid #666 !important;
    padding-left: 10pt !important;
    font-style: italic !important;
    color: #666 !important;
  }

  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none !important;
  }

  * {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }
}

/* 打印预览样式 */
.print-preview {
  background: #f5f5f5;
  padding: 20px;
}

.print-preview .print-page {
  background: white;
  margin: 0 auto 20px;
  padding: 2cm;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  min-height: 297mm;
  width: 210mm;
}

.print-preview .print-page.landscape {
  width: 297mm;
  min-height: 210mm;
}

/* 打印按钮样式 */
.print-actions {
  position: sticky;
  top: 0;
  background: white;
  padding: 15px;
  border-bottom: 1px solid #ddd;
  z-index: 100;
  display: flex;
  justify-content: center;
  gap: 10px;
}

@media print {
  .print-actions {
    display: none !important;
  }
}
