/**
 * Element Plus 主题变量覆盖
 * @description 基于WCAG标准调整的Element Plus主题色
 */

@import './colors.scss';

// 覆盖Element Plus默认变量
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': $primary-color
    ),
    'success': (
      'base': $success-color
    ),
    'warning': (
      'base': $warning-color
    ),
    'danger': (
      'base': $danger-color
    ),
    'info': (
      'base': $info-color
    )
  ),

  $text-color: (
    'primary': $text-primary,
    'regular': $text-regular,
    'secondary': $text-secondary,
    'placeholder': $text-placeholder,
    'disabled': $text-disabled
  ),

  $border-color: (
    '': $border-base,
    'light': $border-light,
    'lighter': $border-lighter,
    'extra-light': $border-extra-light
  ),

  $fill-color: (
    '': $bg-color,
    'light': $bg-color-page,
    'lighter': $bg-color-overlay,
    'extra-light': #fafafa,
    'dark': #f0f2f5,
    'darker': #e6e8eb,
    'blank': #ffffff
  ),

  $bg-color: (
    '': $bg-color,
    'page': $bg-color-page,
    'overlay': $bg-color-overlay
  )
);

// 额外的Element Plus样式调整
.el-button {
  // 确保按钮文字对比度
  &--primary {
    &:not(.is-disabled) {
      background-color: var(--hr-color-primary);
      border-color: var(--hr-color-primary);

      &:hover,
      &:focus {
        background-color: var(--hr-color-primary-light);
        border-color: var(--hr-color-primary-light);
      }

      &:active {
        background-color: var(--hr-color-primary-dark);
        border-color: var(--hr-color-primary-dark);
      }
    }
  }

  &--success {
    &:not(.is-disabled) {
      background-color: var(--hr-color-success);
      border-color: var(--hr-color-success);
    }
  }

  &--warning {
    &:not(.is-disabled) {
      background-color: var(--hr-color-warning);
      border-color: var(--hr-color-warning);
    }
  }

  &--danger {
    &:not(.is-disabled) {
      background-color: var(--hr-color-danger);
      border-color: var(--hr-color-danger);
    }
  }

  &--info {
    &:not(.is-disabled) {
      background-color: var(--hr-color-info);
      border-color: var(--hr-color-info);
    }
  }
}

// 链接颜色调整
.el-link {
  &:not(.is-disabled) {
    color: var(--hr-link-color);

    &:hover {
      color: var(--hr-link-hover-color);
    }

    &:active {
      color: var(--hr-link-active-color);
    }
  }

  &--primary {
    &:not(.is-disabled) {
      color: var(--hr-color-primary-dark);
    }
  }

  &--success {
    &:not(.is-disabled) {
      color: var(--hr-color-success);
    }
  }

  &--warning {
    &:not(.is-disabled) {
      color: var(--hr-color-warning);
    }
  }

  &--danger {
    &:not(.is-disabled) {
      color: var(--hr-color-danger);
    }
  }

  &--info {
    &:not(.is-disabled) {
      color: var(--hr-color-info);
    }
  }
}

// 表单元素focus状态增强
.el-input__inner:focus,
.el-textarea__inner:focus,
.el-select:focus-within {
  outline: 2px solid var(--hr-color-primary);
  outline-offset: 2px;
}

// 确保错误信息对比度
.el-form-item__error {
  color: var(--hr-color-danger);
  font-weight: 500;
}

// 工具提示对比度增强
.el-tooltip__popper {
  &.is-dark {
    background: var(--hr-color-black);
    color: var(--hr-color-white);
  }

  &.is-light {
    background: var(--hr-color-white);
    color: var(--hr-text-color-primary);
    border: 1px solid var(--hr-border-color);
  }
}

// 消息提示对比度
.el-message {
  &--success {
    .el-message__content {
      color: var(--hr-color-success);
    }
  }

  &--warning {
    .el-message__content {
      color: var(--hr-color-warning);
    }
  }

  &--error {
    .el-message__content {
      color: var(--hr-color-danger);
    }
  }

  &--info {
    .el-message__content {
      color: var(--hr-color-info);
    }
  }
}

// 标签对比度
.el-tag {
  &--primary {
    background-color: var(--hr-color-primary-light);
    color: var(--hr-color-primary-dark);
    border-color: var(--hr-color-primary);
  }

  &--success {
    background-color: var(--hr-color-success-light);
    color: var(--hr-color-success);
    border-color: var(--hr-color-success);
  }

  &--warning {
    background-color: var(--hr-color-warning-light);
    color: var(--hr-color-warning);
    border-color: var(--hr-color-warning);
  }

  &--danger {
    background-color: var(--hr-color-danger-light);
    color: var(--hr-color-danger);
    border-color: var(--hr-color-danger);
  }

  &--info {
    background-color: var(--hr-color-info-light);
    color: var(--hr-color-info);
    border-color: var(--hr-color-info);
  }
}

// 分页器对比度
.el-pagination {
  .el-pager li {
    &:not(.disabled):not(.active) {
      color: var(--hr-text-color-regular);

      &:hover {
        color: var(--hr-color-primary);
      }
    }

    &.active {
      color: var(--hr-color-primary);
      font-weight: 600;
    }
  }
}

// 表格对比度
.el-table {
  th {
    color: var(--hr-text-color-primary);
    font-weight: 600;
  }

  td {
    color: var(--hr-text-color-regular);
  }

  // 排序图标增强
  .el-table__column-header-sortable {
    .sort-caret {
      &.ascending {
        border-bottom-color: var(--hr-color-primary);
      }

      &.descending {
        border-top-color: var(--hr-color-primary);
      }
    }
  }
}

// 选项卡对比度
.el-tabs {
  .el-tabs__item {
    color: var(--hr-text-color-regular);

    &:hover {
      color: var(--hr-color-primary);
    }

    &.is-active {
      color: var(--hr-color-primary);
      font-weight: 600;
    }
  }
}
