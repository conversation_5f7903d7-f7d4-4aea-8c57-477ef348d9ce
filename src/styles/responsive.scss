/**
 * 响应式样式定义
 * 支持多种设备尺寸的自适应布局
 */

// 断点定义
$breakpoints: (
  xs: 0,
  // 手机竖屏 < 576px
  sm: 576px,
  // 手机横屏
  md: 768px,
  // 平板竖屏
  lg: 992px,
  // 平板横屏/小屏笔记本
  xl: 1200px,
  // 桌面显示器
  xxl: 1920px // 大屏显示器
);

// 响应式混入
@use 'sass:map';

@mixin respond-to($breakpoint) {
  $value: map.get($breakpoints, $breakpoint);
  @if $value {
    @media (min-width: $value) {
      @content;
    }
  }
}

@mixin respond-between($min, $max) {
  $min-value: map.get($breakpoints, $min);
  $max-value: map.get($breakpoints, $max);
  @if $min-value and $max-value {
    @media (min-width: $min-value) and (max-width: #{$max-value - 1px}) {
      @content;
    }
  }
}

@mixin respond-below($breakpoint) {
  $value: map.get($breakpoints, $breakpoint);
  @if $value {
    @media (max-width: #{$value - 1px}) {
      @content;
    }
  }
}

// 容器响应式
.hr-container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;

  @include respond-to(sm) {
    max-width: 540px;
  }

  @include respond-to(md) {
    max-width: 720px;
  }

  @include respond-to(lg) {
    max-width: 960px;
  }

  @include respond-to(xl) {
    max-width: 1140px;
  }

  @include respond-to(xxl) {
    max-width: 1320px;
  }
}

// 栅格系统响应式
.el-row {
  @include respond-below(md) {
    margin-left: -10px !important;
    margin-right: -10px !important;
  }
}

.el-col {
  @include respond-below(md) {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}

// 响应式工具类
@each $breakpoint, $value in $breakpoints {
  @if $breakpoint != xs {
    @include respond-to($breakpoint) {
      .hidden-#{$breakpoint}-up {
        display: none !important;
      }
    }
  }

  @include respond-below($breakpoint) {
    .hidden-#{$breakpoint}-down {
      display: none !important;
    }
  }

  @include respond-to($breakpoint) {
    .visible-#{$breakpoint} {
      display: block !important;
    }
  }
}

// 文字响应式
.responsive-text {
  @include respond-below(sm) {
    font-size: 14px;
  }

  @include respond-to(sm) {
    font-size: 16px;
  }

  @include respond-to(lg) {
    font-size: 18px;
  }
}

// 标题响应式
h1,
.h1 {
  @include respond-below(md) {
    font-size: 24px;
  }

  @include respond-to(md) {
    font-size: 32px;
  }

  @include respond-to(lg) {
    font-size: 36px;
  }
}

h2,
.h2 {
  @include respond-below(md) {
    font-size: 20px;
  }

  @include respond-to(md) {
    font-size: 24px;
  }

  @include respond-to(lg) {
    font-size: 28px;
  }
}

// 间距响应式
.responsive-padding {
  @include respond-below(md) {
    padding: 10px;
  }

  @include respond-to(md) {
    padding: 15px;
  }

  @include respond-to(lg) {
    padding: 20px;
  }
}

.responsive-margin {
  @include respond-below(md) {
    margin: 10px;
  }

  @include respond-to(md) {
    margin: 15px;
  }

  @include respond-to(lg) {
    margin: 20px;
  }
}

// 表格响应式
.el-table {
  @include respond-below(md) {
    .el-table__header-wrapper {
      display: none;
    }

    .el-table__body-wrapper {
      .el-table__row {
        display: block;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        margin-bottom: 10px;
        padding: 10px;

        td {
          display: flex;
          padding: 5px 0;
          border: none;

          &:before {
            content: attr(data-label);
            font-weight: bold;
            margin-right: 10px;
            min-width: 100px;
          }
        }
      }
    }
  }
}

// 卡片响应式
.el-card {
  @include respond-below(sm) {
    border-radius: 0;
    margin-left: -15px;
    margin-right: -15px;
  }
}

// 对话框响应式
.el-dialog {
  @include respond-below(md) {
    width: 95% !important;
    margin: 10px auto !important;

    .el-dialog__header {
      padding: 15px;
    }

    .el-dialog__body {
      padding: 15px;
    }

    .el-dialog__footer {
      padding: 10px 15px;
    }
  }
}

// 表单响应式
.el-form {
  @include respond-below(md) {
    .el-form-item__label {
      float: none;
      display: block;
      text-align: left;
      padding: 0 0 10px;
    }

    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}

// 按钮响应式
.el-button {
  @include respond-below(sm) {
    &:not(.el-button--text) {
      padding: 10px 15px;
      font-size: 14px;
    }
  }
}

.button-group-responsive {
  @include respond-below(sm) {
    .el-button {
      display: block;
      width: 100%;
      margin-left: 0 !important;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 导航响应式
.el-menu {
  @include respond-below(lg) {
    &.el-menu--horizontal {
      display: none;
    }
  }
}

// 移动端导航
.mobile-menu-trigger {
  display: none;

  @include respond-below(lg) {
    display: inline-block;
  }
}

// 分页响应式
.el-pagination {
  @include respond-below(sm) {
    .el-pagination__sizes,
    .el-pagination__jump {
      display: none;
    }

    .btn-prev,
    .btn-next {
      padding: 0 8px;
    }

    .el-pager li {
      padding: 0 6px;
      font-size: 12px;
    }
  }
}

// 统计卡片响应式
.stat-card {
  @include respond-below(md) {
    margin-bottom: 15px;

    .stat-content {
      padding: 15px;

      .stat-value {
        font-size: 24px;
      }

      .stat-label {
        font-size: 14px;
      }
    }
  }
}

// 搜索栏响应式
.search-bar {
  @include respond-below(md) {
    .el-col {
      margin-bottom: 10px;
    }

    .el-input {
      width: 100%;
    }
  }
}

// 工作区响应式
.main-content {
  @include respond-below(lg) {
    margin-left: 0;

    &.has-sidebar {
      margin-left: 60px; // 收缩的侧边栏宽度
    }
  }
}

// 侧边栏响应式
.sidebar {
  @include respond-below(lg) {
    width: 60px;

    &.expanded {
      width: 200px;
    }

    .el-menu {
      width: 100%;

      .el-menu-item,
      .el-submenu__title {
        padding: 0 20px;
      }
    }
  }

  @include respond-below(md) {
    position: fixed;
    z-index: 2000;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s;

    &.mobile-visible {
      transform: translateX(0);
    }
  }
}

// 图表响应式
.chart-container {
  @include respond-below(md) {
    height: 300px !important;
  }

  @include respond-below(sm) {
    height: 250px !important;
  }
}

// Flex布局响应式
.flex-responsive {
  display: flex;
  flex-wrap: wrap;

  @include respond-below(md) {
    flex-direction: column;
  }
}

// Grid布局响应式
.grid-responsive {
  display: grid;
  gap: 20px;

  @include respond-to(xs) {
    grid-template-columns: 1fr;
  }

  @include respond-to(sm) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include respond-to(md) {
    grid-template-columns: repeat(3, 1fr);
  }

  @include respond-to(lg) {
    grid-template-columns: repeat(4, 1fr);
  }
}

// 滚动条响应式
@include respond-below(md) {
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}

// 触摸优化
@media (hover: none) and (pointer: coarse) {
  // 增大可点击区域
  .el-button,
  .el-link,
  a {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  // 禁用悬停效果
  .el-button:hover,
  .el-link:hover {
    color: inherit;
    border-color: inherit;
    background-color: inherit;
  }
}

// 打印样式
@media print {
  .no-print,
  .el-header,
  .el-aside,
  .el-button,
  .el-pagination {
    display: none !important;
  }

  .el-main {
    margin: 0;
    padding: 0;
  }
}

// 方向响应式
@media (orientation: landscape) {
  @include respond-below(sm) {
    .mobile-landscape-adjust {
      max-height: 90vh;
      overflow-y: auto;
    }
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .border-1px {
    position: relative;

    &:after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 1px;
      background-color: #e5e5e5;
      transform: scaleY(0.5);
    }
  }
}
