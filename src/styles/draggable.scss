/**
 * 拖拽排序全局样式
 */

// 拖拽时的幽灵元素
.sortable-ghost {
  opacity: 0.5;
}

// 选中的元素
.sortable-chosen {
  background-color: var(--el-color-primary-light-9);
}

// 拖拽中的元素
.sortable-drag {
  cursor: move;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 拖拽回退元素
.sortable-fallback {
  opacity: 0.8;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 拖拽手柄
.drag-handle {
  cursor: move;
  color: var(--el-text-color-secondary);
  transition: color 0.3s;

  &:hover {
    color: var(--el-color-primary);
  }

  &:active {
    color: var(--el-color-primary-dark-2);
  }
}

// 拖拽列表过渡动画
.draggable-list-move {
  transition: transform 0.3s;
}

// 拖拽禁用状态
.draggable-disabled {
  cursor: not-allowed !important;
  opacity: 0.6;

  * {
    cursor: not-allowed !important;
  }
}

// 拖拽占位符
.draggable-placeholder {
  background: var(--el-fill-color-light);
  border: 2px dashed var(--el-border-color);
  border-radius: 4px;
}

// 拖拽提示
.draggable-hint {
  position: absolute;
  padding: 4px 8px;
  background: var(--el-color-primary);
  color: #fff;
  font-size: 12px;
  border-radius: 4px;
  pointer-events: none;
  z-index: 9999;

  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid var(--el-color-primary);
  }
}

// 暗黑模式适配
html[data-theme='dark'] {
  .sortable-ghost {
    opacity: 0.3;
  }

  .sortable-chosen {
    background-color: var(--el-color-primary-dark-2);
  }

  .sortable-drag {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.3);
  }

  .draggable-placeholder {
    background: var(--el-bg-color-page);
    border-color: var(--el-border-color-light);
  }
}
