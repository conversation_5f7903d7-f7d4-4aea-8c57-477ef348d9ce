// 移动端响应式样式
// 定义断点变量
$mobile-xs: 375px;
$mobile-sm: 414px;
$mobile-md: 768px;
$tablet-sm: 834px;
$tablet-md: 1024px;

// 移动端基础样式
@mixin mobile-base {
  // 防止iOS橡皮筋效果
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  -webkit-overflow-scrolling: touch;

  // 禁用用户选择
  -webkit-user-select: none;
  user-select: none;

  // 禁用长按菜单
  -webkit-touch-callout: none;
}

// 安全区域适配
@mixin safe-area-inset {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

// 移动端容器
.mobile-container {
  @include mobile-base;

  .mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 44px;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-lighter);
    z-index: 100;
    @include safe-area-inset;

    display: flex;
    align-items: center;
    padding: 0 16px;

    .header-title {
      flex: 1;
      text-align: center;
      font-size: 17px;
      font-weight: 500;
    }

    .header-action {
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .mobile-content {
    position: absolute;
    top: 44px;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    @include safe-area-inset;
  }

  .mobile-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-lighter);
    z-index: 100;
    @include safe-area-inset;
  }
}

// 移动端导航栏
.mobile-navbar {
  display: flex;
  height: 49px;

  .nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--el-text-color-regular);
    transition: all 0.3s;

    .nav-icon {
      font-size: 24px;
      margin-bottom: 2px;
    }

    .nav-text {
      font-size: 10px;
    }

    &.active {
      color: var(--el-color-primary);
    }

    &:active {
      opacity: 0.7;
    }
  }
}

// 移动端卡片
.mobile-card {
  background: var(--el-bg-color);
  margin: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;

  .card-header {
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .card-subtitle {
      font-size: 14px;
      color: var(--el-text-color-secondary);
      margin-top: 4px;
    }
  }

  .card-body {
    padding: 16px;
  }

  .card-footer {
    padding: 12px 16px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-lighter);
  }
}

// 移动端列表
.mobile-list {
  background: var(--el-bg-color);

  .list-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    transition: background 0.3s;

    &:active {
      background: var(--el-fill-color-light);
    }

    &:last-child {
      border-bottom: none;
    }

    .item-icon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--el-color-primary-light-9);
      border-radius: 50%;
      color: var(--el-color-primary);
      font-size: 20px;
    }

    .item-content {
      flex: 1;
      min-width: 0;

      .item-title {
        font-size: 16px;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .item-desc {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .item-extra {
      margin-left: 12px;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }

    .item-arrow {
      margin-left: 8px;
      color: var(--el-text-color-placeholder);
    }
  }
}

// 移动端表单
.mobile-form {
  .form-group {
    background: var(--el-bg-color);
    margin-bottom: 12px;

    .form-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      &:last-child {
        border-bottom: none;
      }

      .form-label {
        width: 100px;
        color: var(--el-text-color-primary);
        font-size: 16px;
      }

      .form-control {
        flex: 1;

        input,
        textarea {
          width: 100%;
          border: none;
          outline: none;
          font-size: 16px;
          background: transparent;

          &::placeholder {
            color: var(--el-text-color-placeholder);
          }
        }

        textarea {
          min-height: 80px;
          resize: none;
        }
      }

      .form-extra {
        margin-left: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .form-tips {
    padding: 8px 16px;
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

// 移动端按钮
.mobile-button {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  outline: none;
  transition: all 0.3s;

  &.primary {
    background: var(--el-color-primary);
    color: #fff;

    &:active {
      background: var(--el-color-primary-dark-2);
    }
  }

  &.default {
    background: var(--el-fill-color);
    color: var(--el-text-color-primary);

    &:active {
      background: var(--el-fill-color-dark);
    }
  }

  &.text {
    background: transparent;
    color: var(--el-color-primary);

    &:active {
      opacity: 0.7;
    }
  }

  &:disabled {
    opacity: 0.5;
  }
}

// 移动端网格
.mobile-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 12px 6px;

  .grid-item {
    width: 25%;
    padding: 6px;

    .grid-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 0;

      .grid-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--el-fill-color);
        border-radius: 12px;
        font-size: 24px;
        color: var(--el-color-primary);
        margin-bottom: 8px;
      }

      .grid-text {
        font-size: 14px;
        color: var(--el-text-color-primary);
        text-align: center;
      }
    }
  }
}

// 移动端统计卡片
.mobile-stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(
    135deg,
    var(--el-color-primary-light-9),
    var(--el-color-primary-light-8)
  );
  border-radius: 12px;
  margin: 12px;

  .stat-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    font-size: 28px;
    color: var(--el-color-primary);
    margin-right: 16px;
  }

  .stat-content {
    flex: 1;

    .stat-value {
      font-size: 28px;
      font-weight: bold;
      color: var(--el-color-primary);
      line-height: 1;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }

    .stat-trend {
      display: flex;
      align-items: center;
      margin-top: 8px;
      font-size: 14px;

      &.up {
        color: var(--el-color-success);
      }

      &.down {
        color: var(--el-color-danger);
      }

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

// 响应式断点样式
@media (max-width: $mobile-md) {
  // 隐藏桌面端组件
  .desktop-only {
    display: none !important;
  }

  // 显示移动端组件
  .mobile-only {
    display: block !important;
  }

  // Element Plus 组件适配
  .el-dialog {
    width: 90% !important;
    margin: 20px auto !important;

    .el-dialog__header {
      padding: 16px;
    }

    .el-dialog__body {
      padding: 16px;
    }
  }

  .el-message-box {
    width: 80% !important;
  }

  .el-table {
    font-size: 14px;

    .el-table__header th {
      padding: 8px 0;
    }

    .el-table__body td {
      padding: 12px 0;
    }
  }

  // 图表适配
  .chart-container {
    height: 300px !important;
  }
}

// 横屏适配
@media (orientation: landscape) and (max-width: $tablet-sm) {
  .mobile-container {
    .mobile-content {
      padding-bottom: 20px;
    }
  }

  .mobile-navbar {
    height: 40px;

    .nav-icon {
      font-size: 20px;
    }

    .nav-text {
      display: none;
    }
  }
}

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
  .mobile-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .mobile-stat-card {
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(64, 158, 255, 0.2));
  }
}
