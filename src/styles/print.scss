/**
 * 打印样式优化
 * 为各种页面和组件提供打印友好的样式
 */

// 打印媒体查询
@media print {
  // ==================== 全局打印设置 ====================

  // 页面设置
  @page {
    size: A4; // A4纸张
    margin: 20mm 15mm; // 页边距

    // 页眉页脚
    @top-center {
      content: '杭科院人事综合管理系统';
      font-size: 12px;
      color: #666;
    }

    @bottom-center {
      content: '第 ' counter(page) ' 页，共 ' counter(pages) ' 页';
      font-size: 10px;
      color: #999;
    }

    @bottom-right {
      content: '打印时间：' string(print-time);
      font-size: 10px;
      color: #999;
    }
  }

  // 设置打印时间
  body {
    string-set: print-time attr(data-print-time);
  }

  // ==================== 全局元素隐藏 ====================

  // 隐藏不需要打印的元素
  .no-print,
  .el-header,
  .el-aside,
  .el-footer,
  .el-backtop,
  .el-dialog__wrapper,
  .el-drawer__wrapper,
  .el-message-box__wrapper,
  .el-notification,
  .el-loading-mask,
  .el-tooltip__popper,
  .el-popover__popper,
  .el-dropdown__popper,
  .el-select-dropdown,
  .el-picker-panel,
  .el-cascader__dropdown,
  .el-color-dropdown,
  .el-autocomplete-suggestion,
  .page-header .header-actions,
  .toolbar,
  .action-bar,
  .sync-indicator,
  .offline-banner,
  .install-prompt,
  .update-prompt,
  .pwa-enhancement,
  .hr-context-menu,
  button:not(.print-show),
  .el-button:not(.print-show),
  .el-pagination,
  .el-breadcrumb,
  video,
  audio,
  iframe:not(.print-iframe) {
    display: none !important;
  }

  // ==================== 全局样式重置 ====================

  // 重置背景和颜色
  * {
    background: transparent !important;
    color: #000 !important;
    text-shadow: none !important;
    box-shadow: none !important;
  }

  // 保留某些背景色用于区分
  .print-bg-light {
    background-color: #f5f5f5 !important;
  }

  .print-bg-dark {
    background-color: #e0e0e0 !important;
  }

  // 链接样式
  a,
  a:visited {
    text-decoration: underline;
    color: #000 !important;
  }

  // 显示链接地址
  a[href]::after {
    content: ' (' attr(href) ')';
    font-size: 0.8em;
    font-style: italic;
  }

  // 缩略语显示完整内容
  abbr[title]::after {
    content: ' (' attr(title) ')';
    font-size: 0.8em;
  }

  // 图片处理
  img {
    max-width: 100% !important;
    page-break-inside: avoid;
  }

  // 防止元素被分页
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  .el-card,
  .el-table,
  .el-form,
  .print-keep-together {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  // 标题前分页
  h1,
  h2 {
    page-break-before: auto;
  }

  // ==================== 布局调整 ====================

  // 主容器全宽
  .el-container {
    display: block !important;
    width: 100% !important;
  }

  .el-main {
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
  }

  // ==================== 表格打印优化 ====================

  .el-table {
    border: 1px solid #ddd !important;

    // 表头
    .el-table__header {
      th {
        background-color: #f5f5f5 !important;
        color: #000 !important;
        font-weight: bold;
        border: 1px solid #ddd !important;
        padding: 8px !important;
      }
    }

    // 表格内容
    .el-table__body {
      td {
        border: 1px solid #ddd !important;
        padding: 6px !important;
      }

      // 斑马纹
      tr:nth-child(even) {
        background-color: #fafafa !important;
      }
    }

    // 隐藏操作列
    .el-table__column--selection,
    .table-actions,
    .operation-column {
      display: none !important;
    }
  }

  // 表格标题
  .table-title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
  }

  // ==================== 表单打印优化 ====================

  .el-form {
    .el-form-item {
      margin-bottom: 12px !important;

      .el-form-item__label {
        color: #000 !important;
        font-weight: bold;
        width: 120px !important;
        text-align: right;
      }

      .el-form-item__content {
        .el-input,
        .el-select,
        .el-date-editor {
          .el-input__inner {
            border: none !important;
            border-bottom: 1px solid #ddd !important;
            border-radius: 0 !important;
            padding: 4px 0 !important;
          }

          .el-input__suffix,
          .el-input__prefix {
            display: none !important;
          }
        }

        // 只读表单值
        .print-value {
          padding: 4px 0;
          border-bottom: 1px solid #ddd;
        }
      }
    }
  }

  // ==================== 卡片打印优化 ====================

  .el-card {
    border: 1px solid #ddd !important;
    margin-bottom: 20px !important;

    .el-card__header {
      background-color: #f5f5f5 !important;
      border-bottom: 1px solid #ddd !important;
      padding: 10px !important;
      font-weight: bold;
    }

    .el-card__body {
      padding: 15px !important;
    }
  }

  // ==================== 描述列表打印优化 ====================

  .el-descriptions {
    .el-descriptions__header {
      margin-bottom: 10px !important;

      .el-descriptions__title {
        font-size: 16px !important;
        font-weight: bold !important;
      }
    }

    .el-descriptions__body {
      .el-descriptions__table {
        border: 1px solid #ddd !important;

        .el-descriptions__label {
          background-color: #f5f5f5 !important;
          font-weight: bold !important;
          border: 1px solid #ddd !important;
          padding: 8px !important;
        }

        .el-descriptions__content {
          border: 1px solid #ddd !important;
          padding: 8px !important;
        }
      }
    }
  }

  // ==================== 标签打印优化 ====================

  .el-tag {
    border: 1px solid #ddd !important;
    background-color: transparent !important;
    color: #000 !important;
    padding: 2px 6px !important;

    &--success {
      border-color: #67c23a !important;
    }

    &--warning {
      border-color: #e6a23c !important;
    }

    &--danger {
      border-color: #f56c6c !important;
    }

    &--info {
      border-color: #909399 !important;
    }
  }

  // ==================== 统计图表打印 ====================

  // ECharts图表
  .echarts-container,
  [_echarts_instance_] {
    page-break-inside: avoid !important;

    canvas {
      max-width: 100% !important;
      height: auto !important;
    }
  }

  // ==================== 特定页面打印样式 ====================

  // 员工详情页
  .employee-detail {
    .detail-header {
      text-align: center;
      margin-bottom: 20px;

      .employee-photo {
        width: 100px !important;
        height: 100px !important;
        margin: 0 auto 10px;
      }

      .employee-name {
        font-size: 20px;
        font-weight: bold;
      }
    }

    .detail-section {
      margin-bottom: 20px;

      .section-title {
        font-size: 16px;
        font-weight: bold;
        border-bottom: 2px solid #000;
        padding-bottom: 5px;
        margin-bottom: 10px;
      }
    }
  }

  // 工资条
  .salary-slip {
    border: 2px solid #000;
    padding: 20px;

    .slip-header {
      text-align: center;
      margin-bottom: 20px;

      .company-name {
        font-size: 18px;
        font-weight: bold;
      }

      .slip-title {
        font-size: 16px;
        margin-top: 10px;
      }
    }

    .slip-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;

      .info-item {
        flex: 1;
      }
    }

    .slip-table {
      width: 100%;
      border-collapse: collapse;

      th,
      td {
        border: 1px solid #000;
        padding: 8px;
        text-align: center;
      }

      th {
        background-color: #f5f5f5 !important;
        font-weight: bold;
      }

      .total-row {
        font-weight: bold;
        background-color: #fafafa !important;
      }
    }

    .slip-footer {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;

      .signature {
        border-top: 1px solid #000;
        padding-top: 5px;
        min-width: 150px;
        text-align: center;
      }
    }
  }

  // 考勤报表
  .attendance-report {
    .report-header {
      text-align: center;
      margin-bottom: 20px;

      .report-title {
        font-size: 18px;
        font-weight: bold;
      }

      .report-period {
        margin-top: 5px;
        color: #666 !important;
      }
    }

    .summary-cards {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      margin-bottom: 20px;

      .summary-card {
        border: 1px solid #ddd;
        padding: 10px;
        text-align: center;

        .card-title {
          font-size: 12px;
          color: #666 !important;
        }

        .card-value {
          font-size: 20px;
          font-weight: bold;
          margin-top: 5px;
        }
      }
    }
  }

  // 组织架构图
  .org-chart {
    .org-node {
      border: 1px solid #000 !important;
      background-color: #fff !important;

      &.is-root {
        background-color: #f5f5f5 !important;
      }
    }

    .org-node-content {
      padding: 10px !important;

      .node-name {
        font-weight: bold;
      }
    }

    // 连接线
    .org-edge {
      stroke: #000 !important;
    }
  }

  // ==================== 打印工具类 ====================

  // 强制分页
  .print-page-break {
    page-break-after: always !important;
  }

  // 避免分页
  .print-no-break {
    page-break-inside: avoid !important;
  }

  // 打印时显示
  .print-only {
    display: block !important;
  }

  // 打印时隐藏
  .screen-only {
    display: none !important;
  }

  // 打印边框
  .print-border {
    border: 1px solid #000 !important;
  }

  // 打印表格样式
  .print-table {
    width: 100% !important;
    border-collapse: collapse !important;

    th,
    td {
      border: 1px solid #000 !important;
      padding: 8px !important;
    }

    th {
      background-color: #f5f5f5 !important;
      font-weight: bold !important;
    }
  }

  // 横向打印
  .print-landscape {
    @page {
      size: A4 landscape;
    }
  }

  // ==================== 水印支持 ====================

  .print-watermark {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 100px;
    color: rgba(0, 0, 0, 0.1) !important;
    z-index: -1;
    pointer-events: none;
  }

  // ==================== 二维码打印 ====================

  .qrcode-print {
    width: 100px !important;
    height: 100px !important;
    margin: 10px auto;

    img,
    canvas {
      width: 100% !important;
      height: 100% !important;
    }
  }
}

// ==================== 打印预览模式 ====================

.print-preview-mode {
  background: #f5f5f5;
  padding: 20px;

  .print-preview-container {
    background: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    padding: 20mm 15mm;
    width: 210mm; // A4宽度
    min-height: 297mm; // A4高度
  }
}

// ==================== 打印按钮样式 ====================

.print-actions {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 10px;

  @media print {
    display: none !important;
  }
}

// ==================== 响应式打印 ====================

@media print and (max-width: 210mm) {
  // 移动设备打印适配
  body {
    font-size: 12px !important;
  }

  .el-table {
    font-size: 10px !important;
  }

  .el-form-item__label {
    width: 80px !important;
  }
}
