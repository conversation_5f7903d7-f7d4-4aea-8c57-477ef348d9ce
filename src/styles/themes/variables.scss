/**
 * CSS 变量体系
 * 定义所有主题相关的 CSS 变量
 */

// 默认主题（浅色）
:root {
  // ========== 颜色系统 ==========
  // 品牌色
  --hr-color-primary: #409eff;
  --hr-color-primary-light-1: #53a8ff;
  --hr-color-primary-light-2: #66b1ff;
  --hr-color-primary-light-3: #79bbff;
  --hr-color-primary-light-4: #8cc5ff;
  --hr-color-primary-light-5: #a0cfff;
  --hr-color-primary-light-6: #b3d8ff;
  --hr-color-primary-light-7: #c6e2ff;
  --hr-color-primary-light-8: #d9ecff;
  --hr-color-primary-light-9: #ecf5ff;
  --hr-color-primary-dark-1: #3a8edc;
  --hr-color-primary-dark-2: #337ecc;

  // 功能色
  --hr-color-success: #67c23a;
  --hr-color-success-light: #e1f3d8;
  --hr-color-success-lighter: #f0f9e8;
  --hr-color-success-dark: #529b2e;

  --hr-color-warning: #e6a23c;
  --hr-color-warning-light: #faecd8;
  --hr-color-warning-lighter: #fdf6ec;
  --hr-color-warning-dark: #b88230;

  --hr-color-danger: #f56c6c;
  --hr-color-danger-light: #fde2e2;
  --hr-color-danger-lighter: #fef0f0;
  --hr-color-danger-dark: #c45656;

  --hr-color-info: #909399;
  --hr-color-info-light: #e9e9eb;
  --hr-color-info-lighter: #f4f4f5;
  --hr-color-info-dark: #73767a;

  // 中性色
  --hr-color-white: #ffffff;
  --hr-color-black: #000000;

  // 背景色
  --hr-bg-color: #ffffff;
  --hr-bg-color-page: #f2f3f5;
  --hr-bg-color-overlay: #ffffff;

  // 文字色
  --hr-text-color-primary: #303133;
  --hr-text-color-regular: #606266;
  --hr-text-color-secondary: #909399;
  --hr-text-color-placeholder: #a8abb2;
  --hr-text-color-disabled: #c0c4cc;

  // 边框色
  --hr-border-color: #dcdfe6;
  --hr-border-color-light: #e4e7ed;
  --hr-border-color-lighter: #ebeef5;
  --hr-border-color-extra-light: #f2f6fc;
  --hr-border-color-dark: #d4d7de;
  --hr-border-color-darker: #cdd0d6;

  // 填充色
  --hr-fill-color: #f0f2f5;
  --hr-fill-color-light: #f5f7fa;
  --hr-fill-color-lighter: #fafafa;
  --hr-fill-color-extra-light: #fafcff;
  --hr-fill-color-dark: #ebedf0;
  --hr-fill-color-darker: #e6e8eb;
  --hr-fill-color-blank: #ffffff;

  // ========== 尺寸系统 ==========
  // 字体大小
  --hr-font-size-extra-large: 20px;
  --hr-font-size-large: 18px;
  --hr-font-size-medium: 16px;
  --hr-font-size-base: 14px;
  --hr-font-size-small: 13px;
  --hr-font-size-extra-small: 12px;

  // 字体粗细
  --hr-font-weight-primary: 500;
  --hr-font-weight-regular: 400;
  --hr-font-weight-light: 300;
  --hr-font-weight-bold: 600;

  // 行高
  --hr-line-height-large: 1.8;
  --hr-line-height-medium: 1.6;
  --hr-line-height-base: 1.5;
  --hr-line-height-small: 1.3;

  // 圆角
  --hr-border-radius-base: 4px;
  --hr-border-radius-small: 2px;
  --hr-border-radius-round: 20px;
  --hr-border-radius-circle: 100%;

  // 边框宽度
  --hr-border-width-base: 1px;
  --hr-border-width-thick: 2px;
  --hr-border-width-thicker: 3px;

  // 间距
  --hr-spacing-extra-large: 32px;
  --hr-spacing-large: 24px;
  --hr-spacing-medium: 16px;
  --hr-spacing-base: 12px;
  --hr-spacing-small: 8px;
  --hr-spacing-extra-small: 4px;

  // ========== 阴影系统 ==========
  --hr-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --hr-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --hr-shadow-lighter: 0 1px 4px rgba(0, 0, 0, 0.08);
  --hr-shadow-dark: 0 2px 16px 0 rgba(0, 0, 0, 0.15);

  // ========== 动画系统 ==========
  --hr-transition-duration: 0.3s;
  --hr-transition-duration-fast: 0.2s;
  --hr-transition-duration-slow: 0.4s;
  --hr-transition-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  --hr-transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
  --hr-transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);

  // ========== Z-index 层级 ==========
  --hr-index-normal: 1;
  --hr-index-top: 1000;
  --hr-index-popper: 2000;
  --hr-index-dialog: 2001;
  --hr-index-loading: 2010;
  --hr-index-notification: 2020;
  --hr-index-message: 2030;

  // ========== 组件特定变量 ==========
  // 头部
  --hr-header-height: 60px;
  --hr-header-bg-color: var(--hr-bg-color);
  --hr-header-text-color: var(--hr-text-color-primary);
  --hr-header-border-color: var(--hr-border-color-light);

  // 侧边栏
  --hr-sidebar-width: 240px;
  --hr-sidebar-collapsed-width: 64px;
  --hr-sidebar-bg-color: var(--hr-bg-color);
  --hr-sidebar-text-color: var(--hr-text-color-regular);
  --hr-sidebar-active-text-color: var(--hr-color-primary);

  // 菜单
  --hr-menu-item-height: 48px;
  --hr-menu-item-hover-bg-color: var(--hr-fill-color-light);
  --hr-menu-item-active-bg-color: var(--hr-color-primary-light-9);

  // 卡片
  --hr-card-shadow: var(--hr-shadow-lighter);
  --hr-card-hover-shadow: var(--hr-shadow-light);
  --hr-card-border-color: var(--hr-border-color-lighter);

  // 表格
  --hr-table-header-bg-color: var(--hr-fill-color-light);
  --hr-table-header-text-color: var(--hr-text-color-regular);
  --hr-table-row-hover-bg-color: var(--hr-fill-color-lighter);
  --hr-table-stripe-bg-color: var(--hr-fill-color-lighter);

  // 表单
  --hr-input-height: 40px;
  --hr-input-height-small: 32px;
  --hr-input-height-large: 48px;
  --hr-input-bg-color: var(--hr-bg-color);
  --hr-input-border-color: var(--hr-border-color);
  --hr-input-hover-border-color: var(--hr-border-color-dark);
  --hr-input-focus-border-color: var(--hr-color-primary);

  // 按钮
  --hr-button-hover-bg-color: var(--hr-color-primary-light-9);
  --hr-button-active-bg-color: var(--hr-color-primary-light-7);

  // 标签
  --hr-tag-bg-color: var(--hr-fill-color);
  --hr-tag-border-color: var(--hr-border-color-light);

  // 分页
  --hr-pagination-bg-color: var(--hr-bg-color);
  --hr-pagination-hover-bg-color: var(--hr-fill-color-light);

  // 滚动条
  --hr-scrollbar-width: 8px;
  --hr-scrollbar-bg-color: var(--hr-fill-color-lighter);
  --hr-scrollbar-thumb-bg-color: var(--hr-fill-color-darker);
  --hr-scrollbar-thumb-hover-bg-color: var(--hr-fill-color-dark);
}

// 主题色预设
:root {
  // 蓝色主题
  --hr-preset-blue: #409eff;
  --hr-preset-blue-light: #66b1ff;
  --hr-preset-blue-dark: #337ecc;

  // 绿色主题
  --hr-preset-green: #67c23a;
  --hr-preset-green-light: #85ce61;
  --hr-preset-green-dark: #529b2e;

  // 橙色主题
  --hr-preset-orange: #e6a23c;
  --hr-preset-orange-light: #ebb563;
  --hr-preset-orange-dark: #b88230;

  // 红色主题
  --hr-preset-red: #f56c6c;
  --hr-preset-red-light: #f78989;
  --hr-preset-red-dark: #c45656;

  // 紫色主题
  --hr-preset-purple: #6b5ce6;
  --hr-preset-purple-light: #8b7ee8;
  --hr-preset-purple-dark: #5444c4;

  // 粉色主题
  --hr-preset-pink: #ff6b9d;
  --hr-preset-pink-light: #ff8bb0;
  --hr-preset-pink-dark: #e44d7b;

  // 青色主题
  --hr-preset-cyan: #1ebfb3;
  --hr-preset-cyan-light: #4ecdc4;
  --hr-preset-cyan-dark: #16a085;
}

// 媒体查询断点
:root {
  --hr-breakpoint-xs: 480px;
  --hr-breakpoint-sm: 768px;
  --hr-breakpoint-md: 992px;
  --hr-breakpoint-lg: 1200px;
  --hr-breakpoint-xl: 1920px;
}

// 同步 Element Plus 变量
:root {
  // 颜色
  --el-color-primary: var(--hr-color-primary);
  --el-color-success: var(--hr-color-success);
  --el-color-warning: var(--hr-color-warning);
  --el-color-danger: var(--hr-color-danger);
  --el-color-info: var(--hr-color-info);

  // 背景
  --el-bg-color: var(--hr-bg-color);
  --el-bg-color-page: var(--hr-bg-color-page);
  --el-bg-color-overlay: var(--hr-bg-color-overlay);

  // 文字
  --el-text-color-primary: var(--hr-text-color-primary);
  --el-text-color-regular: var(--hr-text-color-regular);
  --el-text-color-secondary: var(--hr-text-color-secondary);
  --el-text-color-placeholder: var(--hr-text-color-placeholder);
  --el-text-color-disabled: var(--hr-text-color-disabled);

  // 边框
  --el-border-color: var(--hr-border-color);
  --el-border-color-light: var(--hr-border-color-light);
  --el-border-color-lighter: var(--hr-border-color-lighter);
  --el-border-color-extra-light: var(--hr-border-color-extra-light);

  // 填充
  --el-fill-color: var(--hr-fill-color);
  --el-fill-color-light: var(--hr-fill-color-light);
  --el-fill-color-lighter: var(--hr-fill-color-lighter);
  --el-fill-color-extra-light: var(--hr-fill-color-extra-light);
  --el-fill-color-dark: var(--hr-fill-color-dark);
  --el-fill-color-darker: var(--hr-fill-color-darker);
  --el-fill-color-blank: var(--hr-fill-color-blank);

  // 字体
  --el-font-size-extra-large: var(--hr-font-size-extra-large);
  --el-font-size-large: var(--hr-font-size-large);
  --el-font-size-medium: var(--hr-font-size-medium);
  --el-font-size-base: var(--hr-font-size-base);
  --el-font-size-small: var(--hr-font-size-small);
  --el-font-size-extra-small: var(--hr-font-size-extra-small);
}
