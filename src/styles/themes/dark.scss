/**
 * 暗黑模式主题样式
 * 支持手动切换和系统跟随
 */

// 暗黑模式根变量 mixin
@mixin dark-theme-root {
  // 基础颜色
  --hr-bg-color: #141414;
  --hr-bg-color-page: #0a0a0a;
  --hr-bg-color-overlay: #1d1e1f;
  --hr-text-color-primary: #e5eaf3;
  --hr-text-color-regular: #cfd3dc;
  --hr-text-color-secondary: #a3a6ad;
  --hr-text-color-placeholder: #8d9095;
  --hr-text-color-disabled: #6c6e72;

  // 边框颜色
  --hr-border-color: #4c4d4f;
  --hr-border-color-light: #414243;
  --hr-border-color-lighter: #363637;
  --hr-border-color-extra-light: #2c2d2e;

  // 填充颜色
  --hr-fill-color: #303030;
  --hr-fill-color-light: #262626;
  --hr-fill-color-lighter: #1d1d1d;
  --hr-fill-color-extra-light: #191919;
  --hr-fill-color-dark: #3a3a3a;
  --hr-fill-color-darker: #424242;
  --hr-fill-color-blank: #141414;

  // 阴影
  --hr-shadow-light: 0 0 12px rgba(0, 0, 0, 0.72);
  --hr-shadow-lighter: 0 0 6px rgba(0, 0, 0, 0.72);
  --hr-shadow-dark: 0 0 24px rgba(0, 0, 0, 0.72);

  // Element Plus 变量覆盖
  --el-bg-color: var(--hr-bg-color);
  --el-bg-color-page: var(--hr-bg-color-page);
  --el-bg-color-overlay: var(--hr-bg-color-overlay);
  --el-text-color-primary: var(--hr-text-color-primary);
  --el-text-color-regular: var(--hr-text-color-regular);
  --el-text-color-secondary: var(--hr-text-color-secondary);
  --el-text-color-placeholder: var(--hr-text-color-placeholder);
  --el-text-color-disabled: var(--hr-text-color-disabled);
  --el-border-color: var(--hr-border-color);
  --el-border-color-light: var(--hr-border-color-light);
  --el-border-color-lighter: var(--hr-border-color-lighter);
  --el-fill-color: var(--hr-fill-color);
  --el-fill-color-light: var(--hr-fill-color-light);
  --el-fill-color-lighter: var(--hr-fill-color-lighter);
  --el-fill-color-blank: var(--hr-fill-color-blank);

  // 品牌色调整
  --el-color-primary-light-3: rgba(64, 158, 255, 0.3);
  --el-color-primary-light-5: rgba(64, 158, 255, 0.2);
  --el-color-primary-light-7: rgba(64, 158, 255, 0.15);
  --el-color-primary-light-8: rgba(64, 158, 255, 0.12);
  --el-color-primary-light-9: rgba(64, 158, 255, 0.08);
}

// 暗黑模式 HTML 样式 mixin
@mixin dark-theme-html {
  color-scheme: dark;

  body {
    background-color: var(--hr-bg-color-page);
    color: var(--hr-text-color-primary);
  }

  // 滚动条样式
  ::-webkit-scrollbar {
    background-color: var(--hr-fill-color-lighter);
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--hr-fill-color-darker);

    &:hover {
      background-color: var(--hr-fill-color-dark);
    }
  }

  // 选中文本
  ::selection {
    background-color: rgba(64, 158, 255, 0.3);
    color: var(--hr-text-color-primary);
  }

  // 输入框
  input,
  textarea,
  select {
    background-color: var(--hr-fill-color);
    color: var(--hr-text-color-primary);

    &::placeholder {
      color: var(--hr-text-color-placeholder);
    }
  }

  // 链接
  a {
    color: #4b96ff;

    &:hover {
      color: #79b3ff;
    }
  }

  // 代码块
  code {
    background-color: var(--hr-fill-color-darker);
    color: #ff8c69;
  }

  pre {
    background-color: var(--hr-fill-color);
    border-color: var(--hr-border-color);
  }

  // 表格
  table {
    thead {
      background-color: var(--hr-fill-color-dark);
    }

    tbody {
      tr:hover {
        background-color: var(--hr-fill-color-light);
      }
    }
  }

  // 卡片
  .el-card {
    background-color: var(--hr-bg-color);
    border-color: var(--hr-border-color-lighter);

    .el-card__header {
      background-color: var(--hr-fill-color-lighter);
      border-color: var(--hr-border-color-lighter);
    }
  }

  // 对话框
  .el-dialog {
    background-color: var(--hr-bg-color);

    .el-dialog__header {
      border-color: var(--hr-border-color-lighter);
    }

    .el-dialog__footer {
      border-color: var(--hr-border-color-lighter);
    }
  }

  // 表单
  .el-form-item__label {
    color: var(--hr-text-color-regular);
  }

  // 输入框
  .el-input {
    .el-input__wrapper {
      background-color: var(--hr-fill-color);
      box-shadow: 0 0 0 1px var(--hr-border-color-lighter) inset;

      &:hover {
        box-shadow: 0 0 0 1px var(--hr-border-color) inset;
      }
    }

    &.is-focus .el-input__wrapper {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }

  // 下拉框
  .el-select-dropdown {
    background-color: var(--hr-bg-color-overlay);
    border-color: var(--hr-border-color-lighter);
  }

  // 日期选择器
  .el-picker__popper {
    background-color: var(--hr-bg-color-overlay);
    border-color: var(--hr-border-color-lighter);
  }

  // 按钮
  .el-button {
    &--default {
      background-color: var(--hr-fill-color);
      border-color: var(--hr-border-color);
      color: var(--hr-text-color-regular);

      &:hover,
      &:focus {
        background-color: var(--hr-fill-color-dark);
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }

  // 分页
  .el-pagination {
    .el-pager li {
      background-color: var(--hr-fill-color);
      color: var(--hr-text-color-regular);

      &:hover {
        color: var(--el-color-primary);
      }

      &.is-active {
        background-color: var(--el-color-primary);
        color: #fff;
      }
    }

    button {
      background-color: var(--hr-fill-color);
      color: var(--hr-text-color-regular);

      &:disabled {
        background-color: var(--hr-fill-color-lighter);
        color: var(--hr-text-color-disabled);
      }
    }
  }

  // 表格
  .el-table {
    background-color: transparent;

    &::before {
      background-color: var(--hr-border-color-lighter);
    }

    th.el-table__cell {
      background-color: var(--hr-fill-color-dark);
      color: var(--hr-text-color-primary);
    }

    tr {
      background-color: var(--hr-bg-color);

      &:hover > td.el-table__cell {
        background-color: var(--hr-fill-color-light);
      }
    }

    td.el-table__cell {
      border-color: var(--hr-border-color-extra-light);
    }

    &--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
      background-color: var(--hr-fill-color-lighter);
    }
  }

  // 菜单
  .el-menu {
    background-color: var(--hr-bg-color);
    border-color: var(--hr-border-color-lighter);

    .el-menu-item {
      color: var(--hr-text-color-regular);

      &:hover {
        background-color: var(--hr-fill-color-light);
        color: var(--el-color-primary);
      }

      &.is-active {
        background-color: rgba(64, 158, 255, 0.1);
        color: var(--el-color-primary);
      }
    }

    .el-sub-menu__title {
      color: var(--hr-text-color-regular);

      &:hover {
        background-color: var(--hr-fill-color-light);
        color: var(--el-color-primary);
      }
    }
  }

  // 标签
  .el-tag {
    &--plain {
      background-color: var(--hr-fill-color-lighter);
    }
  }

  // 进度条
  .el-progress {
    .el-progress-bar__outer {
      background-color: var(--hr-fill-color-darker);
    }
  }

  // 消息提示
  .el-message {
    background-color: var(--hr-bg-color-overlay);
    border-color: var(--hr-border-color-lighter);
    color: var(--hr-text-color-primary);

    .el-message__icon,
    .el-message__closeBtn {
      color: var(--hr-text-color-regular);
    }
  }

  // 通知
  .el-notification {
    background-color: var(--hr-bg-color-overlay);
    border-color: var(--hr-border-color-lighter);
    color: var(--hr-text-color-primary);
  }

  // 加载
  .el-loading-mask {
    background-color: rgba(0, 0, 0, 0.8);
  }

  // 统计卡片
  .stat-card {
    background-color: var(--hr-bg-color);

    .stat-icon {
      background-color: var(--hr-fill-color-dark);
    }
  }

  // 图表适配
  .chart-container {
    .echarts-tooltip {
      background-color: var(--hr-bg-color-overlay) !important;
      border-color: var(--hr-border-color) !important;
      color: var(--hr-text-color-primary) !important;
    }
  }
}

// 使用 mixins
:root[data-theme='dark'] {
  @include dark-theme-root;
}

html[data-theme='dark'] {
  @include dark-theme-html;
}

// 暗黑模式切换过渡
* {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}

// 排除不需要过渡的元素
.el-loading-mask,
.el-message,
.el-notification,
.el-tooltip__popper {
  transition: none;
}

// 系统主题跟随
@media (prefers-color-scheme: dark) {
  :root:not([data-theme='light']) {
    @include dark-theme-root;
  }

  html:not([data-theme='light']) {
    @include dark-theme-html;
  }
}
