/**
 * 浅色主题样式
 * 默认主题，定义在 variables.scss 中
 */

// 浅色主题特定样式
:root[data-theme='light'] {
  // 这里可以覆盖默认变量
  // 默认值已在 variables.scss 中定义
}

html[data-theme='light'] {
  color-scheme: light;

  // 特定组件的浅色主题样式
  // 统计卡片
  .stat-card {
    &:hover {
      box-shadow: var(--hr-shadow-light);
    }
  }

  // 代码块
  code {
    background-color: #f0f2f5;
    color: #e74c3c;
  }

  pre {
    background-color: #f8f9fa;
    border: 1px solid var(--hr-border-color-lighter);

    code {
      background-color: transparent;
    }
  }

  // 图表
  .chart-container {
    .echarts-tooltip {
      box-shadow: var(--hr-shadow-base);
    }
  }

  // 高亮文本
  mark {
    background-color: #fff3cd;
    color: var(--hr-text-color-primary);
  }

  // 引用块
  blockquote {
    border-left: 4px solid var(--hr-color-primary);
    background-color: var(--hr-fill-color-lighter);
    color: var(--hr-text-color-secondary);
  }

  // 键盘按键
  kbd {
    background-color: var(--hr-fill-color);
    border: 1px solid var(--hr-border-color);
    border-bottom: 2px solid var(--hr-border-color-dark);
    color: var(--hr-text-color-regular);
  }

  // 骨架屏
  .skeleton-loading {
    background: linear-gradient(
      90deg,
      var(--hr-fill-color-lighter) 25%,
      var(--hr-fill-color-light) 50%,
      var(--hr-fill-color-lighter) 75%
    );
  }

  // 水印
  .watermark {
    color: rgba(0, 0, 0, 0.05);
  }

  // 拖拽
  .draggable-ghost {
    opacity: 0.5;
    background-color: var(--hr-fill-color-light);
  }

  .draggable-drag {
    box-shadow: var(--hr-shadow-light);
  }

  // 选中状态
  .is-selected {
    background-color: var(--hr-color-primary-light-9);
    border-color: var(--hr-color-primary-light-5);
  }

  // 禁用状态
  .is-disabled {
    background-color: var(--hr-fill-color-lighter);
    color: var(--hr-text-color-disabled);
    cursor: not-allowed;
  }

  // 加载状态
  .is-loading {
    &::before {
      background-color: rgba(255, 255, 255, 0.9);
    }
  }

  // 空状态
  .empty-state {
    color: var(--hr-text-color-secondary);

    svg {
      color: var(--hr-fill-color-darker);
    }
  }

  // 徽标
  .badge {
    &--primary {
      background-color: var(--hr-color-primary);
      color: white;
    }

    &--success {
      background-color: var(--hr-color-success);
      color: white;
    }

    &--warning {
      background-color: var(--hr-color-warning);
      color: white;
    }

    &--danger {
      background-color: var(--hr-color-danger);
      color: white;
    }

    &--info {
      background-color: var(--hr-color-info);
      color: white;
    }
  }

  // 步骤条
  .process-step {
    &.is-active {
      color: var(--hr-color-primary);
      border-color: var(--hr-color-primary);
    }

    &.is-finished {
      color: var(--hr-color-success);
      border-color: var(--hr-color-success);
    }
  }

  // 时间线
  .timeline-item {
    &::before {
      background-color: var(--hr-border-color-lighter);
    }

    .timeline-dot {
      background-color: var(--hr-bg-color);
      border: 2px solid var(--hr-color-primary);
    }
  }

  // 日历
  .calendar {
    .is-today {
      background-color: var(--hr-color-primary-light-9);
      color: var(--hr-color-primary);
    }

    .is-selected {
      background-color: var(--hr-color-primary);
      color: white;
    }

    .is-weekend {
      color: var(--hr-color-danger);
    }
  }
}
