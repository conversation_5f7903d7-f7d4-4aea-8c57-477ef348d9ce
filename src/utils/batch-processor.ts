/**
 * 批量操作处理器
 * 用于优化大批量数据操作，避免阻塞UI
 */

import { ref } from 'vue'

/**
 * 批处理选项
 */
export interface BatchProcessOptions {
  /** 每批处理的数量 */
  chunkSize?: number
  /** 批次之间的延迟（毫秒） */
  delay?: number
  /** 是否在后台运行（使用 Web Worker） */
  useWorker?: boolean
  /** 进度回调 */
  onProgress?: (progress: BatchProgress) => void
  /** 错误处理 */

  onError?: (error: Error, item: unknown, index: number) => void
  /** 完成回调 */

  onComplete?: (results: unknown[]) => void
  /** 是否可以取消 */
  cancellable?: boolean
}

/**
 * 批处理进度
 */
export interface BatchProgress {
  /** 已处理数量 */
  processed: number
  /** 总数量 */
  total: number
  /** 百分比 (0-100) */
  percentage: number
  /** 当前批次 */
  currentChunk: number
  /** 总批次数 */
  totalChunks: number
  /** 预计剩余时间（毫秒） */
  estimatedTimeRemaining: number
  /** 处理速度（项/秒） */
  speed: number
}

/**
 * 批处理结果
 */
export interface BatchResult<T> {
  /** 成功的结果 */
  success: T[]
  /** 失败的项 */

  failed: Array<{ item: unknown; error: Error; index: number }>
  /** 总耗时（毫秒） */
  duration: number
  /** 是否被取消 */
  cancelled: boolean
}

/**
 * 批量处理器类
 */
export class BatchProcessor<T = unknown, R = unknown> {
  private cancelled = false
  private paused = false
  private startTime = 0
  private processedCount = 0

  constructor(private options: BatchProcessOptions = {}) {
    this.options = {
      chunkSize: 100,
      delay: 0,
      useWorker: false,
      cancellable: true,
      ...options
    }
  }

  /**
   * 处理数据
   */
  async process(
    data: T[],
    processor: (item: T, index: number) => Promise<R> | R
  ): Promise<BatchResult<R>> {
    this.cancelled = false
    this.paused = false
    this.startTime = Date.now()
    this.processedCount = 0

    const results: R[] = []
    const failed: Array<{ item: T; error: Error; index: number }> = []
    const chunks = this.createChunks(data)

    try {
      for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
        if (this.cancelled) break

        // 等待暂停结束
        while (this.paused && !this.cancelled) {
          await this.sleep(100)
        }

        const chunk = chunks[chunkIndex]

        // 处理当前批次
        const chunkResults = await Promise.allSettled(
          chunk.map(async (item, localIndex) => {
            const globalIndex = chunkIndex * this.options.chunkSize! + localIndex
            try {
              return await processor(item, globalIndex)
            } catch (__error) {
              const err = error as Error
              this.options.onError?.(err, item, globalIndex)
              failed.push({ item, error: err, index: globalIndex })
              throw err
            }
          })
        )

        // 收集结果
        chunkResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            results.push(result.value)
            this.processedCount++
          }
        })

        // 更新进度
        this.reportProgress(data.length, chunks.length, chunkIndex + 1)

        // 批次间延迟
        if (this.options.delay && chunkIndex < chunks.length - 1) {
          await this.sleep(this.options.delay)
        }
      }

      const duration = Date.now() - this.startTime

      // 完成回调
      this.options.onComplete?.(results)

      return {
        success: results,
        failed,
        duration,
        cancelled: this.cancelled
      }
    } catch (__error) {
      throw error
    }
  }

  /**
   * 并行处理（限制并发数）
   */
  async processParallel(
    data: T[],
    processor: (item: T, index: number) => Promise<R> | R,
    concurrency = 5
  ): Promise<BatchResult<R>> {
    this.cancelled = false
    this.startTime = Date.now()
    this.processedCount = 0

    const results: R[] = []
    const failed: Array<{ item: T; error: Error; index: number }> = []
    const executing: Promise<void>[] = []

    for (let i = 0; i < data.length; i++) {
      if (this.cancelled) break

      const item = data[i]
      const index = i

      const promise = (async () => {
        try {
          const result = await processor(item, index)
          results[index] = result
          this.processedCount++
          this.reportProgress(data.length, 1, 1)
        } catch (__error) {
          const err = error as Error
          this.options.onError?.(err, item, index)
          failed.push({ item, error: err, index })
        }
      })()

      executing.push(promise)

      // 限制并发数
      if (executing.length >= concurrency) {
        await Promise.race(executing)
        executing.splice(
          executing.findIndex(p => p === promise),
          1
        )
      }
    }

    // 等待所有任务完成
    await Promise.all(executing)

    const duration = Date.now() - this.startTime

    this.options.onComplete?.(results)

    return {
      success: results.filter(r => r !== undefined),
      failed,
      duration,
      cancelled: this.cancelled
    }
  }

  /**
   * 使用 Web Worker 处理
   */
  async processWithWorker(data: T[], workerScript: string): Promise<BatchResult<R>> {
    return new Promise((resolve, reject) => {
      const worker = new Worker(workerScript)
      const results: R[] = []
      const failed: Array<{ item: T; error: Error; index: number }> = []

      this.startTime = Date.now()

      worker.postMessage({ type: 'process', data, options: this.options })

      worker.onmessage = __event => {
        const { type, payload: _payload } = event.data

        switch (type) {
          case 'progress':
            this.options.onProgress?.(payload)
            break

          case 'result':
            results.push(payload.result)
            break

          case 'error':
            failed.push(payload)
            this.options.onError?.(payload.error, payload.item, payload.index)
            break

          case 'complete':
            const duration = Date.now() - this.startTime
            worker.terminate()
            resolve({
              success: results,
              failed,
              duration,
              cancelled: this.cancelled
            })
            break
        }
      }

      worker.onerror = error => {
        worker.terminate()
        reject(error)
      }
    })
  }

  /**
   * 创建批次
   */
  private createChunks(data: T[]): T[][] {
    const chunks: T[][] = []
    const chunkSize = this.options.chunkSize || 100

    for (let i = 0; i < data.length; i += chunkSize) {
      chunks.push(data.slice(i, i + chunkSize))
    }

    return chunks
  }

  /**
   * 报告进度
   */
  private reportProgress(total: number, totalChunks: number, currentChunk: number) {
    const percentage = Math.round((this.processedCount / total) * 100)
    const elapsed = Date.now() - this.startTime
    const speed = this.processedCount / (elapsed / 1000)
    const remaining = total - this.processedCount
    const estimatedTimeRemaining = (remaining / speed) * 1000

    const progress: BatchProgress = {
      processed: this.processedCount,
      total,
      percentage,
      currentChunk,
      totalChunks,
      estimatedTimeRemaining,
      speed
    }

    this.options.onProgress?.(progress)
  }

  /**
   * 延迟函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * 创建批处理器的便捷函数
 */
export function createBatchProcessor<T = unknown, R = unknown>(
  options?: BatchProcessOptions
): BatchProcessor<T, R> {
  return new BatchProcessor<T, R>(options)
}

/**
 * 使用批处理的 Composable
 */
export function useBatchProcessor<T = unknown, R = unknown>(options?: BatchProcessOptions) {
  const progress = ref<BatchProgress>({
    processed: 0,
    total: 0,
    percentage: 0,
    currentChunk: 0,
    totalChunks: 0,
    estimatedTimeRemaining: 0,
    speed: 0
  })

  const processing = ref(false)
  const error = ref<Error | null>(null)

  const processor = new BatchProcessor<T, R>({
    ...options,
    onProgress: _p => {
      progress.value = p
      options?.onProgress?.(p)
    },
    onError: (err, item, index) => {
      error.value = err
      options?.onError?.(err, item, index)
    }
  })

  const process = async (data: T[], handler: (item: T, index: number) => Promise<R> | R) => {
    processing.value = true
    error.value = null

    try {
      const result = await processor.process(data, handler)
      return result
    } catch (__err) {
      error.value = err as Error
      throw err
    } finally {
      processing.value = false
    }
  }

  const processParallel = async (
    data: T[],
    handler: (item: T, index: number) => Promise<R> | R,
    concurrency = 5
  ) => {
    processing.value = true
    error.value = null

    try {
      const result = await processor.processParallel(data, handler, concurrency)
      return result
    } catch (__err) {
      error.value = err as Error
      throw err
    } finally {
      processing.value = false
    }
  }

  return {
    progress,
    processing,
    error,
    process,
    processParallel,
    cancel: () => processor.cancel(),
    pause: () => processor.pause(),
    resume: () => processor.resume()
  }
}

/**
 * 批量操作示例
 */

// 批量更新员工状态
export async function batchUpdateEmployeeStatus(
  employees: unknown[],
  status: string
): Promise<BatchResult<unknown>> {
  const processor = createBatchProcessor({
    chunkSize: 50,
    delay: 100,
    onProgress: _progress => {}
  })

  return processor.process(employees, async _employee => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 50))
    return { ...employee, status }
  })
}

// 批量导入数据
export async function batchImportData(
  data: unknown[],
  validator: (item: unknown) => boolean,

  importer: (item: unknown) => Promise<unknown>
): Promise<BatchResult<unknown>> {
  const processor = createBatchProcessor({
    chunkSize: 100,
    delay: 0,
    onError: (error, item, index) => {}
  })

  return processor.processParallel(
    data,
    async (item, index) => {
      // 验证数据
      if (!validator(item)) {
        throw new Error(`Validation failed for row ${index}`)
      }

      // 导入数据
      return importer(item)
    },
    10
  ) // 10个并发
}

// 批量删除
export async function batchDelete(
  ids: string[],
  deleteApi: (id: string) => Promise<void>
): Promise<BatchResult<void>> {
  const processor = createBatchProcessor<string, void>({
    chunkSize: 20,
    delay: 200,
    cancellable: true
  })

  return processor.process(ids, async _id => {
    await deleteApi(id)
  })
}
