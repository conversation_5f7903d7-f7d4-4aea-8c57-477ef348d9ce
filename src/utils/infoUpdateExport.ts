interface InfoUpdateRecord {
  id: string
  updateType:
    | 'personal'
    | 'contact'
    | 'family'
    | 'education'
    | 'work'
    | 'emergency'
    | 'bank'
    | 'other'
  employeeId: string
  employeeName: string
  departmentId: string
  departmentName: string
  position: string
  updateFields: UpdateField[]
  updateReason: string
  updateSource: 'self' | 'hr' | 'system' | 'import'
  status: 'pending' | 'reviewing' | 'approved' | 'rejected' | 'implemented'
  submissionTime: string
  reviewTime?: string
  implementTime?: string
  reviewerId?: string
  reviewerName?: string
  reviewComments?: string
  attachments?: FileAttachment[]
  batchId?: string
  urgency: 'low' | 'medium' | 'high' | 'urgent'
  businessImpact: 'none' | 'low' | 'medium' | 'high'
  auditTrail: AuditRecord[]
  createTime: string
  updateTime: string
}

interface UpdateField {
  fieldName: string
  fieldLabel: string
  fieldCategory: string
  oldValue: string | number | boolean | null
  newValue: string | number | boolean | null
  changeType: 'create' | 'update' | 'delete'
  validationStatus: 'pending' | 'passed' | 'failed'
  validationMessage?: string
  requiresApproval: boolean
  dataSource?: string
}

interface AuditRecord {
  id: string
  action: 'submit' | 'review' | 'approve' | 'reject' | 'implement' | 'rollback'
  operatorId: string
  operatorName: string
  operatorRole: string
  timestamp: string
  details?: string
  ipAddress?: string
  userAgent?: string
}

interface FileAttachment {
  id: string
  fileName: string
  fileSize: number
  fileType: string
  uploadTime: string
  downloadUrl?: string
  description?: string
}

interface InfoUpdateExportFilter {
  employeeName?: string
  employeeId?: string
  departmentIds?: string[]
  updateTypes?: string[]
  status?: string[]
  updateSource?: string[]
  dateRange?: {
    start: string
    end: string
    type: 'submission' | 'review' | 'implement'
  }
  urgency?: string[]
  businessImpact?: string[]
  reviewerId?: string
  batchId?: string
  hasAttachments?: boolean
}

interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf'
  includeAuditTrail: boolean
  includeAttachments: boolean
  includeFieldDetails: boolean
  groupBy?: 'employee' | 'department' | 'type' | 'status' | 'batch'
  template?: string
  summaryLevel: 'none' | 'basic' | 'detailed'
  columns?: string[]
}

interface ExportResult {
  success: boolean
  blob?: Blob
  filename: string
  recordCount: number
  updatesSummary?: {
    totalUpdates: number
    totalFields: number
    byType: Record<string, number>
    byStatus: Record<string, number>
    byDepartment: Record<string, number>
    byUrgency: Record<string, number>
  }
  error?: string
}

// 导出数据项类型
interface ExportDataItem {
  [key: string]: string | number | boolean | undefined
  员工姓名?: string
  员工工号?: string
  部门?: string
  职位?: string
  更新类型?: string
  状态?: string
  提交时间?: string
  审核时间?: string
  实施时间?: string
  审核人?: string
  紧急程度?: string
  业务影响?: string
  变更类型?: string
  验证状态?: string
}

// 汇总数据类型
interface SummaryData {
  项目: string
  数值: string | number
}

/**
 * CLEAN-AUX-010: 信息更新导出功能
 * 支持多条件筛选和多格式导出的员工信息更新记录管理系统
 */
export class InfoUpdateExport {
  private updateRecords: Map<string, InfoUpdateRecord> = new Map()

  // 预定义更新类型
  private readonly updateTypes = [
    {
      value: 'personal',
      label: '个人基本信息',
      fields: ['姓名', '身份证号', '性别', '出生日期', '民族', '政治面貌']
    },
    { value: 'contact', label: '联系方式', fields: ['手机号码', '邮箱', '现住址', '户籍地址'] },
    { value: 'family', label: '家庭情况', fields: ['婚姻状况', '家庭成员', '紧急联系人'] },
    {
      value: 'education',
      label: '教育经历',
      fields: ['学历', '学位', '毕业院校', '专业', '毕业时间']
    },
    { value: 'work', label: '工作经历', fields: ['工作单位', '职务', '起止时间', '工作内容'] },
    { value: 'emergency', label: '紧急联系信息', fields: ['紧急联系人', '关系', '联系电话'] },
    { value: 'bank', label: '银行信息', fields: ['开户银行', '银行账号', '户名'] },
    { value: 'other', label: '其他信息', fields: ['备注', '特殊说明'] }
  ]

  // 状态标签映射
  private readonly statusLabels = {
    pending: '待审核',
    reviewing: '审核中',
    approved: '已批准',
    rejected: '已拒绝',
    implemented: '已实施'
  }

  // 更新来源映射
  private readonly sourceLabels = {
    self: '员工自主更新',
    hr: '人事部门更新',
    system: '系统自动更新',
    import: '批量导入更新'
  }

  // 紧急程度映射
  private readonly urgencyLabels = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }

  // 业务影响程度映射
  private readonly impactLabels = {
    none: '无影响',
    low: '轻微影响',
    medium: '中等影响',
    high: '重大影响'
  }

  constructor() {
    this.initializeMockData()
  }

  // 初始化模拟数据
  private initializeMockData() {
    const mockRecords: InfoUpdateRecord[] = [
      {
        id: 'update_001',
        updateType: 'contact',
        employeeId: 'emp_001',
        employeeName: '张三',
        departmentId: 'dept_001',
        departmentName: '技术部',
        position: '软件工程师',
        updateFields: [
          {
            fieldName: 'phoneNumber',
            fieldLabel: '手机号码',
            fieldCategory: '联系方式',
            oldValue: '13812345678',
            newValue: '13987654321',
            changeType: 'update',
            validationStatus: 'passed',
            requiresApproval: false
          },
          {
            fieldName: 'email',
            fieldLabel: '邮箱地址',
            fieldCategory: '联系方式',
            oldValue: '<EMAIL>',
            newValue: '<EMAIL>',
            changeType: 'update',
            validationStatus: 'passed',
            requiresApproval: false
          }
        ],
        updateReason: '联系方式变更',
        updateSource: 'self',
        status: 'approved',
        submissionTime: '2025-01-10T09:00:00.000Z',
        reviewTime: '2025-01-10T14:30:00.000Z',
        implementTime: '2025-01-10T15:00:00.000Z',
        reviewerId: 'hr_001',
        reviewerName: '李人事',
        reviewComments: '信息核实无误，同意更新',
        urgency: 'medium',
        businessImpact: 'low',
        auditTrail: [
          {
            id: 'audit_001',
            action: 'submit',
            operatorId: 'emp_001',
            operatorName: '张三',
            operatorRole: '员工',
            timestamp: '2025-01-10T09:00:00.000Z',
            details: '提交联系方式变更申请',
            ipAddress: '*************'
          },
          {
            id: 'audit_002',
            action: 'approve',
            operatorId: 'hr_001',
            operatorName: '李人事',
            operatorRole: '人事专员',
            timestamp: '2025-01-10T14:30:00.000Z',
            details: '审核通过，同意变更',
            ipAddress: '*************'
          }
        ],
        createTime: '2025-01-10T09:00:00.000Z',
        updateTime: '2025-01-10T15:00:00.000Z'
      },
      {
        id: 'update_002',
        updateType: 'education',
        employeeId: 'emp_002',
        employeeName: '李四',
        departmentId: 'dept_002',
        departmentName: '人事部',
        position: '人事专员',
        updateFields: [
          {
            fieldName: 'education',
            fieldLabel: '最高学历',
            fieldCategory: '教育信息',
            oldValue: '本科',
            newValue: '硕士',
            changeType: 'update',
            validationStatus: 'passed',
            requiresApproval: true
          },
          {
            fieldName: 'graduateSchool',
            fieldLabel: '毕业院校',
            fieldCategory: '教育信息',
            oldValue: '某某大学',
            newValue: '某某研究生院',
            changeType: 'update',
            validationStatus: 'passed',
            requiresApproval: true
          }
        ],
        updateReason: '学历提升，获得硕士学位',
        updateSource: 'self',
        status: 'pending',
        submissionTime: '2025-01-12T10:00:00.000Z',
        attachments: [
          {
            id: 'att_001',
            fileName: '硕士学位证书.pdf',
            fileSize: 3145728,
            fileType: 'application/pdf',
            uploadTime: '2025-01-12T10:05:00.000Z',
            description: '硕士学位证书扫描件'
          }
        ],
        urgency: 'high',
        businessImpact: 'medium',
        auditTrail: [
          {
            id: 'audit_003',
            action: 'submit',
            operatorId: 'emp_002',
            operatorName: '李四',
            operatorRole: '员工',
            timestamp: '2025-01-12T10:00:00.000Z',
            details: '提交学历信息更新申请',
            ipAddress: '*************'
          }
        ],
        createTime: '2025-01-12T10:00:00.000Z',
        updateTime: '2025-01-12T10:00:00.000Z'
      }
    ]

    mockRecords.forEach(record => {
      this.updateRecords.set(record.id, record)
    })
  }

  // 导出信息更新记录
  async exportInfoUpdates(
    filter: InfoUpdateExportFilter = {},
    options: ExportOptions = {
      format: 'excel',
      includeAuditTrail: true,
      includeAttachments: false,
      includeFieldDetails: true,
      summaryLevel: 'basic'
    }
  ): Promise<ExportResult> {
    try {
      // 筛选更新记录
      const filteredRecords = this.filterUpdateRecords(filter)

      if (filteredRecords.length === 0) {
        return {
          success: false,
          filename: '',
          recordCount: 0,
          error: '没有符合条件的信息更新记录'
        }
      }

      // 生成导出数据
      const exportData = this.prepareExportData(filteredRecords, options)

      // 计算汇总信息
      const summary = this.calculateSummary(filteredRecords)

      // 根据格式生成文件
      let blob: Blob
      let filename: string

      switch (options.format) {
        case 'excel':
          blob = await this.generateExcelFile(exportData, summary, options)
          filename = this.generateFilename('信息更新记录', 'xlsx')
          break
        case 'csv':
          blob = this.generateCsvFile(exportData)
          filename = this.generateFilename('信息更新记录', 'csv')
          break
        case 'pdf':
          blob = await this.generatePdfFile(exportData, summary, options)
          filename = this.generateFilename('信息更新记录', 'pdf')
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }

      return {
        success: true,
        blob,
        filename,
        recordCount: filteredRecords.length,
        updatesSummary: summary
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        recordCount: 0,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  // 导出字段变更明细
  async exportFieldChanges(
    filter: InfoUpdateExportFilter = {},
    options: ExportOptions = {
      format: 'excel',
      includeAuditTrail: false,
      includeAttachments: false,
      includeFieldDetails: true,
      summaryLevel: 'detailed'
    }
  ): Promise<ExportResult> {
    try {
      const filteredRecords = this.filterUpdateRecords(filter)

      if (filteredRecords.length === 0) {
        return {
          success: false,
          filename: '',
          recordCount: 0,
          error: '没有符合条件的字段变更记录'
        }
      }

      // 生成字段变更明细数据
      const fieldChangeData = this.prepareFieldChangeData(filteredRecords, options)

      let blob: Blob
      let filename: string

      switch (options.format) {
        case 'excel':
          blob = await this.generateFieldChangeExcelFile(fieldChangeData, options)
          filename = this.generateFilename('字段变更明细', 'xlsx')
          break
        case 'csv':
          blob = this.generateFieldChangeCsvFile(fieldChangeData)
          filename = this.generateFilename('字段变更明细', 'csv')
          break
        case 'pdf':
          blob = await this.generateFieldChangePdfFile(fieldChangeData, options)
          filename = this.generateFilename('字段变更明细', 'pdf')
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }

      return {
        success: true,
        blob,
        filename,
        recordCount: fieldChangeData.length
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        recordCount: 0,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  // 筛选更新记录
  private filterUpdateRecords(filter: InfoUpdateExportFilter): InfoUpdateRecord[] {
    let records = Array.from(this.updateRecords.values())

    // 按员工姓名筛选
    if (filter.employeeName) {
      const keyword = filter.employeeName.toLowerCase()
      records = records.filter(record => record.employeeName.toLowerCase().includes(keyword))
    }

    // 按员工工号筛选
    if (filter.employeeId) {
      records = records.filter(record => record.employeeId.includes(filter.employeeId!))
    }

    // 按部门筛选
    if (filter.departmentIds && filter.departmentIds.length > 0) {
      records = records.filter(record => filter.departmentIds!.includes(record.departmentId))
    }

    // 按更新类型筛选
    if (filter.updateTypes && filter.updateTypes.length > 0) {
      records = records.filter(record => filter.updateTypes!.includes(record.updateType))
    }

    // 按状态筛选
    if (filter.status && filter.status.length > 0) {
      records = records.filter(record => filter.status!.includes(record.status))
    }

    // 按更新来源筛选
    if (filter.updateSource && filter.updateSource.length > 0) {
      records = records.filter(record => filter.updateSource!.includes(record.updateSource))
    }

    // 按紧急程度筛选
    if (filter.urgency && filter.urgency.length > 0) {
      records = records.filter(record => filter.urgency!.includes(record.urgency))
    }

    // 按业务影响筛选
    if (filter.businessImpact && filter.businessImpact.length > 0) {
      records = records.filter(record => filter.businessImpact!.includes(record.businessImpact))
    }

    // 按审核人筛选
    if (filter.reviewerId) {
      records = records.filter(record => record.reviewerId === filter.reviewerId)
    }

    // 按批次ID筛选
    if (filter.batchId) {
      records = records.filter(record => record.batchId === filter.batchId)
    }

    // 按是否有附件筛选
    if (filter.hasAttachments !== undefined) {
      records = records.filter(record =>
        filter.hasAttachments
          ? record.attachments && record.attachments.length > 0
          : !record.attachments || record.attachments.length === 0
      )
    }

    // 按日期范围筛选
    if (filter.dateRange) {
      const { start, end, type } = filter.dateRange
      records = records.filter(record => {
        let dateToCompare: string
        switch (type) {
          case 'submission':
            dateToCompare = record.submissionTime
            break
          case 'review':
            dateToCompare = record.reviewTime || ''
            break
          case 'implement':
            dateToCompare = record.implementTime || ''
            break
          default:
            dateToCompare = record.submissionTime
        }
        return dateToCompare >= start && dateToCompare <= end
      })
    }

    return records
  }

  // 准备导出数据
  private prepareExportData(records: InfoUpdateRecord[], options: ExportOptions): ExportDataItem[] {
    return records.map(record => ({
      员工姓名: record.employeeName,
      员工工号: record.employeeId,
      部门: record.departmentName,
      职位: record.position,
      更新类型: this.getUpdateTypeLabel(record.updateType),
      更新来源: this.sourceLabels[record.updateSource],
      状态: this.statusLabels[record.status],
      提交时间: new Date(record.submissionTime).toLocaleString('zh-CN'),
      审核时间: record.reviewTime ? new Date(record.reviewTime).toLocaleString('zh-CN') : '',
      实施时间: record.implementTime ? new Date(record.implementTime).toLocaleString('zh-CN') : '',
      审核人: record.reviewerName || '',
      紧急程度: this.urgencyLabels[record.urgency],
      业务影响: this.impactLabels[record.businessImpact],
      更新原因: record.updateReason,
      审核意见: record.reviewComments || '',
      附件数量: record.attachments ? record.attachments.length : 0
    }))
  }

  // 准备字段变更数据
  private prepareFieldChangeData(
    records: InfoUpdateRecord[],
    options: ExportOptions
  ): ExportDataItem[] {
    const fieldChangeData: ExportDataItem[] = []

    records.forEach(record => {
      record.updateFields.forEach(field => {
        fieldChangeData.push({
          员工姓名: record.employeeName,
          员工工号: record.employeeId,
          部门: record.departmentName,
          更新类型: this.getUpdateTypeLabel(record.updateType),
          字段名称: field.fieldLabel,
          字段分类: field.fieldCategory,
          原值: String(field.oldValue || ''),
          新值: String(field.newValue || ''),
          变更类型:
            field.changeType === 'create'
              ? '新增'
              : field.changeType === 'update'
                ? '修改'
                : '删除',
          验证状态:
            field.validationStatus === 'passed'
              ? '通过'
              : field.validationStatus === 'failed'
                ? '失败'
                : '待验证',
          需要审批: field.requiresApproval ? '是' : '否',
          提交时间: new Date(record.submissionTime).toLocaleString('zh-CN'),
          状态: this.statusLabels[record.status]
        })
      })
    })

    return fieldChangeData
  }

  // 计算汇总信息
  private calculateSummary(records: InfoUpdateRecord[]) {
    const totalUpdates = records.length
    const totalFields = records.reduce((sum, record) => sum + record.updateFields.length, 0)

    const byType: Record<string, number> = {}
    const byStatus: Record<string, number> = {}
    const byDepartment: Record<string, number> = {}
    const byUrgency: Record<string, number> = {}

    records.forEach(record => {
      // 按类型统计
      const typeLabel = this.getUpdateTypeLabel(record.updateType)
      byType[typeLabel] = (byType[typeLabel] || 0) + 1

      // 按状态统计
      const statusLabel = this.statusLabels[record.status]
      byStatus[statusLabel] = (byStatus[statusLabel] || 0) + 1

      // 按部门统计
      byDepartment[record.departmentName] = (byDepartment[record.departmentName] || 0) + 1

      // 按紧急程度统计
      const urgencyLabel = this.urgencyLabels[record.urgency]
      byUrgency[urgencyLabel] = (byUrgency[urgencyLabel] || 0) + 1
    })

    return {
      totalUpdates,
      totalFields,
      byType,
      byStatus,
      byDepartment,
      byUrgency
    }
  }

  // 生成Excel文件
  private async generateExcelFile(
    data: ExportDataItem[],
    summary: ReturnType<typeof this.calculateSummary>,
    options: ExportOptions
  ): Promise<Blob> {
    // 这里应该使用Excel库，如ExcelJS
    // 暂时返回CSV格式
    const csvContent = this.arrayToCsv(data)
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'application/vnd.ms-excel;charset=utf-8' })
  }

  // 生成CSV文件
  private generateCsvFile(data: ExportDataItem[]): Blob {
    const csvContent = this.arrayToCsv(data)
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
  }

  // 生成字段变更Excel文件
  private async generateFieldChangeExcelFile(
    data: ExportDataItem[],
    options: ExportOptions
  ): Promise<Blob> {
    // 暂时返回CSV格式
    return this.generateFieldChangeCsvFile(data)
  }

  // 生成字段变更CSV文件
  private generateFieldChangeCsvFile(data: ExportDataItem[]): Blob {
    const csvContent = this.arrayToCsv(data)
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
  }

  // 数组转CSV
  private arrayToCsv(data: ExportDataItem[]): string {
    if (data.length === 0) return ''

    const headers = Object.keys(data[0])
    const rows = data.map(item =>
      headers.map(header => `"${String(item[header] || '').replace(/"/g, '""')}"`)
    )

    return [headers.map(h => `"${h}"`).join(','), ...rows.map(row => row.join(','))].join('\n')
  }

  // 生成PDF文件
  private async generatePdfFile(
    data: ExportDataItem[],
    summary: ReturnType<typeof this.calculateSummary>,
    options: ExportOptions
  ): Promise<Blob> {
    const pdfContent = {
      title: '信息更新记录报表',
      data,
      summary,
      options,
      generateTime: new Date().toISOString()
    }

    return new Blob([JSON.stringify(pdfContent)], { type: 'application/pdf' })
  }

  // 生成字段变更PDF文件
  private async generateFieldChangePdfFile(
    data: ExportDataItem[],
    options: ExportOptions
  ): Promise<Blob> {
    const pdfContent = {
      title: '字段变更明细报表',
      data,
      options,
      generateTime: new Date().toISOString()
    }

    return new Blob([JSON.stringify(pdfContent)], { type: 'application/pdf' })
  }

  // 生成汇总表格
  private generateSummarySheet(summary: ReturnType<typeof this.calculateSummary>): SummaryData[] {
    return [
      {
        项目: '总更新记录数',
        数值: summary.totalUpdates
      },
      {
        项目: '总更新字段数',
        数值: summary.totalFields
      },
      {
        项目: '平均每次更新字段数',
        数值:
          summary.totalUpdates > 0
            ? Math.round((summary.totalFields / summary.totalUpdates) * 100) / 100
            : 0
      },
      ...Object.entries(summary.byType).map(([type, count]) => ({
        项目: `${type}更新数`,
        数值: count
      })),
      ...Object.entries(summary.byStatus).map(([status, count]) => ({
        项目: `${status}记录数`,
        数值: count
      }))
    ]
  }

  // 计算字段变更统计
  private calculateFieldChangeStatistics(data: ExportDataItem[]): Record<string, string | number> {
    const totalChanges = data.length
    const updateCount = data.filter(item => item['变更类型'] === '修改').length
    const createCount = data.filter(item => item['变更类型'] === '新增').length
    const deleteCount = data.filter(item => item['变更类型'] === '删除').length
    const passedCount = data.filter(item => item['验证状态'] === '通过').length

    return {
      总变更数: totalChanges,
      修改变更: updateCount,
      新增变更: createCount,
      删除变更: deleteCount,
      验证通过: passedCount,
      验证通过率: totalChanges > 0 ? `${Math.round((passedCount / totalChanges) * 100)}%` : '0%'
    }
  }

  // 获取更新类型标签
  private getUpdateTypeLabel(type: string): string {
    const typeConfig = this.updateTypes.find(t => t.value === type)
    return typeConfig ? typeConfig.label : type
  }

  // 生成文件名
  private generateFilename(prefix: string, extension: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19)
    return `${prefix}_${timestamp}.${extension}`
  }

  // 获取更新类型列表
  getUpdateTypes() {
    return this.updateTypes
  }

  // 获取状态标签
  getStatusLabels() {
    return this.statusLabels
  }

  // 获取来源标签
  getSourceLabels() {
    return this.sourceLabels
  }

  // 添加更新记录
  addUpdateRecord(record: InfoUpdateRecord) {
    this.updateRecords.set(record.id, record)
  }

  // 获取更新统计
  getUpdateStatistics() {
    const records = Array.from(this.updateRecords.values())
    return this.calculateSummary(records)
  }

  // 获取字段变更统计
  getFieldChangeStatistics() {
    const records = Array.from(this.updateRecords.values())
    const fieldData = this.prepareFieldChangeData(records, {
      format: 'excel',
      includeAuditTrail: false,
      includeAttachments: false,
      includeFieldDetails: true,
      summaryLevel: 'basic'
    })
    return this.calculateFieldChangeStatistics(fieldData)
  }
}

// 全局实例
export const infoUpdateExporter = new InfoUpdateExport()

// 便捷函数
export async function exportInfoUpdates(
  filter?: InfoUpdateExportFilter,
  options?: Partial<ExportOptions>
): Promise<ExportResult> {
  return infoUpdateExporter.exportInfoUpdates(filter, {
    format: 'excel',
    includeAuditTrail: true,
    includeAttachments: false,
    includeFieldDetails: true,
    summaryLevel: 'basic',
    ...options
  })
}

export async function exportFieldChanges(
  filter?: InfoUpdateExportFilter,
  options?: Partial<ExportOptions>
): Promise<ExportResult> {
  return infoUpdateExporter.exportFieldChanges(filter, {
    format: 'excel',
    includeAuditTrail: false,
    includeAttachments: false,
    includeFieldDetails: true,
    summaryLevel: 'detailed',
    ...options
  })
}
