/**
 * @name API缓存监控和调试工具
 * @description 提供缓存性能监控、调试信息和开发工具
 */
import { globalApiCache } from './apiCache'
import type { CacheStats } from './apiCache'

// 扩展Performance接口以支持memory属性
interface PerformanceWithMemory extends Performance {
  memory?: {
    jsHeapSizeLimit: number
    totalJSHeapSize: number
    usedJSHeapSize: number
  }
}

// 扩展Window接口以支持调试工具
interface WindowWithDebugger extends Window {
  cacheDebugger?: typeof CacheDebugger
  cacheMonitor?: CacheMonitor
}

// ===== 监控数据接口 =====
interface CacheMetrics {
  /** 时间戳 */
  timestamp: number
  /** 缓存统计 */
  stats: CacheStats
  /** 内存使用情况 */
  memoryUsage?: {
    total: number
    used: number
    percentage: number
  }
}

interface CacheEvent {
  /** 事件类型 */
  type: 'hit' | 'miss' | 'set' | 'delete' | 'clear' | 'expire'
  /** 时间戳 */
  timestamp: number
  /** 缓存键 */
  key?: string
  /** 额外数据 */
  data?: unknown
}

// ===== 监控器类 =====
export class CacheMonitor {
  private metrics: CacheMetrics[] = []
  private events: CacheEvent[] = []
  private maxMetricsHistory = 100
  private maxEventsHistory = 500
  private monitoringInterval: number | null = null
  private isMonitoring = false

  constructor() {
    // 在开发环境下自动启用监控
    if (import.meta.env.DEV) {
      this.startMonitoring()
    }

    // 监听页面可见性变化
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible' && !this.isMonitoring) {
          this.startMonitoring()
        } else if (document.visibilityState === 'hidden' && this.isMonitoring) {
          this.stopMonitoring()
        }
      })
    }
  }

  /**
   * 开始监控
   */
  startMonitoring(interval = 5000): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.collectMetrics()

    this.monitoringInterval = window.setInterval(() => {
      this.collectMetrics()
    }, interval)

    console.debug('API缓存监控已启动', { interval })
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
    this.isMonitoring = false
    console.debug('API缓存监控已停止')
  }

  /**
   * 收集监控指标
   */
  private collectMetrics(): void {
    const stats = globalApiCache.getStats()
    const memoryUsage = this.getMemoryUsage()

    const metric: CacheMetrics = {
      timestamp: Date.now(),
      stats,
      memoryUsage
    }

    this.metrics.push(metric)

    // 限制历史记录数量
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics.shift()
    }

    // 检查异常情况
    this.checkAnomalies(metric)
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage() {
    const perfWithMemory = performance as PerformanceWithMemory
    if (typeof performance !== 'undefined' && perfWithMemory.memory) {
      const memory = perfWithMemory.memory
      return {
        total: memory.jsHeapSizeLimit,
        used: memory.usedJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      }
    }
    return undefined
  }

  /**
   * 检查异常情况
   */
  private checkAnomalies(metric: CacheMetrics): void {
    const { stats, memoryUsage } = metric

    // 检查命中率过低
    if (stats.hitRate < 30 && stats.hits + stats.misses > 10) {
      console.warn('缓存命中率过低:', stats.hitRate.toFixed(2) + '%', {
        hits: stats.hits,
        misses: stats.misses,
        size: stats.size
      })
    }

    // 检查缓存大小过大
    if (stats.size > 80) {
      console.warn('缓存条目数量过多:', stats.size)
    }

    // 检查内存使用过高
    if (memoryUsage && memoryUsage.percentage > 80) {
      console.warn('内存使用率过高:', memoryUsage.percentage.toFixed(2) + '%')
    }
  }

  /**
   * 记录缓存事件
   */
  recordEvent(event: Omit<CacheEvent, 'timestamp'>): void {
    const fullEvent: CacheEvent = {
      ...event,
      timestamp: Date.now()
    }

    this.events.push(fullEvent)

    // 限制事件历史记录
    if (this.events.length > this.maxEventsHistory) {
      this.events.shift()
    }

    // 在开发环境下打印详细事件
    if (import.meta.env.DEV && event.type !== 'hit') {
      console.debug('缓存事件:', fullEvent)
    }
  }

  /**
   * 获取最新的监控指标
   */
  getLatestMetrics(): CacheMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null
  }

  /**
   * 获取历史监控指标
   */
  getMetricsHistory(limit?: number): CacheMetrics[] {
    if (limit) {
      return this.metrics.slice(-limit)
    }
    return [...this.metrics]
  }

  /**
   * 获取缓存事件历史
   */
  getEventsHistory(limit?: number): CacheEvent[] {
    if (limit) {
      return this.events.slice(-limit)
    }
    return [...this.events]
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const latest = this.getLatestMetrics()
    if (!latest) {
      return { error: '暂无监控数据' }
    }

    const { stats } = latest
    const history = this.getMetricsHistory(10)
    const events = this.getEventsHistory(50)

    // 计算趋势
    const hitRateTrend = this.calculateTrend(history.map(m => m.stats.hitRate))
    const sizeTrend = this.calculateTrend(history.map(m => m.stats.size))

    // 统计事件类型
    const eventCounts = events.reduce(
      (counts, event) => {
        counts[event.type] = (counts[event.type] || 0) + 1
        return counts
      },
      {} as Record<string, number>
    )

    return {
      current: {
        hitRate: stats.hitRate,
        size: stats.size,
        hits: stats.hits,
        misses: stats.misses
      },
      trends: {
        hitRate: hitRateTrend,
        size: sizeTrend
      },
      events: eventCounts,
      health: this.assessHealth(stats),
      recommendations: this.generateRecommendations(stats, history)
    }
  }

  /**
   * 计算趋势
   */
  private calculateTrend(values: number[]): 'up' | 'down' | 'stable' {
    if (values.length < 2) return 'stable'

    const recent = values.slice(-3)
    const earlier = values.slice(-6, -3)

    if (recent.length === 0 || earlier.length === 0) return 'stable'

    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length
    const earlierAvg = earlier.reduce((a, b) => a + b, 0) / earlier.length

    const change = ((recentAvg - earlierAvg) / earlierAvg) * 100

    if (change > 5) return 'up'
    if (change < -5) return 'down'
    return 'stable'
  }

  /**
   * 评估缓存健康状态
   */
  private assessHealth(stats: CacheStats): 'excellent' | 'good' | 'fair' | 'poor' {
    const { hitRate, size } = stats

    if (hitRate >= 80 && size <= 50) return 'excellent'
    if (hitRate >= 60 && size <= 100) return 'good'
    if (hitRate >= 40 && size <= 150) return 'fair'
    return 'poor'
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(stats: CacheStats, history: CacheMetrics[]): string[] {
    const recommendations: string[] = []

    // 命中率建议
    if (stats.hitRate < 50) {
      recommendations.push('考虑增加缓存TTL时间')
      recommendations.push('检查缓存键生成逻辑是否合理')
    }

    // 缓存大小建议
    if (stats.size > 100) {
      recommendations.push('考虑减少缓存条目数量限制')
      recommendations.push('实施更积极的缓存清理策略')
    }

    // 趋势建议
    const recentHitRates = history.slice(-5).map(m => m.stats.hitRate)
    if (recentHitRates.length >= 3) {
      const isDecreasing = recentHitRates.every(
        (rate, i) => i === 0 || rate <= recentHitRates[i - 1]
      )
      if (isDecreasing) {
        recommendations.push('命中率持续下降，检查数据访问模式')
      }
    }

    return recommendations
  }

  /**
   * 导出监控数据
   */
  exportData() {
    return {
      metrics: this.metrics,
      events: this.events,
      timestamp: Date.now(),
      report: this.getPerformanceReport()
    }
  }

  /**
   * 清空监控数据
   */
  clearData(): void {
    this.metrics = []
    this.events = []
    console.debug('缓存监控数据已清空')
  }

  /**
   * 销毁监控器
   */
  destroy(): void {
    this.stopMonitoring()
    this.clearData()
  }
}

// ===== 开发者调试工具 =====
export class CacheDebugger {
  /**
   * 打印缓存详细信息
   */
  static printCacheInfo(): void {
    const stats = globalApiCache.getStats()
    const keys = globalApiCache.getKeys()

    console.group('🔍 API缓存详细信息')
    console.log('📊 统计信息:', stats)
    console.log('🔑 缓存键列表:', keys)
    console.log('📈 命中率:', stats.hitRate.toFixed(2) + '%')
    console.log('💾 缓存大小:', stats.size)
    console.groupEnd()
  }

  /**
   * 测试缓存性能
   */
  static async testCachePerformance(testCount = 100): Promise<void> {
    console.group('⚡ 缓存性能测试')

    const testKey = 'test_performance_key'
    const testData = { test: 'data', timestamp: Date.now() }

    // 测试写入性能
    const writeStart = performance.now()
    for (let i = 0; i < testCount; i++) {
      globalApiCache.set(`${testKey}_${i}`, testData)
    }
    const writeTime = performance.now() - writeStart

    // 测试读取性能
    const readStart = performance.now()
    for (let i = 0; i < testCount; i++) {
      globalApiCache.get(`${testKey}_${i}`)
    }
    const readTime = performance.now() - readStart

    console.log(`📝 写入${testCount}条数据耗时: ${writeTime.toFixed(2)}ms`)
    console.log(`📖 读取${testCount}条数据耗时: ${readTime.toFixed(2)}ms`)
    console.log(`⚡ 写入性能: ${((testCount / writeTime) * 1000).toFixed(0)} ops/sec`)
    console.log(`⚡ 读取性能: ${((testCount / readTime) * 1000).toFixed(0)} ops/sec`)

    // 清理测试数据
    for (let i = 0; i < testCount; i++) {
      globalApiCache.delete(`${testKey}_${i}`)
    }

    console.groupEnd()
  }

  /**
   * 模拟缓存压力测试
   */
  static async stressTest(): Promise<void> {
    console.group('🔥 缓存压力测试')

    const iterations = 1000
    const keyPrefix = 'stress_test'

    console.log(`开始压力测试 - ${iterations}次随机操作`)

    const start = performance.now()

    for (let i = 0; i < iterations; i++) {
      const operation = Math.random()
      const key = `${keyPrefix}_${Math.floor(Math.random() * 100)}`

      if (operation < 0.6) {
        // 60% 读操作
        globalApiCache.get(key)
      } else if (operation < 0.9) {
        // 30% 写操作
        globalApiCache.set(key, { data: `test_${i}`, timestamp: Date.now() })
      } else {
        // 10% 删除操作
        globalApiCache.delete(key)
      }
    }

    const duration = performance.now() - start
    const stats = globalApiCache.getStats()

    console.log(`✅ 压力测试完成，耗时: ${duration.toFixed(2)}ms`)
    console.log(`📊 最终统计:`, stats)
    console.log(`⚡ 操作性能: ${((iterations / duration) * 1000).toFixed(0)} ops/sec`)

    // 清理测试数据
    globalApiCache.deleteByPattern(keyPrefix)

    console.groupEnd()
  }
}

// ===== 全局监控实例 =====
export const cacheMonitor = new CacheMonitor()

// ===== 开发环境调试工具 =====
if (import.meta.env.DEV) {
  // 将调试工具挂载到全局
  const windowWithDebugger = window as WindowWithDebugger
  windowWithDebugger.cacheDebugger = CacheDebugger
  windowWithDebugger.cacheMonitor = cacheMonitor

  console.log('🔧 缓存调试工具已挂载到 window.cacheDebugger 和 window.cacheMonitor')
}

export default cacheMonitor
