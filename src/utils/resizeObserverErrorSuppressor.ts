/**
 * ResizeObserver 错误抑制器
 * 用于忽略 ResizeObserver 循环限制错误
 */

let errorHandler: ((event: ErrorEvent) => void) | null = null

/**
 * 安装 ResizeObserver 错误抑制器
 */
export function installResizeObserverErrorSuppressor(): void {
  if (errorHandler) return

  errorHandler = (event: ErrorEvent) => {
    const message = event.message || ''
    
    // 检查是否是 ResizeObserver 相关错误
    if (
      message.includes('ResizeObserver loop completed with undelivered notifications') ||
      message.includes('ResizeObserver loop limit exceeded') ||
      message.includes('ResizeObserver loop')
    ) {
      // 阻止错误传播
      event.stopImmediatePropagation()
      event.preventDefault()
      return
    }
  }

  // 添加事件监听器（捕获阶段）
  window.addEventListener('error', errorHandler, true)
}

/**
 * 卸载 ResizeObserver 错误抑制器
 */
export function uninstallResizeObserverErrorSuppressor(): void {
  if (errorHandler) {
    window.removeEventListener('error', errorHandler, true)
    errorHandler = null
  }
}

/**
 * 检查是否为 ResizeObserver 错误
 */
export function isResizeObserverError(message: string): boolean {
  return (
    message.includes('ResizeObserver loop completed with undelivered notifications') ||
    message.includes('ResizeObserver loop limit exceeded') ||
    message.includes('ResizeObserver loop')
  )
}