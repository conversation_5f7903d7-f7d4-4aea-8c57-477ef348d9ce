interface AlertRecord {
  id: string
  alertType: string
  level: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  processInstanceId?: string
  processDefinitionKey?: string
  processDefinitionName?: string
  taskId?: string
  assignee?: string
  createTime: Date
  resolveTime?: Date
  status: 'active' | 'resolved' | 'ignored'
  resolveUser?: string
  resolveComment?: string
  notificationSent: boolean
  responseTime?: number // 响应时间(分钟)
  businessImpact: 'low' | 'medium' | 'high' | 'critical'
  triggerRule: string
  affectedUsers?: string[]
  department?: string
}

interface AlertExportRequest {
  startDate: string
  endDate: string
  alertType?: string[]
  level?: string[]
  status?: string[]
  processDefinitionKey?: string
  department?: string
  format: 'excel' | 'csv' | 'pdf'
  includeResolution: boolean
  includeStatistics: boolean
  maxRecords: number
  columns?: string[]
  reason: string
  groupBy?: 'type' | 'level' | 'department' | 'date'
}

interface AlertExportResponse {
  taskId: string
  fileName: string
  fileSize: number
  recordCount: number
  downloadUrl: string
  expireTime: Date
  exportTime: Date
  success: boolean
  error?: string
}

interface AlertStatistics {
  totalAlerts: number
  activeAlerts: number
  resolvedAlerts: number
  averageResponseTime: number
  alertsByType: Record<string, number>
  alertsByLevel: Record<string, number>
  alertsByDepartment: Record<string, number>
  resolutionRate: number
  escalationRate: number
}

/**
 * CLEAN-AUX-015: 异常预警导出功能
 * 提供异常预警记录、统计数据的导出功能，支持多种格式和深度定制
 */
export class AlertExport {
  private alertRecords: Map<string, AlertRecord> = new Map()
  private alertStatistics: AlertStatistics = {
    totalAlerts: 0,
    activeAlerts: 0,
    resolvedAlerts: 0,
    averageResponseTime: 0,
    alertsByType: {},
    alertsByLevel: {},
    alertsByDepartment: {},
    resolutionRate: 0,
    escalationRate: 0
  }

  // 预警类型配置
  private readonly alertTypes = {
    timeout: {
      label: '超时预警',
      types: ['任务超时', '流程超时', '审批超时', '响应超时']
    },
    exception: {
      label: '异常预警',
      types: ['系统异常', '业务异常', '数据异常', '网络异常']
    },
    backlog: {
      label: '积压预警',
      types: ['任务积压', '审批积压', '处理积压', '队列积压']
    },
    failure: {
      label: '失败预警',
      types: ['流程失败', '任务失败', '系统失败', '集成失败']
    },
    performance: {
      label: '性能预警',
      types: ['响应缓慢', '资源不足', '并发过高', '内存告警']
    },
    security: {
      label: '安全预警',
      types: ['异常登录', '权限异常', '数据访问异常', '操作异常']
    },
    business: {
      label: '业务预警',
      types: ['证照到期', '合同到期', '考勤异常', '薪酬异常']
    }
  }

  // 预警级别配置
  private readonly alertLevels = [
    { value: 'low', label: '低级', color: '#67C23A', priority: 1 },
    { value: 'medium', label: '中级', color: '#E6A23C', priority: 2 },
    { value: 'high', label: '高级', color: '#F56C6C', priority: 3 },
    { value: 'critical', label: '紧急', color: '#F20C00', priority: 4 }
  ]

  // 导出列配置
  private readonly exportColumns = {
    basic: [
      { key: 'alertType', label: '预警类型', width: 120 },
      { key: 'level', label: '预警级别', width: 100 },
      { key: 'title', label: '预警标题', width: 200 },
      { key: 'createTime', label: '发生时间', width: 160 },
      { key: 'status', label: '状态', width: 80 }
    ],
    detailed: [
      { key: 'message', label: '预警详情', width: 300 },
      { key: 'processDefinitionName', label: '相关流程', width: 150 },
      { key: 'assignee', label: '责任人', width: 100 },
      { key: 'department', label: '部门', width: 120 },
      { key: 'businessImpact', label: '业务影响', width: 100 }
    ],
    resolution: [
      { key: 'resolveTime', label: '解决时间', width: 160 },
      { key: 'responseTime', label: '响应时长(分)', width: 120 },
      { key: 'resolveUser', label: '解决人', width: 100 },
      { key: 'resolveComment', label: '解决备注', width: 200 }
    ],
    technical: [
      { key: 'processInstanceId', label: '流程实例ID', width: 200 },
      { key: 'taskId', label: '任务ID', width: 150 },
      { key: 'triggerRule', label: '触发规则', width: 150 },
      { key: 'notificationSent', label: '通知状态', width: 100 }
    ]
  }

  constructor() {
    this.initializeMockData()
  }

  /**
   * 初始化模拟数据
   */
  private initializeMockData(): void {
    const mockAlerts: AlertRecord[] = [
      {
        id: 'alert_001',
        alertType: '任务超时',
        level: 'high',
        title: '员工入职审批流程超时',
        message: '张三的入职审批流程已超过2小时未处理，请及时跟进',
        processInstanceId: 'pi_20250113_001',
        processDefinitionKey: 'employee_onboarding',
        processDefinitionName: '员工入职审批流程',
        taskId: 'task_001',
        assignee: 'hr_manager_001',
        createTime: new Date(Date.now() - 3 * 60 * 60 * 1000),
        status: 'active',
        businessImpact: 'medium',
        triggerRule: 'task_timeout_2h',
        affectedUsers: ['张三', 'hr_manager_001'],
        department: '人力资源部',
        notificationSent: true
      },
      {
        id: 'alert_002',
        alertType: '积压预警',
        level: 'medium',
        title: '薪酬审批任务积压',
        message: '当前有15个薪酬调整审批任务待处理，请安排处理',
        processDefinitionKey: 'salary_adjustment',
        processDefinitionName: '薪酬调整审批流程',
        createTime: new Date(Date.now() - 1 * 60 * 60 * 1000),
        resolveTime: new Date(Date.now() - 30 * 60 * 1000),
        status: 'resolved',
        responseTime: 30,
        resolveUser: 'hr_director_001',
        resolveComment: '已安排额外人员处理积压任务',
        businessImpact: 'low',
        triggerRule: 'backlog_count_15',
        department: '人力资源部',
        notificationSent: true
      },
      {
        id: 'alert_003',
        alertType: '证照到期',
        level: 'critical',
        title: '护照即将到期提醒',
        message: '李四的护照将于7天后到期，请及时办理续签',
        createTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
        status: 'active',
        businessImpact: 'high',
        triggerRule: 'passport_expire_7d',
        affectedUsers: ['李四'],
        department: '国际交流处',
        notificationSent: true
      },
      {
        id: 'alert_004',
        alertType: '系统异常',
        level: 'high',
        title: '数据库连接异常',
        message: '检测到数据库连接频繁超时，可能影响系统稳定性',
        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
        resolveTime: new Date(Date.now() - 1 * 60 * 60 * 1000),
        status: 'resolved',
        responseTime: 60,
        resolveUser: 'system_admin_001',
        resolveComment: '数据库连接池配置已优化',
        businessImpact: 'critical',
        triggerRule: 'db_timeout_rate_10%',
        department: '信息技术部',
        notificationSent: true
      },
      {
        id: 'alert_005',
        alertType: '考勤异常',
        level: 'medium',
        title: '异常考勤记录',
        message: '王五本月已有3次异常考勤记录，请核实',
        createTime: new Date(Date.now() - 12 * 60 * 60 * 1000),
        status: 'active',
        businessImpact: 'low',
        triggerRule: 'abnormal_attendance_3',
        affectedUsers: ['王五'],
        department: '教务处',
        notificationSent: true
      }
    ]

    // 存储模拟数据
    mockAlerts.forEach(alert => {
      this.alertRecords.set(alert.id, alert)
    })

    // 计算统计数据
    this.calculateStatistics()
  }

  /**
   * 计算预警统计数据
   */
  private calculateStatistics(): void {
    const alerts = Array.from(this.alertRecords.values())

    this.alertStatistics.totalAlerts = alerts.length
    this.alertStatistics.activeAlerts = alerts.filter(a => a.status === 'active').length
    this.alertStatistics.resolvedAlerts = alerts.filter(a => a.status === 'resolved').length

    // 计算平均响应时间
    const resolvedAlerts = alerts.filter(a => a.status === 'resolved' && a.responseTime)
    this.alertStatistics.averageResponseTime =
      resolvedAlerts.length > 0
        ? resolvedAlerts.reduce((sum, a) => sum + (a.responseTime || 0), 0) / resolvedAlerts.length
        : 0

    // 按类型统计
    this.alertStatistics.alertsByType = alerts.reduce(
      (acc, alert) => {
        acc[alert.alertType] = (acc[alert.alertType] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    // 按级别统计
    this.alertStatistics.alertsByLevel = alerts.reduce(
      (acc, alert) => {
        acc[alert.level] = (acc[alert.level] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    // 按部门统计
    this.alertStatistics.alertsByDepartment = alerts.reduce(
      (acc, alert) => {
        if (alert.department) {
          acc[alert.department] = (acc[alert.department] || 0) + 1
        }
        return acc
      },
      {} as Record<string, number>
    )

    // 计算解决率
    this.alertStatistics.resolutionRate =
      this.alertStatistics.totalAlerts > 0
        ? (this.alertStatistics.resolvedAlerts / this.alertStatistics.totalAlerts) * 100
        : 0

    // 计算升级率（高级和紧急预警占比）
    const highPriorityAlerts = alerts.filter(a => ['high', 'critical'].includes(a.level)).length
    this.alertStatistics.escalationRate =
      this.alertStatistics.totalAlerts > 0
        ? (highPriorityAlerts / this.alertStatistics.totalAlerts) * 100
        : 0
  }

  /**
   * 查询预警记录
   */
  async queryAlerts(
    request: AlertExportRequest,
    userRole: string,
    userId: string
  ): Promise<{
    alerts: AlertRecord[]
    statistics: AlertStatistics
    total: number
  }> {
    try {
      // 权限验证
      const permission = this.validateQueryPermission(userRole)
      if (!permission.allowed) {
        throw new Error(permission.reason)
      }

      // 应用查询条件
      let alerts = Array.from(this.alertRecords.values())

      // 时间范围过滤
      const startDate = new Date(request.startDate)
      const endDate = new Date(request.endDate)
      alerts = alerts.filter(alert => alert.createTime >= startDate && alert.createTime <= endDate)

      // 预警类型过滤
      if (request.alertType && request.alertType.length > 0) {
        alerts = alerts.filter(alert => request.alertType!.includes(alert.alertType))
      }

      // 预警级别过滤
      if (request.level && request.level.length > 0) {
        alerts = alerts.filter(alert => request.level!.includes(alert.level))
      }

      // 状态过滤
      if (request.status && request.status.length > 0) {
        alerts = alerts.filter(alert => request.status!.includes(alert.status))
      }

      // 流程过滤
      if (request.processDefinitionKey) {
        alerts = alerts.filter(alert => alert.processDefinitionKey === request.processDefinitionKey)
      }

      // 部门过滤
      if (request.department) {
        alerts = alerts.filter(alert => alert.department === request.department)
      }

      // 限制记录数
      if (alerts.length > request.maxRecords) {
        alerts = alerts.slice(0, request.maxRecords)
      }

      // 重新计算统计数据
      const statistics = this.calculateFilteredStatistics(alerts)

      return {
        alerts,
        statistics,
        total: alerts.length
      }
    } catch (error) {
      throw error
    }
  }

  /**
   * 计算过滤后的统计数据
   */
  private calculateFilteredStatistics(alerts: AlertRecord[]): AlertStatistics {
    const totalAlerts = alerts.length
    const activeAlerts = alerts.filter(a => a.status === 'active').length
    const resolvedAlerts = alerts.filter(a => a.status === 'resolved').length

    const resolvedAlertsWithTime = alerts.filter(a => a.status === 'resolved' && a.responseTime)
    const averageResponseTime =
      resolvedAlertsWithTime.length > 0
        ? resolvedAlertsWithTime.reduce((sum, a) => sum + (a.responseTime || 0), 0) /
          resolvedAlertsWithTime.length
        : 0

    const alertsByType = alerts.reduce(
      (acc, alert) => {
        acc[alert.alertType] = (acc[alert.alertType] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const alertsByLevel = alerts.reduce(
      (acc, alert) => {
        acc[alert.level] = (acc[alert.level] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const alertsByDepartment = alerts.reduce(
      (acc, alert) => {
        if (alert.department) {
          acc[alert.department] = (acc[alert.department] || 0) + 1
        }
        return acc
      },
      {} as Record<string, number>
    )

    const resolutionRate = totalAlerts > 0 ? (resolvedAlerts / totalAlerts) * 100 : 0

    const highPriorityAlerts = alerts.filter(a => ['high', 'critical'].includes(a.level)).length
    const escalationRate = totalAlerts > 0 ? (highPriorityAlerts / totalAlerts) * 100 : 0

    return {
      totalAlerts,
      activeAlerts,
      resolvedAlerts,
      averageResponseTime,
      alertsByType,
      alertsByLevel,
      alertsByDepartment,
      resolutionRate,
      escalationRate
    }
  }

  /**
   * 导出预警记录
   */
  async exportAlerts(
    request: AlertExportRequest,
    userRole: string,
    userId: string
  ): Promise<AlertExportResponse> {
    try {
      // 权限验证
      const permission = this.validateExportPermission(request, userRole)
      if (!permission.allowed) {
        throw new Error(permission.reason)
      }

      // 查询数据
      const queryResult = await this.queryAlerts(request, userRole, userId)
      const { alerts, statistics } = queryResult

      // 生成导出文件
      const fileName = this.generateFileName(request)
      const taskId = `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      let fileData: { content: string; size: number }

      switch (request.format) {
        case 'excel':
          fileData = await this.generateExcelFile(alerts, statistics, request, fileName, taskId)
          break
        case 'csv':
          fileData = this.generateCsvFile(alerts, request, fileName)
          break
        case 'pdf':
          fileData = await this.generatePdfFile(alerts, statistics, request, fileName)
          break
        default:
          throw new Error(`不支持的导出格式: ${request.format}`)
      }

      const result = {
        taskId,
        fileName,
        fileSize: fileData.size,
        recordCount: alerts.length,
        downloadUrl: `/downloads/${taskId}/${fileName}`,
        expireTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后过期
        exportTime: new Date(),
        success: true
      }

      // 记录导出操作
      await this.logExportOperation(request, userRole, userId, result)

      return result
    } catch (error) {
      return {
        taskId: '',
        fileName: '',
        fileSize: 0,
        recordCount: 0,
        downloadUrl: '',
        expireTime: new Date(),
        exportTime: new Date(),
        success: false,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  /**
   * 权限验证 - 查询
   */
  private validateQueryPermission(userRole: string): { allowed: boolean; reason?: string } {
    const rolePermissions = {
      employee: false,
      dept_head: true,
      hr_staff: true,
      hr_manager: true,
      audit_admin: true,
      system_admin: true
    }

    const allowed = rolePermissions[userRole as keyof typeof rolePermissions] || false
    return {
      allowed,
      reason: allowed ? undefined : '权限不足，无法查询预警记录'
    }
  }

  /**
   * 权限验证 - 导出
   */
  private validateExportPermission(
    request: AlertExportRequest,
    userRole: string
  ): { allowed: boolean; reason?: string } {
    const queryPermission = this.validateQueryPermission(userRole)
    if (!queryPermission.allowed) {
      return queryPermission
    }

    // 特殊格式权限检查
    if (
      request.format === 'pdf' &&
      !['hr_manager', 'audit_admin', 'system_admin'].includes(userRole)
    ) {
      return { allowed: false, reason: 'PDF格式导出需要管理员权限' }
    }

    // 大数据量权限检查
    const maxRecordsLimits = {
      dept_head: 1000,
      hr_staff: 5000,
      hr_manager: 20000,
      audit_admin: 50000,
      system_admin: 50000
    }

    const maxAllowed = maxRecordsLimits[userRole as keyof typeof maxRecordsLimits] || 1000
    if (request.maxRecords > maxAllowed) {
      return {
        allowed: false,
        reason: `最大导出记录数不能超过 ${maxAllowed} 条`
      }
    }

    return { allowed: true }
  }

  /**
   * 生成Excel文件
   */
  private async generateExcelFile(
    alerts: AlertRecord[],
    statistics: AlertStatistics,
    request: AlertExportRequest,
    fileName: string,
    taskId: string
  ): Promise<{ content: string; size: number }> {
    // 确定导出列
    const columns = this.getExportColumns(request)

    // 生成主工作表内容
    let content = this.generateExcelWorksheet(alerts, columns, '预警记录')

    // 生成统计工作表
    if (request.includeStatistics) {
      content += this.generateStatisticsWorksheet(statistics, '统计分析')
    }

    // 模拟文件大小计算
    const estimatedSize = alerts.length * 500 + (request.includeStatistics ? 2048 : 0)

    return {
      content,
      size: estimatedSize
    }
  }

  /**
   * 生成Excel工作表
   */
  private generateExcelWorksheet(
    alerts: AlertRecord[],
    columns: Array<{ key: string; label: string; width?: number }>,
    sheetName: string
  ): string {
    // 表头
    const headers = columns.map(col => col.label).join('\t')

    // 数据行
    const rows = alerts.map(alert => {
      return columns
        .map(col => {
          const value = this.formatCellValue(alert, col.key)
          return value
        })
        .join('\t')
    })

    return `${sheetName}\n${headers}\n${rows.join('\n')}\n\n`
  }

  /**
   * 生成统计工作表
   */
  private generateStatisticsWorksheet(statistics: AlertStatistics, sheetName: string): string {
    const statsData = [
      ['指标', '数值'],
      ['总预警数', statistics.totalAlerts],
      ['活跃预警数', statistics.activeAlerts],
      ['已解决预警数', statistics.resolvedAlerts],
      ['平均响应时间(分钟)', Math.round(statistics.averageResponseTime)],
      ['解决率(%)', Math.round(statistics.resolutionRate)],
      ['升级率(%)', Math.round(statistics.escalationRate)],
      ['', ''],
      ['按类型分布', ''],
      ...Object.entries(statistics.alertsByType).map(([type, count]) => [type, count]),
      ['', ''],
      ['按级别分布', ''],
      ...Object.entries(statistics.alertsByLevel).map(([level, count]) => [level, count]),
      ['', ''],
      ['按部门分布', ''],
      ...Object.entries(statistics.alertsByDepartment).map(([dept, count]) => [dept, count])
    ]

    const content = statsData.map(row => row.join('\t')).join('\n')
    return `${sheetName}\n${content}\n\n`
  }

  /**
   * 生成CSV文件
   */
  private generateCsvFile(
    alerts: AlertRecord[],
    request: AlertExportRequest,
    fileName: string
  ): { content: string; size: number } {
    const columns = this.getExportColumns(request)

    // CSV头部（UTF-8 BOM）
    let content = '\uFEFF'

    // 表头
    const headers = columns.map(col => `"${col.label}"`).join(',')
    content += headers + '\n'

    // 数据行
    alerts.forEach(alert => {
      const row = columns
        .map(col => {
          const value = this.formatCellValue(alert, col.key)
          return `"${String(value).replace(/"/g, '""')}"`
        })
        .join(',')
      content += row + '\n'
    })

    return {
      content,
      size: content.length
    }
  }

  /**
   * 生成PDF文件
   */
  private async generatePdfFile(
    alerts: AlertRecord[],
    statistics: AlertStatistics,
    request: AlertExportRequest,
    fileName: string
  ): Promise<{ content: string; size: number }> {
    // PDF内容生成（简化实现）
    let content = `预警记录导出报告\n`
    content += `导出时间: ${new Date().toLocaleString()}\n`
    content += `导出原因: ${request.reason}\n\n`

    // 统计摘要
    if (request.includeStatistics) {
      content += `统计摘要:\n`
      content += `总预警数: ${statistics.totalAlerts}\n`
      content += `活跃预警数: ${statistics.activeAlerts}\n`
      content += `解决率: ${Math.round(statistics.resolutionRate)}%\n\n`
    }

    // 预警记录
    content += `预警记录:\n`
    alerts.forEach((alert, index) => {
      content += `${index + 1}. ${alert.title}\n`
      content += `   类型: ${alert.alertType} | 级别: ${alert.level} | 状态: ${alert.status}\n`
      content += `   时间: ${alert.createTime.toLocaleString()}\n`
      if (alert.department) {
        content += `   部门: ${alert.department}\n`
      }
      content += `   详情: ${alert.message}\n\n`
    })

    return {
      content,
      size: content.length * 2 // PDF文件通常比文本大
    }
  }

  /**
   * 格式化单元格值
   */
  private formatCellValue(alert: AlertRecord, key: string): string | number {
    const value = (alert as unknown)[key]

    if (value === null || value === undefined) {
      return ''
    }

    // 日期格式化
    if (value instanceof Date) {
      return value.toLocaleString('zh-CN')
    }

    // 布尔值格式化
    if (typeof value === 'boolean') {
      return value ? '是' : '否'
    }

    // 级别标签转换
    if (key === 'level') {
      const level = this.alertLevels.find(l => l.value === value)
      return level ? level.label : value
    }

    // 状态标签转换
    if (key === 'status') {
      const statusLabels = {
        active: '活跃',
        resolved: '已解决',
        ignored: '已忽略'
      }
      return statusLabels[value as keyof typeof statusLabels] || value
    }

    // 业务影响标签转换
    if (key === 'businessImpact') {
      const impactLabels = {
        low: '低',
        medium: '中',
        high: '高',
        critical: '严重'
      }
      return impactLabels[value as keyof typeof impactLabels] || value
    }

    return String(value)
  }

  /**
   * 获取导出列配置
   */
  private getExportColumns(
    request: AlertExportRequest
  ): Array<{ key: string; label: string; width?: number }> {
    let columns = [...this.exportColumns.basic]

    if (request.columns) {
      if (request.columns.includes('detailed')) {
        columns = columns.concat(this.exportColumns.detailed)
      }
      if (request.columns.includes('resolution') && request.includeResolution) {
        columns = columns.concat(this.exportColumns.resolution)
      }
      if (request.columns.includes('technical')) {
        columns = columns.concat(this.exportColumns.technical)
      }
    }

    return columns
  }

  /**
   * 生成文件名
   */
  private generateFileName(request: AlertExportRequest): string {
    const now = new Date()
    const dateStr = now
      .toISOString()
      .slice(0, 19)
      .replace(/[:\-T]/g, '')
    const typeStr =
      request.alertType && request.alertType.length > 0 ? `_${request.alertType[0]}` : ''

    return `异常预警导出${typeStr}_${dateStr}.${request.format}`
  }

  /**
   * 记录导出操作
   */
  private async logExportOperation(
    request: AlertExportRequest,
    userRole: string,
    userId: string,
    result: { taskId: string; fileName: string; fileSize: number; recordCount: number }
  ): Promise<void> {
    const exportLog = {
      exportId: result.taskId,
      operatorId: userId,
      exportType: '预警导出' as const,
      queryConditions: {
        startDate: request.startDate,
        endDate: request.endDate,
        alertType: request.alertType,
        level: request.level,
        status: request.status,
        department: request.department
      },
      exportFormat: request.format,
      recordCount: result.recordCount,
      fileName: result.fileName,
      fileSize: result.fileSize,
      exportTime: new Date(),
      ipAddress: '127.0.0.1', // 实际实现中应获取真实IP
      reason: request.reason
    }

    // 这里应该调用审计日志API记录导出操作
    // await auditLogService.recordExportOperation(exportLog)
  }

  /**
   * 获取预警类型列表
   */
  getAlertTypes(): Array<{ category: string; types: string[] }> {
    return Object.entries(this.alertTypes).map(([key, value]) => ({
      category: value.label,
      types: value.types
    }))
  }

  /**
   * 获取预警级别列表
   */
  getAlertLevels(): Array<{ value: string; label: string; color: string }> {
    return this.alertLevels
  }

  /**
   * 获取部门列表
   */
  getDepartments(): string[] {
    const departments = Array.from(
      new Set(
        Array.from(this.alertRecords.values())
          .map(alert => alert.department)
          .filter(dept => dept)
      )
    )
    return departments.sort()
  }

  /**
   * 获取流程定义列表
   */
  getProcessDefinitions(): Array<{ key: string; name: string }> {
    const processes = Array.from(
      new Set(
        Array.from(this.alertRecords.values())
          .filter(alert => alert.processDefinitionKey)
          .map(alert => ({
            key: alert.processDefinitionKey!,
            name: alert.processDefinitionName!
          }))
      )
    )

    // 去重
    const uniqueProcesses = processes.filter(
      (process, index, self) => index === self.findIndex(p => p.key === process.key)
    )

    return uniqueProcesses.sort((a, b) => a.name.localeCompare(b.name))
  }
}

// 导出全局实例
export const alertExporter = new AlertExport()

// 导出API函数
export async function queryAlerts(request: AlertExportRequest, userRole: string, userId: string) {
  return alertExporter.queryAlerts(request, userRole, userId)
}

export async function exportAlerts(
  request: AlertExportRequest,
  userRole: string,
  userId: string
): Promise<AlertExportResponse> {
  return alertExporter.exportAlerts(request, userRole, userId)
}

// 导出类型
export type { AlertRecord, AlertExportRequest, AlertExportResponse, AlertStatistics }
