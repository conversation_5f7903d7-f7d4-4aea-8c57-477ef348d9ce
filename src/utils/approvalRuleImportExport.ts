interface ApprovalRule {
  id: string
  name: string
  description: string
  processType: string
  priority: number
  enabled: boolean
  conditions: RuleCondition[]
  actions: RuleAction[]
  settings: RuleSettings
  createTime: string
  updateTime: string
}

interface RuleCondition {
  id: string
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'contains' | 'between'

  value: unknown
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'array'
  logicalOperator?: 'and' | 'or'
}

interface RuleAction {
  id: string
  type:
    | 'assign_approver'
    | 'set_timeout'
    | 'send_notification'
    | 'auto_approve'
    | 'reject'
    | 'route'
  config: ActionConfig
  order: number
}

interface ActionConfig {
  approverType?: 'user' | 'role' | 'department' | 'position' | 'dynamic'
  approverValue?: string | string[]
  timeoutDays?: number
  notificationTemplate?: string
  routeTarget?: string
  autoCondition?: string
}

interface RuleSettings {
  allowOverride: boolean
  requireComment: boolean
  enableEscalation: boolean
  escalationDays?: number
  escalationTarget?: string
  auditLevel: 'none' | 'basic' | 'detailed'
}

interface ImportResult {
  success: boolean
  total: number
  successCount: number
  failureCount: number
  errors: ImportError[]
  warnings: ImportWarning[]
  data?: ApprovalRule[]
}

interface ImportError {
  row: number
  field: string

  value: unknown
  message: string
  severity: 'error' | 'warning'
}

interface ImportWarning {
  row: number
  field: string
  message: string
}

interface ExportOptions {
  format: 'excel' | 'json' | 'xml'
  includeDisabled: boolean
  selectedIds?: string[]
  processTypes?: string[]
  includeConditions: boolean
  includeActions: boolean
  includeHistory: boolean
}

/**
 * CLEAN-AUX-003 & CLEAN-AUX-004: 审批规则导入导出功能
 * 支持审批规则的批量导入导出，包含条件、动作、设置等完整配置
 */
export class ApprovalRuleImportExport {
  private rules: Map<string, ApprovalRule> = new Map()

  // 预定义流程类型
  private readonly processTypes = [
    { value: 'leave', label: '请假申请' },
    { value: 'transfer', label: '调岗申请' },
    { value: 'resignation', label: '离职申请' },
    { value: 'recruitment', label: '招聘申请' },
    { value: 'expense', label: '费用申请' },
    { value: 'training', label: '培训申请' },
    { value: 'contract', label: '合同审批' },
    { value: 'purchase', label: '采购申请' }
  ]

  // 预定义字段类型
  private readonly fieldTypes = [
    { field: 'amount', label: '金额', dataType: 'number' },
    { field: 'days', label: '天数', dataType: 'number' },
    { field: 'department', label: '申请人部门', dataType: 'string' },
    { field: 'position', label: '申请人职位', dataType: 'string' },
    { field: 'level', label: '申请人级别', dataType: 'number' },
    { field: 'urgent', label: '是否紧急', dataType: 'boolean' },
    { field: 'category', label: '申请类别', dataType: 'string' },
    { field: 'submitTime', label: '提交时间', dataType: 'date' }
  ]

  // 导入审批规则
  async importRules(file: File): Promise<ImportResult> {
    try {
      const fileContent = await this.readFileContent(file)

      let rulesData: unknown[]

      // 根据文件类型解析内容
      if (file.name.endsWith('.json')) {
        rulesData = JSON.parse(fileContent)
      } else if (file.name.endsWith('.xml')) {
        rulesData = await this.parseXmlFile(fileContent)
      } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        rulesData = await this.parseExcelFile(file)
      } else {
        throw new Error('不支持的文件格式，仅支持 JSON、XML、Excel 格式')
      }

      return this.validateAndImportRules(rulesData)
    } catch (__error) {
      return {
        success: false,
        total: 0,
        successCount: 0,
        failureCount: 0,
        errors: [
          {
            row: 0,
            field: 'file',
            value: file.name,
            message: error instanceof Error ? error.message : '未知错误',
            severity: 'error'
          }
        ],
        warnings: []
      }
    }
  }

  // 验证并导入规则

  private async validateAndImportRules(rulesData: unknown[]): Promise<ImportResult> {
    const result: ImportResult = {
      success: true,
      total: rulesData.length,
      successCount: 0,
      failureCount: 0,
      errors: [],
      warnings: [],
      data: []
    }

    for (let i = 0; i < rulesData.length; i++) {
      const ruleData = rulesData[i]
      const rowIndex = i + 1

      try {
        // 验证规则数据
        const validationResult = this.validateRule(ruleData, rowIndex)

        if (validationResult.errors.length > 0) {
          result.errors.push(...validationResult.errors)
          result.failureCount++
          continue
        }

        if (validationResult.warnings.length > 0) {
          result.warnings.push(...validationResult.warnings)
        }

        // 转换为标准规则格式
        const rule = this.normalizeRule(ruleData)

        // 检查是否已存在
        const existingRule = this.rules.get(rule.id)
        if (existingRule) {
          // 更新现有规则
          this.rules.set(rule.id, {
            ...existingRule,
            ...rule,
            updateTime: new Date().toISOString()
          })
          result.warnings.push({
            row: rowIndex,
            field: 'id',
            message: `规则 "${rule.name}" 已存在，已更新`
          })
        } else {
          // 创建新规则
          this.rules.set(rule.id, rule)
        }

        result.data!.push(rule)
        result.successCount++
      } catch (__error) {
        result.errors.push({
          row: rowIndex,
          field: 'rule',
          value: ruleData.name || '未知',
          message: error instanceof Error ? error.message : '处理失败',
          severity: 'error'
        })
        result.failureCount++
      }
    }

    result.success = result.errors.length === 0
    return result
  }

  // 验证规则数据

  private validateRule(
    data: unknown,
    row: number
  ): { errors: ImportError[]; warnings: ImportWarning[] } {
    const errors: ImportError[] = []
    const warnings: ImportWarning[] = []

    // 必填字段检查
    const requiredFields = ['name', 'processType']
    for (const field of requiredFields) {
      if (!data[field] || data[field].toString().trim() === '') {
        errors.push({
          row,
          field,
          value: data[field],
          message: `${field} 为必填字段`,
          severity: 'error'
        })
      }
    }

    // 流程类型检查
    if (data.processType && !this.processTypes.some(p => p.value === data.processType)) {
      warnings.push({
        row,
        field: 'processType',
        message: `流程类型 "${data.processType}" 不在预定义列表中`
      })
    }

    // 优先级检查
    if (
      data.priority !== undefined &&
      (isNaN(data.priority) || data.priority < 0 || data.priority > 100)
    ) {
      errors.push({
        row,
        field: 'priority',
        value: data.priority,
        message: '优先级必须是0-100之间的数字',
        severity: 'error'
      })
    }

    // 条件验证
    if (data.conditions && Array.isArray(data.conditions)) {
      for (let i = 0; i < data.conditions.length; i++) {
        const condition = data.conditions[i]
        if (!condition.field || !condition.operator) {
          errors.push({
            row,
            field: `conditions[${i}]`,
            value: condition,
            message: '条件必须包含字段名和操作符',
            severity: 'error'
          })
        }
      }
    }

    // 动作验证
    if (data.actions && Array.isArray(data.actions)) {
      for (let i = 0; i < data.actions.length; i++) {
        const action = data.actions[i]
        if (!action.type) {
          errors.push({
            row,
            field: `actions[${i}]`,
            value: action,
            message: '动作必须指定类型',
            severity: 'error'
          })
        }
      }
    }

    return { errors, warnings }
  }

  // 标准化规则数据

  private normalizeRule(data: unknown): ApprovalRule {
    const now = new Date().toISOString()

    return {
      id: data.id || `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: data.name.trim(),
      description: data.description || '',
      processType: data.processType,
      priority: Number(data.priority) || 50,
      enabled: Boolean(data.enabled ?? true),
      conditions: this.normalizeConditions(data.conditions || []),
      actions: this.normalizeActions(data.actions || []),
      settings: this.normalizeSettings(data.settings || {}),
      createTime: data.createTime || now,
      updateTime: now
    }
  }

  // 标准化条件配置

  private normalizeConditions(conditions: unknown[]): RuleCondition[] {
    return conditions.map((condition, index) => ({
      id: condition.id || `condition_${index}`,
      field: condition.field || '',
      operator: condition.operator || 'eq',
      value: condition.value,
      dataType: condition.dataType || 'string',
      logicalOperator: condition.logicalOperator || 'and'
    }))
  }

  // 标准化动作配置

  private normalizeActions(actions: unknown[]): RuleAction[] {
    return actions
      .map((action, index) => ({
        id: action.id || `action_${index}`,
        type: action.type,
        config: this.normalizeActionConfig(action.config || {}),
        order: Number(action.order) || index
      }))
      .sort((a, b) => a.order - b.order)
  }

  // 标准化动作配置

  private normalizeActionConfig(config: unknown): ActionConfig {
    return {
      approverType: config.approverType,
      approverValue: config.approverValue,
      timeoutDays: Number(config.timeoutDays) || undefined,
      notificationTemplate: config.notificationTemplate,
      routeTarget: config.routeTarget,
      autoCondition: config.autoCondition
    }
  }

  // 标准化设置配置

  private normalizeSettings(settings: unknown): RuleSettings {
    return {
      allowOverride: Boolean(settings.allowOverride),
      requireComment: Boolean(settings.requireComment),
      enableEscalation: Boolean(settings.enableEscalation),
      escalationDays: Number(settings.escalationDays) || undefined,
      escalationTarget: settings.escalationTarget,
      auditLevel: settings.auditLevel || 'basic'
    }
  }

  // 导出审批规则
  async exportRules(
    options: ExportOptions = {
      format: 'excel',
      includeDisabled: false,
      includeConditions: true,
      includeActions: true,
      includeHistory: false
    }
  ): Promise<Blob> {
    try {
      // 获取要导出的规则
      const rules = this.getRulesForExport(options)

      let blob: Blob

      switch (options.format) {
        case 'json':
          blob = this.exportAsJson(rules, options)
          break
        case 'xml':
          blob = this.exportAsXml(rules, options)
          break
        case 'excel':
        default:
          blob = await this.exportAsExcel(rules, options)
          break
      }

      return blob
    } catch (__error) {
      throw error
    }
  }

  // 获取要导出的规则
  private getRulesForExport(options: ExportOptions): ApprovalRule[] {
    let rules = Array.from(this.rules.values())

    // 过滤禁用状态
    if (!options.includeDisabled) {
      rules = rules.filter(rule => rule.enabled)
    }

    // 按指定ID过滤
    if (options.selectedIds && options.selectedIds.length > 0) {
      rules = rules.filter(rule => options.selectedIds!.includes(rule.id))
    }

    // 按流程类型过滤
    if (options.processTypes && options.processTypes.length > 0) {
      rules = rules.filter(rule => options.processTypes!.includes(rule.processType))
    }

    return rules.sort((a, b) => b.priority - a.priority || a.name.localeCompare(b.name, 'zh-CN'))
  }

  // 导出为JSON格式
  private exportAsJson(rules: ApprovalRule[], options: ExportOptions): Blob {
    const data = {
      exportTime: new Date().toISOString(),
      version: '1.0',
      total: rules.length,
      includeConditions: options.includeConditions,
      includeActions: options.includeActions,
      rules: rules.map(rule => {
        const exportRule: unknown = {
          id: rule.id,
          name: rule.name,
          description: rule.description,
          processType: rule.processType,
          priority: rule.priority,
          enabled: rule.enabled,
          settings: rule.settings,
          createTime: rule.createTime,
          updateTime: rule.updateTime
        }

        if (options.includeConditions) {
          exportRule.conditions = rule.conditions
        }

        if (options.includeActions) {
          exportRule.actions = rule.actions
        }

        return exportRule
      })
    }

    return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json;charset=utf-8' })
  }

  // 导出为XML格式
  private exportAsXml(rules: ApprovalRule[], options: ExportOptions): Blob {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    xml += '<approval-rules>\n'
    xml += `  <metadata>\n`
    xml += `    <exportTime>${new Date().toISOString()}</exportTime>\n`
    xml += `    <version>1.0</version>\n`
    xml += `    <total>${rules.length}</total>\n`
    xml += `  </metadata>\n`

    for (const rule of rules) {
      xml += '  <rule>\n'
      xml += `    <id>${this.escapeXml(rule.id)}</id>\n`
      xml += `    <name><![CDATA[${rule.name}]]></name>\n`
      xml += `    <description><![CDATA[${rule.description}]]></description>\n`
      xml += `    <processType>${rule.processType}</processType>\n`
      xml += `    <priority>${rule.priority}</priority>\n`
      xml += `    <enabled>${rule.enabled}</enabled>\n`

      if (options.includeConditions && rule.conditions.length > 0) {
        xml += '    <conditions>\n'
        for (const condition of rule.conditions) {
          xml += '      <condition>\n'
          xml += `        <field>${condition.field}</field>\n`
          xml += `        <operator>${condition.operator}</operator>\n`
          xml += `        <value><![CDATA[${JSON.stringify(condition.value)}]]></value>\n`
          xml += `        <dataType>${condition.dataType}</dataType>\n`
          xml += '      </condition>\n'
        }
        xml += '    </conditions>\n'
      }

      if (options.includeActions && rule.actions.length > 0) {
        xml += '    <actions>\n'
        for (const action of rule.actions) {
          xml += '      <action>\n'
          xml += `        <type>${action.type}</type>\n`
          xml += `        <order>${action.order}</order>\n`
          xml += `        <config><![CDATA[${JSON.stringify(action.config)}]]></config>\n`
          xml += '      </action>\n'
        }
        xml += '    </actions>\n'
      }

      xml += '  </rule>\n'
    }

    xml += '</approval-rules>'

    return new Blob([xml], { type: 'application/xml;charset=utf-8' })
  }

  // 导出为Excel格式
  private async exportAsExcel(rules: ApprovalRule[], options: ExportOptions): Promise<Blob> {
    const workbook = this.createExcelWorkbook(rules, options)

    // 这里应该使用真实的Excel库进行转换
    // 为了演示，返回JSON格式
    return new Blob([JSON.stringify(workbook, null, 2)], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
  }

  // 创建Excel工作簿
  private createExcelWorkbook(rules: ApprovalRule[], options: ExportOptions) {
    const workbook: unknown = { sheets: {} }

    // 基本信息表
    workbook.sheets['审批规则'] = {
      headers: ['ID', '规则名称', '描述', '流程类型', '优先级', '启用状态', '创建时间', '更新时间'],
      data: rules.map(rule => [
        rule.id,
        rule.name,
        rule.description,
        rule.processType,
        rule.priority,
        rule.enabled ? '是' : '否',
        rule.createTime,
        rule.updateTime
      ])
    }

    // 条件详情表
    if (options.includeConditions) {
      workbook.sheets['规则条件'] = {
        headers: ['规则ID', '规则名称', '条件字段', '操作符', '条件值', '数据类型', '逻辑操作符'],
        data: rules.flatMap(rule =>
          rule.conditions.map(condition => [
            rule.id,
            rule.name,
            condition.field,
            condition.operator,
            JSON.stringify(condition.value),
            condition.dataType,
            condition.logicalOperator || ''
          ])
        )
      }
    }

    // 动作详情表
    if (options.includeActions) {
      workbook.sheets['规则动作'] = {
        headers: ['规则ID', '规则名称', '动作类型', '执行顺序', '配置信息'],
        data: rules.flatMap(rule =>
          rule.actions.map(action => [
            rule.id,
            rule.name,
            action.type,
            action.order,
            JSON.stringify(action.config)
          ])
        )
      }
    }

    return workbook
  }

  // XML转义
  private escapeXml(str: string): string {
    return str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
  }

  // 读取文件内容
  private readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file, 'utf-8')
    })
  }

  // 解析XML文件
  private async parseXmlFile(content: string): Promise<unknown[]> {
    // 这里应该使用真实的XML解析库
    return []
  }

  // 解析Excel文件
  private async parseExcelFile(file: File): Promise<unknown[]> {
    // 这里应该使用真实的Excel解析库
    return []
  }

  // 获取流程类型列表
  getProcessTypes() {
    return this.processTypes
  }

  // 获取字段类型列表
  getFieldTypes() {
    return this.fieldTypes
  }

  // 获取所有规则
  getAllRules(): ApprovalRule[] {
    return Array.from(this.rules.values())
  }

  // 根据ID获取规则
  getRuleById(id: string): ApprovalRule | undefined {
    return this.rules.get(id)
  }

  // 根据流程类型获取规则
  getRulesByProcessType(processType: string): ApprovalRule[] {
    return Array.from(this.rules.values())
      .filter(rule => rule.processType === processType && rule.enabled)
      .sort((a, b) => b.priority - a.priority)
  }

  // 删除规则
  deleteRule(id: string): boolean {
    return this.rules.delete(id)
  }

  // 批量删除规则
  deleteRules(ids: string[]): number {
    let deletedCount = 0
    for (const id of ids) {
      if (this.rules.delete(id)) {
        deletedCount++
      }
    }
    return deletedCount
  }

  // 启用/禁用规则
  toggleRuleStatus(id: string, enabled: boolean): boolean {
    const rule = this.rules.get(id)
    if (rule) {
      rule.enabled = enabled
      rule.updateTime = new Date().toISOString()
      return true
    }
    return false
  }

  // 清空所有规则
  clearAllRules() {
    this.rules.clear()
  }
}

// 全局实例
export const approvalRuleManager = new ApprovalRuleImportExport()

// 便捷函数
export async function importApprovalRules(file: File): Promise<ImportResult> {
  return approvalRuleManager.importRules(file)
}

export async function exportApprovalRules(options?: Partial<ExportOptions>): Promise<Blob> {
  return approvalRuleManager.exportRules({
    format: 'excel',
    includeDisabled: false,
    includeConditions: true,
    includeActions: true,
    includeHistory: false,
    ...options
  })
}
