/**
 * DOM操作工具函数
 */

/**
 * 添加事件监听器（支持委托）
 * @param element 元素
 * @param event 事件名
 * @param handler 处理函数
 * @param options 选项
 */
export function on(
  element: Element | Window | Document,
  event: string,
  handler: EventListener,
  options?: boolean | AddEventListenerOptions
): () => void {
  element.addEventListener(event, handler, options)

  // 返回移除函数
  return () => {
    element.removeEventListener(event, handler, options)
  }
}

/**
 * 移除事件监听器
 * @param element 元素
 * @param event 事件名
 * @param handler 处理函数
 * @param options 选项
 */
export function off(
  element: Element | Window | Document,
  event: string,
  handler: EventListener,
  options?: boolean | EventListenerOptions
): void {
  element.removeEventListener(event, handler, options)
}

/**
 * 事件委托
 * @param element 容器元素
 * @param event 事件名
 * @param selector 选择器
 * @param handler 处理函数
 */
export function delegate(
  element: Element,
  event: string,
  selector: string,
  handler: (e: Event, target: Element) => void
): () => void {
  const listener = (e: Event) => {
    const target = e.target as Element
    const delegateTarget = target.closest(selector)

    if (delegateTarget && element.contains(delegateTarget)) {
      handler.call(delegateTarget, e, delegateTarget)
    }
  }

  return on(element, event, listener)
}

/**
 * 获取元素样式
 * @param element 元素
 * @param prop 属性名
 */
export function getStyle(element: Element, prop: string): string {
  return window.getComputedStyle(element).getPropertyValue(prop)
}

/**
 * 设置元素样式
 * @param element 元素
 * @param styles 样式对象
 */
export function setStyle(element: HTMLElement, styles: Partial<CSSStyleDeclaration>): void {
  Object.assign(element.style, styles)
}

/**
 * 添加类名
 * @param element 元素
 * @param classes 类名
 */
export function addClass(element: Element, ...classes: string[]): void {
  element.classList.add(...classes)
}

/**
 * 移除类名
 * @param element 元素
 * @param classes 类名
 */
export function removeClass(element: Element, ...classes: string[]): void {
  element.classList.remove(...classes)
}

/**
 * 切换类名
 * @param element 元素
 * @param className 类名
 * @param force 强制添加或移除
 */
export function toggleClass(element: Element, className: string, force?: boolean): boolean {
  return element.classList.toggle(className, force)
}

/**
 * 判断是否有类名
 * @param element 元素
 * @param className 类名
 */
export function hasClass(element: Element, className: string): boolean {
  return element.classList.contains(className)
}

/**
 * 获取元素位置和大小
 * @param element 元素
 */
export function getRect(element: Element): DOMRect {
  return element.getBoundingClientRect()
}

/**
 * 获取元素相对于文档的位置
 * @param element 元素
 */
export function getOffset(element: Element): { top: number; left: number } {
  const rect = getRect(element)
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop

  return {
    top: rect.top + scrollTop,
    left: rect.left + scrollLeft
  }
}

/**
 * 判断元素是否在视口内
 * @param element 元素
 * @param threshold 阈值
 */
export function isInViewport(element: Element, threshold: number = 0): boolean {
  const rect = getRect(element)

  return (
    rect.top >= -threshold &&
    rect.left >= -threshold &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) + threshold &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth) + threshold
  )
}

/**
 * 滚动到元素
 * @param element 元素
 * @param options 选项
 */
export function scrollToElement(
  element: Element,
  options: ScrollIntoViewOptions = { behavior: 'smooth', block: 'center' }
): void {
  element.scrollIntoView(options)
}

/**
 * 创建元素
 * @param tag 标签名
 * @param options 选项
 */
export function createElement<K extends keyof HTMLElementTagNameMap>(
  tag: K,
  options?: {
    className?: string
    attributes?: Record<string, string>
    styles?: Partial<CSSStyleDeclaration>
    text?: string
    html?: string
    children?: Element[]
  }
): HTMLElementTagNameMap[K] {
  const element = document.createElement(tag)

  if (options) {
    const { className, attributes, styles, text, html, children } = options

    if (className) {
      element.className = className
    }

    if (attributes) {
      Object.entries(attributes).forEach(([key, value]) => {
        element.setAttribute(key, value)
      })
    }

    if (styles) {
      setStyle(element, styles)
    }

    if (text) {
      element.textContent = text
    } else if (html) {
      element.innerHTML = html
    }

    if (children) {
      children.forEach(child => element.appendChild(child))
    }
  }

  return element
}

/**
 * 查询元素
 * @param selector 选择器
 * @param parent 父元素
 */
export function query<T extends Element = Element>(
  selector: string,
  parent: Element | Document = document
): T | null {
  return parent.querySelector<T>(selector)
}

/**
 * 查询所有元素
 * @param selector 选择器
 * @param parent 父元素
 */
export function queryAll<T extends Element = Element>(
  selector: string,
  parent: Element | Document = document
): T[] {
  return Array.from(parent.querySelectorAll<T>(selector))
}

/**
 * 等待DOM加载完成
 * @param callback 回调函数
 */
export function ready(callback: () => void): void {
  if (document.readyState !== 'loading') {
    callback()
  } else {
    document.addEventListener('DOMContentLoaded', callback)
  }
}

/**
 * 监听元素大小变化
 * @param element 元素
 * @param callback 回调函数
 */
export function observeResize(
  element: Element,
  callback: (entry: ResizeObserverEntry) => void
): () => void {
  const observer = new ResizeObserver(entries => {
    entries.forEach(callback)
  })

  observer.observe(element)

  return () => {
    observer.unobserve(element)
    observer.disconnect()
  }
}

/**
 * 监听元素可见性变化
 * @param element 元素
 * @param callback 回调函数
 * @param options 选项
 */
export function observeIntersection(
  element: Element,
  callback: (entry: IntersectionObserverEntry) => void,
  options?: IntersectionObserverInit
): () => void {
  const observer = new IntersectionObserver(entries => {
    entries.forEach(callback)
  }, options)

  observer.observe(element)

  return () => {
    observer.unobserve(element)
    observer.disconnect()
  }
}

/**
 * 全屏操作
 */
export const fullscreen = {
  /**
   * 进入全屏
   * @param element 元素
   */
  async enter(element: Element = document.documentElement): Promise<void> {
    const el = element as unknown
    const requestFullscreen =
      el.requestFullscreen ||
      el.webkitRequestFullscreen ||
      el.mozRequestFullScreen ||
      el.msRequestFullscreen

    if (requestFullscreen) {
      await requestFullscreen.call(el)
    }
  },

  /**
   * 退出全屏
   */
  async exit(): Promise<void> {
    const doc = document as unknown
    const exitFullscreen =
      doc.exitFullscreen ||
      doc.webkitExitFullscreen ||
      doc.mozCancelFullScreen ||
      doc.msExitFullscreen

    if (exitFullscreen) {
      await exitFullscreen.call(doc)
    }
  },

  /**
   * 切换全屏
   * @param element 元素
   */
  async toggle(element?: Element): Promise<void> {
    if (this.isFullscreen()) {
      await this.exit()
    } else {
      await this.enter(element)
    }
  },

  /**
   * 判断是否全屏
   */
  isFullscreen(): boolean {
    const doc = document as unknown
    return !!(
      doc.fullscreenElement ||
      doc.webkitFullscreenElement ||
      doc.mozFullScreenElement ||
      doc.msFullscreenElement
    )
  }
}
