/**
 * 数据加密传输工具
 * 提供敏感数据的加密和解密功能
 */

import CryptoJS from 'crypto-js'

// 加密配置
interface EncryptionConfig {
  // AES密钥（从环境变量获取）
  aesKey: string
  // AES向量
  aesIv: string
  // RSA公钥
  rsaPublicKey: string
  // RSA私钥（通常只在服务端）
  rsaPrivateKey?: string
  // 是否启用加密
  enabled: boolean
  // 需要加密的字段
  sensitiveFields: string[]
}

// 默认配置
const defaultConfig: EncryptionConfig = {
  aesKey: import.meta.env.VITE_AES_KEY || 'default-aes-key-32-bytes-long!!!',
  aesIv: import.meta.env.VITE_AES_IV || 'default-iv-16-by',
  rsaPublicKey: import.meta.env.VITE_RSA_PUBLIC_KEY || '',
  enabled: import.meta.env.VITE_ENABLE_ENCRYPTION === 'true',
  sensitiveFields: [
    'password',
    'idCard',
    'bankAccount',
    'phone',
    'email',
    'salary',
    'bonus',
    'socialSecurity',
    'address',
    'emergencyContact'
  ]
}

class Encryption {
  private config: EncryptionConfig

  constructor(config: Partial<EncryptionConfig> = {}) {
    this.config = { ...defaultConfig, ...config }
  }

  /**
   * AES加密
   */
  aesEncrypt(data: string): string {
    if (!this.config.enabled) return data

    try {
      const key = CryptoJS.enc.Utf8.parse(this.config.aesKey)
      const iv = CryptoJS.enc.Utf8.parse(this.config.aesIv)

      const encrypted = CryptoJS.AES.encrypt(data, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })

      return encrypted.toString()
    } catch (__error) {
      throw new Error('数据加密失败')
    }
  }

  /**
   * AES解密
   */
  aesDecrypt(encryptedData: string): string {
    if (!this.config.enabled) return encryptedData

    try {
      const key = CryptoJS.enc.Utf8.parse(this.config.aesKey)
      const iv = CryptoJS.enc.Utf8.parse(this.config.aesIv)

      const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })

      return decrypted.toString(CryptoJS.enc.Utf8)
    } catch (__error) {
      throw new Error('数据解密失败')
    }
  }

  /**
   * RSA加密（使用JSEncrypt库）
   */
  async rsaEncrypt(data: string): Promise<string> {
    if (!this.config.enabled || !this.config.rsaPublicKey) return data

    try {
      // 动态导入JSEncrypt以减少初始包大小
      const { JSEncrypt } = await import('jsencrypt')
      const encrypt = new JSEncrypt()
      encrypt.setPublicKey(this.config.rsaPublicKey!)

      const encrypted = encrypt.encrypt(data)
      if (!encrypted) {
        throw new Error('RSA encryption failed')
      }

      return encrypted
    } catch (error) {
      // 加密失败时返回原始数据或抛出错误
      if (this.config.throwOnError) {
        throw error
      }
      return data
    }
  }

  /**
   * 生成随机密钥
   */
  private generateRandomKey(length: number = 16): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let key = ''

    for (let i = 0; i < length; i++) {
      key += chars.charAt(Math.floor(Math.random() * chars.length))
    }

    return key
  }

  /**
   * 加密对象中的敏感字段
   */
  encryptObject<T extends Record<string, unknown>>(obj: T): T {
    if (!this.config.enabled) return obj

    const encrypted = { ...obj }

    for (const [key, value] of Object.entries(encrypted)) {
      if (this.isSensitiveField(key) && typeof value === 'string') {
        encrypted[key] = this.aesEncrypt(value)
      } else if (typeof value === 'object' && value !== null) {
        encrypted[key] = this.encryptObject(value)
      }
    }

    return encrypted
  }

  /**
   * 解密对象中的敏感字段
   */
  decryptObject<T extends Record<string, unknown>>(obj: T): T {
    if (!this.config.enabled) return obj

    const decrypted = { ...obj }

    for (const [key, value] of Object.entries(decrypted)) {
      if (this.isSensitiveField(key) && typeof value === 'string') {
        try {
          decrypted[key] = this.aesDecrypt(value)
        } catch (__error) {
          // 解密失败时保留原值
        }
      } else if (typeof value === 'object' && value !== null) {
        decrypted[key] = this.decryptObject(value)
      }
    }

    return decrypted
  }

  /**
   * 判断是否为敏感字段
   */
  private isSensitiveField(fieldName: string): boolean {
    const lowerFieldName = fieldName.toLowerCase()
    return this.config.sensitiveFields.some(field => lowerFieldName.includes(field.toLowerCase()))
  }

  /**
   * 加密请求数据
   */
   
  encryptRequest(data: unknown): unknown {
    if (!this.config.enabled) return data

    // 如果是FormData，特殊处理
    if (data instanceof FormData) {
      const encryptedFormData = new FormData()

      for (const [key, value] of data.entries()) {
        if (this.isSensitiveField(key) && typeof value === 'string') {
          encryptedFormData.append(key, this.aesEncrypt(value))
        } else {
          encryptedFormData.append(key, value)
        }
      }

      return encryptedFormData
    }

    // 普通对象
    return this.encryptObject(data)
  }

  /**
   * 解密响应数据
   */
   
  decryptResponse(data: unknown): unknown {
    if (!this.config.enabled) return data

    // 处理数组
    if (Array.isArray(data)) {
      return data.map(item => this.decryptResponse(item))
    }

    // 处理对象
    if (typeof data === 'object' && data !== null) {
      return this.decryptObject(data)
    }

    return data
  }

  /**
   * 生成数据签名（用于防篡改）
   */
   
  generateSignature(data: unknown, timestamp: number): string {
    const dataStr = typeof data === 'string' ? data : JSON.stringify(data)
    const signStr = `${dataStr}${timestamp}${this.config.aesKey}`
    return this.sha256Hash(signStr)
  }

  /**
   * 验证数据签名
   */
   
  verifySignature(data: unknown, timestamp: number, signature: string): boolean {
    const expectedSignature = this.generateSignature(data, timestamp)
    return expectedSignature === signature
  }

  /**
   * 配置加密选项
   */
  configure(options: Partial<EncryptionConfig>) {
    this.config = { ...this.config, ...options }
  }

  /**
   * 添加敏感字段
   */
  addSensitiveField(field: string) {
    if (!this.config.sensitiveFields.includes(field)) {
      this.config.sensitiveFields.push(field)
    }
  }

  /**
   * 移除敏感字段
   */
  removeSensitiveField(field: string) {
    const index = this.config.sensitiveFields.indexOf(field)
    if (index > -1) {
      this.config.sensitiveFields.splice(index, 1)
    }
  }

  /**
   * 获取加密状态
   */
  isEnabled(): boolean {
    return this.config.enabled
  }

  /**
   * 启用/禁用加密
   */
  setEnabled(enabled: boolean) {
    this.config.enabled = enabled
  }
}

// 创建默认实例
export const encryption = new Encryption()

// 便捷函数
export const encryptData = (data: string) => encryption.aesEncrypt(data)
export const decryptData = (data: string) => encryption.aesDecrypt(data)
export const encryptObject = <T extends Record<string, unknown>>(obj: T) =>
  encryption.encryptObject(obj)
export const decryptObject = <T extends Record<string, unknown>>(obj: T) =>
  encryption.decryptObject(obj)

// 用于axios请求拦截器
 
export function setupEncryptionInterceptor(axios: unknown) {
  // 请求拦截器 - 加密
  axios.interceptors.request.use(
     
    (config: unknown) => {
      if (config.data && encryption.isEnabled()) {
        // 添加时间戳和签名
        const timestamp = Date.now()
        const signature = encryption.generateSignature(config.data, timestamp)

        // 加密数据
        config.data = encryption.encryptRequest(config.data)

        // 添加安全头
        config.headers['X-Timestamp'] = timestamp
        config.headers['X-Signature'] = signature
        config.headers['X-Encrypted'] = 'true'
      }

      return config
    },
     
    (error: unknown) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器 - 解密
  axios.interceptors.response.use(
     
    (response: unknown) => {
      // 检查是否为加密响应
      const isEncrypted = response.headers['x-encrypted'] === 'true'

      if (isEncrypted && response.data) {
        // 验证签名
        const timestamp = response.headers['x-timestamp']
        const signature = response.headers['x-signature']

        if (timestamp && signature) {
          const isValid = encryption.verifySignature(response.data, timestamp, signature)
          if (!isValid) {
            }
        }

        // 解密数据
        response.data = encryption.decryptResponse(response.data)
      }

      return response
    },
     
    (error: unknown) => {
      return Promise.reject(error)
    }
  )
}

export default encryption
