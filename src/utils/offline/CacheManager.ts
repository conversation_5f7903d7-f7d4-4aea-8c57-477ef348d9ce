/**
 * 离线缓存管理器
 * 提供缓存策略、离线检测、后台同步等功能
 */

import { CacheStrategy } from './types'
import type {
  CacheConfig,
  CacheRule,
  CacheStats,
  OfflineState,
  SyncTask,
  SyncConfig
} from './types'

// 扩展Navigator接口以支持connection API
interface NavigatorWithConnection extends Navigator {
  connection?: {
    effectiveType?: string
    downlink?: number
    addEventListener: (type: string, listener: EventListener) => void
  }
}

export class CacheManager {
  private static instance: CacheManager | null = null
  private config: CacheConfig
  private rules: CacheRule[] = []
  private syncTasks: Map<string, SyncTask> = new Map()
  private offlineState: OfflineState = {
    isOnline: navigator.onLine,
    networkType: 'unknown'
  }
  private syncTimer: number | null = null
  private eventHandlers: Map<string, Set<Function>> = new Map()

  constructor(config: CacheConfig) {
    this.config = {
      ...config,
      name: config.name || 'hr-cache',
      version: config.version || 1,
      strategy: config.strategy || CacheStrategy.NETWORK_FIRST,
      maxAge: config.maxAge || 7 * 24 * 60 * 60 * 1000, // 7天
      maxEntries: config.maxEntries || 500
    }

    this.initialize()
  }

  /**
   * 获取单例实例
   */
  static getInstance(config?: CacheConfig): CacheManager {
    if (!CacheManager.instance) {
      if (!config) {
        throw new Error('首次调用需要提供配置')
      }
      CacheManager.instance = new CacheManager(config)
    }
    return CacheManager.instance
  }

  /**
   * 初始化
   */
  private initialize(): void {
    // 加载同步任务
    this.loadSyncTasks()

    // 监听网络状态
    this.setupNetworkListeners()

    // 检测网络类型
    this.detectNetworkType()

    // 启动同步任务
    this.startSyncTimer()

    // 注册Service Worker
    this.registerServiceWorker()
  }

  /**
   * 注册Service Worker
   */
  private async registerServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        const basePath = import.meta.env.BASE_URL || '/'
        // 确保 basePath 以 / 结尾
        const normalizedBasePath = basePath.endsWith('/') ? basePath : basePath + '/'
        const swPath = normalizedBasePath + 'sw.js'
        const registration = await navigator.serviceWorker.register(swPath, {
          scope: normalizedBasePath
        })

        // 监听更新
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'activated') {
                this.emit('update-available')
              }
            })
          }
        })
      } catch (__error) {}
    }
  }

  /**
   * 设置网络监听器
   */
  private setupNetworkListeners(): void {
    // 在线/离线事件
    window.addEventListener('online', () => {
      this.updateOfflineState({ isOnline: true })
      this.emit('online')
      this.processSyncQueue()
    })

    window.addEventListener('offline', () => {
      this.updateOfflineState({
        isOnline: false,
        lastOnlineTime: Date.now()
      })
      this.emit('offline')
    })

    // 网络状态变化
    const navWithConnection = navigator as NavigatorWithConnection
    if (navWithConnection.connection) {
      navWithConnection.connection.addEventListener('change', () => {
        this.detectNetworkType()
      })
    }
  }

  /**
   * 检测网络类型
   */
  private detectNetworkType(): void {
    const navWithConnection = navigator as NavigatorWithConnection
    if (navWithConnection.connection) {
      const connection = navWithConnection.connection
      const effectiveType = connection.effectiveType || 'unknown'
      const downlink = connection.downlink || 0

      this.updateOfflineState({
        networkType: effectiveType,
        networkSpeed: downlink
      })
    }
  }

  /**
   * 更新离线状态
   */
  private updateOfflineState(state: Partial<OfflineState>): void {
    const oldState = { ...this.offlineState }
    this.offlineState = { ...this.offlineState, ...state }

    // 计算离线时长
    if (!state.isOnline && this.offlineState.lastOnlineTime) {
      this.offlineState.offlineDuration = Date.now() - this.offlineState.lastOnlineTime
    } else if (state.isOnline) {
      this.offlineState.offlineDuration = undefined
    }

    // 触发状态变化事件
    if (oldState.isOnline !== this.offlineState.isOnline) {
      this.emit('connection-change', this.offlineState)
    }
  }

  /**
   * 添加缓存规则
   */
  addRule(rule: CacheRule): void {
    this.rules.push(rule)

    // 通知Service Worker更新规则
    this.sendMessageToSW('update-rules', { rules: this.rules })
  }

  /**
   * 删除缓存规则
   */
  removeRule(name: string): void {
    this.rules = this.rules.filter(r => r.name !== name)
    this.sendMessageToSW('update-rules', { rules: this.rules })
  }

  /**
   * 获取缓存规则
   */
  getRules(): CacheRule[] {
    return [...this.rules]
  }

  /**
   * 缓存请求
   */
  async cacheRequest(request: Request | string, response?: Response): Promise<void> {
    const cache = await caches.open(this.config.name)
    const req = typeof request === 'string' ? new Request(request) : request

    if (response) {
      await cache.put(req, response)
    } else {
      // 获取并缓存
      const res = await fetch(req)
      if (res.ok) {
        await cache.put(req, res.clone())
      }
    }
  }

  /**
   * 获取缓存
   */
  async getCached(request: Request | string): Promise<Response | null> {
    const cache = await caches.open(this.config.name)
    const req = typeof request === 'string' ? new Request(request) : request
    const response = await cache.match(req)

    if (response) {
      // 检查是否过期
      const cachedTime = response.headers.get('sw-cached-time')
      if (cachedTime && this.config.maxAge) {
        const age = Date.now() - parseInt(cachedTime)
        if (age > this.config.maxAge) {
          await cache.delete(req)
          return null
        }
      }
    }

    return response || null
  }

  /**
   * 删除缓存
   */
  async deleteCache(request: Request | string): Promise<boolean> {
    const cache = await caches.open(this.config.name)
    const req = typeof request === 'string' ? new Request(request) : request
    return await cache.delete(req)
  }

  /**
   * 清空缓存
   */
  async clearCache(cacheName?: string): Promise<void> {
    if (cacheName) {
      await caches.delete(cacheName)
    } else {
      const names = await caches.keys()
      await Promise.all(names.map(name => caches.delete(name)))
    }
  }

  /**
   * 获取缓存统计
   */
  async getCacheStats(): Promise<CacheStats[]> {
    const stats: CacheStats[] = []
    const cacheNames = await caches.keys()

    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName)
      const requests = await cache.keys()

      let totalSize = 0
      for (const request of requests) {
        const response = await cache.match(request)
        if (response) {
          const blob = await response.blob()
          totalSize += blob.size
        }
      }

      stats.push({
        cacheName,
        count: requests.length,
        size: totalSize,
        hits: 0, // 需要在Service Worker中统计
        misses: 0,
        hitRate: 0
      })
    }

    return stats
  }

  /**
   * 预缓存资源
   */
  async precache(urls: string[]): Promise<void> {
    const cache = await caches.open(this.config.name)

    // 过滤已缓存的URL
    const uncachedUrls = []
    for (const url of urls) {
      const response = await cache.match(url)
      if (!response) {
        uncachedUrls.push(url)
      }
    }

    // 批量缓存
    if (uncachedUrls.length > 0) {
      await cache.addAll(uncachedUrls)
    }
  }

  /**
   * 添加同步任务
   */
  addSyncTask(task: Omit<SyncTask, 'id' | 'createdAt' | 'retryCount' | 'status'>): string {
    const id = this.generateTaskId()
    const syncTask: SyncTask = {
      id,
      createdAt: Date.now(),
      retryCount: 0,
      status: 'pending',
      ...task
    }

    this.syncTasks.set(id, syncTask)
    this.saveSyncTasks()

    // 如果在线，立即尝试同步
    if (this.offlineState.isOnline) {
      this.syncTask(id)
    }

    return id
  }

  /**
   * 获取同步任务
   */
  getSyncTasks(status?: SyncTask['status']): SyncTask[] {
    const tasks = Array.from(this.syncTasks.values())
    return status ? tasks.filter(t => t.status === status) : tasks
  }

  /**
   * 删除同步任务
   */
  removeSyncTask(id: string): void {
    this.syncTasks.delete(id)
    this.saveSyncTasks()
  }

  /**
   * 清空同步任务
   */
  clearSyncTasks(status?: SyncTask['status']): void {
    if (status) {
      for (const [id, task] of this.syncTasks) {
        if (task.status === status) {
          this.syncTasks.delete(id)
        }
      }
    } else {
      this.syncTasks.clear()
    }
    this.saveSyncTasks()
  }

  /**
   * 启动同步定时器
   */
  private startSyncTimer(config: SyncConfig = {}): void {
    const { interval = 30000 } = config // 默认30秒

    if (this.syncTimer) {
      clearInterval(this.syncTimer)
    }

    this.syncTimer = window.setInterval(() => {
      if (this.offlineState.isOnline) {
        this.processSyncQueue()
      }
    }, interval)
  }

  /**
   * 处理同步队列
   */
  private async processSyncQueue(): Promise<void> {
    const pendingTasks = this.getSyncTasks('pending')

    for (const task of pendingTasks) {
      await this.syncTask(task.id)
    }
  }

  /**
   * 同步单个任务
   */
  private async syncTask(taskId: string): Promise<void> {
    const task = this.syncTasks.get(taskId)
    if (!task || task.status === 'syncing') return

    // 更新状态
    task.status = 'syncing'
    this.syncTasks.set(taskId, task)

    try {
      // 根据任务类型处理
      switch (task.type) {
        case 'api':
          await this.syncApiTask(task)
          break
        case 'upload':
          await this.syncUploadTask(task)
          break
        case 'download':
          await this.syncDownloadTask(task)
          break
        case 'custom':
          await this.syncCustomTask(task)
          break
      }

      // 同步成功
      task.status = 'success'
      this.emit('sync-success', task)
      this.removeSyncTask(taskId)
    } catch (__error) {
      // 同步失败
      task.retryCount++

      if (task.retryCount >= task.maxRetries) {
        task.status = 'failed'
        task.error = __error instanceof Error ? __error.message : '同步失败'
        this.emit('sync-failed', task)
      } else {
        task.status = 'pending'
        // 延迟重试
        setTimeout(
          () => {
            if (this.offlineState.isOnline) {
              this.syncTask(taskId)
            }
          },
          Math.min(1000 * Math.pow(2, task.retryCount), 30000)
        )
      }

      this.syncTasks.set(taskId, task)
      this.saveSyncTasks()
    }
  }

  /**
   * 同步API任务
   */
  private async syncApiTask(task: SyncTask): Promise<void> {
    const data = task.data as {
      url: string
      method: string
      headers?: HeadersInit
      body?: BodyInit
    }
    const { url, method, headers, body } = data
    const response = await fetch(url, { method, headers, body })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  }

  /**
   * 同步上传任务
   */
  private async syncUploadTask(task: SyncTask): Promise<void> {
    const data = task.data as { url: string; file: File; onProgress?: (progress: number) => void }
    const { url, file, onProgress } = data

    // 创建FormData
    const formData = new FormData()
    formData.append('file', file)

    // 使用XMLHttpRequest以支持进度
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()

      xhr.upload.addEventListener('progress', e => {
        if (e.lengthComputable && onProgress) {
          const progress = (e.loaded / e.total) * 100
          onProgress(progress)
        }
      })

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve()
        } else {
          reject(new Error(`Upload failed: ${xhr.status}`))
        }
      })

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'))
      })

      xhr.open('POST', url)
      xhr.send(formData)
    })
  }

  /**
   * 同步下载任务
   */
  private async syncDownloadTask(task: SyncTask): Promise<void> {
    const data = task.data as { url: string; filename: string }
    const { url, filename } = data

    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Download failed: ${response.status}`)
    }

    const blob = await response.blob()

    // 保存文件
    const a = document.createElement('a')
    a.href = URL.createObjectURL(blob)
    a.download = filename
    a.click()
    URL.revokeObjectURL(a.href)
  }

  /**
   * 同步自定义任务
   */
  private async syncCustomTask(task: SyncTask): Promise<void> {
    const data = task.data as { handler?: (task: SyncTask) => Promise<void> }
    const { handler } = data
    if (typeof handler === 'function') {
      await handler(task)
    }
  }

  /**
   * 保存同步任务到本地存储
   */
  private saveSyncTasks(): void {
    const tasks = Array.from(this.syncTasks.entries())
    localStorage.setItem('sync-tasks', JSON.stringify(tasks))
  }

  /**
   * 从本地存储加载同步任务
   */
  private loadSyncTasks(): void {
    const stored = localStorage.getItem('sync-tasks')
    if (stored) {
      try {
        const tasks = JSON.parse(stored) as Array<[string, SyncTask]>
        this.syncTasks = new Map(tasks)
      } catch (__error) {}
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 发送消息到Service Worker
   */
  private async sendMessageToSW(type: string, data: unknown): Promise<unknown> {
    if (!navigator.serviceWorker.controller) return

    return new Promise(resolve => {
      const channel = new MessageChannel()
      channel.port1.onmessage = event => resolve(event.data)

      navigator.serviceWorker.controller!.postMessage({ type, data }, [channel.port2])
    })
  }

  /**
   * 监听事件
   */
  on(event: string, handler: Function): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }

    const handlers = this.eventHandlers.get(event)!
    handlers.add(handler)

    // 返回取消监听函数
    return () => {
      handlers.delete(handler)
    }
  }

  /**
   * 触发事件
   */

  private emit(event: string, data?: unknown): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (__error) {
          // Error handled silently
        }
      })
    }
  }

  /**
   * 获取离线状态
   */
  getOfflineState(): OfflineState {
    return { ...this.offlineState }
  }

  /**
   * 是否在线
   */
  isOnline(): boolean {
    return this.offlineState.isOnline
  }

  /**
   * 获取网络类型
   */
  getNetworkType(): string {
    return this.offlineState.networkType || 'unknown'
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = null
    }

    this.eventHandlers.clear()
    this.syncTasks.clear()

    CacheManager.instance = null
  }
}

// 导出单例获取函数
export function getCacheManager(config?: CacheConfig): CacheManager {
  return CacheManager.getInstance(config)
}
