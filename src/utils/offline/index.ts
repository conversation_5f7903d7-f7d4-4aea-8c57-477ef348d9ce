/**
 * 离线功能导出
 */

export * from './types'
export { CacheManager, getCacheManager } from './CacheManager'

import { getCacheManager } from './CacheManager'
import { CacheStrategy } from './types'
import type { CacheRule } from './types'

/**
 * 初始化离线功能
 */
export function initializeOfflineSupport() {
  // 创建缓存管理器
  const cacheManager = getCacheManager({
    name: 'hr-cache',
    version: 1,
    strategy: CacheStrategy.NETWORK_FIRST,
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
    maxEntries: 500
  })

  // 添加缓存规则
  const rules: CacheRule[] = [
    // API数据 - 网络优先，快速过期
    {
      name: 'api-data',
      urlPattern: /^\/api\/(?!auth|upload)/,
      strategy: CacheStrategy.NETWORK_FIRST,
      options: {
        cacheName: 'api-cache',
        maxAge: 5 * 60 * 1000, // 5分钟
        networkTimeoutSeconds: 3
      }
    },
    // 认证相关 - 仅网络
    {
      name: 'auth',
      urlPattern: /^\/api\/auth/,
      strategy: CacheStrategy.NETWORK_ONLY
    },
    // 静态资源 - 缓存优先
    {
      name: 'static-assets',
      urlPattern: /\.(js|css|png|jpg|jpeg|svg|gif|woff2?|ttf|eot)$/,
      strategy: CacheStrategy.CACHE_FIRST,
      options: {
        cacheName: 'static-cache',
        maxAge: 30 * 24 * 60 * 60 * 1000 // 30天
      }
    },
    // 员工照片 - 缓存并更新
    {
      name: 'employee-photos',
      urlPattern: /^\/api\/employee\/photo/,
      strategy: CacheStrategy.STALE_WHILE_REVALIDATE,
      options: {
        cacheName: 'photo-cache',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
        maxEntries: 200
      }
    },
    // 报表数据 - 缓存优先，定期更新
    {
      name: 'reports',
      urlPattern: /^\/api\/reports/,
      strategy: CacheStrategy.CACHE_FIRST,
      options: {
        cacheName: 'report-cache',
        maxAge: 60 * 60 * 1000 // 1小时
      }
    }
  ]

  rules.forEach(rule => cacheManager.addRule(rule))

  // 预缓存关键资源
  const criticalResources = [
    '/api/organization/tree',
    '/api/employee/list?page=1&size=20',
    '/api/position/list',
    '/api/department/list'
  ]

  cacheManager.precache(criticalResources).catch(() => {
    })

  // 监听离线状态
  cacheManager.on('offline', () => {
    // 记录最后在线时间
    localStorage.setItem('last-online-time', Date.now().toString())
  })

  cacheManager.on('online', () => {
    })

  // 监听更新
  cacheManager.on('update-available', () => {
    // 可以在这里提示用户刷新页面
  })

  return cacheManager
}

/**
 * 注册后台同步任务
 */
export async function registerSyncTask(
  type: 'api' | 'upload' | 'download' | 'custom',
  data: unknown,
  options: {
    maxRetries?: number
    priority?: 'high' | 'normal' | 'low'
    metadata?: Record<string, unknown>
  } = {}
) {
  const cacheManager = getCacheManager()

  return cacheManager.addSyncTask({
    type,
    data,
    maxRetries: options.maxRetries || 3,
    metadata: options.metadata
  })
}

/**
 * 检查是否支持PWA
 */
export function checkPWASupport(): {
  serviceWorker: boolean
  notification: boolean
  pushManager: boolean
  backgroundSync: boolean
  persistentStorage: boolean
} {
  return {
    serviceWorker: 'serviceWorker' in navigator,
    notification: 'Notification' in window,
    pushManager: 'PushManager' in window,
    backgroundSync: 'sync' in ServiceWorkerRegistration.prototype,
    persistentStorage: 'storage' in navigator && 'persist' in navigator.storage
  }
}

/**
 * 请求持久化存储权限
 */
export async function requestPersistentStorage(): Promise<boolean> {
  if ('storage' in navigator && 'persist' in navigator.storage) {
    const isPersisted = await navigator.storage.persist()
    if (isPersisted) {
      }
    return isPersisted
  }
  return false
}

/**
 * 获取存储使用情况
 */
export async function getStorageUsage(): Promise<{
  usage: number
  quota: number
  percentage: number
} | null> {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    const estimate = await navigator.storage.estimate()
    const usage = estimate.usage || 0
    const quota = estimate.quota || 0
    const percentage = quota > 0 ? (usage / quota) * 100 : 0

    return {
      usage,
      quota,
      percentage
    }
  }
  return null
}

/**
 * 清理过期缓存
 */
export async function cleanupExpiredCache(): Promise<void> {
  const cacheManager = getCacheManager()
  const stats = await cacheManager.getCacheStats()

  // 清理过期的缓存
  for (const stat of stats) {
    if (stat.lastAccessed) {
      const age = Date.now() - stat.lastAccessed
      // 超过30天未访问的缓存
      if (age > 30 * 24 * 60 * 60 * 1000) {
        await cacheManager.clearCache(stat.cacheName)
      }
    }
  }
}

/**
 * 导出数据用于离线使用
 */
export async function exportOfflineData(
  dataType: 'employees' | 'organization' | 'all'
): Promise<Blob> {
  const data: unknown = {
    version: 1,
    exportDate: new Date().toISOString(),
    dataType
  }

  // 根据类型导出数据
  switch (dataType) {
    case 'employees':
      // 从缓存或API获取员工数据
      data.employees = await fetchEmployeeData()
      break

    case 'organization':
      // 获取组织架构数据
      data.organization = await fetchOrganizationData()
      break

    case 'all':
      // 导出所有数据
      data.employees = await fetchEmployeeData()
      data.organization = await fetchOrganizationData()
      data.positions = await fetchPositionData()
      break
  }

  // 转换为JSON并创建Blob
  const json = JSON.stringify(data, null, 2)
  return new Blob([json], { type: 'application/json' })
}

/**
 * 导入离线数据
 */
export async function importOfflineData(file: File): Promise<void> {
  const text = await file.text()
  const data = JSON.parse(text)

  // 验证数据格式
  if (!data.version || !data.dataType) {
    throw new Error('无效的数据格式')
  }

  const cacheManager = getCacheManager()

  // 根据数据类型缓存数据
  if (data.employees) {
    await cacheManager.cacheRequest(
      '/api/employee/offline-data',
      new Response(JSON.stringify(data.employees))
    )
  }

  if (data.organization) {
    await cacheManager.cacheRequest(
      '/api/organization/offline-data',
      new Response(JSON.stringify(data.organization))
    )
  }

  if (data.positions) {
    await cacheManager.cacheRequest(
      '/api/position/offline-data',
      new Response(JSON.stringify(data.positions))
    )
  }
}

// 辅助函数
async function fetchEmployeeData() {
  // 实际实现中应该从API或缓存获取
  return []
}

async function fetchOrganizationData() {
  // 实际实现中应该从API或缓存获取
  return []
}

async function fetchPositionData() {
  // 实际实现中应该从API或缓存获取
  return []
}
