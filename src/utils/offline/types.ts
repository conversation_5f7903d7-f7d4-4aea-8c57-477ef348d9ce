/**
 * 离线缓存相关类型定义
 */

/**
 * 缓存策略类型
 */
export enum CacheStrategy {
  // 缓存优先：先查找缓存，缓存没有再请求网络
  CACHE_FIRST = 'cache-first',
  // 网络优先：先请求网络，失败后使用缓存
  NETWORK_FIRST = 'network-first',
  // 仅缓存：只使用缓存，不请求网络
  CACHE_ONLY = 'cache-only',
  // 仅网络：只请求网络，不使用缓存
  NETWORK_ONLY = 'network-only',
  // 缓存并更新：使用缓存的同时，后台更新缓存
  STALE_WHILE_REVALIDATE = 'stale-while-revalidate'
}

/**
 * 缓存配置
 */
export interface CacheConfig {
  // 缓存名称
  name: string
  // 缓存版本
  version: number
  // 缓存策略
  strategy: CacheStrategy
  // 缓存过期时间（毫秒）
  maxAge?: number
  // 最大缓存条目数
  maxEntries?: number
  // 需要缓存的URL模式
  urlPatterns?: RegExp[]
  // 需要排除的URL模式
  excludePatterns?: RegExp[]
  // 是否缓存POST请求
  cachePOST?: boolean
}

/**
 * 缓存规则
 */
export interface CacheRule {
  // 规则名称
  name: string
  // URL匹配模式
  urlPattern: RegExp | string
  // 缓存策略
  strategy: CacheStrategy
  // 缓存配置
  options?: {
    cacheName?: string
    maxAge?: number
    maxEntries?: number
    networkTimeoutSeconds?: number
    ignoreVary?: boolean
    ignoreSearch?: boolean
    plugins?: CachePlugin[]
  }
}

/**
 * 缓存插件
 */
export interface CachePlugin {
  // 请求将被缓存前调用
  requestWillFetch?: (options: { request: Request }) => Request | Promise<Request>
  // 响应将被缓存前调用
  cacheWillUpdate?: (options: {
    request: Request
    response: Response
  }) => Response | null | Promise<Response | null>
  // 缓存响应已使用后调用
  cachedResponseWillBeUsed?: (options: {
    request: Request
    cachedResponse: Response | null
  }) => Response | null | Promise<Response | null>
  // 缓存key生成
  cacheKeyWillBeUsed?: (options: { request: Request; mode: string }) => string | Promise<string>
}

/**
 * 离线状态
 */
export interface OfflineState {
  // 是否在线
  isOnline: boolean
  // 最后在线时间
  lastOnlineTime?: number
  // 离线时长（毫秒）
  offlineDuration?: number
  // 网络类型
  networkType?: 'wifi' | '4g' | '3g' | '2g' | 'slow-2g' | 'offline' | 'unknown'
  // 网络速度（Mbps）
  networkSpeed?: number
}

/**
 * 同步任务
 */
export interface SyncTask {
  // 任务ID
  id: string
  // 任务类型
  type: 'api' | 'upload' | 'download' | 'custom'
  // 任务数据

  data: unknown
  // 创建时间
  createdAt: number
  // 重试次数
  retryCount: number
  // 最大重试次数
  maxRetries: number
  // 任务状态
  status: 'pending' | 'syncing' | 'success' | 'failed'
  // 错误信息
  error?: string
  // 任务元数据
  metadata?: Record<string, unknown>
}

/**
 * 同步配置
 */
export interface SyncConfig {
  // 同步间隔（毫秒）
  interval?: number
  // 最大重试次数
  maxRetries?: number
  // 重试延迟（毫秒）
  retryDelay?: number
  // 批量同步大小
  batchSize?: number
  // 同步优先级
  priority?: 'high' | 'normal' | 'low'
}

/**
 * 预缓存配置
 */
export interface PrecacheConfig {
  // 需要预缓存的URLs
  urls: string[]
  // 缓存名称
  cacheName?: string
  // 是否在安装时缓存
  cacheOnInstall?: boolean
  // 是否在激活时清理旧缓存
  cleanupOnActivate?: boolean
}

/**
 * 缓存统计
 */
export interface CacheStats {
  // 缓存名称
  cacheName: string
  // 缓存条目数
  count: number
  // 缓存大小（字节）
  size: number
  // 命中次数
  hits: number
  // 未命中次数
  misses: number
  // 命中率
  hitRate: number
  // 最后访问时间
  lastAccessed?: number
}

/**
 * 更新提示配置
 */
export interface UpdatePromptConfig {
  // 是否自动检查更新
  autoCheck?: boolean
  // 检查间隔（毫秒）
  checkInterval?: number
  // 是否强制更新
  forceUpdate?: boolean
  // 更新提示文本
  message?: string
  // 确认按钮文本
  confirmText?: string
  // 取消按钮文本
  cancelText?: string
}

/**
 * PWA配置
 */
export interface PWAConfig {
  // 应用名称
  name: string
  // 应用简称
  shortName: string
  // 应用描述
  description: string
  // 主题色
  themeColor: string
  // 背景色
  backgroundColor: string
  // 显示模式
  display: 'fullscreen' | 'standalone' | 'minimal-ui' | 'browser'
  // 屏幕方向
  orientation?: 'any' | 'natural' | 'landscape' | 'portrait'
  // 启动画面
  splashScreens?: Array<{
    src: string
    sizes: string
    type: string
  }>
  // 图标
  icons: Array<{
    src: string
    sizes: string
    type: string
    purpose?: 'maskable' | 'any'
  }>
}

/**
 * 离线回退页面配置
 */
export interface OfflineFallbackConfig {
  // 页面URL
  pagePath: string
  // 图片URL
  imagePath?: string
  // 字体URLs
  fontPaths?: string[]
  // 样式URLs
  stylePaths?: string[]
  // 脚本URLs
  scriptPaths?: string[]
}
