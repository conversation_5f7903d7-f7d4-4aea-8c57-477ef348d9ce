/**
 * 响应式适配工具类
 * 提供多端适配、断点管理、自适应布局等功能
 */

import { ref, Ref, computed, onMounted, onUnmounted } from 'vue'
import echarts, { type ECOption } from '@/utils/echarts'

// 断点定义
export interface Breakpoint {
  name: string
  min?: number
  max?: number
  cols?: number
  gutter?: number
}

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop' | 'wide'

// 方向类型
export type OrientationType = 'portrait' | 'landscape'

// 响应式配置
export interface ResponsiveConfig {
  breakpoints?: Record<string, Breakpoint>
  enableTouch?: boolean
  enableOrientation?: boolean
  enableDarkMode?: boolean
  enableHighDPI?: boolean
}

export class ResponsiveAdapter {
  private static instance: ResponsiveAdapter
  private resizeObserver: ResizeObserver | null = null
  private orientationHandler: ((e: Event) => void) | null = null

  // 默认断点配置
  private defaultBreakpoints: Record<string, Breakpoint> = {
    xs: { name: 'mobile', max: 575, cols: 4, gutter: 8 },
    sm: { name: 'mobile', min: 576, max: 767, cols: 8, gutter: 12 },
    md: { name: 'tablet', min: 768, max: 991, cols: 12, gutter: 16 },
    lg: { name: 'desktop', min: 992, max: 1199, cols: 12, gutter: 20 },
    xl: { name: 'desktop', min: 1200, max: 1919, cols: 12, gutter: 24 },
    xxl: { name: 'wide', min: 1920, cols: 12, gutter: 24 }
  }

  private config: ResponsiveConfig

  private constructor(config: ResponsiveConfig = {}) {
    this.config = {
      breakpoints: this.defaultBreakpoints,
      enableTouch: true,
      enableOrientation: true,
      enableDarkMode: true,
      enableHighDPI: true,
      ...config
    }
  }

  static getInstance(config?: ResponsiveConfig): ResponsiveAdapter {
    if (!ResponsiveAdapter.instance) {
      ResponsiveAdapter.instance = new ResponsiveAdapter(config)
    }
    return ResponsiveAdapter.instance
  }

  /**
   * 响应式状态钩子
   */
  useResponsive() {
    const width = ref(window.innerWidth)
    const height = ref(window.innerHeight)
    const breakpoint = ref(this.getCurrentBreakpoint())
    const deviceType = ref(this.getDeviceType())
    const orientation = ref(this.getOrientation())
    const isMobile = computed(() => deviceType.value === 'mobile')
    const isTablet = computed(() => deviceType.value === 'tablet')
    const isDesktop = computed(() => deviceType.value === 'desktop' || deviceType.value === 'wide')
    const isTouch = ref(this.isTouchDevice())
    const isDarkMode = ref(this.isDarkMode())
    const pixelRatio = ref(window.devicePixelRatio || 1)

    const handleResize = () => {
      width.value = window.innerWidth
      height.value = window.innerHeight
      breakpoint.value = this.getCurrentBreakpoint()
      deviceType.value = this.getDeviceType()
      orientation.value = this.getOrientation()
    }

    const handleOrientationChange = () => {
      orientation.value = this.getOrientation()
      handleResize()
    }

    const handleThemeChange = (e: MediaQueryListEvent) => {
      isDarkMode.value = e.matches
    }

    onMounted(() => {
      window.addEventListener('resize', handleResize)

      if (this.config.enableOrientation) {
        window.addEventListener('orientationchange', handleOrientationChange)
      }

      if (this.config.enableDarkMode) {
        const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
        darkModeQuery.addEventListener('change', handleThemeChange)
      }
    })

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
    })

    return {
      width,
      height,
      breakpoint,
      deviceType,
      orientation,
      isMobile,
      isTablet,
      isDesktop,
      isTouch,
      isDarkMode,
      pixelRatio
    }
  }

  /**
   * 响应式图表配置
   */
  getChartOption(baseOption: ECOption, deviceType: DeviceType): ECOption {
    const responsiveOption = { ...baseOption }

    switch (deviceType) {
      case 'mobile':
        return this.getMobileChartOption(responsiveOption)
      case 'tablet':
        return this.getTabletChartOption(responsiveOption)
      case 'desktop':
      case 'wide':
        return this.getDesktopChartOption(responsiveOption)
      default:
        return responsiveOption
    }
  }

  /**
   * 移动端图表优化
   */
  private getMobileChartOption(option: ECOption): ECOption {
    return {
      ...option,
      grid: {
        left: '3%',
        right: '3%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      legend: {
        ...(option.legend as unknown),
        type: 'scroll',
        orient: 'horizontal',
        bottom: 0,
        textStyle: {
          fontSize: 10
        }
      },
      tooltip: {
        ...(option.tooltip as unknown),
        trigger: 'axis',
        confine: true,
        textStyle: {
          fontSize: 12
        }
      },
      toolbox: {
        show: false
      },
      xAxis: this.simplifyAxis(option.xAxis),
      yAxis: this.simplifyAxis(option.yAxis),
      series: this.simplifySeries(option.series as unknown[])
    }
  }

  /**
   * 平板图表优化
   */
  private getTabletChartOption(option: ECOption): ECOption {
    return {
      ...option,
      grid: {
        left: '5%',
        right: '5%',
        bottom: '8%',
        top: '12%',
        containLabel: true
      },
      legend: {
        ...(option.legend as unknown),
        textStyle: {
          fontSize: 12
        }
      },
      tooltip: {
        ...(option.tooltip as unknown),
        textStyle: {
          fontSize: 14
        }
      }
    }
  }

  /**
   * 桌面端图表优化
   */
  private getDesktopChartOption(option: ECOption): ECOption {
    return option
  }

  /**
   * 简化坐标轴
   */

  private simplifyAxis(axis: unknown): unknown {
    if (!axis) return axis

    const axisArray = Array.isArray(axis) ? axis : [axis]

    return axisArray.map(ax => ({
      ...ax,
      axisLabel: {
        ...ax.axisLabel,
        fontSize: 10,
        interval: 'auto',
        rotate: ax.type === 'category' ? 45 : 0
      },
      splitLine: {
        ...ax.splitLine,
        show: false
      }
    }))
  }

  /**
   * 简化系列配置
   */

  private simplifySeries(series: unknown[]): unknown[] {
    if (!series) return series

    return series.map(s => ({
      ...s,
      label: {
        ...s.label,
        show: false
      },
      emphasis: {
        ...s.emphasis,
        label: {
          show: true,
          fontSize: 10
        }
      }
    }))
  }

  /**
   * 响应式表格配置
   */

  getTableColumns(columns: unknown[], deviceType: DeviceType): unknown[] {
    switch (deviceType) {
      case 'mobile':
        // 移动端只显示关键列
        return columns.filter(col => col.important !== false).slice(0, 3)
      case 'tablet':
        // 平板显示主要列
        return columns.filter(col => col.hideOnTablet !== true)
      default:
        // 桌面端显示全部列
        return columns
    }
  }

  /**
   * 响应式布局网格
   */
  getGridLayout(deviceType: DeviceType) {
    const breakpoint = this.getBreakpointByDevice(deviceType)

    return {
      cols: breakpoint?.cols || 12,
      gutter: breakpoint?.gutter || 16,
      rowHeight: deviceType === 'mobile' ? 80 : 100
    }
  }

  /**
   * 获取当前断点
   */
  getCurrentBreakpoint(): string {
    const width = window.innerWidth
    const breakpoints = this.config.breakpoints!

    for (const key in breakpoints) {
      const bp = breakpoints[key]
      if ((!bp.min || width >= bp.min) && (!bp.max || width <= bp.max)) {
        return key
      }
    }

    return 'md'
  }

  /**
   * 获取设备类型
   */
  getDeviceType(): DeviceType {
    const breakpoint = this.getCurrentBreakpoint()
    const bp = this.config.breakpoints![breakpoint]

    return (bp?.name || 'desktop') as DeviceType
  }

  /**
   * 获取设备方向
   */
  getOrientation(): OrientationType {
    return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
  }

  /**
   * 判断是否触摸设备
   */
  isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  }

  /**
   * 判断是否深色模式
   */
  isDarkMode(): boolean {
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  }

  /**
   * 根据设备类型获取断点
   */
  private getBreakpointByDevice(deviceType: DeviceType): Breakpoint | undefined {
    const breakpoints = this.config.breakpoints!

    for (const key in breakpoints) {
      if (breakpoints[key].name === deviceType) {
        return breakpoints[key]
      }
    }

    return undefined
  }

  /**
   * 创建响应式样式
   */
  createResponsiveStyles() {
    const styles: string[] = []
    const breakpoints = this.config.breakpoints!

    // 生成断点样式
    for (const key in breakpoints) {
      const bp = breakpoints[key]
      const conditions: string[] = []

      if (bp.min) conditions.push(`(min-width: ${bp.min}px)`)
      if (bp.max) conditions.push(`(max-width: ${bp.max}px)`)

      if (conditions.length > 0) {
        styles.push(`
          @media ${conditions.join(' and ')} {
            .responsive-${key} { display: block !important; }
            .responsive-hide-${key} { display: none !important; }
          }
        `)
      }
    }

    // 生成网格样式
    for (let i = 1; i <= 12; i++) {
      styles.push(`
        .col-${i} { flex: 0 0 ${(i / 12) * 100}%; max-width: ${(i / 12) * 100}%; }
      `)
    }

    return styles.join('\n')
  }

  /**
   * 监听容器大小变化
   */
  observeContainer(
    container: HTMLElement,
    callback: (width: number, height: number) => void
  ): () => void {
    if (!this.resizeObserver) {
      this.resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect
          callback(width, height)
        }
      })
    }

    this.resizeObserver.observe(container)

    return () => {
      if (this.resizeObserver) {
        this.resizeObserver.unobserve(container)
      }
    }
  }

  /**
   * 获取最佳图片尺寸
   */
  getImageSize(baseSize: number, deviceType: DeviceType): number {
    const multipliers: Record<DeviceType, number> = {
      mobile: 0.5,
      tablet: 0.75,
      desktop: 1,
      wide: 1.25
    }

    const pixelRatio = window.devicePixelRatio || 1
    return Math.round(baseSize * multipliers[deviceType] * pixelRatio)
  }

  /**
   * 获取响应式字体大小
   */
  getFontSize(baseSize: number, deviceType: DeviceType): number {
    const multipliers: Record<DeviceType, number> = {
      mobile: 0.875,
      tablet: 0.9375,
      desktop: 1,
      wide: 1.125
    }

    return Math.round(baseSize * multipliers[deviceType])
  }
}

// 导出单例
export const responsiveAdapter = ResponsiveAdapter.getInstance()

// 导出便捷方法
export const useResponsive = () => responsiveAdapter.useResponsive()
export const getChartOption = (option: ECOption, deviceType: DeviceType) =>
  responsiveAdapter.getChartOption(option, deviceType)

export const getTableColumns = (columns: unknown[], deviceType: DeviceType) =>
  responsiveAdapter.getTableColumns(columns, deviceType)
