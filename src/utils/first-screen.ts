/**
 * 首屏加载优化工具
 * 提供关键资源预加载、延迟加载等功能
 */

// 资源优先级
type ResourcePriority = 'critical' | 'high' | 'medium' | 'low' | 'lazy'

// 资源类型
type ResourceType = 'script' | 'style' | 'image' | 'font' | 'data'

// 资源配置
interface ResourceConfig {
  url: string
  type: ResourceType
  priority: ResourcePriority
  async?: boolean
  defer?: boolean
  preload?: boolean
  prefetch?: boolean
  crossOrigin?: string
  integrity?: string
  media?: string
}

// 首屏优化配置
interface FirstScreenConfig {
  // 关键资源列表
  criticalResources?: ResourceConfig[]
  // 预加载资源
  preloadResources?: ResourceConfig[]
  // 预取资源
  prefetchResources?: ResourceConfig[]
  // 延迟加载时间
  lazyLoadDelay?: number
  // 是否启用资源提示
  resourceHints?: boolean
  // 是否启用关键 CSS 内联
  inlineCriticalCSS?: boolean
}

/**
 * 首屏加载优化管理器
 */
class FirstScreenOptimizer {
  private config: FirstScreenConfig
  private loadedResources: Set<string> = new Set()
  private resourceTimings: Map<string, number> = new Map()
  private observer: PerformanceObserver | null = null

  constructor(config: FirstScreenConfig = {}) {
    this.config = {
      lazyLoadDelay: 3000,
      resourceHints: true,
      inlineCriticalCSS: true,
      ...config
    }

    this.initPerformanceObserver()
  }

  /**
   * 初始化性能观察器
   */
  private initPerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming
            this.resourceTimings.set(resourceEntry.name, resourceEntry.duration)
          }
        }
      })

      this.observer.observe({ entryTypes: ['resource'] })
    }
  }

  /**
   * 加载关键资源
   */
  async loadCriticalResources(): Promise<void> {
    const critical = this.config.criticalResources || []

    // 按优先级排序
    const sorted = critical.sort((a, b) => {
      const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3, lazy: 4 }
      return priorityOrder[a.priority] - priorityOrder[b.priority]
    })

    // 加载资源
    const promises = sorted.map(resource => this.loadResource(resource))
    await Promise.all(promises)
  }

  /**
   * 加载单个资源
   */
  private async loadResource(resource: ResourceConfig): Promise<void> {
    if (this.loadedResources.has(resource.url)) {
      return
    }

    const startTime = performance.now()

    try {
      switch (resource.type) {
        case 'script':
          await this.loadScript(resource)
          break
        case 'style':
          await this.loadStyle(resource)
          break
        case 'image':
          await this.loadImage(resource)
          break
        case 'font':
          await this.loadFont(resource)
          break
        case 'data':
          await this.loadData(resource)
          break
      }

      this.loadedResources.add(resource.url)
      const loadTime = performance.now() - startTime
      console.log(`Resource loaded: ${resource.url} in ${loadTime}ms`)
    } catch (__error) {
      console.error(`Failed to load resource: ${resource.url}`, __error)
    }
  }

  /**
   * 加载脚本
   */
  private loadScript(resource: ResourceConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = resource.url

      if (resource.async) script.async = true
      if (resource.defer) script.defer = true
      if (resource.crossOrigin) script.crossOrigin = resource.crossOrigin
      if (resource.integrity) script.integrity = resource.integrity

      script.onload = () => resolve()
      script.onerror = () => reject(new Error(`Failed to load script: ${resource.url}`))

      document.head.appendChild(script)
    })
  }

  /**
   * 加载样式
   */
  private loadStyle(resource: ResourceConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = resource.url

      if (resource.media) link.media = resource.media
      if (resource.crossOrigin) link.crossOrigin = resource.crossOrigin
      if (resource.integrity) link.integrity = resource.integrity

      link.onload = () => resolve()
      link.onerror = () => reject(new Error(`Failed to load style: ${resource.url}`))

      document.head.appendChild(link)
    })
  }

  /**
   * 加载图片
   */
  private loadImage(resource: ResourceConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image()

      if (resource.crossOrigin) img.crossOrigin = resource.crossOrigin

      img.onload = () => resolve()
      img.onerror = () => reject(new Error(`Failed to load image: ${resource.url}`))

      img.src = resource.url
    })
  }

  /**
   * 加载字体
   */
  private async loadFont(resource: ResourceConfig): Promise<void> {
    if ('fonts' in document) {
      const fontFace = new FontFace('CustomFont', `url(${resource.url})`)
      await fontFace.load()
      document.fonts.add(fontFace)
    } else {
      // 降级方案
      await this.loadStyle(resource)
    }
  }

  /**
   * 加载数据
   */
  private async loadData(resource: ResourceConfig): Promise<void> {
    const response = await fetch(resource.url, {
      credentials: resource.crossOrigin === 'use-credentials' ? 'include' : 'same-origin'
    })

    if (!response.ok) {
      throw new Error(`Failed to load data: ${resource.url}`)
    }

    // 缓存数据
    const data = await response.json()
    window.__PRELOADED_DATA__ = window.__PRELOADED_DATA__ || {}
    window.__PRELOADED_DATA__[resource.url] = data
  }

  /**
   * 添加资源提示
   */
  addResourceHints(): void {
    if (!this.config.resourceHints) return

    // 添加预加载
    this.config.preloadResources?.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = this.getResourceAs(resource.type)
      link.href = resource.url

      if (resource.crossOrigin) link.crossOrigin = resource.crossOrigin
      if (resource.media) link.media = resource.media

      document.head.appendChild(link)
    })

    // 添加预取
    this.config.prefetchResources?.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.as = this.getResourceAs(resource.type)
      link.href = resource.url

      document.head.appendChild(link)
    })
  }

  /**
   * 获取资源 as 属性
   */
  private getResourceAs(type: ResourceType): string {
    const asMap: Record<ResourceType, string> = {
      script: 'script',
      style: 'style',
      image: 'image',
      font: 'font',
      data: 'fetch'
    }
    return asMap[type] || 'fetch'
  }

  /**
   * 延迟加载非关键资源
   */
  lazyLoadResources(): void {
    // 使用 requestIdleCallback 或 setTimeout
    const loadLazy = () => {
      // 加载 Google Analytics
      this.loadGoogleAnalytics()

      // 加载其他第三方脚本
      this.loadThirdPartyScripts()

      // 加载非关键图片
      this.lazyLoadImages()
    }

    if ('requestIdleCallback' in window) {
      requestIdleCallback(loadLazy, { timeout: this.config.lazyLoadDelay })
    } else {
      setTimeout(loadLazy, this.config.lazyLoadDelay)
    }
  }

  /**
   * 加载 Google Analytics
   */
  private loadGoogleAnalytics(): void {
    if (window.gtag) return

    const script = document.createElement('script')
    script.async = true
    script.src = 'https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID'
    document.head.appendChild(script)

    window.dataLayer = window.dataLayer || []
    window.gtag = function (...args: unknown[]) {
      window.dataLayer.push(args)
    }
    window.gtag('js', new Date())
    window.gtag('config', 'GA_MEASUREMENT_ID')
  }

  /**
   * 加载第三方脚本
   */
  private loadThirdPartyScripts(): void {
    // 示例：加载客服系统
    const scripts = [
      { src: '//cdn.example.com/chat.js', id: 'chat-script' },
      { src: '//cdn.example.com/analytics.js', id: 'analytics-script' }
    ]

    scripts.forEach(({ src, id }) => {
      if (!document.getElementById(id)) {
        const script = document.createElement('script')
        script.id = id
        script.src = src
        script.async = true
        document.body.appendChild(script)
      }
    })
  }

  /**
   * 懒加载图片
   */
  private lazyLoadImages(): void {
    // 使用原生 lazy loading
    const images = document.querySelectorAll('img[data-lazy]')
    images.forEach(img => {
      const imgElement = img as HTMLImageElement
      if ('loading' in imgElement) {
        imgElement.loading = 'lazy'
        imgElement.src = imgElement.dataset.lazy || ''
      }
    })
  }

  /**
   * 获取首屏性能指标
   */
  getFirstScreenMetrics(): {
    FCP: number | null
    LCP: number | null
    TTI: number | null
    FID: number | null
    resourceCount: number
    totalResourceTime: number
  } {
    let FCP: number | null = null
    const LCP: number | null = null
    const TTI: number | null = null
    const FID: number | null = null

    // 获取 Paint Timing
    const paintEntries = performance.getEntriesByType('paint')
    paintEntries.forEach(entry => {
      if (entry.name === 'first-contentful-paint') {
        FCP = entry.startTime
      }
    })

    // 计算资源加载总时间
    let totalResourceTime = 0
    this.resourceTimings.forEach(time => {
      totalResourceTime += time
    })

    return {
      FCP,
      LCP,
      TTI,
      FID,
      resourceCount: this.loadedResources.size,
      totalResourceTime
    }
  }

  /**
   * 生成关键 CSS
   */
  async generateCriticalCSS(): Promise<string> {
    // 获取所有样式表
    const stylesheets = Array.from(document.styleSheets)
    const criticalCSS: string[] = []

    // 提取关键 CSS 规则
    for (const sheet of stylesheets) {
      try {
        const rules = Array.from(sheet.cssRules || [])

        rules.forEach(rule => {
          if (rule instanceof CSSStyleRule) {
            // 检查是否为关键选择器
            if (this.isCriticalSelector(rule.selectorText)) {
              criticalCSS.push(rule.cssText)
            }
          }
        })
      } catch (__e) {
        // 跨域样式表无法访问
      }
    }

    return criticalCSS.join('\n')
  }

  /**
   * 判断是否为关键选择器
   */
  private isCriticalSelector(selector: string): boolean {
    const criticalSelectors = [
      'body',
      'html',
      '#app',
      '.el-container',
      '.el-header',
      '.el-main',
      '.loading',
      '#app-loading',
      ':root',
      '*'
    ]

    return criticalSelectors.some(critical => selector.includes(critical))
  }

  /**
   * 销毁
   */
  destroy(): void {
    this.observer?.disconnect()
    this.loadedResources.clear()
    this.resourceTimings.clear()
  }
}

// 全局变量类型声明
declare global {
  interface Window {
    __PRELOADED_DATA__: Record<string, unknown>

    dataLayer: unknown[]

    gtag: (...args: unknown[]) => void
  }
}

// 创建并导出实例
export const firstScreenOptimizer = new FirstScreenOptimizer()

// 导出类和类型
export { FirstScreenOptimizer }
export type { FirstScreenConfig, ResourceConfig, ResourcePriority, ResourceType }
