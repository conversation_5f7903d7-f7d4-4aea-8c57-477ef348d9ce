import { ElMessage } from 'element-plus'

interface AuditLog {
  logId: string
  timestamp: Date
  operatorId: string
  operatorName: string
  operatorCode: string
  operatorDept: string
  clientIp: string
  userAgent: string
  sessionId: string
  operationType: string
  targetModule: string
  targetType: string
  targetId: string
  description: string
  requestParams: Record<string, unknown>
  responseData: Record<string, unknown>
  success: boolean
  errorMessage?: string
  duration: number
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  businessDate: string
  createdTime: Date
}

interface AuditLogDetail {
  detailId: string
  logId: string
  fieldName: string
  oldValue?: string
  newValue?: string
  changeType: 'create' | 'update' | 'delete'
}

interface AuditLogExportRequest {
  startDate: string
  endDate: string
  operationType?: string[]
  operatorId?: string
  targetModule?: string[]
  targetType?: string
  riskLevel?: string[]
  success?: boolean
  format: 'excel' | 'csv' | 'json'
  includeDetails: boolean
  maxRecords: number
  columns?: string[]
  reason: string
  sensitiveData: boolean
  encryptFile: boolean
}

interface AuditLogExportResponse {
  taskId: string
  fileName: string
  fileSize: number
  recordCount: number
  downloadUrl: string
  expireTime: Date
  checksum: string
  success: boolean
  error?: string
}

interface ExportAuditLog {
  exportId: string
  operatorId: string
  exportType: '日志查询' | '日志导出'
  queryConditions: Record<string, unknown>
  exportFormat: 'excel' | 'csv' | 'json'
  recordCount: number
  fileName: string
  fileSize: number
  exportTime: Date
  ipAddress: string
  reason: string
  approver?: string
}

enum AuditPermissionLevel {
  READ_ONLY = 1,      // 只能查看自己的操作日志
  DEPT_AUDIT = 2,     // 可查看本部门的操作日志
  SYSTEM_AUDIT = 3,   // 可查看全系统操作日志
  FULL_AUDIT = 4      // 可查看所有日志并导出
}

/**
 * CLEAN-AUX-014: 审计日志导出功能
 * 提供完整的审计日志查询、导出和管理功能，满足合规和安全要求
 */
export class AuditLogExport {
  private auditLogs: Map<string, AuditLog> = new Map()
  private auditLogDetails: Map<string, AuditLogDetail[]> = new Map()
  private exportAuditLogs: ExportAuditLog[] = []
  
  // 操作类型分类
  private readonly operationTypes = {
    authentication: {
      category: '身份认证',
      types: ['用户登录', '用户登出', '密码修改', '密码重置', '账号锁定', '账号解锁']
    },
    authorization: {
      category: '权限管理',
      types: ['角色分配', '权限变更', '角色创建', '角色删除', '权限审批']
    },
    dataManagement: {
      category: '数据管理',
      types: ['数据新增', '数据修改', '数据删除', '数据查询', '批量操作', '数据恢复']
    },
    workflow: {
      category: '工作流程',
      types: ['流程发起', '流程审批', '流程拒绝', '流程撤回', '流程转办', '流程催办']
    },
    importExport: {
      category: '导入导出',
      types: ['数据导入', '数据导出', '文件上传', '文件下载', '批量导入', '批量导出']
    },
    systemConfig: {
      category: '系统配置',
      types: ['参数配置', '模板管理', '字典维护', '组织架构', '岗位管理']
    },
    security: {
      category: '安全操作',
      types: ['敏感数据访问', '特权操作', '安全配置', '日志清理', '备份恢复']
    }
  }
  
  // 风险等级评估规则
  private readonly riskAssessmentRules = {
    critical: [
      '敏感数据导出', '大批量数据删除', '权限越级操作', '系统配置修改',
      '用户数据批量修改', '审计日志删除', '安全策略变更'
    ],
    high: [
      '敏感数据查询', '批量数据操作', '权限分配', '重要数据修改',
      '财务数据访问', '人事档案修改', '工作流干预'
    ],
    medium: [
      '普通数据修改', '报表导出', '流程审批', '常规查询',
      '文件上传下载', '模板配置'
    ],
    low: [
      '用户登录', '数据查询', '报表查看', '常规操作'
    ]
  }
  
  // 数据脱敏规则
  private readonly maskingRules = {
    operatorCode: { mask: false },  // 工号不脱敏，用于审计追踪
    clientIp: { 
      mask: true, 
      rule: (ip: string) => ip.replace(/(\d+\.\d+\.\d+)\.\d+/, '$1.*') 
    },
    userAgent: { 
      mask: true, 
      rule: (ua: string) => ua.substring(0, 50) + '...' 
    },
    requestParams: { 
      mask: true, 
   
      rule: (params: unknown) => this.maskSensitiveParams(params) 
    },
    responseData: { 
      mask: true, 
   
      rule: (data: unknown) => this.maskSensitiveData(data) 
    }
  }
  
  // 导出列配置
  private readonly exportColumns = {
    basic: [
      { key: 'logId', header: '日志ID', width: 25, required: true },
      { key: 'timestamp', header: '操作时间', width: 20, required: true, formatter: 'datetime'
  },
      { key: 'operatorName', header: '操作人', width: 15, required: true },
      { key: 'operatorCode', header: '工号', width: 15, required: true },
      { key: 'operatorDept', header: '部门', width: 20, required: true },
      { key: 'operationType', header: '操作类型', width: 20, required: true },
      { key: 'targetModule', header: '目标模块', width: 15, required: true },
      { key: 'targetType', header: '操作对象', width: 20, required: true },
      { key: 'description', header: '操作描述', width: 40, required: true },
      { key: 'success', header: '操作结果', width: 10, required: true, formatter: 'boolean'
  },
      { key: 'riskLevel', header: '风险等级', width: 10, required: true }
    ],
    detailed: [
      { key: 'targetId', header: '对象ID', width: 25, sensitive: false },
      { key: 'clientIp', header: '客户端IP', width: 15, sensitive: true },
      { key: 'sessionId', header: '会话ID', width: 30, sensitive: true },
      { key: 'duration', header: '耗时(ms)', width: 10, sensitive: false },
      { key: 'errorMessage', header: '错误信息', width: 30, sensitive: false },
      { key: 'businessDate', header: '业务日期', width: 15, sensitive: false }
    ],
    technical: [
      { key: 'userAgent', header: '浏览器信息', width: 50, sensitive: true },
      { key: 'requestParams', header: '请求参数', width: 50, sensitive: true },
      { key: 'responseData', header: '响应数据', width: 50, sensitive: true }
    ]
  }
  
  constructor() {
    this.initializeMockData()
  }
  
  // 初始化模拟数据
  private initializeMockData() {
    const mockLogs: AuditLog[] = [
      {
        logId: 'audit_001',
        timestamp: new Date('2025-01-13T09:15:30.123Z'),
        operatorId: 'user_001',
        operatorName: '张三',
        operatorCode: 'EMP001',
        operatorDept: '人事部',
        clientIp: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        sessionId: 'sess_123456789',
        operationType: '员工信息修改',
        targetModule: 'employee',
        targetType: 'Employee',
        targetId: 'emp_001',
        description: '修改员工张三的基本信息',
        requestParams: {
          employeeId: 'emp_001',
          fields: ['phone', 'address'],
          newValues: { phone: '***********', address: '杭州市西湖区' }
        },
        responseData: { success: true, message: '修改成功' },
  success: true,
        duration: 245,
        riskLevel: 'medium',
        businessDate: '2025-01-13',
        createdTime: new Date('2025-01-13T09:15:30.123Z')
      },
      {
        logId: 'audit_002',
        timestamp: new Date('2025-01-13T10:30:15.456Z'),
        operatorId: 'user_002',
        operatorName: '李四',
        operatorCode: 'EMP002',
        operatorDept: '人事部',
        clientIp: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        sessionId: 'sess_987654321',
        operationType: '薪酬数据导出',
        targetModule: 'salary',
        targetType: 'SalaryRecord',
        targetId: 'export_batch_001',
        description: '导出2024年12月全部员工薪酬数据',
        requestParams: {
          module: 'salary',
          period: '2024-12',
          scope: 'all',
          format: 'excel',
          recordCount: 1256
        },
        responseData: {
          success: true,
          fileName: 'salary_202412.xlsx',
          fileSize: 2048576,
          downloadUrl: '/downloads/salary_202412.xlsx' },
  success: true,
        duration: 5420,
        riskLevel: 'high',
        businessDate: '2025-01-13',
        createdTime: new Date('2025-01-13T10:30:15.456Z')
      },
      {
        logId: 'audit_003',
        timestamp: new Date('2025-01-13T14:20:45.789Z'),
        operatorId: 'user_003',
        operatorName: '王五',
        operatorCode: 'EMP003',
        operatorDept: '技术部',
        clientIp: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        sessionId: 'sess_456789123',
        operationType: '权限分配',
        targetModule: 'system',
        targetType: 'UserRole',
        targetId: 'role_assignment_001',
        description: '为用户赵六分配人事管理员角色',
        requestParams: {
          userId: 'user_004',
          roleId: 'hr_manager',
          operation: 'assign',
          approver: 'user_003'
  },
        responseData: { success: true, message: '权限分配成功' },
  success: true,
        duration: 123,
        riskLevel: 'high',
        businessDate: '2025-01-13',
        createdTime: new Date('2025-01-13T14:20:45.789Z')
      },
      {
        logId: 'audit_004',
        timestamp: new Date('2025-01-13T16:45:12.345Z'),
        operatorId: 'user_001',
        operatorName: '张三',
        operatorCode: 'EMP001',
        operatorDept: '人事部',
        clientIp: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        sessionId: 'sess_789123456',
        operationType: '审批流程操作',
        targetModule: 'workflow',
        targetType: 'ProcessInstance',
        targetId: 'process_001',
        description: '审批通过李四的调岗申请',
        requestParams: {
          processId: 'process_001',
          taskId: 'task_001',
          action: 'approve',
          comment: '同意调岗申请，符合业务需要'
  },
        responseData: { success: true, nextNode: 'hr_final_approval' },
  success: true,
        duration: 567,
        riskLevel: 'medium',
        businessDate: '2025-01-13',
        createdTime: new Date('2025-01-13T16:45:12.345Z')
      },
      {
        logId: 'audit_005',
        timestamp: new Date('2025-01-13T08:30:00.000Z'),
        operatorId: 'user_005',
        operatorName: '赵六',
        operatorCode: 'EMP005',
        operatorDept: '技术部',
        clientIp: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        sessionId: 'sess_111222333',
        operationType: '用户登录',
        targetModule: 'auth',
        targetType: 'UserSession',
        targetId: 'login_001',
        description: '系统登录成功',
        requestParams: {
          username: 'EMP005',
          loginTime: '2025-01-13T08:30:00.000Z',
          loginMethod: 'password'
  },
        responseData: {
          success: true,
          sessionId: 'sess_111222333',
          expireTime: '2025-01-13T16:30:00.000Z' },
  success: true,
        duration: 234,
        riskLevel: 'low',
        businessDate: '2025-01-13',
        createdTime: new Date('2025-01-13T08:30:00.000Z')
      }
    ]
    
    mockLogs.forEach(log => {
      this.auditLogs.set(log.logId, log)
    })
    
    // 添加详细变更记录
    this.auditLogDetails.set('audit_001', [
      {
        detailId: 'detail_001',
        logId: 'audit_001',
        fieldName: 'phone',
        oldValue: '***********',
        newValue: '***********',
        changeType: 'update'
  },
      {
        detailId: 'detail_002',
        logId: 'audit_001',
        fieldName: 'address',
        oldValue: '杭州市余杭区',
        newValue: '杭州市西湖区',
        changeType: 'update'
      }
    ])
  }
  
  // 执行审计日志导出
  async exportAuditLogs(
    request: AuditLogExportRequest,
    userRole: string = 'employee',
    userId: string = 'current_user'
  ): Promise<AuditLogExportResponse> {
    
    try {
      // 验证导出权限
      const permissionCheck = this.validateExportPermission(request, userRole)
      if (!permissionCheck.allowed) {
        throw new Error(`导出权限不足: ${permissionCheck.reason}`)
      }
      
      // 查询审计日志
      const logs = await this.queryAuditLogs(request, userRole, userId)
      
      // 数据脱敏处理
      const maskedLogs = this.applyDataMasking(logs, request.sensitiveData, userRole)
      
      // 生成导出文件
      const exportResult = await this.generateExportFile(maskedLogs, request)
      
      // 记录导出审计
      const exportAudit: ExportAuditLog = {
        exportId: this.generateId(),
        operatorId: userId,
        exportType: '日志导出',
        queryConditions: {
          startDate: request.startDate,
          endDate: request.endDate,
          operationType: request.operationType,
          targetModule: request.targetModule,
          riskLevel: request.riskLevel
        },
        exportFormat: request.format,
        recordCount: maskedLogs.length,
        fileName: exportResult.fileName,
        fileSize: exportResult.fileSize,
        exportTime: new Date(),
        ipAddress: this.getCurrentIp(),
        reason: request.reason
      }
      
      this.exportAuditLogs.push(exportAudit)
      
      ElMessage.success(`审计日志导出成功，共导出 ${maskedLogs.length} 条记录`)
      
      return {
        taskId: exportResult.taskId,
        fileName: exportResult.fileName,
        fileSize: exportResult.fileSize,
        recordCount: maskedLogs.length,
        downloadUrl: exportResult.downloadUrl,
        expireTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后过期
        checksum: exportResult.checksum,
        success: true
      }
      
    } catch (__error) {
      // 记录失败的导出尝试
      const failedExportAudit: ExportAuditLog = {
        exportId: this.generateId(),
        operatorId: userId,
        exportType: '日志导出',
        queryConditions: request,
        exportFormat: request.format,
        recordCount: 0,
        fileName: '',
        fileSize: 0,
        exportTime: new Date(),
        ipAddress: this.getCurrentIp(),
        reason: request.reason
      }
      
      this.exportAuditLogs.push(failedExportAudit)
      
      return {
        taskId: '',
        fileName: '',
        fileSize: 0,
        recordCount: 0,
        downloadUrl: '',
        expireTime: new Date(),
        checksum: '',
        success: false,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }
  
  // 验证导出权限
  private validateExportPermission(
    request: AuditLogExportRequest,
    userRole: string
  ): { allowed: boolean; reason?: string } {
    const permissionLevel = this.getPermissionLevel(userRole)
    
    // 检查基础权限
    if (permissionLevel < AuditPermissionLevel.DEPT_AUDIT) {
      return { allowed: false, reason: '用户权限不足，无法导出审计日志' }
    }
    
    // 检查敏感数据权限
    if (request.sensitiveData && permissionLevel < AuditPermissionLevel.FULL_AUDIT) {
      return { allowed: false, reason: '无权导出包含敏感信息的审计日志' }
    }
    
    // 检查数据范围权限
    if (request.maxRecords > 100000 && permissionLevel < AuditPermissionLevel.SYSTEM_AUDIT) {
      return { allowed: false, reason: '数据量过大，需要系统审计员权限' }
    }
    
    // 检查时间范围
    const daysDiff = Math.abs(
      new Date(request.endDate).getTime() - new Date(request.startDate).getTime()
    ) / (1000 * 60 * 60 * 24)
    
    if (daysDiff > 365 && permissionLevel < AuditPermissionLevel.FULL_AUDIT) {
      return { allowed: false, reason: '查询时间范围超过1年，需要完整审计权限' }
    }
    
    return { allowed: true }
  }
  
  // 获取权限等级
  private getPermissionLevel(userRole: string): AuditPermissionLevel {
    const roleMappings = {
      employee: AuditPermissionLevel.READ_ONLY,
      dept_head: AuditPermissionLevel.DEPT_AUDIT,
      hr_staff: AuditPermissionLevel.DEPT_AUDIT,
      hr_manager: AuditPermissionLevel.SYSTEM_AUDIT,
      audit_admin: AuditPermissionLevel.FULL_AUDIT,
      system_admin: AuditPermissionLevel.FULL_AUDIT
    }
    
    return roleMappings[userRole as keyof typeof roleMappings] || AuditPermissionLevel.READ_ONLY
  }
  
  // 查询审计日志
  private async queryAuditLogs(
    request: AuditLogExportRequest,
    userRole: string,
    userId: string
  ): Promise<AuditLog[]> {
    let logs = Array.from(this.auditLogs.values())
    
    // 时间范围筛选
    const startDate = new Date(request.startDate)
    const endDate = new Date(request.endDate)
    logs = logs.filter(log => 
      log.timestamp >= startDate && log.timestamp <= endDate
    )
    
    // 操作类型筛选
    if (request.operationType && request.operationType.length > 0) {
      logs = logs.filter(log => 
        request.operationType!.includes(log.operationType)
      )
    }
    
    // 操作人筛选
    if (request.operatorId) {
      logs = logs.filter(log => log.operatorId === request.operatorId)
    }
    
    // 目标模块筛选
    if (request.targetModule && request.targetModule.length > 0) {
      logs = logs.filter(log => 
        request.targetModule!.includes(log.targetModule)
      )
    }
    
    // 目标类型筛选
    if (request.targetType) {
      logs = logs.filter(log => log.targetType === request.targetType)
    }
    
    // 风险等级筛选
    if (request.riskLevel && request.riskLevel.length > 0) {
      logs = logs.filter(log => 
        request.riskLevel!.includes(log.riskLevel)
      )
    }
    
    // 操作结果筛选
    if (request.success !== undefined) {
      logs = logs.filter(log => log.success === request.success)
    }
    
    // 权限范围筛选
    const permissionLevel = this.getPermissionLevel(userRole)
    if (permissionLevel === AuditPermissionLevel.READ_ONLY) {
      logs = logs.filter(log => log.operatorId === userId)
    } else if (permissionLevel === AuditPermissionLevel.DEPT_AUDIT) {
      // 这里应该根据实际部门信息筛选，简化处理
      logs = logs.filter(log => 
        log.operatorDept === this.getUserDepartment(userId)
      )
    }
    
    // 数量限制
    if (request.maxRecords && logs.length > request.maxRecords) {
      logs = logs.slice(0, request.maxRecords)
    }
    
    // 按时间倒序排列
    logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    
    return logs
  }
  
  // 应用数据脱敏
  private applyDataMasking(
    logs: AuditLog[], 
    includeSensitive: boolean,
    userRole: string
  ): AuditLog[] {
    const permissionLevel = this.getPermissionLevel(userRole)
    
    return logs.map(log => {
      const maskedLog = { ...log }
      
      // 如果没有敏感数据权限或者不包含敏感数据，则应用脱敏
      if (!includeSensitive || permissionLevel < AuditPermissionLevel.FULL_AUDIT) {
        // IP地址脱敏
        if (this.maskingRules.clientIp.mask) {
          maskedLog.clientIp = this.maskingRules.clientIp.rule(log.clientIp)
        }
        
        // User Agent脱敏
        if (this.maskingRules.userAgent.mask) {
          maskedLog.userAgent = this.maskingRules.userAgent.rule(log.userAgent)
        }
        
        // 请求参数脱敏
        if (this.maskingRules.requestParams.mask) {
          maskedLog.requestParams = this.maskingRules.requestParams.rule(log.requestParams)
        }
        
        // 响应数据脱敏
        if (this.maskingRules.responseData.mask) {
          maskedLog.responseData = this.maskingRules.responseData.rule(log.responseData)
        }
      }
      
      return maskedLog
    })
  }
  
  // 脱敏敏感参数
   
  private maskSensitiveParams(params: unknown): unknown {
    if (!params || typeof params !== 'object') return params
    
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'credential']
    const masked = { ...params }
    
    Object.keys(masked).forEach(key => {
      if (sensitiveKeys.some(sk => key.toLowerCase().includes(sk))) {
        masked[key] = '***'
      } else if (typeof masked[key] === 'object') {
        masked[key] = this.maskSensitiveParams(masked[key])
      }
    })
    
    return masked
  }
  
  // 脱敏敏感数据
   
  private maskSensitiveData(data: unknown): unknown {
    if (!data || typeof data !== 'object') return data
    
    const sensitiveKeys = ['idCard', 'phone', 'email', 'salary', 'bankAccount']
    const masked = { ...data }
    
    Object.keys(masked).forEach(key => {
      if (sensitiveKeys.includes(key)) {
        if (key === 'idCard') {
          masked[key] = this.maskIdCard(data[key])
        } else if (key === 'phone') {
          masked[key] = this.maskPhone(data[key])
        } else if (key === 'email') {
          masked[key] = this.maskEmail(data[key])
        } else {
          masked[key] = '***'
        }
      } else if (typeof masked[key] === 'object') {
        masked[key] = this.maskSensitiveData(masked[key])
      }
    })
    
    return masked
  }
  
  // 身份证脱敏
  private maskIdCard(idCard: string): string {
    if (!idCard || idCard.length < 18) return idCard
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  }
  
  // 手机号脱敏
  private maskPhone(phone: string): string {
    if (!phone || phone.length < 11) return phone
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
  
  // 邮箱脱敏
  private maskEmail(email: string): string {
    if (!email || !email.includes('@')) return email
    return email.replace(/^(.{3}).*(@.*)/, '$1***$2')
  }
  
  // 生成导出文件
  private async generateExportFile(
    logs: AuditLog[],
    request: AuditLogExportRequest
  ): Promise<{
    taskId: string
    fileName: string
    fileSize: number
    downloadUrl: string
    checksum: string
  }> {
    const fileName = this.generateFileName(request)
    const taskId = this.generateId()
    
    switch (request.format) {
      case 'excel':
        return await this.generateExcelFile(logs, request, fileName, taskId)
      case 'csv':
        return await this.generateCsvFile(logs, request, fileName, taskId)
      case 'json':
        return await this.generateJsonFile(logs, request, fileName, taskId)
      default:
        throw new Error(`不支持的导出格式: ${request.format}`)
    }
  }
  
  // 生成Excel文件
  private async generateExcelFile(
    logs: AuditLog[],
    request: AuditLogExportRequest,
    fileName: string,
    taskId: string
  ): Promise<{
    taskId: string
    fileName: string
    fileSize: number
    downloadUrl: string
    checksum: string
  }> {
    // 确定导出列
    const columns = this.getExportColumns(request)
    
    // 生成Excel内容
    let content = '<?xml version="1.0"?>\n<Workbook>\n<Worksheet name="审计日志">\n'
    
    // 添加表头
    content += '<Row>\n'
    columns.forEach(col => {
      content += `<Cell><Data>${col.header}</Data></Cell>\n`
    })
    content += '</Row>\n'
    
    // 添加数据行
    logs.forEach(log => {
      content += '<Row>\n'
      columns.forEach(col => {
        const value = this.getColumnValue(log, col)
        content += `<Cell><Data>${value}</Data></Cell>\n`
      })
      content += '</Row>\n'
    })
    
    // 添加详细信息（如果需要）
    if (request.includeDetails) {
      content += this.generateDetailsSheet(logs)
    }
    
    content += '</Worksheet>\n</Workbook>'
    
    // 创建文件
    const blob = new Blob([content], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = URL.createObjectURL(blob)
    const checksum = this.generateChecksum(content)
    
    return {
      taskId,
      fileName,
      fileSize: blob.size,
      downloadUrl: url,
      checksum
    }
  }
  
  // 生成CSV文件
  private async generateCsvFile(
    logs: AuditLog[],
    request: AuditLogExportRequest,
    fileName: string,
    taskId: string
  ): Promise<{
    taskId: string
    fileName: string
    fileSize: number
    downloadUrl: string
    checksum: string
  }> {
    const columns = this.getExportColumns(request)
    
    // CSV内容
    let content = ''
    
    // 添加BOM头（Excel兼容）
    content = '\ufeff'
    
    // 添加表头
    const headers = columns.map(col => col.header)
    content += headers.join(',') + '\n'
    
    // 添加数据行
    logs.forEach(log => {
      const row = columns.map(col => {
        let value = this.getColumnValue(log, col)
        // CSV转义
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          value = `"${value.replace(/"/g, '""')}"`
        }
        return value
      })
      content += row.join(',') + '\n'
    })
    
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const checksum = this.generateChecksum(content)
    
    return {
      taskId,
      fileName,
      fileSize: blob.size,
      downloadUrl: url,
      checksum
    }
  }
  
  // 生成JSON文件
  private async generateJsonFile(
    logs: AuditLog[],
    request: AuditLogExportRequest,
    fileName: string,
    taskId: string
  ): Promise<{
    taskId: string
    fileName: string
    fileSize: number
    downloadUrl: string
    checksum: string
  }> {
    const exportData = {
      metadata: {
        exportTime: new Date().toISOString(),
        exportFormat: 'json',
        totalRecords: logs.length,
        queryConditions: {
          startDate: request.startDate,
          endDate: request.endDate,
          operationType: request.operationType,
          targetModule: request.targetModule,
          riskLevel: request.riskLevel
        },
        columns: this.getExportColumns(request).map(col => ({
          key: col.key,
          header: col.header,
          sensitive: col.sensitive
        }))
      },
      data: logs,
      details: request.includeDetails ? this.getLogDetails(logs) : null
    }
    
    const content = JSON.stringify(exportData, null, 2)
    const blob = new Blob([content], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const checksum = this.generateChecksum(content)
    
    return {
      taskId,
      fileName,
      fileSize: blob.size,
      downloadUrl: url,
      checksum
    }
  }
  
  // 获取导出列
  private getExportColumns(request: AuditLogExportRequest) {
    let columns = [...this.exportColumns.basic]
    
    // 添加详细列
    if (request.columns?.includes('detailed') || request.includeDetails) {
      columns = columns.concat(this.exportColumns.detailed)
    }
    
    // 添加技术列
    if (request.columns?.includes('technical') || request.sensitiveData) {
      columns = columns.concat(this.exportColumns.technical)
    }
    
    // 自定义列选择
    if (request.columns && request.columns.length > 0) {
      const customColumns = request.columns.filter(col => 
        !['detailed', 'technical'].includes(col)
      )
      if (customColumns.length > 0) {
        columns = columns.filter(col => 
          col.required || customColumns.includes(col.key)
        )
      }
    }
    
    return columns
  }
  
  // 获取列值
   
  private getColumnValue(log: AuditLog, column: unknown): unknown {
    let value = log[column.key as keyof AuditLog]
    
    // 格式化处理
    if (column.formatter) {
      switch (column.formatter) {
        case 'datetime':
          value = new Date(value as Date).toLocaleString('zh-CN')
          break
        case 'boolean':
          value = value ? '成功' : '失败'
          break
      }
    }
    
    // 对象转字符串
    if (typeof value === 'object' && value !== null) {
      value = JSON.stringify(value)
    }
    
    return value || ''
  }
  
  // 生成详细信息工作表
  private generateDetailsSheet(logs: AuditLog[]): string {
    let content = '\n<Worksheet name="变更详情">\n'
    
    // 详细信息表头
    content += '<Row>\n'
    const detailHeaders = ['日志ID', '字段名称', '原值', '新值', '变更类型']
    detailHeaders.forEach(header => {
      content += `<Cell><Data>${header}</Data></Cell>\n`
    })
    content += '</Row>\n'
    
    // 详细信息数据
    logs.forEach(log => {
      const details = this.auditLogDetails.get(log.logId) || []
      details.forEach(detail => {
        content += '<Row>\n'
        content += `<Cell><Data>${detail.logId}</Data></Cell>\n`
        content += `<Cell><Data>${detail.fieldName}</Data></Cell>\n`
        content += `<Cell><Data>${detail.oldValue || ''}</Data></Cell>\n`
        content += `<Cell><Data>${detail.newValue || ''}</Data></Cell>\n`
        content += `<Cell><Data>${detail.changeType}</Data></Cell>\n`
        content += '</Row>\n'
      })
    })
    
    content += '</Worksheet>\n'
    return content
  }
  
  // 获取日志详细信息
  private getLogDetails(logs: AuditLog[]) {
    const details: Record<string, AuditLogDetail[]> = {}
    logs.forEach(log => {
      const logDetails = this.auditLogDetails.get(log.logId)
      if (logDetails) {
        details[log.logId] = logDetails
      }
    })
    return details
  }
  
  // 生成文件名
  private generateFileName(request: AuditLogExportRequest): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19)
    const dateRange = `${request.startDate}_${request.endDate}`
    const format = request.format
    
    return `audit_log_${dateRange}_${timestamp}.${format}`
  }
  
  // 生成校验和
  private generateChecksum(content: string): string {
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString(16)
  }
  
  // 获取当前IP（模拟）
  private getCurrentIp(): string {
    return '*************'
  }
  
  // 获取用户部门（模拟）
  private getUserDepartment(userId: string): string {
    return '人事部'
  }
  
  // 生成唯一ID
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  // 获取操作类型列表
  getOperationTypes(): Array<{ category: string; types: string[] }> {
    return Object.values(this.operationTypes)
  }
  
  // 获取风险等级选项
  getRiskLevels(): Array<{ value: string; label: string; color: string }> {
    return [
      { value: 'low', label: '低风险', color: '#67C23A'
  },
      { value: 'medium', label: '中风险', color: '#E6A23C'
  },
      { value: 'high', label: '高风险', color: '#F56C6C'
  },
      { value: 'critical', label: '紧急风险', color: '#F56C6C' }
    ]
  }
  
  // 获取目标模块列表
  getTargetModules(): string[] {
    return [
      'auth', 'employee', 'organization', 'position', 'recruitment',
      'salary', 'attendance', 'performance', 'contract', 'workflow',
      'system', 'security', 'audit'
    ]
  }
  
  // 获取导出统计
  getExportStatistics(period: 'day' | 'week' | 'month' = 'month'): {
    totalExports: number
    successRate: number
    avgFileSize: number
    topOperators: { name: string; count: number }[]
    topModules: { module: string; count: number }[]
    riskDistribution: { level: string; count: number }[]
  } {
    const periodStart = new Date()
    switch (period) {
      case 'day':
        periodStart.setDate(periodStart.getDate() - 1)
        break
      case 'week':
        periodStart.setDate(periodStart.getDate() - 7)
        break
      case 'month':
        periodStart.setMonth(periodStart.getMonth() - 1)
        break
    }
    
    const recentExports = this.exportAuditLogs.filter(exp => 
      exp.exportTime >= periodStart
    )
    
    const totalExports = recentExports.length
    const successfulExports = recentExports.filter(exp => exp.recordCount > 0).length
    const successRate = totalExports > 0 ? successfulExports / totalExports : 0
    
    const fileSizes = recentExports
      .filter(exp => exp.fileSize > 0)
      .map(exp => exp.fileSize)
    const avgFileSize = fileSizes.length > 0 ? 
      fileSizes.reduce((a, b) => a + b, 0) / fileSizes.length : 0
    
    // 统计操作员
    const operatorCount: Record<string, number> = {}
    const moduleCount: Record<string, number> = {}
    const riskCount: Record<string, number> = { low: 0, medium: 0, high: 0, critical: 0 }
    
    const recentLogs = Array.from(this.auditLogs.values()).filter(log => 
      log.timestamp >= periodStart
    )
    
    recentLogs.forEach(log => {
      operatorCount[log.operatorName] = (operatorCount[log.operatorName] || 0) + 1
      moduleCount[log.targetModule] = (moduleCount[log.targetModule] || 0) + 1
      riskCount[log.riskLevel] = (riskCount[log.riskLevel] || 0) + 1
    })
    
    const topOperators = Object.entries(operatorCount)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
    
    const topModules = Object.entries(moduleCount)
      .map(([module, count]) => ({ module, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
    
    const riskDistribution = Object.entries(riskCount)
      .map(([level, count]) => ({ level, count }))
    
    return {
      totalExports,
      successRate,
      avgFileSize,
      topOperators,
      topModules,
      riskDistribution
    }
  }
  
  // 查询审计日志（不导出）
  async queryLogs(
    request: Partial<AuditLogExportRequest>,
    userRole: string = 'employee',
    userId: string = 'current_user'
  ): Promise<{ logs: AuditLog[]; total: number }> {
    const fullRequest: AuditLogExportRequest = {
      startDate: request.startDate || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10),
      endDate: request.endDate || new Date().toISOString().slice(0, 10),
      format: 'json',
      includeDetails: false,
      maxRecords: 1000,
      reason: '日志查询',
      sensitiveData: false,
      encryptFile: false,
      ...request
    }
    
    const logs = await this.queryAuditLogs(fullRequest, userRole, userId)
    const maskedLogs = this.applyDataMasking(logs, fullRequest.sensitiveData, userRole)
    
    return {
      logs: maskedLogs,
      total: maskedLogs.length
    }
  }
}

// 全局实例
export const auditLogExporter = new AuditLogExport()

// 便捷函数
export async function exportAuditLogs(request: AuditLogExportRequest, userRole: string = 'employee', userId: string = 'current_user'): Promise<AuditLogExportResponse> {
  return auditLogExporter.exportAuditLogs(request, userRole, userId)
}

export async function queryAuditLogs(request: Partial<AuditLogExportRequest>, userRole: string = 'employee', userId: string = 'current_user'): Promise<{ logs: AuditLog[]; total: number }> {
  return auditLogExporter.queryLogs(request, userRole, userId)
}