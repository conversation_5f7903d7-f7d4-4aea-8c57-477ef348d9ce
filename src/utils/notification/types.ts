/**
 * 通知消息相关类型定义
 */

/**
 * WebSocket 连接状态
 */
export type WebSocketState = 'connecting' | 'connected' | 'disconnected' | 'error'

/**
 * 通知消息类型
 */
export interface NotificationMessage {
  id: string
  type: string

  data: unknown
  timestamp: number
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  category?: string
  read?: boolean
}

/**
 * WebSocket 配置选项
 */
export interface WebSocketConfig {
  url: string
  reconnect?: boolean
  reconnectInterval?: number
  reconnectMaxAttempts?: number
  heartbeat?: boolean
  heartbeatInterval?: number
  messageQueueSize?: number
  showNotification?: boolean
  params?: Record<string, string>
  onConnected?: () => void
  onDisconnected?: (event: CloseEvent) => void
  onError?: (error: Event) => void
  onMessage?: (message: NotificationMessage) => void
}

/**
 * 通知类型枚举
 */
export enum NotificationType {
  // 系统通知
  SYSTEM = 'system',
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',

  // 业务通知
  ANNOUNCEMENT = 'announcement',
  TASK = 'task',
  APPROVAL = 'approval',
  REMINDER = 'reminder',
  ALERT = 'alert',

  // 人事相关
  EMPLOYEE_JOIN = 'employee_join',
  EMPLOYEE_LEAVE = 'employee_leave',
  CONTRACT_EXPIRE = 'contract_expire',
  LEAVE_REQUEST = 'leave_request',
  OVERTIME_APPROVAL = 'overtime_approval',

  // 薪资相关
  SALARY_SLIP = 'salary_slip',
  SALARY_ADJUSTMENT = 'salary_adjustment',
  BONUS_NOTIFICATION = 'bonus_notification',

  // 考勤相关
  ATTENDANCE_EXCEPTION = 'attendance_exception',
  CLOCK_IN_REMINDER = 'clock_in_reminder',
  LEAVE_BALANCE_LOW = 'leave_balance_low',

  // 绩效相关
  APPRAISAL_START = 'appraisal_start',
  APPRAISAL_DEADLINE = 'appraisal_deadline',
  APPRAISAL_RESULT = 'appraisal_result'
}

/**
 * 通知数据接口
 */
export interface NotificationData {
  title: string
  content: string
  duration?: number
  link?: string
  actions?: NotificationAction[]
  metadata?: Record<string, unknown>
}

/**
 * 通知操作
 */
export interface NotificationAction {
  label: string
  action: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'

  data?: unknown
}

/**
 * 通知中心配置
 */
export interface NotificationCenterConfig {
  maxNotifications: number
  autoMarkAsRead: boolean
  soundEnabled: boolean
  desktopEnabled: boolean
  groupByCategory: boolean
  persistToStorage: boolean
}

/**
 * 通知过滤器
 */
export interface NotificationFilter {
  types?: NotificationType[]
  categories?: string[]
  priorities?: Array<'low' | 'normal' | 'high' | 'urgent'>
  dateRange?: {
    start: Date
    end: Date
  }
  read?: boolean
  keyword?: string
}

/**
 * 通知统计
 */
export interface NotificationStats {
  total: number
  unread: number
  byType: Record<string, number>
  byCategory: Record<string, number>
  byPriority: Record<string, number>
}
