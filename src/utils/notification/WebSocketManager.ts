/**
 * WebSocket 管理器
 * 用于处理实时消息推送
 */

import { ElNotification } from 'element-plus'
import type { NotificationMessage, WebSocketConfig, WebSocketState } from './types'

export class WebSocketManager {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private reconnectAttempts = 0

  private messageHandlers = new Map<string, Array<(data: unknown) => void>>()
  private state: WebSocketState = 'disconnected'
  private messageQueue: NotificationMessage[] = []
  private isManualClose = false

  constructor(config: WebSocketConfig) {
    this.config = {
      url: '',
      reconnect: true,
      reconnectInterval: 5000,
      reconnectMaxAttempts: 5,
      heartbeat: true,
      heartbeatInterval: 30000,
      messageQueueSize: 100,
      ...config
    }
  }

  /**
   * 连接 WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.state === 'connected') {
        resolve()
        return
      }

      try {
        this.state = 'connecting'
        this.isManualClose = false

        // 构建 WebSocket URL
        const url = this.buildUrl()
        this.ws = new WebSocket(url)

        // 连接成功
        this.ws.onopen = () => {
          this.state = 'connected'
          this.reconnectAttempts = 0

          // 启动心跳
          if (this.config.heartbeat) {
            this.startHeartbeat()
          }

          // 发送队列中的消息
          this.flushMessageQueue()

          // 触发连接成功回调
          this.emit('connected')
          resolve()
        }

        // 接收消息
        this.ws.onmessage = _event => {
          this.handleMessage(event.data)
        }

        // 连接错误
        this.ws.onerror = _error => {
          this.state = 'error'
          this.emit('error', error)
          reject(error)
        }

        // 连接关闭
        this.ws.onclose = _event => {
          this.state = 'disconnected'
          this.stopHeartbeat()
          this.emit('disconnected', event)

          // 自动重连
          if (this.config.reconnect && !this.isManualClose) {
            this.scheduleReconnect()
          }
        }
      } catch (__error) {
        this.state = 'error'
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.isManualClose = true
    this.stopReconnect()
    this.stopHeartbeat()

    if (this.ws) {
      this.ws.close()
      this.ws = null
    }

    this.state = 'disconnected'
  }

  /**
   * 发送消息
   */

  send(type: string, data: unknown): void {
    const message: NotificationMessage = {
      id: this.generateId(),
      type,
      data,
      timestamp: Date.now()
    }

    if (this.state === 'connected' && this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
      this.emit('sent', message)
    } else {
      // 加入消息队列
      this.addToQueue(message)
      }
  }

  /**
   * 订阅消息类型
   */

  on(type: string, handler: (data: unknown) => void): () => void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, [])
    }

    const handlers = this.messageHandlers.get(type)!
    handlers.push(handler)

    // 返回取消订阅函数
    return () => {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 取消订阅
   */

  off(type: string, handler?: (data: unknown) => void): void {
    if (!handler) {
      this.messageHandlers.delete(type)
    } else {
      const handlers = this.messageHandlers.get(type)
      if (handlers) {
        const index = handlers.indexOf(handler)
        if (index > -1) {
          handlers.splice(index, 1)
        }
      }
    }
  }

  /**
   * 触发事件
   */

  private emit(type: string, data?: unknown): void {
    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (__error) {
          :`, error)
        }
      })
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(rawData: string): void {
    try {
      const message = JSON.parse(rawData) as NotificationMessage

      // 心跳响应
      if (message.type === 'pong') {
        return
      }

      // 触发消息处理器
      this.emit(message.type, message.data)
      this.emit('message', message)

      // 显示通知
      if (this.config.showNotification !== false) {
        this.showNotification(message)
      }
    } catch (__error) {
      }
  }

  /**
   * 显示通知
   */
  private showNotification(message: NotificationMessage): void {
    const { type, data } = message

    // 根据消息类型显示不同的通知
    switch (type) {
      case 'info':
        ElNotification.info({
          title: data.title || '通知',
          message: data.content,
          duration: data.duration || 4500,
          position: 'bottom-right'
        })
        break

      case 'success':
        ElNotification.success({
          title: data.title || '成功',
          message: data.content,
          duration: data.duration || 4500,
          position: 'bottom-right'
        })
        break

      case 'warning':
        ElNotification.warning({
          title: data.title || '警告',
          message: data.content,
          duration: data.duration || 4500,
          position: 'bottom-right'
        })
        break

      case 'error':
        ElNotification.error({
          title: data.title || '错误',
          message: data.content,
          duration: data.duration || 0,
          position: 'bottom-right'
        })
        break

      default:
        // 自定义消息类型
        this.emit(`notification:${type}`, data)
    }
  }

  /**
   * 构建 WebSocket URL
   */
  private buildUrl(): string {
    const { url, params } = this.config

    // 添加认证令牌
    const token = localStorage.getItem('token')
    const queryParams = new URLSearchParams({
      ...params,
      token: token || ''
    })

    return `${url}?${queryParams.toString()}`
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()

    this.heartbeatTimer = window.setInterval(() => {
      if (this.state === 'connected' && this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send('ping', { timestamp: Date.now() })
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 计划重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.reconnectMaxAttempts!) {
      this.emit('reconnectFailed')
      return
    }

    this.reconnectAttempts++
    const delay = this.config.reconnectInterval! * this.reconnectAttempts

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch(() => {
        })
    }, delay)
  }

  /**
   * 停止重连
   */
  private stopReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    this.reconnectAttempts = 0
  }

  /**
   * 添加到消息队列
   */
  private addToQueue(message: NotificationMessage): void {
    this.messageQueue.push(message)

    // 限制队列大小
    if (this.messageQueue.length > this.config.messageQueueSize!) {
      this.messageQueue.shift()
    }
  }

  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()!
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(message))
      }
    }
  }

  /**
   * 生成消息 ID
   */
  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取连接状态
   */
  getState(): WebSocketState {
    return this.state
  }

  /**
   * 是否已连接
   */
  isConnected(): boolean {
    return this.state === 'connected' && this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  /**
   * 获取消息队列长度
   */
  getQueueLength(): number {
    return this.messageQueue.length
  }

  /**
   * 清空消息队列
   */
  clearQueue(): void {
    this.messageQueue = []
  }
}

// 单例实例
let instance: WebSocketManager | null = null

/**
 * 获取 WebSocket 管理器实例
 */
export function getWebSocketManager(config?: WebSocketConfig): WebSocketManager {
  if (!instance) {
    if (!config) {
      throw new Error('首次调用需要提供配置')
    }
    instance = new WebSocketManager(config)
  }
  return instance
}

/**
 * 销毁 WebSocket 管理器实例
 */
export function destroyWebSocketManager(): void {
  if (instance) {
    instance.disconnect()
    instance = null
  }
}
