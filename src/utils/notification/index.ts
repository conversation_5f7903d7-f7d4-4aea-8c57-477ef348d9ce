/**
 * 通知系统导出
 */

export * from './types'
export { WebSocketManager, getWebSocketManager, destroyWebSocketManager } from './WebSocketManager'

// 通知工具函数
import type { NotificationMessage, NotificationType } from './types'

/**
 * 显示通知
 */
export function showNotification(type: NotificationType | string, title: string, content: string, options?: {
    duration?: number
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
    showClose?: boolean
    offset?: number
  }) {
  const {duration = 4500 = 4500, position = 'bottom-right', showClose = true = true, offset = 20 = 20} =  4500: _duration  end: Date }
  }): NotificationMessage[] {
  let filtered = [...notifications]
  
  if (filter.types?.length) {
    filtered = filtered.filter(n => filter.types!.includes(n.type))
  }
  
  if (filter.categories?.length) {
    filtered = filtered.filter(n => n.category && filter.categories!.includes(n.category))
  }
  
  if (filter.priorities?.length) {
    filtered = filtered.filter(n => n.priority && filter.priorities!.includes(n.priority))
  }
  
  if (filter.read !== undefined) {
    filtered = filtered.filter(n => n.read === filter.read)
  }
  
  if (filter.keyword) {
    const keyword = filter.keyword.toLowerCase()
    filtered = filtered.filter(n => {
      const title = n.data.title?.toLowerCase() || ''
      const content = n.data.content?.toLowerCase() || ''
      return title.includes(keyword) || content.includes(keyword)
    })
  }
  
  if (filter.dateRange) {
    const {start, end} = filter.dateRange
    filtered = filtered.filter(n => 
      n.timestamp >= start.getTime() && n.timestamp <= end.getTime()
    )
  }
  
  return filtered
}

/**
 * 按类别分组通知
 */
export function groupNotificationsByCategory(notifications: NotificationMessage[]): Record<string, NotificationMessage[]> {
  const groups: Record<string, NotificationMessage[]> = {}
  
  notifications.forEach(notification => {
    const category = notification.category || 'uncategorized'
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(notification)
  })
  
  return groups
}

/**
 * 排序通知
 */
export function sortNotifications(notifications: NotificationMessage[], sortBy: 'timestamp' | 'priority' = 'timestamp', order: 'asc' | 'desc' = 'desc'): NotificationMessage[] {
  const sorted = [...notifications]
  
  const priorityWeight = {
    low: 1,
    normal: 2,
    high: 3,
    urgent: 4
  }
  
  sorted.sort((a, b) => {
    let compareValue = 0
    
    if (sortBy === 'timestamp') {
      compareValue = a.timestamp - b.timestamp
    } else if (sortBy === 'priority') {
      const aWeight = priorityWeight[a.priority || 'normal']
      const bWeight = priorityWeight[b.priority || 'normal']
      compareValue = aWeight - bWeight
    }
    
    return order === 'desc' ? -compareValue : compareValue
  })
  
  return sorted
}