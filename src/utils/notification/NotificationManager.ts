/**
 * 通知管理器
 * 管理Web Push通知、本地通知和应用内通知
 */

import { ref, computed } from 'vue'
import { ElNotification } from 'element-plus'
import { offlineManager } from '../pwa/OfflineManager'
import { pushNotificationApi } from '@/api/pushNotification'

// 通知类型
export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  WORKFLOW = 'workflow',
  SYSTEM = 'system'
}

// 通知优先级
export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 通知配置
export interface NotificationConfig {
  id?: string
  title: string
  body: string
  type?: NotificationType
  priority?: NotificationPriority
  icon?: string
  badge?: string
  image?: string
  tag?: string
  requireInteraction?: boolean
  silent?: boolean
  vibrate?: number[]
  timestamp?: number

  data?: unknown
  actions?: NotificationAction[]
  onClick?: () => void
  onClose?: () => void

  onError?: (error: unknown) => void
}

// 通知动作
export interface NotificationAction {
  action: string
  title: string
  icon?: string
}

// 通知状态
export interface NotificationState {
  permission: NotificationPermission
  isSupported: boolean
  isPushEnabled: boolean
  subscription: PushSubscription | null
  unreadCount: number
  notifications: NotificationConfig[]
}

// 通知设置
export interface NotificationSettings {
  enabled: boolean
  sound: boolean
  vibration: boolean
  showInApp: boolean
  types: {
    [key in NotificationType]: boolean
  }
  priorities: {
    [key in NotificationPriority]: boolean
  }
}

export class NotificationManager {
  private static instance: NotificationManager
  private vapidPublicKey = '' // 需要从服务器获取

  // 响应式状态
  public state = ref<NotificationState>({
    permission: 'default',
    isSupported: false,
    isPushEnabled: false,
    subscription: null,
    unreadCount: 0,
    notifications: []
  })

  // 通知设置
  public settings = ref<NotificationSettings>({
    enabled: true,
    sound: true,
    vibration: true,
    showInApp: true,
    types: {
      [NotificationType.INFO]: true,
      [NotificationType.SUCCESS]: true,
      [NotificationType.WARNING]: true,
      [NotificationType.ERROR]: true,
      [NotificationType.WORKFLOW]: true,
      [NotificationType.SYSTEM]: true
    },
    priorities: {
      [NotificationPriority.LOW]: true,
      [NotificationPriority.NORMAL]: true,
      [NotificationPriority.HIGH]: true,
      [NotificationPriority.URGENT]: true
    }
  })

  private constructor() {
    this.init()
  }

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager()
    }
    return NotificationManager.instance
  }

  /**
   * 初始化
   */
  private async init(): Promise<void> {
    // 检查支持性
    this.state.value.isSupported = 'Notification' in window

    if (!this.state.value.isSupported) {
      return
    }

    // 获取当前权限状态
    this.state.value.permission = Notification.permission

    // 加载设置
    this.loadSettings()

    // 监听Service Worker消息
    this.setupServiceWorkerListeners()

    // 检查推送订阅
    await this.checkPushSubscription()
  }

  /**
   * 请求通知权限
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!this.state.value.isSupported) {
      return 'denied'
    }

    try {
      const permission = await Notification.requestPermission()
      this.state.value.permission = permission

      if (permission === 'granted') {
        // 权限授予后自动启用推送
        await this.enablePush()
      }

      return permission
    } catch (__error) {
      return 'denied'
    }
  }

  /**
   * 显示通知
   */
  async show(config: NotificationConfig): Promise<void> {
    // 检查设置
    if (!this.shouldShowNotification(config)) {
      return
    }

    // 添加到通知列表
    const notification: NotificationConfig = {
      ...config,
      id: config.id || this.generateId(),
      timestamp: config.timestamp || Date.now()
    }

    this.state.value.notifications.unshift(notification)
    this.state.value.unreadCount++

    // 显示应用内通知
    if (this.settings.value.showInApp) {
      this.showInAppNotification(notification)
    }

    // 显示系统通知
    if (this.state.value.permission === 'granted' && !document.hasFocus()) {
      await this.showSystemNotification(notification)
    }
  }

  /**
   * 显示系统通知
   */
  private async showSystemNotification(config: NotificationConfig): Promise<void> {
    try {
      const options: NotificationOptions = {
        body: config.body,
        icon: config.icon || '/icons/icon-192x192.png',
        badge: config.badge || '/icons/badge-72x72.png',
        tag: config.tag || config.type,
        requireInteraction:
          config.requireInteraction || config.priority === NotificationPriority.URGENT,
        silent: config.silent || !this.settings.value.sound,
        data: {
          ...config.data,
          id: config.id,
          type: config.type,
          priority: config.priority
        }
      }

      // 图片
      if (config.image) {
        options.image = config.image
      }

      // 震动
      if (this.settings.value.vibration && config.vibrate) {
        options.vibrate = config.vibrate
      } else if (this.settings.value.vibration) {
        // 默认震动模式
        switch (config.priority) {
          case NotificationPriority.URGENT:
            options.vibrate = [200, 100, 200, 100, 200]
            break
          case NotificationPriority.HIGH:
            options.vibrate = [200, 100, 200]
            break
          default:
            options.vibrate = [200]
        }
      }

      // 动作按钮
      if (config.actions && config.actions.length > 0) {
        options.actions = config.actions
      }

      // 使用Service Worker显示通知
      if (navigator.serviceWorker.controller) {
        await navigator.serviceWorker.ready
        const registration = await navigator.serviceWorker.getRegistration()

        if (registration) {
          await registration.showNotification(config.title, options)
        }
      } else {
        // 降级到Notification API
        const notification = new Notification(config.title, options)

        notification.onclick = () => {
          window.focus()
          config.onClick?.()
          notification.close()
        }

        notification.onclose = () => {
          config.onClose?.()
        }

        notification.onerror = _error => {
          config.onError?.(error)
        }
      }
    } catch (__error) {
      config.onError?.(error)
    }
  }

  /**
   * 显示应用内通知
   */
  private showInAppNotification(config: NotificationConfig): void {
    const typeMap = {
      [NotificationType.INFO]: 'info',
      [NotificationType.SUCCESS]: 'success',
      [NotificationType.WARNING]: 'warning',
      [NotificationType.ERROR]: 'error',
      [NotificationType.WORKFLOW]: 'info',
      [NotificationType.SYSTEM]: 'info'
    }

    ElNotification({
      title: config.title,
      message: config.body,
      type: typeMap[config.type || NotificationType.INFO] as unknown,
      duration: config.priority === NotificationPriority.URGENT ? 0 : 4500,
      position: 'top-right',
      showClose: true,
      onClick: config.onClick,
      onClose: config.onClose
    })
  }

  /**
   * 启用推送通知
   */
  async enablePush(): Promise<boolean> {
    if (!this.state.value.isSupported || this.state.value.permission !== 'granted') {
      return false
    }

    try {
      // 获取VAPID公钥
      if (!this.vapidPublicKey) {
        this.vapidPublicKey = await this.fetchVapidPublicKey()
      }

      // 订阅推送
      const subscription = await offlineManager.subscribePush(this.vapidPublicKey)

      if (subscription) {
        this.state.value.subscription = subscription
        this.state.value.isPushEnabled = true

        // 发送订阅信息到服务器
        await this.sendSubscriptionToServer(subscription)

        return true
      }

      return false
    } catch (__error) {
      return false
    }
  }

  /**
   * 禁用推送通知
   */
  async disablePush(): Promise<void> {
    if (!this.state.value.subscription) return

    try {
      await this.state.value.subscription.unsubscribe()
      this.state.value.subscription = null
      this.state.value.isPushEnabled = false

      // 通知服务器
      await this.removeSubscriptionFromServer()
    } catch (__error) {
      }
  }

  /**
   * 检查推送订阅
   */
  private async checkPushSubscription(): Promise<void> {
    if (!navigator.serviceWorker) return

    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()

      if (subscription) {
        this.state.value.subscription = subscription
        this.state.value.isPushEnabled = true
      }
    } catch (__error) {
      }
  }

  /**
   * 设置Service Worker监听
   */
  private setupServiceWorkerListeners(): void {
    if (!navigator.serviceWorker) return

    navigator.serviceWorker.addEventListener('message', _event => {
      if (event.data.type === 'notification-click') {
        this.handleNotificationClick(event.data)
      }
    })
  }

  /**
   * 处理通知点击
   */

  private handleNotificationClick(data: unknown): void {
    const notification = this.state.value.notifications.find(n => n.id === data.notificationId)

    if (notification) {
      notification.onClick?.()
      this.markAsRead(notification.id!)
    }

    // 根据通知类型导航
    if (data.type === NotificationType.WORKFLOW) {
      window.location.href = '/workflow/todo'
    }
  }

  /**
   * 标记为已读
   */
  markAsRead(id: string): void {
    const index = this.state.value.notifications.findIndex(n => n.id === id)

    if (index > -1) {
      this.state.value.notifications.splice(index, 1)
      this.state.value.unreadCount = Math.max(0, this.state.value.unreadCount - 1)
    }
  }

  /**
   * 标记全部已读
   */
  markAllAsRead(): void {
    this.state.value.notifications = []
    this.state.value.unreadCount = 0
  }

  /**
   * 清空通知
   */
  clear(): void {
    this.state.value.notifications = []
    this.state.value.unreadCount = 0
  }

  /**
   * 检查是否应该显示通知
   */
  private shouldShowNotification(config: NotificationConfig): boolean {
    if (!this.settings.value.enabled) return false

    const type = config.type || NotificationType.INFO
    const priority = config.priority || NotificationPriority.NORMAL

    return this.settings.value.types[type] && this.settings.value.priorities[priority]
  }

  /**
   * 获取VAPID公钥
   */
  private async fetchVapidPublicKey(): Promise<string> {
    try {
      const publicKey = await pushNotificationApi.getVapidPublicKey()
      return publicKey
    } catch (__error) {
      // 返回默认公钥，确保功能可用
      return 'BKd0lLuPtezYQphLrqYMpbzFHPpuVQiRZkk8H_ji9PxUjyXUyJvK7xVFPHlMGCPKZMGkxZXQGhVLFdsuMoD3WKw'
    }
  }

  /**
   * 发送订阅到服务器
   */
  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    try {
      await pushNotificationApi.registerPushSubscription(subscription)
      } catch (__error) {
      throw new Error('无法注册推送通知服务')
    }
  }

  /**
   * 从服务器移除订阅
   */
  private async removeSubscriptionFromServer(): Promise<void> {
    try {
      // 获取当前的推送订阅
      if ('serviceWorker' in navigator && 'PushManager' in window) {
        const registration = await navigator.serviceWorker.ready
        const subscription = await registration.pushManager.getSubscription()

        if (subscription) {
          // 使用endpoint来标识要移除的订阅
          await pushNotificationApi.unregisterPushSubscription(subscription.endpoint)
          } else {
          // 如果没有当前订阅，移除用户的所有订阅
          await pushNotificationApi.unregisterPushSubscription()
          }
      }
    } catch (__error) {
      // 静默失败，不影响用户体验
    }
  }

  /**
   * 加载设置
   */
  private loadSettings(): void {
    const saved = localStorage.getItem('notification-settings')
    if (saved) {
      try {
        Object.assign(this.settings.value, JSON.parse(saved))
      } catch (__error) {
        }
    }
  }

  /**
   * 保存设置
   */
  saveSettings(): void {
    localStorage.setItem('notification-settings', JSON.stringify(this.settings.value))
  }

  /**
   * 生成ID
   */
  private generateId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 发送测试通知
   */
  async sendTestNotification(): Promise<void> {
    await this.show({
      title: '测试通知',
      body: '这是一条测试通知，用于验证通知功能是否正常工作。',
      type: NotificationType.INFO,
      priority: NotificationPriority.NORMAL,
      vibrate: [200, 100, 200],
      actions: [
        { action: 'view', title: '查看' },
        { action: 'dismiss', title: '忽略' }
      ],
      onClick: () => {
        }
    })
  }
}

// 导出单例
export const notificationManager = NotificationManager.getInstance()

// 导出Vue组合式API
export function useNotifications() {
  const manager = notificationManager

  const permission = computed(() => manager.state.value.permission)
  const isEnabled = computed(() => manager.settings.value.enabled)
  const isPushEnabled = computed(() => manager.state.value.isPushEnabled)
  const unreadCount = computed(() => manager.state.value.unreadCount)
  const notifications = computed(() => manager.state.value.notifications)

  const hasPermission = computed(() => permission.value === 'granted')
  const canRequestPermission = computed(() => permission.value === 'default')

  return {
    // 状态
    permission,
    isEnabled,
    isPushEnabled,
    unreadCount,
    notifications,
    hasPermission,
    canRequestPermission,
    settings: manager.settings,

    // 方法
    requestPermission: () => manager.requestPermission(),
    show: (config: NotificationConfig) => manager.show(config),
    enablePush: () => manager.enablePush(),
    disablePush: () => manager.disablePush(),
    markAsRead: (id: string) => manager.markAsRead(id),
    markAllAsRead: () => manager.markAllAsRead(),
    clear: () => manager.clear(),
    saveSettings: () => manager.saveSettings(),
    sendTest: () => manager.sendTestNotification()
  }
}
