/**
 * 薪资公式解析器
 * 支持基本运算、函数调用、条件判断等
 */

// Token类型定义
export enum TokenType {
  NUMBER = 'NUMBER',
  VARIABLE = 'VARIABLE',
  OPERATOR = 'OPERATOR',
  FUNCTION = 'FUNCTION',
  LPAREN = 'LPAREN',
  RPAREN = 'RPAREN',
  COMMA = 'COMMA',
  EOF = 'EOF'
}

// Token接口
export interface Token {
  type: TokenType
  value: string
  position: number
}

// AST节点类型
export type ASTNodeType = 'number' | 'variable' | 'binary' | 'unary' | 'function' | 'conditional'

// AST节点接口
export interface ASTNode {
  type: ASTNodeType

  value?: unknown
  operator?: string
  left?: ASTNode
  right?: ASTNode
  condition?: ASTNode
  trueBranch?: ASTNode
  falseBranch?: ASTNode
  name?: string
  args?: ASTNode[]
}

// 词法分析器
export class Lexer {
  private input: string
  private position: number = 0
  private currentChar: string | null

  constructor(input: string) {
    this.input = input
    this.currentChar = this.input[0] || null
  }

  private advance(): void {
    this.position++
    this.currentChar = this.position < this.input.length ? this.input[this.position] : null
  }

  private skipWhitespace(): void {
    while (this.currentChar !== null && /\s/.test(this.currentChar)) {
      this.advance()
    }
  }

  private readNumber(): string {
    let result = ''
    while (this.currentChar !== null && (/\d/.test(this.currentChar) || this.currentChar === '.')) {
      result += this.currentChar
      this.advance()
    }
    return result
  }

  private readIdentifier(): string {
    let result = ''
    while (this.currentChar !== null && /[a-zA-Z0-9_]/.test(this.currentChar)) {
      result += this.currentChar
      this.advance()
    }
    return result
  }

  public getNextToken(): Token {
    while (this.currentChar !== null) {
      this.skipWhitespace()

      if (this.currentChar === null) {
        break
      }

      const position = this.position

      // 数字
      if (/\d/.test(this.currentChar)) {
        return { type: TokenType.NUMBER, value: this.readNumber(), position }
      }

      // 变量或函数
      if (/[a-zA-Z_]/.test(this.currentChar)) {
        const identifier = this.readIdentifier()
        // 检查是否是函数（后面跟着括号）
        this.skipWhitespace()
        if (this.currentChar === '(') {
          return { type: TokenType.FUNCTION, value: identifier, position }
        }
        return { type: TokenType.VARIABLE, value: identifier, position }
      }

      // 运算符
      if ('+-*/(),%'.includes(this.currentChar)) {
        const char = this.currentChar
        this.advance()

        if (char === '(') return { type: TokenType.LPAREN, value: char, position }
        if (char === ')') return { type: TokenType.RPAREN, value: char, position }
        if (char === ',') return { type: TokenType.COMMA, value: char, position }

        return { type: TokenType.OPERATOR, value: char, position }
      }

      // 比较运算符
      if ('<>=!'.includes(this.currentChar)) {
        let operator = this.currentChar
        this.advance()
        if (this.currentChar === '=') {
          operator += this.currentChar
          this.advance()
        }
        return { type: TokenType.OPERATOR, value: operator, position }
      }

      // 逻辑运算符
      if (this.currentChar === '&' || this.currentChar === '|') {
        const firstChar = this.currentChar
        this.advance()
        if (this.currentChar === firstChar) {
          this.advance()
          return { type: TokenType.OPERATOR, value: firstChar + firstChar, position }
        }
        throw new Error(`Invalid operator at position ${position}`)
      }

      // 条件运算符
      if (this.currentChar === '?') {
        this.advance()
        return { type: TokenType.OPERATOR, value: '?', position }
      }

      if (this.currentChar === ':') {
        this.advance()
        return { type: TokenType.OPERATOR, value: ':', position }
      }

      throw new Error(`Unexpected character '${this.currentChar}' at position ${position}`)
    }

    return { type: TokenType.EOF, value: '', position: this.position }
  }
}

// 语法分析器
export class Parser {
  private lexer: Lexer
  private currentToken: Token

  constructor(lexer: Lexer) {
    this.lexer = lexer
    this.currentToken = this.lexer.getNextToken()
  }

  private eat(tokenType: TokenType): void {
    if (this.currentToken.type === tokenType) {
      this.currentToken = this.lexer.getNextToken()
    } else {
      throw new Error(`Expected ${tokenType} but got ${this.currentToken.type}`)
    }
  }

  // 解析主入口
  public parse(): ASTNode {
    const node = this.parseConditional()
    if (this.currentToken.type !== TokenType.EOF) {
      throw new Error('Unexpected token after expression')
    }
    return node
  }

  // 解析条件表达式 (? :)
  private parseConditional(): ASTNode {
    const node = this.parseOr()

    if (this.currentToken.type === TokenType.OPERATOR && this.currentToken.value === '?') {
      this.eat(TokenType.OPERATOR)
      const trueBranch = this.parseConditional()

      if (this.currentToken.type !== TokenType.OPERATOR || this.currentToken.value !== ':') {
        throw new Error('Expected : in conditional expression')
      }
      this.eat(TokenType.OPERATOR)

      const falseBranch = this.parseConditional()

      return {
        type: 'conditional',
        condition: node,
        trueBranch,
        falseBranch
      }
    }

    return node
  }

  // 解析逻辑或 (||)
  private parseOr(): ASTNode {
    let node = this.parseAnd()

    while (this.currentToken.type === TokenType.OPERATOR && this.currentToken.value === '||') {
      const operator = this.currentToken.value
      this.eat(TokenType.OPERATOR)
      node = {
        type: 'binary',
        operator,
        left: node,
        right: this.parseAnd()
      }
    }

    return node
  }

  // 解析逻辑与 (&&)
  private parseAnd(): ASTNode {
    let node = this.parseComparison()

    while (this.currentToken.type === TokenType.OPERATOR && this.currentToken.value === '&&') {
      const operator = this.currentToken.value
      this.eat(TokenType.OPERATOR)
      node = {
        type: 'binary',
        operator,
        left: node,
        right: this.parseComparison()
      }
    }

    return node
  }

  // 解析比较运算 (<, >, <=, >=, ==, !=)
  private parseComparison(): ASTNode {
    let node = this.parseAddition()

    while (
      this.currentToken.type === TokenType.OPERATOR &&
      ['<', '>', '<=', '>=', '==', '!='].includes(this.currentToken.value)
    ) {
      const operator = this.currentToken.value
      this.eat(TokenType.OPERATOR)
      node = {
        type: 'binary',
        operator,
        left: node,
        right: this.parseAddition()
      }
    }

    return node
  }

  // 解析加减法
  private parseAddition(): ASTNode {
    let node = this.parseMultiplication()

    while (
      this.currentToken.type === TokenType.OPERATOR &&
      ['+', '-'].includes(this.currentToken.value)
    ) {
      const operator = this.currentToken.value
      this.eat(TokenType.OPERATOR)
      node = {
        type: 'binary',
        operator,
        left: node,
        right: this.parseMultiplication()
      }
    }

    return node
  }

  // 解析乘除法
  private parseMultiplication(): ASTNode {
    let node = this.parseUnary()

    while (
      this.currentToken.type === TokenType.OPERATOR &&
      ['*', '/', '%'].includes(this.currentToken.value)
    ) {
      const operator = this.currentToken.value
      this.eat(TokenType.OPERATOR)
      node = {
        type: 'binary',
        operator,
        left: node,
        right: this.parseUnary()
      }
    }

    return node
  }

  // 解析一元运算
  private parseUnary(): ASTNode {
    if (
      this.currentToken.type === TokenType.OPERATOR &&
      ['+', '-', '!'].includes(this.currentToken.value)
    ) {
      const operator = this.currentToken.value
      this.eat(TokenType.OPERATOR)
      return {
        type: 'unary',
        operator,
        right: this.parseUnary()
      }
    }

    return this.parsePrimary()
  }

  // 解析基本表达式
  private parsePrimary(): ASTNode {
    const token = this.currentToken

    // 数字
    if (token.type === TokenType.NUMBER) {
      this.eat(TokenType.NUMBER)
      return {
        type: 'number',
        value: parseFloat(token.value)
      }
    }

    // 函数调用
    if (token.type === TokenType.FUNCTION) {
      const functionName = token.value
      this.eat(TokenType.FUNCTION)
      this.eat(TokenType.LPAREN)

      const args: ASTNode[] = []
      if (this.currentToken.type !== TokenType.RPAREN) {
        args.push(this.parseConditional())

        while (this.currentToken.type === TokenType.COMMA) {
          this.eat(TokenType.COMMA)
          args.push(this.parseConditional())
        }
      }

      this.eat(TokenType.RPAREN)

      return {
        type: 'function',
        name: functionName,
        args
      }
    }

    // 变量
    if (token.type === TokenType.VARIABLE) {
      this.eat(TokenType.VARIABLE)
      return {
        type: 'variable',
        name: token.value
      }
    }

    // 括号表达式
    if (token.type === TokenType.LPAREN) {
      this.eat(TokenType.LPAREN)
      const node = this.parseConditional()
      this.eat(TokenType.RPAREN)
      return node
    }

    throw new Error(`Unexpected token: ${token.type}`)
  }
}

// 公式解析器主类
export class FormulaParser {
  /**
   * 解析公式字符串，返回AST
   */
  public parse(formula: string): ASTNode {
    const lexer = new Lexer(formula)
    const parser = new Parser(lexer)
    return parser.parse()
  }

  /**
   * 验证公式是否合法
   */
  public validate(formula: string): { valid: boolean; error?: string } {
    try {
      this.parse(formula)
      return { valid: true }
    } catch (__error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}
