/**
 * 薪资计算执行引擎
 * 负责执行公式计算、变量解析、函数调用等
 */

import { ASTNode } from './formulaParser'
// 计算上下文
export interface CalculationContext {
  variables: Map<string, number>
  functions: Map<string, Function>

  employee?: unknown
  period?: string
}

// 内置函数库
export const builtInFunctions = {
  // 数学函数
  MAX: (...args: number[]) => Math.max(...args),
  MIN: (...args: number[]) => Math.min(...args),
  ROUND: (value: number, decimals: number = 2) => {
    const factor = Math.pow(10, decimals)
    return Math.round(value * factor) / factor
  },
  FLOOR: (value: number) => Math.floor(value),
  CEIL: (value: number) => Math.ceil(value),
  ABS: (value: number) => Math.abs(value),

  // 条件函数

  IF: (condition: unknown, trueValue: unknown, falseValue: unknown) => {
    return condition ? trueValue : falseValue
  },

  // 聚合函数
  SUM: (...args: number[]) => args.reduce((sum, val) => sum + val, 0),
  AVG: (...args: number[]) => {
    if (args.length === 0) return 0
    return args.reduce((sum, val) => sum + val, 0) / args.length
  },

  COUNT: (...args: unknown[]) => args.length,

  // 日期相关
  DAYS: (startDate: string, endDate: string) => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  },

  // 薪资相关
  WORKDAYS: (year: number, month: number) => {
    // 简化计算，实际应该排除节假日
    const daysInMonth = new Date(year, month, 0).getDate()
    const weekends = Math.floor((daysInMonth * 2) / 7)
    return daysInMonth - weekends
  }
}

// 计算引擎类
export class CalculationEngine {
  private context: CalculationContext

  constructor(context?: Partial<CalculationContext>) {
    this.context = {
      variables: new Map(context?.variables || []),
      functions: new Map(Object.entries(builtInFunctions)),
      ...context
    }

    // 添加自定义函数
    if (context?.functions) {
      context.functions.forEach((func, name) => {
        this.context.functions.set(name, func)
      })
    }
  }

  /**
   * 设置变量值
   */
  public setVariable(name: string, value: number): void {
    this.context.variables.set(name, value)
  }

  /**
   * 批量设置变量
   */
  public setVariables(variables: Record<string, number>): void {
    Object.entries(variables).forEach(([name, value]) => {
      this.setVariable(name, value)
    })
  }

  /**
   * 注册自定义函数
   */
  public registerFunction(name: string, func: Function): void {
    this.context.functions.set(name, func)
  }

  /**
   * 执行AST节点计算
   */

  public evaluate(node: ASTNode): unknown {
    switch (node.type) {
      case 'number':
        return node.value

      case 'variable':
        if (!this.context.variables.has(node.name!)) {
          throw new Error(`Variable '${node.name}' is not defined`)
        }
        return this.context.variables.get(node.name!)

      case 'binary':
        return this.evaluateBinary(node)

      case 'unary':
        return this.evaluateUnary(node)

      case 'function':
        return this.evaluateFunction(node)

      case 'conditional':
        return this.evaluateConditional(node)

      default:
        throw new Error(`Unknown node type: ${node.type}`)
    }
  }

  /**
   * 执行二元运算
   */

  private evaluateBinary(node: ASTNode): unknown {
    const left = this.evaluate(node.left!)
    const right = this.evaluate(node.right!)

    switch (node.operator) {
      // 算术运算
      case '+':
        return left + right
      case '-':
        return left - right
      case '*':
        return left * right
      case '/':
        if (right === 0) throw new Error('Division by zero')
        return left / right
      case '%':
        return left % right

      // 比较运算
      case '<':
        return left < right
      case '>':
        return left > right
      case '<=':
        return left <= right
      case '>=':
        return left >= right
      case '==':
        return left == right
      case '!=':
        return left != right

      // 逻辑运算
      case '&&':
        return left && right
      case '||':
        return left || right

      default:
        throw new Error(`Unknown operator: ${node.operator}`)
    }
  }

  /**
   * 执行一元运算
   */

  private evaluateUnary(node: ASTNode): unknown {
    const operand = this.evaluate(node.right!)

    switch (node.operator) {
      case '+':
        return +operand
      case '-':
        return -operand
      case '!':
        return !operand
      default:
        throw new Error(`Unknown unary operator: ${node.operator}`)
    }
  }

  /**
   * 执行函数调用
   */

  private evaluateFunction(node: ASTNode): unknown {
    if (!this.context.functions.has(node.name!)) {
      throw new Error(`Function '${node.name}' is not defined`)
    }

    const func = this.context.functions.get(node.name!)
    const args = node.args?.map(arg => this.evaluate(arg)) || []

    try {
      return func(...args)
    } catch (__error) {
      throw new Error(`Error in function '${node.name}': ${error}`)
    }
  }

  /**
   * 执行条件表达式
   */

  private evaluateConditional(node: ASTNode): unknown {
    const condition = this.evaluate(node.condition!)

    if (condition) {
      return this.evaluate(node.trueBranch!)
    } else {
      return this.evaluate(node.falseBranch!)
    }
  }
}

// 个税计算类
export class TaxCalculator {
  // 个税起征点
  private static readonly THRESHOLD = 5000

  // 税率表
  private static readonly TAX_BRACKETS = [
    { min: 0, max: 3000, rate: 0.03, deduction: 0 },
    { min: 3000, max: 12000, rate: 0.1, deduction: 210 },
    { min: 12000, max: 25000, rate: 0.2, deduction: 1410 },
    { min: 25000, max: 35000, rate: 0.25, deduction: 2660 },
    { min: 35000, max: 55000, rate: 0.3, deduction: 4410 },
    { min: 55000, max: 80000, rate: 0.35, deduction: 7160 },
    { min: 80000, max: Infinity, rate: 0.45, deduction: 15160 }
  ]

  /**
   * 计算个人所得税
   */
  public static calculate(income: number, deductions: number = 0): number {
    // 应纳税所得额
    const taxableIncome = income - this.THRESHOLD - deductions

    if (taxableIncome <= 0) {
      return 0
    }

    // 查找适用税率
    const bracket = this.TAX_BRACKETS.find(b => taxableIncome > b.min && taxableIncome <= b.max)

    if (bracket) {
      return taxableIncome * bracket.rate - bracket.deduction
    }

    // 超过最高税率档
    const lastBracket = this.TAX_BRACKETS[this.TAX_BRACKETS.length - 1]
    return taxableIncome * lastBracket.rate - lastBracket.deduction
  }

  /**
   * 计算专项附加扣除
   */
  public static calculateSpecialDeductions(config: {
    childEducation?: number // 子女教育
    continuingEducation?: number // 继续教育
    seriousIllness?: number // 大病医疗
    housingLoan?: number // 住房贷款利息
    housingRent?: number // 住房租金
    elderlySupport?: number // 赡养老人
  }): number {
    let total = 0

    // 子女教育：每个子女每月1000元
    if (config.childEducation) {
      total += config.childEducation * 1000
    }

    // 继续教育：学历(学位)继续教育每月400元
    if (config.continuingEducation) {
      total += 400
    }

    // 大病医疗：年度内超过15000元的部分，不超过80000元
    if (config.seriousIllness) {
      total += Math.min(config.seriousIllness, 80000) / 12
    }

    // 住房贷款利息：每月1000元
    if (config.housingLoan) {
      total += 1000
    }

    // 住房租金：根据城市不同，800/1000/1500元
    if (config.housingRent) {
      total += config.housingRent
    }

    // 赡养老人：独生子女每月2000元，非独生子女分摊
    if (config.elderlySupport) {
      total += config.elderlySupport
    }

    return total
  }
}

// 社保公积金计算类
export class SocialInsuranceCalculator {
  // 默认缴纳比例
  private static readonly DEFAULT_RATES = {
    // 养老保险
    pension: { employer: 0.16, employee: 0.08 },
    // 医疗保险
    medical: { employer: 0.08, employee: 0.02 },
    // 失业保险
    unemployment: { employer: 0.005, employee: 0.005 },
    // 工伤保险（只有单位缴纳）
    injury: { employer: 0.002, employee: 0 },
    // 生育保险（只有单位缴纳）
    maternity: { employer: 0.01, employee: 0 },
    // 公积金
    housing: { employer: 0.12, employee: 0.12 }
  }

  /**
   * 计算社保公积金
   */

  public static calculate(base: number, config?: unknown) {
    const rates = config?.rates || this.DEFAULT_RATES
    const result = {
      employer: {} as unknown,
      employee: {} as unknown,
      total: { employer: 0, employee: 0 }
    }

    // 计算各项缴纳金额
    Object.entries(rates).forEach(
      ([key, rate]: [string, { employer: number; employee: number }]) => {
        result.employer[key] = base * rate.employer
        result.employee[key] = base * rate.employee
        result.total.employer += result.employer[key]
        result.total.employee += result.employee[key]
      }
    )

    return result
  }

  /**
   * 获取缴纳基数范围
   */
  public static getBaseRange(averageSalary: number) {
    return {
      min: averageSalary * 0.6, // 最低缴纳基数：社平工资的60%
      max: averageSalary * 3 // 最高缴纳基数：社平工资的300%
    }
  }
}
