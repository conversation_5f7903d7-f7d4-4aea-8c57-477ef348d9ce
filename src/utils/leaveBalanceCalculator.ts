/**
 * 假期余额计算引擎
 * 处理各类假期的余额计算、自动累计、清零规则等
 */

import dayjs, { Dayjs } from 'dayjs'

// 假期类型枚举
export enum LeaveType {
  ANNUAL = 'annual',           // 年假
  SICK = 'sick',              // 病假
  PERSONAL = 'personal',       // 事假
  MARRIAGE = 'marriage',       // 婚假
  MATERNITY = 'maternity',     // 产假
  PATERNITY = 'paternity',     // 陪产假
  BEREAVEMENT = 'bereavement', // 丧假
  COMPENSATORY = 'compensatory', // 调休
  STUDY = 'study',            // 学习假
  OTHER = 'other'             // 其他
}

// 假期规则接口
export interface LeaveRule {
  type: LeaveType
  name: string
  baseQuota: number           // 基础额度（天）
  unit: 'day' | 'hour'       // 计算单位
  validityPeriod: {
    type: 'year' | 'month' | 'custom'
    months?: number           // 有效期（月）
  }
  carryOverRule: {
    allowed: boolean          // 是否允许结转
    maxDays?: number         // 最大结转天数
    deadline?: string        // 结转截止日期（MM-DD）
  }
  accumulationRule: {
    type: 'fixed' | 'seniority' | 'none'  // 固定/工龄/无累计
    seniorityRules?: Array<{
      minYears: number
      maxYears: number
      days: number
    }>
  }
  clearingRule: {
    enabled: boolean         // 是否启用清零
    date?: string           // 清零日期（MM-DD）
    type: 'year_end' | 'hire_anniversary' | 'custom'
  }
  restrictions?: {
    minDays?: number        // 最少请假天数
    maxDays?: number        // 单次最多请假天数
    advanceDays?: number    // 提前申请天数
    blackoutDates?: string[] // 限制日期
  }
}

// 员工信息接口
export interface EmployeeInfo {
  id: string
  name: string
  hireDate: string         // 入职日期
  department: string
  position: string
  employeeType: string     // 员工类型：正式/试用/实习等
}

// 假期余额接口
export interface LeaveBalance {
  employeeId: string
  type: LeaveType
  year: number
  totalQuota: number       // 总额度
  usedQuota: number       // 已使用
  remainingQuota: number  // 剩余额度
  expiredQuota: number    // 已过期
  carryOverQuota: number  // 结转额度
  details: {
    annual: number        // 年度额度
    carryOver: number     // 结转额度
    adjustment: number    // 调整额度
    compensatory: number  // 补偿额度
  }
  history: LeaveTransaction[]
}

// 假期交易记录
export interface LeaveTransaction {
  id: string
  date: string
  type: 'grant' | 'use' | 'expire' | 'adjust' | 'carryover'
  amount: number
  balance: number         // 交易后余额
  reason: string
  relatedId?: string      // 关联的请假单ID
}

// 假期使用记录
export interface LeaveUsage {
  id: string
  employeeId: string
  type: LeaveType
  startDate: string
  endDate: string
  days: number
  hours?: number
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  approvedDate?: string
  remark?: string
}

/**
 * 假期余额计算器类
 */
export class LeaveBalanceCalculator {
  private rules: Map<LeaveType, LeaveRule> = new Map()
  private holidays: Set<string> = new Set()

  /**
   * 设置假期规则
   */
  setRules(rules: LeaveRule[]) {
    this.rules.clear()
    rules.forEach(rule => {
      this.rules.set(rule.type, rule)
    })
  }

  /**
   * 设置节假日
   */
  setHolidays(holidays: string[]) {
    this.holidays = new Set(holidays)
  }

  /**
   * 计算员工年度假期额度
   */
  calculateAnnualQuota(
    employee: EmployeeInfo,
    type: LeaveType,
    year: number
  ): number {
    const rule = this.rules.get(type)
    if (!rule) return 0

    let quota = rule.baseQuota

    // 根据累计规则计算
    if (rule.accumulationRule.type === 'seniority') {
      const seniority = this.calculateSeniority(employee.hireDate, `${year}-12-31`)
      const seniorityRule = rule.accumulationRule.seniorityRules?.find(
        r => seniority >= r.minYears && seniority <= r.maxYears
      )
      if (seniorityRule) {
        quota = seniorityRule.days
      }
    }

    // 新员工按比例计算
    const hireYear = dayjs(employee.hireDate).year()
    if (hireYear === year) {
      const hireMonth = dayjs(employee.hireDate).month() + 1
      const remainingMonths = 12 - hireMonth + 1
      quota = Math.floor(quota * remainingMonths / 12)
    }

    // 试用期员工特殊处理
    if (employee.employeeType === 'probation') {
      quota = Math.floor(quota * 0.5) // 试用期享受50%
    }

    return quota
  }

  /**
   * 计算工龄（年）
   */
  private calculateSeniority(hireDate: string, endDate: string): number {
    const hire = dayjs(hireDate)
    const end = dayjs(endDate)
    return end.diff(hire, 'year')
  }

  /**
   * 计算假期余额
   */
  calculateBalance(
    employee: EmployeeInfo,
    type: LeaveType,
    year: number,
    usageRecords: LeaveUsage[],
    previousBalance?: LeaveBalance
  ): LeaveBalance {
    const rule = this.rules.get(type)
    if (!rule) {
      throw new Error(`未找到假期类型 ${type} 的规则`)
    }

    // 初始化余额
    const balance: LeaveBalance = {
      employeeId: employee.id,
      type,
      year,
      totalQuota: 0,
      usedQuota: 0,
      remainingQuota: 0,
      expiredQuota: 0,
      carryOverQuota: 0,
      details: {
        annual: 0,
        carryOver: 0,
        adjustment: 0,
        compensatory: 0
      },
      history: []
    }

    // 1. 计算年度额度
    balance.details.annual = this.calculateAnnualQuota(employee, type, year)
    balance.totalQuota += balance.details.annual

    // 2. 添加年度额度发放记录
    balance.history.push({
      id: `grant-${year}`,
      date: `${year}-01-01`,
      type: 'grant',
      amount: balance.details.annual,
      balance: balance.totalQuota,
      reason: `${year}年度${rule.name}额度`
    })

    // 3. 处理结转
    if (previousBalance && rule.carryOverRule.allowed) {
      const carryOverAmount = this.calculateCarryOver(
        previousBalance.remainingQuota,
        rule.carryOverRule,
        year
      )
      if (carryOverAmount > 0) {
        balance.details.carryOver = carryOverAmount
        balance.carryOverQuota = carryOverAmount
        balance.totalQuota += carryOverAmount

        balance.history.push({
          id: `carryover-${year}`,
          date: `${year}-01-01`,
          type: 'carryover',
          amount: carryOverAmount,
          balance: balance.totalQuota,
          reason: `${year - 1}年度结转`
        })
      }
    }

    // 4. 计算已使用额度
    const approvedUsages = usageRecords.filter(
      u => u.status === 'approved' && 
          u.type === type &&
          dayjs(u.startDate).year() === year
    )

    approvedUsages.forEach(usage => {
      const usedDays = this.calculateUsedDays(usage)
      balance.usedQuota += usedDays

      balance.history.push({
        id: `use-${usage.id}`,
        date: usage.approvedDate || usage.startDate,
        type: 'use',
        amount: -usedDays,
        balance: balance.totalQuota - balance.usedQuota,
        reason: `请假：${usage.startDate} 至 ${usage.endDate}`,
        relatedId: usage.id
      })
    })

    // 5. 计算过期额度
    if (rule.clearingRule.enabled) {
      const expiredAmount = this.calculateExpired(balance, rule, year)
      if (expiredAmount > 0) {
        balance.expiredQuota = expiredAmount
        balance.totalQuota -= expiredAmount

        const clearDate = this.getClearingDate(rule.clearingRule, year, employee.hireDate)
        balance.history.push({
          id: `expire-${year}`,
          date: clearDate,
          type: 'expire',
          amount: -expiredAmount,
          balance: balance.totalQuota - balance.usedQuota,
          reason: '假期过期清零'
        })
      }
    }

    // 6. 计算剩余额度
    balance.remainingQuota = Math.max(0, balance.totalQuota - balance.usedQuota)

    // 7. 排序历史记录
    balance.history.sort((a, b) => 
      dayjs(a.date).valueOf() - dayjs(b.date).valueOf()
    )

    return balance
  }

  /**
   * 计算结转额度
   */
  private calculateCarryOver(
    remainingQuota: number,
   
    carryOverRule: unknown,
    year: number
  ): number {
    if (!carryOverRule.allowed) return 0

    let carryOverAmount = remainingQuota

    // 检查最大结转天数限制
    if (carryOverRule.maxDays !== undefined) {
      carryOverAmount = Math.min(carryOverAmount, carryOverRule.maxDays)
    }

    // 检查结转截止日期
    if (carryOverRule.deadline) {
      const deadline = dayjs(`${year}-${carryOverRule.deadline}`)
      if (dayjs().isAfter(deadline)) {
        return 0 // 已过结转截止日期
      }
    }

    return carryOverAmount
  }

  /**
   * 计算已使用天数
   */
  private calculateUsedDays(usage: LeaveUsage): number {
    const start = dayjs(usage.startDate)
    const end = dayjs(usage.endDate)
    
    // 计算日期差
    let days = 0
    let current = start

    while (current.isSameOrBefore(end, 'day')) {
      // 排除周末和节假日
      const isWeekend = current.day() === 0 || current.day() === 6
      const isHoliday = this.holidays.has(current.format('YYYY-MM-DD'))
      
      if (!isWeekend && !isHoliday) {
        days++
      }
      
      current = current.add(1, 'day')
    }

    return usage.days || days
  }

  /**
   * 计算过期额度
   */
  private calculateExpired(
    balance: LeaveBalance,
    rule: LeaveRule,
    year: number
  ): number {
    if (!rule.clearingRule.enabled) return 0

    // 根据不同的有效期规则计算
    if (rule.validityPeriod.type === 'year') {
      // 年度有效期，年底清零
      const now = dayjs()
      if (now.year() > year) {
        return balance.remainingQuota
      }
    } else if (rule.validityPeriod.type === 'month' && rule.validityPeriod.months) {
      // 月度有效期
      // 这里需要更复杂的逻辑来跟踪每笔额度的有效期
      // 简化处理：暂不实现
    }

    return 0
  }

  /**
   * 获取清零日期
   */
  private getClearingDate(
   
    clearingRule: unknown,
    year: number,
    hireDate: string
  ): string {
    if (clearingRule.type === 'year_end') {
      return `${year}-12-31`
    } else if (clearingRule.type === 'hire_anniversary') {
      const hire = dayjs(hireDate)
      return dayjs(`${year}-${hire.format('MM-DD')}`).format('YYYY-MM-DD')
    } else if (clearingRule.date) {
      return `${year}-${clearingRule.date}`
    }
    return `${year}-12-31`
  }

  /**
   * 批量计算多个员工的假期余额
   */
  batchCalculateBalance(
    employees: EmployeeInfo[],
    type: LeaveType,
    year: number,
    usageRecords: Map<string, LeaveUsage[]>,
    previousBalances: Map<string, LeaveBalance>
  ): Map<string, LeaveBalance> {
    const results = new Map<string, LeaveBalance>()

    employees.forEach(employee => {
      const empUsages = usageRecords.get(employee.id) || []
      const prevBalance = previousBalances.get(employee.id)
      
      const balance = this.calculateBalance(
        employee,
        type,
        year,
        empUsages,
        prevBalance
      )
      
      results.set(employee.id, balance)
    })

    return results
  }

  /**
   * 检查假期申请是否符合规则
   */
  validateLeaveRequest(
    employee: EmployeeInfo,
    type: LeaveType,
    startDate: string,
    endDate: string,
    currentBalance: LeaveBalance
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    const rule = this.rules.get(type)
    
    if (!rule) {
      errors.push('未找到对应的假期规则')
      return { valid: false, errors }
    }

    // 计算请假天数
    const leaveDays = this.calculateUsedDays({
      id: '',
      employeeId: employee.id,
      type,
      startDate,
      endDate,
      days: 0,
      status: 'pending'
    })

    // 检查余额是否充足
    if (leaveDays > currentBalance.remainingQuota) {
      errors.push(`假期余额不足，剩余${currentBalance.remainingQuota}天`)
    }

    // 检查限制条件
    if (rule.restrictions) {
      // 最少请假天数
      if (rule.restrictions.minDays && leaveDays < rule.restrictions.minDays) {
        errors.push(`最少需要请假${rule.restrictions.minDays}天`)
      }

      // 单次最多请假天数
      if (rule.restrictions.maxDays && leaveDays > rule.restrictions.maxDays) {
        errors.push(`单次最多可请假${rule.restrictions.maxDays}天`)
      }

      // 提前申请天数
      if (rule.restrictions.advanceDays) {
        const daysBefore = dayjs(startDate).diff(dayjs(), 'day')
        if (daysBefore < rule.restrictions.advanceDays) {
          errors.push(`需要提前${rule.restrictions.advanceDays}天申请`)
        }
      }

      // 限制日期
      if (rule.restrictions.blackoutDates) {
        const blackoutSet = new Set(rule.restrictions.blackoutDates)
        let current = dayjs(startDate)
        const end = dayjs(endDate)
        
        while (current.isSameOrBefore(end, 'day')) {
          if (blackoutSet.has(current.format('YYYY-MM-DD'))) {
            errors.push(`${current.format('YYYY-MM-DD')}为限制请假日期`)
            break
          }
          current = current.add(1, 'day')
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 生成假期余额报表
   */
  generateBalanceReport(
    balances: LeaveBalance[],
    employees: Map<string, EmployeeInfo>
   
  ): unknown[] {
    return balances.map(balance => {
      const employee = employees.get(balance.employeeId)
      return {
        员工姓名: employee?.name || '',
        部门: employee?.department || '',
        假期类型: this.getLeaveTypeName(balance.type),
        年度: balance.year,
        总额度: balance.totalQuota,
        已使用: balance.usedQuota,
        剩余: balance.remainingQuota,
        已过期: balance.expiredQuota,
        结转额度: balance.carryOverQuota,
        使用率: balance.totalQuota > 0 
          ? `${(balance.usedQuota / balance.totalQuota * 100).toFixed(1)}%`
          : '0%'
      }
    })
  }

  /**
   * 获取假期类型名称
   */
  private getLeaveTypeName(type: LeaveType): string {
    const rule = this.rules.get(type)
    return rule?.name || type
  }
}

// 创建默认实例
export const leaveBalanceCalculator = new LeaveBalanceCalculator()

// 默认假期规则配置
export const defaultLeaveRules: LeaveRule[] = [
  {
    type: LeaveType.ANNUAL,
    name: '年假',
    baseQuota: 5,
    unit: 'day',
    validityPeriod: { type: 'year'
  },
    carryOverRule: {
      allowed: true,
      maxDays: 5,
      deadline: '03-31'
  },
    accumulationRule: {
      type: 'seniority',
      seniorityRules: [
        { minYears: 0, maxYears: 1, days: 5 },
        { minYears: 1, maxYears: 10, days: 10 },
        { minYears: 10, maxYears: 20, days: 15 },
        { minYears: 20, maxYears: 99, days: 20 }
      ]
    },
    clearingRule: {
      enabled: true,
      type: 'year_end'
  },
    restrictions: {
      minDays: 0.5,
      advanceDays: 3
    }
  },
  {
    type: LeaveType.SICK,
    name: '病假',
    baseQuota: 15,
    unit: 'day',
    validityPeriod: { type: 'year'
  },
    carryOverRule: { allowed: false },
    accumulationRule: { type: 'fixed'
  },
    clearingRule: {
      enabled: true,
      type: 'year_end'
    }
  },
  {
    type: LeaveType.PERSONAL,
    name: '事假',
    baseQuota: 7,
    unit: 'day',
    validityPeriod: { type: 'year'
  },
    carryOverRule: { allowed: false },
    accumulationRule: { type: 'fixed'
  },
    clearingRule: {
      enabled: true,
      type: 'year_end'
  },
    restrictions: {
      advanceDays: 1
    }
  },
  {
    type: LeaveType.MARRIAGE,
    name: '婚假',
    baseQuota: 3,
    unit: 'day',
    validityPeriod: { type: 'custom', months: 12 },
    carryOverRule: { allowed: false },
    accumulationRule: { type: 'none'
  },
    clearingRule: { enabled: false }
  },
  {
    type: LeaveType.MATERNITY,
    name: '产假',
    baseQuota: 98,
    unit: 'day',
    validityPeriod: { type: 'custom'
  },
    carryOverRule: { allowed: false },
    accumulationRule: { type: 'none'
  },
    clearingRule: { enabled: false }
  },
  {
    type: LeaveType.COMPENSATORY,
    name: '调休',
    baseQuota: 0,
    unit: 'hour',
    validityPeriod: { type: 'month', months: 3 },
    carryOverRule: { allowed: false },
    accumulationRule: { type: 'none'
  },
    clearingRule: {
      enabled: true,
      type: 'custom'
    }
  }
]