/**
 * @name 带缓存的请求工具
 * @description 集成API缓存功能的增强版请求工具
 */
import { AxiosRequestConfig, AxiosResponse } from 'axios'
import { request } from './request'
import { ApiCache, CacheStrategy, CachePresets, globalApiCache } from './apiCache'
import { ElMessage } from 'element-plus'

// ===== 接口定义 =====
interface CachedRequestConfig extends AxiosRequestConfig {
  /** 缓存策略 */
  cacheStrategy?: CacheStrategy
  /** 缓存TTL（毫秒） */
  cacheTTL?: number
  /** 是否禁用缓存 */
  disableCache?: boolean
  /** 缓存标签，用于批量失效 */
  cacheTag?: string
  /** 是否在缓存失效时显示加载状态 */
  showLoadingOnCacheMiss?: boolean
}

interface RequestCacheManager {
  /** 缓存实例 */
  cache: ApiCache
  /** 按标签管理的缓存键 */
  taggedKeys: Map<string, Set<string>>
  /** 正在进行的请求（防止重复请求） */
  pendingRequests: Map<string, Promise<unknown>>
}

// ===== 缓存管理器 =====
class CacheManager implements RequestCacheManager {
  cache: ApiCache
  taggedKeys = new Map<string, Set<string>>()
  pendingRequests = new Map<string, Promise<unknown>>()

  constructor(cache?: ApiCache) {
    this.cache = cache || globalApiCache
  }

  /**
   * 生成请求唯一标识
   */
  private getRequestKey(config: CachedRequestConfig): string {
    const { url = '', method = 'get', params, data } = config
    const baseKey = `${method.toUpperCase()}:${url}`

    if (method.toLowerCase() === 'get' && params) {
      return this.cache['generateKey'](baseKey, params)
    }

    if (['post', 'put', 'patch'].includes(method.toLowerCase()) && data) {
      return this.cache['generateKey'](baseKey, data)
    }

    return baseKey
  }

  /**
   * 添加标签关联
   */
  private addTaggedKey(tag: string, key: string): void {
    if (!this.taggedKeys.has(tag)) {
      this.taggedKeys.set(tag, new Set())
    }
    this.taggedKeys.get(tag)!.add(key)
  }

  /**
   * 根据标签失效缓存
   */
  invalidateByTag(tag: string): number {
    const keys = this.taggedKeys.get(tag)
    if (!keys) return 0

    let count = 0
    keys.forEach(key => {
      if (this.cache['cache'].has(key)) {
        this.cache['cache'].delete(key)
        count++
      }
    })

    this.taggedKeys.delete(tag)
    return count
  }

  /**
   * 批量失效缓存
   */
  invalidateByTags(tags: string[]): number {
    return tags.reduce((total, tag) => total + this.invalidateByTag(tag), 0)
  }

  /**
   * 获取缓存统计按标签分组
   */
  getStatsByTag(): Record<string, number> {
    const stats: Record<string, number> = {}

    for (const [tag, keys] of this.taggedKeys) {
      stats[tag] = keys.size
    }

    return stats
  }
}

// ===== 全局缓存管理器 =====
const cacheManager = new CacheManager()

// ===== 缓存请求函数 =====
/**
 * 执行带缓存的HTTP请求
 */
export async function cachedRequest<T = unknown>(
  config: CachedRequestConfig
): Promise<AxiosResponse<T>> {
  const {
    cacheStrategy = CacheStrategy.CACHE_FIRST,
    cacheTTL,
    disableCache = false,
    cacheTag,
    showLoadingOnCacheMiss = true,
    ...axiosConfig
  } = config

  // 如果禁用缓存或非GET请求，直接发送请求
  if (disableCache || !['get', 'GET'].includes(axiosConfig.method || 'get')) {
    const response = await request<T>(axiosConfig)

    // 对于修改操作，需要失效相关缓存
    if (['post', 'put', 'patch', 'delete'].includes((axiosConfig.method || '').toLowerCase())) {
      if (cacheTag) {
        cacheManager.invalidateByTag(cacheTag)
      }
      // 失效相关的查询缓存
      invalidateRelatedCache(axiosConfig.url || '')
    }

    return response
  }

  const requestKey = cacheManager.getRequestKey(config)

  try {
    switch (cacheStrategy) {
      case CacheStrategy.CACHE_FIRST:
        return await handleCacheFirst<T>(
          requestKey,
          config,
          cacheTag,
          cacheTTL,
          showLoadingOnCacheMiss
        )

      case CacheStrategy.NETWORK_FIRST:
        return await handleNetworkFirst<T>(requestKey, config, cacheTag, cacheTTL)

      case CacheStrategy.CACHE_ONLY:
        return await handleCacheOnly<T>(requestKey, config)

      case CacheStrategy.NETWORK_ONLY:
        return await handleNetworkOnly<T>(config, cacheTag)

      case CacheStrategy.STALE_WHILE_REVALIDATE:
        return await handleStaleWhileRevalidate<T>(requestKey, config, cacheTag, cacheTTL)

      default:
        return await handleCacheFirst<T>(
          requestKey,
          config,
          cacheTag,
          cacheTTL,
          showLoadingOnCacheMiss
        )
    }
  } catch (error) {
    console.error('缓存请求失败:', error)
    throw error
  }
}

// ===== 缓存策略实现 =====
/**
 * 缓存优先策略
 */
async function handleCacheFirst<T>(
  requestKey: string,
  config: CachedRequestConfig,
  cacheTag?: string,
  cacheTTL?: number,
  showLoading = true
): Promise<AxiosResponse<T>> {
  // 检查缓存
  const cachedData = cacheManager.cache.get<AxiosResponse<T>>(requestKey)
  if (cachedData) {
    return cachedData
  }

  // 检查是否有相同的请求正在进行
  if (cacheManager.pendingRequests.has(requestKey)) {
    return await cacheManager.pendingRequests.get(requestKey)!
  }

  // 发送网络请求
  const requestPromise = request<T>(config)
  cacheManager.pendingRequests.set(requestKey, requestPromise)

  try {
    const response = await requestPromise

    // 缓存响应
    cacheManager.cache.set(requestKey, response, { ttl: cacheTTL })

    // 添加标签关联
    if (cacheTag) {
      cacheManager.addTaggedKey(cacheTag, requestKey)
    }

    return response
  } finally {
    cacheManager.pendingRequests.delete(requestKey)
  }
}

/**
 * 网络优先策略
 */
async function handleNetworkFirst<T>(
  requestKey: string,
  config: CachedRequestConfig,
  cacheTag?: string,
  cacheTTL?: number
): Promise<AxiosResponse<T>> {
  try {
    // 检查是否有相同的请求正在进行
    if (cacheManager.pendingRequests.has(requestKey)) {
      return await cacheManager.pendingRequests.get(requestKey)!
    }

    // 先尝试网络请求
    const requestPromise = request<T>(config)
    cacheManager.pendingRequests.set(requestKey, requestPromise)

    const response = await requestPromise

    // 更新缓存
    cacheManager.cache.set(requestKey, response, { ttl: cacheTTL })

    if (cacheTag) {
      cacheManager.addTaggedKey(cacheTag, requestKey)
    }

    cacheManager.pendingRequests.delete(requestKey)
    return response
  } catch (error) {
    cacheManager.pendingRequests.delete(requestKey)

    // 网络请求失败，尝试使用缓存
    const cachedData = cacheManager.cache.get<AxiosResponse<T>>(requestKey)
    if (cachedData) {
      ElMessage.warning('网络请求失败，使用缓存数据')
      return cachedData
    }

    throw error
  }
}

/**
 * 仅缓存策略
 */
async function handleCacheOnly<T>(
  requestKey: string,
  config: CachedRequestConfig
): Promise<AxiosResponse<T>> {
  const cachedData = cacheManager.cache.get<AxiosResponse<T>>(requestKey)

  if (!cachedData) {
    throw new Error(`缓存中没有找到数据: ${config.url}`)
  }

  return cachedData
}

/**
 * 仅网络策略
 */
async function handleNetworkOnly<T>(
  config: CachedRequestConfig,
  cacheTag?: string
): Promise<AxiosResponse<T>> {
  const response = await request<T>(config)

  // 对于修改操作，失效相关缓存
  if (['post', 'put', 'patch', 'delete'].includes((config.method || '').toLowerCase())) {
    if (cacheTag) {
      cacheManager.invalidateByTag(cacheTag)
    }
    invalidateRelatedCache(config.url || '')
  }

  return response
}

/**
 * 失效时重新验证策略
 */
async function handleStaleWhileRevalidate<T>(
  requestKey: string,
  config: CachedRequestConfig,
  cacheTag?: string,
  cacheTTL?: number
): Promise<AxiosResponse<T>> {
  const cachedData = cacheManager.cache.get<AxiosResponse<T>>(requestKey)

  // 在后台发送请求更新缓存
  if (!cacheManager.pendingRequests.has(requestKey)) {
    const updatePromise = request<T>(config)
      .then(response => {
        cacheManager.cache.set(requestKey, response, { ttl: cacheTTL })
        if (cacheTag) {
          cacheManager.addTaggedKey(cacheTag, requestKey)
        }
        return response
      })
      .catch(error => {
        console.warn('后台缓存更新失败:', error)
      })

    cacheManager.pendingRequests.set(requestKey, updatePromise)

    // 清理Promise引用
    updatePromise.finally(() => {
      cacheManager.pendingRequests.delete(requestKey)
    })
  }

  // 如果有缓存，立即返回
  if (cachedData) {
    return cachedData
  }

  // 如果没有缓存，等待网络请求
  return await cacheManager.pendingRequests.get(requestKey)!
}

// ===== 辅助函数 =====
/**
 * 失效相关缓存
 */
function invalidateRelatedCache(url: string): void {
  // 根据URL模式失效相关缓存
  const patterns = [
    url.replace(/\/\d+$/, ''), // 移除ID后缀
    url.split('/').slice(0, -1).join('/'), // 移除最后一级路径
    url.split('?')[0] // 移除查询参数
  ]

  patterns.forEach(pattern => {
    if (pattern && pattern !== url) {
      cacheManager.cache.deleteByPattern(pattern)
    }
  })
}

// ===== 便捷方法 =====
export const cachedGet = <T = unknown>(
  url: string,
  config?: Omit<CachedRequestConfig, 'url' | 'method'>
) => cachedRequest<T>({ ...config, url, method: 'get' })

export const cachedPost = <T = unknown>(
  url: string,
  data?: unknown,
  config?: Omit<CachedRequestConfig, 'url' | 'method' | 'data'>
) => cachedRequest<T>({ ...config, url, method: 'post', data })

export const cachedPut = <T = unknown>(
  url: string,
  data?: unknown,
  config?: Omit<CachedRequestConfig, 'url' | 'method' | 'data'>
) => cachedRequest<T>({ ...config, url, method: 'put', data })

export const cachedDelete = <T = unknown>(
  url: string,
  config?: Omit<CachedRequestConfig, 'url' | 'method'>
) => cachedRequest<T>({ ...config, url, method: 'delete' })

// ===== 缓存管理方法 =====
export const cacheUtils = {
  /** 获取缓存管理器 */
  getManager: () => cacheManager,

  /** 失效指定标签的缓存 */
  invalidateTag: (tag: string) => cacheManager.invalidateByTag(tag),

  /** 批量失效缓存标签 */
  invalidateTags: (tags: string[]) => cacheManager.invalidateByTags(tags),

  /** 获取缓存统计 */
  getStats: () => cacheManager.cache.getStats(),

  /** 获取按标签分组的统计 */
  getStatsByTag: () => cacheManager.getStatsByTag(),

  /** 清空所有缓存 */
  clear: () => {
    cacheManager.cache.clear()
    cacheManager.taggedKeys.clear()
    cacheManager.pendingRequests.clear()
  },

  /** 预热缓存 */
  warmup: <T = unknown>(
    entries: Array<{
      url: string
      data: T
      cacheTTL?: number
      cacheTag?: string
    }>
  ) => {
    entries.forEach(entry => {
      const key = `GET:${entry.url}`
      cacheManager.cache.set(key, entry.data, { ttl: entry.cacheTTL })
      if (entry.cacheTag) {
        cacheManager.addTaggedKey(entry.cacheTag, key)
      }
    })
  }
}

export default cachedRequest
