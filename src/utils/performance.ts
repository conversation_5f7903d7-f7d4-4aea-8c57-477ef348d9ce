/**
 * 性能监控工具
 * @description 用于监控和上报应用性能指标
 */

interface PerformanceMetrics {
  // 核心 Web 指标
  FCP?: number // First Contentful Paint
  LCP?: number // Largest Contentful Paint
  FID?: number // First Input Delay
  CLS?: number // Cumulative Layout Shift
  TTFB?: number // Time to First Byte
  TTI?: number // Time to Interactive
  TBT?: number // Total Blocking Time

  // 自定义指标
  pageLoadTime?: number
  domReady?: number
  resourceLoadTime?: number
  apiResponseTime?: Record<string, number[]>
  jsErrorCount?: number
  resourceErrorCount?: number

  // 用户行为指标
  pageViewCount?: number
  clickCount?: number
  scrollDepth?: number
  sessionDuration?: number
}

interface PerformanceConfig {
  enableMonitoring: boolean
  enableReporting: boolean
  reportUrl?: string
  sampleRate: number // 采样率 0-1
  reportInterval: number // 上报间隔（毫秒）
  debug: boolean
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {}
  private config: PerformanceConfig
  private observer: PerformanceObserver | null = null
  private reportTimer: number | null = null
  private sessionStartTime: number
  private apiTimings: Map<string, number[]> = new Map()
  private resourceTimings: Map<string, number> = new Map()

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enableMonitoring: true,
      enableReporting: true,
      sampleRate: 1,
      reportInterval: 30000, // 30秒
      debug: false,
      ...config
    }

    this.sessionStartTime = Date.now()

    if (this.config.enableMonitoring && this.shouldSample()) {
      this.init()
    }
  }

  /**
   * 初始化性能监控
   */
  private init(): void {
    // 监听页面加载性能
    this.observeNavigationTiming()

    // 监听核心 Web 指标
    this.observeWebVitals()

    // 监听资源加载
    this.observeResourceTiming()

    // 监听用户交互
    this.observeUserInteractions()

    // 监听错误
    this.observeErrors()

    // 定期上报
    if (this.config.enableReporting && this.config.reportUrl) {
      this.startReporting()
    }

    // 页面卸载时上报
    this.setupUnloadHandler()
  }

  /**
   * 是否应该采样
   */
  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate
  }

  /**
   * 监听导航时间
   */
  private observeNavigationTiming(): void {
    if ('PerformanceNavigationTiming' in window) {
      const navigation = performance.getEntriesByType(
        'navigation'
      )[0] as PerformanceNavigationTiming

      if (navigation) {
        this.metrics.TTFB = navigation.responseStart - navigation.requestStart
        this.metrics.domReady =
          navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
        this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.loadEventStart
      }
    }

    // 兼容旧版 API
    if (window.performance?.timing) {
      const timing = window.performance.timing
      this.metrics.TTFB = timing.responseStart - timing.requestStart
      this.metrics.domReady = timing.domContentLoadedEventEnd - timing.domContentLoadedEventStart
      this.metrics.pageLoadTime = timing.loadEventEnd - timing.loadEventStart
    }
  }

  /**
   * 监听核心 Web 指标
   */
  private observeWebVitals(): void {
    if (!('PerformanceObserver' in window)) return

    try {
      const fcpObserver = new PerformanceObserver(list => {
        const entries = list.getEntries()
        const fcp = entries.find(entry => entry.name === 'first-contentful-paint')
        if (fcp) {
          this.metrics.FCP = fcp.startTime
          this.log('FCP:', fcp.startTime)
        }
      })
      fcpObserver.observe({ entryTypes: ['paint'] })
    } catch (__e) {
      this.log('FCP observer error:', e)
    }

    try {
      const lcpObserver = new PerformanceObserver(list => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as unknown
        this.metrics.LCP = lastEntry.startTime
        this.log('LCP:', lastEntry.startTime)
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (__e) {
      this.log('LCP observer error:', e)
    }

    try {
      const fidObserver = new PerformanceObserver(list => {
        const entries = list.getEntries()
        const firstInput = entries[0] as unknown
        if (firstInput) {
          this.metrics.FID = firstInput.processingStart - firstInput.startTime
          this.log('FID:', this.metrics.FID)
        }
      })
      fidObserver.observe({ entryTypes: ['first-input'] })
    } catch (__e) {
      this.log('FID observer error:', e)
    }

    try {
      let clsScore = 0
      const clsObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          if (!(entry as unknown).hadRecentInput) {
            clsScore += (entry as unknown).value
          }
        }
        this.metrics.CLS = clsScore
        this.log('CLS:', clsScore)
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    } catch (__e) {
      this.log('CLS observer error:', e)
    }

    // Long Tasks (for TBT)
    try {
      let totalBlockingTime = 0
      const longTaskObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            totalBlockingTime += entry.duration - 50
          }
        }
        this.metrics.TBT = totalBlockingTime
      })
      longTaskObserver.observe({ entryTypes: ['longtask'] })
    } catch (__e) {
      this.log('Long task observer error:', e)
    }
  }

  /**
   * 监听资源加载
   */
  private observeResourceTiming(): void {
    if (!('PerformanceObserver' in window)) return

    try {
      const resourceObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          const resource = entry as PerformanceResourceTiming
          this.resourceTimings.set(resource.name, resource.duration)

          // 统计慢资源
          if (resource.duration > 1000) {
            this.log('Slow resource:', resource.name, resource.duration)
          }
        }

        // 计算总资源加载时间
        this.metrics.resourceLoadTime = Array.from(this.resourceTimings.values()).reduce(
          (sum, duration) => sum + duration,
          0
        )
      })
      resourceObserver.observe({ entryTypes: ['resource'] })
    } catch (__e) {
      this.log('Resource observer error:', e)
    }
  }

  /**
   * 监听用户交互
   */
  private observeUserInteractions(): void {
    // 页面浏览次数
    this.metrics.pageViewCount = 1

    // 点击次数
    this.metrics.clickCount = 0
    document.addEventListener(
      'click',
      () => {
        this.metrics.clickCount!++
      },
      { passive: true }
    )

    // 滚动深度
    let maxScrollDepth = 0
    const calculateScrollDepth = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollDepth = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0
      maxScrollDepth = Math.max(maxScrollDepth, scrollDepth)
      this.metrics.scrollDepth = maxScrollDepth
    }

    let scrollTimer: number | null = null
    window.addEventListener(
      'scroll',
      () => {
        if (scrollTimer) clearTimeout(scrollTimer)
        scrollTimer = window.setTimeout(calculateScrollDepth, 100)
      },
      { passive: true }
    )
  }

  /**
   * 监听错误
   */
  private observeErrors(): void {
    this.metrics.jsErrorCount = 0
    this.metrics.resourceErrorCount = 0

    // JavaScript 错误
    window.addEventListener('error', _event => {
      if (event.error) {
        this.metrics.jsErrorCount!++
        this.log('JS Error:', event.error)
      } else {
        // 资源加载错误
        this.metrics.resourceErrorCount!++
        this.log('Resource Error:', event)
      }
    })

    // Promise 错误
    window.addEventListener('unhandledrejection', _event => {
      this.metrics.jsErrorCount!++
      this.log('Promise Rejection:', event.reason)
    })
  }

  /**
   * 开始定期上报
   */
  private startReporting(): void {
    this.reportTimer = window.setInterval(() => {
      this.report()
    }, this.config.reportInterval)
  }

  /**
   * 设置页面卸载处理
   */
  private setupUnloadHandler(): void {
    const reportBeforeUnload = () => {
      this.metrics.sessionDuration = Date.now() - this.sessionStartTime
      this.report(true)
    }

    // 使用多种方式确保上报
    window.addEventListener('beforeunload', reportBeforeUnload)
    window.addEventListener('pagehide', reportBeforeUnload)

    // 使用 sendBeacon 确保数据发送
    if ('sendBeacon' in navigator && this.config.reportUrl) {
      window.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'hidden') {
          reportBeforeUnload()
        }
      })
    }
  }

  /**
   * 标记自定义性能点
   */
  public mark(name: string): void {
    if (performance.mark) {
      performance.mark(name)
      this.log('Performance mark:', name)
    }
  }

  /**
   * 测量两个标记点之间的时间
   */
  public measure(name: string, startMark: string, endMark?: string): void {
    if (performance.measure) {
      try {
        performance.measure(name, startMark, endMark)
        const measures = performance.getEntriesByName(name, 'measure')
        const measure = measures[measures.length - 1]
        if (measure) {
          this.log('Performance measure:', name, measure.duration)
        }
      } catch (__e) {
        this.log('Measure error:', e)
      }
    }
  }

  /**
   * 记录 API 响应时间
   */
  public recordApiTiming(url: string, duration: number): void {
    const apiPath = this.normalizeApiPath(url)
    if (!this.apiTimings.has(apiPath)) {
      this.apiTimings.set(apiPath, [])
    }
    this.apiTimings.get(apiPath)!.push(duration)

    // 更新平均响应时间
    const timings = this.apiTimings.get(apiPath)!
    const avgTime = timings.reduce((sum, t) => sum + t, 0) / timings.length

    if (!this.metrics.apiResponseTime) {
      this.metrics.apiResponseTime = {}
    }
    this.metrics.apiResponseTime[apiPath] = [avgTime, Math.min(...timings), Math.max(...timings)]
  }

  /**
   * 规范化 API 路径
   */
  private normalizeApiPath(url: string): string {
    try {
      const urlObj = new URL(url, window.location.origin)
      // 移除查询参数和具体ID
      return urlObj.pathname.replace(/\/\d+/g, '/:id')
    } catch {
      return url
    }
  }

  /**
   * 获取当前性能指标
   */
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 手动上报性能数据
   */
  public report(immediate = false): void {
    if (!this.config.enableReporting || !this.config.reportUrl) return

    const data = {
      metrics: this.getMetrics(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      sessionId: this.getSessionId(),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      connection: (navigator as unknown).connection
        ? {
            effectiveType: (navigator as unknown).connection.effectiveType,
            downlink: (navigator as unknown).connection.downlink,
            rtt: (navigator as unknown).connection.rtt
          }
        : undefined
    }

    this.log('Reporting metrics:', data)

    // 使用 sendBeacon 优先，确保数据发送
    if (immediate && 'sendBeacon' in navigator) {
      navigator.sendBeacon(this.config.reportUrl, JSON.stringify(data))
    } else {
      // 使用 fetch 发送
      fetch(this.config.reportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data),
        keepalive: true
      }).catch(() => {
        this.log('Report error:', error)
      })
    }
  }

  /**
   * 获取会话ID
   */
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('performance-session-id')
    if (!sessionId) {
      sessionId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('performance-session-id', sessionId)
    }
    return sessionId
  }

  /**
   * 调试日志
   */

  private log(..._args: unknown[]): void {
    if (this.config.debug) {
      }
  }

  /**
   * 销毁监控器
   */
  public destroy(): void {
    if (this.observer) {
      this.observer.disconnect()
    }
    if (this.reportTimer) {
      clearInterval(this.reportTimer)
    }
  }
}

// 创建默认实例
const performanceMonitor = new PerformanceMonitor({
  enableMonitoring: import.meta.env.PROD,
  enableReporting: import.meta.env.PROD,
  reportUrl: import.meta.env.VITE_PERFORMANCE_REPORT_URL || '/api/performance/report',
  sampleRate: 0.1, // 10% 采样率
  debug: import.meta.env.DEV
})

// 导出工具函数
export const markPerformance = (name: string) => performanceMonitor.mark(name)
export const measurePerformance = (name: string, startMark: string, endMark?: string) =>
  performanceMonitor.measure(name, startMark, endMark)
export const recordApiTiming = (url: string, duration: number) =>
  performanceMonitor.recordApiTiming(url, duration)
export const getPerformanceMetrics = () => performanceMonitor.getMetrics()
export const reportPerformance = (immediate = false) => performanceMonitor.report(immediate)

// 导出类和实例
export { PerformanceMonitor, performanceMonitor }
export type { PerformanceMetrics, PerformanceConfig }

// 自动集成到 Axios
if (window.axios) {
  // 请求拦截器

  interface AxiosRequestConfigWithMetadata {
    metadata?: { startTime: number }
    url?: string
    [key: string]: unknown
  }

  window.axios.interceptors.request.use(
    (config: AxiosRequestConfigWithMetadata) => {
      config.metadata = { startTime: Date.now() }
      return config
    }
  )

  // 响应拦截器
  interface AxiosResponseWithMetadata {
    config: AxiosRequestConfigWithMetadata
    [key: string]: unknown
  }

  window.axios.interceptors.response.use(
    (response: AxiosResponseWithMetadata) => {
      if (response.config.metadata) {
        const duration = Date.now() - response.config.metadata.startTime
        recordApiTiming(response.config.url || '', duration)
      }
      return response
    },

    (error: Error) => {
      if (error.config?.metadata) {
        const duration = Date.now() - error.config.metadata.startTime
        recordApiTiming(error.config.url, duration)
      }
      return Promise.reject(error)
    }
  )
}
