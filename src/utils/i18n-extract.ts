/**
 * 国际化文本提取工具
 * 用于从代码中提取需要翻译的文本
 */

import { globSync } from 'glob'
import { readFileSync, writeFileSync } from 'fs'
import { resolve, relative } from 'path'

// 配置
const config = {
  // 要扫描的文件模式
  patterns: [
    'src/**/*.vue',
    'src/**/*.ts',
    'src/**/*.tsx',
    '!src/i18n/**',
    '!src/**/*.spec.ts',
    '!src/**/*.test.ts'
  ],
  // 中文匹配正则
  chineseRegex: /[\u4e00-\u9fa5]+/g,
  // 排除的文本（如注释等）
  excludePatterns: [
    /\/\/.*/g, // 单行注释
    /\/\*[\s\S]*?\*\//g, // 多行注释
    /console\.(log|warn|error|info)/g
  ],
  // 输出文件
  outputFile: 'src/i18n/extracted-texts.json'
}

interface ExtractedText {
  text: string
  file: string
  line: number
  suggestion?: string
}

/**
 * 提取文件中的中文文本
 */
function extractChineseFromFile(filePath: string): ExtractedText[] {
  const content = readFileSync(filePath, 'utf-8')
  const lines = content.split('\n')
  const results: ExtractedText[] = []
  const relativePath = relative(process.cwd(), filePath)

  lines.forEach((line, index) => {
    // 跳过注释行
    if (config.excludePatterns.some(pattern => pattern.test(line))) {
      return
    }

    // 提取中文文本
    const matches = line.match(config.chineseRegex)
    if (matches) {
      matches.forEach(text => {
        // 过滤太短的文本
        if (text.length >= 2) {
          results.push({
            text,
            file: relativePath,
            line: index + 1,
            suggestion: generateI18nKey(text, filePath)
          })
        }
      })
    }
  })

  return results
}

/**
 * 生成建议的 i18n key
 */
function generateI18nKey(text: string, filePath: string): string {
  // 根据文件路径生成模块名
  const pathParts = filePath.split('/')
  let module = 'common'

  if (filePath.includes('/views/')) {
    const viewIndex = pathParts.indexOf('views')
    if (viewIndex >= 0 && pathParts[viewIndex + 1]) {
      module = pathParts[viewIndex + 1]
    }
  } else if (filePath.includes('/components/')) {
    module = 'component'
  }

  // 根据文本内容生成 key
  let key = text
    .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 移除特殊字符
    .slice(0, 20) // 限制长度

  // 转换为驼峰命名
  key = key.toLowerCase().replace(/\s+(.)/g, (match, char) => char.toUpperCase())

  return `${module}.${key}`
}

/**
 * 提取所有文件中的中文文本
 */
export function extractAllChineseTexts(): Map<string, ExtractedText[]> {
  const allTexts = new Map<string, ExtractedText[]>()

  // 获取所有需要扫描的文件
  const files = config.patterns
    .filter(pattern => !pattern.startsWith('!'))
    .flatMap(pattern => globSync(pattern))

  // 获取排除的文件
  const excludeFiles = config.patterns
    .filter(pattern => pattern.startsWith('!'))
    .flatMap(pattern => globSync(pattern.slice(1)))

  // 过滤文件
  const filesToScan = files.filter(file => !excludeFiles.includes(file))

  // 提取文本
  filesToScan.forEach(file => {
    const texts = extractChineseFromFile(file)
    if (texts.length > 0) {
      // 按文本分组
      texts.forEach(item => {
        if (!allTexts.has(item.text)) {
          allTexts.set(item.text, [])
        }
        allTexts.get(item.text)!.push(item)
      })
    }
  })

  return allTexts
}

/**
 * 生成提取报告
 */
export function generateExtractionReport() {
  const texts = extractAllChineseTexts()
  const report = {
    summary: {
      totalTexts: texts.size,
      totalOccurrences: Array.from(texts.values()).reduce((sum, arr) => sum + arr.length, 0),
      filesScanned: new Set(
        Array.from(texts.values())
          .flat()
          .map(item => item.file)
      ).size
    },
    texts: Array.from(texts.entries())
      .map(([text, occurrences]) => ({
        text,
        count: occurrences.length,
        suggestion: occurrences[0].suggestion,
        locations: occurrences.map(item => ({
          file: item.file,
          line: item.line
        }))
      }))
      .sort((a, b) => b.count - a.count)
  }

  // 写入文件
  writeFileSync(resolve(process.cwd(), config.outputFile), JSON.stringify(report, null, 2), 'utf-8')

  return report
}

/**
 * 检查已翻译的文本
 */
export function checkTranslatedTexts(localeData: Record<string, unknown>) {
  const extractedTexts = extractAllChineseTexts()
  const untranslated: string[] = []

  // 扁平化语言包
  const flattenedLocale = flattenObject(localeData)
  const translatedTexts = new Set(Object.values(flattenedLocale))

  // 检查未翻译的文本
  extractedTexts.forEach((occurrences, text) => {
    if (!translatedTexts.has(text)) {
      untranslated.push(text)
    }
  })

  return {
    total: extractedTexts.size,
    translated: extractedTexts.size - untranslated.length,
    untranslated: untranslated.length,
    coverage:
      (((extractedTexts.size - untranslated.length) / extractedTexts.size) * 100).toFixed(2) + '%',
    untranslatedTexts: untranslated
  }
}

/**
 * 扁平化对象
 */

function flattenObject(obj: unknown, prefix = ''): Record<string, string> {
  const result: Record<string, string> = {}

  for (const key in obj) {
    const value = obj[key]
    const newKey = prefix ? `${prefix}.${key}` : key

    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      Object.assign(result, flattenObject(value, newKey))
    } else if (typeof value === 'string') {
      result[newKey] = value
    }
  }

  return result
}

// 如果直接运行此文件
if (process.argv[1] === import.meta.url) {
  generateExtractionReport()
}
