/**
 * 考勤统计计算引擎
 * 处理复杂的考勤规则计算、统计分析和异常判定
 */

import dayjs, { Dayjs } from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import duration from 'dayjs/plugin/duration'

dayjs.extend(isBetween)
dayjs.extend(duration)

// 考勤状态枚举
export enum AttendanceStatus {
  NORMAL = 'normal',
  LATE = 'late',
  EARLY = 'early',
  ABSENT = 'absent',
  LEAVE = 'leave',
  OVERTIME = 'overtime',
  HOLIDAY = 'holiday',
  WEEKEND = 'weekend',
  ABNORMAL = 'abnormal'
}

// 工作制度类型
export enum WorkType {
  STANDARD = 'standard', // 标准工时制
  COMPREHENSIVE = 'comprehensive', // 综合工时制
  FLEXIBLE = 'flexible', // 不定时工时制
  ELASTIC = 'elastic' // 弹性工作制
}

// 考勤规则接口
export interface AttendanceRule {
  id: string
  name: string
  workType: WorkType
  workDays: number[] // 工作日（0-6，0为周日）
  workTime: {
    start: string // 上班时间 HH:mm
    end: string // 下班时间 HH:mm
    breakStart?: string // 午休开始
    breakEnd?: string // 午休结束
    flexibleMinutes?: number // 弹性时间（分钟）
  }
  lateThreshold: number // 迟到阈值（分钟）
  earlyThreshold: number // 早退阈值（分钟）
  absentRules: {
    lateMinutes?: number // 迟到多少分钟算旷工
    noRecord: boolean // 无打卡记录算旷工
  }
  overtimeThreshold: number // 加班阈值（分钟）
  priority: number // 优先级
}

// 打卡记录接口
export interface ClockRecord {
  date: string
  clockIn?: string // 上班打卡时间
  clockOut?: string // 下班打卡时间
  location?: string
  device?: string
  abnormalMark?: string // 异常标记
}

// 假期信息接口
export interface LeaveInfo {
  startDate: string
  endDate: string
  type: string // 事假、病假、年假等
  duration: number // 时长（小时）
  status: string // 审批状态
}

// 节假日信息接口
export interface Holiday {
  date: string
  name: string
  type: 'holiday' | 'workday' // 节假日或调休工作日
}

// 考勤统计结果接口
export interface AttendanceStats {
  date: string
  status: AttendanceStatus
  workHours: number // 工作时长（分钟）
  lateMinutes: number // 迟到分钟数
  earlyMinutes: number // 早退分钟数
  overtimeHours: number // 加班小时数
  details: {
    scheduledStart: string
    scheduledEnd: string
    actualStart?: string
    actualEnd?: string
    isHoliday: boolean
    isWeekend: boolean
    hasLeave: boolean
    leaveType?: string
  }
}

// 月度统计结果接口
export interface MonthlyStats {
  totalDays: number // 应出勤天数
  actualDays: number // 实际出勤天数
  lateDays: number // 迟到天数
  earlyDays: number // 早退天数
  absentDays: number // 旷工天数
  leaveDays: number // 请假天数
  overtimeDays: number // 加班天数
  totalWorkHours: number // 总工时（小时）
  totalOvertimeHours: number // 总加班时长（小时）
  attendanceRate: number // 出勤率
}

/**
 * 考勤计算器类
 */
export class AttendanceCalculator {
  private rules: Map<string, AttendanceRule> = new Map()
  private holidays: Map<string, Holiday> = new Map()

  /**
   * 设置考勤规则
   */
  setRules(rules: AttendanceRule[]) {
    this.rules.clear()
    rules.forEach(rule => {
      this.rules.set(rule.id, rule)
    })
  }

  /**
   * 设置节假日信息
   */
  setHolidays(holidays: Holiday[]) {
    this.holidays.clear()
    holidays.forEach(holiday => {
      this.holidays.set(holiday.date, holiday)
    })
  }

  /**
   * 计算单日考勤状态
   */
  calculateDailyAttendance(
    date: string,
    record: ClockRecord,
    rule: AttendanceRule,
    leaveInfo?: LeaveInfo
  ): AttendanceStats {
    const dateObj = dayjs(date)
    const dayOfWeek = dateObj.day()

    // 检查是否为节假日
    const holiday = this.holidays.get(date)
    const isHoliday = holiday?.type === 'holiday'
    const isWorkday = holiday?.type === 'workday'

    // 检查是否为周末
    const isWeekend = !isWorkday && (dayOfWeek === 0 || dayOfWeek === 6)

    // 检查是否有请假
    const hasLeave = !!leaveInfo && leaveInfo.status === 'approved'

    // 初始化结果
    const result: AttendanceStats = {
      date,
      status: AttendanceStatus.NORMAL,
      workHours: 0,
      lateMinutes: 0,
      earlyMinutes: 0,
      overtimeHours: 0,
      details: {
        scheduledStart: rule.workTime.start,
        scheduledEnd: rule.workTime.end,
        actualStart: record.clockIn,
        actualEnd: record.clockOut,
        isHoliday,
        isWeekend,
        hasLeave,
        leaveType: leaveInfo?.type
      }
    }

    // 特殊日期处理
    if (isHoliday) {
      result.status = AttendanceStatus.HOLIDAY
      return result
    }

    if (isWeekend && !rule.workDays.includes(dayOfWeek)) {
      result.status = AttendanceStatus.WEEKEND
      // 周末加班处理
      if (record.clockIn && record.clockOut) {
        result.status = AttendanceStatus.OVERTIME
        result.workHours = this.calculateWorkMinutes(record.clockIn, record.clockOut, rule)
        result.overtimeHours = result.workHours / 60
      }
      return result
    }

    // 请假处理
    if (hasLeave) {
      result.status = AttendanceStatus.LEAVE
      return result
    }

    // 正常工作日计算
    if (!rule.workDays.includes(dayOfWeek) && !isWorkday) {
      // 非工作日
      return result
    }

    // 无打卡记录
    if (!record.clockIn && !record.clockOut) {
      result.status = rule.absentRules.noRecord
        ? AttendanceStatus.ABSENT
        : AttendanceStatus.ABNORMAL
      return result
    }

    // 计算迟到
    if (record.clockIn) {
      const lateMinutes = this.calculateLateMinutes(
        record.clockIn,
        rule.workTime.start,
        rule.workTime.flexibleMinutes
      )

      if (lateMinutes > 0) {
        result.lateMinutes = lateMinutes
        result.status = AttendanceStatus.LATE

        // 检查是否迟到过久算旷工
        if (rule.absentRules.lateMinutes && lateMinutes >= rule.absentRules.lateMinutes) {
          result.status = AttendanceStatus.ABSENT
          return result
        }
      }
    }

    // 计算早退
    if (record.clockOut) {
      const earlyMinutes = this.calculateEarlyMinutes(
        record.clockOut,
        rule.workTime.end,
        rule.workTime.flexibleMinutes
      )

      if (earlyMinutes > 0) {
        result.earlyMinutes = earlyMinutes
        result.status =
          result.status === AttendanceStatus.LATE
            ? AttendanceStatus.ABNORMAL
            : AttendanceStatus.EARLY
      }
    }

    // 计算工作时长
    if (record.clockIn && record.clockOut) {
      result.workHours = this.calculateWorkMinutes(record.clockIn, record.clockOut, rule)

      // 计算加班
      const standardMinutes = this.getStandardWorkMinutes(rule)
      const overtimeMinutes = result.workHours - standardMinutes - rule.overtimeThreshold

      if (overtimeMinutes > 0) {
        result.overtimeHours = overtimeMinutes / 60
        if (result.status === AttendanceStatus.NORMAL) {
          result.status = AttendanceStatus.OVERTIME
        }
      }
    }

    return result
  }

  /**
   * 计算月度考勤统计
   */
  calculateMonthlyStats(year: number, month: number, dailyStats: AttendanceStats[]): MonthlyStats {
    const monthStart = dayjs(`${year}-${month}-01`)
    const monthEnd = monthStart.endOf('month')
    const totalDays = monthEnd.date()

    // 计算应出勤天数（排除周末和节假日）
    let workDays = 0
    for (let i = 1; i <= totalDays; i++) {
      const date = monthStart.date(i)
      const dayOfWeek = date.day()
      const dateStr = date.format('YYYY-MM-DD')
      const holiday = this.holidays.get(dateStr)

      if (holiday?.type === 'workday' || (!holiday && dayOfWeek !== 0 && dayOfWeek !== 6)) {
        workDays++
      }
    }

    // 统计各项数据
    const stats: MonthlyStats = {
      totalDays: workDays,
      actualDays: 0,
      lateDays: 0,
      earlyDays: 0,
      absentDays: 0,
      leaveDays: 0,
      overtimeDays: 0,
      totalWorkHours: 0,
      totalOvertimeHours: 0,
      attendanceRate: 0
    }

    dailyStats.forEach(day => {
      switch (day.status) {
        case AttendanceStatus.NORMAL:
        case AttendanceStatus.OVERTIME:
          stats.actualDays++
          break
        case AttendanceStatus.LATE:
          stats.actualDays++
          stats.lateDays++
          break
        case AttendanceStatus.EARLY:
          stats.actualDays++
          stats.earlyDays++
          break
        case AttendanceStatus.ABSENT:
          stats.absentDays++
          break
        case AttendanceStatus.LEAVE:
          stats.leaveDays++
          break
      }

      if (day.overtimeHours > 0) {
        stats.overtimeDays++
        stats.totalOvertimeHours += day.overtimeHours
      }

      stats.totalWorkHours += day.workHours / 60
    })

    // 计算出勤率
    if (stats.totalDays > 0) {
      stats.attendanceRate = ((stats.actualDays + stats.leaveDays) / stats.totalDays) * 100
    }

    return stats
  }

  /**
   * 计算迟到分钟数
   */
  private calculateLateMinutes(
    clockIn: string,
    scheduledStart: string,
    flexibleMinutes: number = 0
  ): number {
    const actualTime = dayjs(`2000-01-01 ${clockIn}`)
    const scheduledTime = dayjs(`2000-01-01 ${scheduledStart}`).add(flexibleMinutes, 'minute')

    if (actualTime.isAfter(scheduledTime)) {
      return actualTime.diff(scheduledTime, 'minute')
    }

    return 0
  }

  /**
   * 计算早退分钟数
   */
  private calculateEarlyMinutes(
    clockOut: string,
    scheduledEnd: string,
    flexibleMinutes: number = 0
  ): number {
    const actualTime = dayjs(`2000-01-01 ${clockOut}`)
    const scheduledTime = dayjs(`2000-01-01 ${scheduledEnd}`).subtract(flexibleMinutes, 'minute')

    if (actualTime.isBefore(scheduledTime)) {
      return scheduledTime.diff(actualTime, 'minute')
    }

    return 0
  }

  /**
   * 计算工作时长（分钟）
   */
  private calculateWorkMinutes(clockIn: string, clockOut: string, rule: AttendanceRule): number {
    const startTime = dayjs(`2000-01-01 ${clockIn}`)
    const endTime = dayjs(`2000-01-01 ${clockOut}`)

    let totalMinutes = endTime.diff(startTime, 'minute')

    // 扣除午休时间
    if (rule.workTime.breakStart && rule.workTime.breakEnd) {
      const breakStart = dayjs(`2000-01-01 ${rule.workTime.breakStart}`)
      const breakEnd = dayjs(`2000-01-01 ${rule.workTime.breakEnd}`)
      const breakMinutes = breakEnd.diff(breakStart, 'minute')

      // 检查是否跨越午休时间
      if (startTime.isBefore(breakEnd) && endTime.isAfter(breakStart)) {
        totalMinutes -= breakMinutes
      }
    }

    return Math.max(0, totalMinutes)
  }

  /**
   * 获取标准工作时长（分钟）
   */
  private getStandardWorkMinutes(rule: AttendanceRule): number {
    const start = dayjs(`2000-01-01 ${rule.workTime.start}`)
    const end = dayjs(`2000-01-01 ${rule.workTime.end}`)

    let minutes = end.diff(start, 'minute')

    // 扣除午休时间
    if (rule.workTime.breakStart && rule.workTime.breakEnd) {
      const breakStart = dayjs(`2000-01-01 ${rule.workTime.breakStart}`)
      const breakEnd = dayjs(`2000-01-01 ${rule.workTime.breakEnd}`)
      minutes -= breakEnd.diff(breakStart, 'minute')
    }

    return minutes
  }

  /**
   * 批量计算考勤
   */
  batchCalculate(
    records: ClockRecord[],
    rule: AttendanceRule,
    leaves: LeaveInfo[] = [],
    startDate: string,
    endDate: string
  ): AttendanceStats[] {
    const results: AttendanceStats[] = []
    const leaveMap = new Map<string, LeaveInfo>()

    // 构建请假映射
    leaves.forEach(leave => {
      const start = dayjs(leave.startDate)
      const end = dayjs(leave.endDate)
      let current = start

      while (current.isSameOrBefore(end, 'day')) {
        leaveMap.set(current.format('YYYY-MM-DD'), leave)
        current = current.add(1, 'day')
      }
    })

    // 构建打卡记录映射
    const recordMap = new Map<string, ClockRecord>()
    records.forEach(record => {
      recordMap.set(record.date, record)
    })

    // 遍历日期范围
    let current = dayjs(startDate)
    const end = dayjs(endDate)

    while (current.isSameOrBefore(end, 'day')) {
      const dateStr = current.format('YYYY-MM-DD')
      const record = recordMap.get(dateStr) || { date: dateStr }
      const leave = leaveMap.get(dateStr)

      const stats = this.calculateDailyAttendance(dateStr, record, rule, leave)

      results.push(stats)
      current = current.add(1, 'day')
    }

    return results
  }

  /**
   * 导出统计报表数据
   */

  exportStats(stats: AttendanceStats[]): unknown[] {
    return stats.map(stat => ({
      日期: stat.date,
      状态: this.getStatusText(stat.status),
      上班时间: stat.details.actualStart || '-',
      下班时间: stat.details.actualEnd || '-',
      工作时长: `${(stat.workHours / 60).toFixed(1)}小时`,
      迟到时长: stat.lateMinutes ? `${stat.lateMinutes}分钟` : '-',
      早退时长: stat.earlyMinutes ? `${stat.earlyMinutes}分钟` : '-',
      加班时长: stat.overtimeHours ? `${stat.overtimeHours.toFixed(1)}小时` : '-',
      备注: this.getRemarks(stat)
    }))
  }

  /**
   * 获取状态文本
   */
  private getStatusText(status: AttendanceStatus): string {
    const map: Record<AttendanceStatus, string> = {
      [AttendanceStatus.NORMAL]: '正常',
      [AttendanceStatus.LATE]: '迟到',
      [AttendanceStatus.EARLY]: '早退',
      [AttendanceStatus.ABSENT]: '旷工',
      [AttendanceStatus.LEAVE]: '请假',
      [AttendanceStatus.OVERTIME]: '加班',
      [AttendanceStatus.HOLIDAY]: '节假日',
      [AttendanceStatus.WEEKEND]: '周末',
      [AttendanceStatus.ABNORMAL]: '异常'
    }
    return map[status] || status
  }

  /**
   * 获取备注信息
   */
  private getRemarks(stat: AttendanceStats): string {
    const remarks: string[] = []

    if (stat.details.isHoliday) {
      remarks.push('法定节假日')
    }
    if (stat.details.isWeekend && stat.status !== AttendanceStatus.OVERTIME) {
      remarks.push('周末休息')
    }
    if (stat.details.hasLeave) {
      remarks.push(`${stat.details.leaveType || '请假'}`)
    }

    return remarks.join('，') || '-'
  }
}

// 创建默认实例
export const attendanceCalculator = new AttendanceCalculator()

// 辅助函数：根据员工获取适用的考勤规则
export function getApplicableRule(
  employeeId: string,
  department: string,
  rules: AttendanceRule[]
): AttendanceRule | null {
  // 这里应该根据员工信息匹配最合适的规则
  // 暂时返回优先级最高的规则
  return rules.sort((a, b) => b.priority - a.priority)[0] || null
}

// 辅助函数：验证打卡位置
export function validateClockLocation(
  actualLocation: { lat: number; lng: number },
  allowedLocations: Array<{ lat: number; lng: number; radius: number }>
): boolean {
  for (const allowed of allowedLocations) {
    const distance = calculateDistance(
      actualLocation.lat,
      actualLocation.lng,
      allowed.lat,
      allowed.lng
    )

    if (distance <= allowed.radius) {
      return true
    }
  }

  return false
}

// 计算两点之间的距离（米）
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371000 // 地球半径（米）
  const dLat = ((lat2 - lat1) * Math.PI) / 180
  const dLng = ((lng2 - lng1) * Math.PI) / 180
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}
