/**
 * 对象工具函数
 */

/**
 * 深拷贝
 * @param obj 要拷贝的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as T
  if (obj instanceof RegExp) return new RegExp(obj) as T
  if (obj instanceof Map)
    return new Map(Array.from(obj.entries()).map(([k, v]) => [k, deepClone(v)])) as T
  if (obj instanceof Set) return new Set(Array.from(obj).map(v => deepClone(v))) as T

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }

  if (obj instanceof Object) {
    const cloneObj = {} as Record<string, unknown>
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloneObj[key] = deepClone((obj as Record<string, unknown>)[key])
      }
    }
    return cloneObj as T
  }

  return obj
}

/**
 * 对象差异比较
 * @param obj1 对象1
 * @param obj2 对象2
 */

interface DiffResult {
  [key: string]: {
    type: 'added' | 'modified' | 'deleted'
    value?: unknown
    from?: unknown
    to?: unknown
    diff?: DiffResult
  }
}

export function diff(obj1: Record<string, unknown>, obj2: Record<string, unknown>): DiffResult {
  const result: DiffResult = {}

  // 检查obj2中的所有键
  for (const key in obj2) {
    if (obj2.hasOwnProperty(key)) {
      if (!(key in obj1)) {
        // 新增的键
        result[key] = { type: 'added', value: obj2[key] }
      } else if (obj1[key] !== obj2[key]) {
        // 值不同
        if (typeof obj2[key] === 'object' && obj2[key] !== null && !Array.isArray(obj2[key])) {
          // 递归比较嵌套对象
          const nestedDiff = diff(
            (obj1[key] as Record<string, unknown>) || {},
            obj2[key] as Record<string, unknown>
          )
          if (Object.keys(nestedDiff).length > 0) {
            result[key] = { type: 'modified', diff: nestedDiff }
          }
        } else {
          result[key] = {
            type: 'modified',
            from: obj1[key],
            to: obj2[key]
          }
        }
      }
    }
  }

  // 检查obj1中被删除的键
  for (const key in obj1) {
    if (obj1.hasOwnProperty(key) && !(key in obj2)) {
      result[key] = { type: 'deleted', value: obj1[key] }
    }
  }

  return result
}

/**
 * 对象合并（深度合并）
 * @param target 目标对象
 * @param sources 源对象
 */
export function merge<T extends Record<string, unknown>>(target: T, ...sources: Partial<T>[]): T {
  if (!sources.length) return target

  const source = sources.shift()
  if (!source) return target

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key]
      const targetValue = target[key]

      if (isObject(targetValue) && isObject(sourceValue)) {
        ;(target as Record<string, unknown>)[key] = merge(
          {} as Record<string, unknown>,
          targetValue,
          sourceValue
        )
      } else if (Array.isArray(targetValue) && Array.isArray(sourceValue)) {
        ;(target as Record<string, unknown>)[key] = [...targetValue, ...sourceValue]
      } else {
        ;(target as Record<string, unknown>)[key] = sourceValue
      }
    }
  }

  return merge(target, ...sources)
}

/**
 * 判断是否为普通对象
 * @param value 要判断的值
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return value !== null && typeof value === 'object' && value.constructor === Object
}

/**
 * 判断是否为空值
 * @param value 要判断的值
 */

export function isEmpty(value: unknown): boolean {
  if (value === null || value === undefined || value === '') return true
  if (Array.isArray(value)) return value.length === 0
  if (value instanceof Map || value instanceof Set) return value.size === 0
  if (isObject(value)) return Object.keys(value).length === 0
  return false
}

/**
 * 对象选择器（pick）
 * @param obj 原对象
 * @param keys 要选择的键
 */
export function pick<T extends Record<string, unknown>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}

/**
 * 对象排除器（omit）
 * @param obj 原对象
 * @param keys 要排除的键
 */
export function omit<T extends Record<string, unknown>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = { ...obj }
  keys.forEach(key => {
    delete result[key]
  })
  return result
}

/**
 * 获取嵌套对象的值
 * @param obj 对象
 * @param path 路径
 * @param defaultValue 默认值
 */
export function get<T = unknown>(
  obj: unknown,
  path: string | string[],
  defaultValue?: T
): T | undefined {
  const keys = Array.isArray(path) ? path : path.split('.')
  let result: unknown = obj

  for (const key of keys) {
    if (result === null || result === undefined) {
      return defaultValue
    }
    if (typeof result === 'object' && result !== null && key in result) {
      result = (result as Record<string, unknown>)[key]
    } else {
      return defaultValue
    }
  }

  return result === undefined ? defaultValue : (result as T)
}

/**
 * 设置嵌套对象的值
 * @param obj 对象
 * @param path 路径
 * @param value 值
 */
export function set<T extends Record<string, unknown>>(
  obj: T,
  path: string | string[],

  value: unknown
): T {
  const keys = Array.isArray(path) ? path : path.split('.')
  const lastKey = keys.pop()!

  let current: Record<string, unknown> = obj
  for (const key of keys) {
    if (!(key in current) || !isObject(current[key])) {
      current[key] = {}
    }
    current = current[key] as Record<string, unknown>
  }

  current[lastKey] = value
  return obj
}

/**
 * 检查嵌套对象是否有某个路径
 * @param obj 对象
 * @param path 路径
 */

export function has(obj: unknown, path: string | string[]): boolean {
  const keys = Array.isArray(path) ? path : path.split('.')
  let current: unknown = obj

  for (const key of keys) {
    if (current === null || current === undefined) {
      return false
    }
    if (typeof current === 'object' && current !== null && key in current) {
      current = (current as Record<string, unknown>)[key]
    } else {
      return false
    }
  }

  return true
}

/**
 * 对象键名转换（驼峰转下划线）
 * @param obj 原对象
 */
export function keysToSnakeCase<T extends Record<string, unknown>>(
  obj: T
): Record<string, unknown> {
  if (!isObject(obj)) return obj

  const result: Record<string, unknown> = {}
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
      const value = obj[key]

      if (Array.isArray(value)) {
        result[snakeKey] = value.map(item => (isObject(item) ? keysToSnakeCase(item) : item))
      } else if (isObject(value)) {
        result[snakeKey] = keysToSnakeCase(value)
      } else {
        result[snakeKey] = value
      }
    }
  }
  return result
}

/**
 * 对象键名转换（下划线转驼峰）
 * @param obj 原对象
 */
export function keysToCamelCase<T extends Record<string, unknown>>(
  obj: T
): Record<string, unknown> {
  if (!isObject(obj)) return obj

  const result: Record<string, unknown> = {}
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
      const value = obj[key]

      if (Array.isArray(value)) {
        result[camelKey] = value.map(item => (isObject(item) ? keysToCamelCase(item) : item))
      } else if (isObject(value)) {
        result[camelKey] = keysToCamelCase(value)
      } else {
        result[camelKey] = value
      }
    }
  }
  return result
}

/**
 * 冻结对象（深度冻结）
 * @param obj 要冻结的对象
 */
export function deepFreeze<T extends Record<string, unknown>>(obj: T): Readonly<T> {
  Object.freeze(obj)

  Object.getOwnPropertyNames(obj).forEach(prop => {
    const value = obj[prop]
    if (value !== null && (typeof value === 'object' || typeof value === 'function')) {
      if (isObject(value)) {
        deepFreeze(value)
      } else if (typeof value === 'object') {
        Object.freeze(value)
      }
    }
  })

  return obj
}
