/**
 * 核心工具函数统一导出
 */

// 数组工具
import * as array from './array'

// 对象工具
import * as object from './object'

// 字符串工具
import * as string from './string'

// 数字工具
import * as number from './number'

// 日期工具
import * as date from './date'

// 树形结构工具
import * as tree from './tree'

// 导出所有工具
export { array, object, string, number, date, tree }

// 导出常用函数
export {
  // 数组
  unique,
  groupBy,
  chunk,
  flatten,
  difference,
  intersection,
  union,
  sum,
  average,
  max,
  min,
  shuffle,
  sortBy
} from './array'

export {
  // 对象
  deepClone,
  diff,
  merge,
  isObject,
  isEmpty,
  pick,
  omit,
  get,
  set,
  has
} from './object'

export {
  // 字符串
  camelToSnake,
  snakeToCamel,
  camelToKebab,
  kebabToCamel,
  capitalize,
  uuid,
  uniqueId,
  truncate,
  highlight,
  escapeHtml,
  template
} from './string'

export {
  // 数字
  format as formatNumber,
  formatBytes,
  formatCurrency,
  formatPercent,
  add,
  subtract,
  multiply,
  divide,
  round,
  clamp,
  percentage
} from './number'

export {
  // 日期
  format as formatDate,
  formatRelative,
  formatDuration,
  getDateRange,
  add as addDate,
  subtract as subtractDate,
  startOf,
  endOf,
  isSame,
  isBetween,
  getWorkdays
} from './date'

export {
  // 树形结构
  fromList as arrayToTree,
  toList as treeToArray,
  find as findTreeNode,
  findAll as findAllTreeNodes,
  findPath as findTreePath,
  filter as filterTree,
  map as mapTree,
  traverse as traverseTree,
  getDepth as getTreeDepth,
  getLeaves as getTreeLeaves
} from './tree'
