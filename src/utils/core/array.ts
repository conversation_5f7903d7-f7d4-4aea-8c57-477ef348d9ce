/**
 * 数组工具函数
 */

/**
 * 数组去重
 * @param arr 原数组
 * @param key 对象数组去重时的键名
 */
export function unique<T>(arr: T[], key?: keyof T): T[] {
  if (!key) {
    return [...new Set(arr)]
  }

  const seen = new Set()
  return arr.filter(item => {
    const value = item[key]
    if (seen.has(value)) {
      return false
    }
    seen.add(value)
    return true
  })
}

/**
 * 数组分组
 * @param arr 原数组
 * @param key 分组键名
 */
export function groupBy<T>(arr: T[], key: keyof T): Record<string, T[]> {
  return arr.reduce(
    (groups, item) => {
      const groupKey = String(item[key])
      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push(item)
      return groups
    },
    {} as Record<string, T[]>
  )
}

/**
 * 数组分块
 * @param array 原数组
 * @param size 块大小
 */
export function chunk<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}

/**
 * 数组扁平化
 * @param arr 多维数组
 * @param depth 扁平化深度
 */

export function flatten<T>(arr: unknown[], depth: number = 1): T[] {
  if (depth <= 0) return arr
  return arr.reduce((acc, val) => {
    if (Array.isArray(val)) {
      return acc.concat(flatten(val, depth - 1))
    }
    return acc.concat(val)
  }, [])
}

/**
 * 数组差集
 * @param arr1 数组1
 * @param arr2 数组2
 * @param key 对象数组比较时的键名
 */
export function difference<T>(arr1: T[], arr2: T[], key?: keyof T): T[] {
  if (!key) {
    const set2 = new Set(arr2)
    return arr1.filter(x => !set2.has(x))
  }

  const map2 = new Map(arr2.map(item => [item[key], item]))
  return arr1.filter(item => !map2.has(item[key]))
}

/**
 * 数组交集
 * @param arr1 数组1
 * @param arr2 数组2
 * @param key 对象数组比较时的键名
 */
export function intersection<T>(arr1: T[], arr2: T[], key?: keyof T): T[] {
  if (!key) {
    const set2 = new Set(arr2)
    return arr1.filter(x => set2.has(x))
  }

  const map2 = new Map(arr2.map(item => [item[key], item]))
  return arr1.filter(item => map2.has(item[key]))
}

/**
 * 数组并集
 * @param arr1 数组1
 * @param arr2 数组2
 * @param key 对象数组去重时的键名
 */
export function union<T>(arr1: T[], arr2: T[], key?: keyof T): T[] {
  const combined = [...arr1, ...arr2]
  return unique(combined, key)
}

/**
 * 数组求和
 * @param arr 数字数组
 * @param key 对象数组时的数值键名
 */
export function sum<T>(arr: T[], key?: keyof T): number {
  if (!key) {
    return (arr as unknown as number[]).reduce((acc, val) => acc + val, 0)
  }

  return arr.reduce((acc, item) => acc + Number(item[key] || 0), 0)
}

/**
 * 数组求平均值
 * @param arr 数字数组
 * @param key 对象数组时的数值键名
 */
export function average<T>(arr: T[], key?: keyof T): number {
  if (arr.length === 0) return 0
  return sum(arr, key) / arr.length
}

/**
 * 数组最大值
 * @param arr 数字数组
 * @param key 对象数组时的数值键名
 */
export function max<T>(arr: T[], key?: keyof T): number {
  if (arr.length === 0) return -Infinity

  if (!key) {
    return Math.max(...(arr as unknown as number[]))
  }

  return Math.max(...arr.map(item => Number(item[key] || -Infinity)))
}

/**
 * 数组最小值
 * @param arr 数字数组
 * @param key 对象数组时的数值键名
 */
export function min<T>(arr: T[], key?: keyof T): number {
  if (arr.length === 0) return Infinity

  if (!key) {
    return Math.min(...(arr as unknown as number[]))
  }

  return Math.min(...arr.map(item => Number(item[key] || Infinity)))
}

/**
 * 数组洗牌
 * @param arr 原数组
 */
export function shuffle<T>(arr: T[]): T[] {
  const shuffled = [...arr]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

/**
 * 数组按属性排序
 * @param arr 原数组
 * @param key 排序键名
 * @param order 排序方向
 */
export function sortBy<T>(arr: T[], key: keyof T, order: 'asc' | 'desc' = 'asc'): T[] {
  const sorted = [...arr].sort((a, b) => {
    const aVal = a[key]
    const bVal = b[key]

    if (aVal < bVal) return order === 'asc' ? -1 : 1
    if (aVal > bVal) return order === 'asc' ? 1 : -1
    return 0
  })

  return sorted
}

/**
 * 多条件排序
 * @param arr 原数组
 * @param criteria 排序条件数组
 */
export function multiSort<T>(
  arr: T[],
  criteria: Array<{ key: keyof T; order?: 'asc' | 'desc' }>
): T[] {
  return [...arr].sort((a, b) => {
    for (const { key, order = 'asc' } of criteria) {
      const aVal = a[key]
      const bVal = b[key]

      if (aVal < bVal) return order === 'asc' ? -1 : 1
      if (aVal > bVal) return order === 'asc' ? 1 : -1
    }
    return 0
  })
}
