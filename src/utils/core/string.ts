/**
 * 字符串工具函数
 */

/**
 * 驼峰转下划线
 * @param str 驼峰字符串
 */
export function camelToSnake(str: string): string {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

/**
 * 下划线转驼峰
 * @param str 下划线字符串
 */
export function snakeToCamel(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 驼峰转短横线
 * @param str 驼峰字符串
 */
export function camelToKebab(str: string): string {
  return str.replace(/[A-Z]/g, letter => `-${letter.toLowerCase()}`)
}

/**
 * 短横线转驼峰
 * @param str 短横线字符串
 */
export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 首字母大写
 * @param str 字符串
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * 首字母小写
 * @param str 字符串
 */
export function uncapitalize(str: string): string {
  return str.charAt(0).toLowerCase() + str.slice(1)
}

/**
 * 生成随机字符串
 * @param length 长度
 * @param chars 字符集
 */
export function random(
  length: number = 8,
  chars: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成UUID
 */
export function uuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 */
export function uniqueId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 文本截断
 * @param str 原字符串
 * @param length 截断长度
 * @param suffix 后缀
 */
export function truncate(str: string, length: number, suffix: string = '...'): string {
  if (str.length <= length) return str
  return str.substr(0, length - suffix.length) + suffix
}

/**
 * 文本高亮
 * @param text 原文本
 * @param keyword 关键词
 * @param className CSS类名
 */
export function highlight(text: string, keyword: string, className: string = 'highlight'): string {
  if (!keyword) return text
  const regex = new RegExp(`(${escapeRegExp(keyword)})`, 'gi')
  return text.replace(regex, `<span class="${className}">$1</span>`)
}

/**
 * 转义正则表达式特殊字符
 * @param str 字符串
 */
export function escapeRegExp(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 转义HTML
 * @param str HTML字符串
 */
export function escapeHtml(str: string): string {
  const htmlEscapes: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
  }

  return str.replace(/[&<>"']/g, match => htmlEscapes[match])
}

/**
 * 反转义HTML
 * @param str 转义后的字符串
 */
export function unescapeHtml(str: string): string {
  const htmlUnescapes: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'"
  }

  return str.replace(/&(?:amp|lt|gt|quot|#39);/g, match => htmlUnescapes[match])
}

/**
 * 移除HTML标签
 * @param str HTML字符串
 */
export function stripHtml(str: string): string {
  return str.replace(/<[^>]*>/g, '')
}

/**
 * 填充字符串
 * @param str 原字符串
 * @param length 目标长度
 * @param char 填充字符
 * @param position 填充位置
 */
export function pad(
  str: string,
  length: number,
  char: string = ' ',
  position: 'start' | 'end' | 'both' = 'end'
): string {
  const padLength = length - str.length
  if (padLength <= 0) return str

  const padStr = char.repeat(Math.ceil(padLength / char.length)).substr(0, padLength)

  switch (position) {
    case 'start':
      return padStr + str
    case 'end':
      return str + padStr
    case 'both':
      const start = Math.floor(padLength / 2)
      const end = padLength - start
      return char.repeat(start) + str + char.repeat(end)
  }
}

/**
 * 字符串模板替换
 * @param template 模板字符串
 * @param data 数据对象
 */
export function template(template: string, data: Record<string, unknown>): string {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return data[key] !== undefined ? String(data[key]) : match
  })
}

/**
 * 计算字符串字节长度
 * @param str 字符串
 */
export function byteLength(str: string): number {
  let length = 0
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i)
    if (charCode < 0x80) {
      length += 1
    } else if (charCode < 0x800) {
      length += 2
    } else if (charCode < 0xd800 || charCode >= 0xe000) {
      length += 3
    } else {
      length += 4
      i++ // 跳过低位代理
    }
  }
  return length
}

/**
 * 按字节长度截断字符串
 * @param str 原字符串
 * @param maxBytes 最大字节数
 * @param suffix 后缀
 */
export function truncateByBytes(str: string, maxBytes: number, suffix: string = '...'): string {
  let bytes = 0
  let i = 0

  for (; i < str.length; i++) {
    const charCode = str.charCodeAt(i)
    let charBytes = 1

    if (charCode < 0x80) {
      charBytes = 1
    } else if (charCode < 0x800) {
      charBytes = 2
    } else if (charCode < 0xd800 || charCode >= 0xe000) {
      charBytes = 3
    } else {
      charBytes = 4
      i++ // 跳过低位代理
    }

    if (bytes + charBytes > maxBytes - byteLength(suffix)) {
      break
    }

    bytes += charBytes
  }

  return i < str.length ? str.substr(0, i) + suffix : str
}
