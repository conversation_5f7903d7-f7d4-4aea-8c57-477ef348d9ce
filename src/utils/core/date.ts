/**
 * 日期时间工具函数
 */

/**
 * 日期格式化
 * @param date 日期
 * @param format 格式字符串
 */
export function format(date: Date | string | number, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  const d = new Date(date)
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  const milliseconds = String(d.getMilliseconds()).padStart(3, '0')
  
  const replacements: Record<string, string> = {
    'YYYY': String(year),
    'YY': String(year).slice(-2),
    'MM': month,
    'M': String(d.getMonth() + 1),
    'DD': day,
    'D': String(d.getDate()),
    'HH': hours,
    'H': String(d.getHours()),
    'hh': String(d.getHours() % 12 || 12).padStart(2, '0'),
    'h': String(d.getHours() % 12 || 12),
    'mm': minutes,
    'm': String(d.getMinutes()),
    'ss': seconds,
    's': String(d.getSeconds()),
    'SSS': milliseconds,
    'A': d.getHours() >= 12 ? 'PM' : 'AM',
    'a': d.getHours() >= 12 ? 'pm' : 'am'
  }
  
  return format.replace(/YYYY|YY|MM|M|DD|D|HH|H|hh|h|mm|m|ss|s|SSS|A|a/g, match => replacements[match])
}

/**
 * 相对时间格式化
 * @param date 日期
 * @param locale 语言环境
 */
export function formatRelative(date: Date | string | number, locale: 'zh' | 'en' = 'zh'): string {
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()
  
  const minute = 60 * 1000
  const hour = minute * 60
  const day = hour * 24
  const week = day * 7
  const month = day * 30
  const year = day * 365
  
  const messages = {
    zh: {
      justNow: '刚刚',
      minutesAgo: (n: number) => `${n}分钟前`,
      hoursAgo: (n: number) => `${n}小时前`,
      daysAgo: (n: number) => `${n}天前`,
      weeksAgo: (n: number) => `${n}周前`,
      monthsAgo: (n: number) => `${n}个月前`,
      yearsAgo: (n: number) => `${n}年前`,
      future: '未来'
  },
    en: {
      justNow: 'just now',
      minutesAgo: (n: number) => `${n} minute${n > 1 ? 's' : ''} ago`,
      hoursAgo: (n: number) => `${n} hour${n > 1 ? 's' : ''} ago`,
      daysAgo: (n: number) => `${n} day${n > 1 ? 's' : ''} ago`,
      weeksAgo: (n: number) => `${n} week${n > 1 ? 's' : ''} ago`,
      monthsAgo: (n: number) => `${n} month${n > 1 ? 's' : ''} ago`,
      yearsAgo: (n: number) => `${n} year${n > 1 ? 's' : ''} ago`,
      future: 'future'
    }
  }
  
  const msg = messages[locale]
  
  if (diff < 0) return msg.future
  if (diff < minute) return msg.justNow
  if (diff < hour) return msg.minutesAgo(Math.floor(diff / minute))
  if (diff < day) return msg.hoursAgo(Math.floor(diff / hour))
  if (diff < week) return msg.daysAgo(Math.floor(diff / day))
  if (diff < month) return msg.weeksAgo(Math.floor(diff / week))
  if (diff < year) return msg.monthsAgo(Math.floor(diff / month))
  return msg.yearsAgo(Math.floor(diff / year))
}

/**
 * 时间范围格式化
 * @param start 开始时间
 * @param end 结束时间
 * @param locale 语言环境
 */
export function formatDuration(start: Date | string | number, end: Date | string | number, locale: 'zh' | 'en' = 'zh'): string {
  const startDate = new Date(start)
  const endDate = new Date(end)
  const diff = endDate.getTime() - startDate.getTime()
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)
  
  const parts = []
  
  if (locale === 'zh') {
    if (days > 0) parts.push(`${days}天`)
    if (hours > 0) parts.push(`${hours}小时`)
    if (minutes > 0) parts.push(`${minutes}分钟`)
    if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`)
  } else {
    if (days > 0) parts.push(`${days} day${days > 1 ? 's' : ''}`)
    if (hours > 0) parts.push(`${hours} hour${hours > 1 ? 's' : ''}`)
    if (minutes > 0) parts.push(`${minutes} minute${minutes > 1 ? 's' : ''}`)
    if (seconds > 0 || parts.length === 0) parts.push(`${seconds} second${seconds > 1 ? 's' : ''}`)
  }
  
  return parts.join(locale === 'zh' ? '' : ' ')
}

/**
 * 获取日期范围
 * @param type 范围类型
 * @param date 基准日期
 */
export function getDateRange(type: 'day' | 'week' | 'month' | 'quarter' | 'year', date: Date = new Date()
): { start: Date; end: Date } {
  const year = date.getFullYear()
  const month = date.getMonth()
  const day = date.getDate()
  
  let start: Date
  let end: Date
  
  switch (type) {
    case 'day':
      start = new Date(year, month, day, 0, 0, 0, 0)
      end = new Date(year, month, day, 23, 59, 59, 999)
      break
      
    case 'week':
      const weekDay = date.getDay()
      const monday = day - weekDay + (weekDay === 0 ? -6 : 1)
      start = new Date(year, month, monday, 0, 0, 0, 0)
      end = new Date(year, month, monday + 6, 23, 59, 59, 999)
      break
      
    case 'month':
      start = new Date(year, month, 1, 0, 0, 0, 0)
      end = new Date(year, month + 1, 0, 23, 59, 59, 999)
      break
      
    case 'quarter':
      const quarter = Math.floor(month / 3)
      start = new Date(year, quarter * 3, 1, 0, 0, 0, 0)
      end = new Date(year, quarter * 3 + 3, 0, 23, 59, 59, 999)
      break
      
    case 'year':
      start = new Date(year, 0, 1, 0, 0, 0, 0)
      end = new Date(year, 11, 31, 23, 59, 59, 999)
      break
  }
  
  return { start, end }
}

/**
 * 添加时间
 * @param date 基准日期
 * @param amount 数量
 * @param unit 单位
 */
export function add(date: Date | string | number, amount: number, unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second' | 'millisecond'): Date {
  const d = new Date(date)
  
  switch (unit) {
    case 'year':
      d.setFullYear(d.getFullYear() + amount)
      break
    case 'month':
      d.setMonth(d.getMonth() + amount)
      break
    case 'day':
      d.setDate(d.getDate() + amount)
      break
    case 'hour':
      d.setHours(d.getHours() + amount)
      break
    case 'minute':
      d.setMinutes(d.getMinutes() + amount)
      break
    case 'second':
      d.setSeconds(d.getSeconds() + amount)
      break
    case 'millisecond':
      d.setMilliseconds(d.getMilliseconds() + amount)
      break
  }
  
  return d
}

/**
 * 减少时间
 * @param date 基准日期
 * @param amount 数量
 * @param unit 单位
 */
export function subtract(date: Date | string | number, amount: number, unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second' | 'millisecond'): Date {
  return add(date, -amount, unit)
}

/**
 * 开始时间
 * @param date 日期
 * @param unit 单位
 */
export function startOf(date: Date | string | number, unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second'): Date {
  const d = new Date(date)
  
  switch (unit) {
    case 'year':
      d.setMonth(0, 1)
      d.setHours(0, 0, 0, 0)
      break
    case 'month':
      d.setDate(1)
      d.setHours(0, 0, 0, 0)
      break
    case 'day':
      d.setHours(0, 0, 0, 0)
      break
    case 'hour':
      d.setMinutes(0, 0, 0)
      break
    case 'minute':
      d.setSeconds(0, 0)
      break
    case 'second':
      d.setMilliseconds(0)
      break
  }
  
  return d
}

/**
 * 结束时间
 * @param date 日期
 * @param unit 单位
 */
export function endOf(date: Date | string | number, unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second'): Date {
  const d = new Date(date)
  
  switch (unit) {
    case 'year':
      d.setMonth(11, 31)
      d.setHours(23, 59, 59, 999)
      break
    case 'month':
      d.setMonth(d.getMonth() + 1, 0)
      d.setHours(23, 59, 59, 999)
      break
    case 'day':
      d.setHours(23, 59, 59, 999)
      break
    case 'hour':
      d.setMinutes(59, 59, 999)
      break
    case 'minute':
      d.setSeconds(59, 999)
      break
    case 'second':
      d.setMilliseconds(999)
      break
  }
  
  return d
}

/**
 * 判断是否为同一时间单位
 * @param date1 日期1
 * @param date2 日期2
 * @param unit 单位
 */
export function isSame(date1: Date | string | number, date2: Date | string | number, unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second' = 'day'): boolean {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  
  switch (unit) {
    case 'year':
      return d1.getFullYear() === d2.getFullYear()
    case 'month':
      return isSame(d1, d2, 'year') && d1.getMonth() === d2.getMonth()
    case 'day':
      return isSame(d1, d2, 'month') && d1.getDate() === d2.getDate()
    case 'hour':
      return isSame(d1, d2, 'day') && d1.getHours() === d2.getHours()
    case 'minute':
      return isSame(d1, d2, 'hour') && d1.getMinutes() === d2.getMinutes()
    case 'second':
      return isSame(d1, d2, 'minute') && d1.getSeconds() === d2.getSeconds()
  }
}

/**
 * 判断是否在日期范围内
 * @param date 日期
 * @param start 开始日期
 * @param end 结束日期
 * @param inclusive 是否包含边界
 */
export function isBetween(date: Date | string | number, start: Date | string | number, end: Date | string | number, inclusive: boolean = true): boolean {
  const d = new Date(date).getTime()
  const s = new Date(start).getTime()
  const e = new Date(end).getTime()
  
  return inclusive ? d >= s && d <= e : d > s && d < e
}

/**
 * 获取工作日数量
 * @param start 开始日期
 * @param end 结束日期
 * @param holidays 假期数组
 */
export function getWorkdays(start: Date | string | number, end: Date | string | number, holidays: Array<Date | string | number> = []): number {
  const startDate = new Date(start)
  const endDate = new Date(end)
  const holidaySet = new Set(holidays.map(h => format(h, 'YYYY-MM-DD')))
  
  let count = 0
  const current = new Date(startDate)
  
  while (current <= endDate) {
    const dayOfWeek = current.getDay()
    const dateStr = format(current, 'YYYY-MM-DD')
    
    // 非周末且非假期
    if (dayOfWeek !== 0 && dayOfWeek !== 6 && !holidaySet.has(dateStr)) {
      count++
    }
    
    current.setDate(current.getDate() + 1)
  }
  
  return count
}