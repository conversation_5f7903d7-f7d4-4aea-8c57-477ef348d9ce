 

/**
 * array 单元测试
 * @description 自动生成的测试文件
 */

import { describe, it, expect } from 'vitest'
import {
  unique,
  groupBy,
  chunk,
  flatten,
  difference,
  intersection,
  union,
  sum,
  average,
  max,
  min,
  shuffle,
  sortBy,
  multiSort
} from '../array'
describe('unique', () => {
  it('应该被正确导出', () => {
    expect(unique).toBeDefined()
    expect(typeof unique).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = unique([], undefined)
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => unique(null, undefined)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => unique({}, {})).not.toThrow()
  })
})

describe('groupBy', () => {
  it('应该被正确导出', () => {
    expect(groupBy).toBeDefined()
    expect(typeof groupBy).toBe('function')
  })

  it('应该正确处理数组数据', () => {
    const testData = [1, 2, 3, 4, 5]
    const result = groupBy(testData)

    expect(Array.isArray(result)).toBe(true)
    expect(result).toHaveLength(testData.length)
  })

  it('应该处理空数组', () => {
    const result = groupBy([])

    expect(Array.isArray(result)).toBe(true)
    expect(result).toHaveLength(0)
  })

  it('应该保持数据不可变性', () => {
    const original = [1, 2, 3]
    const copy = [...original]

    groupBy(original)

    expect(original).toEqual(copy) // 原数组不应被修改
  })
})

describe('chunk', () => {
  it('应该被正确导出', () => {
    expect(chunk).toBeDefined()
    expect(typeof chunk).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = chunk([], 123)
    expect(result).toBeDefined()
  })

  it('应该正确处理边界值', () => {
    // 边界值: 0
    const result = chunk([], 0)
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => chunk(null, null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => chunk({}, {})).not.toThrow()
  })
})

describe('flatten', () => {
  it('应该被正确导出', () => {
    expect(flatten).toBeDefined()
    expect(typeof flatten).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = flatten([], 123)
    expect(result).toBeDefined()
  })

  it('应该正确处理边界值', () => {
    // 边界值: 0
    const result = flatten([], 0)
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => flatten(null, null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => flatten({}, {})).not.toThrow()
  })
})

describe('difference', () => {
  it('应该被正确导出', () => {
    expect(difference).toBeDefined()
    expect(typeof difference).toBe('function')
  })

  it('应该正确处理数组数据', () => {
    const testData = [1, 2, 3, 4, 5]
    const result = difference(testData)

    expect(Array.isArray(result)).toBe(true)
    expect(result).toHaveLength(testData.length)
  })

  it('应该处理空数组', () => {
    const result = difference([])

    expect(Array.isArray(result)).toBe(true)
    expect(result).toHaveLength(0)
  })

  it('应该保持数据不可变性', () => {
    const original = [1, 2, 3]
    const copy = [...original]

    difference(original)

    expect(original).toEqual(copy) // 原数组不应被修改
  })
})

describe('intersection', () => {
  it('应该被正确导出', () => {
    expect(intersection).toBeDefined()
    expect(typeof intersection).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = intersection([], [], undefined)
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => intersection(null, null, undefined)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => intersection({}, {}, {})).not.toThrow()
  })
})

describe('union', () => {
  it('应该被正确导出', () => {
    expect(union).toBeDefined()
    expect(typeof union).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = union([], [], undefined)
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => union(null, null, undefined)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => union({}, {}, {})).not.toThrow()
  })
})

describe('sum', () => {
  it('应该被正确导出', () => {
    expect(sum).toBeDefined()
    expect(typeof sum).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = sum([], undefined)
    expect(typeof result).toBe('number')
    expect(result).toBeGreaterThan(0)
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => sum(null, undefined)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => sum({}, {})).not.toThrow()
  })
})

describe('average', () => {
  it('应该被正确导出', () => {
    expect(average).toBeDefined()
    expect(typeof average).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = average([], undefined)
    expect(typeof result).toBe('number')
    expect(result).toBeGreaterThan(0)
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => average(null, undefined)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => average({}, {})).not.toThrow()
  })
})

describe('max', () => {
  it('应该被正确导出', () => {
    expect(max).toBeDefined()
    expect(typeof max).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = max([], undefined)
    expect(typeof result).toBe('number')
    expect(result).toBeGreaterThan(0)
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => max(null, undefined)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => max({}, {})).not.toThrow()
  })
})

describe('min', () => {
  it('应该被正确导出', () => {
    expect(min).toBeDefined()
    expect(typeof min).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = min([], undefined)
    expect(typeof result).toBe('number')
    expect(result).toBeGreaterThan(0)
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => min(null, undefined)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => min({}, {})).not.toThrow()
  })
})

describe('shuffle', () => {
  it('应该被正确导出', () => {
    expect(shuffle).toBeDefined()
    expect(typeof shuffle).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = shuffle([])
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => shuffle(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => shuffle({})).not.toThrow()
  })
})

describe('sortBy', () => {
  it('应该被正确导出', () => {
    expect(sortBy).toBeDefined()
    expect(typeof sortBy).toBe('function')
  })

  it('应该正确处理数组数据', () => {
    const testData = [1, 2, 3, 4, 5]
    const result = sortBy(testData)

    expect(Array.isArray(result)).toBe(true)
    expect(result).toHaveLength(testData.length)
  })

  it('应该处理空数组', () => {
    const result = sortBy([])

    expect(Array.isArray(result)).toBe(true)
    expect(result).toHaveLength(0)
  })

  it('应该保持数据不可变性', () => {
    const original = [1, 2, 3]
    const copy = [...original]

    sortBy(original)

    expect(original).toEqual(copy) // 原数组不应被修改
  })
})

describe('multiSort', () => {
  it('应该被正确导出', () => {
    expect(multiSort).toBeDefined()
    expect(typeof multiSort).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = multiSort([], [])
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => multiSort(null, null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => multiSort({}, {})).not.toThrow()
  })
})
