 

/**
 * string 单元测试
 * @description 自动生成的测试文件
 */

import { describe, it, expect } from 'vitest'
import {
  camelToSnake,
  snakeToCamel,
  camelToKebab,
  kebabToCamel,
  capitalize,
  uncapitalize,
  random,
  uuid,
  uniqueId,
  truncate,
  highlight,
  escapeRegExp,
  escapeHtml,
  unescapeHtml,
  stripHtml,
  pad,
  template,
  byteLength,
  truncateByBytes
} from '../string'
describe('camelToSnake', () => {
  it('应该被正确导出', () => {
    expect(camelToSnake).toBeDefined()
    expect(typeof camelToSnake).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = camelToSnake('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => camelToSnake(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => camelToSnake({})).not.toThrow()
  })
})

describe('snakeToCamel', () => {
  it('应该被正确导出', () => {
    expect(snakeToCamel).toBeDefined()
    expect(typeof snakeToCamel).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = snakeToCamel('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => snakeToCamel(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => snakeToCamel({})).not.toThrow()
  })
})

describe('camelToKebab', () => {
  it('应该被正确导出', () => {
    expect(camelToKebab).toBeDefined()
    expect(typeof camelToKebab).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = camelToKebab('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => camelToKebab(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => camelToKebab({})).not.toThrow()
  })
})

describe('kebabToCamel', () => {
  it('应该被正确导出', () => {
    expect(kebabToCamel).toBeDefined()
    expect(typeof kebabToCamel).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = kebabToCamel('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => kebabToCamel(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => kebabToCamel({})).not.toThrow()
  })
})

describe('capitalize', () => {
  it('应该被正确导出', () => {
    expect(capitalize).toBeDefined()
    expect(typeof capitalize).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = capitalize('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => capitalize(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => capitalize({})).not.toThrow()
  })
})

describe('uncapitalize', () => {
  it('应该被正确导出', () => {
    expect(uncapitalize).toBeDefined()
    expect(typeof uncapitalize).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = uncapitalize('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => uncapitalize(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => uncapitalize({})).not.toThrow()
  })
})

describe('random', () => {
  it('应该被正确导出', () => {
    expect(random).toBeDefined()
    expect(typeof random).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = random(123, 'test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理边界值', () => {
    // 边界值: 0
    const result = random(0, 'test')
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => random(null, null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => random({}, {})).not.toThrow()
  })
})

describe('uuid', () => {
  it('应该被正确导出', () => {
    expect(uuid).toBeDefined()
    expect(typeof uuid).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = uuid()
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    const result = uuid()
    expect(result).toBeDefined()
  })
})

describe('uniqueId', () => {
  it('应该被正确导出', () => {
    expect(uniqueId).toBeDefined()
    expect(typeof uniqueId).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = uniqueId('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => uniqueId(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => uniqueId({})).not.toThrow()
  })
})

describe('truncate', () => {
  it('应该被正确导出', () => {
    expect(truncate).toBeDefined()
    expect(typeof truncate).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = truncate('test', 123, 'test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理边界值', () => {
    // 边界值: 0
    const result = truncate('test', 0, 'test')
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => truncate(null, null, null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => truncate({}, {}, {})).not.toThrow()
  })
})

describe('highlight', () => {
  it('应该被正确导出', () => {
    expect(highlight).toBeDefined()
    expect(typeof highlight).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = highlight('test', 'test', 'test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => highlight(null, null, null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => highlight({}, {}, {})).not.toThrow()
  })
})

describe('escapeRegExp', () => {
  it('应该被正确导出', () => {
    expect(escapeRegExp).toBeDefined()
    expect(typeof escapeRegExp).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = escapeRegExp('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => escapeRegExp(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => escapeRegExp({})).not.toThrow()
  })
})

describe('escapeHtml', () => {
  it('应该被正确导出', () => {
    expect(escapeHtml).toBeDefined()
    expect(typeof escapeHtml).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = escapeHtml('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => escapeHtml(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => escapeHtml({})).not.toThrow()
  })
})

describe('unescapeHtml', () => {
  it('应该被正确导出', () => {
    expect(unescapeHtml).toBeDefined()
    expect(typeof unescapeHtml).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = unescapeHtml('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => unescapeHtml(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => unescapeHtml({})).not.toThrow()
  })
})

describe('stripHtml', () => {
  it('应该被正确导出', () => {
    expect(stripHtml).toBeDefined()
    expect(typeof stripHtml).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = stripHtml('test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => stripHtml(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => stripHtml({})).not.toThrow()
  })
})

describe('pad', () => {
  it('应该被正确导出', () => {
    expect(pad).toBeDefined()
    expect(typeof pad).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = pad('test', 123, 'test', undefined)
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理边界值', () => {
    // 边界值: 0
    const result = pad('test', 0, 'test', undefined)
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => pad(null, null, null, null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => pad({}, {}, {}, {})).not.toThrow()
  })
})

describe('template', () => {
  it('应该被正确导出', () => {
    expect(template).toBeDefined()
    expect(typeof template).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = template('test', 'test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => template(null, null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => template({}, {})).not.toThrow()
  })
})

describe('byteLength', () => {
  it('应该被正确导出', () => {
    expect(byteLength).toBeDefined()
    expect(typeof byteLength).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = byteLength('test')
    expect(typeof result).toBe('number')
    expect(result).toBeGreaterThan(0)
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => byteLength(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => byteLength({})).not.toThrow()
  })
})

describe('truncateByBytes', () => {
  it('应该被正确导出', () => {
    expect(truncateByBytes).toBeDefined()
    expect(typeof truncateByBytes).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = truncateByBytes('test', 123, 'test')
    expect(typeof result).toBe('string')
    expect(result).toBeTruthy()
  })

  it('应该正确处理边界值', () => {
    // 边界值: 0
    const result = truncateByBytes('test', 0, 'test')
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => truncateByBytes(null, null, null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => truncateByBytes({}, {}, {})).not.toThrow()
  })
})
