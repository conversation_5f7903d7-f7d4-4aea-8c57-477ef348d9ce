/**
 * 树形结构工具函数
 */

interface TreeNode {
  [key: string]: unknown
  children?: TreeNode[]
}

interface TreeOptions {
  idKey?: string
  parentKey?: string
  childrenKey?: string
  rootValue?: unknown
}

interface TraverseOptions {
  childrenKey?: string
  order?: 'pre' | 'post'
}

interface FindOptions {
  childrenKey?: string
  deep?: boolean
}

/**
 * 数组转树形结构
 * @param list 扁平数组
 * @param options 配置选项
 */
export function fromList<T extends Record<string, unknown>>(
  list: T[],
  options: TreeOptions = {}
): T[] {
  const {
    idKey = 'id',
    parentKey = 'parentId',
    childrenKey = 'children',
    rootValue = null
  } = options

  const itemMap = new Map<unknown, T & { [key: string]: T[] }>()
  const result: T[] = []

  // 初始化映射
  list.forEach(item => {
    const enhancedItem = { ...item, [childrenKey]: [] } as T & { [key: string]: T[] }
    itemMap.set(item[idKey], enhancedItem)
  })

  // 构建树形结构
  list.forEach(item => {
    const enhancedItem = itemMap.get(item[idKey])!
    const parentId = item[parentKey]

    if (parentId === rootValue) {
      result.push(enhancedItem)
    } else {
      const parent = itemMap.get(parentId)
      if (parent) {
        parent[childrenKey].push(enhancedItem)
      }
    }
  })

  return result
}

/**
 * 遍历树形结构
 * @param tree 树形数据
 * @param callback 回调函数
 * @param options 遍历选项
 */
export function traverse<T extends Record<string, unknown>>(
  tree: T[],
  callback: (node: T, level: number, parent?: T) => void | boolean,
  options: TraverseOptions = {}
): void {
  const { childrenKey = 'children', order = 'pre' } = options

  function walk(nodes: T[], level = 0, parent?: T) {
    for (const node of nodes) {
      // 前序遍历：先处理当前节点
      if (order === 'pre') {
        const result = callback(node, level, parent)
        if (result === false) return // 提前退出
      }

      // 遍历子节点
      const children = node[childrenKey] as T[]
      if (Array.isArray(children) && children.length > 0) {
        walk(children, level + 1, node)
      }

      // 后序遍历：后处理当前节点
      if (order === 'post') {
        const result = callback(node, level, parent)
        if (result === false) return // 提前退出
      }
    }
  }

  walk(tree)
}

/**
 * 查找节点
 * @param tree 树形数据
 * @param predicate 查找条件
 * @param options 查找选项
 */
export function find<T extends Record<string, unknown>>(
  tree: T[],
  predicate: (node: T) => boolean,
  options: FindOptions = {}
): T[] {
  const { childrenKey = 'children', deep = true } = options
  const result: T[] = []

  function search(nodes: T[]) {
    for (const node of nodes) {
      if (predicate(node)) {
        result.push(node)
        if (!deep) break
      }

      const children = node[childrenKey] as T[]
      if (Array.isArray(children) && children.length > 0) {
        search(children)
      }
    }
  }

  search(tree)
  return result
}

/**
 * 移动节点
 * @param tree 树形数据
 * @param sourceId 源节点ID
 * @param targetId 目标节点ID
 * @param position 插入位置
 * @param options 选项
 */
export function move<T extends Record<string, unknown>>(
  tree: T[],
  sourceId: unknown,
  targetId: unknown,
  position: 'before' | 'after' | 'inside',
  options: { idKey?: string; childrenKey?: string } = {}
): T[] {
  const { idKey = 'id', childrenKey = 'children' } = options
  const newTree = JSON.parse(JSON.stringify(tree)) as T[]

  let sourceNode: T | null = null
  let sourceParent: T[] | null = null
  let sourceIndex = -1

  // 查找源节点
  function findSource(nodes: T[], parent?: T[]): boolean {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i][idKey] === sourceId) {
        sourceNode = nodes[i]
        sourceParent = parent || nodes
        sourceIndex = i
        return true
      }
      const children = nodes[i][childrenKey] as T[]
      if (Array.isArray(children) && findSource(children, children)) {
        return true
      }
    }
    return false
  }

  if (!findSource(newTree)) {
    return tree // 源节点不存在
  }

  // 从原位置移除
  sourceParent!.splice(sourceIndex, 1)

  // 查找目标位置并插入
  function insertAtTarget(nodes: T[]): boolean {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i][idKey] === targetId) {
        switch (position) {
          case 'before':
            nodes.splice(i, 0, sourceNode!)
            break
          case 'after':
            nodes.splice(i + 1, 0, sourceNode!)
            break
          case 'inside':
            const children = nodes[i][childrenKey] as T[]
            if (!Array.isArray(children)) {
              (nodes[i] as Record<string, unknown>)[childrenKey] = []
            }
            ;(nodes[i][childrenKey] as T[]).push(sourceNode!)
            break
        }
        return true
      }
      const children = nodes[i][childrenKey] as T[]
      if (Array.isArray(children) && insertAtTarget(children)) {
        return true
      }
    }
    return false
  }

  insertAtTarget(newTree)

  return newTree
}

/**
 * 树形结构扁平化
 * @param tree 树形数据
 * @param options 选项
 */
export function toList<T extends Record<string, unknown>>(
  tree: T[],
  options: { childrenKey?: string; level?: boolean } = {}
): T[] {
  const { childrenKey = 'children', level = false } = options
  const result: T[] = []

  traverse(tree, (node, nodeLevel) => {
    const { [childrenKey]: children, ...rest } = node
    const item = level ? { ...rest, level: nodeLevel } : rest
    result.push(item as T)
  }, { childrenKey })

  return result
}

/**
 * 获取节点路径
 * @param tree 树形数据
 * @param targetId 目标节点ID
 * @param options 选项
 */
export function getPath<T extends Record<string, unknown>>(
  tree: T[],
  targetId: unknown,
  options: { idKey?: string; childrenKey?: string } = {}
): T[] {
  const { idKey = 'id', childrenKey = 'children' } = options
  const path: T[] = []

  function findPath(nodes: T[]): boolean {
    for (const node of nodes) {
      path.push(node)

      if (node[idKey] === targetId) {
        return true
      }

      const children = node[childrenKey] as T[]
      if (Array.isArray(children) && findPath(children)) {
        return true
      }

      path.pop()
    }
    return false
  }

  findPath(tree)
  return path
}