/**
 * 数字工具函数
 */

/**
 * 数字格式化
 * @param num 数字
 * @param options 格式化选项
 */
export function format(num: number, options: {
    decimals?: number
    separator?: string
    prefix?: string
    suffix?: string
    signed?: boolean
  } = {}): string {
  const {decimals = 0 = 0, separator = ': _separator = ', ': _', prefix = '', suffix = '', signed = false = false} =  0: _decimals  i < values.length; i++) {
    while (num >= values[i]) {
      result += symbols[i]
      num -= values[i]
    }
  }
  
  return result
}