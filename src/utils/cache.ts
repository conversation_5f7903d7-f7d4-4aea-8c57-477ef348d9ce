/**
 * 缓存管理工具
 * 用于优化高频请求和批量数据的性能
 */

import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number
  hits?: number // 命中次数
  size?: number // 数据大小
}

// 缓存配置接口
interface CacheConfig {
  ttl?: number // 默认过期时间
  maxSize?: number // 最大缓存数量
  maxMemory?: number // 最大内存占用（字节）
  storage?: 'memory' | 'localStorage' | 'sessionStorage' // 存储类型
  prefix?: string // 缓存键前缀
  compress?: boolean // 是否压缩
  encrypt?: boolean // 是否加密
}

// 默认配置
const DEFAULT_CONFIG: CacheConfig = {
  ttl: 5 * 60 * 1000, // 5分钟
  maxSize: 100,
  maxMemory: 50 * 1024 * 1024, // 50MB
  storage: 'memory',
  prefix: 'hr_cache_',
  compress: false,
  encrypt: false
}

class CacheManager {
  private cache: Map<string, CacheItem<unknown>> = new Map()
  private timers: Map<string, number> = new Map()
  private config: CacheConfig
  private memoryUsage: number = 0
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0
  }

  constructor(config: CacheConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.setupStorageSync()
  }

  /**
   * 设置存储同步
   */
  private setupStorageSync(): void {
    if (this.config.storage !== 'memory') {
      // 从存储中恢复缓存
      this.loadFromStorage()

      // 监听存储变化
      window.addEventListener('storage', _e => {
        if (e.key?.startsWith(this.config.prefix!)) {
          this.syncFromStorage(e.key)
        }
      })
    }
  }

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttl 过期时间（毫秒）
   */
  set<T>(key: string, data: T, ttl: number = this.config.ttl!): void {
    // 检查内存限制
    const size = this.calculateSize(data)
    if (this.memoryUsage + size > this.config.maxMemory!) {
      this.evictLRU()
    }

    // 检查数量限制
    if (this.cache.size >= this.config.maxSize! && !this.cache.has(key)) {
      this.evictLRU()
    }

    // 清除旧的定时器
    this.clearTimer(key)

    // 准备缓存数据
    let cacheData = data
    if (this.config.compress) {
      cacheData = this.compress(data)
    }
    if (this.config.encrypt) {
      cacheData = this.encrypt(cacheData)
    }

    // 设置新缓存
    const item: CacheItem<T> = {
      data: cacheData as T,
      timestamp: Date.now(),
      ttl,
      hits: 0,
      size
    }

    this.cache.set(key, item)
    this.memoryUsage += size
    this.stats.sets++

    // 同步到存储
    if (this.config.storage !== 'memory') {
      this.saveToStorage(key, item)
    }

    // 设置过期定时器
    const timer = window.setTimeout(() => {
      this.delete(key)
    }, ttl)
    this.timers.set(key, timer)
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据或null
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) {
      this.stats.misses++
      return null
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key)
      this.stats.misses++
      return null
    }

    // 更新统计
    item.hits = (item.hits || 0) + 1
    this.stats.hits++

    // 解密和解压
    let data = item.data
    if (this.config.encrypt) {
      data = this.decrypt(data)
    }
    if (this.config.compress) {
      data = this.decompress(data)
    }

    return data as T
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  delete(key: string): void {
    const item = this.cache.get(key)
    if (item) {
      this.memoryUsage -= item.size || 0
      this.stats.deletes++
    }

    this.clearTimer(key)
    this.cache.delete(key)

    // 从存储中删除
    if (this.config.storage !== 'memory') {
      this.removeFromStorage(key)
    }
  }

  /**
   * 清除所有缓存
   */
  clear(): void {
    this.timers.forEach(timer => clearTimeout(timer))
    this.timers.clear()
    this.cache.clear()
    this.memoryUsage = 0

    // 清除存储
    if (this.config.storage !== 'memory') {
      this.clearStorage()
    }
  }

  /**
   * 检查缓存是否存在且有效
   * @param key 缓存键
   */
  has(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) return false

    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key)
      return false
    }

    return true
  }

  /**
   * 获取缓存大小
   */
  get size(): number {
    return this.cache.size
  }

  /**
   * 清除过期缓存
   */
  prune(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    this.cache.forEach((item, key) => {
      if (now - item.timestamp > item.ttl) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => this.delete(key))
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      ...this.stats,
      size: this.cache.size,
      memoryUsage: this.memoryUsage,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0
    }
  }

  /**
   * 根据模式删除缓存
   */
  deletePattern(pattern: string | RegExp): void {
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern
    const keysToDelete: string[] = []

    this.cache.forEach((_, key) => {
      if (regex.test(key)) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => this.delete(key))
  }

  /**
   * LRU淘汰
   */
  private evictLRU(): void {
    let oldestKey: string | null = null
    let oldestTime = Infinity
    let lowestHits = Infinity

    this.cache.forEach((item, key) => {
      const score = item.timestamp + (item.hits || 0) * 60000 // 每次命中延长1分钟
      if (score < oldestTime || (score === oldestTime && (item.hits || 0) < lowestHits)) {
        oldestTime = score
        oldestKey = key
        lowestHits = item.hits || 0
      }
    })

    if (oldestKey) {
      this.delete(oldestKey)
      this.stats.evictions++
    }
  }

  /**
   * 计算数据大小
   */

  private calculateSize(data: unknown): number {
    try {
      return JSON.stringify(data).length * 2 // 字符串在JS中占用2字节
    } catch {
      return 1024 // 默认1KB
    }
  }

  /**
   * 压缩数据（简单实现）
   */

  private compress(data: unknown): string {
    // 实际项目中应使用 lz-string 等库
    return JSON.stringify(data)
  }

  /**
   * 解压数据
   */

  private decompress(data: string): unknown {
    return JSON.parse(data)
  }

  /**
   * 加密数据（简单实现）
   */

  private encrypt(data: unknown): string {
    // 实际项目中应使用真正的加密算法
    return btoa(JSON.stringify(data))
  }

  /**
   * 解密数据
   */

  private decrypt(data: string): unknown {
    return JSON.parse(atob(data))
  }

  /**
   * 保存到存储
   */
  private saveToStorage(key: string, item: CacheItem<unknown>): void {
    try {
      const storage = window[this.config.storage as 'localStorage' | 'sessionStorage']
      storage.setItem(this.config.prefix + key, JSON.stringify(item))
    } catch (__e) {}
  }

  /**
   * 从存储加载
   */
  private loadFromStorage(): void {
    try {
      const storage = window[this.config.storage as 'localStorage' | 'sessionStorage']
      const keys = Object.keys(storage)

      keys.forEach(key => {
        if (key.startsWith(this.config.prefix!)) {
          const realKey = key.slice(this.config.prefix!.length)
          const item = JSON.parse(storage.getItem(key)!)

          // 检查是否过期
          if (Date.now() - item.timestamp <= item.ttl) {
            this.cache.set(realKey, item)
            this.memoryUsage += item.size || 0

            // 重新设置过期定时器
            const remainingTime = item.ttl - (Date.now() - item.timestamp)
            const timer = window.setTimeout(() => {
              this.delete(realKey)
            }, remainingTime)
            this.timers.set(realKey, timer)
          }
        }
      })
    } catch (__e) {}
  }

  /**
   * 从存储同步
   */
  private syncFromStorage(storageKey: string): void {
    const realKey = storageKey.slice(this.config.prefix!.length)
    try {
      const storage = window[this.config.storage as 'localStorage' | 'sessionStorage']
      const value = storage.getItem(storageKey)

      if (value) {
        const item = JSON.parse(value)
        this.cache.set(realKey, item)
      } else {
        this.cache.delete(realKey)
      }
    } catch (__e) {}
  }

  /**
   * 从存储移除
   */
  private removeFromStorage(key: string): void {
    try {
      const storage = window[this.config.storage as 'localStorage' | 'sessionStorage']
      storage.removeItem(this.config.prefix + key)
    } catch (__e) {}
  }

  /**
   * 清除存储
   */
  private clearStorage(): void {
    try {
      const storage = window[this.config.storage as 'localStorage' | 'sessionStorage']
      const keys = Object.keys(storage)

      keys.forEach(key => {
        if (key.startsWith(this.config.prefix!)) {
          storage.removeItem(key)
        }
      })
    } catch (__e) {}
  }

  private clearTimer(key: string): void {
    const timer = this.timers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(key)
    }
  }
}

// 创建不同类型的缓存实例
export const memoryCache = new CacheManager({
  storage: 'memory',
  ttl: 5 * 60 * 1000, // 5分钟
  maxSize: 200
})

export const localCache = new CacheManager({
  storage: 'localStorage',
  ttl: 24 * 60 * 60 * 1000, // 24小时
  prefix: 'hr_local_',
  compress: true
})

export const sessionCache = new CacheManager({
  storage: 'sessionStorage',
  ttl: 30 * 60 * 1000, // 30分钟
  prefix: 'hr_session_'
})

// 默认导出内存缓存
export const cache = memoryCache

/**
 * 缓存装饰器
 * @param options 缓存选项
 */
export function Cacheable(
  options: {
    ttl?: number

    key?: string | ((...args: unknown[]) => string)
    cache?: CacheManager

    condition?: (result: unknown) => boolean
  } = {}
) {
  return function (target: unknown, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const cacheInstance = options.cache || memoryCache

    descriptor.value = async function (...args: unknown[]) {
      // 生成缓存键
      const key =
        typeof options.key === 'function'
          ? options.key(...args)
          : options.key || `${target.constructor.name}_${propertyName}_${JSON.stringify(args)}`

      // 尝试从缓存获取
      const cached = cacheInstance.get(key)
      if (cached !== null) {
        return cached
      }

      // 执行原方法
      const result = await originalMethod.apply(this, args)

      // 检查是否应该缓存
      const shouldCache = options.condition ? options.condition(result) : true

      if (shouldCache && result !== null && result !== undefined) {
        cacheInstance.set(key, result, options.ttl)
      }

      return result
    }

    return descriptor
  }
}

/**
 * 批量请求合并器
 * 用于合并短时间内的多个相同请求
 */
export class RequestMerger {
  private pending: Map<string, Promise<unknown>> = new Map()

  private queue: Map<string, Set<(value: unknown) => void>> = new Map()
  private timers: Map<string, number> = new Map()

  /**
   * 合并请求
   * @param key 请求键
   * @param requestFn 请求函数
   * @param delay 延迟时间（毫秒）
   */
  async merge<T>(key: string, requestFn: () => Promise<T>, delay: number = 50): Promise<T> {
    // 如果有正在进行的请求，直接返回
    const pending = this.pending.get(key)
    if (pending) {
      return pending
    }

    // 创建新的 Promise
    const promise = new Promise<T>(__resolve => {
      // 添加到队列
      if (!this.queue.has(key)) {
        this.queue.set(key, new Set())
      }
      this.queue.get(key)!.add(resolve)

      // 清除旧定时器
      const oldTimer = this.timers.get(key)
      if (oldTimer) {
        clearTimeout(oldTimer)
      }

      // 设置新定时器
      const timer = window.setTimeout(async () => {
        this.timers.delete(key)
        const resolvers = this.queue.get(key)
        this.queue.delete(key)

        try {
          // 执行请求
          const pendingPromise = requestFn()
          this.pending.set(key, pendingPromise)
          const result = await pendingPromise

          // 通知所有等待者
          resolvers?.forEach(resolve => resolve(result))
        } catch (__error) {
          // 错误处理
          resolvers?.forEach(resolve => resolve(Promise.reject(error)))
        } finally {
          this.pending.delete(key)
        }
      }, delay)

      this.timers.set(key, timer)
    })

    return promise
  }

  /**
   * 清理所有待处理的请求
   */
  clear(): void {
    this.timers.forEach(timer => clearTimeout(timer))
    this.timers.clear()
    this.queue.clear()
    this.pending.clear()
  }
}

// 创建全局请求合并器实例
export const requestMerger = new RequestMerger()

/**
 * API缓存拦截器
 */
export function createCacheInterceptor(cacheManager: CacheManager = memoryCache) {
  return {
    // 请求拦截器
    request: async (config: AxiosRequestConfig) => {
      // 检查是否启用缓存
      if ((config as unknown).cache === false) {
        return config
      }

      // 只缓存 GET 请求
      if (config.method?.toUpperCase() !== 'GET') {
        return config
      }

      // 生成缓存键
      const cacheKey = generateCacheKey(config)

      // 尝试从缓存获取
      const cachedResponse = cacheManager.get(cacheKey)
      if (cachedResponse) {
        // 返回缓存的响应
        return Promise.reject({
          _CACHE_HIT__: true,
          data: cachedResponse,
          config
        })
      }

      // 保存缓存键到配置中
      ;(config as unknown).__cacheKey__ = cacheKey

      return config
    },

    // 响应拦截器
    response: async (response: AxiosResponse) => {
      const config = response.config as unknown
      const cacheKey = config.__cacheKey__

      // 检查是否需要缓存
      if (cacheKey && response.status === 200) {
        // 获取缓存时间
        const cacheTTL = config.cacheTTL || 5 * 60 * 1000 // 默认5分钟

        // 缓存响应
        cacheManager.set(cacheKey, response.data, cacheTTL)
      }

      return response
    },

    // 错误拦截器

    error: async (error: unknown) => {
      // 处理缓存命中的情况
      if (error?.__CACHE_HIT__) {
        return {
          data: error.data,
          status: 200,
          statusText: 'OK (from cache)',
          headers: {},
          config: error.config
        }
      }

      throw error
    }
  }
}

/**
 * 生成缓存键
 */
function generateCacheKey(config: AxiosRequestConfig): string {
  const { method = 'GET', url, params, data } = config
  const keyParts = [
    method.toUpperCase(),
    url,
    params ? JSON.stringify(params) : '',
    data ? JSON.stringify(data) : ''
  ]
  return keyParts.filter(Boolean).join('_')
}

/**
 * 缓存失效策略
 */
export class CacheInvalidator {
  private patterns: Map<string, RegExp> = new Map()

  constructor() {
    this.setupPatterns()
  }

  private setupPatterns(): void {
    // 设置常见的失效模式
    this.patterns.set('employee', /employee|staff/i)
    this.patterns.set('department', /department|organization|org/i)
    this.patterns.set('salary', /salary|payroll|wage/i)
    this.patterns.set('attendance', /attendance|leave|vacation/i)
    this.patterns.set('performance', /performance|review|evaluation/i)
  }

  /**
   * 根据实体类型失效缓存
   */
  invalidateByEntity(entity: string, operation: 'create' | 'update' | 'delete'): void {
    const pattern = this.patterns.get(entity)
    if (pattern) {
      memoryCache.deletePattern(pattern)
      localCache.deletePattern(pattern)
      sessionCache.deletePattern(pattern)

      ElMessage.info(`${entity}相关缓存已更新`)
    }
  }

  /**
   * 根据模式失效缓存
   */
  invalidateByPattern(pattern: string | RegExp): void {
    memoryCache.deletePattern(pattern)
    localCache.deletePattern(pattern)
    sessionCache.deletePattern(pattern)
  }

  /**
   * 失效指定键
   */
  invalidateKey(key: string): void {
    memoryCache.delete(key)
    localCache.delete(key)
    sessionCache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  invalidateAll(): void {
    memoryCache.clear()
    localCache.clear()
    sessionCache.clear()
    ElMessage.info('所有缓存已清空')
  }
}

// 创建缓存失效器实例
export const cacheInvalidator = new CacheInvalidator()

/**
 * LRU缓存
 * 用于限制缓存大小的场景
 */
export class LRUCache<T> {
  private maxSize: number
  private cache: Map<string, T> = new Map()

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize
  }

  get(key: string): T | undefined {
    const value = this.cache.get(key)
    if (value !== undefined) {
      // 更新访问顺序
      this.cache.delete(key)
      this.cache.set(key, value)
    }
    return value
  }

  set(key: string, value: T): void {
    // 如果已存在，先删除
    if (this.cache.has(key)) {
      this.cache.delete(key)
    } else if (this.cache.size >= this.maxSize) {
      // 删除最老的项
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    this.cache.set(key, value)
  }

  has(key: string): boolean {
    return this.cache.has(key)
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  get size(): number {
    return this.cache.size
  }
}

/**
 * 导出所有类型和接口
 */
export type { CacheConfig, CacheItem }
export { CacheManager }
