/**
 * @name API请求缓存增强工具
 * @description 针对API请求的智能缓存策略，集成现有缓存系统
 */

import { cache, memoryCache, sessionCache, cacheInvalidator, type CacheManager } from './cache'
import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 扩展的请求配置接口
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  cache?: boolean
  cacheTTL?: number
}

// 缓存命中错误类型
interface CacheHitError {
  __CACHE_HIT__: boolean
  data: unknown
  config: AxiosRequestConfig
  status: number
  statusText: string
  headers: Record<string, unknown>
}

// 预热请求类型
interface PreloadRequest {
  url: string
  params?: Record<string, unknown>
}

export interface RequestCacheConfig {
  /** 缓存实例 */
  cacheInstance?: CacheManager
  /** 默认TTL（毫秒） */
  defaultTTL?: number
  /** 是否启用缓存 */
  enabled?: boolean
  /** 缓存策略 */
  strategy?: 'memory' | 'session' | 'auto'
  /** 是否显示缓存命中提示 */
  showCacheHit?: boolean
  /** 自定义缓存键生成器 */
  keyGenerator?: (config: AxiosRequestConfig) => string
}

/**
 * API请求缓存管理器
 */
export class ApiRequestCache {
  private config: Required<RequestCacheConfig>
  private cacheStats = {
    hits: 0,
    misses: 0,
    requests: 0,
    cacheable: 0
  }

  constructor(config: RequestCacheConfig = {}) {
    this.config = {
      cacheInstance: config.cacheInstance || this.selectCacheInstance(config.strategy),
      defaultTTL: config.defaultTTL || 5 * 60 * 1000, // 5分钟
      enabled: config.enabled !== false,
      strategy: config.strategy || 'auto',
      showCacheHit: config.showCacheHit !== false,
      keyGenerator: config.keyGenerator || this.defaultKeyGenerator.bind(this)
    }
  }

  /**
   * 选择缓存实例
   */
  private selectCacheInstance(strategy?: string): CacheManager {
    switch (strategy) {
      case 'memory':
        return memoryCache
      case 'session':
        return sessionCache
      case 'auto':
      default:
        return cache // 默认内存缓存
    }
  }

  /**
   * 默认缓存键生成器
   */
  private defaultKeyGenerator(config: AxiosRequestConfig): string {
    const { method = 'GET', url = '', params, data } = config
    const parts = [
      method.toUpperCase(),
      url,
      params ? this.hashObject(params) : '',
      data ? this.hashObject(data) : ''
    ]
    return `api_${parts.filter(Boolean).join('_')}`
  }

  /**
   * 对象哈希（简单实现）
   */
  private hashObject(obj: Record<string, unknown>): string {
    try {
      const str = JSON.stringify(obj, Object.keys(obj).sort())
      let hash = 0
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = (hash << 5) - hash + char
        hash = hash & hash // 转换为32位整数
      }
      return Math.abs(hash).toString(36)
    } catch {
      return Date.now().toString(36)
    }
  }

  /**
   * 检查请求是否可缓存
   */
  private isCacheable(config: AxiosRequestConfig): boolean {
    // 检查是否启用缓存
    if (!this.config.enabled) return false

    // 检查请求配置中的缓存设置
    const requestConfig = config as ExtendedAxiosRequestConfig
    if (requestConfig.cache === false) return false

    // 只缓存GET请求
    if (config.method?.toUpperCase() !== 'GET') return false

    // 检查URL是否包含排除模式
    const excludePatterns = [
      '/auth/',
      '/login',
      '/logout',
      '/verify',
      '/upload',
      '/download',
      '/export',
      '/realtime',
      '/websocket',
      '/sse'
    ]

    const url = config.url || ''
    if (excludePatterns.some(pattern => url.includes(pattern))) {
      return false
    }

    return true
  }

  /**
   * 获取缓存TTL
   */
  private getCacheTTL(config: AxiosRequestConfig): number {
    const requestConfig = config as ExtendedAxiosRequestConfig

    // 优先使用请求级别的TTL
    if (requestConfig.cacheTTL) {
      return requestConfig.cacheTTL
    }

    // 根据URL类型设置不同的TTL
    const url = config.url || ''

    // 静态配置数据 - 长缓存
    if (url.includes('/config/') || url.includes('/dict/')) {
      return 30 * 60 * 1000 // 30分钟
    }

    // 组织架构数据 - 中等缓存
    if (url.includes('/organization/') || url.includes('/department/')) {
      return 15 * 60 * 1000 // 15分钟
    }

    // 员工列表数据 - 短缓存
    if (url.includes('/employee/') || url.includes('/staff/')) {
      return 3 * 60 * 1000 // 3分钟
    }

    // 统计数据 - 短缓存
    if (url.includes('/statistics/') || url.includes('/dashboard/')) {
      return 2 * 60 * 1000 // 2分钟
    }

    return this.config.defaultTTL
  }

  /**
   * 获取缓存数据
   */
  async get<T = unknown>(config: AxiosRequestConfig): Promise<T | null> {
    this.cacheStats.requests++

    if (!this.isCacheable(config)) {
      return null
    }

    this.cacheStats.cacheable++

    const cacheKey = this.config.keyGenerator(config)
    const cachedData = this.config.cacheInstance.get<T>(cacheKey)

    if (cachedData !== null) {
      this.cacheStats.hits++

      if (this.config.showCacheHit && process.env.NODE_ENV === 'development') {
        console.log(`[Cache Hit] ${config.method?.toUpperCase()} ${config.url}`)
      }

      return cachedData
    }

    this.cacheStats.misses++
    return null
  }

  /**
   * 设置缓存数据
   */
  async set<T = unknown>(config: AxiosRequestConfig, data: T): Promise<void> {
    if (!this.isCacheable(config)) {
      return
    }

    const cacheKey = this.config.keyGenerator(config)
    const ttl = this.getCacheTTL(config)

    this.config.cacheInstance.set(cacheKey, data, ttl)

    if (process.env.NODE_ENV === 'development') {
      console.log(`[Cache Set] ${config.method?.toUpperCase()} ${config.url} (TTL: ${ttl}ms)`)
    }
  }

  /**
   * 失效相关缓存
   */
  invalidateRelated(url: string, method: string = 'POST'): void {
    // 根据操作类型失效相关缓存
    if (method === 'POST' || method === 'PUT' || method === 'PATCH') {
      // 创建和更新操作
      this.invalidateByUrlPattern(url)
    } else if (method === 'DELETE') {
      // 删除操作 - 失效更多相关缓存
      this.invalidateByUrlPattern(url, true)
    }
  }

  /**
   * 根据URL模式失效缓存
   */
  private invalidateByUrlPattern(url: string, extensive = false): void {
    const patterns: string[] = []

    // 提取基础路径
    if (url.includes('/employee/')) {
      patterns.push('api_GET_.*employee.*')
      if (extensive) {
        patterns.push('api_GET_.*department.*') // 员工删除可能影响部门统计
        patterns.push('api_GET_.*statistics.*') // 影响统计数据
      }
    }

    if (url.includes('/organization/')) {
      patterns.push('api_GET_.*organization.*')
      patterns.push('api_GET_.*department.*')
      if (extensive) {
        patterns.push('api_GET_.*employee.*') // 组织变更可能影响员工归属
      }
    }

    if (url.includes('/department/')) {
      patterns.push('api_GET_.*department.*')
      patterns.push('api_GET_.*organization.*')
    }

    // 执行缓存失效
    patterns.forEach(pattern => {
      this.config.cacheInstance.deletePattern(new RegExp(pattern))
    })

    if (patterns.length > 0 && process.env.NODE_ENV === 'development') {
      console.log(`[Cache Invalidate] Patterns: ${patterns.join(', ')}`)
    }
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    const instanceStats = this.config.cacheInstance.getStats()
    return {
      ...this.cacheStats,
      instance: instanceStats,
      hitRate: (this.cacheStats.hits / Math.max(this.cacheStats.cacheable, 1)) * 100,
      cacheableRate: (this.cacheStats.cacheable / Math.max(this.cacheStats.requests, 1)) * 100
    }
  }

  /**
   * 重置统计
   */
  resetStats(): void {
    this.cacheStats = {
      hits: 0,
      misses: 0,
      requests: 0,
      cacheable: 0
    }
  }

  /**
   * 预热缓存
   */
  async preload(requests: PreloadRequest[]): Promise<void> {
    console.log('[Cache Preload] Starting cache preload...')

    // 这里需要实际的HTTP客户端来执行请求
    // 在实际使用中，应该注入request实例
    for (const req of requests) {
      try {
        // 模拟预热逻辑
        const cacheKey = this.config.keyGenerator({
          method: 'GET',
          url: req.url,
          params: req.params
        })

        // 检查是否已缓存
        if (!this.config.cacheInstance.has(cacheKey)) {
          console.log(`[Cache Preload] Would preload: ${req.url}`)
          // 实际项目中这里应该发起真实请求
        }
      } catch (error) {
        console.warn(`[Cache Preload] Failed to preload ${req.url}:`, error)
      }
    }
  }

  /**
   * 获取缓存大小信息
   */
  getSize(): { count: number; memory?: number } {
    const stats = this.config.cacheInstance.getStats()
    return {
      count: stats.size,
      memory: stats.memoryUsage
    }
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    this.config.cacheInstance.prune()
    console.log('[Cache Cleanup] Expired cache entries removed')
  }

  /**
   * 根据正则模式删除缓存
   */
  deleteByPattern(pattern: RegExp): void {
    this.config.cacheInstance.deletePattern(pattern)
    console.log(`[Cache Delete] Pattern: ${pattern}`)
  }
}

/**
 * 创建请求缓存拦截器
 */
export function createRequestCacheInterceptor(cacheConfig?: RequestCacheConfig) {
  const requestCache = new ApiRequestCache(cacheConfig)

  return {
    cache: requestCache,

    // 请求拦截器
    request: async (config: AxiosRequestConfig) => {
      // 尝试从缓存获取数据
      const cachedData = await requestCache.get(config)

      if (cachedData !== null) {
        // 返回缓存数据（通过Promise.reject触发错误拦截器）
        return Promise.reject({
          __CACHE_HIT__: true,
          data: cachedData,
          config,
          status: 200,
          statusText: 'OK (from cache)',
          headers: {}
        })
      }

      return config
    },

    // 响应拦截器
    response: async (response: AxiosResponse) => {
      // 缓存响应数据
      if (response.status === 200 && response.data) {
        await requestCache.set(response.config, response.data)
      }

      return response
    },

    // 错误拦截器
    error: async (error: CacheHitError | Error) => {
      // 处理缓存命中
      if ('__CACHE_HIT__' in error && error.__CACHE_HIT__) {
        return {
          data: error.data,
          status: error.status,
          statusText: error.statusText,
          headers: error.headers,
          config: error.config
        }
      }

      // 处理其他错误时的缓存失效
      if ('config' in error && error.config) {
        const { method, url } = error.config
        if (method && url && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method.toUpperCase())) {
          requestCache.invalidateRelated(url, method.toUpperCase())
        }
      }

      throw error
    }
  }
}

/**
 * 缓存注解装饰器
 */
export function CacheApi(
  options: {
    ttl?: number
    key?: string | ((...args: unknown[]) => string)
    enabled?: boolean
  } = {}
) {
  return function (
    target: Record<string, unknown>,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value
    const requestCache = new ApiRequestCache({
      defaultTTL: options.ttl,
      enabled: options.enabled
    })

    descriptor.value = async function (...args: unknown[]) {
      // 生成缓存键
      const cacheKey =
        typeof options.key === 'function'
          ? options.key(...args)
          : options.key || `${target.constructor.name}_${propertyName}_${JSON.stringify(args)}`

      // 尝试获取缓存
      const cached = requestCache.config.cacheInstance.get(cacheKey)
      if (cached !== null) {
        return cached
      }

      // 执行原方法
      const result = await originalMethod.apply(this, args)

      // 缓存结果
      if (result !== null && result !== undefined) {
        requestCache.config.cacheInstance.set(cacheKey, result, options.ttl)
      }

      return result
    }

    return descriptor
  }
}

// 创建默认的请求缓存实例
export const defaultRequestCache = new ApiRequestCache()

// 导出常用的缓存配置
export const CacheConfigs = {
  // 快速缓存（1分钟）
  FAST: { defaultTTL: 60 * 1000 },

  // 标准缓存（5分钟）
  STANDARD: { defaultTTL: 5 * 60 * 1000 },

  // 慢速缓存（15分钟）
  SLOW: { defaultTTL: 15 * 60 * 1000 },

  // 静态数据缓存（30分钟）
  STATIC: { defaultTTL: 30 * 60 * 1000 },

  // 仅内存缓存
  MEMORY_ONLY: { strategy: 'memory' as const },

  // 会话缓存
  SESSION_ONLY: { strategy: 'session' as const }
}

export default ApiRequestCache
