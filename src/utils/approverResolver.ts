import type { User, Department, Position } from '@/types/user'

interface ApprovalRule {
  id: string
  name: string
  type: 'supervisor' | 'dept_manager' | 'hr_manager' | 'custom' | 'role_based'
  level?: number
  conditions?: ApprovalCondition[]
  fallbackRule?: string
}

interface ApprovalCondition {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'contains'
   
  value: unknown
}

interface ApprovalContext {
  applicant: User
  processKey: string
  processVariables: Record<string, unknown>
  department?: Department
  position?: Position
}

interface ApprovalResult {
  approvers: User[]
  rule: ApprovalRule
  level: number
  reason: string
}

/**
 * CLEAN-DATA-009: 获取上级审批人功能
 * 根据组织架构和审批规则自动解析审批人
 */
export class ApproverResolver {
  private rules: Map<string, ApprovalRule> = new Map()
  private organizationCache: Map<string, unknown> = new Map()
  
  constructor() {
    this.initializeDefaultRules()
  }
  
  // 初始化默认审批规则
  private initializeDefaultRules() {
    const defaultRules: ApprovalRule[] = [
      {
        id: 'direct_supervisor',
        name: '直接上级',
        type: 'supervisor',
        level: 1
      },
      {
        id: 'dept_manager',
        name: '部门经理',
        type: 'dept_manager',
        level: 1
      },
      {
        id: 'hr_manager',
        name: '人事经理',
        type: 'hr_manager',
        level: 1
      },
      {
        id: 'multi_level_supervisor',
        name: '多级上级审批',
        type: 'supervisor',
        level: 2
      },
      {
        id: 'amount_based',
        name: '基于金额的审批',
        type: 'custom',
        conditions: [
          { field: 'amount', operator: 'lte', value: 1000 },
          { field: 'processKey', operator: 'eq', value: 'expense_approval' }
        ]
      }
    ]
    
    defaultRules.forEach(rule => {
      this.rules.set(rule.id, rule)
    })
  }
  
  // 获取审批人
  async getApprovers(context: ApprovalContext, ruleId?: string): Promise<ApprovalResult> {
    try {
      // 如果没有指定规则，根据流程类型选择默认规则
      const rule = ruleId ? this.rules.get(ruleId) : this.selectDefaultRule(context)
      
      if (!rule) {
        throw new Error('未找到适用的审批规则')
      }
      
      // 检查规则条件
      if (rule.conditions && !this.evaluateConditions(rule.conditions, context)) {
        // 如果有后备规则，尝试使用后备规则
        if (rule.fallbackRule) {
          return this.getApprovers(context, rule.fallbackRule)
        }
        throw new Error('当前上下文不满足审批规则条件')
      }
      
      let approvers: User[] = []
      
      switch (rule.type) {
        case 'supervisor':
          approvers = await this.getSupervisorApprovers(context, rule.level || 1)
          break
        case 'dept_manager':
          approvers = await this.getDepartmentManagerApprovers(context)
          break
        case 'hr_manager':
          approvers = await this.getHRManagerApprovers(context)
          break
        case 'role_based':
          approvers = await this.getRoleBasedApprovers(context, rule)
          break
        case 'custom':
          approvers = await this.getCustomApprovers(context, rule)
          break
      }
      
      if (approvers.length === 0) {
        throw new Error('未找到合适的审批人')
      }
      
      return {
        approvers,
        rule,
        level: rule.level || 1,
        reason: this.generateApprovalReason(rule, context)
      }
      
    } catch (__error) {
      throw error
    }
  }
  
  // 获取上级审批人
  private async getSupervisorApprovers(context: ApprovalContext, level: number): Promise<User[]> {
    const approvers: User[] = []
    let currentUser = context.applicant
    
    // 逐级向上查找指定层级的上级
    for (let i = 0; i < level; i++) {
      const supervisor = await this.getDirectSupervisor(currentUser)
      if (!supervisor) {
        // 如果找不到上级，查找部门负责人
        const deptManager = await this.getDepartmentManager(currentUser.departmentId)
        if (deptManager && !approvers.some(a => a.id === deptManager.id)) {
          approvers.push(deptManager)
        }
        break
      }
      
      if (!approvers.some(a => a.id === supervisor.id)) {
        approvers.push(supervisor)
      }
      
      currentUser = supervisor
    }
    
    return approvers
  }
  
  // 获取部门经理审批人
  private async getDepartmentManagerApprovers(context: ApprovalContext): Promise<User[]> {
    const manager = await this.getDepartmentManager(context.applicant.departmentId)
    return manager ? [manager] : []
  }
  
  // 获取人事经理审批人
  private async getHRManagerApprovers(context: ApprovalContext): Promise<User[]> {
    // 查找人事部门的经理
    const hrDepartment = await this.getHRDepartment()
    if (hrDepartment) {
      const hrManager = await this.getDepartmentManager(hrDepartment.id)
      return hrManager ? [hrManager] : []
    }
    return []
  }
  
  // 获取基于角色的审批人
  private async getRoleBasedApprovers(context: ApprovalContext, rule: ApprovalRule): Promise<User[]> {
    // 根据规则配置查找特定角色的用户
    const roleConfig = rule.conditions?.find(c => c.field === 'role')
    if (roleConfig) {
      return this.getUsersByRole(roleConfig.value)
    }
    return []
  }
  
  // 获取自定义审批人
  private async getCustomApprovers(context: ApprovalContext, rule: ApprovalRule): Promise<User[]> {
    // 根据自定义规则逻辑获取审批人
    // 这里可以实现复杂的业务逻辑
    
    // 示例：基于金额的审批逻辑
    const amount = context.processVariables.amount
    if (amount > 10000) {
      // 大额审批需要财务经理和总经理
      const financeManager = await this.getFinanceManager()
      const generalManager = await this.getGeneralManager()
      return [financeManager, generalManager].filter(Boolean) as User[]
    } else if (amount > 5000) {
      // 中额审批需要部门经理和财务经理
      const deptManager = await this.getDepartmentManager(context.applicant.departmentId)
      const financeManager = await this.getFinanceManager()
      return [deptManager, financeManager].filter(Boolean) as User[]
    } else {
      // 小额审批只需要直接上级
      return this.getSupervisorApprovers(context, 1)
    }
  }
  
  // 获取直接上级
  private async getDirectSupervisor(user: User): Promise<User | null> {
    try {
      // 从缓存获取
      const cacheKey = `supervisor_${user.id}`
      if (this.organizationCache.has(cacheKey)) {
        return this.organizationCache.get(cacheKey)
      }
      
      // 实际项目中这里应该调用API
      
      // 模拟数据
      const supervisor = this.mockGetSupervisor(user)
      
      // 缓存结果
      this.organizationCache.set(cacheKey, supervisor)
      
      return supervisor
    } catch (__error) {
      return null
    }
  }
  
  // 获取部门经理
  private async getDepartmentManager(departmentId: string): Promise<User | null> {
    try {
      const cacheKey = `dept_manager_${departmentId}`
      if (this.organizationCache.has(cacheKey)) {
        return this.organizationCache.get(cacheKey)
      }
      
      // 实际项目中调用API
      
      // 模拟数据
      const manager = this.mockGetDepartmentManager(departmentId)
      
      this.organizationCache.set(cacheKey, manager)
      return manager
    } catch (__error) {
      return null
    }
  }
  
  // 获取人事部门
  private async getHRDepartment(): Promise<Department | null> {
    const cacheKey = 'hr_department'
    if (this.organizationCache.has(cacheKey)) {
      return this.organizationCache.get(cacheKey)
    }
    
    // 模拟数据
    const hrDept: Department = {
      id: 'dept_hr',
      name: '人事部',
      parentId: null,
      level: 1,
      sort: 1,
      manager: 'user_hr_manager',
      description: '人力资源管理部门'
    }
    
    this.organizationCache.set(cacheKey, hrDept)
    return hrDept
  }
  
  // 获取财务经理
  private async getFinanceManager(): Promise<User | null> {
    const cacheKey = 'finance_manager'
    if (this.organizationCache.has(cacheKey)) {
      return this.organizationCache.get(cacheKey)
    }
    
    // 模拟数据
    const manager: User = {
      id: 'user_finance_manager',
      username: 'finance_mgr',
      name: '财务经理',
      email: '<EMAIL>',
      phone: '13800000003',
      departmentId: 'dept_finance',
      positionId: 'pos_finance_manager',
      roles: ['finance_manager'],
      status: 'active'
    }
    
    this.organizationCache.set(cacheKey, manager)
    return manager
  }
  
  // 获取总经理
  private async getGeneralManager(): Promise<User | null> {
    const cacheKey = 'general_manager'
    if (this.organizationCache.has(cacheKey)) {
      return this.organizationCache.get(cacheKey)
    }
    
    // 模拟数据
    const manager: User = {
      id: 'user_general_manager',
      username: 'general_mgr',
      name: '总经理',
      email: '<EMAIL>',
      phone: '13800000001',
      departmentId: 'dept_management',
      positionId: 'pos_general_manager',
      roles: ['general_manager'],
      status: 'active'
    }
    
    this.organizationCache.set(cacheKey, manager)
    return manager
  }
  
  // 根据角色获取用户
  private async getUsersByRole(role: string): Promise<User[]> {
    // 实际项目中调用API
    
    // 模拟数据
    return []
  }
  
  // 选择默认规则
  private selectDefaultRule(context: ApprovalContext): ApprovalRule | undefined {
    // 根据流程类型选择默认规则
    const ruleMap: Record<string, string> = {
      'leave_application': 'direct_supervisor',
      'expense_approval': 'amount_based',
      'recruitment': 'hr_manager',
      'transfer_application': 'multi_level_supervisor'
    }
    
    const ruleId = ruleMap[context.processKey] || 'direct_supervisor'
    return this.rules.get(ruleId)
  }
  
  // 评估规则条件
  private evaluateConditions(conditions: ApprovalCondition[], context: ApprovalContext): boolean {
    return conditions.every(condition => {
      const value = this.getContextValue(condition.field, context)
      return this.evaluateCondition(value, condition.operator, condition.value)
    })
  }
  
  // 获取上下文值
   
  private getContextValue(field: string, context: ApprovalContext): unknown {
    if (field.startsWith('applicant.')) {
      const prop = field.substring(10)
      return (context.applicant as unknown)[prop]
    }
    
    if (field.startsWith('variables.')) {
      const prop = field.substring(10)
      return context.processVariables[prop]
    }
    
    switch (field) {
      case 'processKey':
        return context.processKey
      case 'amount':
        return context.processVariables.amount
      default:
        return context.processVariables[field]
    }
  }
  
  // 评估单个条件
   
  private evaluateCondition(value: unknown, operator: string, expectedValue: unknown): boolean {
    switch (operator) {
      case 'eq':
        return value === expectedValue
      case 'ne':
        return value !== expectedValue
      case 'gt':
        return value > expectedValue
      case 'lt':
        return value < expectedValue
      case 'gte':
        return value >= expectedValue
      case 'lte':
        return value <= expectedValue
      case 'in':
        return Array.isArray(expectedValue) && expectedValue.includes(value)
      case 'contains':
        return typeof value === 'string' && value.includes(expectedValue)
      default:
        return false
    }
  }
  
  // 生成审批原因
  private generateApprovalReason(rule: ApprovalRule, context: ApprovalContext): string {
    switch (rule.type) {
      case 'supervisor':
        return `根据组织架构，需要${rule.level}级上级审批`
      case 'dept_manager':
        return '根据部门管理制度，需要部门经理审批'
      case 'hr_manager':
        return '根据人事管理制度，需要人事经理审批'
      case 'custom':
        const amount = context.processVariables.amount
        if (amount) {
          return `根据金额审批制度，${amount}元需要相应级别审批`
        }
        return '根据自定义规则确定审批人'
      default:
        return '根据审批规则确定审批人'
    }
  }
  
  // 模拟获取上级
  private mockGetSupervisor(user: User): User | null {
    // 模拟组织架构数据
    const supervisorMap: Record<string, User> = {
      'user_001': {
        id: 'user_manager_001',
        username: 'manager1',
        name: '部门经理',
        email: '<EMAIL>',
        phone: '13800000002',
        departmentId: user.departmentId,
        positionId: 'pos_manager',
        roles: ['manager'],
        status: 'active'
      }
    }
    
    return supervisorMap[user.id] || null
  }
  
  // 模拟获取部门经理
  private mockGetDepartmentManager(departmentId: string): User | null {
    const managerMap: Record<string, User> = {
      'dept_tech': {
        id: 'user_tech_manager',
        username: 'tech_mgr',
        name: '技术部经理',
        email: '<EMAIL>',
        phone: '13800000004',
        departmentId: departmentId,
        positionId: 'pos_dept_manager',
        roles: ['dept_manager'],
        status: 'active'
  },
      'dept_hr': {
        id: 'user_hr_manager',
        username: 'hr_mgr',
        name: '人事部经理',
        email: '<EMAIL>',
        phone: '13800000005',
        departmentId: departmentId,
        positionId: 'pos_dept_manager',
        roles: ['hr_manager'],
        status: 'active'
      }
    }
    
    return managerMap[departmentId] || null
  }
  
  // 添加自定义规则
  addRule(rule: ApprovalRule) {
    this.rules.set(rule.id, rule)
  }
  
  // 删除规则
  removeRule(ruleId: string) {
    this.rules.delete(ruleId)
  }
  
  // 获取所有规则
  getAllRules(): ApprovalRule[] {
    return Array.from(this.rules.values())
  }
  
  // 清除缓存
  clearCache() {
    this.organizationCache.clear()
  }
}

// 全局审批人解析器实例
export const approverResolver = new ApproverResolver()

// 便捷函数
export async function getProcessApprovers(applicant: User, processKey: string, processVariables: Record<string, unknown> = {}, ruleId?: string): Promise<ApprovalResult> {
  const context: ApprovalContext = {
    applicant,
    processKey,
    processVariables
  }
  
  return approverResolver.getApprovers(context, ruleId)
}