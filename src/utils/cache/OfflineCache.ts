/**
 * 离线缓存工具类
 * 提供Service Worker、IndexedDB、LocalStorage等多级缓存策略
 */

import { ref } from 'vue'

// 缓存策略
export type CacheStrategy =
  | 'cache-first' // 缓存优先
  | 'network-first' // 网络优先
  | 'cache-only' // 仅缓存
  | 'network-only' // 仅网络
  | 'stale-while-revalidate' // 返回缓存同时更新

// 缓存配置
export interface CacheConfig {
  name: string
  version: number
  stores?: string[]
  maxAge?: number
  maxSize?: number
  enableServiceWorker?: boolean
  enableIndexedDB?: boolean
  enableLocalStorage?: boolean
}

// 缓存项
export interface CacheItem<T = unknown> {
  key: string
  value: T
  timestamp: number
  expires?: number
  size?: number
  tags?: string[]
}

// 缓存统计
export interface CacheStats {
  totalSize: number
  itemCount: number
  hitRate: number
  stores: {
    [key: string]: {
      size: number
      count: number
    }
  }
}

export class OfflineCache {
  private static instance: OfflineCache
  private db: IDBDatabase | null = null
  private swRegistration: ServiceWorkerRegistration | null = null
  private config: CacheConfig
  private stats = {
    hits: 0,
    misses: 0
  }

  private defaultConfig: CacheConfig = {
    name: 'hr-visualization-cache',
    version: 1,
    stores: ['api', 'charts', 'images', 'documents'],
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
    maxSize: 50 * 1024 * 1024, // 50MB
    enableServiceWorker: true,
    enableIndexedDB: true,
    enableLocalStorage: true
  }

  private constructor(config: CacheConfig) {
    this.config = { ...this.defaultConfig, ...config }
    this.init()
  }

  static getInstance(config?: Partial<CacheConfig>): OfflineCache {
    if (!OfflineCache.instance) {
      OfflineCache.instance = new OfflineCache(config as CacheConfig)
    }
    return OfflineCache.instance
  }

  /**
   * 初始化缓存系统
   */
  private async init() {
    // 初始化IndexedDB
    if (this.config.enableIndexedDB) {
      await this.initIndexedDB()
    }

    // 注册Service Worker
    if (this.config.enableServiceWorker && 'serviceWorker' in navigator) {
      await this.registerServiceWorker()
    }

    // 清理过期缓存
    this.scheduleCleanup()
  }

  /**
   * 初始化IndexedDB
   */
  private async initIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.config.name, this.config.version)

      request.onerror = () => reject(request.error)
      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }

      request.onupgradeneeded = _event => {
        const db = (event.target as IDBOpenDBRequest).result

        // 创建对象存储
        this.config.stores?.forEach(storeName => {
          if (!db.objectStoreNames.contains(storeName)) {
            const store = db.createObjectStore(storeName, { keyPath: 'key' })
            store.createIndex('timestamp', 'timestamp')
            store.createIndex('tags', 'tags', { multiEntry: true })
          }
        })
      }
    })
  }

  /**
   * 注册Service Worker
   */
  private async registerServiceWorker(): Promise<void> {
    try {
      this.swRegistration = await navigator.serviceWorker.register('/sw.js')

      // 监听更新
      this.swRegistration.addEventListener('updatefound', () => {
        const newWorker = this.swRegistration!.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'activated') {
              this.notifyUpdate()
            }
          })
        }
      })
    } catch (__error) {
      }
  }

  /**
   * 设置缓存
   */
  async set<T>(
    key: string,
    value: T,
    options?: {
      store?: string
      expires?: number
      tags?: string[]
      strategy?: CacheStrategy
    }
  ): Promise<void> {
    const store = options?.store || 'api'
    const item: CacheItem<T> = {
      key,
      value,
      timestamp: Date.now(),
      expires: options?.expires,
      size: this.calculateSize(value),
      tags: options?.tags
    }

    // 根据策略选择存储位置
    const promises: Promise<void>[] = []

    // IndexedDB存储
    if (this.config.enableIndexedDB && this.db) {
      promises.push(this.setIndexedDB(store, item))
    }

    // LocalStorage存储（小数据）
    if (this.config.enableLocalStorage && item.size! < 1024 * 10) {
      // 10KB
      promises.push(this.setLocalStorage(key, item))
    }

    // Service Worker缓存（网络资源）
    if (this.config.enableServiceWorker && options?.strategy) {
      promises.push(this.setSWCache(key, value, options.strategy))
    }

    await Promise.all(promises)
  }

  /**
   * 获取缓存
   */
  async get<T>(
    key: string,
    options?: {
      store?: string
      strategy?: CacheStrategy
    }
  ): Promise<T | null> {
    const store = options?.store || 'api'

    // 尝试从多级缓存获取
    let result: T | null = null

    // 1. 尝试LocalStorage（最快）
    if (this.config.enableLocalStorage) {
      result = await this.getLocalStorage<T>(key)
      if (result !== null) {
        this.stats.hits++
        return result
      }
    }

    // 2. 尝试IndexedDB
    if (this.config.enableIndexedDB && this.db) {
      result = await this.getIndexedDB<T>(store, key)
      if (result !== null) {
        this.stats.hits++
        return result
      }
    }

    // 3. 尝试Service Worker缓存
    if (this.config.enableServiceWorker && options?.strategy) {
      result = await this.getSWCache<T>(key, options.strategy)
      if (result !== null) {
        this.stats.hits++
        return result
      }
    }

    this.stats.misses++
    return null
  }

  /**
   * 删除缓存
   */
  async delete(key: string, store?: string): Promise<void> {
    const promises: Promise<void>[] = []

    if (this.config.enableIndexedDB && this.db) {
      promises.push(this.deleteIndexedDB(store || 'api', key))
    }

    if (this.config.enableLocalStorage) {
      promises.push(this.deleteLocalStorage(key))
    }

    await Promise.all(promises)
  }

  /**
   * 清空缓存
   */
  async clear(store?: string): Promise<void> {
    if (store) {
      // 清空指定存储
      if (this.config.enableIndexedDB && this.db) {
        await this.clearIndexedDBStore(store)
      }
    } else {
      // 清空所有缓存
      if (this.config.enableIndexedDB && this.db) {
        await this.clearAllIndexedDB()
      }

      if (this.config.enableLocalStorage) {
        this.clearLocalStorage()
      }

      if (this.config.enableServiceWorker) {
        await this.clearSWCache()
      }
    }

    // 重置统计
    this.stats.hits = 0
    this.stats.misses = 0
  }

  /**
   * IndexedDB操作
   */
  private async setIndexedDB<T>(store: string, item: CacheItem<T>): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'))
        return
      }

      const transaction = this.db.transaction([store], 'readwrite')
      const objectStore = transaction.objectStore(store)
      const request = objectStore.put(item)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  private async getIndexedDB<T>(store: string, key: string): Promise<T | null> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'))
        return
      }

      const transaction = this.db.transaction([store], 'readonly')
      const objectStore = transaction.objectStore(store)
      const request = objectStore.get(key)

      request.onsuccess = () => {
        const item = request.result as CacheItem<T>
        if (item && this.isValid(item)) {
          resolve(item.value)
        } else {
          resolve(null)
        }
      }
      request.onerror = () => reject(request.error)
    })
  }

  private async deleteIndexedDB(store: string, key: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'))
        return
      }

      const transaction = this.db.transaction([store], 'readwrite')
      const objectStore = transaction.objectStore(store)
      const request = objectStore.delete(key)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  private async clearIndexedDBStore(store: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'))
        return
      }

      const transaction = this.db.transaction([store], 'readwrite')
      const objectStore = transaction.objectStore(store)
      const request = objectStore.clear()

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  private async clearAllIndexedDB(): Promise<void> {
    const promises = this.config.stores?.map(store => this.clearIndexedDBStore(store)) || []

    await Promise.all(promises)
  }

  /**
   * LocalStorage操作
   */
  private async setLocalStorage<T>(key: string, item: CacheItem<T>): Promise<void> {
    try {
      const prefixedKey = `${this.config.name}:${key}`
      localStorage.setItem(prefixedKey, JSON.stringify(item))
    } catch (__error) {
      }
  }

  private async getLocalStorage<T>(key: string): Promise<T | null> {
    try {
      const prefixedKey = `${this.config.name}:${key}`
      const data = localStorage.getItem(prefixedKey)

      if (data) {
        const item = JSON.parse(data) as CacheItem<T>
        if (this.isValid(item)) {
          return item.value
        } else {
          localStorage.removeItem(prefixedKey)
        }
      }
    } catch (__error) {
      }

    return null
  }

  private async deleteLocalStorage(key: string): Promise<void> {
    const prefixedKey = `${this.config.name}:${key}`
    localStorage.removeItem(prefixedKey)
  }

  private clearLocalStorage(): void {
    const keysToRemove: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith(this.config.name + ':')) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key))
  }

  /**
   * Service Worker缓存操作
   */
  private async setSWCache<T>(key: string, value: T, strategy: CacheStrategy): Promise<void> {
    if (!this.swRegistration?.active) return

    // 通过postMessage与Service Worker通信
    this.swRegistration.active.postMessage({
      type: 'CACHE_SET',
      key,
      value,
      strategy
    })
  }

  private async getSWCache<T>(key: string, strategy: CacheStrategy): Promise<T | null> {
    if (!this.swRegistration?.active) return null

    return new Promise(_resolve => {
      const messageChannel = new MessageChannel()

      messageChannel.port1.onmessage = _event => {
        resolve(event.data.value || null)
      }

      this.swRegistration!.active!.postMessage(
        {
          type: 'CACHE_GET',
          key,
          strategy
        },
        [messageChannel.port2]
      )

      // 超时处理
      setTimeout(() => resolve(null), 1000)
    })
  }

  private async clearSWCache(): Promise<void> {
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.map(name => caches.delete(name)))
    }
  }

  /**
   * 工具方法
   */
  private isValid(item: CacheItem): boolean {
    // 检查是否过期
    if (item.expires && Date.now() > item.expires) {
      return false
    }

    // 检查是否超过最大年龄
    if (this.config.maxAge && Date.now() - item.timestamp > this.config.maxAge) {
      return false
    }

    return true
  }

  private calculateSize(value: unknown): number {
    const str = JSON.stringify(value)
    return new Blob([str]).size
  }

  private notifyUpdate(): void {
    // 通知应用有更新
    window.dispatchEvent(new Event('sw-update'))
  }

  /**
   * 定期清理过期缓存
   */
  private scheduleCleanup(): void {
    setInterval(
      async () => {
        await this.cleanup()
      },
      60 * 60 * 1000
    ) // 每小时清理一次
  }

  private async cleanup(): Promise<void> {
    if (!this.db) return

    const now = Date.now()

    for (const store of this.config.stores || []) {
      const transaction = this.db.transaction([store], 'readwrite')
      const objectStore = transaction.objectStore(store)
      const index = objectStore.index('timestamp')

      const request = index.openCursor()

      request.onsuccess = _event => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          const item = cursor.value as CacheItem
          if (!this.isValid(item)) {
            cursor.delete()
          }
          cursor.continue()
        }
      }
    }
  }

  /**
   * 获取缓存统计
   */
  async getStats(): Promise<CacheStats> {
    const stats: CacheStats = {
      totalSize: 0,
      itemCount: 0,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
      stores: {}
    }

    if (this.db) {
      for (const storeName of this.config.stores || []) {
        const storeStats = await this.getStoreStats(storeName)
        stats.stores[storeName] = storeStats
        stats.totalSize += storeStats.size
        stats.itemCount += storeStats.count
      }
    }

    return stats
  }

  private async getStoreStats(storeName: string): Promise<{ size: number; count: number }> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'))
        return
      }

      let size = 0
      let count = 0

      const transaction = this.db.transaction([storeName], 'readonly')
      const objectStore = transaction.objectStore(storeName)
      const request = objectStore.openCursor()

      request.onsuccess = _event => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          const item = cursor.value as CacheItem
          size += item.size || 0
          count++
          cursor.continue()
        } else {
          resolve({ size, count })
        }
      }

      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 导出缓存数据
   */
  async export(): Promise<Blob> {
    const data: unknown = {
      version: this.config.version,
      timestamp: Date.now(),
      stores: {}
    }

    if (this.db) {
      for (const storeName of this.config.stores || []) {
        data.stores[storeName] = await this.exportStore(storeName)
      }
    }

    return new Blob([JSON.stringify(data)], { type: 'application/json' })
  }

  private async exportStore(storeName: string): Promise<CacheItem[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'))
        return
      }

      const items: CacheItem[] = []

      const transaction = this.db.transaction([storeName], 'readonly')
      const objectStore = transaction.objectStore(storeName)
      const request = objectStore.openCursor()

      request.onsuccess = _event => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          items.push(cursor.value)
          cursor.continue()
        } else {
          resolve(items)
        }
      }

      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 导入缓存数据
   */
  async import(blob: Blob): Promise<void> {
    const text = await blob.text()
    const data = JSON.parse(text)

    if (data.version !== this.config.version) {
      throw new Error('Cache version mismatch')
    }

    for (const storeName in data.stores) {
      const items = data.stores[storeName] as CacheItem[]

      for (const item of items) {
        await this.setIndexedDB(storeName, item)
      }
    }
  }
}

// 导出单例
export const offlineCache = OfflineCache.getInstance()

// 导出Vue组合式API
export function useOfflineCache() {
  const isOnline = ref(navigator.onLine)
  const hasUpdate = ref(false)
  const cacheStats = ref<CacheStats | null>(null)

  // 监听在线状态
  window.addEventListener('online', () => {
    isOnline.value = true
  })

  window.addEventListener('offline', () => {
    isOnline.value = false
  })

  // 监听Service Worker更新
  window.addEventListener('sw-update', () => {
    hasUpdate.value = true
  })

  // 更新缓存统计
  const updateStats = async () => {
    cacheStats.value = await offlineCache.getStats()
  }

  return {
    isOnline,
    hasUpdate,
    cacheStats,
    updateStats,
    cache: offlineCache
  }
}
