/**
 * 浏览器存储引擎（localStorage/sessionStorage）
 */

import type { ICacheStorage } from '../core/cache.interface'

export class BrowserStorage implements ICacheStorage {
  constructor(private storage: Storage) {}

  async get(key: string): Promise<string | null> {
    try {
      const item = this.storage.getItem(key)

      if (!item) {
        return null
      }

      // 检查是否是带过期时间的数据
      try {
        const data = JSON.parse(item)
        if (data && typeof data === 'object' && 'value' in data && 'expires' in data) {
          if (data.expires && data.expires <= Date.now()) {
            this.storage.removeItem(key)
            return null
          }
          return data.value
        }
      } catch {
        // 如果解析失败，直接返回原始值
      }

      return item
    } catch (__error) {
      return null
    }
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (ttl && ttl > 0) {
        // 带过期时间的数据
        const data = {
          value,
          expires: Date.now() + ttl
        }
        this.storage.setItem(key, JSON.stringify(data))
      } else {
        // 永久数据
        this.storage.setItem(key, value)
      }
    } catch (__error) {
      // 可能是存储空间已满
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        // 尝试清理一些旧数据
        await this.cleanup()
        // 重试一次
        try {
          this.storage.setItem(key, value)
        } catch {
          throw new Error('Storage quota exceeded')
        }
      } else {
        throw error
      }
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      const exists = this.storage.getItem(key) !== null
      this.storage.removeItem(key)
      return exists
    } catch (__error) {
      return false
    }
  }

  async clear(): Promise<void> {
    try {
      // 只清除带特定前缀的键
      const keysToRemove: string[] = []

      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key?.startsWith('cache_')) {
          keysToRemove.push(key)
        }
      }

      keysToRemove.forEach(key => this.storage.removeItem(key))
    } catch (__error) {
      }
  }

  async keys(): Promise<string[]> {
    const keys: string[] = []

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key?.startsWith('cache_')) {
        keys.push(key)
      }
    }

    return keys
  }

  async size(): Promise<number> {
    let count = 0

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key?.startsWith('cache_')) {
        count++
      }
    }

    return count
  }

  private async cleanup(): Promise<void> {
    const now = Date.now()
    const keysToRemove: string[] = []

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key?.startsWith('cache_')) {
        try {
          const item = this.storage.getItem(key)
          if (item) {
            const data = JSON.parse(item)
            if (data && data.expires && data.expires <= now) {
              keysToRemove.push(key)
            }
          }
        } catch {
          // 忽略解析错误
        }
      }
    }

    keysToRemove.forEach(key => this.storage.removeItem(key))
  }
}
