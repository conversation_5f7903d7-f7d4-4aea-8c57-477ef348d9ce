/**
 * 内存存储引擎
 */

import type { ICacheStorage } from '../core/cache.interface'

export class MemoryStorage implements ICacheStorage {
  private data: Map<string, { value: string; expires?: number }> = new Map()

  async get(key: string): Promise<string | null> {
    const item = this.data.get(key)

    if (!item) {
      return null
    }

    if (item.expires && item.expires <= Date.now()) {
      this.data.delete(key)
      return null
    }

    return item.value
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    const expires = ttl && ttl > 0 ? Date.now() + ttl : undefined
    this.data.set(key, { value, expires })
  }

  async delete(key: string): Promise<boolean> {
    return this.data.delete(key)
  }

  async clear(): Promise<void> {
    this.data.clear()
  }

  async keys(): Promise<string[]> {
    return Array.from(this.data.keys())
  }

  async size(): Promise<number> {
    return this.data.size
  }
}
