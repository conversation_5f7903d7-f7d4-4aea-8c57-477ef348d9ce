/**
 * TTL（基于过期时间）缓存实现
 */

import { BaseCache } from '../core/base-cache'
import type { CacheConfig, CacheMetadata } from '../core/cache.interface'
import type { ICacheStorage } from '../core/cache.interface'

interface TTLEntry {
  key: string
  expires: number
}

export class TTLCache extends BaseCache {
  private expirationQueue: TTLEntry[] = []
  private cleanupInterval?: NodeJS.Timeout

  constructor(storage: ICacheStorage, config: CacheConfig = {}) {
    super(storage, {
      ...config,
      evictionPolicy: 'ttl'
    })

    // 启动定期清理
    if (this.config.checkInterval > 0) {
      this.startTTLCleanup()
    }
  }

  async set<T = unknown>(key: string, value: T, options = {}): Promise<void> {
    await super.set(key, value, options)

    const metadata = this.metadata.get(key)
    if (metadata && metadata.expires) {
      // 添加到过期队列
      this.addToExpirationQueue(key, metadata.expires)
    }
  }

  async delete(key: string): Promise<boolean> {
    const deleted = await super.delete(key)

    if (deleted) {
      // 从过期队列中移除
      this.removeFromExpirationQueue(key)
    }

    return deleted
  }

  async clear(): Promise<void> {
    await super.clear()
    this.expirationQueue = []
    this.stopTTLCleanup()
  }

  protected async evictIfNeeded(): Promise<void> {
    // TTL缓存主要依赖过期时间进行淘汰
    // 先清理过期项
    await this.cleanupExpired()

    // 如果仍然超过限制，按过期时间淘汰
    if (this.metadata.size > this.config.maxSize) {
      await this.evictByTTL()
    }

    if (this.statistics.memoryUsage > this.config.maxMemory) {
      await this.evictByMemory()
    }
  }

  private addToExpirationQueue(key: string, expires: number): void {
    // 移除旧的条目
    this.removeFromExpirationQueue(key)

    // 添加新条目并保持队列有序
    const entry: TTLEntry = { key, expires }

    // 二分查找插入位置
    let left = 0
    let right = this.expirationQueue.length

    while (left < right) {
      const mid = Math.floor((left + right) / 2)
      if (this.expirationQueue[mid].expires < expires) {
        left = mid + 1
      } else {
        right = mid
      }
    }

    this.expirationQueue.splice(left, 0, entry)
  }

  private removeFromExpirationQueue(key: string): void {
    const index = this.expirationQueue.findIndex(entry => entry.key === key)
    if (index > -1) {
      this.expirationQueue.splice(index, 1)
    }
  }

  private async cleanupExpired(): Promise<number> {
    const now = Date.now()
    let cleaned = 0

    // 从队列前面开始，清理所有过期项
    while (this.expirationQueue.length > 0) {
      const entry = this.expirationQueue[0]
      if (entry.expires > now) {
        break // 后面的都还没过期
      }

      const metadata = this.metadata.get(entry.key)
      if (metadata) {
        await this.evictKey(entry.key, metadata)
        cleaned++
      }

      this.expirationQueue.shift()
    }

    return cleaned
  }

  private async evictByTTL(): Promise<void> {
    // 计算需要淘汰的数量（10%或至少1个）
    const evictCount = Math.max(1, Math.floor(this.metadata.size * 0.1))
    let evicted = 0

    // 优先淘汰即将过期的项
    for (const entry of this.expirationQueue) {
      if (evicted >= evictCount) break

      const metadata = this.metadata.get(entry.key)
      if (metadata) {
        await this.evictKey(entry.key, metadata)
        evicted++
      }
    }

    // 如果还需要淘汰，淘汰没有过期时间的项（按创建时间）
    if (evicted < evictCount) {
      const permanentItems = Array.from(this.metadata.entries())
        .filter(([_, meta]) => !meta.expires)
        .sort((a, b) => a[1].created - b[1].created)

      for (const [key, metadata] of permanentItems) {
        if (evicted >= evictCount) break

        await this.evictKey(key, metadata)
        evicted++
      }
    }
  }

  private async evictByMemory(): Promise<void> {
    // 目标内存使用量（80%）
    const targetMemory = this.config.maxMemory * 0.8
    let currentMemory = this.statistics.memoryUsage

    // 先淘汰即将过期的项
    for (const entry of this.expirationQueue) {
      if (currentMemory <= targetMemory) break

      const metadata = this.metadata.get(entry.key)
      if (metadata) {
        currentMemory -= metadata.size
        await this.evictKey(entry.key, metadata)
      }
    }

    // 如果还需要释放内存，淘汰永久项
    if (currentMemory > targetMemory) {
      const permanentItems = Array.from(this.metadata.entries())
        .filter(([_, meta]) => !meta.expires)
        .sort((a, b) => a[1].size - b[1].size) // 优先淘汰大的

      for (const [key, metadata] of permanentItems) {
        if (currentMemory <= targetMemory) break

        currentMemory -= metadata.size
        await this.evictKey(key, metadata)
      }
    }
  }

  private async evictKey(key: string, metadata: CacheMetadata): Promise<void> {
    // 删除数据
    await this.delete(key)

    // 记录淘汰
    this.recordEviction()

    // 触发淘汰回调
    if (this.config.onEvict) {
      const value = await this.get(key)
      this.config.onEvict(key, value)
    }
  }

  private startTTLCleanup(): void {
    this.cleanupInterval = setInterval(
      async () => {
        await this.cleanupExpired()
      },
      Math.min(this.config.checkInterval, 10000)
    ) // 最多10秒检查一次
  }

  private stopTTLCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = undefined
    }
  }

  async optimize(): Promise<void> {
    // 清理过期项
    const cleaned = await this.cleanupExpired()

    // 重建过期队列，确保一致性
    this.expirationQueue = []

    for (const [key, metadata] of this.metadata) {
      if (metadata.expires) {
        this.addToExpirationQueue(key, metadata.expires)
      }
    }
  }

  // 清理资源
  async destroy(): Promise<void> {
    this.stopTTLCleanup()
    await this.clear()
  }
}
