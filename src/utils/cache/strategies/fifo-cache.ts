/**
 * FIFO（先进先出）缓存实现
 */

import { BaseCache } from '../core/base-cache'
import type { CacheConfig, CacheMetadata } from '../core/cache.interface'
import type { ICacheStorage } from '../core/cache.interface'

export class FIFOCache extends BaseCache {
  private insertOrder: string[] = []

  constructor(storage: ICacheStorage, config: CacheConfig = {}) {
    super(storage, {
      ...config,
      evictionPolicy: 'fifo'
    })
  }

  async set<T = unknown>(key: string, value: T, options = {}): Promise<void> {
    const isNew = !(await this.has(key))

    await super.set(key, value, options)

    if (isNew) {
      // 新项添加到队列末尾
      this.insertOrder.push(key)

      // 限制数组大小，防止内存泄漏
      if (this.insertOrder.length > this.config.maxSize * 2) {
        this.cleanupInsertOrder()
      }
    }
  }

  async delete(key: string): Promise<boolean> {
    const deleted = await super.delete(key)

    if (deleted) {
      // 从插入顺序中移除
      const index = this.insertOrder.indexOf(key)
      if (index > -1) {
        this.insertOrder.splice(index, 1)
      }
    }

    return deleted
  }

  async clear(): Promise<void> {
    await super.clear()
    this.insertOrder = []
  }

  protected async evictIfNeeded(): Promise<void> {
    // 检查是否超过最大大小
    if (this.metadata.size > this.config.maxSize) {
      await this.evictFIFO()
    }

    // 检查是否超过最大内存
    if (this.statistics.memoryUsage > this.config.maxMemory) {
      await this.evictByMemory()
    }
  }

  private async evictFIFO(): Promise<void> {
    // 计算需要淘汰的数量（10%或至少1个）
    const evictCount = Math.max(1, Math.floor(this.metadata.size * 0.1))
    let evicted = 0

    // 从最早插入的开始淘汰
    for (const key of this.insertOrder) {
      if (evicted >= evictCount) break

      const metadata = this.metadata.get(key)
      if (metadata) {
        await this.evictKey(key, metadata)
        evicted++
      }
    }
  }

  private async evictByMemory(): Promise<void> {
    // 目标内存使用量（80%）
    const targetMemory = this.config.maxMemory * 0.8
    let currentMemory = this.statistics.memoryUsage

    // 按插入顺序淘汰，直到内存使用量低于目标
    for (const key of this.insertOrder) {
      if (currentMemory <= targetMemory) break

      const metadata = this.metadata.get(key)
      if (metadata) {
        currentMemory -= metadata.size
        await this.evictKey(key, metadata)
      }
    }
  }

  private async evictKey(key: string, metadata: CacheMetadata): Promise<void> {
    // 删除数据
    await this.delete(key)

    // 记录淘汰
    this.recordEviction()

    // 触发淘汰回调
    if (this.config.onEvict) {
      const value = await this.get(key)
      this.config.onEvict(key, value)
    }
  }

  private cleanupInsertOrder(): void {
    // 只保留仍然存在的键
    this.insertOrder = this.insertOrder.filter(key => this.metadata.has(key))
  }

  async optimize(): Promise<void> {
    // 清理过期项
    await this.prune()

    // 清理插入顺序数组
    this.cleanupInsertOrder()
  }
}
