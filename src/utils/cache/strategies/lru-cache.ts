/**
 * LRU（最近最少使用）缓存实现
 */

import { BaseCache } from '../core/base-cache'
import type { CacheConfig, CacheMetadata } from '../core/cache.interface'
import type { ICacheStorage } from '../core/cache.interface'

export class LRUCache extends BaseCache {
  private accessOrder: string[] = []

  constructor(storage: ICacheStorage, config: CacheConfig = {}) {
    super(storage, {
      ...config,
      evictionPolicy: 'lru'
    })
  }

  async get<T = unknown>(key: string): Promise<T | null> {
    const value = await super.get<T>(key)

    if (value !== null) {
      // 更新访问顺序
      this.updateAccessOrder(key)
    }

    return value
  }

  async set<T = unknown>(key: string, value: T, options = {}): Promise<void> {
    await super.set(key, value, options)
    this.updateAccessOrder(key)
  }

  protected async evictIfNeeded(): Promise<void> {
    // 检查是否超过最大大小
    if (this.metadata.size > this.config.maxSize) {
      await this.evictLRU()
    }

    // 检查是否超过最大内存
    if (this.statistics.memoryUsage > this.config.maxMemory) {
      await this.evictByMemory()
    }
  }

  private updateAccessOrder(key: string): void {
    // 移除旧位置
    const index = this.accessOrder.indexOf(key)
    if (index > -1) {
      this.accessOrder.splice(index, 1)
    }

    // 添加到末尾（最近访问）
    this.accessOrder.push(key)

    // 限制数组大小，防止内存泄漏
    if (this.accessOrder.length > this.config.maxSize * 2) {
      // 只保留有效的键
      this.accessOrder = this.accessOrder.filter(k => this.metadata.has(k))
    }
  }

  private async evictLRU(): Promise<void> {
    // 计算需要淘汰的数量（10%或至少1个）
    const evictCount = Math.max(1, Math.floor(this.metadata.size * 0.1))
    let evicted = 0

    // 从最少访问的开始淘汰
    for (const key of this.accessOrder) {
      if (evicted >= evictCount) break

      const metadata = this.metadata.get(key)
      if (metadata) {
        await this.evictKey(key, metadata)
        evicted++
      }
    }
  }

  private async evictByMemory(): Promise<void> {
    // 目标内存使用量（80%）
    const targetMemory = this.config.maxMemory * 0.8
    let currentMemory = this.statistics.memoryUsage

    // 按访问顺序淘汰，直到内存使用量低于目标
    for (const key of this.accessOrder) {
      if (currentMemory <= targetMemory) break

      const metadata = this.metadata.get(key)
      if (metadata) {
        currentMemory -= metadata.size
        await this.evictKey(key, metadata)
      }
    }
  }

  private async evictKey(key: string, metadata: CacheMetadata): Promise<void> {
    // 删除数据
    await this.delete(key)

    // 从访问顺序中移除
    const index = this.accessOrder.indexOf(key)
    if (index > -1) {
      this.accessOrder.splice(index, 1)
    }

    // 记录淘汰
    this.recordEviction()

    // 触发淘汰回调
    if (this.config.onEvict) {
      const value = await this.get(key)
      this.config.onEvict(key, value)
    }
  }

  async optimize(): Promise<void> {
    // 清理过期项
    await this.prune()

    // 重建访问顺序数组
    this.accessOrder = this.accessOrder.filter(key => this.metadata.has(key))

    // 根据实际访问频率重新排序
    this.accessOrder.sort((a, b) => {
      const metaA = this.metadata.get(a)
      const metaB = this.metadata.get(b)

      if (!metaA || !metaB) return 0

      // 综合考虑最后访问时间和访问次数
      const scoreA = metaA.accessed + metaA.hits * 60000 // 每次访问延长1分钟
      const scoreB = metaB.accessed + metaB.hits * 60000

      return scoreA - scoreB
    })
  }
}
