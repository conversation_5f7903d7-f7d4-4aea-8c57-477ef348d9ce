/**
 * LFU（最不经常使用）缓存实现
 */

import { BaseCache } from '../core/base-cache'
import type { CacheConfig, CacheMetadata } from '../core/cache.interface'
import type { ICacheStorage } from '../core/cache.interface'

interface FrequencyNode {
  frequency: number
  keys: Set<string>
  prev: FrequencyNode | null
  next: FrequencyNode | null
}

export class LFUCache extends BaseCache {
  private frequencyMap: Map<string, number> = new Map()
  private frequencyList: Map<number, FrequencyNode> = new Map()
  private minFrequency: number = 0

  constructor(storage: ICacheStorage, config: CacheConfig = {}) {
    super(storage, {
      ...config,
      evictionPolicy: 'lfu'
    })
  }

  async get<T = unknown>(key: string): Promise<T | null> {
    const value = await super.get<T>(key)

    if (value !== null) {
      // 更新访问频率
      this.updateFrequency(key)
    }

    return value
  }

  async set<T = unknown>(key: string, value: T, options = {}): Promise<void> {
    await super.set(key, value, options)

    // 初始化频率为1
    this.frequencyMap.set(key, 1)
    this.addToFrequencyNode(key, 1)
    this.minFrequency = 1
  }

  async delete(key: string): Promise<boolean> {
    const deleted = await super.delete(key)

    if (deleted) {
      const frequency = this.frequencyMap.get(key)
      if (frequency !== undefined) {
        this.removeFromFrequencyNode(key, frequency)
        this.frequencyMap.delete(key)
      }
    }

    return deleted
  }

  async clear(): Promise<void> {
    await super.clear()
    this.frequencyMap.clear()
    this.frequencyList.clear()
    this.minFrequency = 0
  }

  protected async evictIfNeeded(): Promise<void> {
    // 检查是否超过最大大小
    if (this.metadata.size > this.config.maxSize) {
      await this.evictLFU()
    }

    // 检查是否超过最大内存
    if (this.statistics.memoryUsage > this.config.maxMemory) {
      await this.evictByMemory()
    }
  }

  private updateFrequency(key: string): void {
    const currentFreq = this.frequencyMap.get(key) || 0
    const newFreq = currentFreq + 1

    // 从当前频率节点移除
    if (currentFreq > 0) {
      this.removeFromFrequencyNode(key, currentFreq)
    }

    // 添加到新频率节点
    this.frequencyMap.set(key, newFreq)
    this.addToFrequencyNode(key, newFreq)

    // 更新最小频率
    if (currentFreq === this.minFrequency && !this.frequencyList.get(currentFreq)?.keys.size) {
      this.minFrequency = newFreq
    }
  }

  private addToFrequencyNode(key: string, frequency: number): void {
    let node = this.frequencyList.get(frequency)

    if (!node) {
      node = {
        frequency,
        keys: new Set(),
        prev: null,
        next: null
      }
      this.frequencyList.set(frequency, node)
    }

    node.keys.add(key)
  }

  private removeFromFrequencyNode(key: string, frequency: number): void {
    const node = this.frequencyList.get(frequency)
    if (!node) return

    node.keys.delete(key)

    // 如果节点为空，删除它
    if (node.keys.size === 0) {
      this.frequencyList.delete(frequency)
    }
  }

  private async evictLFU(): Promise<void> {
    // 计算需要淘汰的数量（10%或至少1个）
    const evictCount = Math.max(1, Math.floor(this.metadata.size * 0.1))
    let evicted = 0

    // 从最小频率开始淘汰
    const frequencies = Array.from(this.frequencyList.keys()).sort((a, b) => a - b)

    for (const freq of frequencies) {
      if (evicted >= evictCount) break

      const node = this.frequencyList.get(freq)
      if (!node) continue

      // 在同频率内，选择最老的项（LRU within LFU）
      const keysToEvict = Array.from(node.keys).slice(0, evictCount - evicted)

      for (const key of keysToEvict) {
        const metadata = this.metadata.get(key)
        if (metadata) {
          await this.evictKey(key, metadata, freq)
          evicted++
        }
      }
    }

    // 更新最小频率
    this.updateMinFrequency()
  }

  private async evictByMemory(): Promise<void> {
    // 目标内存使用量（80%）
    const targetMemory = this.config.maxMemory * 0.8
    let currentMemory = this.statistics.memoryUsage

    // 按频率从低到高淘汰
    const frequencies = Array.from(this.frequencyList.keys()).sort((a, b) => a - b)

    for (const freq of frequencies) {
      if (currentMemory <= targetMemory) break

      const node = this.frequencyList.get(freq)
      if (!node) continue

      for (const key of node.keys) {
        if (currentMemory <= targetMemory) break

        const metadata = this.metadata.get(key)
        if (metadata) {
          currentMemory -= metadata.size
          await this.evictKey(key, metadata, freq)
        }
      }
    }
  }

  private async evictKey(key: string, metadata: CacheMetadata, frequency: number): Promise<void> {
    // 删除数据
    await this.delete(key)

    // 记录淘汰
    this.recordEviction()

    // 触发淘汰回调
    if (this.config.onEvict) {
      const value = await this.get(key)
      this.config.onEvict(key, value)
    }
  }

  private updateMinFrequency(): void {
    if (this.frequencyList.size === 0) {
      this.minFrequency = 0
      return
    }

    const frequencies = Array.from(this.frequencyList.keys()).sort((a, b) => a - b)
    this.minFrequency = frequencies[0] || 0
  }

  async optimize(): Promise<void> {
    // 清理过期项
    await this.prune()

    // 清理空的频率节点
    for (const [freq, node] of this.frequencyList) {
      if (node.keys.size === 0) {
        this.frequencyList.delete(freq)
      }
    }

    // 更新最小频率
    this.updateMinFrequency()

    // 重建频率映射，确保一致性
    const validKeys = new Set(this.metadata.keys())
    for (const [key, freq] of this.frequencyMap) {
      if (!validKeys.has(key)) {
        this.frequencyMap.delete(key)
        this.removeFromFrequencyNode(key, freq)
      }
    }
  }
}
