/**
 * 缓存工厂
 */

import type { ICache, CacheConfig, CacheStorageType } from './core/cache.interface'
import { LRUCache } from './strategies/lru-cache'
import { LFUCache } from './strategies/lfu-cache'
import { FIFOCache } from './strategies/fifo-cache'
import { TTLCache } from './strategies/ttl-cache'
import { MemoryStorage } from './storage/memory-storage'
import { BrowserStorage } from './storage/browser-storage'

export class CacheFactory {
  private static instances: Map<string, ICache> = new Map()

  /**
   * 创建缓存实例
   */
  static create(name: string, config: CacheConfig = {}): ICache {
    // 检查是否已存在
    const existingCache = this.instances.get(name)
    if (existingCache) {
      return existingCache
    }

    // 创建存储引擎
    const storage = this.createStorage(config.storage || 'memory')

    // 创建缓存实例
    let cache: ICache

    switch (config.evictionPolicy) {
      case 'lru':
        cache = new LRUCache(storage, {
          ...config,
          prefix: `${config.prefix || 'cache_'}${name}_`
        })
        break

      case 'lfu':
        cache = new LFUCache(storage, {
          ...config,
          prefix: `${config.prefix || 'cache_'}${name}_`
        })
        break

      case 'fifo':
        cache = new FIFOCache(storage, {
          ...config,
          prefix: `${config.prefix || 'cache_'}${name}_`
        })
        break

      case 'ttl':
        cache = new TTLCache(storage, {
          ...config,
          prefix: `${config.prefix || 'cache_'}${name}_`
        })
        break

      default:
        // 默认使用LRU
        cache = new LRUCache(storage, {
          ...config,
          prefix: `${config.prefix || 'cache_'}${name}_`
        })
        break
    }

    // 缓存实例
    this.instances.set(name, cache)

    return cache
  }

  /**
   * 获取缓存实例
   */
  static get(name: string): ICache | undefined {
    return this.instances.get(name)
  }

  /**
   * 销毁缓存实例
   */
  static async destroy(name: string): Promise<void> {
    const cache = this.instances.get(name)
    if (cache) {
      await cache.clear()
      this.instances.delete(name)
    }
  }

  /**
   * 销毁所有缓存实例
   */
  static async destroyAll(): Promise<void> {
    for (const [name, cache] of this.instances) {
      await cache.clear()
    }
    this.instances.clear()
  }

  /**
   * 创建存储引擎
   */
  private static createStorage(type: CacheStorageType) {
    switch (type) {
      case 'localStorage':
        return new BrowserStorage(localStorage)

      case 'sessionStorage':
        return new BrowserStorage(sessionStorage)

      case 'memory':
      default:
        return new MemoryStorage()
    }
  }
}

// 预定义的缓存实例
export const memoryCache = CacheFactory.create('memory', {
  storage: 'memory',
  maxSize: 1000,
  defaultTTL: 5 * 60 * 1000 // 5分钟
})

export const sessionCache = CacheFactory.create('session', {
  storage: 'sessionStorage',
  maxSize: 500,
  defaultTTL: 30 * 60 * 1000 // 30分钟
})

export const localCache = CacheFactory.create('local', {
  storage: 'localStorage',
  maxSize: 200,
  defaultTTL: 24 * 60 * 60 * 1000, // 24小时
  enableCompression: true
})
