/**
 * 缓存基础实现
 */

import type {
  ICache,
  CacheConfig,
  CacheOptions,
  CacheMetadata,
  CacheStatistics,
  BatchResult,
  ICacheStorage
} from './cache.interface'

export abstract class BaseCache implements ICache {
  protected config: Required<CacheConfig>
  protected storage: ICacheStorage
  protected metadata: Map<string, CacheMetadata> = new Map()
  protected timers: Map<string, NodeJS.Timeout> = new Map()
  protected statistics: CacheStatistics
  protected cleanupTimer?: NodeJS.Timeout

  constructor(storage: ICacheStorage, config: CacheConfig = {}) {
    this.storage = storage
    this.config = this.mergeConfig(config)
    this.statistics = this.initStatistics()

    if (this.config.checkInterval > 0) {
      this.startCleanupTimer()
    }
  }

  // 基础操作
  async get<T = unknown>(key: string): Promise<T | null> {
    const startTime = Date.now()

    try {
      const fullKey = this.getFullKey(key)
      const data = await this.storage.get(fullKey)

      if (!data) {
        this.recordMiss()
        return null
      }

      const metadata = this.metadata.get(key)
      if (metadata && this.isExpired(metadata)) {
        await this.delete(key)
        this.recordMiss()
        return null
      }

      const value = this.deserialize<T>(data)

      if (metadata) {
        metadata.accessed = Date.now()
        metadata.hits++
      }

      this.recordHit(Date.now() - startTime)
      return value
    } catch (__error) {
      this.handleError(error as Error)
      return null
    }
  }

  async set<T = unknown>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    const startTime = Date.now()

    try {
      const fullKey = this.getFullKey(key)
      const serialized = this.serialize(value)
      const ttl = options.ttl ?? this.config.defaultTTL

      // 存储数据
      await this.storage.set(fullKey, serialized, ttl)

      // 更新元数据
      const now = Date.now()
      const metadata: CacheMetadata = {
        key,
        size: new Blob([serialized]).size,
        created: now,
        accessed: now,
        modified: now,
        expires: ttl > 0 ? now + ttl : undefined,
        hits: 0,
        priority: options.priority ?? 5,
        tags: options.tags ?? [],
        compressed: options.compress ?? false,
        encrypted: options.encrypt ?? false
      }

      this.metadata.set(key, metadata)

      // 设置过期定时器
      if (ttl > 0) {
        this.setExpirationTimer(key, ttl)
      }

      this.recordSet(Date.now() - startTime)

      // 检查是否需要淘汰
      await this.evictIfNeeded()
    } catch (__error) {
      this.handleError(error as Error)
      throw error
    }
  }

  async has(key: string): Promise<boolean> {
    const fullKey = this.getFullKey(key)
    const exists = (await this.storage.get(fullKey)) !== null

    if (exists) {
      const metadata = this.metadata.get(key)
      if (metadata && this.isExpired(metadata)) {
        await this.delete(key)
        return false
      }
    }

    return exists
  }

  async delete(key: string): Promise<boolean> {
    try {
      const fullKey = this.getFullKey(key)
      const deleted = await this.storage.delete(fullKey)

      if (deleted) {
        this.clearExpirationTimer(key)
        this.metadata.delete(key)
        this.recordDelete()
      }

      return deleted
    } catch (__error) {
      this.handleError(error as Error)
      return false
    }
  }

  async clear(): Promise<void> {
    try {
      await this.storage.clear()
      this.metadata.clear()
      this.clearAllTimers()
      this.resetStatistics()
    } catch (__error) {
      this.handleError(error as Error)
      throw error
    }
  }

  // 批量操作
  async mget<T = unknown>(keys: string[]): Promise<Map<string, T | null>> {
    const results = new Map<string, T | null>()

    await Promise.all(
      keys.map(async key => {
        const value = await this.get<T>(key)
        results.set(key, value)
      })
    )

    return results
  }

  async mset<T = unknown>(entries: Array<[string, T, CacheOptions?]>): Promise<BatchResult<void>> {
    const success: string[] = []
    const failed: Array<{ key: string; error: Error }> = []

    await Promise.all(
      entries.map(async ([key, value, options]) => {
        try {
          await this.set(key, value, options)
          success.push(key)
        } catch (__error) {
          failed.push({ key, error: error as Error })
        }
      })
    )

    return { success, failed, results: new Map() }
  }

  async mdelete(keys: string[]): Promise<BatchResult<boolean>> {
    const success: string[] = []
    const failed: Array<{ key: string; error: Error }> = []
    const results = new Map<string, boolean>()

    await Promise.all(
      keys.map(async key => {
        try {
          const deleted = await this.delete(key)
          results.set(key, deleted)
          if (deleted) success.push(key)
        } catch (__error) {
          failed.push({ key, error: error as Error })
        }
      })
    )

    return { success, failed, results }
  }

  // 高级操作
  async touch(key: string): Promise<boolean> {
    const metadata = this.metadata.get(key)
    if (metadata) {
      metadata.accessed = Date.now()
      metadata.hits++
      return true
    }
    return false
  }

  async expire(key: string, ttl: number): Promise<boolean> {
    const metadata = this.metadata.get(key)
    if (metadata) {
      const now = Date.now()
      metadata.expires = now + ttl
      this.setExpirationTimer(key, ttl)
      return true
    }
    return false
  }

  async persist(key: string): Promise<boolean> {
    const metadata = this.metadata.get(key)
    if (metadata) {
      metadata.expires = undefined
      this.clearExpirationTimer(key)
      return true
    }
    return false
  }

  async ttl(key: string): Promise<number> {
    const metadata = this.metadata.get(key)
    if (metadata && metadata.expires) {
      const remaining = metadata.expires - Date.now()
      return remaining > 0 ? remaining : -1
    }
    return -1
  }

  // 元数据操作
  async getMetadata(key: string): Promise<CacheMetadata | null> {
    return this.metadata.get(key) ?? null
  }

  async getByTags(tags: string[]): Promise<Map<string, unknown>> {
    const results = new Map<string, unknown>()
    const tagSet = new Set(tags)

    for (const [key, metadata] of this.metadata) {
      if (metadata.tags.some(tag => tagSet.has(tag))) {
        const value = await this.get(key)
        if (value !== null) {
          results.set(key, value)
        }
      }
    }

    return results
  }

  async deleteByTags(tags: string[]): Promise<number> {
    const tagSet = new Set(tags)
    const keysToDelete: string[] = []

    for (const [key, metadata] of this.metadata) {
      if (metadata.tags.some(tag => tagSet.has(tag))) {
        keysToDelete.push(key)
      }
    }

    let deleted = 0
    for (const key of keysToDelete) {
      if (await this.delete(key)) {
        deleted++
      }
    }

    return deleted
  }

  // 统计操作
  async getStatistics(): Promise<CacheStatistics> {
    return { ...this.statistics }
  }

  async resetStatistics(): Promise<void> {
    this.statistics = this.initStatistics()
  }

  // 维护操作
  async prune(): Promise<number> {
    let pruned = 0
    const now = Date.now()

    for (const [key, metadata] of this.metadata) {
      if (metadata.expires && metadata.expires <= now) {
        if (await this.delete(key)) {
          pruned++
        }
      }
    }

    return pruned
  }

  async optimize(): Promise<void> {
    // 子类实现具体的优化逻辑
    await this.prune()
  }

  async export(): Promise<Record<string, unknown>> {
    const data: Record<string, unknown> = {}

    for (const [key, metadata] of this.metadata) {
      const value = await this.get(key)
      if (value !== null) {
        data[key] = {
          value,
          metadata: { ...metadata }
        }
      }
    }

    return data
  }

  async import(data: Record<string, unknown>): Promise<void> {
    for (const [key, item] of Object.entries(data)) {
      if (item && typeof item === 'object' && 'value' in item) {
        const options: CacheOptions = {}

        if (item.metadata) {
          options.ttl = item.metadata.expires ? item.metadata.expires - Date.now() : undefined
          options.priority = item.metadata.priority
          options.tags = item.metadata.tags
        }

        await this.set(key, item.value, options)
      }
    }
  }

  // 保护方法
  protected abstract evictIfNeeded(): Promise<void>

  protected getFullKey(key: string): string {
    return `${this.config.prefix}${this.config.version ? `v${this.config.version}_` : ''}${key}`
  }

  protected serialize(value: unknown): string {
    return JSON.stringify(value)
  }

  protected deserialize<T>(data: string): T {
    return JSON.parse(data)
  }

  protected isExpired(metadata: CacheMetadata): boolean {
    return metadata.expires !== undefined && metadata.expires <= Date.now()
  }

  protected setExpirationTimer(key: string, ttl: number): void {
    this.clearExpirationTimer(key)

    const timer = setTimeout(async () => {
      await this.delete(key)
      if (this.config.onExpire) {
        const value = await this.get(key)
        this.config.onExpire(key, value)
      }
    }, ttl)

    this.timers.set(key, timer)
  }

  protected clearExpirationTimer(key: string): void {
    const timer = this.timers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(key)
    }
  }

  protected clearAllTimers(): void {
    for (const timer of this.timers.values()) {
      clearTimeout(timer)
    }
    this.timers.clear()
  }

  protected startCleanupTimer(): void {
    this.cleanupTimer = setInterval(async () => {
      await this.prune()
    }, this.config.checkInterval)
  }

  protected stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  protected mergeConfig(config: CacheConfig): Required<CacheConfig> {
    return {
      storage: 'memory',
      prefix: 'cache_',
      version: '',
      maxSize: 1000,
      maxMemory: 50 * 1024 * 1024, // 50MB
      defaultTTL: 5 * 60 * 1000, // 5分钟
      checkInterval: 60 * 1000, // 1分钟
      evictionPolicy: 'lru',
      enableCompression: false,
      compressionThreshold: 1024, // 1KB
      enableEncryption: false,
      enableStatistics: true,
      enablePersistence: false,
      onEvict: undefined,
      onExpire: undefined,
      onError: undefined,
      ...config
    }
  }

  protected initStatistics(): CacheStatistics {
    return {
      size: 0,
      memoryUsage: 0,
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      hitRate: 0,
      missRate: 0,
      averageGetTime: 0,
      averageSetTime: 0,
      storageDistribution: {
        memory: 0,
        localStorage: 0,
        sessionStorage: 0,
        indexedDB: 0
      },
      tagDistribution: {}
    }
  }

  protected recordHit(time: number): void {
    if (!this.config.enableStatistics) return

    this.statistics.hits++
    this.updateHitRate()
    this.updateAverageGetTime(time)
  }

  protected recordMiss(): void {
    if (!this.config.enableStatistics) return

    this.statistics.misses++
    this.updateHitRate()
  }

  protected recordSet(time: number): void {
    if (!this.config.enableStatistics) return

    this.statistics.sets++
    this.statistics.size = this.metadata.size
    this.updateAverageSetTime(time)
    this.updateMemoryUsage()
  }

  protected recordDelete(): void {
    if (!this.config.enableStatistics) return

    this.statistics.deletes++
    this.statistics.size = this.metadata.size
    this.updateMemoryUsage()
  }

  protected recordEviction(): void {
    if (!this.config.enableStatistics) return

    this.statistics.evictions++
  }

  protected updateHitRate(): void {
    const total = this.statistics.hits + this.statistics.misses
    if (total > 0) {
      this.statistics.hitRate = this.statistics.hits / total
      this.statistics.missRate = 1 - this.statistics.hitRate
    }
  }

  protected updateAverageGetTime(time: number): void {
    const count = this.statistics.hits + this.statistics.misses
    this.statistics.averageGetTime = (this.statistics.averageGetTime * (count - 1) + time) / count
  }

  protected updateAverageSetTime(time: number): void {
    const count = this.statistics.sets
    this.statistics.averageSetTime = (this.statistics.averageSetTime * (count - 1) + time) / count
  }

  protected updateMemoryUsage(): void {
    let totalSize = 0
    for (const metadata of this.metadata.values()) {
      totalSize += metadata.size
    }
    this.statistics.memoryUsage = totalSize
  }

  protected handleError(error: Error): void {
    if (this.config.onError) {
      this.config.onError(error)
    }
  }
}
