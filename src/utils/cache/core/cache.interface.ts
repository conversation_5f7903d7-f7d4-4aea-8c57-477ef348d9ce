/**
 * 统一缓存接口定义
 */

// 缓存存储引擎类型
export type CacheStorageType = 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB'

// 缓存策略类型
export type CacheEvictionPolicy = 'lru' | 'lfu' | 'fifo' | 'ttl' | 'priority'

// 缓存配置接口
export interface CacheConfig {
  // 基础配置
  storage?: CacheStorageType // 存储类型，默认 memory
  prefix?: string // 键前缀，默认 'cache_'
  version?: string // 缓存版本，用于失效旧缓存

  // 容量配置
  maxSize?: number // 最大缓存项数
  maxMemory?: number // 最大内存占用（字节）

  // 时间配置
  defaultTTL?: number // 默认过期时间（毫秒）
  checkInterval?: number // 过期检查间隔（毫秒）

  // 策略配置
  evictionPolicy?: CacheEvictionPolicy // 淘汰策略

  // 功能配置
  enableCompression?: boolean // 启用压缩
  compressionThreshold?: number // 压缩阈值（字节）
  enableEncryption?: boolean // 启用加密
  enableStatistics?: boolean // 启用统计
  enablePersistence?: boolean // 启用持久化

  // 回调配置

  onEvict?: (key: string, value: unknown) => void // 淘汰回调

  onExpire?: (key: string, value: unknown) => void // 过期回调
  onError?: (error: Error) => void // 错误回调
}

// 缓存选项（单次操作）
export interface CacheOptions {
  ttl?: number // 过期时间
  priority?: number // 优先级（1-10）
  tags?: string[] // 标签
  compress?: boolean // 是否压缩
  encrypt?: boolean // 是否加密
  persistent?: boolean // 是否持久化
}

// 缓存元数据
export interface CacheMetadata {
  key: string
  size: number
  created: number
  accessed: number
  modified: number
  expires?: number
  hits: number
  priority: number
  tags: string[]
  compressed: boolean
  encrypted: boolean
  checksum?: string
}

// 缓存统计信息
export interface CacheStatistics {
  // 基础统计
  size: number // 当前缓存项数
  memoryUsage: number // 内存使用量

  // 性能统计
  hits: number // 命中次数
  misses: number // 未命中次数
  sets: number // 设置次数
  deletes: number // 删除次数
  evictions: number // 淘汰次数

  // 计算指标
  hitRate: number // 命中率
  missRate: number // 未命中率
  averageGetTime: number // 平均获取时间
  averageSetTime: number // 平均设置时间

  // 存储分布
  storageDistribution: Record<CacheStorageType, number>

  // 标签分布
  tagDistribution: Record<string, number>
}

// 批量操作结果
export interface BatchResult<T> {
  success: string[]
  failed: Array<{ key: string; error: Error }>
  results: Map<string, T>
}

// 缓存接口
export interface ICache {
  // 基础操作
  get<T = unknown>(key: string): Promise<T | null>
  set<T = unknown>(key: string, value: T, options?: CacheOptions): Promise<void>
  has(key: string): Promise<boolean>
  delete(key: string): Promise<boolean>
  clear(): Promise<void>

  // 批量操作
  mget<T = unknown>(keys: string[]): Promise<Map<string, T | null>>
  mset<T = unknown>(entries: Array<[string, T, CacheOptions?]>): Promise<BatchResult<void>>
  mdelete(keys: string[]): Promise<BatchResult<boolean>>

  // 高级操作
  touch(key: string): Promise<boolean> // 更新访问时间
  expire(key: string, ttl: number): Promise<boolean> // 设置过期时间
  persist(key: string): Promise<boolean> // 移除过期时间
  ttl(key: string): Promise<number> // 获取剩余时间

  // 元数据操作
  getMetadata(key: string): Promise<CacheMetadata | null>
  getByTags(tags: string[]): Promise<Map<string, unknown>>
  deleteByTags(tags: string[]): Promise<number>

  // 统计操作
  getStatistics(): Promise<CacheStatistics>
  resetStatistics(): Promise<void>

  // 维护操作
  prune(): Promise<number> // 清理过期项
  optimize(): Promise<void> // 优化存储
  export(): Promise<Record<string, unknown>> // 导出数据
  import(data: Record<string, unknown>): Promise<void> // 导入数据
}

// 缓存装饰器选项
export interface CacheDecoratorOptions extends CacheOptions {
  key?: string | ((...args: unknown[]) => string) // 缓存键

  condition?: (result: unknown) => boolean // 缓存条件
  fallback?: () => unknown // 失败回退
}

// 缓存存储引擎接口
export interface ICacheStorage {
  get(key: string): Promise<string | null>
  set(key: string, value: string, ttl?: number): Promise<void>
  delete(key: string): Promise<boolean>
  clear(): Promise<void>
  keys(): Promise<string[]>
  size(): Promise<number>
}

// 缓存序列化器接口
export interface ICacheSerializer {
  serialize(value: unknown): string

  deserialize(data: string): unknown
}

// 缓存压缩器接口
export interface ICacheCompressor {
  compress(data: string): Promise<string>
  decompress(data: string): Promise<string>
}

// 缓存加密器接口
export interface ICacheEncryptor {
  encrypt(data: string): Promise<string>
  decrypt(data: string): Promise<string>
}
