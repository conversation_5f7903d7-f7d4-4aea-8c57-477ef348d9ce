/**
 * 统一缓存模块导出
 */

// 核心接口
export type {
  ICache,
  ICacheStorage,
  CacheConfig,
  CacheOptions,
  CacheMetadata,
  CacheStatistics,
  CacheStorageType,
  CacheEvictionPolicy,
  BatchResult
} from './core/cache.interface'

// 缓存工厂和预定义实例
export { CacheFactory, memoryCache, sessionCache, localCache } from './cache-factory'

// 缓存策略
export { LRUCache } from './strategies/lru-cache'
export { LFUCache } from './strategies/lfu-cache'
export { FIFOCache } from './strategies/fifo-cache'
export { TTLCache } from './strategies/ttl-cache'

// 存储引擎
export { MemoryStorage } from './storage/memory-storage'
export { BrowserStorage } from './storage/browser-storage'

// 装饰器
export {
  Cacheable,
  CacheEvict,
  CachePut,
  CacheableBatch,
  type CacheDecoratorOptions
} from './decorators/cache.decorator'

// 基础类（供扩展使用）
export { BaseCache } from './core/base-cache'
