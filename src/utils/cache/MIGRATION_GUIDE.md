# 缓存系统迁移指南

本指南帮助您从旧的缓存实现迁移到新的统一缓存系统。

## 主要变化

1. **统一的缓存接口**：所有缓存实现都遵循 `ICache` 接口
2. **多种淘汰策略**：支持 LRU、LFU、FIFO、TTL 等策略
3. **装饰器支持**：提供方法级缓存装饰器
4. **更丰富的功能**：标签、批量操作、统计、导入导出等

## 迁移步骤

### 1. 从 localStorage/sessionStorage 直接操作迁移

**旧代码：**

```typescript
// 直接使用 localStorage
localStorage.setItem('user', JSON.stringify(user))
const user = JSON.parse(localStorage.getItem('user') || '{}')
localStorage.removeItem('user')
```

**新代码：**

```typescript
import { localCache } from '@/utils/cache'

// 使用预定义的 localCache
await localCache.set('user', user)
const user = await localCache.get('user')
await localCache.delete('user')
```

### 2. 从自定义缓存工具迁移

**旧代码：**

```typescript
// utils/cache.ts 中的旧实现
export function setCache(key: string, value: any, expire?: number) {
  const data = {
    value,
    expire: expire ? Date.now() + expire : 0
  }
  localStorage.setItem(key, JSON.stringify(data))
}

export function getCache(key: string) {
  const data = localStorage.getItem(key)
  if (!data) return null
  const parsed = JSON.parse(data)
  if (parsed.expire && parsed.expire < Date.now()) {
    localStorage.removeItem(key)
    return null
  }
  return parsed.value
}
```

**新代码：**

```typescript
import { CacheFactory } from '@/utils/cache'

// 创建兼容的缓存实例
const cache = CacheFactory.create('app-cache', {
  storage: 'localStorage',
  evictionPolicy: 'ttl',
  defaultTTL: 0 // 默认不过期
})

// 使用新的API
await cache.set(key, value, { ttl: expire })
const value = await cache.get(key)
```

### 3. 从内存缓存 Map 迁移

**旧代码：**

```typescript
class MemoryCache {
  private cache = new Map()

  set(key: string, value: any) {
    this.cache.set(key, value)
  }

  get(key: string) {
    return this.cache.get(key)
  }
}
```

**新代码：**

```typescript
import { memoryCache } from '@/utils/cache'

// 直接使用预定义的内存缓存
await memoryCache.set(key, value)
const value = await memoryCache.get(key)

// 或创建自定义配置的内存缓存
const customCache = CacheFactory.create('custom', {
  storage: 'memory',
  evictionPolicy: 'lru',
  maxSize: 1000
})
```

### 4. API 服务迁移

**旧代码：**

```typescript
class EmployeeAPI {
  private cache = new Map()

  async getEmployee(id: number) {
    if (this.cache.has(id)) {
      return this.cache.get(id)
    }

    const data = await fetch(`/api/employee/${id}`)
    this.cache.set(id, data)
    return data
  }
}
```

**新代码 - 方式1（使用装饰器）：**

```typescript
import { Cacheable, CacheEvict } from '@/utils/cache'

class EmployeeAPI {
  @Cacheable({
    cacheName: 'api',
    keyGenerator: (id: number) => `employee:${id}`,
    ttl: 10 * 60 * 1000 // 10分钟
  })
  async getEmployee(id: number) {
    return await fetch(`/api/employee/${id}`)
  }

  @CacheEvict({
    cacheName: 'api',
    keyGenerator: (id: number) => `employee:${id}`
  })
  async updateEmployee(id: number, data: any) {
    return await fetch(`/api/employee/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }
}
```

**新代码 - 方式2（直接使用缓存）：**

```typescript
import { CacheFactory } from '@/utils/cache'

class EmployeeAPI {
  private cache = CacheFactory.create('employee-api', {
    storage: 'memory',
    evictionPolicy: 'lru',
    maxSize: 100,
    defaultTTL: 10 * 60 * 1000
  })

  async getEmployee(id: number) {
    const cacheKey = `employee:${id}`

    const cached = await this.cache.get(cacheKey)
    if (cached) return cached

    const data = await fetch(`/api/employee/${id}`)
    await this.cache.set(cacheKey, data)
    return data
  }
}
```

## 功能对比

| 功能     | 旧实现   | 新实现             |
| -------- | -------- | ------------------ |
| 过期时间 | 手动实现 | 内置支持           |
| 淘汰策略 | 无       | LRU/LFU/FIFO/TTL   |
| 批量操作 | 无       | mget/mset/mdelete  |
| 标签系统 | 无       | 支持标签查询和删除 |
| 统计信息 | 无       | 命中率、响应时间等 |
| 压缩     | 无       | 可选压缩           |
| 持久化   | 手动     | 自动持久化到存储   |
| 装饰器   | 无       | @Cacheable 等      |

## 最佳实践

### 1. 选择合适的淘汰策略

- **LRU**：适合大多数场景，淘汰最少使用的数据
- **LFU**：适合有明显热点数据的场景
- **FIFO**：适合时序数据，先进先出
- **TTL**：适合有明确过期时间的数据

### 2. 使用标签管理相关数据

```typescript
// 给相关数据打标签
await cache.set('user:1', user1, { tags: ['user', 'dept:1'] })
await cache.set('user:2', user2, { tags: ['user', 'dept:1'] })
await cache.set('user:3', user3, { tags: ['user', 'dept:2'] })

// 按标签查询
const dept1Users = await cache.getByTags(['dept:1'])

// 按标签删除
await cache.deleteByTags(['dept:2'])
```

### 3. 监控缓存性能

```typescript
// 定期检查缓存统计
setInterval(async () => {
  const stats = await cache.getStatistics()
  if (stats.hitRate < 0.3) {
    console.warn('Cache hit rate is low:', stats.hitRate)
  }
}, 60000)
```

### 4. 合理设置缓存大小

```typescript
const cache = CacheFactory.create('bounded-cache', {
  maxSize: 1000, // 最多1000个条目
  maxMemory: 50 * 1024 * 1024, // 最多50MB
  evictionPolicy: 'lru'
})
```

## 兼容性说明

新的缓存系统设计为向后兼容，但有以下注意事项：

1. **异步API**：所有操作都是异步的，需要使用 `await`
2. **存储格式**：新系统使用不同的存储格式，旧数据需要迁移
3. **键前缀**：新系统自动添加前缀，避免键冲突

## 数据迁移脚本

如果需要迁移旧的缓存数据：

```typescript
async function migrateLegacyCache() {
  const newCache = CacheFactory.create('migrated', {
    storage: 'localStorage'
  })

  // 迁移 localStorage 数据
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key && !key.startsWith('cache_')) {
      try {
        const value = localStorage.getItem(key)
        const parsed = JSON.parse(value!)

        // 迁移到新缓存
        await newCache.set(key, parsed.value || parsed, {
          ttl: parsed.expire ? parsed.expire - Date.now() : undefined
        })

        // 删除旧数据
        localStorage.removeItem(key)
      } catch (e) {
        console.error(`Failed to migrate ${key}:`, e)
      }
    }
  }
}
```

## 常见问题

### Q: 如何处理缓存未命中？

```typescript
const value = (await cache.get(key)) ?? (await fetchFromAPI(key))
```

### Q: 如何实现缓存预热？

```typescript
async function warmupCache() {
  const hotData = await fetchHotData()
  const entries = hotData.map(item => [item.id, item, { priority: 10, ttl: 60 * 60 * 1000 }])
  await cache.mset(entries)
}
```

### Q: 如何清理过期数据？

```typescript
// 自动清理（推荐）
const cache = CacheFactory.create('auto-clean', {
  checkInterval: 60 * 1000 // 每分钟检查一次
})

// 手动清理
await cache.prune()
```
