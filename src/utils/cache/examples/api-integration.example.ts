/**
 * API集成示例 - 展示如何在现有API服务中集成缓存
 */

import { CacheFactory, type ICache } from '../index'
import { Cacheable, CacheEvict } from '../decorators/cache.decorator'
import type { Employee } from '@/types'

// 创建API缓存实例
const apiCache = CacheFactory.create('api-responses', {
  storage: 'memory',
  evictionPolicy: 'lru',
  maxSize: 200,
  defaultTTL: 5 * 60 * 1000, // 5分钟默认过期
  enableStatistics: true,
  onEvict: (key, value) => {
    }
})

// 方式1：直接使用缓存实例
export class EmployeeApiWithCache {
  private cache: ICache = apiCache

   
  async getEmployeeList(params: unknown) {
    // 生成缓存键
    const cacheKey = `employee:list:${JSON.stringify(params)}`

    // 尝试从缓存获取
    const cached = await this.cache.get<unknown>(cacheKey)
    if (cached) {
      return cached
    }

    // 调用实际API
    const response = await fetch('/api/employee/list', {
      method: 'POST',
      body: JSON.stringify(params)
    })
    const data = await response.json()

    // 存入缓存
    await this.cache.set(cacheKey, data, {
      ttl: 10 * 60 * 1000, // 10分钟
      tags: ['employee', 'list']
    })

    return data
  }

  async getEmployeeById(id: number) {
    const cacheKey = `employee:detail:${id}`

    // 尝试从缓存获取
    const cached = await this.cache.get<Employee>(cacheKey)
    if (cached) {
      return cached
    }

    // 调用实际API
    const response = await fetch(`/api/employee/${id}`)
    const data = await response.json()

    // 存入缓存，根据数据设置不同的过期时间
    const ttl =
      data.status === 'active'
        ? 30 * 60 * 1000 // 活跃员工缓存30分钟
        : 60 * 60 * 1000 // 非活跃员工缓存1小时

    await this.cache.set(cacheKey, data, {
      ttl,
      tags: ['employee', 'detail', data.departmentId],
      priority: data.level || 5
    })

    return data
  }

  async updateEmployee(id: number, data: Partial<Employee>) {
    // 更新API
    const response = await fetch(`/api/employee/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
    const updated = await response.json()

    // 清除相关缓存
    await this.cache.delete(`employee:detail:${id}`)
    await this.cache.deleteByTags(['list']) // 清除所有列表缓存

    return updated
  }

  async clearDepartmentCache(departmentId: string) {
    // 清除特定部门的所有缓存
    await this.cache.deleteByTags([departmentId])
  }

  async getCacheStats() {
    return await this.cache.getStatistics()
  }
}

// 方式2：使用装饰器
export class EmployeeApiWithDecorator {
  @Cacheable({
    cacheName: 'api-responses',
     
    keyGenerator: (params: unknown) => `employee:list:${JSON.stringify(params)}`,
    ttl: 10 * 60 * 1000,
    tags: ['employee', 'list']
  })
   
  async getEmployeeList(params: unknown) {
    const response = await fetch('/api/employee/list', {
      method: 'POST',
      body: JSON.stringify(params)
    })
    return await response.json()
  }

  @Cacheable({
    cacheName: 'api-responses',
    keyGenerator: (id: number) => `employee:detail:${id}`,
    ttl: 30 * 60 * 1000,
    condition: (id: number) => id > 0 // 只缓存有效ID
  })
  async getEmployeeById(id: number) {
    const response = await fetch(`/api/employee/${id}`)
    return await response.json()
  }

  @CacheEvict({
    cacheName: 'api-responses',
    keyGenerator: (id: number) => `employee:detail:${id}`
  })
  async updateEmployee(id: number, data: Partial<Employee>) {
    const response = await fetch(`/api/employee/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
    return await response.json()
  }

  @CacheEvict({
    cacheName: 'api-responses',
    allEntries: true,
    condition: () => confirm('确定要清除所有缓存吗？')
  })
  async clearAllCache() {
    }
}

// 方式3：混合使用 - 适合复杂场景
export class AdvancedEmployeeApi {
  private cache = CacheFactory.create('employee-cache', {
    storage: 'memory',
    evictionPolicy: 'lfu', // 使用LFU策略缓存热点数据
    maxSize: 500,
    defaultTTL: 15 * 60 * 1000,
    enableCompression: true,
    compressionThreshold: 1024 // 1KB以上的数据压缩
  })

  // 智能缓存策略
  async getEmployee(
    id: number,
    options?: {
      forceRefresh?: boolean
      includeRelations?: boolean
    }
  ) {
    const { forceRefresh = (false = false), includeRelations = (false = false) } = options || {}

    // 根据参数生成不同的缓存键
    const cacheKey = includeRelations ? `employee:full:${id}` : `employee:basic:${id}`

    // 强制刷新时跳过缓存
    if (!forceRefresh) {
      const cached = await this.cache.get(cacheKey)
      if (cached) {
        // 更新访问统计
        await this.cache.touch(cacheKey)
        return cached
      }
    }

    // 获取数据
    const data = await this.fetchEmployee(id, includeRelations)

    // 智能缓存策略
    const cacheOptions = {
      ttl: this.calculateTTL(data),
      tags: this.generateTags(data),
      priority: this.calculatePriority(data),
      compress: includeRelations // 完整数据启用压缩
    }

    await this.cache.set(cacheKey, data, cacheOptions)

    return data
  }

  // 预加载常用数据
  async preloadHotData() {
    const hotEmployeeIds = await this.getHotEmployeeIds()

    // 批量预加载
    const entries: Array<[string, any, any]> = []

    for (const id of hotEmployeeIds) {
      const data = await this.fetchEmployee(id, false)
      entries.push([
        `employee:basic:${id}`,
        data,
        { priority: 10, ttl: 60 * 60 * 1000 } // 高优先级，1小时过期
      ])
    }

    await this.cache.mset(entries)
  }

  // 智能清理策略
  async smartCleanup() {
    const stats = await this.cache.getStatistics()

    // 根据命中率调整策略
    if (stats.hitRate < 0.5) {
      await this.cache.optimize()
    }

    // 清理低优先级数据
    const allData = await this.cache.export()
    const lowPriorityKeys = Object.entries(allData)
      .filter(([_, item]) => item.metadata.priority < 3)
      .map(([key]) => key)

    if (lowPriorityKeys.length > 0) {
      await this.cache.mdelete(lowPriorityKeys)
    }
  }

  private async fetchEmployee(id: number, includeRelations: boolean) {
    // 模拟API调用
    return {
      id,
      name: `Employee ${id}`,
      department: includeRelations ? { id: 1, name: 'IT' } : undefined,
      team: includeRelations ? [] : undefined
    }
  }

   
  private calculateTTL(data: unknown): number {
    // 根据数据特征动态计算过期时间
    if (data.status === 'active') return 30 * 60 * 1000 // 30分钟
    if (data.status === 'inactive') return 2 * 60 * 60 * 1000 // 2小时
    return 60 * 60 * 1000 // 默认1小时
  }

   
  private generateTags(data: unknown): string[] {
    const tags = ['employee']
    if (data.departmentId) tags.push(`dept:${data.departmentId}`)
    if (data.teamId) tags.push(`team:${data.teamId}`)
    if (data.role) tags.push(`role:${data.role}`)
    return tags
  }

   
  private calculatePriority(data: unknown): number {
    // 根据员工级别等计算优先级
    const levelPriority = data.level || 5
    const isManager = data.role?.includes('manager')
    return isManager ? Math.min(levelPriority + 3, 10) : levelPriority
  }

  private async getHotEmployeeIds(): Promise<number[]> {
    // 获取热点数据ID（实际应从统计数据获取）
    return [1, 2, 3, 4, 5]
  }
}

// 使用示例
export async function demonstrateApiCache() {
  // 方式1：直接使用
  const api1 = new EmployeeApiWithCache()
  await api1.getEmployeeList({ page: 1 })
  await api1.getEmployeeList({ page: 1 }) // 缓存命中
  // 方式2：装饰器
  const api2 = new EmployeeApiWithDecorator()
  await api2.getEmployeeById(1)
  await api2.getEmployeeById(1) // 缓存命中

  // 方式3：高级用法
  const api3 = new AdvancedEmployeeApi()
  await api3.preloadHotData()
  await api3.getEmployee(1) // 已预加载
  await api3.getEmployee(100, { includeRelations: true })
  await api3.smartCleanup()
}
