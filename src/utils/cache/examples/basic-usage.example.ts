/**
 * 缓存基础使用示例
 */

import { CacheFactory, memoryCache, localCache, sessionCache } from '../index'

// 示例1：使用预定义的缓存实例
export async function example1() {
  // 使用内存缓存
  await memoryCache.set('user:1', { id: 1, name: '<PERSON>' }, { ttl: 5 * 60 * 1000 })
  const user = await memoryCache.get('user:1')
  // 使用会话缓存
  await sessionCache.set('token', 'abc123', { ttl: 30 * 60 * 1000 })
  const token = await sessionCache.get('token')
  // 使用本地缓存
  await localCache.set('settings', { theme: 'dark' }, { compress: true })
  const settings = await localCache.get('settings')
  }

// 示例2：创建自定义缓存实例
export async function example2() {
  // 创建LRU缓存
    storage: 'memory',
    evictionPolicy: 'lru',
    maxSize: 100,
    defaultTTL: 10 * 60 * 1000, // 10分钟
    prefix: 'api_',
    onEvict: (key, value) => {
      }
  })

  // 创建LFU缓存
    storage: 'sessionStorage',
    evictionPolicy: 'lfu',
    maxSize: 50,
    defaultTTL: 60 * 60 * 1000 // 1小时
  })

  // 创建TTL缓存
    storage: 'memory',
    evictionPolicy: 'ttl',
    maxSize: 200,
    checkInterval: 10 * 1000 // 10秒检查一次过期
  })
}

// 示例3：批量操作
export async function example3() {
  const cache = CacheFactory.create('batch-cache', {
    storage: 'memory',
    maxSize: 1000
  })

  // 批量设置
  const entries: Array<[string, any, any?]> = [
    ['key1', 'value1', { ttl: 60000 }],
    ['key2', 'value2', { tags: ['temp'] }],
    ['key3', 'value3', { priority: 10 }]
  ]

  // 批量获取
  // 批量删除
  }

// 示例4：使用标签
export async function example4() {
  const cache = memoryCache

  // 设置带标签的缓存
  await cache.set('user:1', { name: 'John' }, { tags: ['user', 'active'] })
  await cache.set('user:2', { name: 'Jane' }, { tags: ['user', 'inactive'] })
  await cache.set('post:1', { title: 'Hello' }, { tags: ['post', 'published'] })

  // 根据标签获取
  // 根据标签删除
  }

// 示例5：缓存统计
export async function example5() {
  const cache = CacheFactory.create('stats-cache', {
    storage: 'memory',
    enableStatistics: true
  })

  // 执行一些操作
  await cache.set('key1', 'value1')
  await cache.get('key1') // 命中
  await cache.get('key2') // 未命中
  await cache.set('key2', 'value2')
  await cache.delete('key1')

  // 获取统计信息
  const stats = await cache.getStatistics()
  }

// 示例6：缓存导入导出
export async function example6() {
  const sourceCache = CacheFactory.create('source', {
    storage: 'memory'
  })

  // 添加一些数据
  await sourceCache.set('key1', 'value1')
  await sourceCache.set('key2', 'value2')

  // 导出数据
  const exportedData = await sourceCache.export()
  // 导入到另一个缓存
  const targetCache = CacheFactory.create('target', {
    storage: 'localStorage'
  })

  await targetCache.import(exportedData)

  // 验证导入
  }

// 示例7：缓存过期和持久化
export async function example7() {
  const cache = memoryCache

  // 设置带过期时间的缓存
  await cache.set('temp', 'data', { ttl: 5000 }) // 5秒后过期

  // 检查剩余时间
  const ttl = await cache.ttl('temp')
  // 延长过期时间
  await cache.expire('temp', 10000) // 延长到10秒

  // 设置为永久
  await cache.persist('temp')

  // 手动触发清理
  }
