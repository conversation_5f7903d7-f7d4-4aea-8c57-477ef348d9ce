/**
 * 缓存装饰器使用示例
 */

import { Cacheable, CacheEvict, CachePut, CacheableBatch } from '../decorators/cache.decorator'
import { CacheFactory } from '../cache-factory'

// 创建API缓存
CacheFactory.create('api', {
  storage: 'memory',
  evictionPolicy: 'lru',
  maxSize: 100,
  defaultTTL: 5 * 60 * 1000 // 5分钟
})

// 示例服务类
export class UserService {
  /**
   * 使用@Cacheable装饰器自动缓存方法结果
   */
  @Cacheable({
    cacheName: 'api',
    ttl: 10 * 60 * 1000, // 10分钟
    keyGenerator: (id: number) => `user:${id}`,
    onHit: (key, value) => ,
    onMiss: _key => })
  async getUser(id: number) {
    // 模拟API调用
    return {
      id,
      name: `User ${id}`,
      email: `user${id}@example.com`
    }
  }

  /**
   * 使用@CachePut更新缓存
   */
  @CachePut({
    cacheName: 'api',

    keyGenerator: (user: unknown) => `user:${user.id}`
  })
  async updateUser(user: unknown) {
    // 模拟API调用
    return user
  }

  /**
   * 使用@CacheEvict清除缓存
   */
  @CacheEvict({
    cacheName: 'api',
    keyGenerator: (id: number) => `user:${id}`
  })
  async deleteUser(id: number) {
    // 模拟API调用
    return true
  }

  /**
   * 清除所有用户缓存
   */
  @CacheEvict({
    cacheName: 'api',
    allEntries: true
  })
  async clearAllUsers() {
    return true
  }

  /**
   * 条件缓存 - 只缓存活跃用户
   */
  @Cacheable({
    cacheName: 'api',
    condition: (id: number) => id < 1000, // 只缓存ID小于1000的用户
    keyGenerator: (id: number) => `active-user:${id}`
  })
  async getActiveUser(id: number) {
    return {
      id,
      name: `Active User ${id}`,
      status: 'active'
    }
  }

  /**
   * 批量获取用户
   */
  @CacheableBatch({
    cacheName: 'api',
    keyExtractor: _user => `user:${user.id}`,
    ttl: 5 * 60 * 1000
  })
  async getUsersByIds(ids: number[]) {
    } from API...`)
    // 模拟批量API调用
    return ids.map(id => ({
      id,
      name: `User ${id}`,
      email: `user${id}@example.com`
    }))
  }
}

// 复杂示例：组合使用
export class OrderService {
  private orderCache = CacheFactory.create('orders', {
    storage: 'sessionStorage',
    evictionPolicy: 'lru',
    maxSize: 50
  })

  /**
   * 获取订单 - 带复杂缓存键
   */
  @Cacheable({
    cacheName: 'orders',
    keyGenerator: (userId: number, status?: string, page?: number) => {
      return `orders:${userId}:${status || 'all'}:${page || 1}`
    },
    ttl: 3 * 60 * 1000 // 3分钟
  })
  async getOrders(userId: number, status?: string, page?: number) {
    // 模拟API调用
    return {
      userId,
      status,
      page,
      orders: []
    }
  }

  /**
   * 创建订单 - 清除相关缓存
   */
  @CacheEvict({
    cacheName: 'orders',

    keyGenerator: (order: unknown) => `orders:${order.userId}:all:1`
  })
  async createOrder(order: unknown) {
    // 创建订单后清除用户的订单列表缓存
    return { ...order, id: Date.now() }
  }
}

// 使用示例
export async function runExamples() {
  const userService = new UserService()
  const orderService = new OrderService()

  // 第一次调用 - 缓存未命中
  // 第二次调用 - 缓存命中
  // 更新用户 - 更新缓存
  // 再次获取 - 获取到更新后的数据
  // 批量获取
  // 再次批量获取 - 部分命中
  // 条件缓存
  await userService.getActiveUser(500) // 会被缓存
  await userService.getActiveUser(1500) // 不会被缓存

  // 复杂缓存键
  await orderService.getOrders(1, 'pending', 1)
  await orderService.getOrders(1, 'pending', 1) // 缓存命中
  await orderService.getOrders(1, 'completed', 1) // 不同的键，缓存未命中
}

// 自定义装饰器示例
export function CustomCacheable(ttl: number = 60000) {
  return Cacheable({
    cacheName: 'custom',
    ttl,

    keyGenerator: function (...args: unknown[]) {
      // 使用方法名和参数生成键
      const className = this.constructor.name
      const methodName = arguments.callee.name
      return `${className}:${methodName}:${JSON.stringify(args)}`
    }
  })
}

// 使用自定义装饰器
export class ProductService {
  @CustomCacheable(30000) // 30秒缓存
  async getProduct(id: number) {
    return { id, name: `Product ${id}` }
  }
}
