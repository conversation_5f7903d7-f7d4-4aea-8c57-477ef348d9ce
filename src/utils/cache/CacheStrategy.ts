/**
 * 缓存策略管理器
 * 提供多级缓存协同、智能缓存策略、预热机制等高级功能
 */

import { ref, computed } from 'vue'
import { offlineCache } from './OfflineCache'

// 缓存级别
export enum CacheLevel {
  MEMORY = 'memory',      // 内存缓存（最快）
  SESSION = 'session',    // 会话缓存
  LOCAL = 'local',        // 本地缓存
  INDEXED = 'indexed',    // IndexedDB缓存
  NETWORK = 'network'     // 网络请求
}

// 缓存策略类型
export type CacheStrategyType = 
  | 'lru'           // 最近最少使用
  | 'lfu'           // 最不经常使用
  | 'ttl'           // 基于时间过期
  | 'priority'      // 基于优先级
  | 'adaptive'      // 自适应策略

// 缓存配置
export interface CacheConfig {
  maxMemorySize?: number        // 内存缓存最大大小
  maxItems?: number             // 最大缓存项数
  defaultTTL?: number           // 默认过期时间
  enableCompression?: boolean   // 启用压缩
  enableEncryption?: boolean    // 启用加密
  preloadKeys?: string[]        // 预加载键列表
  strategy?: CacheStrategyType  // 缓存策略
}

// 缓存项元数据
export interface CacheMetadata {
  key: string
  size: number
  level: CacheLevel
  accessCount: number
  lastAccess: number
  createTime: number
  ttl?: number
  priority?: number
  compressed?: boolean
  encrypted?: boolean
}

// 缓存统计信息
export interface CacheStatistics {
  hitCount: number
  missCount: number
  evictCount: number
  hitRate: number
  averageLoadTime: number
  memoryUsage: number
  levelDistribution: Record<CacheLevel, number>
}

export class CacheStrategy {
  private static instance: CacheStrategy
  private memoryCache: Map<string, unknown> = new Map()
  private metadata: Map<string, CacheMetadata> = new Map()
  private accessQueue: string[] = []
  private config: Required<CacheConfig>
  private statistics: CacheStatistics = {
    hitCount: 0,
    missCount: 0,
    evictCount: 0,
    hitRate: 0,
    averageLoadTime: 0,
    memoryUsage: 0,
    levelDistribution: {
      [CacheLevel.MEMORY]: 0,
      [CacheLevel.SESSION]: 0,
      [CacheLevel.LOCAL]: 0,
      [CacheLevel.INDEXED]: 0,
      [CacheLevel.NETWORK]: 0
    }
  }
  
  private readonly defaultConfig: Required<CacheConfig> = {
    maxMemorySize: 50 * 1024 * 1024,  // 50MB
    maxItems: 1000,
    defaultTTL: 5 * 60 * 1000,         // 5分钟
    enableCompression: true,
    enableEncryption: false,
    preloadKeys: [],
    strategy: 'lru'
  }
  
  private constructor(config?: CacheConfig) {
    this.config = { ...this.defaultConfig, ...config }
    this.initializePreload()
    this.startCleanupTimer()
  }
  
  static getInstance(config?: CacheConfig): CacheStrategy {
    if (!CacheStrategy.instance) {
      CacheStrategy.instance = new CacheStrategy(config)
    }
    return CacheStrategy.instance
  }
  
  /**
   * 智能获取缓存
   */
  async get<T>(
    key: string,
    loader?: () => Promise<T>,
    options?: {
      ttl?: number
      priority?: number
      forceRefresh?: boolean
      levels?: CacheLevel[]
    }
  ): Promise<T | null> {
    const startTime = performance.now()
    
    // 强制刷新
    if (options?.forceRefresh && loader) {
      return this.loadAndCache(key, loader, options)
    }
    
    // 定义缓存级别顺序
    const levels = options?.levels || [
      CacheLevel.MEMORY,
      CacheLevel.SESSION,
      CacheLevel.LOCAL,
      CacheLevel.INDEXED
    ]
    
    // 逐级查找缓存
    for (const level of levels) {
      const value = await this.getFromLevel<T>(key, level)
      if (value !== null) {
        this.recordHit(key, level, performance.now() - startTime)
        
        // 提升到更高级别缓存
        if (level !== CacheLevel.MEMORY) {
          await this.promote(key, value, level, options)
        }
        
        return value
      }
    }
    
    // 缓存未命中
    this.recordMiss(key)
    
    // 如果提供了加载器，则加载数据
    if (loader) {
      return this.loadAndCache(key, loader, options)
    }
    
    return null
  }
  
  /**
   * 设置缓存
   */
  async set<T>(
    key: string,
    value: T,
    options?: {
      ttl?: number
      priority?: number
      levels?: CacheLevel[]
      skipCompression?: boolean
    }
  ): Promise<void> {
    const size = this.calculateSize(value)
    const ttl = options?.ttl || this.config.defaultTTL
    const priority = options?.priority || 5
    const levels = options?.levels || [CacheLevel.MEMORY, CacheLevel.LOCAL]
    
    // 创建元数据
    const metadata: CacheMetadata = {
      key,
      size,
      level: levels[0],
      accessCount: 0,
      lastAccess: Date.now(),
      createTime: Date.now(),
      ttl,
      priority,
      compressed: !options?.skipCompression && this.config.enableCompression,
      encrypted: this.config.enableEncryption
    }
    
    // 处理数据
    let processedValue = value
    if (metadata.compressed) {
      processedValue = await this.compress(value)
    }
    if (metadata.encrypted) {
      processedValue = await this.encrypt(processedValue)
    }
    
    // 存储到各级缓存
    for (const level of levels) {
      await this.setToLevel(key, processedValue, level, metadata)
    }
    
    // 更新元数据
    this.metadata.set(key, metadata)
    
    // 执行淘汰策略
    this.evictIfNeeded()
  }
  
  /**
   * 批量预热缓存
   */
  async preload(
    items: Array<{
      key: string
      loader: () => Promise<unknown>
      options?: {
        ttl?: number
        priority?: number
        levels?: CacheLevel[]
      }
    }>
  ): Promise<void> {
    const promises = items.map(item => 
      this.get(item.key, item.loader, { ...item.options, forceRefresh: true })
    )
    
    await Promise.allSettled(promises)
  }
  
  /**
   * 智能预测预热
   */
  async predictivePreload(
    currentKey: string,
    predictor: (key: string) => string[]
  ): Promise<void> {
    const relatedKeys = predictor(currentKey)
    
    // 异步预热相关数据
    setTimeout(async () => {
      for (const key of relatedKeys) {
        const metadata = this.metadata.get(key)
        if (!metadata || this.isExpired(metadata)) {
          // 标记为预热候选
          }
      }
    }, 100)
  }
  
  /**
   * 从指定级别获取缓存
   */
  private async getFromLevel<T>(key: string, level: CacheLevel): Promise<T | null> {
    switch (level) {
      case CacheLevel.MEMORY:
        return this.getFromMemory(key)
        
      case CacheLevel.SESSION:
        return this.getFromSession(key)
        
      case CacheLevel.LOCAL:
        return this.getFromLocal(key)
        
      case CacheLevel.INDEXED:
        return this.getFromIndexed(key)
        
      default:
        return null
    }
  }
  
  /**
   * 存储到指定级别
   */
  private async setToLevel<T>(
    key: string,
    value: T,
    level: CacheLevel,
    metadata: CacheMetadata
  ): Promise<void> {
    switch (level) {
      case CacheLevel.MEMORY:
        this.memoryCache.set(key, value)
        this.statistics.memoryUsage += metadata.size
        break
        
      case CacheLevel.SESSION:
        sessionStorage.setItem(this.getCacheKey(key), JSON.stringify({
          value,
          metadata
        }))
        break
        
      case CacheLevel.LOCAL:
        localStorage.setItem(this.getCacheKey(key), JSON.stringify({
          value,
          metadata
        }))
        break
        
      case CacheLevel.INDEXED:
        await offlineCache.set(key, value, {
          store: 'cache',
          expires: metadata.createTime + metadata.ttl!
        })
        break
    }
    
    this.statistics.levelDistribution[level]++
  }
  
  /**
   * 内存缓存获取
   */
  private getFromMemory<T>(key: string): T | null {
    const value = this.memoryCache.get(key)
    if (value !== undefined) {
      const metadata = this.metadata.get(key)
      if (metadata && !this.isExpired(metadata)) {
        this.updateAccess(key)
        return value
      } else {
        this.memoryCache.delete(key)
      }
    }
    return null
  }
  
  /**
   * 会话缓存获取
   */
  private getFromSession<T>(key: string): T | null {
    try {
      const item = sessionStorage.getItem(this.getCacheKey(key))
      if (item) {
        const {value, metadata} =  JSON.parse(item)
        if (!this.isExpired(metadata)) {
          return value
        }
        sessionStorage.removeItem(this.getCacheKey(key))
      }
    } catch (error) {
      // 静默处理解析错误
    }
    return null
  }
  
  /**
   * 本地缓存获取
   */
  private getFromLocal<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(this.getCacheKey(key))
      if (item) {
        const { value, metadata } = JSON.parse(item)
        if (!this.isExpired(metadata)) {
          return value
        }
        localStorage.removeItem(this.getCacheKey(key))
      }
    } catch (error) {
      // 静默处理解析错误
    }
    return null
  }
  
  /**
   * IndexedDB缓存获取
   */
  private async getFromIndexed<T>(key: string): Promise<T | null> {
    try {
      const result = await offlineCache.get(key, { store: 'cache' })
      return result || null
    } catch (error) {
      return null
    }
  }
  
  /**
   * 加载并缓存数据
   */
  private async loadAndCache<T>(
    key: string,
    loader: () => Promise<T>,
    options?: {
      ttl?: number
      priority?: number
      levels?: CacheLevel[]
    }
  ): Promise<T> {
    const startTime = performance.now()
    const value = await loader()
    const loadTime = performance.now() - startTime
    
    await this.set(key, value, options)
    this.updateAverageLoadTime(loadTime)
    
    return value
  }
  
  /**
   * 提升缓存级别
   */
  private async promote<T>(
    key: string,
    value: T,
    currentLevel: CacheLevel,
    options?: {
      ttl?: number
      priority?: number
    }
  ): Promise<void> {
    // 提升到内存缓存
    if (currentLevel !== CacheLevel.MEMORY) {
      await this.set(key, value, {
        ...options,
        levels: [CacheLevel.MEMORY]
      })
    }
  }
  
  /**
   * 检查是否需要淘汰
   */
  private evictIfNeeded(): void {
    // 检查内存使用
    if (this.statistics.memoryUsage > this.config.maxMemorySize) {
      this.evictMemory()
    }
    
    // 检查项目数量
    if (this.memoryCache.size > this.config.maxItems) {
      switch (this.config.strategy) {
        case 'lru':
          this.evictLRU()
          break
        case 'lfu':
          this.evictLFU()
          break
        case 'ttl':
          this.evictExpired()
          break
        case 'priority':
          this.evictByPriority()
          break
        case 'adaptive':
          this.evictAdaptive()
          break
      }
    }
  }
  
  /**
   * LRU淘汰
   */
  private evictLRU(): void {
    const sortedKeys = Array.from(this.metadata.entries())
      .sort((a, b) => a[1].lastAccess - b[1].lastAccess)
      .map(([key]) => key)
    
    const evictCount = Math.floor(this.memoryCache.size * 0.1)
    
    for (let i = 0; i < evictCount; i++) {
      this.evictKey(sortedKeys[i])
    }
  }
  
  /**
   * LFU淘汰
   */
  private evictLFU(): void {
    const sortedKeys = Array.from(this.metadata.entries())
      .sort((a, b) => a[1].accessCount - b[1].accessCount)
      .map(([key]) => key)
    
    const evictCount = Math.floor(this.memoryCache.size * 0.1)
    
    for (let i = 0; i < evictCount; i++) {
      this.evictKey(sortedKeys[i])
    }
  }
  
  /**
   * 基于优先级淘汰
   */
  private evictByPriority(): void {
    const sortedKeys = Array.from(this.metadata.entries())
      .sort((a, b) => (a[1].priority || 5) - (b[1].priority || 5))
      .map(([key]) => key)
    
    const evictCount = Math.floor(this.memoryCache.size * 0.1)
    
    for (let i = 0; i < evictCount; i++) {
      this.evictKey(sortedKeys[i])
    }
  }
  
  /**
   * 淘汰过期缓存
   */
  private evictExpired(): void {
    const now = Date.now()
    const expiredKeys: string[] = []
    
    this.metadata.forEach((meta, key) => {
      if (this.isExpired(meta)) {
        expiredKeys.push(key)
      }
    })
    
    expiredKeys.forEach(key => this.evictKey(key))
  }
  
  /**
   * 自适应淘汰
   */
  private evictAdaptive(): void {
    // 综合考虑多个因素
    const scores = new Map<string, number>()
    const now = Date.now()
    
    this.metadata.forEach((meta, key) => {
      const ageScore = (now - meta.createTime) / 1000 // 年龄得分
      const accessScore = 1 / (meta.accessCount + 1) // 访问得分
      const sizeScore = meta.size / 1024 // 大小得分
      const priorityScore = 10 - (meta.priority || 5) // 优先级得分
      
      // 综合得分（权重可调整）
      const totalScore = 
        ageScore * 0.3 +
        accessScore * 0.3 +
        sizeScore * 0.2 +
        priorityScore * 0.2
      
      scores.set(key, totalScore)
    })
    
    // 按得分排序并淘汰
    const sortedKeys = Array.from(scores.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([key]) => key)
    
    const evictCount = Math.floor(this.memoryCache.size * 0.1)
    
    for (let i = 0; i < evictCount; i++) {
      this.evictKey(sortedKeys[i])
    }
  }
  
  /**
   * 淘汰内存缓存
   */
  private evictMemory(): void {
    const targetSize = this.config.maxMemorySize * 0.8 // 目标80%
    let currentSize = this.statistics.memoryUsage
    
    const sortedKeys = Array.from(this.metadata.entries())
      .sort((a, b) => b[1].size - a[1].size) // 按大小降序
      .map(([key]) => key)
    
    for (const key of sortedKeys) {
      if (currentSize <= targetSize) break
      
      const metadata = this.metadata.get(key)
      if (metadata) {
        currentSize -= metadata.size
        this.evictKey(key)
      }
    }
  }
  
  /**
   * 淘汰指定键
   */
  private evictKey(key: string): void {
    const metadata = this.metadata.get(key)
    if (metadata) {
      this.memoryCache.delete(key)
      this.metadata.delete(key)
      this.statistics.memoryUsage -= metadata.size
      this.statistics.evictCount++
      this.statistics.levelDistribution[CacheLevel.MEMORY]--
    }
  }
  
  /**
   * 检查是否过期
   */
  private isExpired(metadata: CacheMetadata): boolean {
    if (!metadata.ttl) return false
    return Date.now() > metadata.createTime + metadata.ttl
  }
  
  /**
   * 更新访问信息
   */
  private updateAccess(key: string): void {
    const metadata = this.metadata.get(key)
    if (metadata) {
      metadata.lastAccess = Date.now()
      metadata.accessCount++
      
      // 更新访问队列（用于LRU）
      const index = this.accessQueue.indexOf(key)
      if (index > -1) {
        this.accessQueue.splice(index, 1)
      }
      this.accessQueue.push(key)
      
      // 限制队列大小
      if (this.accessQueue.length > this.config.maxItems * 2) {
        this.accessQueue = this.accessQueue.slice(-this.config.maxItems)
      }
    }
  }
  
  /**
   * 记录命中
   */
  private recordHit(key: string, level: CacheLevel, loadTime: number): void {
    this.statistics.hitCount++
    this.updateHitRate()
    this.updateAverageLoadTime(loadTime)
  }
  
  /**
   * 记录未命中
   */
  private recordMiss(key: string): void {
    this.statistics.missCount++
    this.updateHitRate()
  }
  
  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    const total = this.statistics.hitCount + this.statistics.missCount
    this.statistics.hitRate = total > 0 
      ? this.statistics.hitCount / total 
      : 0
  }
  
  /**
   * 更新平均加载时间
   */
  private updateAverageLoadTime(loadTime: number): void {
    const count = this.statistics.hitCount + this.statistics.missCount
    this.statistics.averageLoadTime = 
      (this.statistics.averageLoadTime * (count - 1) + loadTime) / count
  }
  
  /**
   * 计算数据大小
   */
  private calculateSize(value: unknown): number {
    const str = JSON.stringify(value)
    return new Blob([str]).size
  }
  
  /**
   * 压缩数据
   */
  private async compress(value: unknown): Promise<unknown> {
    // 简单实现，实际可使用pako等压缩库
    return value
  }
  
  /**
   * 加密数据
   */
  private async encrypt(value: unknown): Promise<unknown> {
    // 简单实现，实际可使用crypto API
    return value
  }
  
  /**
   * 获取缓存键
   */
  private getCacheKey(key: string): string {
    return `cache_strategy_${key}`
  }
  
  /**
   * 初始化预加载
   */
  private async initializePreload(): Promise<void> {
    if (this.config.preloadKeys.length > 0) {
      // 预加载逻辑
    }
  }
  
  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    setInterval(() => {
      this.evictExpired()
      this.cleanupStorage()
    }, 60 * 1000) // 每分钟清理一次
  }
  
  /**
   * 清理存储
   */
  private cleanupStorage(): void {
    // 清理SessionStorage
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key?.startsWith('cache_strategy_')) {
        try {
          const item = JSON.parse(sessionStorage.getItem(key)!)
          if (this.isExpired(item.metadata)) {
            sessionStorage.removeItem(key)
          }
        } catch {}
      }
    }
    
    // 清理LocalStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith('cache_strategy_')) {
        try {
          const item = JSON.parse(localStorage.getItem(key)!)
          if (this.isExpired(item.metadata)) {
            localStorage.removeItem(key)
          }
        } catch {}
      }
    }
  }
  
  /**
   * 获取缓存统计
   */
  getStatistics(): CacheStatistics {
    return { ...this.statistics }
  }
  
  /**
   * 获取缓存详情
   */
  getCacheDetails(): Array<CacheMetadata> {
    return Array.from(this.metadata.values())
  }
  
  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    // 清空内存缓存
    this.memoryCache.clear()
    this.metadata.clear()
    this.accessQueue = []
    
    // 清空会话缓存
    const sessionKeys: string[] = []
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key?.startsWith('cache_strategy_')) {
        sessionKeys.push(key)
      }
    }
    sessionKeys.forEach(key => sessionStorage.removeItem(key))
    
    // 清空本地缓存
    const localKeys: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith('cache_strategy_')) {
        localKeys.push(key)
      }
    }
    localKeys.forEach(key => localStorage.removeItem(key))
    
    // 重置统计
    this.statistics = {
      hitCount: 0,
      missCount: 0,
      evictCount: 0,
      hitRate: 0,
      averageLoadTime: 0,
      memoryUsage: 0,
      levelDistribution: {
        [CacheLevel.MEMORY]: 0,
        [CacheLevel.SESSION]: 0,
        [CacheLevel.LOCAL]: 0,
        [CacheLevel.INDEXED]: 0,
        [CacheLevel.NETWORK]: 0
      }
    }
  }
}

// 导出单例
export const cacheStrategy = CacheStrategy.getInstance()

// 导出Vue组合式API
export function useCacheStrategy() {
  const statistics = ref(cacheStrategy.getStatistics())
  const details = ref(cacheStrategy.getCacheDetails())
  
  const updateStats = () => {
    statistics.value = cacheStrategy.getStatistics()
    details.value = cacheStrategy.getCacheDetails()
  }
  
  const cachedGet = async <T>(
    key: string,
    loader: () => Promise<T>,
    options?: {
      ttl?: number
      priority?: number
      forceRefresh?: boolean
      levels?: CacheLevel[]
    }
  ): Promise<T | null> => {
    const result = await cacheStrategy.get(key, loader, options)
    updateStats()
    return result
  }
  
  const cachedSet = async <T>(
    key: string,
    value: T,
    options?: {
      ttl?: number
      priority?: number
      levels?: CacheLevel[]
      skipCompression?: boolean
    }
  ): Promise<void> => {
    await cacheStrategy.set(key, value, options)
    updateStats()
  }
  
  const clearCache = async () => {
    await cacheStrategy.clear()
    updateStats()
  }
  
  const hitRate = computed(() => 
    (statistics.value.hitRate * 100).toFixed(2) + '%'
  )
  
  const memoryUsage = computed(() => {
    const mb = statistics.value.memoryUsage / (1024 * 1024)
    return mb.toFixed(2) + ' MB'
  })
  
  return {
    statistics,
    details,
    hitRate,
    memoryUsage,
    get: cachedGet,
    set: cachedSet,
    clear: clearCache,
    updateStats
  }
}