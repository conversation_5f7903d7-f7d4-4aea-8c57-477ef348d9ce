 

/**
 * cache.decorator 单元测试
 * @description 自动生成的测试文件
 */

import { describe, it, expect } from 'vitest'
import { Cacheable, CacheEvict, CachePut, CacheableBatch } from '../cache.decorator'
describe('Cacheable', () => {
  it('应该被正确导出', () => {
    expect(Cacheable).toBeDefined()
    expect(typeof Cacheable).toBe('function')
  })
})

describe('CacheEvict', () => {
  it('应该被正确导出', () => {
    expect(CacheEvict).toBeDefined()
    expect(typeof CacheEvict).toBe('function')
  })
})

describe('CachePut', () => {
  it('应该被正确导出', () => {
    expect(CachePut).toBeDefined()
    expect(typeof CachePut).toBe('function')
  })
})

describe('CacheableBatch', () => {
  it('应该被正确导出', () => {
    expect(CacheableBatch).toBeDefined()
    expect(typeof CacheableBatch).toBe('function')
  })
})
