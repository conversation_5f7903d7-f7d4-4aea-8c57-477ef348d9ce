/**
 * 缓存装饰器
 */

import type { ICache, CacheOptions } from '../core/cache.interface'
import { CacheFactory } from '../cache-factory'

export interface CacheDecoratorOptions extends CacheOptions {
  /**
   * 缓存键生成器
   */

  keyGenerator?: (...args: unknown[]) => string

  /**
   * 缓存名称
   */
  cacheName?: string

  /**
   * 条件判断（返回false时不缓存）
   */

  condition?: (...args: unknown[]) => boolean

  /**
   * 缓存未命中时的回调
   */
  onMiss?: (key: string) => void

  /**
   * 缓存命中时的回调
   */

  onHit?: (key: string, value: unknown) => void
}

/**
 * 方法级缓存装饰器
 */
export function Cacheable(options: CacheDecoratorOptions = {}) {
  return function (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: unknown[]) {
      // 检查条件
      if (options.condition && !options.condition(...args)) {
        return originalMethod.apply(this, args)
      }

      // 生成缓存键
      const key = options.keyGenerator
        ? options.keyGenerator(...args)
        : `${target.constructor.name}:${propertyKey}:${JSON.stringify(args)}`

      // 获取缓存实例
      const cacheName = options.cacheName || 'default'
      let cache = CacheFactory.get(cacheName)

      if (!cache) {
        // 创建默认缓存
        cache = CacheFactory.create(cacheName, {
          storage: 'memory',
          maxSize: 1000,
          defaultTTL: 5 * 60 * 1000 // 5分钟
        })
      }

      // 尝试从缓存获取
      let value = await cache.get(key)

      if (value !== null) {
        // 缓存命中
        if (options.onHit) {
          options.onHit(key, value)
        }
        return value
      }

      // 缓存未命中
      if (options.onMiss) {
        options.onMiss(key)
      }

      // 执行原方法
      value = await originalMethod.apply(this, args)

      // 存入缓存
      if (value !== undefined && value !== null) {
        await cache.set(key, value, options)
      }

      return value
    }

    return descriptor
  }
}

/**
 * 清除缓存装饰器
 */
export function CacheEvict(
  options: {
    cacheName?: string
    key?: string

    keyGenerator?: (...args: unknown[]) => string
    allEntries?: boolean

    condition?: (...args: unknown[]) => boolean
  } = {}
) {
  return function (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: unknown[]) {
      // 执行原方法
      const result = await originalMethod.apply(this, args)

      // 检查条件
      if (options.condition && !options.condition(...args)) {
        return result
      }

      // 获取缓存实例
      const cacheName = options.cacheName || 'default'
      const cache = CacheFactory.get(cacheName)

      if (!cache) {
        return result
      }

      if (options.allEntries) {
        // 清除所有缓存
        await cache.clear()
      } else {
        // 清除指定键
        const key =
          options.key ||
          (options.keyGenerator
            ? options.keyGenerator(...args)
            : `${target.constructor.name}:${propertyKey}:${JSON.stringify(args)}`)

        await cache.delete(key)
      }

      return result
    }

    return descriptor
  }
}

/**
 * 更新缓存装饰器
 */
export function CachePut(options: CacheDecoratorOptions = {}) {
  return function (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: unknown[]) {
      // 执行原方法
      const result = await originalMethod.apply(this, args)

      // 检查条件
      if (options.condition && !options.condition(...args)) {
        return result
      }

      // 生成缓存键
      const key = options.keyGenerator
        ? options.keyGenerator(...args)
        : `${target.constructor.name}:${propertyKey}:${JSON.stringify(args)}`

      // 获取缓存实例
      const cacheName = options.cacheName || 'default'
      let cache = CacheFactory.get(cacheName)

      if (!cache) {
        // 创建默认缓存
        cache = CacheFactory.create(cacheName, {
          storage: 'memory',
          maxSize: 1000,
          defaultTTL: 5 * 60 * 1000 // 5分钟
        })
      }

      // 更新缓存
      if (result !== undefined && result !== null) {
        await cache.set(key, result, options)
      }

      return result
    }

    return descriptor
  }
}

/**
 * 批量缓存装饰器
 */
export function CacheableBatch(
  options: CacheDecoratorOptions & {
    keyExtractor?: (item: unknown) => string
  } = {}
) {
  return function (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: unknown[]) {
      // 获取缓存实例
      const cacheName = options.cacheName || 'default'
      let cache = CacheFactory.get(cacheName)

      if (!cache) {
        // 创建默认缓存
        cache = CacheFactory.create(cacheName, {
          storage: 'memory',
          maxSize: 1000,
          defaultTTL: 5 * 60 * 1000 // 5分钟
        })
      }

      // 假设第一个参数是键数组
      const keys = args[0] as string[]
      if (!Array.isArray(keys)) {
        return originalMethod.apply(this, args)
      }

      // 批量获取缓存
      const cached = await cache.mget(keys)
      const missingKeys: string[] = []
      const results = new Map<string, unknown>()

      // 找出未命中的键
      for (const key of keys) {
        const value = cached.get(key)
        if (value !== null) {
          results.set(key, value)
        } else {
          missingKeys.push(key)
        }
      }

      // 如果有未命中的键，调用原方法
      if (missingKeys.length > 0) {
        const missingResults = await originalMethod.apply(this, [missingKeys, ...args.slice(1)])

        // 缓存新获取的结果
        if (Array.isArray(missingResults)) {
          const entries: Array<[string, unknown, CacheOptions?]> = []

          for (const item of missingResults) {
            const key = options.keyExtractor ? options.keyExtractor(item) : item.id
            if (key) {
              results.set(key, item)
              entries.push([key, item, options])
            }
          }

          if (entries.length > 0) {
            await cache.mset(entries)
          }
        }
      }

      // 返回所有结果
      return keys.map(key => results.get(key)).filter(Boolean)
    }

    return descriptor
  }
}
