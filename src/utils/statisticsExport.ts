interface StatisticsData {
  id: string
  title: string
  type: 'count' | 'percentage' | 'trend' | 'distribution' | 'comparison'
  category: string
  timeRange: {
    start: string
    end: string
    granularity: 'day' | 'week' | 'month' | 'quarter' | 'year'
  }
  metrics: MetricData[]
  charts: ChartData[]
   
  rawData: unknown[]
  summary: StatisticsSummary
  generateTime: string
}

interface MetricData {
  name: string
  value: number | string
  unit?: string
  change?: number
  changeType?: 'increase' | 'decrease' | 'stable'
  description?: string
}

interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'heatmap' | 'gauge'
  title: string
   
  data: unknown[]
  config: ChartConfig
}

interface ChartConfig {
  xAxis?: string
  yAxis?: string
  colors?: string[]
  showLegend?: boolean
  showGrid?: boolean
  animation?: boolean
}

interface StatisticsSummary {
  totalRecords: number
  keyInsights: string[]
  recommendations: string[]
  dataQuality: 'excellent' | 'good' | 'fair' | 'poor'
  completeness: number
}

interface ExportOptions {
  format: 'excel' | 'pdf' | 'csv' | 'json' | 'word'
  includeCharts: boolean
  includeRawData: boolean
  includeSummary: boolean
  chartFormat?: 'png' | 'svg' | 'base64'
  pageLayout?: 'portrait' | 'landscape'
  template?: string
  watermark?: string
}

interface ExportResult {
  success: boolean
  blob?: Blob
  filename: string
  size: number
  error?: string
}

/**
 * CLEAN-AUX-005: 统计数据导出功能
 * 支持多种格式的统计数据导出，包含图表、原始数据、汇总信息等
 */
export class StatisticsExport {
  private statisticsCache: Map<string, StatisticsData> = new Map()
  
  // 预定义统计类别
  private readonly categories = [
    { value: 'employee', label: '员工统计'
  },
    { value: 'department', label: '部门统计'
  },
    { value: 'position', label: '岗位统计'
  },
    { value: 'workflow', label: '流程统计'
  },
    { value: 'attendance', label: '考勤统计'
  },
    { value: 'performance', label: '绩效统计'
  },
    { value: 'salary', label: '薪酬统计'
  },
    { value: 'training', label: '培训统计'
  },
    { value: 'recruitment', label: '招聘统计' }
  ]
  
  // 导出统计数据
  async exportStatistics(
    statisticsId: string, 
    options: ExportOptions = {
      format: 'excel',
      includeCharts: true,
      includeRawData: true,
      includeSummary: true,
      chartFormat: 'png'
    }
  ): Promise<ExportResult> {
    try {
      const statistics = this.statisticsCache.get(statisticsId)
      if (!statistics) {
        return {
          success: false,
          filename: '',
          size: 0,
          error: '统计数据不存在'
        }
      }
      
      const filename = this.generateFilename(statistics, options.format)
      let blob: Blob
      
      switch (options.format) {
        case 'excel':
          blob = await this.exportAsExcel(statistics, options)
          break
        case 'pdf':
          blob = await this.exportAsPdf(statistics, options)
          break
        case 'csv':
          blob = this.exportAsCsv(statistics, options)
          break
        case 'json':
          blob = this.exportAsJson(statistics, options)
          break
        case 'word':
          blob = await this.exportAsWord(statistics, options)
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }
      
      return {
        success: true,
        blob,
        filename,
        size: blob.size
      }
      
    } catch (__error) {
      return {
        success: false,
        filename: '',
        size: 0,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }
  
  // 导出为Excel格式
  private async exportAsExcel(statistics: StatisticsData, options: ExportOptions): Promise<Blob> {
    const workbook = this.createExcelWorkbook(statistics, options)
    
    // 这里应该使用真实的Excel库，如exceljs
    // 为了演示，返回JSON格式
    return new Blob([JSON.stringify(workbook, null, 2)], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
  }
  
  // 创建Excel工作簿
  private createExcelWorkbook(statistics: StatisticsData, options: ExportOptions) {
   
    const workbook: unknown = { sheets: {} }
    
    // 概览表
    workbook.sheets['统计概览'] = {
      title: statistics.title,
      headers: ['指标名称', '指标值', '单位', '变化', '说明'],
      data: statistics.metrics.map(metric => [
        metric.name,
        metric.value,
        metric.unit || '',
        metric.change ? `${metric.change > 0 ? '+' : ''}${metric.change}` : '',
        metric.description || ''
      ]),
      summary: options.includeSummary ? {
        totalRecords: statistics.summary.totalRecords,
        dataQuality: statistics.summary.dataQuality,
        completeness: `${(statistics.summary.completeness * 100).toFixed(1)}%`,
        insights: statistics.summary.keyInsights.join('\n'),
        recommendations: statistics.summary.recommendations.join('\n')
      } : undefined
    }
    
    // 原始数据表
    if (options.includeRawData && statistics.rawData.length > 0) {
      const firstRow = statistics.rawData[0]
      const headers = Object.keys(firstRow)
      
      workbook.sheets['原始数据'] = {
        headers,
        data: statistics.rawData.map(row => headers.map(header => row[header]))
      }
    }
    
    // 图表数据表
    if (options.includeCharts) {
      statistics.charts.forEach((chart, index) => {
        const sheetName = `图表${index + 1}_${chart.title.substring(0, 10)}`
        workbook.sheets[sheetName] = {
          title: chart.title,
          type: chart.type,
          data: chart.data,
          config: chart.config
        }
      })
    }
    
    return workbook
  }
  
  // 导出为PDF格式
  private async exportAsPdf(statistics: StatisticsData, options: ExportOptions): Promise<Blob> {
    // 这里应该使用PDF库，如jsPDF或PDFKit
    // 为了演示，创建简单的PDF结构
    const pdfContent = this.createPdfContent(statistics, options)
    
    return new Blob([JSON.stringify(pdfContent)], { type: 'application/pdf' })
  }
  
  // 创建PDF内容
  private createPdfContent(statistics: StatisticsData, options: ExportOptions) {
    const content = {
      title: statistics.title,
      metadata: {
        generateTime: statistics.generateTime,
        timeRange: `${statistics.timeRange.start} 至 ${statistics.timeRange.end}`,
        category: statistics.category,
        pageLayout: options.pageLayout || 'portrait'
      },
      sections: []
    }
    
    // 添加概览部分
    content.sections.push({
      type: 'overview',
      title: '统计概览',
      content: statistics.metrics.map(metric => ({
        name: metric.name,
        value: metric.value,
        unit: metric.unit,
        change: metric.change,
        description: metric.description
      }))
    })
    
    // 添加图表部分
    if (options.includeCharts) {
      content.sections.push({
        type: 'charts',
        title: '数据图表',
        charts: statistics.charts.map(chart => ({
          title: chart.title,
          type: chart.type,
          data: chart.data,
          config: chart.config
        }))
      })
    }
    
    // 添加汇总部分
    if (options.includeSummary) {
      content.sections.push({
        type: 'summary',
        title: '数据汇总',
        content: {
          totalRecords: statistics.summary.totalRecords,
          dataQuality: statistics.summary.dataQuality,
          completeness: statistics.summary.completeness,
          insights: statistics.summary.keyInsights,
          recommendations: statistics.summary.recommendations
        }
      })
    }
    
    return content
  }
  
  // 导出为CSV格式
  private exportAsCsv(statistics: StatisticsData, options: ExportOptions): Blob {
    let csvContent = ''
    
    // 添加标题和元数据
    csvContent += `"${statistics.title}"\n`
    csvContent += `"生成时间","${statistics.generateTime}"\n`
    csvContent += `"时间范围","${statistics.timeRange.start} 至 ${statistics.timeRange.end}"\n`
    csvContent += '\n'
    
    // 添加指标数据
    csvContent += '"指标名称","指标值","单位","变化","说明"\n'
    for (const metric of statistics.metrics) {
      const row = [
        metric.name,
        metric.value,
        metric.unit || '',
        metric.change ? `${metric.change > 0 ? '+' : ''}${metric.change}` : '',
        metric.description || ''
      ]
      csvContent += row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',') + '\n'
    }
    
    // 添加原始数据
    if (options.includeRawData && statistics.rawData.length > 0) {
      csvContent += '\n"原始数据"\n'
      const firstRow = statistics.rawData[0]
      const headers = Object.keys(firstRow)
      
      csvContent += headers.map(header => `"${header}"`).join(',') + '\n'
      for (const row of statistics.rawData) {
        const values = headers.map(header => `"${String(row[header]).replace(/"/g, '""')}"`)
        csvContent += values.join(',') + '\n'
      }
    }
    
    // 添加BOM以支持中文
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
  }
  
  // 导出为JSON格式
  private exportAsJson(statistics: StatisticsData, options: ExportOptions): Blob {
    const data = {
      ...statistics,
      exportOptions: options,
      exportTime: new Date().toISOString()
    }
    
    if (!options.includeRawData) {
      delete data.rawData
    }
    
    if (!options.includeCharts) {
      delete data.charts
    }
    
    if (!options.includeSummary) {
      delete data.summary
    }
    
    return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json;charset=utf-8' })
  }
  
  // 导出为Word格式
  private async exportAsWord(statistics: StatisticsData, options: ExportOptions): Promise<Blob> {
    // 这里应该使用Word库，如docx
    // 为了演示，创建简单的Word结构
    const wordContent = this.createWordContent(statistics, options)
    
    return new Blob([JSON.stringify(wordContent)], { 
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
    })
  }
  
  // 创建Word内容
  private createWordContent(statistics: StatisticsData, options: ExportOptions) {
    return {
      title: statistics.title,
      paragraphs: [
        {
          text: statistics.title,
          style: 'title'
  },
        {
          text: `生成时间: ${statistics.generateTime}`,
          style: 'normal'
  },
        {
          text: `统计周期: ${statistics.timeRange.start} 至 ${statistics.timeRange.end}`,
          style: 'normal'
  },
        {
          text: '统计概览',
          style: 'heading1'
  },
        {
          type: 'table',
          headers: ['指标名称', '指标值', '单位', '变化', '说明'],
          rows: statistics.metrics.map(metric => [
            metric.name,
            String(metric.value),
            metric.unit || '',
            metric.change ? `${metric.change > 0 ? '+' : ''}${metric.change}` : '',
            metric.description || ''
          ])
        }
      ]
    }
  }
  
  // 生成文件名
  private generateFilename(statistics: StatisticsData, format: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const safeName = statistics.title.replace(/[^\w\u4e00-\u9fa5]/g, '_')
    return `${safeName}_${timestamp}.${format}`
  }
  
  // 批量导出统计数据
  async exportMultipleStatistics(
    statisticsIds: string[], 
    options: ExportOptions & { compress?: boolean } = { format: 'excel', includeCharts: true, includeRawData: false, includeSummary: true }
  ): Promise<ExportResult> {
    try {
      const results: Blob[] = []
      const filenames: string[] = []
      
      for (const id of statisticsIds) {
        const result = await this.exportStatistics(id, options)
        if (result.success && result.blob) {
          results.push(result.blob)
          filenames.push(result.filename)
        }
      }
      
      if (results.length === 0) {
        return {
          success: false,
          filename: '',
          size: 0,
          error: '没有可导出的统计数据'
        }
      }
      
      // 如果只有一个文件，直接返回
      if (results.length === 1) {
        return {
          success: true,
          blob: results[0],
          filename: filenames[0],
          size: results[0].size
        }
      }
      
      // 多个文件时，创建压缩包
      const zipBlob = await this.createZipFile(results, filenames)
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      
      return {
        success: true,
        blob: zipBlob,
        filename: `统计数据导出_${timestamp}.zip`,
        size: zipBlob.size
      }
      
    } catch (__error) {
      return {
        success: false,
        filename: '',
        size: 0,
        error: error instanceof Error ? error.message : '批量导出失败'
      }
    }
  }
  
  // 创建ZIP文件
  private async createZipFile(files: Blob[], filenames: string[]): Promise<Blob> {
    // 这里应该使用ZIP库，如JSZip
    // 为了演示，返回第一个文件
    return files[0]
  }
  
  // 缓存统计数据
  cacheStatistics(statistics: StatisticsData) {
    this.statisticsCache.set(statistics.id, statistics)
  }
  
  // 获取缓存的统计数据
  getCachedStatistics(id: string): StatisticsData | undefined {
    return this.statisticsCache.get(id)
  }
  
  // 清除统计数据缓存
  clearStatisticsCache(id?: string) {
    if (id) {
      this.statisticsCache.delete(id)
    } else {
      this.statisticsCache.clear()
    }
  }
  
  // 获取统计类别
  getCategories() {
    return this.categories
  }
  
  // 预览导出数据
  async previewExport(statisticsId: string, format: string): Promise<unknown> {
    const statistics = this.statisticsCache.get(statisticsId)
    if (!statistics) {
      throw new Error('统计数据不存在')
    }
    
    switch (format) {
      case 'excel':
        return this.createExcelWorkbook(statistics, { format: 'excel', includeCharts: true, includeRawData: true, includeSummary: true })
      case 'csv':
        return {
          type: 'text',
          content: statistics.metrics.map(m => `${m.name}: ${m.value}`).join('\n')
        }
      case 'json':
        return {
          type: 'json',
          content: statistics
        }
      default:
        throw new Error(`不支持预览格式: ${format}`)
    }
  }
  
  // 验证导出选项
  validateExportOptions(options: ExportOptions): { valid: boolean, errors: string[] } {
    const errors: string[] = []
    
    if (!['excel', 'pdf', 'csv', 'json', 'word'].includes(options.format)) {
      errors.push('不支持的导出格式')
    }
    
    if (options.chartFormat && !['png', 'svg', 'base64'].includes(options.chartFormat)) {
      errors.push('不支持的图表格式')
    }
    
    if (options.pageLayout && !['portrait', 'landscape'].includes(options.pageLayout)) {
      errors.push('不支持的页面布局')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// 全局实例
export const statisticsExporter = new StatisticsExport()

// 便捷函数
export async function exportStatisticsData(statisticsId: string, options?: Partial<ExportOptions>): Promise<ExportResult> {
  return statisticsExporter.exportStatistics(statisticsId, {
    format: 'excel',
    includeCharts: true,
    includeRawData: true,
    includeSummary: true,
    chartFormat: 'png',
    ...options
  })
}

export async function exportMultipleStatisticsData(statisticsIds: string[], options?: Partial<ExportOptions & { compress?: boolean }>): Promise<ExportResult> {
  return statisticsExporter.exportMultipleStatistics(statisticsIds, {
    format: 'excel',
    includeCharts: true,
    includeRawData: false,
    includeSummary: true,
    compress: false,
    ...options
  })
}