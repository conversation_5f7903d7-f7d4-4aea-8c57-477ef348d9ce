/**
 * 验证工具函数
 */

/**
 * 邮箱验证
 * @param email 邮箱
 */
export function isEmail(email: string): boolean {
  const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return regex.test(email)
}

/**
 * 手机号验证（中国大陆）
 * @param phone 手机号
 */
export function isPhone(phone: string): boolean {
  const regex = /^1[3-9]\d{9}$/
  return regex.test(phone)
}

/**
 * 电话号码验证（座机）
 * @param tel 电话号码
 */
export function isTel(tel: string): boolean {
  const regex = /^0\d{2,3}-?\d{7,8}$/
  return regex.test(tel)
}

/**
 * 身份证验证（中国大陆）
 * @param idCard 身份证号
 */
export function isIdCard(idCard: string): boolean {
  const regex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (!regex.test(idCard)) return false

  // 18位身份证校验
  if (idCard.length === 18) {
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

    let sum = 0
    for (let i = 0; i < 17; i++) {
      sum += parseInt(idCard[i]) * weights[i]
    }

    const checkCode = checkCodes[sum % 11]
    return idCard[17].toUpperCase() === checkCode
  }

  return true
}

/**
 * URL验证
 * @param url URL
 */
export function isUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * IP地址验证
 * @param ip IP地址
 */
export function isIP(ip: string): boolean {
  const ipv4Regex =
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  const ipv6Regex =
    /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/

  return ipv4Regex.test(ip) || ipv6Regex.test(ip)
}

/**
 * 邮政编码验证（中国大陆）
 * @param postcode 邮政编码
 */
export function isPostcode(postcode: string): boolean {
  const regex = /^[1-9]\d{5}$/
  return regex.test(postcode)
}

/**
 * 用户名验证
 * @param username 用户名
 * @param options 选项
 */
export function isUsername(
  username: string,
  options: {
    minLength?: number
    maxLength?: number
    allowChinese?: boolean
    allowSpecial?: boolean
  } = {}
): boolean {
  const { minLength = 3, maxLength = 20, allowChinese = false, allowSpecial = false } = options

  if (username.length < minLength || username.length > maxLength) {
    return false
  }

  let regex: RegExp
  if (allowChinese && allowSpecial) {
    regex = /^[\u4e00-\u9fa5a-zA-Z0-9_@.-]+$/
  } else if (allowChinese) {
    regex = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/
  } else if (allowSpecial) {
    regex = /^[a-zA-Z0-9_@.-]+$/
  } else {
    regex = /^[a-zA-Z0-9_]+$/
  }

  return regex.test(username)
}

/**
 * 银行卡号验证（Luhn算法）
 * @param cardNumber 银行卡号
 */
export function isBankCard(cardNumber: string): boolean {
  const digits = cardNumber.replace(/\s/g, '')
  if (!/^\d+$/.test(digits)) return false

  let sum = 0
  let isEven = false

  for (let i = digits.length - 1; i >= 0; i--) {
    let digit = parseInt(digits[i])

    if (isEven) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }

    sum += digit
    isEven = !isEven
  }

  return sum % 10 === 0
}

/**
 * 社会信用代码验证（中国大陆）
 * @param code 社会信用代码
 */
export function isSocialCreditCode(code: string): boolean {
  const regex = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/
  if (!regex.test(code)) return false

  const weights = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28]
  const codes = '0123456789ABCDEFGHJKLMNPQRTUWXY'

  let sum = 0
  for (let i = 0; i < 17; i++) {
    sum += codes.indexOf(code[i]) * weights[i]
  }

  const checkCode = codes[31 - (sum % 31)]
  return code[17] === checkCode
}

/**
 * 车牌号验证（中国大陆）
 * @param plateNumber 车牌号
 */
export function isPlateNumber(plateNumber: string): boolean {
  const regex =
    /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/
  return regex.test(plateNumber)
}

/**
 * 日期格式验证
 * @param date 日期字符串
 * @param format 格式
 */
export function isDateFormat(date: string, format: string = 'YYYY-MM-DD'): boolean {
  const patterns: Record<string, RegExp> = {
    'YYYY-MM-DD': /^\d{4}-\d{2}-\d{2}$/,
    'YYYY/MM/DD': /^\d{4}\/\d{2}\/\d{2}$/,
    'DD-MM-YYYY': /^\d{2}-\d{2}-\d{4}$/,
    'DD/MM/YYYY': /^\d{2}\/\d{2}\/\d{4}$/,
    'YYYY-MM-DD HH:mm:ss': /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,
    'YYYY/MM/DD HH:mm:ss': /^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/
  }

  const pattern = patterns[format]
  if (!pattern) return false

  if (!pattern.test(date)) return false

  // 验证日期有效性
  const d = new Date(date)
  return d instanceof Date && !isNaN(d.getTime())
}

/**
 * 数字范围验证
 * @param value 值
 * @param min 最小值
 * @param max 最大值
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return value >= min && value <= max
}

/**
 * 字符串长度验证
 * @param str 字符串
 * @param min 最小长度
 * @param max 最大长度
 */
export function isLength(str: string, min: number, max?: number): boolean {
  const len = str.length
  return max !== undefined ? len >= min && len <= max : len >= min
}

/**
 * 整数验证
 * @param value 值
 */
export function isInteger(value: unknown): boolean {
  return Number.isInteger(Number(value))
}

/**
 * 正整数验证
 * @param value 值
 */
export function isPositiveInteger(value: unknown): boolean {
  const num = Number(value)
  return Number.isInteger(num) && num > 0
}

/**
 * 负整数验证
 * @param value 值
 */
export function isNegativeInteger(value: unknown): boolean {
  const num = Number(value)
  return Number.isInteger(num) && num < 0
}

/**
 * 小数验证
 * @param value 值
 * @param decimals 小数位数
 */
export function isDecimal(value: unknown, decimals?: number): boolean {
  const regex = decimals ? new RegExp(`^-?\\d+\\.\\d{1,${decimals}}$`) : /^-?\d+\.\d+$/
  return regex.test(String(value))
}

/**
 * 中文验证
 * @param str 字符串
 */
export function isChinese(str: string): boolean {
  const regex = /^[\u4e00-\u9fa5]+$/
  return regex.test(str)
}

/**
 * 英文验证
 * @param str 字符串
 */
export function isEnglish(str: string): boolean {
  const regex = /^[a-zA-Z]+$/
  return regex.test(str)
}

/**
 * 大写字母验证
 * @param str 字符串
 */
export function isUpperCase(str: string): boolean {
  return str === str.toUpperCase()
}

/**
 * 小写字母验证
 * @param str 字符串
 */
export function isLowerCase(str: string): boolean {
  return str === str.toLowerCase()
}

/**
 * Base64验证
 * @param str 字符串
 */
export function isBase64(str: string): boolean {
  const regex = /^[A-Za-z0-9+/]*={0,2}$/
  return regex.test(str) && str.length % 4 === 0
}

/**
 * MD5验证
 * @param str 字符串
 */
export function isMD5(str: string): boolean {
  const regex = /^[a-f0-9]{32}$/i
  return regex.test(str)
}

/**
 * UUID验证
 * @param str 字符串
 */
export function isUUID(str: string): boolean {
  const regex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return regex.test(str)
}

/**
 * 颜色值验证
 * @param color 颜色值
 */
export function isColor(color: string): boolean {
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  const rgbRegex = /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/
  const rgbaRegex = /^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(0|1|0?\.\d+)\s*\)$/

  return hexRegex.test(color) || rgbRegex.test(color) || rgbaRegex.test(color)
}
