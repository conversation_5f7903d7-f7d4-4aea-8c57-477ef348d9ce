/**
 * @name g6Util
 * @description G6 图表库动态导入工具
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */

// G6 实例缓存
let G6Module: typeof import('@antv/g6') | null = null

/**
 * 动态导入 G6
 */
export async function importG6() {
  if (!G6Module) {
    G6Module = await import('@antv/g6')
  }
  return G6Module.default || G6Module
}

/**
 * 创建 G6 Graph 实例
 * @param container 容器元素
 * @param options 配置选项
 */
export async function createGraph(
  container: string | HTMLElement,
  options: Record<string, unknown>
): Promise<unknown> {
  const G6 = await importG6()
  return new G6.Graph({
    container,
    ...options
  })
}

/**
 * 创建 G6 TreeGraph 实例
 * @param container 容器元素
 * @param options 配置选项
 */
export async function createTreeGraph(
  container: string | HTMLElement,
  options: Record<string, unknown>
): Promise<unknown> {
  const G6 = await importG6()
  return new G6.TreeGraph({
    container,
    ...options
  })
}

/**
 * 获取组织架构图默认配置
 */
export function getOrgChartConfig(): unknown {
  return {
    modes: {
      default: ['drag-canvas', 'zoom-canvas']
    },
    defaultNode: {
      type: 'rect',
      size: [180, 60],
      style: {
        fill: '#409EFF',
        stroke: '#409EFF',
        radius: 4
      },
      labelCfg: {
        style: {
          fill: '#ffffff',
          fontSize: 14
        }
      }
    },
    defaultEdge: {
      type: 'cubic-vertical',
      style: {
        stroke: '#A3B1BF'
      }
    },
    layout: {
      type: 'compactBox',
      direction: 'TB',
      getId: (d: { id: string }) => d.id,
      getHeight: () => 60,
      getWidth: () => 180,
      getVGap: () => 20,
      getHGap: () => 20
    }
  }
}

/**
 * 销毁 G6 实例
 * @param graph G6 实例
 */
interface GraphInstance {
  destroy?: () => void
}

export function destroyGraph(graph: unknown) {
  const graphInstance = graph as GraphInstance
  if (graphInstance && typeof graphInstance.destroy === 'function') {
    graphInstance.destroy()
  }
}
