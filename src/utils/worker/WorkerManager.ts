/**
 * Web Worker管理器
 * 提供Worker池、任务队列、错误处理等功能
 */

import { ref } from 'vue'

// Worker任务类型
export interface WorkerTask {
  id: string
  type: string
  data: Record<string, unknown>
  priority?: number
  timeout?: number
  transferable?: Transferable[]
}

// Worker响应类型
export interface WorkerResponse<T = unknown> {
  id: string
  type: 'success' | 'error' | 'progress'
  data?: T
  error?: string
  progress?: number
}

// Worker配置
export interface WorkerConfig {
  maxWorkers?: number
  taskTimeout?: number
  enableSharedArrayBuffer?: boolean
  workerPath?: string
}

// Worker状态
export interface WorkerState {
  id: number
  busy: boolean
  currentTask?: WorkerTask
  taskCount: number
  errors: number
  lastActive: number
}

export class WorkerManager {
  private static instance: WorkerManager
  private workers: Map<number, Worker> = new Map()
  private workerStates: Map<number, WorkerState> = new Map()
  private taskQueue: WorkerTask[] = []
  private pendingTasks: Map<
    string,
    {
      resolve: (value: unknown) => void
      reject: (error: Error) => void
      timeout?: NodeJS.Timeout
    }
  > = new Map()

  private config: Required<WorkerConfig> = {
    maxWorkers: navigator.hardwareConcurrency || 4,
    taskTimeout: 30000,
    enableSharedArrayBuffer: typeof SharedArrayBuffer !== 'undefined',
    workerPath: '/worker.js'
  }

  private workerCode = `
    // Worker全局状态
    const state = {
      cache: new Map(),
      initialized: false
    }
    
    // 消息处理器
    self.addEventListener('message', async (event) => {
      const {id, type, data} = event.data
      
      try {
        let result
        
        switch (type) {
          case 'init':
            result = await this.initialize(data)
            break
          case 'process':
            result = await this.processChunks(data)
            break
          case 'compute':
            result = await this.compute(data)
            break
          case 'analyze':
            result = await this.analyze(data)
            break
          default:
            throw new Error('Unknown task type: ' + type)
        }
        
        // 发送成功响应
        self.postMessage({
          id,
          type: 'success',
          data: result
        })
      } catch (error) {
        // 发送错误响应
        self.postMessage({
          id,
          type: 'error',
          error: error.message
        })
      }
    })
    
    // 初始化
    async function initialize(data) {
      state.initialized = true
      return { status: 'initialized' }
    }
    
    // 处理数据块
    async function processChunks(data) {
      const {chunks, processor} = data
      const processChunk = new Function('chunk', processor)
      const results = []
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i]
        const result = await processChunk(chunk, processor)
        results.push(result)
        
        // 发送进度
        self.postMessage({
          id: event.data.id,
          type: 'progress',
          progress: ((i + 1) / chunks.length) * 100
        })
      }
      
      return results
    }
    
    // 统计计算
    function computeStatistics(values) {
      const n = values.length
      if (n === 0) return null
      
      // 排序
      const sorted = [...values].sort((a, b) => a - b)
      
      // 基本统计
      const sum = values.reduce((a, b) => a + b, 0)
      const mean = sum / n
      
      // 方差和标准差
      const variance = values.reduce((acc, val) => 
        acc + Math.pow(val - mean, 2), 0) / n
      const stdDev = Math.sqrt(variance)
      
      // 分位数
      const q1 = sorted[Math.floor(n * 0.25)]
      const median = sorted[Math.floor(n * 0.5)]
      const q3 = sorted[Math.floor(n * 0.75)]
      
      return {
        count: n,
        sum,
        mean,
        median,
        mode: findMode(values),
        variance,
        stdDev,
        min: sorted[0],
        max: sorted[n - 1],
        q1,
        q3,
        iqr: q3 - q1,
        skewness: computeSkewness(values, mean, stdDev),
        kurtosis: computeKurtosis(values, mean, stdDev)
      }
    }
    
    // 聚合计算
    function computeAggregation(data) {
      const {values, groupBy, metrics} = data
      const groups = new Map()
      
      // 按字段分组
      values.forEach(item => {
        const key = groupBy.map(field => item[field]).join('_')
        if (!groups.has(key)) {
          groups.set(key, [])
        }
        groups.get(key).push(item)
      })
      
      // 计算每组的指标
      const results = []
      groups.forEach((groupData, key) => {
        const result = { _group: key }
        
        metrics.forEach(metric => {
          const values = groupData.map(item => item[metric.field])
          
          switch (metric.type) {
            case 'sum':
              result[metric.name] = values.reduce((a, b) => a + b, 0)
              break
            case 'avg':
              result[metric.name] = values.reduce((a, b) => a + b, 0) / values.length
              break
            case 'count':
              result[metric.name] = values.length
              break
            case 'min':
              result[metric.name] = Math.min(...values)
              break
            case 'max':
              result[metric.name] = Math.max(...values)
              break
          }
        })
        
        results.push(result)
      })
      
      return results
    }
    
    // 相关性计算
    function computeCorrelation(x, y, method) {
      // 简化的相关性计算
      const n = x.length
      const sumX = x.reduce((a, b) => a + b)
      const sumY = y.reduce((a, b) => a + b)
      const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0)
      const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0)
      const sumY2 = y.reduce((sum, yi) => sum + yi * yi, 0)
      
      const coefficient = (n * sumXY - sumX * sumY) / 
        Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY))
      
      return {
        coefficient: isNaN(coefficient) ? 0 : coefficient,
        pValue: 0.05 // 简化的p值
      }
    }
    
    // 异常检测
    function detectOutliers(data, method, threshold) {
      const values = data.map(d => d.value)
      const outliers = []
      const indices = []
      
      if (method === 'zscore') {
        const mean = values.reduce((a, b) => a + b) / values.length
        const std = Math.sqrt(
          values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
        )
        
        data.forEach((item, index) => {
          const zscore = Math.abs((item.value - mean) / std)
          if (zscore > threshold) {
            outliers.push(item)
            indices.push(index)
          }
        })
      } else if (method === 'iqr') {
        const sorted = [...values].sort((a, b) => a - b)
        const q1 = sorted[Math.floor(values.length * 0.25)]
        const q3 = sorted[Math.floor(values.length * 0.75)]
        const iqr = q3 - q1
        const lower = q1 - threshold * iqr
        const upper = q3 + threshold * iqr
        
        data.forEach((item, index) => {
          if (item.value < lower || item.value > upper) {
            outliers.push(item)
            indices.push(index)
          }
        })
      }
      
      return { outliers, indices }
    }
    
    // 计算主函数
    async function compute(data) {
      const {operation, ...params} = data
      
      switch (operation) {
        case 'statistics':
          return computeStatistics(params.values)
        case 'aggregation':
          return computeAggregation(params)
        case 'correlation':
          return computeCorrelation(params.x, params.y, params.method)
        default:
          throw new Error('Unknown compute operation: ' + operation)
      }
    }
    
    // 分析主函数
    async function analyze(data) {
      const {method, dataset, params} = data
      
      switch (method) {
        case 'outliers':
          return detectOutliers(dataset, params.method, params.threshold)
        default:
          throw new Error('Unknown analyze method: ' + method)
      }
    }
    
    // 辅助函数
    function findMode(values) {
      const frequency = {}
      let maxFreq = 0
      let mode = values[0]
      
      values.forEach(val => {
        frequency[val] = (frequency[val] || 0) + 1
        if (frequency[val] > maxFreq) {
          maxFreq = frequency[val]
          mode = val
        }
      })
      
      return mode
    }
    
    function computeSkewness(values, mean, stdDev) {
      const n = values.length
      const sum = values.reduce((acc, val) => 
        acc + Math.pow((val - mean) / stdDev, 3), 0)
      return (n / ((n - 1) * (n - 2))) * sum
    }
    
    function computeKurtosis(values, mean, stdDev) {
      const n = values.length
      const sum = values.reduce((acc, val) => 
        acc + Math.pow((val - mean) / stdDev, 4), 0)
      return ((n * (n + 1)) / ((n - 1) * (n - 2) * (n - 3))) * sum - 
        (3 * (n - 1) * (n - 1)) / ((n - 2) * (n - 3))
    }
  `

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): WorkerManager {
    if (!WorkerManager.instance) {
      WorkerManager.instance = new WorkerManager()
      WorkerManager.instance.initialize()
    }
    return WorkerManager.instance
  }

  /**
   * 初始化Worker池
   */
  private initialize(): void {
    // 创建Worker
    const blob = new Blob([this.workerCode], { type: 'application/javascript' })
    const workerUrl = URL.createObjectURL(blob)

    for (let i = 0; i < this.config.maxWorkers; i++) {
      const worker = new Worker(workerUrl)
      const state: WorkerState = {
        id: i,
        busy: false,
        taskCount: 0,
        errors: 0,
        lastActive: Date.now()
      }

      // 设置消息处理器
      worker.addEventListener('message', event => {
        this.handleWorkerMessage(i, event.data)
      })

      worker.addEventListener('error', error => {
        this.handleWorkerError(i, error)
      })

      this.workers.set(i, worker)
      this.workerStates.set(i, state)

      // 初始化Worker
      this.postToWorker(i, {
        id: `init_${i}`,
        type: 'init',
        data: {}
      })
    }
  }

  /**
   * 执行任务
   */
  async execute<T = unknown>(task: Omit<WorkerTask, 'id'>): Promise<T> {
    const taskId = this.generateTaskId()
    const fullTask: WorkerTask = {
      ...task,
      id: taskId,
      priority: task.priority ?? 5
    }

    return new Promise((resolve, reject) => {
      // 保存Promise回调
      const timeout = task.timeout ?? this.config.taskTimeout
      const timeoutId = setTimeout(() => {
        this.pendingTasks.delete(taskId)
        reject(new Error(`Task ${taskId} timeout after ${timeout}ms`))
      }, timeout)

      this.pendingTasks.set(taskId, { resolve, reject, timeout: timeoutId })

      // 添加到任务队列
      this.enqueueTask(fullTask)

      // 尝试分配任务
      this.assignTasks()
    })
  }

  /**
   * 批量执行任务
   */
  async executeBatch<T = unknown>(tasks: Array<Omit<WorkerTask, 'id'>>): Promise<T[]> {
    const promises = tasks.map(task => this.execute<T>(task))
    return Promise.all(promises)
  }

  /**
   * 并行处理数据
   */
  async parallel<T, R>(
    data: T[],
    processor: (chunk: T[]) => R,
    options?: {
      chunkSize?: number
      progress?: (percentage: number) => void
    }
  ): Promise<R[]> {
    const chunkSize = options?.chunkSize ?? Math.ceil(data.length / this.config.maxWorkers)
    const chunks: T[][] = []

    // 分块
    for (let i = 0; i < data.length; i += chunkSize) {
      chunks.push(data.slice(i, i + chunkSize))
    }

    let completed = 0
    const results: R[] = new Array(chunks.length)

    // 并行处理
    const promises = chunks.map((chunk, index) =>
      this.execute<R>({
        type: 'process',
        data: {
          chunks: [chunk],
          processor: processor.toString()
        }
      }).then(result => {
        results[index] = result[0]
        completed++

        if (options?.progress) {
          options.progress((completed / chunks.length) * 100)
        }

        return result[0]
      })
    )

    await Promise.all(promises)
    return results
  }

  /**
   * 计算统计信息
   */
  async computeStatistics(values: number[]): Promise<{
    count: number
    sum: number
    mean: number
    median: number
    mode: number
    variance: number
    stdDev: number
    min: number
    max: number
    q1: number
    q3: number
    iqr: number
    skewness: number
    kurtosis: number
  }> {
    return this.execute({
      type: 'compute',
      data: {
        operation: 'statistics',
        values
      }
    })
  }

  /**
   * 数据聚合
   */
  async aggregate<T extends Record<string, unknown>>(
    data: T[],
    groupBy: string[],
    metrics: Array<{
      field: string
      type: 'sum' | 'avg' | 'count' | 'min' | 'max'
      name: string
    }>
  ): Promise<Array<Record<string, unknown>>> {
    return this.execute({
      type: 'compute',
      data: {
        operation: 'aggregation',
        values: data,
        groupBy,
        metrics
      }
    })
  }

  /**
   * 相关性分析
   */
  async correlation(
    x: number[],
    y: number[],
    method: 'pearson' | 'spearman' | 'kendall' = 'pearson'
  ): Promise<{ coefficient: number; pValue: number }> {
    return this.execute({
      type: 'compute',
      data: {
        operation: 'correlation',
        x,
        y,
        method
      }
    })
  }

  /**
   * 异常检测
   */
  async detectOutliers<T extends { value: number }>(
    data: T[],
    method: 'iqr' | 'zscore' | 'isolation' = 'iqr',
    threshold: number = 1.5
  ): Promise<{ outliers: T[]; indices: number[] }> {
    return this.execute({
      type: 'analyze',
      data: {
        method: 'outliers',
        dataset: data,
        params: { method, threshold }
      }
    })
  }

  /**
   * 处理Worker消息
   */
  private handleWorkerMessage(workerId: number, message: WorkerResponse<unknown>): void {
    const { id, type, data, error, progress } = message
    const pending = this.pendingTasks.get(id)

    if (!pending) return

    switch (type) {
      case 'success':
        clearTimeout(pending.timeout)
        pending.resolve(data)
        this.pendingTasks.delete(id)
        this.completeTask(workerId)
        break

      case 'error':
        clearTimeout(pending.timeout)
        pending.reject(new Error(error))
        this.pendingTasks.delete(id)
        this.completeTask(workerId)
        break

      case 'progress':
        // 进度更新，不结束任务
        break
    }
  }

  /**
   * 处理Worker错误
   */
  private handleWorkerError(workerId: number, error: ErrorEvent): void {
    const state = this.workerStates.get(workerId)
    if (state) {
      state.errors++

      // 如果有当前任务，标记失败
      if (state.currentTask) {
        const pending = this.pendingTasks.get(state.currentTask.id)
        if (pending) {
          clearTimeout(pending.timeout)
          pending.reject(error)
          this.pendingTasks.delete(state.currentTask.id)
        }
      }

      // 重启Worker
      this.restartWorker(workerId)
    }
  }

  /**
   * 重启Worker
   */
  private restartWorker(workerId: number): void {
    const oldWorker = this.workers.get(workerId)
    if (oldWorker) {
      oldWorker.terminate()
    }

    // 创建新Worker
    const blob = new Blob([this.workerCode], { type: 'application/javascript' })
    const workerUrl = URL.createObjectURL(blob)
    const worker = new Worker(workerUrl)

    worker.addEventListener('message', event => {
      this.handleWorkerMessage(workerId, event.data)
    })

    worker.addEventListener('error', error => {
      this.handleWorkerError(workerId, error)
    })

    this.workers.set(workerId, worker)

    // 重置状态
    const state = this.workerStates.get(workerId)
    if (state) {
      state.busy = false
      state.currentTask = undefined
    }

    // 初始化
    this.postToWorker(workerId, {
      id: `init_${workerId}_restart`,
      type: 'init',
      data: {}
    })
  }

  /**
   * 任务入队
   */
  private enqueueTask(task: WorkerTask): void {
    // 按优先级插入
    const insertIndex = this.taskQueue.findIndex(t => (t.priority ?? 5) < (task.priority ?? 5))

    if (insertIndex === -1) {
      this.taskQueue.push(task)
    } else {
      this.taskQueue.splice(insertIndex, 0, task)
    }
  }

  /**
   * 分配任务
   */
  private assignTasks(): void {
    while (this.taskQueue.length > 0) {
      const workerId = this.getIdleWorker()
      if (workerId === -1) break

      const task = this.taskQueue.shift()!
      this.assignTaskToWorker(workerId, task)
    }
  }

  /**
   * 获取空闲Worker
   */
  private getIdleWorker(): number {
    for (const [id, state] of this.workerStates) {
      if (!state.busy) {
        return id
      }
    }
    return -1
  }

  /**
   * 分配任务给Worker
   */
  private assignTaskToWorker(workerId: number, task: WorkerTask): void {
    const state = this.workerStates.get(workerId)
    if (!state) return

    state.busy = true
    state.currentTask = task
    state.taskCount++
    state.lastActive = Date.now()

    this.postToWorker(workerId, task)
  }

  /**
   * 完成任务
   */
  private completeTask(workerId: number): void {
    const state = this.workerStates.get(workerId)
    if (!state) return

    state.busy = false
    state.currentTask = undefined
    state.lastActive = Date.now()

    // 继续分配任务
    this.assignTasks()
  }

  /**
   * 发送消息到Worker
   */
  private postToWorker(workerId: number, task: WorkerTask): void {
    const worker = this.workers.get(workerId)
    if (!worker) return

    if (task.transferable) {
      worker.postMessage(task, task.transferable)
    } else {
      worker.postMessage(task)
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取Worker状态
   */
  getWorkerStates(): WorkerState[] {
    return Array.from(this.workerStates.values())
  }

  /**
   * 获取任务队列长度
   */
  getQueueLength(): number {
    return this.taskQueue.length
  }

  /**
   * 终止所有Worker
   */
  terminate(): void {
    for (const worker of this.workers.values()) {
      worker.terminate()
    }

    this.workers.clear()
    this.workerStates.clear()
    this.taskQueue = []

    // 拒绝所有待处理任务
    for (const [id, pending] of this.pendingTasks) {
      clearTimeout(pending.timeout)
      pending.reject(new Error('Worker manager terminated'))
    }

    this.pendingTasks.clear()
  }
}

// 导出单例
export const workerManager = WorkerManager.getInstance()

// 导出Vue组合式API
export function useWorkerManager() {
  const workerStates = ref(workerManager.getWorkerStates())
  const queueLength = ref(workerManager.getQueueLength())

  const updateStatus = () => {
    workerStates.value = workerManager.getWorkerStates()
    queueLength.value = workerManager.getQueueLength()
  }

  // 定期更新状态
  const interval = setInterval(updateStatus, 1000)

  // 清理
  const cleanup = () => {
    clearInterval(interval)
  }

  return {
    workerStates,
    queueLength,
    execute: workerManager.execute.bind(workerManager),
    parallel: workerManager.parallel.bind(workerManager),
    computeStatistics: workerManager.computeStatistics.bind(workerManager),
    aggregate: workerManager.aggregate.bind(workerManager),
    correlation: workerManager.correlation.bind(workerManager),
    detectOutliers: workerManager.detectOutliers.bind(workerManager),
    cleanup
  }
}
