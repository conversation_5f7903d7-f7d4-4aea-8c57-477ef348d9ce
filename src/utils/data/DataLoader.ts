/**
 * 数据加载优化工具类
 * 提供分页加载、虚拟滚动、懒加载等优化策略
 */

import { ref, Ref, shallowRef } from 'vue'

export interface LoadOptions {
  pageSize?: number // 分页大小
  preloadPages?: number // 预加载页数
  cacheSize?: number // 缓存大小
  enableVirtualScroll?: boolean // 启用虚拟滚动
  enableLazyLoad?: boolean // 启用懒加载
  debounceTime?: number // 防抖时间
  retryTimes?: number // 重试次数
  timeout?: number // 超时时间
}

export interface DataPage<T> {
  page: number
  size: number
  total: number
  data: T[]
  hasMore: boolean
}

export interface LoadProgress {
  loaded: number
  total: number
  percentage: number
  status: 'idle' | 'loading' | 'success' | 'error'
  error?: Error
}

export class DataLoader<T = unknown> {
  private cache: Map<string, DataPage<T>> = new Map()
  private loadingMap: Map<string, Promise<DataPage<T>>> = new Map()
  private defaultOptions: LoadOptions = {
    pageSize: 20,
    preloadPages: 1,
    cacheSize: 100,
    enableVirtualScroll: true,
    enableLazyLoad: true,
    debounceTime: 300,
    retryTimes: 3,
    timeout: 30000
  }

  constructor(private options: LoadOptions = {}) {
    this.options = { ...this.defaultOptions, ...options }
  }

  /**
   * 创建分页加载器
   */
  usePagination(
    fetchFn: (page: number, size: number) => Promise<DataPage<T>>,
    initialPage: number = 1
  ) {
    const data = shallowRef<T[]>([])
    const loading = ref(false)
    const error = ref<Error | null>(null)
    const hasMore = ref(true)
    const currentPage = ref(initialPage)
    const total = ref(0)

    const loadPage = async (page: number = currentPage.value, append: boolean = false) => {
      const cacheKey = `page_${page}_${this.options.pageSize}`

      // 检查缓存
      if (this.cache.has(cacheKey) && !append) {
        const cached = this.cache.get(cacheKey)!
        data.value = cached.data
        total.value = cached.total
        hasMore.value = cached.hasMore
        return cached
      }

      // 检查是否正在加载
      if (this.loadingMap.has(cacheKey)) {
        return this.loadingMap.get(cacheKey)!
      }

      loading.value = true
      error.value = null

      const loadPromise = this.loadWithRetry(
        () => fetchFn(page, this.options.pageSize!),
        this.options.retryTimes!
      )

      this.loadingMap.set(cacheKey, loadPromise)

      try {
        const result = await loadPromise

        // 更新缓存
        this.cache.set(cacheKey, result)
        this.maintainCacheSize()

        // 更新数据
        if (append) {
          data.value = [...data.value, ...result.data]
        } else {
          data.value = result.data
        }

        total.value = result.total
        hasMore.value = result.hasMore
        currentPage.value = page

        // 预加载下一页
        if (this.options.preloadPages! > 0 && result.hasMore) {
          this.preloadPages(page, fetchFn)
        }

        return result
      } catch (__err) {
        error.value = err as Error
        throw err
      } finally {
        loading.value = false
        this.loadingMap.delete(cacheKey)
      }
    }

    const loadMore = () => {
      if (!loading.value && hasMore.value) {
        return loadPage(currentPage.value + 1, true)
      }
    }

    const refresh = () => {
      this.cache.clear()
      return loadPage(1)
    }

    return {
      data,
      loading,
      error,
      hasMore,
      currentPage,
      total,
      loadPage,
      loadMore,
      refresh
    }
  }

  /**
   * 创建虚拟滚动加载器
   */
  useVirtualScroll<T>(
    items: Ref<T[]>,
    itemHeight: number,
    containerHeight: number,
    buffer: number = 5
  ) {
    const visibleItems = shallowRef<T[]>([])
    const startIndex = ref(0)
    const endIndex = ref(0)
    const offsetY = ref(0)
    const totalHeight = ref(0)

    const visibleCount = Math.ceil(containerHeight / itemHeight) + buffer * 2

    const updateVisibleItems = (scrollTop: number) => {
      const start = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer)
      const end = Math.min(items.value.length, start + visibleCount)

      startIndex.value = start
      endIndex.value = end
      offsetY.value = start * itemHeight

      visibleItems.value = items.value.slice(start, end)
    }

    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement
      updateVisibleItems(target.scrollTop)
    }

    // 初始化
    updateVisibleItems(0)
    totalHeight.value = items.value.length * itemHeight

    return {
      visibleItems,
      startIndex,
      endIndex,
      offsetY,
      totalHeight,
      handleScroll,
      updateVisibleItems
    }
  }

  /**
   * 创建懒加载器
   */
  useLazyLoad(
    fetchFn: () => Promise<T[]>,
    options?: {
      threshold?: number
      rootMargin?: string
    }
  ) {
    const data = shallowRef<T[]>([])
    const loading = ref(false)
    const loaded = ref(false)
    const error = ref<Error | null>(null)

    let observer: IntersectionObserver | null = null

    const load = async () => {
      if (loading.value || loaded.value) return

      loading.value = true
      error.value = null

      try {
        const result = await this.loadWithRetry(fetchFn, this.options.retryTimes!)
        data.value = result
        loaded.value = true
      } catch (__err) {
        error.value = err as Error
      } finally {
        loading.value = false
      }
    }

    const observe = (element: Element) => {
      if (observer) {
        observer.disconnect()
      }

      observer = new IntersectionObserver(
        _entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting && !loaded.value) {
              load()
            }
          })
        },
        {
          threshold: options?.threshold || 0.1,
          rootMargin: options?.rootMargin || '100px'
        }
      )

      observer.observe(element)
    }

    const disconnect = () => {
      if (observer) {
        observer.disconnect()
        observer = null
      }
    }

    return {
      data,
      loading,
      loaded,
      error,
      load,
      observe,
      disconnect
    }
  }

  /**
   * 创建流式加载器
   */
  useStreamLoader(streamFn: (onData: (item: T) => void, onEnd: () => void) => () => void) {
    const items = shallowRef<T[]>([])
    const loading = ref(false)
    const progress = ref<LoadProgress>({
      loaded: 0,
      total: 0,
      percentage: 0,
      status: 'idle'
    })

    let unsubscribe: (() => void) | null = null

    const start = () => {
      if (loading.value) return

      loading.value = true
      progress.value.status = 'loading'
      items.value = []

      unsubscribe = streamFn(
        (item: T) => {
          items.value = [...items.value, item]
          progress.value.loaded++

          if (progress.value.total > 0) {
            progress.value.percentage = Math.round(
              (progress.value.loaded / progress.value.total) * 100
            )
          }
        },
        () => {
          loading.value = false
          progress.value.status = 'success'
          progress.value.percentage = 100
        }
      )
    }

    const stop = () => {
      if (unsubscribe) {
        unsubscribe()
        unsubscribe = null
      }
      loading.value = false
      progress.value.status = 'idle'
    }

    return {
      items,
      loading,
      progress,
      start,
      stop
    }
  }

  /**
   * 批量加载数据
   */
  async batchLoad<K, V>(
    keys: K[],
    loader: (keys: K[]) => Promise<Map<K, V>>,
    batchSize: number = 100
  ): Promise<Map<K, V>> {
    const results = new Map<K, V>()
    const batches = this.chunk(keys, batchSize)

    const batchPromises = batches.map(async (batch, index) => {
      // 添加延迟避免并发过高
      await this.delay(index * 100)
      return loader(batch)
    })

    const batchResults = await Promise.all(batchPromises)

    // 合并结果
    batchResults.forEach(batchResult => {
      batchResult.forEach((value, key) => {
        results.set(key, value)
      })
    })

    return results
  }

  /**
   * 带重试的加载
   */
  private async loadWithRetry<T>(fn: () => Promise<T>, retries: number): Promise<T> {
    let lastError: Error | null = null

    for (let i = 0; i <= retries; i++) {
      try {
        return await this.withTimeout(fn(), this.options.timeout!)
      } catch (__error) {
        lastError = error as Error

        if (i < retries) {
          // 指数退避
          await this.delay(Math.pow(2, i) * 1000)
        }
      }
    }

    throw lastError
  }

  /**
   * 预加载页面
   */
  private async preloadPages(
    currentPage: number,
    fetchFn: (page: number, size: number) => Promise<DataPage<T>>
  ) {
    const preloadPromises = []

    for (let i = 1; i <= this.options.preloadPages!; i++) {
      const page = currentPage + i
      const cacheKey = `page_${page}_${this.options.pageSize}`

      if (!this.cache.has(cacheKey) && !this.loadingMap.has(cacheKey)) {
        const promise = fetchFn(page, this.options.pageSize!).then(() => {
          this.cache.set(cacheKey, result)
          this.maintainCacheSize()
          return result
        })

        preloadPromises.push(promise)
      }
    }

    // 并行预加载
    await Promise.allSettled(preloadPromises)
  }

  /**
   * 维护缓存大小
   */
  private maintainCacheSize() {
    if (this.cache.size > this.options.cacheSize!) {
      const keysToDelete = Array.from(this.cache.keys()).slice(
        0,
        this.cache.size - this.options.cacheSize!
      )

      keysToDelete.forEach(key => this.cache.delete(key))
    }
  }

  /**
   * 添加超时控制
   */
  private withTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) => setTimeout(() => reject(new Error('Request timeout')), timeout))
    ])
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 数组分块
   */
  private chunk<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear()
    this.loadingMap.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.options.cacheSize,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 导出单例
export const dataLoader = new DataLoader()

// 导出便捷方法
export const usePagination = dataLoader.usePagination.bind(dataLoader)
export const useVirtualScroll = dataLoader.useVirtualScroll.bind(dataLoader)
export const useLazyLoad = dataLoader.useLazyLoad.bind(dataLoader)
export const useStreamLoader = dataLoader.useStreamLoader.bind(dataLoader)
