/**
 * 离线管理器
 * 管理PWA离线功能、缓存策略和后台同步
 */

import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 离线状态
export interface OfflineState {
  isOnline: boolean
  isServiceWorkerReady: boolean
  isPWAInstalled: boolean
  pendingRequests: number
  cacheSize: number
  lastSync: Date | null
}

// PWA安装提示
export interface InstallPrompt {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

// 离线请求
export interface OfflineRequest {
  id?: number
  url: string
  method: string
  headers: Record<string, string>
  body?: BodyInit | null
  timestamp: number
  retries: number
}

// PWA事件类型
interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

// 缓存数据项
interface CacheDataItem {
  key: string
  value: unknown
  expires: number | null
  timestamp: number
}

export class OfflineManager {
  private static instance: OfflineManager
  private registration: ServiceWorkerRegistration | null = null
  private installPrompt: InstallPrompt | null = null
  private db: IDBDatabase | null = null

  // 响应式状态
  public state = ref<OfflineState>({
    isOnline: navigator.onLine,
    isServiceWorkerReady: false,
    isPWAInstalled: false,
    pendingRequests: 0,
    cacheSize: 0,
    lastSync: null
  })

  private constructor() {
    this.init()
  }

  static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager()
    }
    return OfflineManager.instance
  }

  /**
   * 初始化
   */
  private async init(): Promise<void> {
    // 监听网络状态
    this.setupNetworkListeners()

    // 注册Service Worker
    await this.registerServiceWorker()

    // 初始化IndexedDB
    await this.initIndexedDB()

    // 检查PWA安装状态
    this.checkPWAStatus()

    // 监听安装提示
    this.setupInstallPrompt()

    // 定期同步
    this.startPeriodicSync()
  }

  /**
   * 注册Service Worker
   */
  private async registerServiceWorker(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      return
    }

    try {
      this.registration = await navigator.serviceWorker.register('/service-worker.js', {
        scope: '/'
      })

      // 监听更新
      this.registration.addEventListener('updatefound', () => {
        const newWorker = this.registration!.installing

        newWorker?.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // 新版本可用
            this.notifyUpdate()
          }
        })
      })

      // 等待激活
      await navigator.serviceWorker.ready
      this.state.value.isServiceWorkerReady = true

      // 更新缓存大小
      await this.updateCacheSize()
    } catch (error) {
      console.warn('Service Worker registration failed:', error)
    }
  }

  /**
   * 初始化IndexedDB
   */
  private async initIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('hr-offline', 1)

      request.onerror = () => {
        reject(request.error)
      }

      request.onsuccess = () => {
        this.db = request.result
        this.updatePendingCount()
        resolve()
      }

      request.onupgradeneeded = event => {
        const db = (event.target as IDBOpenDBRequest).result

        // 创建离线请求存储
        if (!db.objectStoreNames.contains('pending-requests')) {
          const store = db.createObjectStore('pending-requests', {
            keyPath: 'id',
            autoIncrement: true
          })
          store.createIndex('timestamp', 'timestamp', { unique: false })
        }

        // 创建缓存数据存储
        if (!db.objectStoreNames.contains('cached-data')) {
          const store = db.createObjectStore('cached-data', { keyPath: 'key' })
          store.createIndex('expires', 'expires', { unique: false })
        }
      }
    })
  }

  /**
   * 设置网络监听
   */
  private setupNetworkListeners(): void {
    window.addEventListener('online', () => {
      this.state.value.isOnline = true
      ElMessage.success('网络已连接')
      this.syncOfflineData()
    })

    window.addEventListener('offline', () => {
      this.state.value.isOnline = false
      ElMessage.warning('网络已断开，进入离线模式')
    })
  }

  /**
   * 检查PWA状态
   */
  private checkPWAStatus(): void {
    // 检查是否以PWA模式运行
    this.state.value.isPWAInstalled =
      window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as unknown).standalone === true
  }

  /**
   * 设置安装提示
   */
  private setupInstallPrompt(): void {
    window.addEventListener('beforeinstallprompt', (event: BeforeInstallPromptEvent) => {
      event.preventDefault()
      this.installPrompt = event as unknown as InstallPrompt
    })

    window.addEventListener('appinstalled', () => {
      this.state.value.isPWAInstalled = true
      ElMessage.success('应用已安装到主屏幕')
    })
  }

  /**
   * 触发PWA安装
   */
  async installPWA(): Promise<void> {
    if (!this.installPrompt) {
      ElMessage.warning('当前环境不支持安装')
      return
    }

    try {
      await this.installPrompt.prompt()
      const { outcome } = await this.installPrompt.userChoice

      if (outcome === 'accepted') {
        ElMessage.success('应用安装成功')
      } else {
        ElMessage.info('用户取消安装')
      }
    } catch (error) {
      console.warn('PWA installation failed:', error)
      ElMessage.error('安装失败')
    }

    this.installPrompt = null
  }

  /**
   * 获取缓存数据
   */
  async getCachedData(key: string): Promise<unknown> {
    if (!this.db) return null

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['cached-data'], 'readonly')
      const store = transaction.objectStore('cached-data')
      const request = store.get(key)

      request.onsuccess = () => {
        const data = request.result
        if (data && (!data.expires || data.expires > Date.now())) {
          resolve(data.value)
        } else {
          resolve(null)
        }
      }

      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 设置缓存数据
   */
  async setCachedData(key: string, value: unknown, ttl?: number): Promise<void> {
    if (!this.db) return

    const transaction = this.db.transaction(['cached-data'], 'readwrite')
    const store = transaction.objectStore('cached-data')

    const data: CacheDataItem = {
      key,
      value,
      expires: ttl ? Date.now() + ttl : null,
      timestamp: Date.now()
    }

    await new Promise((resolve, reject) => {
      const request = store.put(data)
      request.onsuccess = resolve
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 清理过期数据
   */
  async cleanExpiredData(): Promise<void> {
    if (!this.db) return

    const transaction = this.db.transaction(['cached-data'], 'readwrite')
    const store = transaction.objectStore('cached-data')
    const index = store.index('expires')
    const range = IDBKeyRange.upperBound(Date.now())

    const request = index.openCursor(range)

    request.onsuccess = event => {
      const cursor = (event.target as IDBRequest).result
      if (cursor) {
        store.delete(cursor.primaryKey)
        cursor.continue()
      }
    }
  }

  /**
   * 更新缓存大小
   */
  private async updateCacheSize(): Promise<void> {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate()
        this.state.value.cacheSize = estimate.usage || 0
      }
    } catch (error) {
      console.warn('Failed to get cache size:', error)
    }
  }

  /**
   * 通知更新
   */
  private notifyUpdate(): void {
    ElMessage({
      type: 'info',
      message: '发现新版本，点击刷新页面',
      showClose: true,
      duration: 0,
      onClick: () => {
        window.location.reload()
      }
    })
  }

  /**
   * 更新待处理请求数量
   */
  private async updatePendingCount(): Promise<void> {
    if (!this.db) return

    try {
      const transaction = this.db.transaction(['pending-requests'], 'readonly')
      const store = transaction.objectStore('pending-requests')
      const countRequest = store.count()

      countRequest.onsuccess = () => {
        this.state.value.pendingRequests = countRequest.result
      }
    } catch (error) {
      console.warn('Failed to update pending count:', error)
    }
  }

  /**
   * 同步离线数据
   */
  async syncOfflineData(): Promise<void> {
    if (!this.db || !this.state.value.isOnline) return

    try {
      const transaction = this.db.transaction(['pending-requests'], 'readwrite')
      const store = transaction.objectStore('pending-requests')
      const getAll = store.getAll()

      getAll.onsuccess = async () => {
        const requests = getAll.result as OfflineRequest[]

        for (const request of requests) {
          try {
            const response = await fetch(request.url, {
              method: request.method,
              headers: request.headers,
              body: request.body
            })

            if (response.ok) {
              // 删除成功同步的请求
              store.delete(request.id)
            } else {
              // 增加重试次数
              request.retries++
              if (request.retries < 3) {
                store.put(request)
              } else {
                store.delete(request.id)
              }
            }
          } catch (error) {
            console.warn('Sync failed for request:', request.url, error)
            request.retries++
            if (request.retries < 3) {
              store.put(request)
            } else {
              store.delete(request.id)
            }
          }
        }

        this.state.value.lastSync = new Date()
        await this.updatePendingCount()
      }
    } catch (error) {
      console.warn('Failed to sync offline data:', error)
    }
  }

  /**
   * 启动定期同步
   */
  private startPeriodicSync(): void {
    // 每5分钟尝试同步一次
    setInterval(
      () => {
        if (this.state.value.isOnline) {
          this.syncOfflineData()
        }
      },
      5 * 60 * 1000
    )
  }

  /**
   * 清理缓存
   */
  async clearCache(): Promise<void> {
    try {
      // 清理Service Worker缓存
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(cacheNames.map(cacheName => caches.delete(cacheName)))
      }

      // 清理IndexedDB缓存
      if (this.db) {
        const transaction = this.db.transaction(['cached-data'], 'readwrite')
        const store = transaction.objectStore('cached-data')
        await new Promise<void>((resolve, reject) => {
          const clearRequest = store.clear()
          clearRequest.onsuccess = () => resolve()
          clearRequest.onerror = () => reject(clearRequest.error)
        })
      }

      await this.updateCacheSize()
      ElMessage.success('缓存已清理')
    } catch (error) {
      console.warn('Failed to clear cache:', error)
      ElMessage.error('缓存清理失败')
    }
  }

  /**
   * 请求通知权限
   */
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('This browser does not support notifications')
    }

    let permission = Notification.permission

    if (permission === 'default') {
      permission = await Notification.requestPermission()
    }

    return permission
  }

  /**
   * 订阅推送通知
   */
  async subscribePush(vapidKey: string): Promise<PushSubscription | null> {
    if (!this.registration) {
      throw new Error('Service Worker not registered')
    }

    try {
      const permission = await this.requestNotificationPermission()
      if (permission !== 'granted') {
        throw new Error('Notification permission denied')
      }

      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(vapidKey)
      })

      return subscription
    } catch (error) {
      console.warn('Failed to subscribe to push notifications:', error)
      return null
    }
  }

  /**
   * 将Base64 VAPID密钥转换为Uint8Array
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - (base64String.length % 4)) % 4)
    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }

    return outputArray
  }

  /**
   * 添加离线请求
   */
  async addOfflineRequest(
    request: Omit<OfflineRequest, 'id' | 'timestamp' | 'retries'>
  ): Promise<void> {
    if (!this.db) return

    const offlineRequest: OfflineRequest = {
      ...request,
      timestamp: Date.now(),
      retries: 0
    }

    const transaction = this.db.transaction(['pending-requests'], 'readwrite')
    const store = transaction.objectStore('pending-requests')

    await new Promise<void>((resolve, reject) => {
      const addRequest = store.add(offlineRequest)
      addRequest.onsuccess = () => resolve()
      addRequest.onerror = () => reject(addRequest.error)
    })

    await this.updatePendingCount()
  }
}

// 导出单例
export const offlineManager = OfflineManager.getInstance()

// 导出Vue组合式API
export function useOfflineManager() {
  const manager = offlineManager

  const isOnline = computed(() => manager.state.value.isOnline)
  const isReady = computed(() => manager.state.value.isServiceWorkerReady)
  const isPWA = computed(() => manager.state.value.isPWAInstalled)
  const pendingCount = computed(() => manager.state.value.pendingRequests)
  const cacheSize = computed(() => manager.state.value.cacheSize)
  const canInstall = computed(() => !isPWA.value && isReady.value)

  const formatCacheSize = computed(() => {
    const size = cacheSize.value
    if (size < 1024) return `${size} B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`
    if (size < 1024 * 1024 * 1024) return `${(size / 1024 / 1024).toFixed(2)} MB`
    return `${(size / 1024 / 1024 / 1024).toFixed(2)} GB`
  })

  return {
    // 状态
    isOnline,
    isReady,
    isPWA,
    pendingCount,
    cacheSize,
    formatCacheSize,
    canInstall,

    // 方法
    installPWA: () => manager.installPWA(),
    syncData: () => manager.syncOfflineData(),
    clearCache: () => manager.clearCache(),
    requestPermission: () => manager.requestNotificationPermission(),
    subscribePush: (key: string) => manager.subscribePush(key),
    getCached: (key: string) => manager.getCachedData(key),
    setCached: (key: string, value: unknown, ttl?: number) =>
      manager.setCachedData(key, value, ttl),
    addOfflineRequest: (request: Omit<OfflineRequest, 'id' | 'timestamp' | 'retries'>) =>
      manager.addOfflineRequest(request),
    cleanExpired: () => manager.cleanExpiredData()
  }
}
