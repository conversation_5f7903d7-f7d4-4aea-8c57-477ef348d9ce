/**
 * 快捷键管理工具
 * 提供全局和局部快捷键注册、管理功能
 */

import { onMounted, onUnmounted } from 'vue'

// 快捷键配置
export interface ShortcutConfig {
  key: string // 快捷键组合，如 'ctrl+s', 'cmd+k'
  handler: (e: KeyboardEvent) => void // 处理函数
  description?: string // 快捷键描述
  preventDefault?: boolean // 是否阻止默认行为
  stopPropagation?: boolean // 是否阻止事件冒泡
  scope?: string // 作用域
  enabled?: boolean // 是否启用
}

// 快捷键管理器
class ShortcutManager {
  private shortcuts: Map<string, ShortcutConfig[]> = new Map()
  private activeScope: string = 'global'
  private enabled: boolean = true

  constructor() {
    this.init()
  }

  // 初始化
  private init() {
    document.addEventListener('keydown', this.handleKeyDown.bind(this))
  }

  // 注册快捷键
  register(config: ShortcutConfig | ShortcutConfig[]) {
    const configs = Array.isArray(config) ? config : [config]

    configs.forEach(cfg => {
      const scope = cfg.scope || 'global'
      const key = this.normalizeKey(cfg.key)

      if (!this.shortcuts.has(scope)) {
        this.shortcuts.set(scope, [])
      }

      const scopeShortcuts = this.shortcuts.get(scope)!
      const existingIndex = scopeShortcuts.findIndex(s => s.key === key)

      if (existingIndex >= 0) {
        // 替换已存在的快捷键
        scopeShortcuts[existingIndex] = { ...cfg, key }
      } else {
        // 添加新快捷键
        scopeShortcuts.push({ ...cfg, key })
      }
    })
  }

  // 注销快捷键
  unregister(key: string, scope: string = 'global') {
    const normalizedKey = this.normalizeKey(key)
    const scopeShortcuts = this.shortcuts.get(scope)

    if (scopeShortcuts) {
      const index = scopeShortcuts.findIndex(s => s.key === normalizedKey)
      if (index >= 0) {
        scopeShortcuts.splice(index, 1)
      }
    }
  }

  // 清空某个作用域的快捷键
  clearScope(scope: string) {
    this.shortcuts.delete(scope)
  }

  // 设置当前作用域
  setScope(scope: string) {
    this.activeScope = scope
  }

  // 启用/禁用快捷键
  setEnabled(enabled: boolean) {
    this.enabled = enabled
  }

  // 获取所有快捷键
  getShortcuts(scope?: string): ShortcutConfig[] {
    if (scope) {
      return this.shortcuts.get(scope) || []
    }

    const allShortcuts: ShortcutConfig[] = []
    this.shortcuts.forEach(shortcuts => {
      allShortcuts.push(...shortcuts)
    })
    return allShortcuts
  }

  // 处理键盘事件
  private handleKeyDown(event: KeyboardEvent) {
    if (!this.enabled) return

    const key = this.getKeyFromEvent(event)

    // 检查当前作用域的快捷键
    const scopeShortcuts = this.shortcuts.get(this.activeScope) || []
    const globalShortcuts = this.shortcuts.get('global') || []

    // 合并快捷键，当前作用域优先
    const allShortcuts = [...scopeShortcuts, ...globalShortcuts]

    for (const shortcut of allShortcuts) {
      if (shortcut.key === key && shortcut.enabled !== false) {
        if (shortcut.preventDefault !== false) {
          event.preventDefault()
        }
        if (shortcut.stopPropagation) {
          event.stopPropagation()
        }

        shortcut.handler(event)
        break
      }
    }
  }

  // 标准化快捷键
  private normalizeKey(key: string): string {
    return key
      .toLowerCase()
      .replace(/\s+/g, '')
      .replace('command', 'cmd')
      .replace('control', 'ctrl')
      .replace('option', 'alt')
      .replace('delete', 'del')
      .split('+')
      .sort()
      .join('+')
  }

  // 从事件获取快捷键
  private getKeyFromEvent(event: KeyboardEvent): string {
    const parts: string[] = []

    if (event.metaKey) parts.push('cmd')
    if (event.ctrlKey) parts.push('ctrl')
    if (event.altKey) parts.push('alt')
    if (event.shiftKey) parts.push('shift')

    // 获取按键
    const key = event.key.toLowerCase()
    if (!['control', 'alt', 'shift', 'meta'].includes(key)) {
      parts.push(this.normalizeKeyName(key))
    }

    return parts.sort().join('+')
  }

  // 标准化按键名称
  private normalizeKeyName(key: string): string {
    const keyMap: Record<string, string> = {
      ' ': 'space',
      arrowup: 'up',
      arrowdown: 'down',
      arrowleft: 'left',
      arrowright: 'right',
      escape: 'esc',
      backspace: 'del'
    }

    return keyMap[key] || key
  }

  // 销毁
  destroy() {
    document.removeEventListener('keydown', this.handleKeyDown.bind(this))
    this.shortcuts.clear()
  }
}

// 全局快捷键管理器实例
export const shortcuts = new ShortcutManager()

// Vue组合式API钩子
export function useShortcuts(configs: ShortcutConfig[], scope?: string) {
  const actualScope = scope || `component-${Date.now()}`

  onMounted(() => {
    configs.forEach(config => {
      shortcuts.register({ ...config, scope: actualScope })
    })

    if (scope) {
      shortcuts.setScope(scope)
    }
  })

  onUnmounted(() => {
    if (!scope) {
      shortcuts.clearScope(actualScope)
    }
  })

  return {
    register: (config: ShortcutConfig) => {
      shortcuts.register({ ...config, scope: actualScope })
    },
    unregister: (key: string) => {
      shortcuts.unregister(key, actualScope)
    },
    setScope: () => {
      shortcuts.setScope(actualScope)
    }
  }
}

// 预定义的快捷键
export const commonShortcuts = {
  // 全局快捷键
  save: 'ctrl+s,cmd+s',
  search: 'ctrl+f,cmd+f',
  new: 'ctrl+n,cmd+n',
  delete: 'del,backspace',
  refresh: 'f5,ctrl+r,cmd+r',
  undo: 'ctrl+z,cmd+z',
  redo: 'ctrl+shift+z,cmd+shift+z',
  copy: 'ctrl+c,cmd+c',
  paste: 'ctrl+v,cmd+v',
  cut: 'ctrl+x,cmd+x',
  selectAll: 'ctrl+a,cmd+a',

  // 导航快捷键
  home: 'alt+h',
  back: 'alt+left',
  forward: 'alt+right',

  // 表格快捷键
  nextRow: 'down',
  prevRow: 'up',
  nextPage: 'pagedown',
  prevPage: 'pageup',

  // 对话框快捷键
  confirm: 'enter',
  cancel: 'esc',

  // 切换快捷键
  toggleTheme: 'ctrl+shift+t,cmd+shift+t',
  toggleFullscreen: 'f11',
  toggleSidebar: 'ctrl+b,cmd+b'
}

// 注册全局快捷键
export function registerGlobalShortcuts() {
  // 搜索
  shortcuts.register({
    key: 'ctrl+k',
    handler: () => {
      const searchInput = document.querySelector('.global-search input') as HTMLInputElement
      searchInput?.focus()
    },
    description: '全局搜索'
  })

  // 主题切换
  shortcuts.register({
    key: 'ctrl+shift+t',
    handler: () => {
      const event = new CustomEvent('toggle-theme')
      window.dispatchEvent(event)
    },
    description: '切换主题'
  })

  // 全屏切换
  shortcuts.register({
    key: 'f11',
    handler: _e => {
      e.preventDefault()
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    },
    description: '全屏切换'
  })
}

// 快捷键帮助对话框
export function showShortcutHelp() {
  const shortcuts = getShortcutList()
  // 这里可以显示一个对话框展示所有快捷键
  }

// 获取快捷键列表
export function getShortcutList() {
  const allShortcuts = shortcuts.getShortcuts()
  return allShortcuts.map(s => ({
    key: s.key,
    description: s.description || '无描述',
    scope: s.scope || 'global'
  }))
}

// 快捷键指令
export const vShortcut = {
  mounted(el: HTMLElement, binding: unknown) {
    const { key, handler, modifiers } = binding.value || {}

    if (!key || !handler) {
      return
    }

    const config: ShortcutConfig = {
      key,
      handler,
      scope: modifiers.global ? 'global' : `element-${Date.now()}`,
      preventDefault: modifiers.prevent !== false,
      stopPropagation: modifiers.stop
    }

    shortcuts.register(config)

    // 保存配置以便卸载时使用
    el._shortcutConfig = config
  },

  unmounted(el: HTMLElement) {
    const config = el._shortcutConfig
    if (config) {
      shortcuts.unregister(config.key, config.scope)
      delete el._shortcutConfig
    }
  }
}

// 检测操作系统
export function getOS(): 'mac' | 'windows' | 'linux' {
  const platform = navigator.platform.toLowerCase()
  if (platform.includes('mac')) return 'mac'
  if (platform.includes('win')) return 'windows'
  return 'linux'
}

// 格式化快捷键显示
export function formatShortcut(key: string): string {
  const os = getOS()
  const parts = key.split('+')

  return parts
    .map(part => {
      switch (part) {
        case 'cmd':
          return os === 'mac' ? '⌘' : 'Ctrl'
        case 'ctrl':
          return os === 'mac' ? '⌃' : 'Ctrl'
        case 'alt':
          return os === 'mac' ? '⌥' : 'Alt'
        case 'shift':
          return os === 'mac' ? '⇧' : 'Shift'
        case 'del':
          return os === 'mac' ? '⌫' : 'Del'
        case 'enter':
          return os === 'mac' ? '⏎' : 'Enter'
        case 'esc':
          return 'Esc'
        default:
          return part.toUpperCase()
      }
    })
    .join(os === 'mac' ? '' : '+')
}
