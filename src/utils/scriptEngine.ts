import { ref } from 'vue'
import type { ScriptVariable, ScriptUser, ScriptProcess, ScriptTask, ServiceResponse } from './types'

interface ScriptContext {
  variables: Record<string, unknown>
  user: ScriptUser | null
  process: ScriptProcess | null
  task?: ScriptTask | null
  helper: <PERSON>riptHelper
}

interface ScriptResult {
  success: boolean

  result?: unknown
  error?: string
  logs: string[]
  duration: number
}

interface ScriptHelper {
  log: (message: string) => void
  getVariable: (name: string) => unknown
  setVariable: (name: string, value: unknown) => void
  getCurrentUser: () => ScriptUser | null
  formatDate: (date: Date | string, format?: string) => string
  calculateDays: (startDate: Date | string, endDate: Date | string) => number
  sendNotification: (message: string, recipients: string[]) => void
  callService: <T = unknown>(serviceName: string, params: unknown) => Promise<ServiceResponse<T>>
}

/**
 * CLEAN-DATA-010: 脚本执行逻辑
 * 安全的脚本执行引擎，支持流程中的自定义脚本逻辑
 */
export class ScriptEngine {
  private logs: string[] = []
  private isExecuting = ref(false)
  private maxExecutionTime = 30000 // 30秒超时
  private allowedGlobals = new Set([
    'console',
    'JSON',
    'Math',
    'Date',
    'String',
    'Number',
    'Boolean',
    'Array',
    'Object'
  ])

  // 执行脚本
  async executeScript(script: string, context: Partial<ScriptContext> = {}): Promise<ScriptResult> {
    const startTime = Date.now()
    this.logs = []
    this.isExecuting.value = true

    try {
      // 验证脚本安全性
      this.validateScript(script)

      // 准备执行上下文
      const executionContext = this.prepareContext(context)

      // 创建安全的执行环境
      const result = await this.executeInSandbox(script, executionContext)

      const duration = Date.now() - startTime

      this.log(`脚本执行完成，耗时: ${duration}ms`)

      return {
        success: true,
        result,
        logs: [...this.logs],
        duration
      }
    } catch (__error) {
      const duration = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)

      this.log(`脚本执行失败: ${errorMessage}`)

      return {
        success: false,
        error: errorMessage,
        logs: [...this.logs],
        duration
      }
    } finally {
      this.isExecuting.value = false
    }
  }

  // 验证脚本安全性
  private validateScript(script: string) {
    // 检查危险操作
    const dangerousPatterns = [
      /eval\s*\(/,
      /Function\s*\(/,
      /setTimeout\s*\(/,
      /setInterval\s*\(/,
      /XMLHttpRequest/,
      /fetch\s*\(/,
      /import\s*\(/,
      /require\s*\(/,
      /process\./,
      /global\./,
      /window\./,
      /document\./,
      /localStorage/,
      /sessionStorage/,
      /indexedDB/,
      /webkitStorageInfo/,
      /navigator\./,
      /location\./,
      /history\./,
      /__proto__/,
      /constructor/,
      /prototype/
    ]

    for (const pattern of dangerousPatterns) {
      if (pattern.test(script)) {
        throw new Error(`脚本包含不允许的操作: ${pattern.toString()}`)
      }
    }

    // 检查脚本长度
    if (script.length > 50000) {
      throw new Error('脚本长度不能超过50KB')
    }
  }

  // 准备执行上下文
  private prepareContext(context: Partial<ScriptContext>): ScriptContext {
    const helper: ScriptHelper = {
      log: (message: string) => this.log(message),
      getVariable: (name: string) => context.variables?.[name],

      setVariable: (name: string, value: unknown) => {
        if (context.variables) {
          context.variables[name] = value
        }
      },
      getCurrentUser: () => context.user,
      formatDate: (date: Date | string, format = 'YYYY-MM-DD') => this.formatDate(date, format),
      calculateDays: (startDate: Date | string, endDate: Date | string) =>
        this.calculateDays(startDate, endDate),
      sendNotification: (message: string, recipients: string[]) =>
        this.sendNotification(message, recipients),

      callService: (serviceName: string, params: unknown) => this.callService(serviceName, params)
    }

    return {
      variables: context.variables || {},
      user: context.user || null,
      process: context.process || null,
      task: context.task || null,
      helper
    }
  }

  // 在沙箱中执行脚本
  private async executeInSandbox(script: string, context: ScriptContext): Promise<unknown> {
    // 创建执行函数
    const executeFunction = new Function(
      'context',
      'variables',
      'user',
      'process',
      'task',
      'helper',
      'console',
      `
        "use strict";
        
        // 禁用危险的全局对象
        const window = undefined;
        const document = undefined;
        const global = undefined;
        const location = undefined;
        const history = undefined;
        const navigator = undefined;
        const localStorage = undefined;
        const sessionStorage = undefined;
        const indexedDB = undefined;
        const XMLHttpRequest = undefined;
        const fetch = undefined;
        const eval = undefined;
        const Function = undefined;
        const setTimeout = undefined;
        const setInterval = undefined;
        const require = undefined;
        const process = undefined;
        
        // 提供安全的控制台对象
        const console = {
          log: (...args) => helper.log(args.map(arg => String(arg)).join(' ')),
          info: (...args) => helper.log('[INFO] ' + args.map(arg => String(arg)).join(' ')),
          warn: (...args) => helper.log('[WARN] ' + args.map(arg => String(arg)).join(' ')),
          error: (...args) => helper.log('[ERROR] ' + args.map(arg => String(arg)).join(' '))
        };
        
        // 执行用户脚本
        try {
          ${script}
        } catch (__error) {
          helper.log('脚本执行出错: ' + error.message);
          throw error;
        }
      `
    )

    // 设置执行超时
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('脚本执行超时'))
      }, this.maxExecutionTime)
    })

    // 执行脚本
    const executionPromise = Promise.resolve().then(() => {
      return executeFunction(
        context,
        context.variables,
        context.user,
        context.process,
        context.task,
        context.helper,
        {
          log: (message: string) => this.log(message),
          info: (message: string) => this.log(`[INFO] ${message}`),
          warn: (message: string) => this.log(`[WARN] ${message}`),
          error: (message: string) => this.log(`[ERROR] ${message}`)
        }
      )
    })

    return Promise.race([executionPromise, timeoutPromise])
  }

  // 日志记录
  private log(message: string) {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] ${message}`
    this.logs.push(logEntry)
    }

  // 日期格式化
  private formatDate(date: Date | string, format: string): string {
    const d = new Date(date)
    if (isNaN(d.getTime())) {
      throw new Error('无效的日期格式')
    }

    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  }

  // 计算日期差
  private calculateDays(startDate: Date | string, endDate: Date | string): number {
    const start = new Date(startDate)
    const end = new Date(endDate)

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      throw new Error('无效的日期格式')
    }

    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  // 发送通知
  private sendNotification(message: string, recipients: string[]) {
    this.log(`发送通知给 ${recipients.join(', ')}: ${message}`)
    // 实际项目中这里会调用通知服务
  }

  // 调用外部服务
  private async callService<T = unknown>(serviceName: string, params: unknown): Promise<ServiceResponse<T>> {
    this.log(`调用服务 ${serviceName}，参数: ${JSON.stringify(params)}`)

    // 预定义的安全服务调用
    const allowedServices = {
      'userService.getById': (id: string) => this.mockGetUser(id),
      'departmentService.getById': (id: string) => this.mockGetDepartment(id),
      'positionService.getById': (id: string) => this.mockGetPosition(id),

      'calculatorService.calculateSalary': (params: unknown) => this.mockCalculateSalary(params),

      'validatorService.validateForm': (data: unknown) => this.mockValidateForm(data)
    }

    if (serviceName in allowedServices) {
      const serviceFunction = allowedServices[serviceName as keyof typeof allowedServices]
      return serviceFunction(params)
    } else {
      throw new Error(`不允许调用的服务: ${serviceName}`)
    }
  }

  // 模拟服务方法
  private mockGetUser(id: string) {
    return {
      id,
      name: `用户_${id}`,
      department: '技术部',
      position: '软件工程师'
    }
  }

  private mockGetDepartment(id: string) {
    return {
      id,
      name: `部门_${id}`,
      manager: `经理_${id}`
    }
  }

  private mockGetPosition(id: string) {
    return {
      id,
      name: `职位_${id}`,
      level: 'P6'
    }
  }

  private mockCalculateSalary(params: unknown) {
    const baseSalary = params.baseSalary || 0
    const performance = params.performance || 1.0
    return {
      baseSalary,
      performanceBonus: baseSalary * (performance - 1) * 0.2,
      totalSalary: baseSalary * performance
    }
  }

  private mockValidateForm(data: unknown) {
    const errors: string[] = []

    if (!data.name) {
      errors.push('姓名不能为空')
    }

    if (!data.email || !/\S+@\S+\.\S+/.test(data.email)) {
      errors.push('邮箱格式不正确')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // 执行条件表达式
  async evaluateCondition(
    expression: string,
    context: Partial<ScriptContext> = {}
  ): Promise<boolean> {
    const script = `
      // 条件表达式评估
      const result = ${expression};
      return Boolean(result);
    `

    const result = await this.executeScript(script, context)
    return result.success ? Boolean(result.result) : false
  }

  // 执行表达式
  async evaluateExpression(expression: string, context: Partial<ScriptContext> = {}): Promise<unknown> {
    const script = `
      // 表达式评估
      return ${expression};
    `

    const result = await this.executeScript(script, context)
    return result.success ? result.result : null
  }

  // 获取执行状态
  getExecutionStatus() {
    return {
      isExecuting: this.isExecuting.value,
      logs: [...this.logs]
    }
  }

  // 清除日志
  clearLogs() {
    this.logs = []
  }
}

// 全局脚本引擎实例
export const scriptEngine = new ScriptEngine()

// 便捷函数
export async function executeProcessScript(
  script: string,
  variables: Record<string, unknown> = {},
  user?: unknown,
  process?: unknown,
  task?: unknown
): Promise<ScriptResult> {
  return scriptEngine.executeScript(script, {
    variables,
    user,
    process,
    task
  })
}

export async function evaluateProcessCondition(
  condition: string,
  variables: Record<string, unknown> = {},
  user?: unknown
): Promise<boolean> {
  return scriptEngine.evaluateCondition(condition, {
    variables,
    user
  })
}
