/**
 * @name ECharts性能优化工具
 * @description 提供ECharts图表的性能优化策略，包括数据更新、渲染优化等
 */

import * as echarts from 'echarts/core'
import type { EChartsOption, SeriesOption } from 'echarts'
import { debounce, throttle } from 'lodash-es'

// 扩展echarts实例类型以支持清理函数
interface ChartWithCleanup extends echarts.ECharts {
  __cleanup__?: () => void
}

export interface ChartPerformanceConfig {
  /** 是否启用动画 */
  enableAnimation?: boolean
  /** 动画时长 */
  animationDuration?: number
  /** 是否使用Canvas渲染器（默认true，SVG为false） */
  useCanvasRenderer?: boolean
  /** 数据采样策略 */
  sampling?: 'average' | 'max' | 'min' | 'sum' | 'none'
  /** 最大数据点数 */
  maxDataPoints?: number
  /** 防抖延迟（毫秒） */
  debounceDelay?: number
  /** 节流延迟（毫秒） */
  throttleDelay?: number
  /** 是否启用懒加载 */
  enableLazyLoading?: boolean
  /** 视口外数据裁剪 */
  enableDataZoom?: boolean
}

/**
 * ECharts性能优化管理器
 */
export class ChartPerformanceManager {
  private charts = new Map<string, echarts.ECharts>()
  private updateQueues = new Map<string, EChartsOption[]>()
  private config: Required<ChartPerformanceConfig>

  constructor(config: ChartPerformanceConfig = {}) {
    this.config = {
      enableAnimation: config.enableAnimation !== false,
      animationDuration: config.animationDuration || 1000,
      useCanvasRenderer: config.useCanvasRenderer !== false,
      sampling: config.sampling || 'none',
      maxDataPoints: config.maxDataPoints || 1000,
      debounceDelay: config.debounceDelay || 300,
      throttleDelay: config.throttleDelay || 100,
      enableLazyLoading: config.enableLazyLoading !== false,
      enableDataZoom: config.enableDataZoom !== false
    }
  }

  /**
   * 注册图表实例
   */
  registerChart(id: string, chart: echarts.ECharts): void {
    this.charts.set(id, chart)
    this.updateQueues.set(id, [])

    // 应用性能优化配置
    this.applyPerformanceConfig(chart)

    // 设置防抖更新函数
    const debouncedUpdate = debounce(() => this.processUpdateQueue(id), this.config.debounceDelay)

    // 监听窗口大小变化
    const throttledResize = throttle(() => chart.resize(), this.config.throttleDelay)

    window.addEventListener('resize', throttledResize)

    // 存储清理函数
    ;(chart as ChartWithCleanup).__cleanup__ = () => {
      window.removeEventListener('resize', throttledResize)
      this.charts.delete(id)
      this.updateQueues.delete(id)
    }
  }

  /**
   * 应用性能配置
   */
  private applyPerformanceConfig(chart: echarts.ECharts): void {
    // 设置全局动画配置
    const baseOption = {
      animation: this.config.enableAnimation,
      animationDuration: this.config.animationDuration,
      animationEasing: 'cubicOut',
      // 启用渐进式渲染
      progressive: 400,
      progressiveThreshold: 3000,
      // 启用脏矩形渲染
      useUTC: true
    }

    chart.setOption(baseOption, { notMerge: false })
  }

  /**
   * 智能数据更新
   */
  updateChart(id: string, option: EChartsOption, immediate = false, notMerge = false): void {
    const chart = this.charts.get(id)
    if (!chart) return

    // 数据预处理
    const optimizedOption = this.optimizeChartOption(option)

    if (immediate) {
      chart.setOption(optimizedOption, { notMerge })
    } else {
      // 加入更新队列
      const queue = this.updateQueues.get(id) || []
      queue.push({ option: optimizedOption, notMerge })
      this.updateQueues.set(id, queue)
    }
  }

  /**
   * 优化图表配置
   */
  private optimizeChartOption(option: EChartsOption): EChartsOption {
    const optimized = { ...option }

    // 处理数据采样
    if (optimized.series) {
      optimized.series = optimized.series.map((series: SeriesOption) => {
        return this.optimizeSeries(series)
      })
    }

    // 添加数据缩放组件
    if (this.config.enableDataZoom && !optimized.dataZoom) {
      optimized.dataZoom = this.createDataZoomConfig()
    }

    // 优化提示框配置
    if (optimized.tooltip) {
      optimized.tooltip = this.optimizeTooltipConfig(optimized.tooltip)
    }

    return optimized
  }

  /**
   * 优化系列数据
   */
  private optimizeSeries(series: SeriesOption): SeriesOption {
    const optimized = { ...series }

    // 数据采样
    if (optimized.data && optimized.data.length > this.config.maxDataPoints) {
      optimized.data = this.sampleData(optimized.data, this.config.maxDataPoints)
      optimized.sampling = this.config.sampling
    }

    // 大数据量优化
    if (optimized.data && optimized.data.length > 1000) {
      // 启用大数据量优化
      optimized.large = true
      optimized.largeThreshold = 500

      // 禁用悬停效果以提升性能
      optimized.silent = true

      // 简化线条样式
      if (optimized.type === 'line') {
        optimized.lineStyle = optimized.lineStyle || {}
        optimized.lineStyle.width = optimized.lineStyle.width || 1
        optimized.symbol = 'none' // 隐藏数据点标记
      }
    }

    // 启用懒加载
    if (this.config.enableLazyLoading && optimized.data && optimized.data.length > 500) {
      optimized.progressive = 200
      optimized.progressiveThreshold = 300
    }

    return optimized
  }

  /**
   * 数据采样
   */
  private sampleData(data: unknown[], targetCount: number): unknown[] {
    if (data.length <= targetCount) return data

    const step = Math.ceil(data.length / targetCount)
    const sampled: unknown[] = []

    for (let i = 0; i < data.length; i += step) {
      const chunk = data.slice(i, i + step)

      switch (this.config.sampling) {
        case 'average':
          sampled.push(this.calculateAverage(chunk))
          break
        case 'max':
          sampled.push(this.findMax(chunk))
          break
        case 'min':
          sampled.push(this.findMin(chunk))
          break
        case 'sum':
          sampled.push(this.calculateSum(chunk))
          break
        default:
          sampled.push(chunk[0]) // 取第一个值
      }
    }

    return sampled
  }

  /**
   * 计算平均值
   */
  private calculateAverage(chunk: unknown[]): unknown {
    if (chunk.length === 0) return null
    if (typeof chunk[0] === 'number') {
      return chunk.reduce((sum, val) => sum + val, 0) / chunk.length
    }
    if (Array.isArray(chunk[0])) {
      const result = [...chunk[0]]
      for (let i = 1; i < result.length; i++) {
        if (typeof result[i] === 'number') {
          result[i] = chunk.reduce((sum, item) => sum + (item[i] || 0), 0) / chunk.length
        }
      }
      return result
    }
    return chunk[0]
  }

  /**
   * 查找最大值
   */
  private findMax(chunk: unknown[]): unknown {
    if (chunk.length === 0) return null
    if (typeof chunk[0] === 'number') {
      return Math.max(...chunk)
    }
    if (Array.isArray(chunk[0])) {
      return chunk.reduce((max, current) => {
        const currentValue = typeof current[1] === 'number' ? current[1] : 0
        const maxValue = typeof max[1] === 'number' ? max[1] : 0
        return currentValue > maxValue ? current : max
      })
    }
    return chunk[0]
  }

  /**
   * 查找最小值
   */
  private findMin(chunk: unknown[]): unknown {
    if (chunk.length === 0) return null
    if (typeof chunk[0] === 'number') {
      return Math.min(...chunk)
    }
    if (Array.isArray(chunk[0])) {
      return chunk.reduce((min, current) => {
        const currentValue = typeof current[1] === 'number' ? current[1] : 0
        const minValue = typeof min[1] === 'number' ? min[1] : 0
        return currentValue < minValue ? current : min
      })
    }
    return chunk[0]
  }

  /**
   * 计算总和
   */
  private calculateSum(chunk: unknown[]): unknown {
    if (chunk.length === 0) return null
    if (typeof chunk[0] === 'number') {
      return chunk.reduce((sum, val) => sum + val, 0)
    }
    if (Array.isArray(chunk[0])) {
      const result = [...chunk[0]]
      for (let i = 1; i < result.length; i++) {
        if (typeof result[i] === 'number') {
          result[i] = chunk.reduce((sum, item) => sum + (item[i] || 0), 0)
        }
      }
      return result
    }
    return chunk[0]
  }

  /**
   * 创建数据缩放配置
   */
  private createDataZoomConfig(): unknown[] {
    return [
      {
        type: 'inside',
        start: 0,
        end: 100,
        filterMode: 'filter'
      },
      {
        type: 'slider',
        start: 0,
        end: 100,
        height: 20,
        bottom: 20,
        handleIcon: 'M8.2,13.6V3.9H6.3v9.7H3.1v2.8h3.2v9.7h1.9V16.4h3.2v-2.8H8.2z',
        handleSize: '120%'
      }
    ]
  }

  /**
   * 优化提示框配置
   */
  private optimizeTooltipConfig(tooltip: Record<string, unknown>): Record<string, unknown> {
    return {
      ...tooltip,
      trigger: tooltip.trigger || 'axis',
      axisPointer: {
        type: 'shadow',
        animation: false // 禁用动画以提升性能
      },
      backgroundColor: 'rgba(50,50,50,0.7)',
      borderColor: 'rgba(255,255,255,0.1)',
      textStyle: {
        color: '#fff'
      },
      // 提示框渲染模式
      renderMode: 'html',
      // 限制提示框内容长度
      formatter: tooltip.formatter || this.createOptimizedFormatter()
    }
  }

  /**
   * 创建优化的格式化器
   */
  private createOptimizedFormatter(): Function {
    return (params: unknown) => {
      if (!Array.isArray(params)) params = [params]

      // 限制显示的系列数量
      const maxSeries = 5
      const displayParams = params.slice(0, maxSeries)

      let result = `<div>${displayParams[0].axisValueLabel || displayParams[0].name}</div>`

      displayParams.forEach((param: Record<string, unknown>) => {
        const value = typeof param.value === 'number' ? param.value.toLocaleString() : param.value
        result += `<div style="margin:2px 0">
          <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${param.color}"></span>
          ${param.seriesName}: ${value}
        </div>`
      })

      if (params.length > maxSeries) {
        result += `<div style="margin:2px 0;color:#999">...还有 ${params.length - maxSeries} 个系列</div>`
      }

      return result
    }
  }

  /**
   * 处理更新队列
   */
  private processUpdateQueue(id: string): void {
    const queue = this.updateQueues.get(id)
    const chart = this.charts.get(id)

    if (!queue || !chart || queue.length === 0) return

    // 合并更新
    const mergedOption = this.mergeUpdates(queue)
    chart.setOption(mergedOption.option, { notMerge: mergedOption.notMerge })

    // 清空队列
    this.updateQueues.set(id, [])
  }

  /**
   * 合并多个更新
   */
  private mergeUpdates(updates: Array<{ option: EChartsOption; notMerge: boolean }>): {
    option: EChartsOption
    notMerge: boolean
  } {
    if (updates.length === 1) return updates[0]

    // 如果有任何一个更新要求notMerge，则使用notMerge
    const notMerge = updates.some(update => update.notMerge)

    // 深度合并所有option
    const mergedOption = updates.reduce((merged, update) => {
      return this.deepMerge(merged, update.option)
    }, {})

    return { option: mergedOption, notMerge }
  }

  /**
   * 深度合并对象
   */
  private deepMerge(
    target: Record<string, unknown>,
    source: Record<string, unknown>
  ): Record<string, unknown> {
    const result = { ...target }

    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key])
      } else {
        result[key] = source[key]
      }
    }

    return result
  }

  /**
   * 批量更新多个图表
   */
  batchUpdate(updates: Array<{ id: string; option: EChartsOption; immediate?: boolean }>): void {
    updates.forEach(({ id, option, immediate = false }) => {
      this.updateChart(id, option, immediate)
    })
  }

  /**
   * 获取图表性能统计
   */
  getPerformanceStats(id: string): unknown {
    const chart = this.charts.get(id)
    if (!chart) return null

    const canvas = chart.getDom().querySelector('canvas')
    const ctx = canvas?.getContext('2d')

    return {
      chartId: id,
      canvasSize: canvas ? { width: canvas.width, height: canvas.height } : null,
      memoryUsage: this.estimateMemoryUsage(chart),
      updateQueueLength: this.updateQueues.get(id)?.length || 0,
      renderMode: ctx ? 'canvas' : 'svg'
    }
  }

  /**
   * 估算内存使用
   */
  private estimateMemoryUsage(chart: echarts.ECharts): number {
    try {
      const option = chart.getOption()
      const serialized = JSON.stringify(option)
      return serialized.length * 2 // 估算字符串内存占用
    } catch {
      return 0
    }
  }

  /**
   * 清理图表资源
   */
  dispose(id: string): void {
    const chart = this.charts.get(id)
    if (chart) {
      // 调用清理函数
      const chartWithCleanup = chart as ChartWithCleanup
      if (chartWithCleanup.__cleanup__) {
        chartWithCleanup.__cleanup__()
      }
      chart.dispose()
    }

    this.charts.delete(id)
    this.updateQueues.delete(id)
  }

  /**
   * 清理所有图表
   */
  disposeAll(): void {
    this.charts.forEach((chart, id) => {
      this.dispose(id)
    })
  }

  /**
   * 获取所有图表的性能概览
   */
  getPerformanceOverview(): unknown {
    const stats = {
      totalCharts: this.charts.size,
      totalMemoryUsage: 0,
      totalQueueLength: 0,
      chartDetails: [] as Array<ReturnType<typeof this.getPerformanceStats>>
    }

    this.charts.forEach((chart, id) => {
      const chartStats = this.getPerformanceStats(id)
      if (chartStats) {
        stats.totalMemoryUsage += chartStats.memoryUsage
        stats.totalQueueLength += chartStats.updateQueueLength
        stats.chartDetails.push(chartStats)
      }
    })

    return stats
  }
}

// 创建全局性能管理器实例
export const chartPerformanceManager = new ChartPerformanceManager()

// 导出预设配置
export const ChartPerformanceConfigs = {
  // 高性能配置（适用于大数据量）
  HIGH_PERFORMANCE: {
    enableAnimation: false,
    sampling: 'average' as const,
    maxDataPoints: 500,
    debounceDelay: 500,
    enableLazyLoading: true,
    enableDataZoom: true
  },

  // 平衡配置（性能和视觉效果兼顾）
  BALANCED: {
    enableAnimation: true,
    animationDuration: 500,
    sampling: 'none' as const,
    maxDataPoints: 1000,
    debounceDelay: 300,
    enableLazyLoading: true
  },

  // 高质量配置（注重视觉效果）
  HIGH_QUALITY: {
    enableAnimation: true,
    animationDuration: 1000,
    sampling: 'none' as const,
    maxDataPoints: 2000,
    debounceDelay: 100,
    enableLazyLoading: false
  }
}

/**
 * ECharts Hook 用于Vue组件
 */
export function useChartPerformance(chartId: string, config?: ChartPerformanceConfig) {
  const manager = new ChartPerformanceManager(config)

  const registerChart = (chart: echarts.ECharts) => {
    manager.registerChart(chartId, chart)
  }

  const updateChart = (option: EChartsOption, immediate = false) => {
    manager.updateChart(chartId, option, immediate)
  }

  const getStats = () => {
    return manager.getPerformanceStats(chartId)
  }

  const dispose = () => {
    manager.dispose(chartId)
  }

  return {
    registerChart,
    updateChart,
    getStats,
    dispose,
    manager
  }
}

export default ChartPerformanceManager
