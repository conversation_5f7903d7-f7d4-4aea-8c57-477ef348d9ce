/**
 * 自动报告生成器
 * 基于模板和数据分析结果生成自然语言报告
 */

import { ref } from 'vue'

// 报告类型
export enum ReportType {
  DAILY = 'daily',           // 日报
  WEEKLY = 'weekly',         // 周报
  MONTHLY = 'monthly',       // 月报
  QUARTERLY = 'quarterly',   // 季报
  ANNUAL = 'annual',         // 年报
  CUSTOM = 'custom'          // 自定义
}

// 报告模板
export interface ReportTemplate {
  id: string
  name: string
  type: ReportType
  sections: ReportSection[]
  metadata: {
    author?: string
    version?: string
    tags?: string[]
  }
}

// 报告章节
export interface ReportSection {
  id: string
  title: string
  type: 'summary' | 'analysis' | 'trend' | 'comparison' | 'recommendation'
  template: string              // 模板字符串，支持变量替换
  dataSource: string           // 数据源标识
  rules?: AnalysisRule[]       // 分析规则
  charts?: ChartConfig[]       // 配套图表
}

// 分析规则
export interface AnalysisRule {
  condition: string            // 条件表达式
  template: string            // 满足条件时的模板
  level: 'info' | 'warning' | 'error' | 'success'
}

// 图表配置
export interface ChartConfig {
  type: string
  dataKey: string
   
  options: unknown
}

// 生成配置
export interface GenerateConfig {
  template: ReportTemplate
  data: Record<string, unknown>
  timeRange: {
    start: Date
    end: Date
  }
  format: 'text' | 'html' | 'markdown' | 'pdf'
  language: string
}

// 生成结果
export interface GenerateResult {
  content: string              // 报告内容
  summary: string              // 摘要
  insights: string[]           // 关键洞察
  recommendations: string[]    // 建议
  metadata: {
    generatedAt: Date
    duration: number
    wordCount: number
    dataPoints: number
  }
}

export class ReportGenerator {
  private static instance: ReportGenerator
  private templates: Map<string, ReportTemplate> = new Map()
  private analysisCache: Map<string, unknown> = new Map()
  
  // 生成状态
  public generating = ref(false)
  public progress = ref(0)
  
  // 默认模板
  private defaultTemplates: ReportTemplate[] = [
    {
      id: 'hr-daily',
      name: '人事日报模板',
      type: ReportType.DAILY,
      sections: [
        {
          id: 'summary',
          title: '今日概况',
          type: 'summary',
          template: '截至{date}，全公司共有员工{totalEmployees}人，其中正式员工{formalEmployees}人，实习生{internEmployees}人。今日新入职{newEmployees}人，离职{leftEmployees}人。',
          dataSource: 'employee.daily'
  },
        {
          id: 'attendance',
          title: '考勤情况',
          type: 'analysis',
          template: '今日应到{shouldAttend}人，实到{actualAttend}人，出勤率{attendanceRate}%。迟到{lateCount}人，早退{earlyLeaveCount}人，请假{leaveCount}人。',
          dataSource: 'attendance.daily',
          rules: [
            {
              condition: 'attendanceRate < 90',
              template: '出勤率较低，需要关注。',
              level: 'warning'
            }
          ]
        }
      ],
      metadata: {
        author: 'system',
        version: '1.0'
      }
    },
    {
      id: 'hr-monthly',
      name: '人事月报模板',
      type: ReportType.MONTHLY,
      sections: [
        {
          id: 'overview',
          title: '本月总览',
          type: 'summary',
          template: '{month}份人事数据总览：员工总数{totalEmployees}人，环比{employeeGrowth}%。本月新增{newEmployees}人，离职{leftEmployees}人，离职率{turnoverRate}%。',
          dataSource: 'employee.monthly'
  },
        {
          id: 'department',
          title: '部门分析',
          type: 'analysis',
          template: '各部门人员分布：{departmentDistribution}。人员增长最快的部门是{fastestGrowthDept}，增长率{growthRate}%。',
          dataSource: 'department.monthly'
  },
        {
          id: 'trend',
          title: '发展趋势',
          type: 'trend',
          template: '近三个月员工总数呈{trendDirection}趋势，平均每月{averageGrowth}。预计下月员工数将达到{predictedEmployees}人。',
          dataSource: 'employee.trend'
        }
      ],
      metadata: {
        author: 'system',
        version: '1.0'
      }
    }
  ]
  
  private constructor() {
    this.initTemplates()
  }
  
  static getInstance(): ReportGenerator {
    if (!ReportGenerator.instance) {
      ReportGenerator.instance = new ReportGenerator()
    }
    return ReportGenerator.instance
  }
  
  /**
   * 初始化模板
   */
  private initTemplates(): void {
    this.defaultTemplates.forEach(template => {
      this.templates.set(template.id, template)
    })
  }
  
  /**
   * 生成报告
   */
  async generate(config: GenerateConfig): Promise<GenerateResult> {
    this.generating.value = true
    this.progress.value = 0
    
    try {
      const startTime = Date.now()
      
      // 1. 验证配置
      this.validateConfig(config)
      this.progress.value = 10
      
      // 2. 获取数据
      const data = await this.fetchData(config)
      this.progress.value = 30
      
      // 3. 分析数据
      const analysis = await this.analyzeData(data, config)
      this.progress.value = 50
      
      // 4. 生成各章节
      const sections = await this.generateSections(config.template, analysis)
      this.progress.value = 70
      
      // 5. 生成洞察和建议
      const insights = this.generateInsights(analysis)
      const recommendations = this.generateRecommendations(analysis)
      this.progress.value = 85
      
      // 6. 格式化输出
      const content = this.formatContent(sections, config.format)
      const summary = this.generateSummary(sections)
      this.progress.value = 95
      
      // 7. 生成结果
      const result: GenerateResult = {
        content,
        summary,
        insights,
        recommendations,
        metadata: {
          generatedAt: new Date(),
          duration: Date.now() - startTime,
          wordCount: this.countWords(content),
          dataPoints: this.countDataPoints(data)
        }
      }
      
      this.progress.value = 100
      return result
      
    } finally {
      this.generating.value = false
    }
  }
  
  /**
   * 验证配置
   */
  private validateConfig(config: GenerateConfig): void {
    if (!config.template) {
      throw new Error('报告模板不能为空')
    }
    
    if (!config.data) {
      throw new Error('报告数据不能为空')
    }
    
    if (!config.timeRange) {
      throw new Error('时间范围不能为空')
    }
  }
  
  /**
   * 获取数据
   */
  private async fetchData(config: GenerateConfig): Promise<Record<string, unknown>> {
    // 模拟数据获取
    await this.delay(500)
    
    const data: Record<string, unknown> = {
      ...config.data,
      date: new Date().toLocaleDateString('zh-CN'),
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear()
    }
    
    // 模拟一些关键指标
    if (!data.totalEmployees) data.totalEmployees = 1234
    if (!data.formalEmployees) data.formalEmployees = 1100
    if (!data.internEmployees) data.internEmployees = 134
    if (!data.newEmployees) data.newEmployees = 12
    if (!data.leftEmployees) data.leftEmployees = 8
    if (!data.employeeGrowth) data.employeeGrowth = 2.5
    if (!data.turnoverRate) data.turnoverRate = 0.65
    
    return data
  }
  
  /**
   * 分析数据
   */
  private async analyzeData(
    data: Record<string, unknown>,
    config: GenerateConfig
  ): Promise<Record<string, unknown>> {
    await this.delay(300)
    
    const analysis: Record<string, unknown> = {
      ...data,
      // 计算趋势
      trendDirection: data.employeeGrowth > 0 ? '上升' : '下降',
      averageGrowth: `增长${Math.abs(data.employeeGrowth)}%`,
      predictedEmployees: Math.round(data.totalEmployees * (1 + data.employeeGrowth / 100)),
      
      // 考勤分析
      shouldAttend: data.totalEmployees || 1234,
      actualAttend: data.actualAttend || 1180,
      attendanceRate: data.attendanceRate || 95.6,
      lateCount: data.lateCount || 23,
      earlyLeaveCount: data.earlyLeaveCount || 15,
      leaveCount: data.leaveCount || 16,
      
      // 部门分析
      departmentDistribution: data.departmentDistribution || '研发部456人，销售部234人，运营部189人，行政部123人',
      fastestGrowthDept: data.fastestGrowthDept || '研发部',
      growthRate: data.growthRate || 5.2
    }
    
    // 缓存分析结果
    const cacheKey = `${config.template.id}-${config.timeRange.start.getTime()}`
    this.analysisCache.set(cacheKey, analysis)
    
    return analysis
  }
  
  /**
   * 生成各章节
   */
  private async generateSections(
    template: ReportTemplate,
    analysis: Record<string, unknown>
  ): Promise<string[]> {
    const sections: string[] = []
    
    for (const section of template.sections) {
      const content = await this.generateSection(section, analysis)
      sections.push(content)
      
      // 更新进度
      this.progress.value += 20 / template.sections.length
    }
    
    return sections
  }
  
  /**
   * 生成单个章节
   */
  private generateSection(
    section: ReportSection,
    data: Record<string, unknown>
  ): string {
    let content = `## ${section.title}\n\n`
    
    // 替换模板变量
    let text = this.replaceVariables(section.template, data)
    
    // 应用规则
    if (section.rules) {
      for (const rule of section.rules) {
        if (this.evaluateCondition(rule.condition, data)) {
          text += ' ' + rule.template
        }
      }
    }
    
    content += text + '\n\n'
    return content
  }
  
  /**
   * 替换变量
   */
  private replaceVariables(template: string, data: Record<string, unknown>): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return data[key] !== undefined ? String(data[key]) : match
    })
  }
  
  /**
   * 评估条件
   */
  private evaluateCondition(condition: string, data: Record<string, unknown>): boolean {
    try {
      // 简单的条件评估
      const parts = condition.split(/\s+/)
      if (parts.length === 3) {
        const [key, operator, value] = parts
        const dataValue = data[key]
        const compareValue = parseFloat(value)
        
        switch (operator) {
          case '<':
            return dataValue < compareValue
          case '>':
            return dataValue > compareValue
          case '=':
          case '==':
            return dataValue == compareValue
          case '<=':
            return dataValue <= compareValue
          case '>=':
            return dataValue >= compareValue
          default:
            return false
        }
      }
      return false
    } catch (__error) {
      return false
    }
  }
  
  /**
   * 生成洞察
   */
  private generateInsights(analysis: Record<string, unknown>): string[] {
    const insights: string[] = []
    
    // 离职率分析
    if (analysis.turnoverRate > 1) {
      insights.push(`离职率较高（${analysis.turnoverRate}%），建议关注员工满意度和福利待遇`)
    } else if (analysis.turnoverRate < 0.5) {
      insights.push(`离职率保持在健康水平（${analysis.turnoverRate}%），员工稳定性良好`)
    }
    
    // 增长率分析
    if (analysis.employeeGrowth > 5) {
      insights.push(`员工数量快速增长（${analysis.employeeGrowth}%），需要关注组织效率和管理能力`)
    }
    
    // 考勤分析
    if (analysis.attendanceRate < 90) {
      insights.push(`出勤率偏低（${analysis.attendanceRate}%），建议加强考勤管理`)
    }
    
    return insights
  }
  
  /**
   * 生成建议
   */
  private generateRecommendations(analysis: Record<string, unknown>): string[] {
    const recommendations: string[] = []
    
    // 基于数据分析生成建议
    if (analysis.turnoverRate > 1) {
      recommendations.push('建议开展员工满意度调查，了解离职原因')
      recommendations.push('优化薪酬福利体系，提高员工留存率')
    }
    
    if (analysis.employeeGrowth > 5) {
      recommendations.push('完善新员工培训体系，确保快速融入')
      recommendations.push('优化组织架构，提升管理效率')
    }
    
    if (analysis.attendanceRate < 90) {
      recommendations.push('加强考勤制度宣贯和执行')
      recommendations.push('了解缺勤原因，提供必要支持')
    }
    
    return recommendations
  }
  
  /**
   * 生成摘要
   */
  private generateSummary(sections: string[]): string {
    // 提取第一段作为摘要
    const firstSection = sections[0] || ''
    const lines = firstSection.split('\n').filter(line => line.trim())
    
    // 去除标题，保留内容
    const contentLines = lines.slice(1, 3)
    return contentLines.join(' ').trim()
  }
  
  /**
   * 格式化内容
   */
  private formatContent(sections: string[], format: string): string {
    const content = sections.join('\n')
    
    switch (format) {
      case 'html':
        return this.convertToHtml(content)
      case 'markdown':
        return content
      case 'text':
        return this.convertToText(content)
      default:
        return content
    }
  }
  
  /**
   * 转换为HTML
   */
  private convertToHtml(markdown: string): string {
    // 简单的Markdown到HTML转换
    const html = markdown
      .replace(/## (.+)/g, '<h2>$1</h2>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/^/, '<p>')
      .replace(/$/, '</p>')
    
    return `<div class="report-content">${html}</div>`
  }
  
  /**
   * 转换为纯文本
   */
  private convertToText(markdown: string): string {
    return markdown
      .replace(/## /g, '')
      .replace(/\*\*/g, '')
      .replace(/\*/g, '')
      .replace(/_/g, '')
  }
  
  /**
   * 统计字数
   */
  private countWords(content: string): number {
    // 中文字符数 + 英文单词数
    const chineseCount = (content.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishWords = (content.match(/[a-zA-Z]+/g) || []).length
    return chineseCount + englishWords
  }
  
  /**
   * 统计数据点
   */
  private countDataPoints(data: Record<string, unknown>): number {
    let count = 0
    
   
    const countRecursive = (obj: unknown) => {
      for (const key in obj) {
        if (typeof obj[key] === 'number') {
          count++
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          countRecursive(obj[key])
        }
      }
    }
    
    countRecursive(data)
    return count
  }
  
  /**
   * 延迟
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  /**
   * 注册模板
   */
  registerTemplate(template: ReportTemplate): void {
    this.templates.set(template.id, template)
  }
  
  /**
   * 获取模板
   */
  getTemplate(id: string): ReportTemplate | undefined {
    return this.templates.get(id)
  }
  
  /**
   * 获取所有模板
   */
  getAllTemplates(): ReportTemplate[] {
    return Array.from(this.templates.values())
  }
  
  /**
   * 删除模板
   */
  removeTemplate(id: string): void {
    this.templates.delete(id)
  }
  
  /**
   * 清空缓存
   */
  clearCache(): void {
    this.analysisCache.clear()
  }
}

// 导出单例
export const reportGenerator = ReportGenerator.getInstance()

// 导出Vue组合式API
export function useReportGenerator() {
  const generator = reportGenerator
  
  const generateReport = async (config: GenerateConfig) => {
    return await generator.generate(config)
  }
  
  return {
    generating: generator.generating,
    progress: generator.progress,
    generateReport,
    registerTemplate: (template: ReportTemplate) => generator.registerTemplate(template),
    getTemplate: (id: string) => generator.getTemplate(id),
    getAllTemplates: () => generator.getAllTemplates(),
    clearCache: () => generator.clearCache()
  }
}