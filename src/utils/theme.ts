/**
 * 主题工具函数
 */

export type Theme = 'light' | 'dark' | 'auto'

const THEME_KEY = 'hr-theme'

/**
 * 获取系统主题
 */
export function getSystemTheme(): 'light' | 'dark' {
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

/**
 * 获取当前主题设置
 */
export function getThemeSetting(): Theme {
  const saved = localStorage.getItem(THEME_KEY) as Theme | null
  return saved && ['light', 'dark', 'auto'].includes(saved) ? saved : 'auto'
}

/**
 * 获取实际主题
 */
export function getActualTheme(): 'light' | 'dark' {
  const setting = getThemeSetting()
  return setting === 'auto' ? getSystemTheme() : setting
}

/**
 * 设置主题
 */
export function setTheme(theme: Theme) {
  localStorage.setItem(THEME_KEY, theme)
  applyTheme()
}

/**
 * 应用主题
 */
export function applyTheme() {
  const actualTheme = getActualTheme()
  const root = document.documentElement

  // 移除旧主题
  root.removeAttribute('data-theme')

  // 应用新主题
  root.setAttribute('data-theme', actualTheme)

  // 更新 meta 标签
  updateMetaTheme(actualTheme)
}

/**
 * 更新 meta 主题色
 */
function updateMetaTheme(theme: 'light' | 'dark') {
  let metaTheme = document.querySelector('meta[name="theme-color"]')
  
  if (!metaTheme) {
    metaTheme = document.createElement('meta')
    metaTheme.setAttribute('name', 'theme-color')
    document.head.appendChild(metaTheme)
  }
  
  metaTheme.setAttribute('content', theme === 'dark' ? '#141414' : '#ffffff')
}

/**
 * 监听系统主题变化
 */
export function watchSystemTheme(callback?: (theme: 'light' | 'dark') => void) {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  
  const handler = (e: MediaQueryListEvent) => {
    const theme = e.matches ? 'dark' : 'light'
    
    // 如果是自动模式，应用新主题
    if (getThemeSetting() === 'auto') {
      applyTheme()
    }
    
    callback?.(theme)
  }
  
  mediaQuery.addEventListener('change', handler)
  
  // 返回清理函数
  return () => {
    mediaQuery.removeEventListener('change', handler)
  }
}

/**
 * 初始化主题
 */
export function initTheme() {
  applyTheme()
  watchSystemTheme()
}

/**
 * 主题色配置
 */
export const themeColors = {
  light: {
    primary: '#409EFF',
    success: '#67C23A',
    warning: '#E6A23C',
    danger: '#F56C6C',
    info: '#909399',
    
    bgColor: '#ffffff',
    bgColorPage: '#f5f7fa',
    textColorPrimary: '#303133',
    textColorRegular: '#606266',
    borderColor: '#DCDFE6'
  },
  dark: {
    primary: '#409EFF',
    success: '#67C23A',
    warning: '#E6A23C',
    danger: '#F56C6C',
    info: '#909399',
    
    bgColor: '#141414',
    bgColorPage: '#0a0a0a',
    textColorPrimary: '#E5EAF3',
    textColorRegular: '#CFD3DC',
    borderColor: '#4C4D4F'
  }
}

/**
 * 获取当前主题色
 */
export function getCurrentThemeColors() {
  return themeColors[getActualTheme()]
}

/**
 * 主题相关的 CSS 变量
 */
export function setThemeCssVars(theme: 'light' | 'dark') {
  const root = document.documentElement
  const colors = themeColors[theme]
  
  // 设置 CSS 变量
  Object.entries(colors).forEach(([key, value]) => {
    root.style.setProperty(`--hr-${key}`, value)
  })
}

/**
 * 判断是否为暗黑模式
 */
export function isDarkMode(): boolean {
  return getActualTheme() === 'dark'
}

/**
 * 切换主题
 */
export function toggleTheme() {
  const current = getThemeSetting()
  const themes: Theme[] = ['light', 'dark', 'auto']
  const index = themes.indexOf(current)
  const next = themes[(index + 1) % themes.length]
  setTheme(next)
}

/**
 * 主题图标映射
 */
export const themeIcons = {
  light: 'Sunny',
  dark: 'Moon',
  auto: 'Monitor'
}

/**
 * 主题名称映射
 */
export const themeNames = {
  light: '浅色模式',
  dark: '深色模式',
  auto: '跟随系统'
}