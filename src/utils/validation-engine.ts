/**
 * 数据校验引擎
 * @description 用于执行报表数据的各种校验规则
 */

import type { ValidationRule, ValidationResult } from '@/types/reportValidation'
import { evaluate } from 'mathjs'

export class ValidationEngine {
  /**
   * 执行单个校验规则
   * @param rule 校验规则
   * @param data 待校验的数据
   * @returns 校验结果
   */
  static async validateRule(
    rule: ValidationRule,
    data: Record<string, unknown>
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      ruleId: rule.id || 'unknown',
      ruleName: rule.name,
      status: 'passed',
      message: '',
      affectedData: [],
      timestamp: new Date()
    }

    try {
      switch (rule.type) {
        case 'formula':
          return this.validateFormula(rule, data)

        case 'range':
          return this.validateRange(rule, data)

        case 'consistency':
          return this.validateConsistency(rule, data)

        case 'format':
          return this.validateFormat(rule, data)

        case 'business':
          return this.validateBusiness(rule, data)

        default:
          result.status = 'failed'
          result.message = `未知的校验类型: ${rule.type}`
          return result
      }
    } catch (__error) {
      result.status = 'failed'
      result.message = `校验执行出错: ${error}`
      return result
    }
  }

  /**
   * 公式校验
   * @param rule 校验规则
   * @param data 数据
   * @returns 校验结果
   */
  private static validateFormula(
    rule: ValidationRule,
    data: Record<string, unknown>
  ): ValidationResult {
    const result: ValidationResult = {
      ruleId: rule.id || 'unknown',
      ruleName: rule.name,
      status: 'passed',
      message: '',
      affectedData: [],
      timestamp: new Date()
    }

    try {
      // 替换表达式中的变量
      const expression = rule.expression || ''
      const variables: Record<string, unknown> = {}

      // 提取变量名并替换
      const variablePattern = /[a-zA-Z_][\w]*/g
      const matches = expression.match(variablePattern) || []

      matches.forEach(varName => {
        if (data.hasOwnProperty(varName)) {
          variables[varName] = data[varName]
        }
      })

      // 使用 mathjs 计算表达式
      const isValid = evaluate(expression, variables)

      if (!isValid) {
        result.status = 'failed'
        result.message = rule.errorMessage || `公式校验失败: ${expression}`

        // 记录影响的数据
        Object.entries(variables).forEach(([key, value]) => {
          result.affectedData?.push({
            row: 1,
            column: key,
            value: value,
            expectedValue: '符合公式要求'
          })
        })
      }
    } catch (__error) {
      result.status = 'failed'
      result.message = `公式计算错误: ${error}`
    }

    return result
  }

  /**
   * 范围校验
   * @param rule 校验规则
   * @param data 数据
   * @returns 校验结果
   */
  private static validateRange(rule: ValidationRule, data: Record<string, unknown>): ValidationResult {
    const result: ValidationResult = {
      ruleId: rule.id || 'unknown',
      ruleName: rule.name,
      status: 'passed',
      message: '',
      affectedData: [],
      timestamp: new Date()
    }

    const metadata = rule.metadata as unknown
    if (!metadata || !metadata.field) {
      result.status = 'failed'
      result.message = '范围校验配置不完整'
      return result
    }

    const value = data[metadata.field]
    const { min, max } = metadata

    if (min !== undefined && value < min) {
      result.status = 'failed'
      result.message = rule.errorMessage || `${metadata.field} 的值 ${value} 小于最小值 ${min}`
      result.affectedData?.push({
        row: 1,
        column: metadata.field,
        value: value,
        expectedValue: `>= ${min}`
      })
    }

    if (max !== undefined && value > max) {
      result.status = 'failed'
      result.message = rule.errorMessage || `${metadata.field} 的值 ${value} 大于最大值 ${max}`
      result.affectedData?.push({
        row: 1,
        column: metadata.field,
        value: value,
        expectedValue: `<= ${max}`
      })
    }

    return result
  }

  /**
   * 一致性校验
   * @param rule 校验规则
   * @param data 数据
   * @returns 校验结果
   */
  private static validateConsistency(
    rule: ValidationRule,
    data: Record<string, unknown>
  ): ValidationResult {
    const result: ValidationResult = {
      ruleId: rule.id || 'unknown',
      ruleName: rule.name,
      status: 'passed',
      message: '',
      affectedData: [],
      timestamp: new Date()
    }

    const metadata = rule.metadata as unknown
    if (!metadata || !metadata.fields || metadata.fields.length < 2) {
      result.status = 'failed'
      result.message = '一致性校验配置不完整'
      return result
    }

    const values = metadata.fields.map((field: string) => data[field])
    const firstValue = values[0]
    const inconsistentFields: string[] = []

    values.forEach((value: unknown, index: number) => {
      if (index > 0 && value !== firstValue) {
        inconsistentFields.push(metadata.fields[index])
      }
    })

    if (inconsistentFields.length > 0) {
      result.status = 'failed'
      result.message = rule.errorMessage || `字段值不一致: ${metadata.fields.join(', ')}`

      metadata.fields.forEach((field: string, index: number) => {
        result.affectedData?.push({
          row: 1,
          column: field,
          value: values[index],
          expectedValue: firstValue
        })
      })
    }

    return result
  }

  /**
   * 格式校验
   * @param rule 校验规则
   * @param data 数据
   * @returns 校验结果
   */
  private static validateFormat(rule: ValidationRule, data: Record<string, unknown>): ValidationResult {
    const result: ValidationResult = {
      ruleId: rule.id || 'unknown',
      ruleName: rule.name,
      status: 'passed',
      message: '',
      affectedData: [],
      timestamp: new Date()
    }

    const metadata = rule.metadata as unknown
    if (!metadata || !metadata.field || !metadata.pattern) {
      result.status = 'failed'
      result.message = '格式校验配置不完整'
      return result
    }

    const value = data[metadata.field]
    const pattern = new RegExp(metadata.pattern)

    if (!pattern.test(value)) {
      result.status = 'failed'
      result.message = rule.errorMessage || `${metadata.field} 的格式不正确`
      result.affectedData?.push({
        row: 1,
        column: metadata.field,
        value: value,
        expectedValue: `符合格式: ${metadata.pattern}`
      })
    }

    return result
  }

  /**
   * 业务规则校验
   * @param rule 校验规则
   * @param data 数据
   * @returns 校验结果
   */
  private static validateBusiness(
    rule: ValidationRule,
    data: Record<string, unknown>
  ): ValidationResult {
    const result: ValidationResult = {
      ruleId: rule.id || 'unknown',
      ruleName: rule.name,
      status: 'passed',
      message: '',
      affectedData: [],
      timestamp: new Date()
    }

    const metadata = rule.metadata as unknown
    if (!metadata || !metadata.script) {
      result.status = 'failed'
      result.message = '业务规则配置不完整'
      return result
    }

    try {
      // 创建沙箱环境执行脚本
      const sandbox = {
        data,
        result: true,
        message: '',
        affectedData: []
      }

      // 使用 Function 构造函数创建安全的执行环境
      const scriptFunction = new Function(
        'sandbox',
        `
        with (sandbox) {
          ${metadata.script}
          return { result, message, affectedData }
        }
      `
      )

      const scriptResult = scriptFunction(sandbox)

      if (!scriptResult.result) {
        result.status = 'failed'
        result.message = rule.errorMessage || scriptResult.message || '业务规则校验失败'
        result.affectedData = scriptResult.affectedData || []
      }
    } catch (__error) {
      result.status = 'failed'
      result.message = `业务规则执行错误: ${error}`
    }

    return result
  }

  /**
   * 批量执行校验规则
   * @param rules 校验规则列表
   * @param data 待校验的数据
   * @returns 所有校验结果
   */
  static async validateBatch(
    rules: ValidationRule[],
    data: Record<string, unknown>
  ): Promise<ValidationResult[]> {
    const enabledRules = rules.filter(rule => rule.isEnabled)
    const results: ValidationResult[] = []

    for (const rule of enabledRules) {
      const result = await this.validateRule(rule, data)
      results.push(result)
    }

    return results
  }

  /**
   * 获取校验摘要
   * @param results 校验结果列表
   * @returns 校验摘要
   */
  static getSummary(results: ValidationResult[]) {
    const total = results.length
    const passed = results.filter(r => r.status === 'passed').length
    const failed = results.filter(r => r.status === 'failed').length

    return {
      total,
      passed,
      failed,
      passRate: total > 0 ? ((passed / total) * 100).toFixed(2) + '%' : '0%',
      hasErrors: failed > 0
    }
  }
}

export default ValidationEngine
