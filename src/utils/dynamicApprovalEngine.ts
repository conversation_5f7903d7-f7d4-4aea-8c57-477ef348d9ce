import { scriptEngine } from './scriptEngine'
import type { User } from '@/types/user'

interface ApprovalStep {
  id: string
  name: string
  type: 'condition' | 'approver' | 'script' | 'parallel' | 'gateway'
  condition?: string
  approverRule?: string
  script?: string
  steps?: ApprovalStep[]
  properties?: Record<string, unknown>
}

interface ApprovalProcess {
  id: string
  name: string
  version: number
  steps: ApprovalStep[]
  variables: Record<string, unknown>
}

interface ApprovalContext {
  process: ApprovalProcess
  currentStep: string
  variables: Record<string, unknown>
  applicant: User
  history: ApprovalHistory[]
}

interface ApprovalHistory {
  stepId: string
  stepName: string
  approver?: User
  action: 'approve' | 'reject' | 'delegate' | 'skip'
  comment?: string
  timestamp: string
  duration?: number
}

interface ApprovalDecision {
  action: 'approve' | 'reject' | 'skip' | 'wait'
  nextSteps: string[]
  reason: string
  requiredApprovers?: User[]
  conditions?: Record<string, unknown>
}

/**
 * CLEAN-DATA-011: 动态审批逻辑
 * 支持复杂的动态审批流程，包括条件分支、并行审批、脚本逻辑等
 */
export class DynamicApprovalEngine {
  private processingSteps = new Set<string>()
  
  // 执行审批步骤
  async executeApprovalStep(
    context: ApprovalContext, 
    stepId: string, 
    userAction?: { action: string; comment?: string; approver?: User }
  ): Promise<ApprovalDecision> {
    try {
      if (this.processingSteps.has(stepId)) {
        throw new Error('步骤正在处理中，请勿重复执行')
      }
      
      this.processingSteps.add(stepId)
      
      const step = this.findStep(context.process.steps, stepId)
      if (!step) {
        throw new Error(`未找到审批步骤: ${stepId}`)
      }
      
      `)
      
      let decision: ApprovalDecision
      
      switch (step.type) {
        case 'condition':
          decision = await this.executeConditionStep(context, step)
          break
        case 'approver':
          decision = await this.executeApproverStep(context, step, userAction)
          break
        case 'script':
          decision = await this.executeScriptStep(context, step)
          break
        case 'parallel':
          decision = await this.executeParallelStep(context, step)
          break
        case 'gateway':
          decision = await this.executeGatewayStep(context, step)
          break
        default:
          throw new Error(`不支持的步骤类型: ${step.type}`)
      }
      
      // 记录审批历史
      if (userAction) {
        this.recordApprovalHistory(context, step, userAction)
      }
      
      return decision
      
    } catch (__error) {
      throw error
    } finally {
      this.processingSteps.delete(stepId)
    }
  }
  
  // 执行条件步骤
  private async executeConditionStep(context: ApprovalContext, step: ApprovalStep): Promise<ApprovalDecision> {
    if (!step.condition) {
      throw new Error('条件步骤缺少条件表达式')
    }
    
    try {
      const conditionResult = await scriptEngine.evaluateCondition(step.condition, {
        variables: context.variables,
        user: context.applicant,
        process: context.process
      })
      
      // 根据条件结果确定下一步
      const nextSteps = this.getConditionalNextSteps(step, conditionResult)
      
      return {
        action: 'skip',
        nextSteps,
        reason: `条件评估: ${step.condition} = ${conditionResult}`,
        conditions: { [step.id]: conditionResult }
      }
      
    } catch (__error) {
      return {
        action: 'reject',
        nextSteps: [],
        reason: `条件评估失败: ${error.message}`
      }
    }
  }
  
  // 执行审批人步骤
  private async executeApproverStep(
    context: ApprovalContext, 
    step: ApprovalStep, 
    userAction?: { action: string; comment?: string; approver?: User }
  ): Promise<ApprovalDecision> {
    try {
      // 如果没有用户操作，获取需要的审批人
      if (!userAction) {
        const approvers = await this.getStepApprovers(context, step)
        return {
          action: 'wait',
          nextSteps: [],
          reason: `等待审批人操作: ${approvers.map(a => a.name).join(', ')}`,
          requiredApprovers: approvers
        }
      }
      
      // 处理用户审批操作
      const {action, comment} =  userAction
      
      switch (action) {
        case 'approve':
          // 检查是否需要更多审批人
          const remainingApprovers  comment?: string; approver?: User }
  ) {
    const history: ApprovalHistory = {
      stepId: step.id,
      stepName: step.name,
      approver: userAction.approver,
      action: userAction.action as unknown,
      comment: userAction.comment,
      timestamp: new Date().toISOString(),
      duration: 0 // 实际项目中计算处理时长
    }
    
    context.history.push(history)
  }
  
  // 验证审批流程
  validateApprovalProcess(process: ApprovalProcess): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    // 检查基本信息
    if (!process.id || !process.name) {
      errors.push('流程缺少ID或名称')
    }
    
    if (!process.steps || process.steps.length === 0) {
      errors.push('流程缺少步骤')
    }
    
    // 检查步骤
    this.validateSteps(process.steps, errors)
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  // 验证步骤
  private validateSteps(steps: ApprovalStep[], errors: string[], path = ''): void {
    steps.forEach((step, index) => {
      const stepPath = path ? `${path}.${index}` : `${index}`
      
      // 检查基本信息
      if (!step.id || !step.name || !step.type) {
        errors.push(`步骤 ${stepPath} 缺少必要信息`)
      }
      
      // 检查类型特定的配置
      switch (step.type) {
        case 'condition':
          if (!step.condition) {
            errors.push(`条件步骤 ${stepPath} 缺少条件表达式`)
          }
          break
        case 'approver':
          if (!step.approverRule) {
            errors.push(`审批步骤 ${stepPath} 缺少审批人规则`)
          }
          break
        case 'script':
          if (!step.script) {
            errors.push(`脚本步骤 ${stepPath} 缺少脚本内容`)
          }
          break
      }
      
      // 递归检查子步骤
      if (step.steps) {
        this.validateSteps(step.steps, errors, stepPath)
      }
    })
  }
}

// 全局动态审批引擎实例
export const dynamicApprovalEngine = new DynamicApprovalEngine()

// 便捷函数
export async function executeApproval(process: ApprovalProcess, stepId: string, variables: Record<string, unknown>, applicant: User, userAction?: { action: string; comment?: string; approver?: User }): Promise<ApprovalDecision> {
  const context: ApprovalContext = {
    process,
    currentStep: stepId,
    variables,
    applicant,
    history: []
  }
  
  return dynamicApprovalEngine.executeApprovalStep(context, stepId, userAction)
}