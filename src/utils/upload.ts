/**
 * 文件上传工具类
 * 支持大文件分片上传、断点续传、进度监控
 */
import { ElMessage } from 'element-plus'
import axios from 'axios'
import type { AxiosProgressEvent } from 'axios'
import SparkMD5 from 'spark-md5'

// CancelTokenSource 类型定义
interface CancelTokenSource {
  token: unknown
  cancel: (message?: string) => void
}

// 分片大小：5MB
const CHUNK_SIZE = 5 * 1024 * 1024

// 上传状态
export enum UploadStatus {
  WAITING = 'waiting',
  UPLOADING = 'uploading',
  PAUSED = 'paused',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 文件分片信息
export interface FileChunk {
  index: number
  start: number
  end: number
  blob: Blob
  hash?: string
  uploaded?: boolean
}

// 上传任务
export interface UploadTask {
  id: string
  file: File
  fileName: string
  fileSize: number
  fileHash?: string
  chunks: FileChunk[]
  uploadedChunks: number
  status: UploadStatus
  progress: number
  speed: number
  remainTime: number
  startTime?: number
  pauseTime?: number
  cancelSource?: CancelTokenSource
  onProgress?: (progress: number) => void

  onSuccess?: (response: unknown) => void

  onError?: (error: unknown) => void
}

// 上传配置
export interface UploadConfig {
  url: string
  headers?: Record<string, string>
  data?: Record<string, unknown>
  withCredentials?: boolean
  timeout?: number
  maxRetries?: number
  chunkSize?: number
  concurrent?: number
}

class FileUploader {
  private tasks: Map<string, UploadTask> = new Map()
  private uploadingTasks: Set<string> = new Set()
  private maxConcurrent = 3

  /**
   * 创建上传任务
   */
  async createTask(
    file: File,
    config: UploadConfig,
    callbacks?: {
      onProgress?: (progress: number) => void

      onSuccess?: (response: unknown) => void

      onError?: (error: unknown) => void
    }
  ): Promise<UploadTask> {
    // 生成任务ID
    const taskId = this.generateTaskId()

    // 计算文件hash
    const fileHash = await this.calculateFileHash(file)

    // 创建文件分片
    const chunks = this.createFileChunks(file, config.chunkSize || CHUNK_SIZE)

    // 创建任务
    const task: UploadTask = {
      id: taskId,
      file,
      fileName: file.name,
      fileSize: file.size,
      fileHash,
      chunks,
      uploadedChunks: 0,
      status: UploadStatus.WAITING,
      progress: 0,
      speed: 0,
      remainTime: 0,
      ...callbacks
    }

    this.tasks.set(taskId, task)

    // 检查文件是否已存在（秒传）
    const exists = await this.checkFileExists(fileHash, config)
    if (exists) {
      task.status = UploadStatus.SUCCESS
      task.progress = 100
      task.uploadedChunks = chunks.length
      task.onSuccess?.(exists)
      return task
    }

    // 检查断点续传
    const uploadedChunks = await this.checkUploadedChunks(fileHash, config)
    if (uploadedChunks.length > 0) {
      uploadedChunks.forEach(index => {
        if (chunks[index]) {
          chunks[index].uploaded = true
          task.uploadedChunks++
        }
      })
      task.progress = Math.floor((task.uploadedChunks / chunks.length) * 100)
    }

    return task
  }

  /**
   * 开始上传
   */
  async startUpload(taskId: string, config: UploadConfig): Promise<void> {
    const task = this.tasks.get(taskId)
    if (!task) {
      throw new Error('上传任务不存在')
    }

    if (task.status === UploadStatus.SUCCESS) {
      return
    }

    task.status = UploadStatus.UPLOADING
    task.startTime = Date.now()
    task.cancelSource = axios.CancelToken.source()

    this.uploadingTasks.add(taskId)

    try {
      // 并发上传分片
      await this.uploadChunks(task, config)

      // 合并分片
      await this.mergeChunks(task, config)

      task.status = UploadStatus.SUCCESS
      task.progress = 100

      ElMessage.success(`文件 ${task.fileName} 上传成功`)
      task.onSuccess?.({ fileHash: task.fileHash, fileName: task.fileName })
    } catch (error: unknown) {
      if (axios.isCancel(error)) {
        task.status = UploadStatus.PAUSED
        ElMessage.info('上传已暂停')
      } else {
        task.status = UploadStatus.ERROR
        ElMessage.error(`上传失败: ${error.message}`)
        task.onError?.(error)
      }
    } finally {
      this.uploadingTasks.delete(taskId)
    }
  }

  /**
   * 暂停上传
   */
  pauseUpload(taskId: string): void {
    const task = this.tasks.get(taskId)
    if (!task || task.status !== UploadStatus.UPLOADING) {
      return
    }

    task.cancelSource?.cancel('用户暂停上传')
    task.status = UploadStatus.PAUSED
    task.pauseTime = Date.now()
  }

  /**
   * 恢复上传
   */
  async resumeUpload(taskId: string, config: UploadConfig): Promise<void> {
    const task = this.tasks.get(taskId)
    if (!task || task.status !== UploadStatus.PAUSED) {
      return
    }

    await this.startUpload(taskId, config)
  }

  /**
   * 取消上传
   */
  cancelUpload(taskId: string): void {
    const task = this.tasks.get(taskId)
    if (!task) {
      return
    }

    task.cancelSource?.cancel('用户取消上传')
    this.tasks.delete(taskId)
    this.uploadingTasks.delete(taskId)

    ElMessage.info('上传已取消')
  }

  /**
   * 重试上传
   */
  async retryUpload(taskId: string, config: UploadConfig): Promise<void> {
    const task = this.tasks.get(taskId)
    if (!task || task.status !== UploadStatus.ERROR) {
      return
    }

    task.status = UploadStatus.WAITING
    await this.startUpload(taskId, config)
  }

  /**
   * 获取任务信息
   */
  getTask(taskId: string): UploadTask | undefined {
    return this.tasks.get(taskId)
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): UploadTask[] {
    return Array.from(this.tasks.values())
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 计算文件hash
   */
  private calculateFileHash(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      const spark = new SparkMD5.ArrayBuffer()
      const chunkSize = 2 * 1024 * 1024 // 2MB chunks for hash calculation
      let currentChunk = 0
      const chunks = Math.ceil(file.size / chunkSize)

      reader.onload = _e => {
        if (e.target?.result) {
          spark.append(e.target.result as ArrayBuffer)
        }
        currentChunk++

        if (currentChunk < chunks) {
          loadNext()
        } else {
          resolve(spark.end())
        }
      }

      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }

      const loadNext = () => {
        const start = currentChunk * chunkSize
        const end = Math.min(start + chunkSize, file.size)
        reader.readAsArrayBuffer(file.slice(start, end))
      }

      loadNext()
    })
  }

  /**
   * 创建文件分片
   */
  private createFileChunks(file: File, chunkSize: number): FileChunk[] {
    const chunks: FileChunk[] = []
    const totalChunks = Math.ceil(file.size / chunkSize)

    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize
      const end = Math.min(start + chunkSize, file.size)

      chunks.push({
        index: i,
        start,
        end,
        blob: file.slice(start, end),
        uploaded: false
      })
    }

    return chunks
  }

  /**
   * 检查文件是否已存在
   */
  private async checkFileExists(fileHash: string, config: UploadConfig): Promise<unknown> {
    try {
      const response = await axios.post(
        `${config.url}/check`,
        { fileHash },
        {
          headers: config.headers,
          withCredentials: config.withCredentials,
          timeout: config.timeout || 30000
        }
      )

      return response.data.exists ? response.data : null
    } catch (__error) {
      return null
    }
  }

  /**
   * 检查已上传的分片
   */
  private async checkUploadedChunks(fileHash: string, config: UploadConfig): Promise<number[]> {
    try {
      const response = await axios.get(`${config.url}/chunks/${fileHash}`, {
        headers: config.headers,
        withCredentials: config.withCredentials,
        timeout: config.timeout || 30000
      })

      return response.data.uploadedChunks || []
    } catch (__error) {
      return []
    }
  }

  /**
   * 上传分片
   */
  private async uploadChunks(task: UploadTask, config: UploadConfig): Promise<void> {
    const concurrent = config.concurrent || this.maxConcurrent
    const chunks = task.chunks.filter(chunk => !chunk.uploaded)

    // 并发控制
    const uploadChunk = async (chunk: FileChunk): Promise<void> => {
      const formData = new FormData()
      formData.append('file', chunk.blob)
      formData.append('chunkIndex', chunk.index.toString())
      formData.append('totalChunks', task.chunks.length.toString())
      formData.append('fileHash', task.fileHash!)
      formData.append('fileName', task.fileName)

      // 添加额外数据
      if (config.data) {
        Object.entries(config.data).forEach(([key, value]) => {
          formData.append(key, value)
        })
      }

      const startTime = Date.now()
      let lastLoaded = 0

      await axios.post(`${config.url}/chunk`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          ...config.headers
        },
        withCredentials: config.withCredentials,
        timeout: config.timeout || 5 * 60 * 1000, // 5分钟超时
        cancelToken: task.cancelSource?.token,
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          if (progressEvent.loaded && progressEvent.total) {
            // 计算速度
            const currentTime = Date.now()
            const timeDiff = (currentTime - startTime) / 1000
            const loadedDiff = progressEvent.loaded - lastLoaded
            task.speed = loadedDiff / timeDiff
            lastLoaded = progressEvent.loaded

            // 更新进度
            const chunkProgress = progressEvent.loaded / progressEvent.total
            const totalProgress = (task.uploadedChunks + chunkProgress) / task.chunks.length
            task.progress = Math.floor(totalProgress * 100)

            // 计算剩余时间
            if (task.speed > 0) {
              const remainSize =
                task.fileSize - (task.uploadedChunks * CHUNK_SIZE + progressEvent.loaded)
              task.remainTime = remainSize / task.speed
            }

            task.onProgress?.(task.progress)
          }
        }
      })

      chunk.uploaded = true
      task.uploadedChunks++
    }

    // 并发上传
    const uploadQueue: Promise<void>[] = []
    for (let i = 0; i < chunks.length; i++) {
      uploadQueue.push(uploadChunk(chunks[i]))

      if (uploadQueue.length >= concurrent || i === chunks.length - 1) {
        await Promise.all(uploadQueue)
        uploadQueue.length = 0
      }
    }
  }

  /**
   * 合并分片
   */
  private async mergeChunks(task: UploadTask, config: UploadConfig): Promise<void> {
    await axios.post(
      `${config.url}/merge`,
      {
        fileHash: task.fileHash,
        fileName: task.fileName,
        totalChunks: task.chunks.length,
        ...config.data
      },
      {
        headers: config.headers,
        withCredentials: config.withCredentials,
        timeout: config.timeout || 60000,
        cancelToken: task.cancelSource?.token
      }
    )
  }
}

// 导出单例
export const fileUploader = new FileUploader()

// 导出便捷方法
export async function uploadFile(
  file: File,
  config: UploadConfig,
  callbacks?: {
    onProgress?: (progress: number) => void

    onSuccess?: (response: unknown) => void

    onError?: (error: unknown) => void
  }
): Promise<string> {
  const task = await fileUploader.createTask(file, config, callbacks)
  await fileUploader.startUpload(task.id, config)
  return task.id
}

// 导出文件大小格式化
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 导出时间格式化
export function formatTime(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)}分钟`
  } else {
    return `${Math.round(seconds / 3600)}小时`
  }
}

// 导出速度格式化
export function formatSpeed(bytesPerSecond: number): string {
  return formatFileSize(bytesPerSecond) + '/s'
}
