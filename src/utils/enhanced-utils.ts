/**
 * 增强的工具函数库
 * 提供常用的业务逻辑处理函数
 */

// ==================== 数据处理工具 ====================

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 对象差异比较
 */
export function objectDiff(obj1: Record<string, unknown>, obj2: Record<string, unknown>): Record<string, unknown> {
  const diff: Record<string, unknown> = {}
  
  for (const key in obj2) {
    if (obj2.hasOwnProperty(key)) {
      if (obj1[key] !== obj2[key]) {
        if (typeof obj2[key] === 'object' && obj2[key] !== null && !Array.isArray(obj2[key])) {
          const nestedDiff = objectDiff(
            (obj1[key] as Record<string, unknown>) || {}, 
            obj2[key] as Record<string, unknown>
          )
          if (Object.keys(nestedDiff).length > 0) {
            diff[key] = nestedDiff
          }
        } else {
          diff[key] = {
            from: obj1[key],
            to: obj2[key]
          }
        }
      }
    }
  }
  
  return diff
}

/**
 * 数组去重
 */
export function uniqueArray<T>(arr: T[], key?: keyof T): T[] {
  if (!key) {
    return [...new Set(arr)]
  }
  
  const seen = new Set()
  return arr.filter(item => {
    const value = item[key]
    if (seen.has(value)) {
      return false
    }
    seen.add(value)
    return true
  })
}

/**
 * 数组分组
 */
export function groupBy<T>(arr: T[], key: keyof T): Record<string, T[]> {
  return arr.reduce((groups, item) => {
    const groupKey = String(item[key])
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(item)
    return groups
  }, {} as Record<string, T[]>)
}

/**
 * 树形数据扁平化
 */
export function flattenTree<T extends { children?: T[] }>(
  tree: T[],
  childrenKey: keyof T = 'children' as keyof T
): T[] {
  const result: T[] = []
  
  function traverse(nodes: T[]) {
    nodes.forEach(node => {
      const { [childrenKey]: children, ...rest } = node
      result.push(rest as T)
      if (children && Array.isArray(children)) {
        traverse(children)
      }
    })
  }
  
  traverse(tree)
  return result
}

/**
 * 扁平数据转树形结构
 */
export function arrayToTree<T extends Record<string, unknown>>(
  arr: T[],
  options: {
    idKey?: string
    parentKey?: string
    childrenKey?: string
    rootValue?: unknown
  } = {}
): T[] {
  const {
    idKey = 'id',
    parentKey = 'parentId',
    childrenKey = 'children',
    rootValue = null
  } = options
  
  const itemMap = new Map<unknown, T & { [key: string]: T[] }>()
  const result: T[] = []
  
  // 初始化映射
  arr.forEach(item => {
    const enhancedItem = { ...item, [childrenKey]: [] } as T & { [key: string]: T[] }
    itemMap.set(item[idKey], enhancedItem)
  })
  
  // 构建树形结构
  arr.forEach(item => {
    const enhancedItem = itemMap.get(item[idKey])!
    const parentId = item[parentKey]
    
    if (parentId === rootValue) {
      result.push(enhancedItem)
    } else {
      const parent = itemMap.get(parentId)
      if (parent) {
        parent[childrenKey].push(enhancedItem)
      }
    }
  })
  
  return result
}

// ==================== 字符串处理工具 ====================

/**
 * 生成随机字符串
 */
export function randomString(length: number = 8): string {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 文本高亮
 */
export function highlightText(text: string, keyword: string, className: string = 'highlight'): string {
  if (!keyword) return text
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, `<span class="${className}">$1</span>`)
}

// ==================== 数字处理工具 ====================

/**
 * 数字格式化
 */
export function formatNumber(num: number, options: {
  decimals?: number
  separator?: string
  prefix?: string
  suffix?: string
} = {}): string {
  const { decimals = 0, separator = ',', prefix = '', suffix = '' } = options
  
  const parts = num.toFixed(decimals).split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  
  return prefix + parts.join('.') + suffix
}

/**
 * 百分比格式化
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return (value * 100).toFixed(decimals) + '%'
}

// ==================== 异步处理工具 ====================

/**
 * 重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      if (attempt === maxAttempts) {
        throw lastError
      }
      await new Promise(resolve => setTimeout(resolve, delay * attempt))
    }
  }
  
  throw lastError!
}

/**
 * 生成UUID
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 防抖动函数
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function(...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return function(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深度合并对象
 */
export function deepMerge<T extends Record<string, unknown>>(
  target: T,
  ...sources: Partial<T>[]
): T {
  if (!sources.length) return target
  const source = sources.shift()
  
  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} })
        deepMerge(target[key] as Record<string, unknown>, source[key] as Record<string, unknown>)
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    }
  }
  
  return deepMerge(target, ...sources)
}

/**
 * 判断是否为对象
 */
function isObject(item: unknown): item is Record<string, unknown> {
  return item !== null && typeof item === 'object' && !Array.isArray(item)
}
