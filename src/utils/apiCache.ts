/**
 * @name API请求缓存工具
 * @description 提供智能化的API请求缓存策略，支持TTL、LRU和自动失效
 */

// ===== 接口定义 =====
interface CacheItem<T = unknown> {
  /** 缓存数据 */
  data: T
  /** 创建时间戳 */
  timestamp: number
  /** 过期时间（毫秒） */
  ttl: number
  /** 访问次数 */
  accessCount: number
  /** 最后访问时间 */
  lastAccess: number
  /** 缓存键 */
  key: string
}

interface CacheConfig {
  /** 默认TTL（毫秒），默认5分钟 */
  defaultTTL?: number
  /** 最大缓存条目数量 */
  maxSize?: number
  /** 是否启用缓存 */
  enabled?: boolean
  /** 缓存键前缀 */
  prefix?: string
  /** 清理间隔（毫秒） */
  cleanupInterval?: number
}

interface CacheStats {
  /** 缓存命中次数 */
  hits: number
  /** 缓存未命中次数 */
  misses: number
  /** 当前缓存大小 */
  size: number
  /** 命中率 */
  hitRate: number
}

// ===== 缓存类 =====
export class ApiCache {
  private cache = new Map<string, CacheItem>()
  private config: Required<CacheConfig>
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0
  }
  private cleanupTimer: number | null = null

  constructor(config: CacheConfig = {}) {
    this.config = {
      defaultTTL: config.defaultTTL || 5 * 60 * 1000, // 5分钟
      maxSize: config.maxSize || 100,
      enabled: config.enabled !== false,
      prefix: config.prefix || 'api_cache',
      cleanupInterval: config.cleanupInterval || 2 * 60 * 1000 // 2分钟清理一次
    }

    // 启动自动清理
    this.startCleanup()

    // 页面卸载时清理
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.destroy()
      })
    }
  }

  /**
   * 生成缓存键
   */
  private generateKey(url: string, params?: Record<string, unknown>): string {
    const baseKey = `${this.config.prefix}:${url}`
    if (!params) return baseKey

    // 对参数进行排序和序列化，确保相同参数生成相同键
    const sortedParams = Object.keys(params)
      .sort()
      .reduce(
        (result, key) => {
          result[key] = params[key]
          return result
        },
        {} as Record<string, unknown>
      )

    const paramString = JSON.stringify(sortedParams)
    return `${baseKey}:${this.hashCode(paramString)}`
  }

  /**
   * 简单的哈希函数
   */
  private hashCode(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(item: CacheItem): boolean {
    return Date.now() - item.timestamp > item.ttl
  }

  /**
   * LRU淘汰策略
   */
  private evictLRU(): void {
    if (this.cache.size <= this.config.maxSize) return

    let oldestKey = ''
    let oldestTime = Date.now()

    for (const [key, item] of this.cache) {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
      this.updateStats()
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.size = this.cache.size
    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0
  }

  /**
   * 启动自动清理
   */
  private startCleanup(): void {
    if (this.cleanupTimer) return

    this.cleanupTimer = window.setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, item] of this.cache) {
      if (this.isExpired(item)) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => {
      this.cache.delete(key)
    })

    if (expiredKeys.length > 0) {
      this.updateStats()
      console.debug(`API缓存清理: 删除了${expiredKeys.length}个过期项`)
    }
  }

  /**
   * 获取缓存
   */
  get<T = unknown>(url: string, params?: Record<string, unknown>): T | null {
    if (!this.config.enabled) return null

    const key = this.generateKey(url, params)
    const item = this.cache.get(key)

    if (!item || this.isExpired(item)) {
      this.stats.misses++
      this.updateStats()

      if (item) {
        // 删除过期项
        this.cache.delete(key)
      }

      return null
    }

    // 更新访问信息
    item.accessCount++
    item.lastAccess = Date.now()

    this.stats.hits++
    this.updateStats()

    return item.data
  }

  /**
   * 设置缓存
   */
  set<T = unknown>(
    url: string,
    data: T,
    options?: {
      params?: Record<string, unknown>
      ttl?: number
    }
  ): void {
    if (!this.config.enabled) return

    const key = this.generateKey(url, options?.params)
    const now = Date.now()

    const item: CacheItem<T> = {
      data,
      timestamp: now,
      ttl: options?.ttl || this.config.defaultTTL,
      accessCount: 1,
      lastAccess: now,
      key
    }

    this.cache.set(key, item)

    // 检查是否需要淘汰
    this.evictLRU()
    this.updateStats()
  }

  /**
   * 删除指定缓存
   */
  delete(url: string, params?: Record<string, unknown>): boolean {
    const key = this.generateKey(url, params)
    const result = this.cache.delete(key)
    if (result) {
      this.updateStats()
    }
    return result
  }

  /**
   * 按模式删除缓存
   */
  deleteByPattern(pattern: string): number {
    const regex = new RegExp(pattern)
    const keysToDelete: string[] = []

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key)
    })

    if (keysToDelete.length > 0) {
      this.updateStats()
    }

    return keysToDelete.length
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
    this.stats = {
      hits: 0,
      misses: 0,
      size: 0,
      hitRate: 0
    }
    this.updateStats()
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats }
  }

  /**
   * 获取所有缓存键
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * 检查缓存是否存在且未过期
   */
  has(url: string, params?: Record<string, unknown>): boolean {
    if (!this.config.enabled) return false

    const key = this.generateKey(url, params)
    const item = this.cache.get(key)

    return item ? !this.isExpired(item) : false
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size
  }

  /**
   * 预热缓存（批量设置）
   */
  warmup<T = unknown>(
    entries: Array<{
      url: string
      data: T
      params?: Record<string, unknown>
      ttl?: number
    }>
  ): void {
    entries.forEach(entry => {
      this.set(entry.url, entry.data, {
        params: entry.params,
        ttl: entry.ttl
      })
    })
  }

  /**
   * 获取缓存配置
   */
  getConfig(): Required<CacheConfig> {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 销毁缓存实例
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    this.clear()
  }
}

// ===== 缓存策略枚举 =====
export enum CacheStrategy {
  /** 缓存优先 - 先从缓存获取，缓存失效则请求API */
  CACHE_FIRST = 'cache-first',
  /** 网络优先 - 先请求API，失败则使用缓存 */
  NETWORK_FIRST = 'network-first',
  /** 仅缓存 - 只使用缓存，不请求API */
  CACHE_ONLY = 'cache-only',
  /** 仅网络 - 只请求API，不使用缓存 */
  NETWORK_ONLY = 'network-only',
  /** 失效时重新验证 - 返回缓存但在后台更新 */
  STALE_WHILE_REVALIDATE = 'stale-while-revalidate'
}

// ===== 预设缓存配置 =====
export const CachePresets = {
  /** 快速缓存 - 适用于频繁访问的数据 */
  FAST: {
    defaultTTL: 30 * 1000, // 30秒
    maxSize: 50,
    cleanupInterval: 60 * 1000 // 1分钟
  } as CacheConfig,

  /** 标准缓存 - 适用于一般数据 */
  STANDARD: {
    defaultTTL: 5 * 60 * 1000, // 5分钟
    maxSize: 100,
    cleanupInterval: 2 * 60 * 1000 // 2分钟
  } as CacheConfig,

  /** 长期缓存 - 适用于很少变化的数据 */
  LONG_TERM: {
    defaultTTL: 30 * 60 * 1000, // 30分钟
    maxSize: 200,
    cleanupInterval: 5 * 60 * 1000 // 5分钟
  } as CacheConfig,

  /** 静态资源缓存 - 适用于不变的静态数据 */
  STATIC: {
    defaultTTL: 24 * 60 * 60 * 1000, // 24小时
    maxSize: 500,
    cleanupInterval: 60 * 60 * 1000 // 1小时
  } as CacheConfig
}

// ===== 全局缓存实例 =====
export const globalApiCache = new ApiCache(CachePresets.STANDARD)

// ===== 便捷方法 =====
export const cache = {
  /** 获取缓存 */
  get: <T = unknown>(url: string, params?: Record<string, unknown>) =>
    globalApiCache.get<T>(url, params),

  /** 设置缓存 */
  set: <T = unknown>(
    url: string,
    data: T,
    options?: {
      params?: Record<string, unknown>
      ttl?: number
    }
  ) => globalApiCache.set(url, data, options),

  /** 删除缓存 */
  delete: (url: string, params?: Record<string, unknown>) => globalApiCache.delete(url, params),

  /** 清空缓存 */
  clear: () => globalApiCache.clear(),

  /** 获取统计 */
  stats: () => globalApiCache.getStats(),

  /** 检查是否存在 */
  has: (url: string, params?: Record<string, unknown>) => globalApiCache.has(url, params)
}

export default ApiCache
