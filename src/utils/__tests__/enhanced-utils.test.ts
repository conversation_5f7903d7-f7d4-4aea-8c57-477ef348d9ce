/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
  import {
  deepClone,
  objectDiff,
  uniqueArray,
  groupBy,
  flattenTree,
  arrayToTree,
  camelToSnake,
  snakeToCamel,
  capitalize,
  randomString,
  highlightText,
  formatNumber,
  formatFileSize,
  calculatePercentage,
  formatRelativeTime,
  formatTimeRange,
  isValidEmail,
  isValidPhone,
  isValidIdCard,
  isValidUrl,
  copyToClipboard,
  downloadFile,
  getBrowserInfo,
  confirmAction,
  debounce,
  throttle,
  retry,
  generateUUID
} from '../enhanced-utils',
  describe('enhanced-utils', () => {
  describe('数据处理工具', () => {
    describe('deepClone', () => {
      it('应该深度克隆简单对象', () => {
        const obj = { a: 1, b: 'test'
  };
  const cloned = deepClone(obj)
        
        expect(cloned).toEqual(obj)
        expect(cloned).not.toBe(obj)
      })

      it('应该深度克隆嵌套对象', () => {
        const obj = { a: { b: { c: 1 } } },
  const cloned = deepClone(obj)
        
        expect(cloned).toEqual(obj)
        expect(cloned.a).not.toBe(obj.a)
        expect(cloned.a.b).not.toBe(obj.a.b)
      })

      it('应该克隆数组', () => {
        const arr = [1, 2, { a: 3 }]
        const cloned = deepClone(arr)
        
        expect(cloned).toEqual(arr)
        expect(cloned).not.toBe(arr)
        expect(cloned[2]).not.toBe(arr[2])
      })

      it('应该克隆日期对象', () => {
        const date = new Date('2025-06-18')
        const cloned = deepClone(date)
        
        expect(cloned).toEqual(date)
        expect(cloned).not.toBe(date)
      })

      it('应该处理null和undefined', () => {
        expect(deepClone(null)).toBe(null)
        expect(deepClone(undefined)).toBe(undefined)
      })
    })

    describe('objectDiff', () => {
      it('应该检测对象差异', () => {
        const obj1 = { a: 1, b: 'old'
  };
  const obj2 = { a: 1, b: 'new', c: 3 },
  const diff = objectDiff(obj1, obj2)
        
        expect(diff).toEqual({
          b: { from: 'old', to: 'new'
  },
          c: { from: undefined, to: 3 }
        })
      })

      it('应该检测嵌套对象差异', () => {
        const obj1 = { nested: { a: 1, b: 2 } },
  const obj2 = { nested: { a: 1, b: 3 } },
  const diff = objectDiff(obj1, obj2)
        
        expect(diff).toEqual({
          nested: {
            b: { from: 2, to: 3 }
          }
        })
      })
    })

    describe('uniqueArray', () => {
      it('应该去除数组重复项', () => {
        const arr = [1, 2, 2, 3, 3, 3]
        const unique = uniqueArray(arr)
        
        expect(unique).toEqual([1, 2, 3])
      })

      it('应该根据指定键去重', () => {
        const arr = [
          { id: 1, name: 'a'
  },
          { id: 2, name: 'b'
  },
          { id: 1, name: 'c' }
        ]
        const unique = uniqueArray(arr, 'id')
        
        expect(unique).toEqual([
          { id: 1, name: 'a'
  },
          { id: 2, name: 'b' }
        ])
      })
    })

    describe('groupBy', () => {
      it('应该按指定键分组', () => {
        const arr = [
          { type: 'A', value: 1 },
          { type: 'B', value: 2 },
          { type: 'A', value: 3 }
        ]
        const grouped = groupBy(arr, 'type')
        
        expect(grouped).toEqual({
          A: [{ type: 'A', value: 1 }, { type: 'A', value: 3 }],
          B: [{ type: 'B', value: 2 }]
        })
      })
    })

    describe('flattenTree', () => {
      it('应该扁平化树形结构', () => {
        const tree = [
          {
            id: 1,
            name: 'root',
            children: [
              { id: 2, name: 'child1'
  },
              { id: 3, name: 'child2', children: [{ id: 4, name: 'grandchild' }] }
            ]
          }
        ]
        const flattened = flattenTree(tree as any)
        
        expect(flattened).toEqual([
          { id: 1, name: 'root'
  },
          { id: 2, name: 'child1'
  },
          { id: 3, name: 'child2'
  },
          { id: 4, name: 'grandchild' }
        ])
      })
    })

    describe('arrayToTree', () => {
      it('应该将扁平数组转换为树形结构', () => {
        const arr = [
          { id: 1, name: 'root', parentId: null },
          { id: 2, name: 'child1', parentId: 1 },
          { id: 3, name: 'child2', parentId: 1 },
          { id: 4, name: 'grandchild', parentId: 2 }
        ]
        const tree = arrayToTree(arr)
        
        expect(tree).toHaveLength(1)
        expect((tree[0] as any).children).toHaveLength(2)
        expect((tree[0] as any).children[0].children).toHaveLength(1)
      })
    })
  })

  describe('字符串处理工具', () => {
    describe('camelToSnake', () => {
      it('应该将驼峰转换为下划线', () => {
        expect(camelToSnake('camelCase')).toBe('camel_case')
        expect(camelToSnake('XMLHttpRequest')).toBe('_x_m_l_http_request')
      })
    })

    describe('snakeToCamel', () => {
      it('应该将下划线转换为驼峰', () => {
        expect(snakeToCamel('snake_case')).toBe('snakeCase')
        expect(snakeToCamel('test_string')).toBe('testString')
      })
    })

    describe('capitalize', () => {
      it('应该首字母大写', () => {
        expect(capitalize('hello')).toBe('Hello')
        expect(capitalize('WORLD')).toBe('WORLD')
        expect(capitalize('')).toBe('')
      })
    })

    describe('randomString', () => {
      it('应该生成指定长度的随机字符串', () => {
        const str = randomString(10)
        expect(str).toHaveLength(10)
      })

      it('应该使用指定字符集', () => {
        const str = randomString(5, '123')
        expect(/^[123]+$/.test(str)).toBe(true)
      })
    })

    describe('highlightText', () => {
      it('应该高亮匹配的文本', () => {
        const result = highlightText('Hello World', 'World')
        expect(result).toBe('Hello <span class="highlight">World</span>')
      })

      it('应该使用自定义类名', () => {
        const result = highlightText('Hello World', 'Hello', 'custom')
        expect(result).toBe('<span class="custom">Hello</span> World')
      })
    })
  })

  describe('数字处理工具', () => {
    describe('formatNumber', () => {
      it('应该格式化数字', () => {
        expect(formatNumber(1234.567)).toBe('1,235')
        expect(formatNumber(1234.567, { decimals: 2 })).toBe('1,234.57')
        expect(formatNumber(1234, { prefix: '$', suffix: ' USD' })).toBe('$1,234 USD')
      })
    })

    describe('formatFileSize', () => {
      it('应该格式化文件大小', () => {
        expect(formatFileSize(0)).toBe('0 B')
        expect(formatFileSize(1024)).toBe('1 KB')
        expect(formatFileSize(1024 * 1024)).toBe('1 MB')
        expect(formatFileSize(1536)).toBe('1.5 KB')
      })
    })

    describe('calculatePercentage', () => {
      it('应该计算百分比', () => {
        expect(calculatePercentage(25, 100)).toBe(25.0)
        expect(calculatePercentage(1, 3, 2)).toBe(33.33)
        expect(calculatePercentage(10, 0)).toBe(0)
      })
    })
  })

  describe('日期时间工具', () => {
    describe('formatRelativeTime', () => {
      it('应该格式化相对时间', () => {
        const now = new Date()
        const oneMinuteAgo = new Date(now.getTime() - 60 * 1000)
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
        
        expect(formatRelativeTime(now)).toBe('刚刚')
        expect(formatRelativeTime(oneMinuteAgo)).toBe('1分钟前')
        expect(formatRelativeTime(oneHourAgo)).toBe('1小时前')
      })
    })

    describe('formatTimeRange', () => {
      it('应该格式化时间范围', () => {
        const start = new Date('2025-06-18 10:00:00')
        const end = new Date('2025-06-18 11:30:00')
        
        expect(formatTimeRange(start, end)).toBe('1小时30分钟')
      })
    })
  })

  describe('验证工具', () => {
    describe('isValidEmail', () => {
      it('应该验证邮箱格式', () => {
        expect(isValidEmail('<EMAIL>')).toBe(true)
        expect(isValidEmail('invalid-email')).toBe(false)
        expect(isValidEmail('test@')).toBe(false)
      })
    })

    describe('isValidPhone', () => {
      it('应该验证手机号格式', () => {
        expect(isValidPhone('13800138000')).toBe(true)
        expect(isValidPhone('12345678901')).toBe(false)
        expect(isValidPhone('1380013800')).toBe(false)
      })
    })

    describe('isValidIdCard', () => {
      it('应该验证身份证格式', () => {
        expect(isValidIdCard('123456789012345')).toBe(true)
        expect(isValidIdCard('123456789012345678')).toBe(true)
        expect(isValidIdCard('12345678901234567X')).toBe(true)
        expect(isValidIdCard('12345')).toBe(false)
      })
    })

    describe('isValidUrl', () => {
      it('应该验证URL格式', () => {
        expect(isValidUrl('https://example.com')).toBe(true)
        expect(isValidUrl('http://localhost:3000')).toBe(true)
        expect(isValidUrl('invalid-url')).toBe(false)
      })
    })
  })

  describe('浏览器工具', () => {
    describe('copyToClipboard', () => {
      beforeEach(() => {
        // Mock clipboard API
        Object.assign(navigator, {
          clipboard: {
            writeText: vi.fn().mockResolvedValue(undefined)
          }
        })
      })

      it('应该复制文本到剪贴板', async () => {
        const result = await copyToClipboard('test text')
        expect(result).toBe(true)
        expect(navigator.clipboard.writeText).toHaveBeenCalledWith('test text')
      })
    })

    describe('downloadFile', () => {
      it('应该创建下载链接', () => {
        const createElementSpy = vi.spyOn(document, 'createElement')
        
        const mockLink = {
          href: '',
          download: '',
          target: '',
          click: vi.fn()
        },
  createElementSpy.mockReturnValue(mockLink as any)
        
        downloadFile('https://example.com/file.pdf', 'test.pdf')
        
        expect(mockLink.href).toBe('https://example.com/file.pdf')
        expect(mockLink.download).toBe('test.pdf')
        expect(mockLink.click).toHaveBeenCalled()
      })
    })

    describe('getBrowserInfo', () => {
      it('应该检测浏览器信息', () => {
        // Mock user agent
        Object.defineProperty(navigator, 'userAgent', {
          value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          configurable: true
        })
        
        const info = getBrowserInfo()
        expect(info.name).toBe('Chrome')
        expect(info.os).toBe('Windows')
      })
    })
  })

  describe('业务工具', () => {
    describe('debounce', () => {
      it('应该防抖执行函数', async () => {
        const fn = vi.fn()
        const debouncedFn = debounce(fn, 100)
        
        debouncedFn()
        debouncedFn()
        debouncedFn()
        
        expect(fn).not.toHaveBeenCalled()
        
        await new Promise(resolve => setTimeout(resolve, 150))
        expect(fn).toHaveBeenCalledTimes(1)
      })
    })

    describe('throttle', () => {
      it('应该节流执行函数', async () => {
        const fn = vi.fn()
        const throttledFn = throttle(fn, 100)
        
        throttledFn()
        throttledFn()
        throttledFn()
        
        expect(fn).toHaveBeenCalledTimes(1)
        
        await new Promise(resolve => setTimeout(resolve, 150))
        throttledFn()
        expect(fn).toHaveBeenCalledTimes(2)
      })
    })

    describe('retry', () => {
      it('应该重试失败的函数', async () => {
        let attempts = 0
        const fn = vi.fn().mockImplementation(() => {
          attempts++
          if (attempts < 3) {
            throw new Error('Failed')
          },
  return 'Success'
        })
        
        const result = await retry(fn, 3, 10)
        expect(result).toBe('Success')
        expect(fn).toHaveBeenCalledTimes(3)
      })

      it('应该在达到最大重试次数后抛出错误', async () => {
        const fn = vi.fn().mockRejectedValue(new Error('Always fails'))
        
        await expect(retry(fn, 2, 10)).rejects.toThrow('Always fails')
        expect(fn).toHaveBeenCalledTimes(2)
      })
    })

    describe('generateUUID', () => {
      it('应该生成有效的UUID', () => {
        const uuid = generateUUID()
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
        
        expect(uuid).toMatch(uuidRegex)
      })

      it('应该生成唯一的UUID', () => {
        const uuid1 = generateUUID()
        const uuid2 = generateUUID()
        
        expect(uuid1).not.toBe(uuid2)
      })
    })
  })
})
