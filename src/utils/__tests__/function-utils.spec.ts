 
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * function-utils 单元测试
 * @description 自动生成的测试文件
 */

import { describe, it, expect, vi } from 'vitest'
import {
  debounce,
  throttle,
  retry,
  memoize,
  once,
  delay,
  withTimeout,
  promiseLimit,
  makeCancelable,
  pipe,
  compose
} from '../function-utils'
describe('debounce', () => {
  it('应该被正确导出', () => {
    expect(debounce).toBeDefined()
    expect(typeof debounce).toBe('function')
  })

  it('应该延迟执行', () => {
    vi.useFakeTimers()
    const callback = vi.fn()
    const delayed = debounce(callback, 1000)

    // 立即调用不应执行
    delayed()
    expect(callback).not.toHaveBeenCalled()

    // 时间推进后应执行
    vi.advanceTimersByTime(1000)
    expect(callback).toHaveBeenCalledTimes(1)

    vi.useRealTimers()
  })

  it('应该正确取消', () => {
    const callback = vi.fn()
    const delayed = debounce(callback, 1000)

    delayed()
    delayed.cancel?.()

    vi.advanceTimersByTime(1000)
    expect(callback).not.toHaveBeenCalled()
  })
})

describe('throttle', () => {
  it('应该被正确导出', () => {
    expect(throttle).toBeDefined()
    expect(typeof throttle).toBe('function')
  })

  it('应该延迟执行', () => {
    vi.useFakeTimers()
    const callback = vi.fn()
    const delayed = throttle(callback, 1000)

    // 立即调用不应执行
    delayed()
    expect(callback).not.toHaveBeenCalled()

    // 时间推进后应执行
    vi.advanceTimersByTime(1000)
    expect(callback).toHaveBeenCalledTimes(1)

    vi.useRealTimers()
  })

  it('应该正确取消', () => {
    const callback = vi.fn()
    const delayed = throttle(callback, 1000)

    delayed()
    delayed.cancel?.()

    vi.advanceTimersByTime(1000)
    expect(callback).not.toHaveBeenCalled()
  })
})

describe('retry', () => {
  it('应该被正确导出', () => {
    expect(retry).toBeDefined()
    expect(typeof retry).toBe('function')
  })
})

describe('memoize', () => {
  it('应该被正确导出', () => {
    expect(memoize).toBeDefined()
    expect(typeof memoize).toBe('function')
  })

  it('应该缓存结果', () => {
    const expensiveFn = vi.fn().mockReturnValue('result')
    const cached = memoize(expensiveFn)

    // 第一次调用
    expect(cached('arg')).toBe('result')
    expect(expensiveFn).toHaveBeenCalledTimes(1)

    // 第二次调用应使用缓存
    expect(cached('arg')).toBe('result')
    expect(expensiveFn).toHaveBeenCalledTimes(1)
  })

  it('应该支持缓存清理', () => {
    const cached = memoize(x => x)

    cached('test')
    cached.clear?.()

    // 清理后应重新计算
    const spy = vi.fn(x => x)
    const newCached = memoize(spy)
    newCached('test')
    expect(spy).toHaveBeenCalled()
  })
})

describe('once', () => {
  it('应该被正确导出', () => {
    expect(once).toBeDefined()
    expect(typeof once).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = once(undefined)
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => once(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => once({})).not.toThrow()
  })
})

describe('delay', () => {
  it('应该被正确导出', () => {
    expect(delay).toBeDefined()
    expect(typeof delay).toBe('function')
  })
})

describe('withTimeout', () => {
  it('应该被正确导出', () => {
    expect(withTimeout).toBeDefined()
    expect(typeof withTimeout).toBe('function')
  })

  it('应该成功返回数据', async () => {
    const result = await withTimeout(undefined, 123, undefined)
    expect(result).toBeDefined()
  })

  it('应该正确处理错误', async () => {
    // 模拟错误
    const error = new Error('Test error')
    // 这里需要根据具体实现mock相应的依赖

    await expect(withTimeout()).rejects.toThrow()
  })

  it('应该处理超时情况', async () => {
    // 超时测试
    const promise = withTimeout()
    const timeout = new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))

    await expect(Promise.race([promise, timeout])).resolves.toBeDefined()
  })
})

describe('promiseLimit', () => {
  it('应该被正确导出', () => {
    expect(promiseLimit).toBeDefined()
    expect(typeof promiseLimit).toBe('function')
  })

  it('应该成功返回数据', async () => {
    const result = await promiseLimit([], 123)
    expect(result).toBeDefined()
  })

  it('应该正确处理错误', async () => {
    // 模拟错误
    const error = new Error('Test error')
    // 这里需要根据具体实现mock相应的依赖

    await expect(promiseLimit()).rejects.toThrow()
  })

  it('应该处理超时情况', async () => {
    // 超时测试
    const promise = promiseLimit()
    const timeout = new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))

    await expect(Promise.race([promise, timeout])).resolves.toBeDefined()
  })
})

describe('makeCancelable', () => {
  it('应该被正确导出', () => {
    expect(makeCancelable).toBeDefined()
    expect(typeof makeCancelable).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = makeCancelable(undefined)
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => makeCancelable(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => makeCancelable({})).not.toThrow()
  })
})

describe('pipe', () => {
  it('应该被正确导出', () => {
    expect(pipe).toBeDefined()
    expect(typeof pipe).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = pipe([])
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => pipe(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => pipe({})).not.toThrow()
  })
})

describe('compose', () => {
  it('应该被正确导出', () => {
    expect(compose).toBeDefined()
    expect(typeof compose).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = compose([])
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    // 空值测试
    expect(() => compose(null)).not.toThrow()
  })

  it('应该处理错误类型的输入', () => {
    // 错误类型
    expect(() => compose({})).not.toThrow()
  })
})
