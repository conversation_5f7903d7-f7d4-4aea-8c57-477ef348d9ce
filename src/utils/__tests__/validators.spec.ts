/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * 表单验证器单元测试
 * @description 测试所有验证器的功能和边界情况
 */

import { describe, it, expect, vi } from 'vitest';
  import { 
  createValidator, 
  validators, 
  combineValidators, 
  conditionalValidator, 
  dynamicValidator,
  validateUtils,
  type ValidatorOptions,
  type ValidatorType
} from '../validators',
  import type { FormItemRule } from 'element-plus';
  describe('validators', () => {
  // 模拟回调函数
  const createMockCallback = () => {
    const callback = vi.fn()
    return {
      callback,
      expectError: (message?: string) => {
        expect(callback).toHaveBeenCalledWith(expect.any(Error))
        if (message) {
          const error = callback.mock.calls[0][0]
          expect(error.message).toBe(message)
        }
      },
      expectSuccess: () => {
        expect(callback).toHaveBeenCalledWith()
      }
    }
  },
  describe('createValidator', () => {
    it('应该创建必填验证器', async () => {
      const validator = createValidator('required')
      const {callback, expectError} =  createMockCallback()
      
      await validator.validator({}, '', callback)
      expectError('此项为必填项')
    })

    it('应该支持自定义错误消息', async ()  // TODO: 添加实际断言})
      const {callback, expectError} =  createMockCallback()
      
      await validator.validator({}, '', callback)
      expectError('请填写此项')
    })

    it('应该支持allowEmpty选项', async ()  // TODO: 添加实际断言})
      const {callback, expectSuccess} =  createMockCallback()
      
      await validator.validator({}, '', callback)
      expectSuccess()
    })

    it('应该支持trim选项', async ()  // TODO: 添加实际断言})
      const {callback: cb1, expectError: expectError1} =  createMockCallback()
      const {callback: cb2, expectSuccess}  // TODO: 添加实际断言})
      const {callback, expectError} =  createMockCallback()
      
      await validator.validator({}, 'test', callback)
      expectError('长度必须为5个字符')
    })

    it('应该支持数值范围验证', async ()  // TODO: 添加实际断言})
      const {callback: cb1, expectError: expectError1} =  createMockCallback()
      const {callback: cb2, expectSuccess}  // TODO: 添加实际断言})
      const {callback, expectError} =  createMockCallback()
      
      await validator.validator({}, '12.345', callback)
      expectError('小数位数不能超过2位')
    })

    it('应该支持自定义正则验证', async ()  // TODO: 添加实际断言})
      const {callback: cb1, expectError} =  createMockCallback()
      const {callback: cb2, expectSuccess}  // TODO: 添加实际断言})
      const {callback, expectSuccess} =  createMockCallback()
      
      await validator.validator({}, 'test', callback)
      expectSuccess()
      expect(asyncValidator).toHaveBeenCalledWith('test', expect.any(Object))
    })

    it('应该处理异步验证失败', async ()  // TODO: 添加实际断言})
      const {callback, expectError} =  createMockCallback()
      
      await validator.validator({}, 'test', callback)
      expectError('异步验证失败')
    })

    it('应该处理异步验证异常', async ()  // TODO: 添加实际断言})
      const {callback, expectError} = createMockCallback()
      
      await validator.validator({}, 'test', callback)
      expectError('验证出错')
    })
  })

  describe('预设验证器', () => {
    describe('email验证器', () => {
      const emailValidator = validators.email()
      
      it('应该验证有效的邮箱', async () => {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>'
        ]
        
        for (const email of validEmails) {
          const {callback, expectSuccess} = createMockCallback()
          await emailValidator.validator({}, email, callback)
          expectSuccess()
        }
      })

      it('应该拒绝无效的邮箱', async () => {
        const invalidEmails = [
          'test',
          'test@',
          '@example.com',
          'test@example',
          'test @example.com',
          'test@example .com'
        ]
        
        for (const email of invalidEmails) {
          const {callback, expectError} = createMockCallback()
          await emailValidator.validator({}, email, callback)
          expectError('请输入有效的邮箱地址')
        }
      })
    })

    describe('mobile验证器', () => {
      const mobileValidator = validators.mobile()
      
      it('应该验证有效的手机号', async () => {
        const validMobiles = ['13800138000', '15912345678', '18888888888']
        
        for (const mobile of validMobiles) {
          const {callback, expectSuccess} = createMockCallback()
          await mobileValidator.validator({}, mobile, callback)
          expectSuccess()
        }
      })

      it('应该拒绝无效的手机号', async () => {
        const invalidMobiles = ['12345678901', '1380013800', '23800138000']
        
        for (const mobile of invalidMobiles) {
          const {callback, expectError} = createMockCallback()
          await mobileValidator.validator({}, mobile, callback)
          expectError('请输入有效的手机号码')
        }
      })
    })

    describe('idCard验证器', () => {
      const idCardValidator = validators.idCard()
      
      it('应该验证有效的身份证号', async () => {
        const validIds = [
          '110101199001011234',  // 18位
          '11010119900101123X',  // 18位带X
          '110101900101123'      // 15位
        ]
        
        for (const id of validIds) {
          const {callback, expectSuccess} = createMockCallback()
          await idCardValidator.validator({}, id, callback)
          expectSuccess()
        }
      })

      it('应该拒绝无效的身份证号', async () => {
        const invalidIds = ['123456', '11010119900101123Y', '1101011990010112345']
        
        for (const id of invalidIds) {
          const {callback, expectError} = createMockCallback()
          await idCardValidator.validator({}, id, callback)
          expectError('请输入有效的身份证号')
        }
      })
    })

    describe('strongPassword验证器', () => {
      const strongPasswordValidator = validators.strongPassword()
      
      it('应该验证强密码', async () => {
        const validPasswords = [
          'Test@123',
          'P@ssw0rd',
          'Admin!234',
          'Complex$Pass1'
        ]
        
        for (const password of validPasswords) {
          const {callback, expectSuccess} = createMockCallback()
          await strongPasswordValidator.validator({}, password, callback)
          expectSuccess()
        }
      })

      it('应该拒绝弱密码', async () => {
        const weakPasswords = [
          'password',      // 没有大写字母、数字、特殊字符
          'Password',      // 没有数字、特殊字符
          'Password1',     // 没有特殊字符
          'Pass@word',     // 没有数字
          'pass@123',      // 没有大写字母
          'P@ss1'          // 长度不够
        ]
        
        for (const password of weakPasswords) {
          const {callback, expectError} = createMockCallback()
          await strongPasswordValidator.validator({}, password, callback)
          expectError('密码必须包含大小写字母、数字和特殊字符，且不少于8位')
        }
      })
    })

    describe('number验证器', () => {
      it('应该验证数字', async () => {
        const numberValidator = validators.number()
        const {callback: cb1, expectSuccess: expectSuccess1} = createMockCallback()
        const {callback: cb2, expectError} = createMockCallback()
        
        await numberValidator.validator({}, '12345', cb1)
        expectSuccess1()
        
        await numberValidator.validator({}, '123abc', cb2)
        expectError('只能输入数字')
      })
    })

    describe('integer验证器', () => {
      it('应该验证整数', async () => {
        const integerValidator = validators.integer()
        const {callback: cb1, expectSuccess: expectSuccess1} = createMockCallback()
        const {callback: cb2, expectSuccess: expectSuccess2} = createMockCallback()
        const {callback: cb3, expectError} = createMockCallback()
        
        await integerValidator.validator({}, '123', cb1)
        expectSuccess1()
        
        await integerValidator.validator({}, '-123', cb2)
        expectSuccess2()
        
        await integerValidator.validator({}, '123.45', cb3)
        expectError('只能输入整数')
      })
    })

    describe('chinese验证器', () => {
      it('应该验证中文字符', async () => {
        const chineseValidator = validators.chinese()
        const {callback: cb1, expectSuccess} = createMockCallback()
        const {callback: cb2, expectError} = createMockCallback()
        
        await chineseValidator.validator({}, '中文测试', cb1)
        expectSuccess()
        
        await chineseValidator.validator({}, '中文test', cb2)
        expectError('只能输入中文字符')
      })
    })

    describe('自定义验证器', () => {
      it('应该支持自定义正则', async () => {
        const customValidator = validators.custom(/^[A-Z]{3}\d{3}$/, '格式必须为3个大写字母加3个数字')
        const {callback: cb1, expectSuccess} = createMockCallback()
        const {callback: cb2, expectError} = createMockCallback()
        
        await customValidator.validator({}, 'ABC123', cb1)
        expectSuccess()
        
        await customValidator.validator({}, 'abc123', cb2)
        expectError('格式必须为3个大写字母加3个数字')
      })
    })

    describe('长度范围验证器', () => {
      it('应该验证长度范围', async () => {
        const lengthValidator = validators.length(5, 10)
        const {callback: cb1, expectError: expectError1} = createMockCallback()
        const {callback: cb2, expectSuccess} = createMockCallback()
        const {callback: cb3, expectError: expectError3} = createMockCallback()
        
        await lengthValidator.validator({}, 'test', cb1)
        expectError1('长度必须在5-10个字符之间')
        
        await lengthValidator.validator({}, 'hello123', cb2)
        expectSuccess()
        
        await lengthValidator.validator({}, 'this is too long', cb3)
        expectError3('长度必须在5-10个字符之间')
      })
    })

    describe('数值范围验证器', () => {
      it('应该验证数值范围', async () => {
        const rangeValidator = validators.range(10, 100)
        const {callback: cb1, expectError: expectError1} = createMockCallback()
        const {callback: cb2, expectSuccess} = createMockCallback()
        const {callback: cb3, expectError: expectError3} = createMockCallback()
        
        await rangeValidator.validator({}, '5', cb1)
        expectError1('值必须在10-100之间')
        
        await rangeValidator.validator({}, '50', cb2)
        expectSuccess()
        
        await rangeValidator.validator({}, '150', cb3)
        expectError3('值必须在10-100之间')
      })
    })

    describe('确认密码验证器', () => {
      it('应该验证密码匹配', async () => {
        const rule = {
          field: 'form.confirmPassword',
          parent: {
            form: {
              password: 'Test@123',
              confirmPassword: 'Test@123'
            }
          }
        },
  const confirmValidator = validators.confirmPassword('password')
        const {callback, expectSuccess} = createMockCallback()
        
        await confirmValidator.validator(rule, 'Test@123', callback)
        expectSuccess()
      })

      it('应该拒绝不匹配的密码', async () => {
        const rule = {
          field: 'form.confirmPassword',
          parent: {
            form: {
              password: 'Test@123',
              confirmPassword: 'Test@456'
            }
          }
        },
  const confirmValidator = validators.confirmPassword('password')
        const {callback, expectError} = createMockCallback()
        
        await confirmValidator.validator(rule, 'Test@456', callback)
        expectError('两次输入的密码不一致')
      })
    })

    describe('异步用户名验证器', () => {
      it('应该验证用户名是否存在', async () => {
        const checkApi = vi.fn().mockResolvedValue(false) // 不存在
        const checkUsernameValidator = validators.checkUsername(checkApi)
        const {callback, expectSuccess} = createMockCallback()
        
        await checkUsernameValidator.validator({}, 'newuser', callback)
        expectSuccess()
        expect(checkApi).toHaveBeenCalledWith('newuser')
      })

      it('应该拒绝已存在的用户名', async () => {
        const checkApi = vi.fn().mockResolvedValue(true) // 已存在
        const checkUsernameValidator = validators.checkUsername(checkApi)
        const {callback, expectError} = createMockCallback()
        
        await checkUsernameValidator.validator({}, 'existinguser', callback)
        expectError('用户名已存在')
      })
    })
  })

  describe('combineValidators', () => {
    it('应该组合多个验证器', () => {
      const rules = combineValidators(
        validators.required(),
        validators.email()
      )
      
      expect(rules).toHaveLength(2)
      expect(rules[0].validator).toBeDefined()
      expect(rules[1].validator).toBeDefined()
    })
  })

  describe('conditionalValidator', () => {
    it('应该在条件为true时执行验证', async () => {
      const condition = () => true
      const validator = conditionalValidator(
        condition,
        validators.email()
      )
      const {callback, expectError} = createMockCallback()
      
      await validator.validator({}, 'invalid-email', callback)
      expectError()
    })

    it('应该在条件为false时跳过验证', async () => {
      const condition = () => false
      const validator = conditionalValidator(
        condition,
        validators.email()
      )
      const {callback, expectSuccess} = createMockCallback()
      
      await validator.validator({}, 'invalid-email', callback)
      expectSuccess()
    })

    it('应该支持多个验证器', async () => {
      const condition = () => true
      const validator = conditionalValidator(
        condition,
        [validators.required(), validators.email()]
      )
      const {callback, expectError} = createMockCallback()
      
      await validator.validator({}, '', callback)
      expectError()
    })
  })

  describe('dynamicValidator', () => {
    it('应该动态获取验证器', async () => {
      let validatorType: ValidatorType = 'email',
  const validator = dynamicValidator(() => 
        validatorType === 'email' ? validators.email() : validators.mobile()
      )
      
      const {callback: cb1, expectError: expectError1} = createMockCallback()
      await validator.validator({}, '13800138000', cb1)
      expectError1() // 期望邮箱，但输入了手机号
      
      validatorType = 'mobile',
  const {callback: cb2, expectSuccess} = createMockCallback()
      await validator.validator({}, '13800138000', cb2)
      expectSuccess() // 现在期望手机号
    })
  })

  describe('validateUtils', () => {
    it('应该正确验证邮箱', () => {
      expect(validateUtils.isEmail('<EMAIL>')).toBe(true)
      expect(validateUtils.isEmail('invalid-email')).toBe(false)
    })

    it('应该正确验证手机号', () => {
      expect(validateUtils.isMobile('13800138000')).toBe(true)
      expect(validateUtils.isMobile('12345678901')).toBe(false)
    })

    it('应该正确验证身份证', () => {
      expect(validateUtils.isIdCard('110101199001011234')).toBe(true)
      expect(validateUtils.isIdCard('123456')).toBe(false)
    })

    it('应该正确验证URL', () => {
      expect(validateUtils.isUrl('https://example.com')).toBe(true)
      expect(validateUtils.isUrl('http://localhost:3000')).toBe(true)
      expect(validateUtils.isUrl('not-a-url')).toBe(false)
    })

    it('应该正确验证银行卡号', () => {
      expect(validateUtils.isBankCard('6222020200012345678')).toBe(true)
      expect(validateUtils.isBankCard('12345')).toBe(false)
    })

    it('应该正确验证中文', () => {
      expect(validateUtils.isChinese('中文')).toBe(true)
      expect(validateUtils.isChinese('Chinese')).toBe(false)
    })

    it('应该正确验证英文', () => {
      expect(validateUtils.isEnglish('English')).toBe(true)
      expect(validateUtils.isEnglish('English123')).toBe(false)
    })

    it('应该正确验证数字', () => {
      expect(validateUtils.isNumber('12345')).toBe(true)
      expect(validateUtils.isNumber('123.45')).toBe(false)
    })

    it('应该正确验证整数', () => {
      expect(validateUtils.isInteger('123')).toBe(true)
      expect(validateUtils.isInteger('-123')).toBe(true)
      expect(validateUtils.isInteger('123.45')).toBe(false)
    })

    it('应该正确验证浮点数', () => {
      expect(validateUtils.isFloat('123.45')).toBe(true)
      expect(validateUtils.isFloat('-123.45')).toBe(true)
      expect(validateUtils.isFloat('abc')).toBe(false)
    })

    it('应该正确验证字母数字', () => {
      expect(validateUtils.isAlphanumeric('test123')).toBe(true)
      expect(validateUtils.isAlphanumeric('test_123')).toBe(false)
    })

    it('应该正确验证强密码', () => {
      expect(validateUtils.isStrongPassword('Test@123')).toBe(true)
      expect(validateUtils.isStrongPassword('password')).toBe(false)
    })

    it('应该正确验证IP地址', () => {
      expect(validateUtils.isIp('***********')).toBe(true)
      expect(validateUtils.isIp('256.256.256.256')).toBe(false)
    })

    it('应该正确验证MAC地址', () => {
      expect(validateUtils.isMac('00:11:22:33:44:55')).toBe(true)
      expect(validateUtils.isMac('00-11-22-33-44-55')).toBe(true)
      expect(validateUtils.isMac('invalid-mac')).toBe(false)
    })
  })
})