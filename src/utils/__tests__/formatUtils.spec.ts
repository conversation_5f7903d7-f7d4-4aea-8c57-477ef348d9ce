/**
 * format.ts 单元测试
 */

import { describe, it, expect } from 'vitest'
import {
  formatDate,
  formatDateTime,
  formatMoney,
  formatPercent,
  formatFileSize,
  formatPhone,
  formatIdNumber,
  truncateText,
  calculateDuration,
  formatDuration,
  formatNumber,
  formatBytes
} from '../format'

describe('Format Utils', () => {
  describe('formatDate', () => {
    it('should format date correctly with default format', () => {
      expect(formatDate('2024-01-15')).toBe('2024-01-15')
      expect(formatDate(new Date('2024-01-15'))).toBe('2024-01-15')
    })
    
    it('should format date with custom format', () => {
      expect(formatDate('2024-01-15', 'YYYY/MM/DD')).toBe('2024/01/15')
      expect(formatDate('2024-01-15', 'DD-MM-YYYY')).toBe('15-01-2024')
    })
    
    it('should handle null/undefined values', () => {
      expect(formatDate(null)).toBe('-')
      expect(formatDate(undefined)).toBe('-')
      expect(formatDate('')).toBe('-')
    })
  })
  
  describe('formatDateTime', () => {
    it('should format datetime correctly with default format', () => {
      expect(formatDateTime('2024-01-15 10:30:45')).toBe('2024-01-15 10:30:45')
      expect(formatDateTime(new Date('2024-01-15T10:30:45'))).toMatch(/2024-01-15/)
    })
    
    it('should format datetime with custom format', () => {
      expect(formatDateTime('2024-01-15 10:30:45', 'YYYY/MM/DD HH:mm')).toBe('2024/01/15 10:30')
    })
    
    it('should handle null/undefined values', () => {
      expect(formatDateTime(null)).toBe('-')
      expect(formatDateTime(undefined)).toBe('-')
    })
  })
  
  describe('formatMoney', () => {
    it('should format money correctly', () => {
      expect(formatMoney(1234.56)).toBe('1,234.56')
      expect(formatMoney(1000000)).toBe('1,000,000.00')
      expect(formatMoney(0)).toBe('0.00')
    })
    
    it('should format money with custom decimals', () => {
      expect(formatMoney(1234.5678, 3)).toBe('1,234.568')
      expect(formatMoney(1234, 0)).toBe('1,234')
    })
    
    it('should handle string input', () => {
      expect(formatMoney('1234.56')).toBe('1,234.56')
      expect(formatMoney('invalid')).toBe('-')
    })
    
    it('should handle null/undefined values', () => {
      expect(formatMoney(null)).toBe('-')
      expect(formatMoney(undefined)).toBe('-')
      expect(formatMoney('')).toBe('-')
    })
  })
  
  describe('formatPercent', () => {
    it('should format percent correctly', () => {
      expect(formatPercent(0.1234)).toBe('12.34%')
      expect(formatPercent(0.5)).toBe('50.00%')
      expect(formatPercent(1)).toBe('100.00%')
      expect(formatPercent(0)).toBe('0.00%')
    })
    
    it('should format percent with custom decimals', () => {
      expect(formatPercent(0.12345, 1)).toBe('12.3%')
      expect(formatPercent(0.12345, 3)).toBe('12.345%')
      expect(formatPercent(0.12345, 0)).toBe('12%')
    })
    
    it('should handle string input', () => {
      expect(formatPercent('0.25')).toBe('25.00%')
      expect(formatPercent('invalid')).toBe('-')
    })
    
    it('should handle null/undefined values', () => {
      expect(formatPercent(null)).toBe('-')
      expect(formatPercent(undefined)).toBe('-')
      expect(formatPercent('')).toBe('-')
    })
  })
  
  describe('formatFileSize', () => {
    it('should format file size correctly', () => {
      expect(formatFileSize(0)).toBe('0 B')
      expect(formatFileSize(1023)).toBe('1023.00 B')
      expect(formatFileSize(1024)).toBe('1.00 KB')
      expect(formatFileSize(1048576)).toBe('1.00 MB')
      expect(formatFileSize(1073741824)).toBe('1.00 GB')
    })
    
    it('should handle large file sizes', () => {
      expect(formatFileSize(1099511627776)).toBe('1.00 TB')
    })
  })
  
  describe('formatPhone', () => {
    it('should format phone number correctly', () => {
      expect(formatPhone('13812345678')).toBe('138****5678')
      expect(formatPhone('18888888888')).toBe('188****8888')
    })
    
    it('should handle invalid phone numbers', () => {
      expect(formatPhone('1234567')).toBe('1234567')
      expect(formatPhone('123456789012')).toBe('123456789012')
    })
    
    it('should handle null/undefined values', () => {
      expect(formatPhone(null)).toBe('-')
      expect(formatPhone(undefined)).toBe('-')
      expect(formatPhone('')).toBe('-')
    })
  })
  
  describe('formatIdNumber', () => {
    it('should format ID number correctly', () => {
      expect(formatIdNumber('110101199001011234')).toBe('1101**********1234')
      expect(formatIdNumber('12345678')).toBe('12345678') // 长度刚好8位，不会被格式化
      expect(formatIdNumber('123456789')).toBe('1234*6789') // 保留前4位和后4位
    })
    
    it('should handle short ID numbers', () => {
      expect(formatIdNumber('1234567')).toBe('1234567')
      expect(formatIdNumber('123')).toBe('123')
    })
    
    it('should handle null/undefined values', () => {
      expect(formatIdNumber(null)).toBe('-')
      expect(formatIdNumber(undefined)).toBe('-')
      expect(formatIdNumber('')).toBe('-')
    })
  })
  
  describe('truncateText', () => {
    it('should truncate text correctly', () => {
      expect(truncateText('Hello World', 5)).toBe('He...')
      expect(truncateText('This is a long text', 10)).toBe('This is...')
    })
    
    it('should not truncate short text', () => {
      expect(truncateText('Hello', 10)).toBe('Hello')
      expect(truncateText('Test', 4)).toBe('Test')
    })
    
    it('should use custom suffix', () => {
      expect(truncateText('Hello World', 7, '…')).toBe('Hello …')
    })
    
    it('should handle null/undefined values', () => {
      expect(truncateText(null, 10)).toBe('')
      expect(truncateText(undefined, 10)).toBe('')
    })
  })
  
  describe('calculateDuration', () => {
    it('should calculate duration in hours', () => {
      const start = '2024-01-15 10:00:00'
      const end = '2024-01-15 15:30:00'
      expect(calculateDuration(start, end)).toBe(5)
    })
    
    it('should calculate duration in different units', () => {
      const start = '2024-01-15 10:00:00'
      const end = '2024-01-16 10:00:00'
      expect(calculateDuration(start, end, 'days')).toBe(1)
      expect(calculateDuration(start, end, 'hours')).toBe(24)
      expect(calculateDuration(start, end, 'minutes')).toBe(1440)
    })
    
    it('should handle null/undefined values', () => {
      expect(calculateDuration(null, '2024-01-15')).toBe(0)
      expect(calculateDuration('2024-01-15', null)).toBe(0)
      expect(calculateDuration(undefined, undefined)).toBe(0)
    })
  })
  
  describe('formatDuration', () => {
    it('should format duration correctly', () => {
      expect(formatDuration(0.5)).toBe('30分钟')
      expect(formatDuration(1.5)).toBe('1.5小时')
      expect(formatDuration(24)).toBe('1天')
      expect(formatDuration(25.5)).toBe('1天1.5小时')
      expect(formatDuration(48)).toBe('2天')
    })
    
    it('should handle invalid values', () => {
      expect(formatDuration(0)).toBe('-')
      expect(formatDuration(-1)).toBe('-')
    })
  })
  
  describe('formatNumber', () => {
    it('should format number correctly', () => {
      expect(formatNumber(1234)).toBe('1,234')
      expect(formatNumber(1234567)).toBe('1,234,567')
      expect(formatNumber(0)).toBe('0')
    })
    
    it('should format number with decimals', () => {
      expect(formatNumber(1234.567, 2)).toBe('1,234.57')
      expect(formatNumber(1234.5, 1)).toBe('1,234.5')
    })
    
    it('should handle string input', () => {
      expect(formatNumber('1234')).toBe('1,234')
      expect(formatNumber('invalid')).toBe('-')
    })
    
    it('should handle null/undefined values', () => {
      expect(formatNumber(null)).toBe('-')
      expect(formatNumber(undefined)).toBe('-')
      expect(formatNumber('')).toBe('-')
    })
  })
  
  describe('formatBytes', () => {
    it('should format bytes correctly', () => {
      expect(formatBytes(0)).toBe('0 Bytes')
      expect(formatBytes(1023)).toBe('1023 Bytes')
      expect(formatBytes(1024)).toBe('1 KB')
      expect(formatBytes(1048576)).toBe('1 MB')
      expect(formatBytes(1073741824)).toBe('1 GB')
    })
    
    it('should format bytes with custom decimals', () => {
      expect(formatBytes(1536, 1)).toBe('1.5 KB')
      expect(formatBytes(1536, 0)).toBe('2 KB')
      expect(formatBytes(1536, 3)).toBe('1.5 KB')
    })
    
    it('should handle null/undefined values', () => {
      expect(formatBytes(null)).toBe('0 Bytes')
      expect(formatBytes(undefined)).toBe('0 Bytes')
    })
  })
})