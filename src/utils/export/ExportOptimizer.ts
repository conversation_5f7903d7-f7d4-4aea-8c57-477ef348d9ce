/**
 * 导出优化工具类
 * 提供高性能的数据导出功能，支持大数据量、多格式导出
 */

import { ref } from 'vue'
import * as XLSX from 'xlsx'

// 导出格式
export type ExportFormat = 'excel' | 'csv' | 'pdf' | 'image' | 'json'

// 表格数据行类型
export interface TableRow {
  [key: string]: string | number | Date | boolean | null | undefined
}

// 自定义样式配置
export interface CustomStyle {
  cell?: {
    font?: { bold?: boolean; color?: string; size?: number }
    fill?: { fgColor?: { rgb: string } }
    border?: Record<string, unknown>
  }
  columnWidths?: Array<{ wch: number }>
  header?: {
    font?: { bold?: boolean; color?: string; size?: number }
    fill?: { fgColor?: { rgb: string } }
  }
}

// Worker消息类型
export interface WorkerMessage {
  type: string
  data: unknown
  options?: Record<string, unknown>
}

// Worker响应类型
export interface WorkerResponse {
  type: 'success' | 'error'
  result?: unknown
  error?: string
}

// CSV处理结果
export interface CSVProcessResult {
  csv: string
  rowCount: number
}

// 数据分块结果
export type DataChunks<T = TableRow> = T[][]

// 导出配置
export interface ExportOptions {
  format: ExportFormat
  filename?: string
  sheetName?: string
  orientation?: 'portrait' | 'landscape'
  pageSize?: 'A4' | 'A3' | 'Letter'
  quality?: number
  compress?: boolean
  chunkSize?: number
  headers?: string[]
  customStyle?: CustomStyle
  watermark?: string
  password?: string
}

// 导出进度
export interface ExportProgress {
  current: number
  total: number
  percentage: number
  status: 'idle' | 'processing' | 'success' | 'error'
  message?: string
}

// 导出任务
export interface ExportTask {
  id: string
  name: string
  format: ExportFormat
  size: number
  progress: ExportProgress
  startTime: Date
  endTime?: Date
  result?: Blob | string
  error?: Error
}

export class ExportOptimizer {
  private static instance: ExportOptimizer
  private tasks: Map<string, ExportTask> = new Map()
  private worker: Worker | null = null

  private constructor() {
    this.initWorker()
  }

  static getInstance(): ExportOptimizer {
    if (!ExportOptimizer.instance) {
      ExportOptimizer.instance = new ExportOptimizer()
    }
    return ExportOptimizer.instance
  }

  /**
   * 初始化Web Worker
   */
  private initWorker() {
    // 创建Web Worker用于大数据处理
    const workerCode = `
      self.addEventListener('message', async (event) => {
        const {type, data, options} = event.data
        
        try {
          let result
          
          switch (type) {
            case 'process-csv':
              result = await processCSV(data, options)
              break
            case 'chunk-data':
              result = await chunkData(data, options)
              break
            default:
              throw new Error('Unknown worker task type: ' + type)
          }
          
          self.postMessage({
            type: 'success',
            result
          })
        } catch (error) {
          self.postMessage({
            type: 'error',
            error: error.message
          })
        }
      })
      
      async function processCSV(data, options) {
        const { headers } = options
        const rows = []
        
        if (headers) {
          rows.push(headers.join(','))
        }
        
        data.forEach(row => {
          const values = headers 
            ? headers.map(h => escapeCsvValue(row[h]))
            : Object.values(row).map(v => escapeCsvValue(v))
          rows.push(values.join(','))
        })
        
        return rows.join('\\n')
      }
      
      async function chunkData(data, options) {
        const { chunkSize = 1000 } = options
        const chunks = []
        
        for (let i = 0; i < data.length; i += chunkSize) {
          chunks.push(data.slice(i, i + chunkSize))
        }
        
        return chunks
      }
      
      function escapeCsvValue(value) {
        if (value == null) return ''
        const str = String(value)
        if (str.includes(',') || str.includes('"') || str.includes('\\n')) {
          return '"' + str.replace(/"/g, '""') + '"'
        }
        return str
      }
    `

    const blob = new Blob([workerCode], { type: 'application/javascript' })
    const workerUrl = URL.createObjectURL(blob)
    this.worker = new Worker(workerUrl)
  }

  /**
   * 导出Excel
   */
  async exportExcel(data: TableRow[], options: Partial<ExportOptions> = {}): Promise<Blob> {
    const taskId = this.createTask('excel', options)

    try {
      const {
        filename: _filename = 'export',
        sheetName = 'Sheet1',
        headers,
        customStyle,
        chunkSize = 5000
      } = options

      // 创建工作簿和工作表
      const workbook = XLSX.utils.book_new()
      const worksheet: XLSX.WorkSheet = {}

      // 添加表头
      if (headers) {
        headers.forEach((header, index) => {
          const cell = XLSX.utils.encode_cell({ c: index, r: 0 })
          worksheet[cell] = {
            v: header,
            t: 's',
            s: customStyle?.header
          }
        })
      }

      // 分块处理数据
      const chunks = await this.chunkData(data, chunkSize)
      let currentRow = headers ? 1 : 0

      for (let i = 0; i < chunks.length; i++) {
        this.updateProgress(taskId, i + 1, chunks.length, '处理数据块')

        const chunk = chunks[i]
        for (let j = 0; j < chunk.length; j++) {
          const row = chunk[j]
          const rowData = headers ? headers.map(h => row[h]) : Object.values(row)

          rowData.forEach((value, colIndex) => {
            const cell = XLSX.utils.encode_cell({
              c: colIndex,
              r: currentRow + j
            })

            // 智能类型识别
            interface CellData {
              v: string | number | Date
              t: 'n' | 'd' | 's'
              s?: CustomStyle['cell']
            }

            const cellData: CellData = { v: '', t: 's' }

            if (typeof value === 'number') {
              cellData.t = 'n'
              cellData.v = value
            } else if (value instanceof Date) {
              cellData.t = 'd'
              cellData.v = value
            } else {
              cellData.t = 's'
              cellData.v = String(value ?? '')
            }

            if (customStyle?.cell) {
              cellData.s = customStyle.cell
            }

            worksheet[cell] = cellData
          })
        }

        currentRow += chunk.length
      }

      // 设置范围
      const range = XLSX.utils.encode_range({
        s: { c: 0, r: 0 },
        e: {
          c: (headers?.length || Object.keys(data[0] || {}).length) - 1,
          r: currentRow - 1
        }
      })
      worksheet['!ref'] = range

      // 设置列宽
      if (customStyle?.columnWidths) {
        worksheet['!cols'] = customStyle.columnWidths
      }

      // 添加到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

      // 生成文件
      const buffer = XLSX.write(workbook, {
        bookType: 'xlsx',
        type: 'array',
        compression: options.compress
      })

      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      this.completeTask(taskId, blob)
      return blob
    } catch (error) {
      this.failTask(taskId, error as Error)
      throw error
    }
  }

  /**
   * 导出CSV
   */
  async exportCSV(data: TableRow[], options: Partial<ExportOptions> = {}): Promise<Blob> {
    const taskId = this.createTask('csv', options)

    try {
      const { headers, chunkSize = 10000 } = options

      // 分块处理数据
      const chunks = await this.chunkData(data, chunkSize)
      const results: string[] = []

      for (let i = 0; i < chunks.length; i++) {
        this.updateProgress(taskId, i + 1, chunks.length, '处理CSV数据')

        const result = await this.processInWorker('process-csv', chunks[i], {
          headers: i === 0 ? headers : null
        })

        results.push(result as string)
      }

      const csv = results.join('\n')
      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8' })

      this.completeTask(taskId, blob)
      return blob
    } catch (error) {
      this.failTask(taskId, error as Error)
      throw error
    }
  }

  /**
   * 导出PDF
   */
  async exportPDF(
    element: HTMLElement | string,
    options: Partial<ExportOptions> = {}
  ): Promise<Blob> {
    const taskId = this.createTask('pdf', options)

    try {
      const {
        filename: _filename = 'export',
        orientation: _orientation = 'portrait',
        pageSize: _pageSize = 'A4',
        quality: _quality = 0.95,
        watermark
      } = options

      // 获取目标元素
      const targetElement =
        typeof element === 'string' ? (document.querySelector(element) as HTMLElement) : element

      if (!targetElement) {
        throw new Error('Export target element not found')
      }

      // 这里应该使用真实的PDF生成库（如jsPDF、puppeteer等）
      // 暂时返回一个简单的HTML内容作为PDF
      const content = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${_filename}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            ${watermark ? `.watermark { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-45deg); opacity: 0.1; font-size: 72px; }` : ''}
          </style>
        </head>
        <body>
          ${watermark ? `<div class="watermark">${watermark}</div>` : ''}
          ${targetElement.outerHTML}
        </body>
        </html>
      `

      const blob = new Blob([content], { type: 'text/html;charset=utf-8' })

      this.completeTask(taskId, blob)
      return blob
    } catch (error) {
      this.failTask(taskId, error as Error)
      throw error
    }
  }

  /**
   * 批量导出
   */
  async batchExport(
    tasks: Array<{
      data: TableRow[]
      format: ExportFormat
      options?: Partial<ExportOptions>
    }>
  ): Promise<Map<string, Blob>> {
    const results = new Map<string, Blob>()

    // 并行处理导出任务
    const promises = tasks.map(async (task, index) => {
      const { data, format, options = {} } = task
      const filename = options.filename || `export_${index + 1}`

      let blob: Blob

      switch (format) {
        case 'excel':
          blob = await this.exportExcel(data, options)
          break
        case 'csv':
          blob = await this.exportCSV(data, options)
          break
        case 'json':
          blob = await this.exportJSON(data, options)
          break
        default:
          throw new Error(`Unsupported format: ${format}`)
      }

      results.set(filename, blob)
    })

    await Promise.all(promises)
    return results
  }

  /**
   * 导出JSON
   */
  async exportJSON(data: TableRow[], options: Partial<ExportOptions> = {}): Promise<Blob> {
    const taskId = this.createTask('json', options)

    try {
      const { filename: _filename = 'export' } = options

      // 格式化JSON数据
      const jsonContent = JSON.stringify(data, null, 2)

      const blob = new Blob([jsonContent], {
        type: 'application/json;charset=utf-8'
      })

      this.completeTask(taskId, blob)
      return blob
    } catch (error) {
      this.failTask(taskId, error as Error)
      throw error
    }
  }

  /**
   * 下载文件
   */
  download(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')

    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }, 100)
  }

  /**
   * 工具方法
   */
  private async chunkData<T = TableRow>(data: T[], chunkSize: number): Promise<DataChunks<T>> {
    const result = await this.processInWorker('chunk-data', data, { chunkSize })
    return result as DataChunks<T>
  }

  private processInWorker(
    type: string,
    data: unknown,
    options: Record<string, unknown>
  ): Promise<unknown> {
    return new Promise((resolve, reject) => {
      if (!this.worker) {
        reject(new Error('Worker not initialized'))
        return
      }

      const handler = (event: MessageEvent<WorkerResponse>) => {
        if (event.data.type === 'success') {
          resolve(event.data.result)
        } else {
          reject(new Error(event.data.error))
        }

        this.worker!.removeEventListener('message', handler)
      }

      this.worker.addEventListener('message', handler)
      const message: WorkerMessage = { type, data, options }
      this.worker.postMessage(message)
    })
  }

  private createTask(format: ExportFormat, options: Partial<ExportOptions>): string {
    const id = `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const task: ExportTask = {
      id,
      name: options.filename || 'export',
      format,
      size: 0,
      progress: {
        current: 0,
        total: 0,
        percentage: 0,
        status: 'processing'
      },
      startTime: new Date()
    }

    this.tasks.set(id, task)
    return id
  }

  private updateProgress(taskId: string, current: number, total: number, message?: string): void {
    const task = this.tasks.get(taskId)
    if (task) {
      task.progress = {
        current,
        total,
        percentage: Math.round((current / total) * 100),
        status: 'processing',
        message
      }
    }
  }

  private completeTask(taskId: string, result: Blob): void {
    const task = this.tasks.get(taskId)
    if (task) {
      task.progress.status = 'success'
      task.progress.percentage = 100
      task.endTime = new Date()
      task.result = result
      task.size = result.size
    }
  }

  private failTask(taskId: string, error: Error): void {
    const task = this.tasks.get(taskId)
    if (task) {
      task.progress.status = 'error'
      task.endTime = new Date()
      task.error = error
    }
  }

  /**
   * 获取任务状态
   */
  getTask(taskId: string): ExportTask | undefined {
    return this.tasks.get(taskId)
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): ExportTask[] {
    return Array.from(this.tasks.values())
  }

  /**
   * 清理已完成任务
   */
  clearCompletedTasks(): void {
    for (const [id, task] of this.tasks) {
      if (task.progress.status === 'success' || task.progress.status === 'error') {
        this.tasks.delete(id)
      }
    }
  }
}

// 导出单例
export const exportOptimizer = ExportOptimizer.getInstance()

// 导出Vue组合式API
export function useExport() {
  const tasks = ref<ExportTask[]>([])
  const currentTask = ref<ExportTask | null>(null)

  const updateTasks = () => {
    tasks.value = exportOptimizer.getAllTasks()
  }

  const exportData = async (
    data: TableRow[],
    format: ExportFormat,
    options?: Partial<ExportOptions>
  ): Promise<void> => {
    try {
      let blob: Blob

      switch (format) {
        case 'excel':
          blob = await exportOptimizer.exportExcel(data, options)
          break
        case 'csv':
          blob = await exportOptimizer.exportCSV(data, options)
          break
        case 'json':
          blob = await exportOptimizer.exportJSON(data, options)
          break
        default:
          throw new Error(`Unsupported format: ${format}`)
      }

      const filename = `${options?.filename || 'export'}.${format}`
      exportOptimizer.download(blob, filename)
    } finally {
      updateTasks()
    }
  }

  return {
    tasks,
    currentTask,
    updateTasks,
    exportData,
    exportOptimizer
  }
}
