import { BaseExporter } from './core/BaseExporter'
import type {
  BaseExportOptions,
  BaseExportResult,
  ExportColumn,
  ExportFormat,
  ExportPermission,
  ExportTask,
  MaskingConfig
} from './core/interfaces'
import { saveAs } from 'file-saver'
import { useUserStore } from '@/stores/user'

// 扩展 window 对象类型
declare global {
  interface Window {
     
    request?: unknown
    auditApi?: {
       
      log: (data: unknown) => Promise<void>
    }
  }
}

/**
 * 统一的导出服务
 */
export class ExportService {
  private static instance: ExportService
  private exportTasks: Map<string, ExportTask> = new Map()
  private exporters: Map<string, BaseExporter> = new Map()

  /**
   * 获取单例实例
   */
  static getInstance(): ExportService {
    if (!ExportService.instance) {
      ExportService.instance = new ExportService()
    }
    return ExportService.instance
  }

  /**
   * 注册导出器
   */
  registerExporter(name: string, exporter: BaseExporter): void {
    this.exporters.set(name, exporter)
  }

  /**
   * 获取导出器
   */
  getExporter(name: string): BaseExporter | undefined {
    return this.exporters.get(name)
  }

  /**
   * 创建通用导出器
   */
  createGenericExporter<T>(config: {
    columns: ExportColumn[]
    filename?: string
    maskingConfig?: MaskingConfig
  }): BaseExporter<T> {
    return new (class extends BaseExporter<T> {
      protected getColumns(): ExportColumn[] {
        return config.columns
      }

      protected getDefaultFilenamePrefix(): string {
        return config.filename || 'export'
      }
    })({ maskingConfig: config.maskingConfig })
  }

  /**
   * 快速导出
   */
  async quickExport<T>(params: {
    data: T[]
    columns: ExportColumn[]
    format?: ExportFormat
    filename?: string
    maskingConfig?: MaskingConfig
    permission?: ExportPermission
  }): Promise<BaseExportResult> {
    const { data, columns, format = 'excel', filename, maskingConfig, permission } = params

    // 检查权限
    if (permission && !this.checkPermission(permission, format, data.length)) {
      return {
        success: false,
        filename: '',
        fileSize: 0,
        recordCount: 0,
        error: '没有导出权限或超出限制'
      }
    }

    // 创建导出器
    const exporter = this.createGenericExporter<T>({
      columns,
      filename,
      maskingConfig
    })

    // 执行导出
    const result = await exporter.export(data, {
      format,
      filename,
      includeHeader: true
    })

    // 记录审计日志
    if (permission?.auditLog && result.success) {
      await this.logExportAudit({
        format,
        recordCount: data.length,
        filename: result.filename,
        fileSize: result.fileSize
      })
    }

    return result
  }

  /**
   * 批量导出
   */
  async batchExport<T>(params: {
    datasets: Array<{
      name: string
      data: T[]
      columns: ExportColumn[]
    }>
    format?: ExportFormat
    filename?: string
    compress?: boolean
  }): Promise<BaseExportResult[]> {
    const { datasets, format = 'excel', filename, compress } = params
    const results: BaseExportResult[] = []

    for (const dataset of datasets) {
      const exporter = this.createGenericExporter<T>({
        columns: dataset.columns,
        filename: dataset.name
      })

      const result = await exporter.export(dataset.data, {
        format,
        filename: dataset.name,
        compress
      })

      results.push(result)
    }

    // 如果需要压缩，将所有文件打包
    if (compress && results.every(r => r.success)) {
      try {
        // 动态导入 JSZip
        const JSZip = (await import('jszip')).default
        const zip = new JSZip()

        // 添加所有成功的文件到压缩包
        results.forEach((result, index) => {
          if (result.success && result.blob) {
            const filename = result.filename || `file_${index + 1}.${configs[index].format}`
            zip.file(filename, result.blob)
          }
        })

        // 生成压缩文件
        const zipBlob = await zip.generateAsync({ type: 'blob' })
        const zipFilename = `batch_export_${new Date().toISOString().split('T')[0]}.zip`

        // 下载压缩文件
        saveAs(zipBlob, zipFilename)

        return [
          {
            success: true,
            filename: zipFilename,
            fileSize: zipBlob.size,
            blob: zipBlob
          }
        ]
      } catch (__error) {
        // 如果压缩失败，仍然返回原始结果
        return results
      }
    }

    return results
  }

  /**
   * 创建导出任务
   */
  async createExportTask<T>(params: {
    type: string
    data: T[]
    columns: ExportColumn[]
    options: BaseExportOptions
  }): Promise<string> {
    const taskId = this.generateTaskId()
    const task: ExportTask = {
      id: taskId,
      type: params.type,
      status: 'pending',
      createdAt: new Date(),
      progress: 0,
      options: params.options
    }

    this.exportTasks.set(taskId, task)

    // 异步执行导出
    this.executeExportTask(taskId, params).catch(() => {
      const task = this.exportTasks.get(taskId)
      if (task) {
        task.status = 'failed'
        task.error = error.message
      }
    })

    return taskId
  }

  /**
   * 获取导出任务状态
   */
  getTaskStatus(taskId: string): ExportTask | undefined {
    return this.exportTasks.get(taskId)
  }

  /**
   * 取消导出任务
   */
  cancelTask(taskId: string): boolean {
    const task = this.exportTasks.get(taskId)
    if (task && task.status === 'processing') {
      task.status = 'cancelled'
      return true
    }
    return false
  }

  /**
   * 清理已完成的任务
   */
  cleanupTasks(olderThan?: Date): void {
    const cutoffTime = olderThan || new Date(Date.now() - 24 * 60 * 60 * 1000) // 默认24小时

    for (const [taskId, task] of this.exportTasks.entries()) {
      if (
        (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') &&
        task.createdAt < cutoffTime
      ) {
        this.exportTasks.delete(taskId)
      }
    }
  }

  /**
   * 导出统计报表
   */
  async exportStatistics(params: {
    startDate: Date
    endDate: Date
    groupBy: 'day' | 'week' | 'month'
    metrics: string[]
    type?: string // 统计类型，如 'employee', 'salary', 'attendance' 等
  }): Promise<BaseExportResult> {
    try {
      // 获取统计数据
      const data = await this.fetchStatisticsData(params)

      // 构建统计列
      const columns = this.buildStatisticsColumns(params.metrics, params.groupBy)

      // 添加汇总行
      const dataWithSummary = this.addSummaryRow(data, params.metrics)

      // 导出选项
      const exportOptions: BaseExportOptions = {
        sheetName: '统计报表',
        includeHeader: true,
        dateFormat: 'YYYY-MM-DD',
        styles: {
          header: {
            font: { bold: true },
            fill: { color: '#F0F0F0' }
          },
          summary: {
            font: { bold: true },
            fill: { color: '#E0E0E0' }
          }
        }
      }

      return this.quickExport({
        data: dataWithSummary,
        columns,
        format: 'excel',
        filename: `统计报表_${params.type || 'general'}_${params.startDate.toISOString().split('T')[0]}_${params.endDate.toISOString().split('T')[0]}`,
        ...exportOptions
      })
    } catch (__error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  /**
   * 检查权限
   */
  private checkPermission(
    permission: ExportPermission,
    format: ExportFormat,
    recordCount: number
  ): boolean {
    if (!permission.canExport) return false

    if (permission.allowedFormats && !permission.allowedFormats.includes(format)) {
      return false
    }

    if (permission.maxRecords && recordCount > permission.maxRecords) {
      return false
    }

    return true
  }

  /**
   * 记录审计日志
   */
  private async logExportAudit(info: {
    format: ExportFormat
    recordCount: number
    filename: string
    fileSize: number
    userId?: string
    exportType?: string
    department?: string
  }): Promise<void> {
    try {
      // 获取当前用户信息
      const userStore = useUserStore?.()
      const userId = info.userId || userStore?.userInfo?.id || 'unknown'
      const department = info.department || userStore?.userInfo?.department || 'unknown'

      // 构建审计日志数据
      const auditLog = {
        userId,
        department,
        action: 'EXPORT_DATA',
        resourceType: info.exportType || 'DATA',
        resourceId: info.filename,
        details: {
          format: info.format,
          recordCount: info.recordCount,
          fileSize: info.fileSize,
          filename: info.filename,
          timestamp: new Date().toISOString(),
          ip: window.location.hostname,
          userAgent: navigator.userAgent
        }
      }

      // 发送审计日志到后端
      if (window.auditApi) {
        await window.auditApi.log(auditLog)
      }

      // 本地存储审计记录（用于离线情况）
      const localAuditKey = 'export_audit_logs'
      const existingLogs = JSON.parse(localStorage.getItem(localAuditKey) || '[]')
      existingLogs.push(auditLog)

      // 只保留最近100条记录
      if (existingLogs.length > 100) {
        existingLogs.splice(0, existingLogs.length - 100)
      }

      localStorage.setItem(localAuditKey, JSON.stringify(existingLogs))

      } catch (__error) {
      // 审计日志失败不应该影响导出操作
    }
  }

  /**
   * 执行导出任务
   */
  private async executeExportTask<T>(
    taskId: string,
    params: {
      type: string
      data: T[]
      columns: ExportColumn[]
      options: BaseExportOptions
    }
  ): Promise<void> {
    const task = this.exportTasks.get(taskId)
    if (!task) return

    task.status = 'processing'
    task.startedAt = new Date()

    try {
      const exporter = this.createGenericExporter<T>({
        columns: params.columns,
        filename: params.options.filename
      })

      // 设置进度回调
      const progressExporter = new (class extends BaseExporter<T> {
        protected getColumns(): ExportColumn[] {
          return params.columns
        }

        protected reportProgress(processed: number, total: number, status: string): void {
          const task = this.exportTasks.get(taskId)
          if (task) {
            task.progress = Math.round((processed / total) * 100)
          }
          super.reportProgress(processed, total, status)
        }
      })()

      const result = await progressExporter.export(params.data, params.options)

      task.status = 'completed'
      task.completedAt = new Date()
      task.result = result
      task.progress = 100
    } catch (__error) {
      task.status = 'failed'
      task.error = error instanceof Error ? error.message : '导出失败'
      task.completedAt = new Date()
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取统计数据
   */
  private async fetchStatisticsData(params: {
    startDate: Date
    endDate: Date
    groupBy: 'day' | 'week' | 'month'
    metrics: string[]
    type?: string
  }): Promise<unknown[]> {
    try {
      // 根据统计类型调用不同的API
      let apiEndpoint = '/api/statistics/general'
       
      const queryParams: unknown = {
        startDate: params.startDate.toISOString(),
        endDate: params.endDate.toISOString(),
        groupBy: params.groupBy,
        metrics: params.metrics.join(',')
      }

      switch (params.type) {
        case 'employee':
          apiEndpoint = '/api/statistics/employee'
          break
        case 'salary':
          apiEndpoint = '/api/statistics/salary'
          break
        case 'attendance':
          apiEndpoint = '/api/statistics/attendance'
          break
        case 'recruitment':
          apiEndpoint = '/api/statistics/recruitment'
          break
        case 'performance':
          apiEndpoint = '/api/statistics/performance'
          break
      }

      // 如果有 request 工具，使用它获取数据
      if (window.request) {
        const response = await window.request.get(apiEndpoint, { params: queryParams })
        return response.data || []
      }

      // 否则返回模拟数据
      return this.generateMockStatisticsData(params)
    } catch (__error) {
      // 失败时返回模拟数据
      return this.generateMockStatisticsData(params)
    }
  }

  /**
   * 生成模拟统计数据
   */
   
  private generateMockStatisticsData(params: unknown): unknown[] {
     
    const data: unknown[] = []
    const startDate = new Date(params.startDate)
    const endDate = new Date(params.endDate)

    const currentDate = new Date(startDate)
    while (currentDate <= endDate) {
       
      const dataPoint: unknown = {
        date: new Date(currentDate).toISOString().split('T')[0]
      }

      // 为每个指标生成随机数据
      params.metrics.forEach((metric: string) => {
        dataPoint[metric] = Math.floor(Math.random() * 100) + 50
      })

      data.push(dataPoint)

      // 根据分组类型递增日期
      switch (params.groupBy) {
        case 'day':
          currentDate.setDate(currentDate.getDate() + 1)
          break
        case 'week':
          currentDate.setDate(currentDate.getDate() + 7)
          break
        case 'month':
          currentDate.setMonth(currentDate.getMonth() + 1)
          break
      }
    }

    return data
  }

  /**
   * 构建统计列
   */
  private buildStatisticsColumns(metrics: string[], groupBy: string): ExportColumn[] {
    // 指标中文名称映射
    const metricLabels: Record<string, string> = {
      // 员工相关
      totalEmployees: '员工总数',
      newEmployees: '新入职人数',
      resignedEmployees: '离职人数',
      activeEmployees: '在职人数',
      turnoverRate: '离职率(%)',

      // 薪资相关
      totalSalary: '薪资总额',
      avgSalary: '平均薪资',
      salaryGrowth: '薪资增长率(%)',
      bonusTotal: '奖金总额',
      benefitsCost: '福利成本',

      // 考勤相关
      attendanceRate: '出勤率(%)',
      lateCount: '迟到次数',
      absentCount: '缺勤次数',
      overtimeHours: '加班时数',
      leaveHours: '请假时数',

      // 招聘相关
      positionsOpen: '在招职位数',
      applicationsReceived: '收到简历数',
      interviewsScheduled: '面试安排数',
      offersExtended: '发放offer数',
      hireRate: '录用率(%)',

      // 绩效相关
      avgPerformanceScore: '平均绩效分',
      excellentRate: '优秀率(%)',
      improvementRate: '待改进率(%)'
    }

    // 时间列
    const columns: ExportColumn[] = [
      {
        key: 'date',
        header: groupBy === 'day' ? '日期' : groupBy === 'week' ? '周' : '月份',
        dataType: 'date',
        width: 120,
        format: groupBy === 'month' ? 'YYYY-MM' : 'YYYY-MM-DD'
      }
    ]

    // 指标列
    metrics.forEach(metric => {
      columns.push({
        key: metric,
        header: metricLabels[metric] || metric,
        dataType: metric.includes('Rate') || metric.includes('率') ? 'percentage' : 'number',
        width: 100,
        format: metric.includes('Rate') || metric.includes('率') ? '0.00%' : '#,##0',
        align: 'right'
      })
    })

    return columns
  }

  /**
   * 添加汇总行
   */
   
  private addSummaryRow(data: unknown[], metrics: string[]): unknown[] {
    if (data.length === 0) return data

    // 计算汇总数据
     
    const summary: unknown = {
      date: '合计',
      isSummary: true
    }

    metrics.forEach(metric => {
      const values = data.map(row => row[metric]).filter(v => typeof v === 'number')
      if (values.length > 0) {
        // 根据指标类型决定汇总方式
        if (metric.includes('avg') || metric.includes('Rate') || metric.includes('率')) {
          // 平均值
          summary[metric] = values.reduce((a, b) => a + b, 0) / values.length
        } else {
          // 总和
          summary[metric] = values.reduce((a, b) => a + b, 0)
        }
      }
    })

    return [...data, summary]
  }
}

// 导出单例实例
export const exportService = ExportService.getInstance()
