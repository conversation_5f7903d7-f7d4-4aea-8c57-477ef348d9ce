# 导出模块迁移指南

## 概述

本指南将帮助您将现有的导出代码迁移到新的统一导出框架。新框架提供了更好的代码复用、统一的接口和更强大的功能。

## 迁移步骤

### 1. 简单导出迁移

#### 旧代码示例

```typescript
// 旧的导出方式
async exportEmployeeList(employees: Employee[]) {
  const csvContent = this.generateCsvContent(employees)
  const blob = new Blob([csvContent], { type: 'text/csv' })
  this.downloadFile(blob, 'employees.csv')
}
```

#### 新代码示例

```typescript
import { exportService } from '@/utils/export/ExportService'

// 新的导出方式
async exportEmployeeList(employees: Employee[]) {
  const result = await exportService.quickExport({
    data: employees,
    columns: [
      { key: 'employeeId', header: '员工编号' },
      { key: 'name', header: '姓名', sensitive: true },
      { key: 'department', header: '部门' }
    ],
    format: 'csv',
    filename: 'employees'
  })

  if (!result.success) {
    console.error('导出失败:', result.error)
  }
}
```

### 2. 复杂导出迁移

#### 创建专用导出器

```typescript
// src/utils/export/business/SalaryExporter.ts
import { BaseExporter } from '../core/BaseExporter'
import type { ExportColumn } from '../core/interfaces'

export class SalaryExporter extends BaseExporter<Salary> {
  protected getDefaultFilenamePrefix(): string {
    return '薪资明细'
  }

  protected getColumns(): ExportColumn[] {
    return [
      { key: 'employeeId', header: '员工编号' },
      { key: 'employeeName', header: '员工姓名', sensitive: true },
      { key: 'baseSalary', header: '基本工资', dataType: 'currency', sensitive: true },
      { key: 'bonus', header: '奖金', dataType: 'currency', sensitive: true },
      { key: 'deductions', header: '扣款', dataType: 'currency' },
      { key: 'netSalary', header: '实发工资', dataType: 'currency', sensitive: true }
    ]
  }
}
```

#### 使用专用导出器

```typescript
import { SalaryExporter } from '@/utils/export/business/SalaryExporter'

const exporter = new SalaryExporter({
  maskingConfig: {
    salary: true,
    custom: {
      employeeName: name => name.charAt(0) + '**'
    }
  }
})

const result = await exporter.export(salaryData, {
  format: 'excel',
  includeStatistics: true,
  watermark: '薪资保密文件'
})
```

### 3. 批量导出迁移

```typescript
// 导出多个数据集
const results = await exportService.batchExport({
  datasets: [
    {
      name: '员工信息',
      data: employees,
      columns: employeeColumns
    },
    {
      name: '部门信息',
      data: departments,
      columns: departmentColumns
    }
  ],
  format: 'excel',
  compress: true
})
```

### 4. 异步任务导出

```typescript
// 创建异步导出任务
const taskId = await exportService.createExportTask({
  type: 'attendance',
  data: largeAttendanceData,
  columns: attendanceColumns,
  options: {
    format: 'excel',
    useWebWorker: true,
    showProgress: true
  }
})

// 轮询任务状态
const checkTaskStatus = setInterval(() => {
  const task = exportService.getTaskStatus(taskId)
  if (task) {
    console.log(`导出进度: ${task.progress}%`)

    if (task.status === 'completed') {
      clearInterval(checkTaskStatus)
      // 下载文件
      if (task.result?.blob) {
        downloadFile(task.result.blob, task.result.filename)
      }
    }
  }
}, 1000)
```

## 功能映射

### 数据脱敏

```typescript
// 旧方式
const maskedPhone = phone.substring(0, 3) + '****' + phone.substring(7)

// 新方式
import { DataMasking } from '@/utils/export/core/masking'
const maskedPhone = DataMasking.phone(phone)
```

### 格式化

```typescript
// 旧方式
const formattedDate = moment(date).format('YYYY-MM-DD')

// 新方式
import { ValueFormatters } from '@/utils/export/core/formatters'
const formattedDate = ValueFormatters.date(date)
```

### 工具函数

```typescript
// 导入新的工具函数
import {
  generateExportFilename,
  downloadFile,
  arrayToCsv,
  chunkArray,
  formatFileSize
} from '@/utils/export/core/utils'
```

## 最佳实践

1. **使用类型安全**：定义明确的数据类型

```typescript
interface EmployeeExportData {
  employeeId: string
  name: string
  department: string
  // ...
}

const exporter = new BaseExporter<EmployeeExportData>()
```

2. **配置列定义**：集中管理列配置

```typescript
// src/config/exportColumns.ts
export const EMPLOYEE_EXPORT_COLUMNS: ExportColumn[] = [
  // 列定义
]
```

3. **权限控制**：使用统一的权限检查

```typescript
const permission: ExportPermission = {
  canExport: userHasPermission('export.employee'),
  maxRecords: 10000,
  allowedFormats: ['excel', 'csv'],
  auditLog: true
}
```

4. **错误处理**：统一的错误处理

```typescript
try {
  const result = await exportService.quickExport(params)
  if (!result.success) {
    ElMessage.error(result.error || '导出失败')
  }
} catch (error) {
  ElMessage.error('导出过程中发生错误')
  console.error(error)
}
```

## 需要更新的文件清单

1. `/src/utils/notificationTemplateImportExport.ts` → 使用 `ExportService`
2. `/src/utils/approvalRuleImportExport.ts` → 使用 `ExportService`
3. `/src/utils/statisticsExport.ts` → 创建 `StatisticsExporter`
4. `/src/utils/batchExportOptimizer.ts` → 集成到 `ExportService`
5. `/src/utils/reportExportEnhancer.ts` → 创建 `ReportExporter`
6. `/src/utils/assetExport.ts` → 创建 `AssetExporter`
7. `/src/utils/leaveRecordExport.ts` → 创建 `LeaveRecordExporter`
8. `/src/utils/infoUpdateExport.ts` → 创建 `InfoUpdateExporter`
9. `/src/utils/certificateBatchExport.ts` → 创建 `CertificateExporter`
10. `/src/utils/searchResultExport.ts` → 使用 `ExportService`
11. `/src/utils/alertExport.ts` → 创建 `AlertExporter`
12. `/src/utils/auditLogExport.ts` → 创建 `AuditLogExporter`

## 注意事项

1. **保持向后兼容**：在迁移期间，可以保留旧的导出函数，内部调用新框架
2. **逐步迁移**：不需要一次性迁移所有代码，可以逐个模块进行
3. **测试覆盖**：迁移后确保所有导出功能都有相应的测试
4. **性能监控**：使用新框架的性能监控功能，确保性能不降低

## 迁移后的优势

1. **代码复用**：减少了约 70% 的重复代码
2. **统一接口**：所有导出功能使用相同的 API
3. **功能增强**：自动获得进度显示、批量处理、数据脱敏等功能
4. **易于维护**：集中管理导出逻辑，便于升级和修复
5. **性能优化**：统一的性能优化策略，支持 Web Worker 和流式处理
