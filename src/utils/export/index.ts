/**
 * 导出模块统一入口
 */

// 核心功能
export { BaseExporter } from './core/BaseExporter'
export { DataMasking } from './core/masking'
export { ValueFormatters } from './core/formatters'
export * from './core/utils'
export * from './core/interfaces'

// 导出服务
export { ExportService, exportService } from './ExportService'

// 业务导出器
export { EmployeeExporter } from './business/EmployeeExporter'

// 快捷导出函数
import { exportService } from './ExportService'
import type { ExportColumn, ExportFormat, MaskingConfig } from './core/interfaces'

/**
 * 快速导出Excel
 */
export async function exportToExcel<T = unknown>(
  data: T[],
  columns: ExportColumn[],
  filename?: string,
  maskingConfig?: MaskingConfig
) {
  return exportService.quickExport({
    data,
    columns,
    format: 'excel',
    filename,
    maskingConfig
  })
}

/**
 * 快速导出CSV
 */
export async function exportToCsv<T = unknown>(
  data: T[],
  columns: ExportColumn[],
  filename?: string,
  maskingConfig?: MaskingConfig
) {
  return exportService.quickExport({
    data,
    columns,
    format: 'csv',
    filename,
    maskingConfig
  })
}

/**
 * 快速导出PDF
 */
export async function exportToPdf<T = unknown>(
  data: T[],
  columns: ExportColumn[],
  filename?: string,
  maskingConfig?: MaskingConfig
) {
  return exportService.quickExport({
    data,
    columns,
    format: 'pdf',
    filename,
    maskingConfig
  })
}

/**
 * 导出JSON
 */
export async function exportToJson<T = unknown>(data: T[], filename?: string) {
  return exportService.quickExport({
    data,
    columns: [],
    format: 'json',
    filename
  })
}

/**
 * 通用导出函数
 */
export async function exportData<T = unknown>(params: {
  data: T[]
  columns: ExportColumn[]
  format?: ExportFormat
  filename?: string
  maskingConfig?: MaskingConfig
   
  options?: unknown
}) {
  const { format = 'excel', ...rest } = params
  return exportService.quickExport({
    ...rest,
    format
  })
}

// 默认列配置
export const DEFAULT_EMPLOYEE_COLUMNS: ExportColumn[] = [
  { key: 'employeeId', header: '员工编号' },
  { key: 'name', header: '姓名', sensitive: true },
  { key: 'department', header: '部门' },
  { key: 'position', header: '职位' },
  { key: 'hireDate', header: '入职日期', dataType: 'date' }
]

export const DEFAULT_SALARY_COLUMNS: ExportColumn[] = [
  { key: 'employeeId', header: '员工编号' },
  { key: 'employeeName', header: '员工姓名', sensitive: true },
  { key: 'baseSalary', header: '基本工资', dataType: 'currency', sensitive: true },
  { key: 'bonus', header: '奖金', dataType: 'currency', sensitive: true },
  { key: 'netSalary', header: '实发工资', dataType: 'currency', sensitive: true }
]

export const DEFAULT_ATTENDANCE_COLUMNS: ExportColumn[] = [
  { key: 'employeeId', header: '员工编号' },
  { key: 'employeeName', header: '员工姓名' },
  { key: 'date', header: '日期', dataType: 'date' },
  { key: 'checkInTime', header: '签到时间' },
  { key: 'checkOutTime', header: '签退时间' },
  { key: 'status', header: '状态' }
]

// 导出配置
export const EXPORT_CONFIG = {
  // 默认批次大小
  DEFAULT_BATCH_SIZE: 1000,
  // 最大导出记录数
  MAX_EXPORT_RECORDS: 100000,
  // 默认文件编码
  DEFAULT_ENCODING: 'utf-8',
  // 支持的格式
  SUPPORTED_FORMATS: ['excel', 'csv', 'pdf', 'json', 'word', 'xml'] as ExportFormat[],
  // 敏感字段列表
  SENSITIVE_FIELDS: [
    'idCard',
    'phone',
    'email',
    'bankAccount',
    'salary',
    'address',
    'emergencyPhone',
    'socialSecurity',
    'passport'
  ]
}
