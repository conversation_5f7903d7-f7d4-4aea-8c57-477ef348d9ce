import { BaseExporter } from '../core/BaseExporter'
import type { ExportColumn } from '../core/interfaces'
import { ValueFormatters } from '../core/formatters'

/**
 * 员工信息导出器
 */
// Employee data interface
interface EmployeeData {
  employeeId?: string
  name?: string
  gender?: string
  department?: string
  position?: string
  phone?: string
  email?: string
  idCard?: string
  hireDate?: string | Date
  education?: string
  status?: string
  [key: string]: unknown
}

export class EmployeeExporter extends BaseExporter<EmployeeData> {
  /**
   * 获取默认文件名前缀
   */
  protected getDefaultFilenamePrefix(): string {
    return '员工信息'
  }

  /**
   * 获取导出列配置
   */
  protected getColumns(): ExportColumn[] {
    return [
      {
        key: 'employeeId',
        header: '员工编号',
        width: 120,
        required: true
      },
      {
        key: 'name',
        header: '姓名',
        width: 100,
        required: true,
        sensitive: true
      },
      {
        key: 'gender',
        header: '性别',
        width: 60,
        formatter: _value => ValueFormatters.gender(value)
      },
      {
        key: 'idCard',
        header: '身份证号',
        width: 180,
        sensitive: true
      },
      {
        key: 'phone',
        header: '手机号',
        width: 120,
        sensitive: true
      },
      {
        key: 'email',
        header: '邮箱',
        width: 200,
        sensitive: true
      },
      {
        key: 'department.name',
        header: '部门',
        width: 150
      },
      {
        key: 'position.name',
        header: '职位',
        width: 120
      },
      {
        key: 'hireDate',
        header: '入职日期',
        width: 100,
        dataType: 'date',
        formatter: _value => ValueFormatters.date(value)
      },
      {
        key: 'employmentStatus',
        header: '在职状态',
        width: 80,
        formatter: _value => ValueFormatters.employeeStatus(value)
      },
      {
        key: 'education',
        header: '学历',
        width: 80,
        formatter: _value => ValueFormatters.education(value)
      },
      {
        key: 'maritalStatus',
        header: '婚姻状况',
        width: 80,
        formatter: _value => ValueFormatters.maritalStatus(value)
      },
      {
        key: 'politicalStatus',
        header: '政治面貌',
        width: 100,
        formatter: _value => ValueFormatters.politicalStatus(value)
      },
      {
        key: 'contractType',
        header: '合同类型',
        width: 100,
        formatter: _value => ValueFormatters.contractType(value)
      },
      {
        key: 'contractStartDate',
        header: '合同开始日期',
        width: 100,
        dataType: 'date'
      },
      {
        key: 'contractEndDate',
        header: '合同结束日期',
        width: 100,
        dataType: 'date'
      },
      {
        key: 'bankAccount',
        header: '银行账号',
        width: 180,
        sensitive: true
      },
      {
        key: 'salary',
        header: '基本工资',
        width: 100,
        dataType: 'currency',
        sensitive: true,
        align: 'right'
      },
      {
        key: 'address',
        header: '家庭住址',
        width: 300,
        sensitive: true
      },
      {
        key: 'emergencyContact',
        header: '紧急联系人',
        width: 100
      },
      {
        key: 'emergencyPhone',
        header: '紧急联系电话',
        width: 120,
        sensitive: true
      },
      {
        key: 'remark',
        header: '备注',
        width: 200
      }
    ]
  }

  /**
   * 准备导出数据（添加额外处理）
   */

  protected prepareData(records: unknown[], options: unknown): unknown[] {
    // 先调用父类的准备方法
    const preparedData = super.prepareData(records, options)

    // 如果需要包含统计信息
    if (options.includeStatistics) {
      preparedData.push(this.generateStatisticsRow(records))
    }

    return preparedData
  }

  /**
   * 生成统计行
   */

  private generateStatisticsRow(records: unknown[]): unknown {
    const totalCount = records.length
    const activeCount = records.filter(r => r.employmentStatus === 'active').length
    const maleCount = records.filter(r => r.gender === '1' || r.gender === 'male').length
    const femaleCount = records.filter(r => r.gender === '0' || r.gender === 'female').length

    const avgSalary = records.reduce((sum, r) => sum + (r.salary || 0), 0) / totalCount

    return {
      employeeId: '统计',
      name: `总计: ${totalCount}人`,
      gender: `男: ${maleCount}, 女: ${femaleCount}`,
      employmentStatus: `在职: ${activeCount}人`,
      salary: `平均: ${avgSalary.toFixed(2)}`
    }
  }

  /**
   * 生成Excel（使用更丰富的格式）
   */

  protected async generateExcel(data: unknown[], options: unknown): Promise<Blob> {
    // 这里应该使用实际的Excel库实现
    // 临时使用父类实现
    return super.generateExcel(data, options)
  }
}
