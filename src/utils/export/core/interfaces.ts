/**
 * 导出格式类型
 */
export type ExportFormat = 'excel' | 'csv' | 'pdf' | 'json' | 'word' | 'xml'

/**
 * 基础导出配置选项
 */
export interface BaseExportOptions {
  /** 导出格式 */
  format: ExportFormat
  /** 文件名（不含扩展名） */
  filename?: string
  /** 是否包含表头 */
  includeHeader?: boolean
  /** 文件编码 */
  encoding?: string
  /** 是否压缩 */
  compress?: boolean
  /** 水印文字 */
  watermark?: string
  /** 是否使用Web Worker处理 */
  useWebWorker?: boolean
  /** 批处理大小 */
  batchSize?: number
  /** 是否显示进度 */
  showProgress?: boolean
  /** 自定义样式 */

  customStyles?: unknown
  /** 元数据 */
  metadata?: Record<string, unknown>
}

/**
 * 导出结果
 */
export interface BaseExportResult {
  /** 是否成功 */
  success: boolean
  /** 导出的文件Blob */
  blob?: Blob
  /** 文件名（含扩展名） */
  filename: string
  /** 文件大小（字节） */
  fileSize: number
  /** 记录条数 */
  recordCount: number
  /** 错误信息 */
  error?: string
  /** 导出耗时（毫秒） */
  duration?: number
  /** 警告信息 */
  warnings?: string[]
}

/**
 * 导出列配置
 */
export interface ExportColumn {
  /** 数据字段名 */
  key: string
  /** 列标题 */
  header: string
  /** 列宽度 */
  width?: number
  /** 值格式化函数 */

  formatter?: (value: unknown, record?: unknown) => string
  /** 是否敏感数据 */
  sensitive?: boolean
  /** 数据类型 */
  dataType?: 'string' | 'number' | 'date' | 'boolean' | 'currency'
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 是否必填 */
  required?: boolean
  /** 默认值 */

  defaultValue?: unknown
  /** 样式配置 */

  style?: unknown
}

/**
 * CSV导出选项
 */
export interface CsvOptions {
  /** 分隔符 */
  delimiter?: string
  /** 换行符 */
  lineTerminator?: string
  /** 引号字符 */
  quoteChar?: string
  /** 是否引用所有字段 */
  quoteAll?: boolean
  /** 是否添加BOM */
  addBOM?: boolean
}

/**
 * Excel导出选项
 */
export interface ExcelOptions extends BaseExportOptions {
  /** 工作表名称 */
  sheetName?: string
  /** 是否自动调整列宽 */
  autoWidth?: boolean
  /** 是否冻结表头 */
  freezeHeader?: boolean
  /** 是否添加筛选器 */
  addFilter?: boolean
  /** 是否添加边框 */
  addBorder?: boolean
  /** 标题行样式 */

  headerStyle?: unknown
  /** 数据行样式 */

  dataStyle?: unknown
  /** 是否包含统计信息 */
  includeStatistics?: boolean
}

/**
 * PDF导出选项
 */
export interface PdfOptions extends BaseExportOptions {
  /** 页面方向 */
  orientation?: 'portrait' | 'landscape'
  /** 页面大小 */
  pageSize?: 'A4' | 'A3' | 'Letter' | 'Legal'
  /** 页边距 */
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
  /** 页眉内容 */
  header?: string | (() => string)
  /** 页脚内容 */
  footer?: string | (() => string)
  /** 是否显示页码 */
  showPageNumbers?: boolean
  /** 字体配置 */
  fontFamily?: string
  /** 字号 */
  fontSize?: number
}

/**
 * 导出进度回调
 */
export interface ExportProgressCallback {
  (progress: {
    /** 当前进度（0-100） */
    percent: number
    /** 已处理记录数 */
    processed: number
    /** 总记录数 */
    total: number
    /** 当前状态描述 */
    status: string
  }): void
}

/**
 * 数据脱敏配置
 */
export interface MaskingConfig {
  /** 身份证脱敏 */
  idCard?: boolean
  /** 手机号脱敏 */
  phone?: boolean
  /** 邮箱脱敏 */
  email?: boolean
  /** 银行账号脱敏 */
  bankAccount?: boolean
  /** 薪资脱敏 */
  salary?: boolean
  /** 自定义脱敏规则 */

  custom?: Record<string, (value: unknown) => string>
}

/**
 * 导出权限配置
 */
export interface ExportPermission {
  /** 是否允许导出 */
  canExport: boolean
  /** 最大导出记录数 */
  maxRecords?: number
  /** 允许的格式 */
  allowedFormats?: ExportFormat[]
  /** 是否需要审批 */
  requireApproval?: boolean
  /** 是否记录审计日志 */
  auditLog?: boolean
  /** 敏感字段控制 */
  sensitiveFields?: string[]
}

/**
 * 导出任务信息
 */
export interface ExportTask {
  /** 任务ID */
  id: string
  /** 任务类型 */
  type: string
  /** 任务状态 */
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  /** 创建时间 */
  createdAt: Date
  /** 开始时间 */
  startedAt?: Date
  /** 完成时间 */
  completedAt?: Date
  /** 进度 */
  progress: number
  /** 导出选项 */
  options: BaseExportOptions
  /** 结果 */
  result?: BaseExportResult
  /** 错误信息 */
  error?: string
}

/**
 * 导出器接口
 */
export interface IExporter<T = unknown> {
  /** 导出数据 */
  export(data: T[], options: BaseExportOptions): Promise<BaseExportResult>
  /** 验证数据 */
  validate?(data: T[]): Promise<{ valid: boolean; errors?: string[] }>
  /** 获取支持的格式 */
  getSupportedFormats(): ExportFormat[]
  /** 获取默认选项 */
  getDefaultOptions(): Partial<BaseExportOptions>
}

/**
 * 批量导出配置
 */
export interface BatchExportConfig {
  /** 批次大小 */
  batchSize: number
  /** 并发数 */
  concurrency: number
  /** 重试次数 */
  retryCount: number
  /** 重试延迟（毫秒） */
  retryDelay: number
  /** 超时时间（毫秒） */
  timeout: number
  /** 是否合并结果 */
  mergeResults: boolean
}

/**
 * 导出统计信息
 */
export interface ExportStatistics {
  /** 总记录数 */
  totalRecords: number
  /** 成功记录数 */
  successRecords: number
  /** 失败记录数 */
  failedRecords: number
  /** 跳过记录数 */
  skippedRecords: number
  /** 导出时间 */
  exportTime: Date
  /** 文件大小 */
  fileSize: number
  /** 用户信息 */
  exportedBy: string
  /** 导出格式 */
  format: ExportFormat
  /** 附加信息 */
  additionalInfo?: Record<string, unknown>
}
