import type {
  BaseExportOptions,
  BaseExportResult,
  ExportColumn,
  ExportFormat,
  ExportProgressCallback,
  MaskingConfig,
  IExporter
} from './interfaces'
import { DataMasking } from './masking'
import { ValueFormatters } from './formatters'
import {
  generateExportFilename,
  getFileExtension,
  getMimeType,
  formatFileSize,
  chunkArray,
  downloadFile
} from './utils'

/**
 * 导出器基类
 */
export abstract class BaseExporter<T = unknown> implements IExporter<T> {
  /**
   * 默认配置
   */
  protected defaultOptions: Partial<BaseExportOptions> = {
    includeHeader: true,
    encoding: 'utf-8',
    compress: false,
    useWebWorker: false,
    batchSize: 1000,
    showProgress: false
  }

  /**
   * 支持的格式
   */
  protected supportedFormats: ExportFormat[] = ['excel', 'csv', 'json']

  /**
   * 数据脱敏配置
   */
  protected maskingConfig?: MaskingConfig

  /**
   * 进度回调
   */
  protected progressCallback?: ExportProgressCallback

  /**
   * 构造函数
   */
  constructor(options?: {
    maskingConfig?: MaskingConfig
    progressCallback?: ExportProgressCallback
  }) {
    if (options) {
      this.maskingConfig = options.maskingConfig
      this.progressCallback = options.progressCallback
    }
  }

  /**
   * 获取导出列配置（子类必须实现）
   */
  protected abstract getColumns(): ExportColumn[]

  /**
   * 准备导出数据（子类可重写）
   */
  protected prepareData(records: T[], options: BaseExportOptions): Record<string, unknown>[] {
    const columns = this.getColumns()

    const preparedData: Record<string, unknown>[] = []

    // 报告进度
    this.reportProgress(0, records.length, '正在准备数据...')

    for (let i = 0; i < records.length; i++) {
      const record = records[i]

      const row: Record<string, unknown> = {}

      for (const column of columns) {
        let value = this.getColumnValue(record, column)

        // 应用格式化
        if (column.formatter) {
          value = column.formatter(value, record)
        } else {
          value = this.formatValue(value, column)
        }

        // 应用脱敏
        if (column.sensitive && this.maskingConfig) {
          value = this.maskValue(value, column.key)
        }

        ;(row as Record<string, unknown>)[column.key] = value
      }

      preparedData.push(row)

      // 定期报告进度
      if (i % 100 === 0) {
        this.reportProgress(i, records.length, '正在准备数据...')
      }
    }

    this.reportProgress(records.length, records.length, '数据准备完成')
    return preparedData
  }

  /**
   * 导出数据
   */
  async export(records: T[], options: BaseExportOptions): Promise<BaseExportResult> {
    const startTime = Date.now()

    try {
      // 合并选项
      const mergedOptions = { ...this.defaultOptions, ...options }

      // 验证格式
      if (!this.supportedFormats.includes(mergedOptions.format)) {
        throw new Error(`不支持的导出格式: ${mergedOptions.format}`)
      }

      // 验证数据
      const validation = await this.validate(records)
      if (!validation.valid) {
        throw new Error(`数据验证失败: ${validation.errors?.join(', ')}`)
      }

      // 准备数据
      const preparedData = this.prepareData(records, mergedOptions)

      // 根据格式生成文件
      let blob: Blob
      switch (mergedOptions.format) {
        case 'excel':
          blob = await this.generateExcel(preparedData, mergedOptions)
          break
        case 'csv':
          blob = await this.generateCsv(preparedData, mergedOptions)
          break
        case 'pdf':
          blob = await this.generatePdf(preparedData, mergedOptions)
          break
        case 'json':
          blob = await this.generateJson(preparedData, mergedOptions)
          break
        case 'word':
          blob = await this.generateWord(preparedData, mergedOptions)
          break
        case 'xml':
          blob = await this.generateXml(preparedData, mergedOptions)
          break
        default:
          throw new Error(`未实现的导出格式: ${mergedOptions.format}`)
      }

      // 生成文件名
      const extension = getFileExtension(mergedOptions.format)
      const filename = mergedOptions.filename
        ? `${mergedOptions.filename}.${extension}`
        : generateExportFilename(this.getDefaultFilenamePrefix(), extension)

      // 计算结果
      const result: BaseExportResult = {
        success: true,
        blob,
        filename,
        fileSize: blob.size,
        recordCount: records.length,
        duration: Date.now() - startTime
      }

      // 自动下载
      if (mergedOptions.showProgress) {
        downloadFile(blob, filename)
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '导出失败'
      return {
        success: false,
        filename: '',
        fileSize: 0,
        recordCount: 0,
        error: errorMessage,
        duration: Date.now() - startTime
      }
    }
  }

  /**
   * 验证数据
   */
  async validate(data: T[]): Promise<{ valid: boolean; errors?: string[] }> {
    const errors: string[] = []

    if (!data || !Array.isArray(data)) {
      errors.push('数据必须是数组')
    } else if (data.length === 0) {
      errors.push('数据不能为空')
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  /**
   * 获取支持的格式
   */
  getSupportedFormats(): ExportFormat[] {
    return [...this.supportedFormats]
  }

  /**
   * 获取默认选项
   */
  getDefaultOptions(): Partial<BaseExportOptions> {
    return { ...this.defaultOptions }
  }

  /**
   * 获取默认文件名前缀（子类可重写）
   */
  protected getDefaultFilenamePrefix(): string {
    return 'export'
  }

  /**
   * 获取列值
   */
  protected getColumnValue(record: T, column: ExportColumn): unknown {
    const keys = column.key.split('.')
    let value: unknown = record

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = (value as Record<string, unknown>)[key]
      } else {
        value = undefined
        break
      }
    }

    return value ?? column.defaultValue ?? ''
  }

  /**
   * 格式化值
   */
  protected formatValue(value: unknown, column: ExportColumn): string {
    if (value === null || value === undefined) {
      return column.defaultValue ?? ''
    }

    switch (column.dataType) {
      case 'date':
        return ValueFormatters.date(value)
      case 'number':
        return ValueFormatters.number(value)
      case 'currency':
        return ValueFormatters.currency(value)
      case 'boolean':
        return ValueFormatters.boolean(value)
      default:
        return String(value)
    }
  }

  /**
   * 脱敏值
   */
  protected maskValue(value: unknown, fieldName: string): string {
    if (!this.maskingConfig) return String(value)

    // 检查自定义脱敏规则
    if (this.maskingConfig.custom && this.maskingConfig.custom[fieldName]) {
      return this.maskingConfig.custom[fieldName](value)
    }

    // 使用自动脱敏
    if (DataMasking.shouldMask(fieldName)) {
      return DataMasking.auto(value, fieldName)
    }

    return String(value)
  }

  /**
   * 报告进度
   */
  protected reportProgress(processed: number, total: number, status: string): void {
    if (this.progressCallback) {
      this.progressCallback({
        percent: Math.round((processed / total) * 100),
        processed,
        total,
        status
      })
    }
  }

  /**
   * 生成Excel（子类可重写）
   */
  protected async generateExcel(
    data: Record<string, unknown>[],
    options: BaseExportOptions
  ): Promise<Blob> {
    // 这里应该使用实际的Excel库，如xlsx
    // 这是一个简化的实现
    const jsonStr = JSON.stringify({ data }, null, 2)
    return new Blob([jsonStr], { type: getMimeType('excel') })
  }

  /**
   * 生成CSV（子类可重写）
   */
  protected async generateCsv(
    data: Record<string, unknown>[],
    options: BaseExportOptions
  ): Promise<Blob> {
    const columns = this.getColumns()
    const headers = columns.map(col => col.header)
    const rows = [headers]

    for (const item of data) {
      const row = columns.map(col => {
        const value = item[col.key]
        return this.escapeCsvValue(String(value ?? ''))
      })
      rows.push(row)
    }

    const csvContent = rows.map(row => row.join(',')).join('\n')
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: getMimeType('csv') })
  }

  /**
   * 生成PDF（子类可重写）
   */
  protected async generatePdf(
    data: Record<string, unknown>[],
    options: BaseExportOptions
  ): Promise<Blob> {
    // 这里应该使用实际的PDF库，如jsPDF
    // 这是一个简化的实现
    const jsonStr = JSON.stringify({ data }, null, 2)
    return new Blob([jsonStr], { type: getMimeType('pdf') })
  }

  /**
   * 生成JSON
   */
  protected async generateJson(
    data: Record<string, unknown>[],
    options: BaseExportOptions
  ): Promise<Blob> {
    const jsonStr = JSON.stringify(data, null, 2)
    return new Blob([jsonStr], { type: getMimeType('json') })
  }

  /**
   * 生成Word（子类可重写）
   */
  protected async generateWord(
    data: Record<string, unknown>[],
    options: BaseExportOptions
  ): Promise<Blob> {
    // 这里应该使用实际的Word库，如docx
    // 这是一个简化的实现
    const jsonStr = JSON.stringify({ data }, null, 2)
    return new Blob([jsonStr], { type: getMimeType('word') })
  }

  /**
   * 生成XML
   */
  protected async generateXml(
    data: Record<string, unknown>[],
    options: BaseExportOptions
  ): Promise<Blob> {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<data>\n'

    for (const item of data) {
      xml += '  <record>\n'
      for (const [key, value] of Object.entries(item)) {
        xml += `    <${key}>${this.escapeXml(String(value ?? ''))}</${key}>\n`
      }
      xml += '  </record>\n'
    }

    xml += '</data>'
    return new Blob([xml], { type: getMimeType('xml') })
  }

  /**
   * 转义CSV值
   */
  protected escapeCsvValue(value: string): string {
    if (value.includes(',') || value.includes('\n') || value.includes('"')) {
      return `"${value.replace(/"/g, '""')}"`
    }
    return value
  }

  /**
   * 转义XML值
   */
  protected escapeXml(value: string): string {
    return value
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')
  }

  /**
   * 使用Web Worker处理（子类可实现）
   */
  protected async processWithWebWorker(
    data: Record<string, unknown>[],
    options: BaseExportOptions
  ): Promise<Record<string, unknown>[]> {
    // 子类可以实现Web Worker处理逻辑
    return data
  }

  /**
   * 批量处理数据
   */
  protected async processBatch<R>(
    data: T[],
    processor: (batch: T[]) => Promise<R>,
    batchSize: number
  ): Promise<R[]> {
    const batches = chunkArray(data, batchSize)
    const results: R[] = []

    for (let i = 0; i < batches.length; i++) {
      const result = await processor(batches[i])
      results.push(result)

      this.reportProgress(i + 1, batches.length, `正在处理第 ${i + 1}/${batches.length} 批数据`)
    }

    return results
  }
}
