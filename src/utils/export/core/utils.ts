import type { ExportFormat } from './interfaces'

/**
 * 生成导出文件名
 * @param prefix 文件名前缀
 * @param extension 文件扩展名
 * @param timestamp 时间戳
 * @returns 完整文件名
 */
export function generateExportFilename(
  prefix: string,
  extension: string,
  timestamp: Date = new Date()
): string {
  const dateStr = timestamp.toISOString().replace(/[:.]/g, '-').substring(0, 19)
  return `${prefix}_${dateStr}.${extension}`
}

/**
 * 获取格式对应的扩展名
 * @param format 导出格式
 * @returns 文件扩展名
 */
export function getFileExtension(format: ExportFormat): string {
  const extensionMap: Record<ExportFormat, string> = {
    excel: 'xlsx',
    csv: 'csv',
    pdf: 'pdf',
    json: 'json',
    word: 'docx',
    xml: 'xml'
  }
  return extensionMap[format] || format
}

/**
 * 获取格式对应的MIME类型
 * @param format 导出格式
 * @returns MIME类型
 */
export function getMimeType(format: ExportFormat): string {
  const mimeMap: Record<ExportFormat, string> = {
    excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    csv: 'text/csv;charset=utf-8',
    pdf: 'application/pdf',
    json: 'application/json;charset=utf-8',
    word: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xml: 'application/xml;charset=utf-8'
  }
  return mimeMap[format] || 'application/octet-stream'
}

/**
 * 下载文件
 * @param blob 文件Blob
 * @param filename 文件名
 */
export function downloadFile(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * 数组分块
 * @param array 原数组
 * @param chunkSize 块大小
 * @returns 分块后的数组
 */
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  if (chunkSize <= 0) {
    throw new Error('Chunk size must be greater than 0')
  }

  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }
  return chunks
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const k = 1024
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + units[i]
}

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化数字
 * @param num 数字
 * @param decimals 小数位数
 * @returns 格式化后的数字字符串
 */
export function formatNumber(num: number | string, decimals = 2): string {
  const n = typeof num === 'string' ? parseFloat(num) : num
  if (isNaN(n)) return ''
  return n.toFixed(decimals)
}

/**
 * 格式化货币
 * @param amount 金额
 * @param currency 货币符号
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(amount: number | string, currency = '¥'): string {
  const n = typeof amount === 'string' ? parseFloat(amount) : amount
  if (isNaN(n)) return ''
  return currency + n.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 转换为CSV安全字符串
 * @param value 原始值
 * @param delimiter 分隔符
 * @returns CSV安全字符串
 */
export function escapeCsvValue(value: unknown, delimiter = ','): string {
  if (value === null || value === undefined) return ''

  let stringValue = String(value)

  // 如果包含分隔符、换行符或双引号，需要用双引号包裹
  if (
    stringValue.includes(delimiter) ||
    stringValue.includes('\n') ||
    stringValue.includes('\r') ||
    stringValue.includes('"')
  ) {
    // 转义双引号
    stringValue = stringValue.replace(/"/g, '""')
    // 用双引号包裹
    stringValue = `"${stringValue}"`
  }

  return stringValue
}

/**
 * 数组转CSV
 * @param data 数据数组
 * @param headers 表头
 * @param options CSV选项
 * @returns CSV字符串
 */
export function arrayToCsv(
  data: Record<string, unknown>[],
  headers?: string[],
  options: {
    delimiter?: string
    lineTerminator?: string
    addBOM?: boolean
  } = {}
): string {
  const { delimiter = ',', lineTerminator = '\r\n', addBOM = true } = options

  const lines: string[] = []

  // 添加表头
  if (headers && headers.length > 0) {
    const headerLine = headers.map(header => escapeCsvValue(header, delimiter)).join(delimiter)
    lines.push(headerLine)
  }

  // 添加数据行
  data.forEach(row => {
    const values = headers
      ? headers.map(header => escapeCsvValue(row[header], delimiter))
      : Object.values(row).map(value => escapeCsvValue(value, delimiter))

    lines.push(values.join(delimiter))
  })

  let csv = lines.join(lineTerminator)

  // 添加BOM
  if (addBOM) {
    csv = '\ufeff' + csv
  }

  return csv
}

/**
 * 数字转中文
 * @param num 数字
 * @returns 中文数字
 */
export function numberToChinese(num: number): string {
  if (num === 0) return '零'

  const digits = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿']

  const numStr = Math.abs(num).toString()
  let result = ''

  for (let i = 0; i < numStr.length; i++) {
    const digit = parseInt(numStr[i])
    const unit = units[numStr.length - 1 - i]

    if (digit !== 0) {
      result += digits[digit] + unit
    } else if (result[result.length - 1] !== '零') {
      result += '零'
    }
  }

  // 清理多余的零
  result = result.replace(/零+/g, '零').replace(/零+$/, '')

  return (num < 0 ? '负' : '') + result
}

/**
 * 验证邮箱格式
 * @param email 邮箱
 * @returns 是否有效
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证手机号格式
 * @param phone 手机号
 * @returns 是否有效
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证身份证号格式
 * @param idCard 身份证号
 * @returns 是否有效
 */
export function isValidIdCard(idCard: string): boolean {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}
