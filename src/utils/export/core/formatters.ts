import { formatDate, formatNumber, formatCurrency, numberToChinese } from './utils'

/**
 * 值格式化器集合
 */
export class ValueFormatters {
  /**
   * 布尔值格式化
   */

  static boolean(value: unknown, trueText = '是', falseText = '否'): string {
    if (value === null || value === undefined) return ''
    return value ? trueText : falseText
  }

  /**
   * 日期格式化
   */

  static date(value: unknown, format = 'YYYY-MM-DD'): string {
    if (!value) return ''
    return formatDate(value, format)
  }

  /**
   * 日期时间格式化
   */

  static datetime(value: unknown, format = 'YYYY-MM-DD HH:mm:ss'): string {
    if (!value) return ''
    return formatDate(value, format)
  }

  /**
   * 时间格式化
   */

  static time(value: unknown, format = 'HH:mm:ss'): string {
    if (!value) return ''
    return formatDate(value, format)
  }

  /**
   * 数字格式化
   */

  static number(value: unknown, decimals = 0): string {
    if (value === null || value === undefined) return ''
    return formatNumber(value, decimals)
  }

  /**
   * 百分比格式化
   */

  static percent(value: unknown, decimals = 2): string {
    if (value === null || value === undefined) return ''
    const num = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(num)) return ''
    return (num * 100).toFixed(decimals) + '%'
  }

  /**
   * 货币格式化
   */

  static currency(value: unknown, symbol = '¥'): string {
    if (value === null || value === undefined) return ''
    return formatCurrency(value, symbol)
  }

  /**
   * 状态格式化
   */

  static status(value: unknown, statusMap: Record<string, string>): string {
    if (value === null || value === undefined) return ''
    return statusMap[String(value)] || String(value)
  }

  /**
   * 性别格式化
   */

  static gender(value: unknown): string {
    const genderMap: Record<string, string> = {
      '0': '女',
      '1': '男',
      '2': '其他',
      female: '女',
      male: '男',
      other: '其他',
      F: '女',
      M: '男'
    }
    return this.status(value, genderMap)
  }

  /**
   * 学历格式化
   */

  static education(value: unknown): string {
    const educationMap: Record<string, string> = {
      '1': '小学',
      '2': '初中',
      '3': '高中',
      '4': '大专',
      '5': '本科',
      '6': '硕士',
      '7': '博士',
      '8': '博士后',
      primary: '小学',
      junior: '初中',
      senior: '高中',
      college: '大专',
      bachelor: '本科',
      master: '硕士',
      doctor: '博士',
      postdoc: '博士后'
    }
    return this.status(value, educationMap)
  }

  /**
   * 婚姻状况格式化
   */

  static maritalStatus(value: unknown): string {
    const maritalMap: Record<string, string> = {
      '0': '未婚',
      '1': '已婚',
      '2': '离异',
      '3': '丧偶',
      single: '未婚',
      married: '已婚',
      divorced: '离异',
      widowed: '丧偶'
    }
    return this.status(value, maritalMap)
  }

  /**
   * 政治面貌格式化
   */

  static politicalStatus(value: unknown): string {
    const politicalMap: Record<string, string> = {
      '1': '中共党员',
      '2': '中共预备党员',
      '3': '共青团员',
      '4': '民革党员',
      '5': '民盟盟员',
      '6': '民建会员',
      '7': '民进会员',
      '8': '农工党党员',
      '9': '致公党党员',
      '10': '九三学社社员',
      '11': '台盟盟员',
      '12': '无党派人士',
      '13': '群众'
    }
    return this.status(value, politicalMap)
  }

  /**
   * 员工状态格式化
   */

  static employeeStatus(value: unknown): string {
    const statusMap: Record<string, string> = {
      active: '在职',
      resigned: '离职',
      retired: '退休',
      suspended: '停职',
      probation: '试用期',
      internship: '实习',
      '1': '在职',
      '2': '离职',
      '3': '退休',
      '4': '停职',
      '5': '试用期',
      '6': '实习'
    }
    return this.status(value, statusMap)
  }

  /**
   * 合同类型格式化
   */

  static contractType(value: unknown): string {
    const typeMap: Record<string, string> = {
      permanent: '无固定期限',
      fixed: '固定期限',
      project: '项目制',
      temporary: '临时',
      internship: '实习',
      outsourcing: '外包',
      '1': '无固定期限',
      '2': '固定期限',
      '3': '项目制',
      '4': '临时',
      '5': '实习',
      '6': '外包'
    }
    return this.status(value, typeMap)
  }

  /**
   * 请假类型格式化
   */

  static leaveType(value: unknown): string {
    const typeMap: Record<string, string> = {
      annual: '年假',
      sick: '病假',
      personal: '事假',
      marriage: '婚假',
      maternity: '产假',
      paternity: '陪产假',
      funeral: '丧假',
      other: '其他',
      '1': '年假',
      '2': '病假',
      '3': '事假',
      '4': '婚假',
      '5': '产假',
      '6': '陪产假',
      '7': '丧假',
      '8': '其他'
    }
    return this.status(value, typeMap)
  }

  /**
   * 审批状态格式化
   */

  static approvalStatus(value: unknown): string {
    const statusMap: Record<string, string> = {
      pending: '待审批',
      approved: '已通过',
      rejected: '已拒绝',
      cancelled: '已撤销',
      processing: '审批中',
      '0': '待审批',
      '1': '已通过',
      '2': '已拒绝',
      '3': '已撤销',
      '4': '审批中'
    }
    return this.status(value, statusMap)
  }

  /**
   * 工作日格式化
   */

  static workday(value: unknown): string {
    const workdayMap: Record<string, string> = {
      '1': '周一',
      '2': '周二',
      '3': '周三',
      '4': '周四',
      '5': '周五',
      '6': '周六',
      '7': '周日',
      monday: '周一',
      tuesday: '周二',
      wednesday: '周三',
      thursday: '周四',
      friday: '周五',
      saturday: '周六',
      sunday: '周日'
    }
    return this.status(value, workdayMap)
  }

  /**
   * 部门层级格式化
   */

  static departmentLevel(value: unknown): string {
    const levelMap: Record<string, string> = {
      '1': '公司',
      '2': '部门',
      '3': '科室',
      '4': '小组',
      company: '公司',
      department: '部门',
      section: '科室',
      group: '小组'
    }
    return this.status(value, levelMap)
  }

  /**
   * 职级格式化
   */

  static jobLevel(value: unknown): string {
    const levelMap: Record<string, string> = {
      '1': '初级',
      '2': '中级',
      '3': '高级',
      '4': '资深',
      '5': '专家',
      '6': '首席',
      junior: '初级',
      intermediate: '中级',
      senior: '高级',
      expert: '专家',
      principal: '首席'
    }
    return this.status(value, levelMap)
  }

  /**
   * 金额大写格式化
   */

  static amountInChinese(value: unknown): string {
    if (value === null || value === undefined) return ''
    const num = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(num)) return ''
    return numberToChinese(num) + '元整'
  }

  /**
   * 数组格式化
   */

  static array(value: unknown, separator = '、'): string {
    if (!Array.isArray(value)) return ''
    return value.filter(item => item !== null && item !== undefined).join(separator)
  }

  /**
   * JSON格式化
   */

  static json(value: unknown, indent = 2): string {
    if (value === null || value === undefined) return ''
    try {
      return JSON.stringify(value, null, indent)
    } catch {
      return String(value)
    }
  }

  /**
   * 文件大小格式化
   */

  static fileSize(value: unknown): string {
    if (value === null || value === undefined) return ''
    const bytes = typeof value === 'string' ? parseInt(value) : value
    if (isNaN(bytes)) return ''

    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return size.toFixed(2) + ' ' + units[unitIndex]
  }

  /**
   * 时长格式化
   */

  static duration(value: unknown, unit: 'seconds' | 'minutes' | 'hours' = 'seconds'): string {
    if (value === null || value === undefined) return ''
    let seconds = typeof value === 'string' ? parseInt(value) : value
    if (isNaN(seconds)) return ''

    // 转换为秒
    if (unit === 'minutes') seconds *= 60
    else if (unit === 'hours') seconds *= 3600

    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}小时${minutes}分${secs}秒`
    } else if (minutes > 0) {
      return `${minutes}分${secs}秒`
    } else {
      return `${secs}秒`
    }
  }

  /**
   * 空值格式化
   */

  static empty(value: unknown, defaultText = '-'): string {
    if (value === null || value === undefined || value === '') {
      return defaultText
    }
    return String(value)
  }

  /**
   * 自定义格式化
   */

  static custom(value: unknown, formatter: (val: unknown) => string): string {
    try {
      return formatter(value)
    } catch {
      return String(value)
    }
  }
}
