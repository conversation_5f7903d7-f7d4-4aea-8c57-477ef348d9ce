/**
 * 数据脱敏工具类
 */
export class DataMasking {
  /**
   * 身份证号脱敏
   * @param idCard 身份证号
   * @returns 脱敏后的身份证号
   */
  static idCard(idCard: string | null | undefined): string {
    if (!idCard || typeof idCard !== 'string') return ''
    if (idCard.length === 18) {
      return idCard.substring(0, 6) + '********' + idCard.substring(14)
    } else if (idCard.length === 15) {
      return idCard.substring(0, 6) + '*****' + idCard.substring(11)
    }
    return idCard
  }

  /**
   * 手机号脱敏
   * @param phone 手机号
   * @returns 脱敏后的手机号
   */
  static phone(phone: string | null | undefined): string {
    if (!phone || typeof phone !== 'string') return ''
    const phoneStr = phone.replace(/[^\d]/g, '')
    if (phoneStr.length === 11) {
      return phoneStr.substring(0, 3) + '****' + phoneStr.substring(7)
    }
    return phone
  }

  /**
   * 邮箱脱敏
   * @param email 邮箱
   * @returns 脱敏后的邮箱
   */
  static email(email: string | null | undefined): string {
    if (!email || typeof email !== 'string' || !email.includes('@')) return ''
    const [localPart, domain] = email.split('@')
    if (localPart.length <= 3) {
      return localPart.charAt(0) + '**@' + domain
    }
    return localPart.substring(0, 3) + '***@' + domain
  }

  /**
   * 银行账号脱敏
   * @param bankAccount 银行账号
   * @returns 脱敏后的银行账号
   */
  static bankAccount(bankAccount: string | null | undefined): string {
    if (!bankAccount || typeof bankAccount !== 'string') return ''
    const account = bankAccount.replace(/\s/g, '')
    if (account.length > 8) {
      return account.substring(0, 4) + ' **** **** ' + account.substring(account.length - 4)
    }
    return bankAccount
  }

  /**
   * 金额脱敏
   * @param amount 金额
   * @param showRange 是否显示范围
   * @returns 脱敏后的金额
   */
  static salary(amount: number | string | null | undefined, showRange = true): string {
    if (amount === null || amount === undefined) return ''
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
    if (isNaN(numAmount)) return ''

    if (!showRange) return '***'

    if (numAmount < 5000) return '<5k'
    if (numAmount < 10000) return '5k-10k'
    if (numAmount < 20000) return '10k-20k'
    if (numAmount < 30000) return '20k-30k'
    if (numAmount < 50000) return '30k-50k'
    return '>50k'
  }

  /**
   * 姓名脱敏
   * @param name 姓名
   * @returns 脱敏后的姓名
   */
  static name(name: string | null | undefined): string {
    if (!name || typeof name !== 'string') return ''
    if (name.length === 2) {
      return name.charAt(0) + '*'
    } else if (name.length > 2) {
      return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
    }
    return name
  }

  /**
   * 地址脱敏
   * @param address 地址
   * @returns 脱敏后的地址
   */
  static address(address: string | null | undefined): string {
    if (!address || typeof address !== 'string') return ''
    if (address.length <= 6) return address

    // 保留前6个字符，后面用星号替代
    return address.substring(0, 6) + '****'
  }

  /**
   * 车牌号脱敏
   * @param plateNumber 车牌号
   * @returns 脱敏后的车牌号
   */
  static plateNumber(plateNumber: string | null | undefined): string {
    if (!plateNumber || typeof plateNumber !== 'string') return ''
    if (plateNumber.length >= 7) {
      return plateNumber.substring(0, 2) + '***' + plateNumber.substring(plateNumber.length - 2)
    }
    return plateNumber
  }

  /**
   * 护照号脱敏
   * @param passport 护照号
   * @returns 脱敏后的护照号
   */
  static passport(passport: string | null | undefined): string {
    if (!passport || typeof passport !== 'string') return ''
    if (passport.length > 4) {
      return passport.substring(0, 2) + '****' + passport.substring(passport.length - 2)
    }
    return passport
  }

  /**
   * 社保账号脱敏
   * @param ssn 社保账号
   * @returns 脱敏后的社保账号
   */
  static socialSecurity(ssn: string | null | undefined): string {
    if (!ssn || typeof ssn !== 'string') return ''
    if (ssn.length > 6) {
      return ssn.substring(0, 3) + '****' + ssn.substring(ssn.length - 3)
    }
    return ssn
  }

  /**
   * 自定义脱敏
   * @param value 原始值
   * @param start 开始位置
   * @param end 结束位置
   * @param maskChar 脱敏字符
   * @returns 脱敏后的值
   */
  static custom(
    value: string | null | undefined,
    start: number,
    end: number,
    maskChar = '*'
  ): string {
    if (!value || typeof value !== 'string') return ''
    if (start < 0 || end > value.length || start >= end) return value

    const maskLength = end - start
    return value.substring(0, start) + maskChar.repeat(maskLength) + value.substring(end)
  }

  /**
   * 根据字段类型自动脱敏
   * @param value 原始值
   * @param fieldType 字段类型
   * @returns 脱敏后的值
   */

  static auto(value: unknown, fieldType: string): string {
    if (!value) return ''

    const fieldTypeMap: Record<string, (val: unknown) => string> = {
      idCard: this.idCard,
      phone: this.phone,
      mobile: this.phone,
      email: this.email,
      bankAccount: this.bankAccount,
      salary: this.salary,
      wage: this.salary,
      income: this.salary,
      name: this.name,
      address: this.address,
      plateNumber: this.plateNumber,
      passport: this.passport,
      ssn: this.socialSecurity,
      socialSecurity: this.socialSecurity
    }

    const maskingFn = fieldTypeMap[fieldType]
    if (maskingFn) {
      return maskingFn(value)
    }

    return String(value)
  }

  /**
   * 批量脱敏对象属性
   * @param obj 原始对象
   * @param maskConfig 脱敏配置
   * @returns 脱敏后的对象
   */
  static maskObject<T extends Record<string, unknown>>(
    obj: T,

    maskConfig: Record<string, boolean | ((val: unknown) => string)>
  ): T {
    const maskedObj = { ...obj }

    for (const [field, maskRule] of Object.entries(maskConfig)) {
      if (field in maskedObj) {
        if (typeof maskRule === 'boolean' && maskRule) {
          // 使用自动脱敏
          maskedObj[field] = this.auto(maskedObj[field], field)
        } else if (typeof maskRule === 'function') {
          // 使用自定义脱敏函数
          maskedObj[field] = maskRule(maskedObj[field])
        }
      }
    }

    return maskedObj
  }

  /**
   * 判断是否需要脱敏
   * @param fieldName 字段名
   * @param sensitiveFields 敏感字段列表
   * @returns 是否需要脱敏
   */
  static shouldMask(fieldName: string, sensitiveFields: string[] = []): boolean {
    const defaultSensitiveFields = [
      'idCard',
      'idNumber',
      'identityCard',
      'phone',
      'mobile',
      'telephone',
      'email',
      'emailAddress',
      'bankAccount',
      'bankCard',
      'accountNumber',
      'salary',
      'wage',
      'income',
      'bonus',
      'address',
      'homeAddress',
      'residenceAddress',
      'passport',
      'passportNumber',
      'ssn',
      'socialSecurity',
      'socialSecurityNumber',
      'plateNumber',
      'licensePlate'
    ]

    const fieldLower = fieldName.toLowerCase()
    return (
      sensitiveFields.includes(fieldName) ||
      defaultSensitiveFields.some(sensitive => fieldLower.includes(sensitive.toLowerCase()))
    )
  }
}
