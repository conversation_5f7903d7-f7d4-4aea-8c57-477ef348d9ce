/**
 * 懒加载工具函数
 * 用于组件级别的代码分割优化
 */

// 基础懒加载函数
export const lazyLoad = (importFn: () => Promise<unknown>) => {
  return () => importFn()
}

// 带错误处理的懒加载函数
export const lazyLoadWithError = (importFn: () => Promise<unknown>, fallback?: unknown) => {
  return () => importFn().catch(() => fallback)
}

// 预加载函数
export const preload = (importFn: () => Promise<unknown>) => {
  return () => {
    const promise = importFn()
    // 预加载但不等待
    promise.catch(() => {})
    return promise
  }
}

// 路由级别的懒加载
export const routeLazyLoad = (path: string) => {
  return () => import(`../views/${path}.vue`)
}

// 组件级别的懒加载
export const componentLazyLoad = (path: string) => {
  return () => import(`../components/${path}.vue`)
}

// 工具函数级别的懒加载
export const utilLazyLoad = (path: string) => {
  return () => import(`../utils/${path}.ts`)
}
