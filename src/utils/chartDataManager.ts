/**
 * @name ECharts数据流管理器
 * @description 提供高效的图表数据管理、更新策略和性能优化
 */
import { reactive, ref, computed, watch } from 'vue'
import type { EChartsOption, SeriesOption } from 'echarts'
import { debounce, throttle, isEqual, cloneDeep } from 'lodash-es'

// ===== 接口定义 =====
interface DataStreamConfig {
  /** 数据更新模式 */
  updateMode: 'replace' | 'append' | 'prepend' | 'merge'
  /** 最大数据点数量 */
  maxDataPoints?: number
  /** 更新频率限制（毫秒） */
  updateThrottle?: number
  /** 批量更新延迟（毫秒） */
  batchDelay?: number
  /** 启用数据压缩 */
  enableCompression?: boolean
  /** 数据采样率 */
  samplingRate?: number
  /** 自动清理过期数据 */
  autoCleanup?: boolean
  /** 数据保留时间（毫秒） */
  retentionTime?: number
}

interface DataPoint {
  /** 数据值 */
  value: Event
  /** 时间戳 */
  timestamp: number
  /** 数据ID */
  id?: string
  /** 额外元数据 */
  meta?: Record<string, unknown>
}

interface SeriesData {
  /** 系列ID */
  seriesId: string
  /** 数据点列表 */
  data: DataPoint[]
  /** 系列配置 */
  config: SeriesOption
  /** 最后更新时间 */
  lastUpdate: number
}

interface UpdateOperation {
  /** 操作类型 */
  type: 'add' | 'update' | 'remove' | 'clear'
  /** 系列ID */
  seriesId: string
  /** 数据 */
  data?: DataPoint | DataPoint[]
  /** 操作时间戳 */
  timestamp: number
  /** 操作ID */
  id: string
}

interface PerformanceMetrics {
  /** 更新次数 */
  updateCount: number
  /** 平均更新时间 */
  avgUpdateTime: number
  /** 数据点总数 */
  totalDataPoints: number
  /** 内存使用估算 */
  memoryUsage: number
  /** 压缩率 */
  compressionRatio: number
}

// ===== 数据流管理器类 =====
export class ChartDataManager {
  private config: Required<DataStreamConfig>
  private seriesMap = new Map<string, SeriesData>()
  private pendingOperations: UpdateOperation[] = []
  private isProcessing = false
  private updateQueue: Array<() => void> = []
  private metrics: PerformanceMetrics = reactive({
    updateCount: 0,
    avgUpdateTime: 0,
    totalDataPoints: 0,
    memoryUsage: 0,
    compressionRatio: 1
  })

  // 事件回调
  private onDataChange?: (option: EChartsOption) => void
  private onError?: (error: Error) => void
  private onPerformanceWarning?: (metrics: PerformanceMetrics) => void

  constructor(config: DataStreamConfig) {
    this.config = {
      updateMode: 'replace',
      maxDataPoints: 10000,
      updateThrottle: 50,
      batchDelay: 100,
      enableCompression: true,
      samplingRate: 1,
      autoCleanup: true,
      retentionTime: 24 * 60 * 60 * 1000, // 24小时
      ...config
    }

    // 启动自动清理
    if (this.config.autoCleanup) {
      this.startAutoCleanup()
    }

    // 创建节流更新函数
    this.throttledUpdate = throttle(
      this.processPendingOperations.bind(this),
      this.config.updateThrottle
    )

    // 创建批量更新函数
    this.debouncedBatchUpdate = debounce(this.executeBatchUpdate.bind(this), this.config.batchDelay)
  }

  private throttledUpdate: () => void
  private debouncedBatchUpdate: () => void

  /**
   * 设置事件监听器
   */
  on(event: 'data-change', callback: (option: EChartsOption) => void): void
  on(event: 'error', callback: (error: Error) => void): void
  on(event: 'performance-warning', callback: (metrics: PerformanceMetrics) => void): void
  on(event: string, callback: (data: unknown) => void): void {
    switch (event) {
      case 'data-change':
        this.onDataChange = callback
        break
      case 'error':
        this.onError = callback
        break
      case 'performance-warning':
        this.onPerformanceWarning = callback
        break
    }
  }

  /**
   * 添加数据系列
   */
  addSeries(seriesId: string, config: SeriesOption): void {
    try {
      if (this.seriesMap.has(seriesId)) {
        console.warn(`系列 ${seriesId} 已存在，将被覆盖`)
      }

      this.seriesMap.set(seriesId, {
        seriesId,
        data: [],
        config: cloneDeep(config),
        lastUpdate: Date.now()
      })

      this.updateMetrics()
      this.triggerUpdate()
    } catch (error) {
      this.handleError(new Error(`添加系列失败: ${error}`))
    }
  }

  /**
   * 添加数据点
   */
  addDataPoint(seriesId: string, value: Event, meta?: Record<string, unknown>): void {
    const operation: UpdateOperation = {
      type: 'add',
      seriesId,
      data: {
        value,
        timestamp: Date.now(),
        id: this.generateId(),
        meta
      },
      timestamp: Date.now(),
      id: this.generateId()
    }

    this.queueOperation(operation)
  }

  /**
   * 批量添加数据点
   */
  addDataPoints(
    seriesId: string,
    dataPoints: Array<{ value: Event; meta?: Record<string, unknown> }>
  ): void {
    const timestamp = Date.now()

    dataPoints.forEach(point => {
      const operation: UpdateOperation = {
        type: 'add',
        seriesId,
        data: {
          value: point.value,
          timestamp,
          id: this.generateId(),
          meta: point.meta
        },
        timestamp,
        id: this.generateId()
      }

      this.queueOperation(operation)
    })
  }

  /**
   * 更新数据点
   */
  updateDataPoint(seriesId: string, dataId: string, newValue: Event): void {
    const operation: UpdateOperation = {
      type: 'update',
      seriesId,
      data: { id: dataId, value: newValue },
      timestamp: Date.now(),
      id: this.generateId()
    }

    this.queueOperation(operation)
  }

  /**
   * 移除数据点
   */
  removeDataPoint(seriesId: string, dataId: string): void {
    const operation: UpdateOperation = {
      type: 'remove',
      seriesId,
      data: { id: dataId },
      timestamp: Date.now(),
      id: this.generateId()
    }

    this.queueOperation(operation)
  }

  /**
   * 清空系列数据
   */
  clearSeries(seriesId: string): void {
    const operation: UpdateOperation = {
      type: 'clear',
      seriesId,
      timestamp: Date.now(),
      id: this.generateId()
    }

    this.queueOperation(operation)
  }

  /**
   * 队列操作
   */
  private queueOperation(operation: UpdateOperation): void {
    this.pendingOperations.push(operation)

    // 根据配置选择更新策略
    if (this.config.batchDelay > 0) {
      this.debouncedBatchUpdate()
    } else {
      this.throttledUpdate()
    }
  }

  /**
   * 处理待处理的操作
   */
  private processPendingOperations(): void {
    if (this.isProcessing || this.pendingOperations.length === 0) return

    this.isProcessing = true
    const startTime = performance.now()

    try {
      const operations = [...this.pendingOperations]
      this.pendingOperations = []

      // 按系列分组操作
      const operationsBySeriesId = new Map<string, UpdateOperation[]>()

      operations.forEach(op => {
        if (!operationsBySeriesId.has(op.seriesId)) {
          operationsBySeriesId.set(op.seriesId, [])
        }
        operationsBySeriesId.get(op.seriesId)!.push(op)
      })

      // 执行操作
      operationsBySeriesId.forEach((ops, seriesId) => {
        this.executeOperations(seriesId, ops)
      })

      // 触发更新
      this.triggerUpdate()

      // 更新性能指标
      const endTime = performance.now()
      this.updatePerformanceMetrics(endTime - startTime)
    } catch (error) {
      this.handleError(new Error(`处理操作失败: ${error}`))
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 执行批量更新
   */
  private executeBatchUpdate(): void {
    this.updateQueue.forEach(fn => fn())
    this.updateQueue = []
    this.processPendingOperations()
  }

  /**
   * 执行系列操作
   */
  private executeOperations(seriesId: string, operations: UpdateOperation[]): void {
    const series = this.seriesMap.get(seriesId)
    if (!series) {
      console.warn(`系列 ${seriesId} 不存在`)
      return
    }

    operations.forEach(op => {
      switch (op.type) {
        case 'add':
          this.executeAddOperation(series, op.data)
          break
        case 'update':
          this.executeUpdateOperation(series, op.data)
          break
        case 'remove':
          this.executeRemoveOperation(series, op.data)
          break
        case 'clear':
          this.executeClearOperation(series)
          break
      }
    })

    // 数据压缩和采样
    if (this.config.enableCompression) {
      this.compressSeriesData(series)
    }

    // 数据采样
    if (this.config.samplingRate < 1) {
      this.sampleSeriesData(series)
    }

    // 限制数据点数量
    this.limitDataPoints(series)

    series.lastUpdate = Date.now()
  }

  /**
   * 执行添加操作
   */
  private executeAddOperation(series: SeriesData, dataPoint: DataPoint): void {
    switch (this.config.updateMode) {
      case 'append':
        series.data.push(dataPoint)
        break
      case 'prepend':
        series.data.unshift(dataPoint)
        break
      case 'replace':
        series.data = [dataPoint]
        break
      case 'merge':
        // 按时间戳排序插入
        this.insertSorted(series.data, dataPoint)
        break
    }
  }

  /**
   * 按时间戳排序插入
   */
  private insertSorted(data: DataPoint[], newPoint: DataPoint): void {
    let insertIndex = data.length

    for (let i = data.length - 1; i >= 0; i--) {
      if (data[i].timestamp <= newPoint.timestamp) {
        insertIndex = i + 1
        break
      }
    }

    data.splice(insertIndex, 0, newPoint)
  }

  /**
   * 执行更新操作
   */
  private executeUpdateOperation(
    series: SeriesData,
    updateData: { id: string; value: Event }
  ): void {
    const index = series.data.findIndex(point => point.id === updateData.id)
    if (index !== -1) {
      series.data[index].value = updateData.value
      series.data[index].timestamp = Date.now()
    }
  }

  /**
   * 执行移除操作
   */
  private executeRemoveOperation(series: SeriesData, removeData: { id: string }): void {
    const index = series.data.findIndex(point => point.id === removeData.id)
    if (index !== -1) {
      series.data.splice(index, 1)
    }
  }

  /**
   * 执行清空操作
   */
  private executeClearOperation(series: SeriesData): void {
    series.data = []
  }

  /**
   * 压缩系列数据
   */
  private compressSeriesData(series: SeriesData): void {
    if (series.data.length < 100) return

    const compressed: DataPoint[] = []
    const tolerance = this.calculateCompressionTolerance(series.data)

    let lastPoint = series.data[0]
    compressed.push(lastPoint)

    for (let i = 1; i < series.data.length - 1; i++) {
      const currentPoint = series.data[i]
      const nextPoint = series.data[i + 1]

      // 使用Douglas-Peucker算法的简化版本
      if (this.calculatePointDistance(lastPoint, currentPoint, nextPoint) > tolerance) {
        compressed.push(currentPoint)
        lastPoint = currentPoint
      }
    }

    // 确保保留最后一个点
    if (series.data.length > 0) {
      compressed.push(series.data[series.data.length - 1])
    }

    const originalLength = series.data.length
    series.data = compressed

    // 更新压缩率
    this.metrics.compressionRatio = compressed.length / originalLength
  }

  /**
   * 计算压缩容差
   */
  private calculateCompressionTolerance(data: DataPoint[]): number {
    if (data.length < 2) return 0

    const values = data.map(point => (typeof point.value === 'number' ? point.value : 0))
    const min = Math.min(...values)
    const max = Math.max(...values)

    return (max - min) * 0.01 // 1%容差
  }

  /**
   * 计算点到直线的距离
   */
  private calculatePointDistance(p1: DataPoint, p2: DataPoint, p3: DataPoint): number {
    const x1 = p1.timestamp,
      y1 = typeof p1.value === 'number' ? p1.value : 0
    const x2 = p2.timestamp,
      y2 = typeof p2.value === 'number' ? p2.value : 0
    const x3 = p3.timestamp,
      y3 = typeof p3.value === 'number' ? p3.value : 0

    const A = y3 - y1
    const B = x1 - x3
    const C = x3 * y1 - x1 * y3

    return Math.abs(A * x2 + B * y2 + C) / Math.sqrt(A * A + B * B)
  }

  /**
   * 数据采样
   */
  private sampleSeriesData(series: SeriesData): void {
    if (this.config.samplingRate >= 1 || series.data.length <= 1) return

    const targetLength = Math.floor(series.data.length * this.config.samplingRate)
    const step = series.data.length / targetLength
    const sampled: DataPoint[] = []

    for (let i = 0; i < targetLength; i++) {
      const index = Math.floor(i * step)
      sampled.push(series.data[index])
    }

    series.data = sampled
  }

  /**
   * 限制数据点数量
   */
  private limitDataPoints(series: SeriesData): void {
    if (series.data.length > this.config.maxDataPoints) {
      const excess = series.data.length - this.config.maxDataPoints
      series.data.splice(0, excess)
    }
  }

  /**
   * 自动清理过期数据
   */
  private startAutoCleanup(): void {
    setInterval(() => {
      const now = Date.now()

      this.seriesMap.forEach(series => {
        const cutoff = now - this.config.retentionTime
        series.data = series.data.filter(point => point.timestamp > cutoff)
      })

      this.updateMetrics()
    }, 60000) // 每分钟执行一次清理
  }

  /**
   * 触发更新
   */
  private triggerUpdate(): void {
    if (!this.onDataChange) return

    try {
      const option = this.generateEChartsOption()
      this.onDataChange(option)
    } catch (error) {
      this.handleError(new Error(`生成图表选项失败: ${error}`))
    }
  }

  /**
   * 生成ECharts选项
   */
  private generateEChartsOption(): EChartsOption {
    const series = Array.from(this.seriesMap.values()).map(seriesData => ({
      ...seriesData.config,
      data: seriesData.data.map(point => point.value)
    }))

    return {
      series
    }
  }

  /**
   * 更新指标
   */
  private updateMetrics(): void {
    this.metrics.totalDataPoints = Array.from(this.seriesMap.values()).reduce(
      (total, series) => total + series.data.length,
      0
    )

    // 估算内存使用（简化计算）
    this.metrics.memoryUsage = this.metrics.totalDataPoints * 64 // 假设每个数据点64字节
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(updateTime: number): void {
    this.metrics.updateCount++
    this.metrics.avgUpdateTime = (this.metrics.avgUpdateTime + updateTime) / 2

    // 性能警告
    if (updateTime > 100 && this.onPerformanceWarning) {
      this.onPerformanceWarning(this.metrics)
    }
  }

  /**
   * 错误处理
   */
  private handleError(error: Error): void {
    console.error('ChartDataManager Error:', error)
    if (this.onError) {
      this.onError(error)
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 获取系列数据
   */
  getSeriesData(seriesId: string): DataPoint[] | null {
    const series = this.seriesMap.get(seriesId)
    return series ? [...series.data] : null
  }

  /**
   * 获取所有系列ID
   */
  getSeriesIds(): string[] {
    return Array.from(this.seriesMap.keys())
  }

  /**
   * 强制更新
   */
  forceUpdate(): void {
    this.processPendingOperations()
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.seriesMap.clear()
    this.pendingOperations = []
    this.updateQueue = []
    this.isProcessing = false
  }
}

// ===== 工厂函数 =====
export function createChartDataManager(config: DataStreamConfig): ChartDataManager {
  return new ChartDataManager(config)
}

// ===== 预设配置 =====
export const DataManagerPresets = {
  /** 实时数据流 */
  REAL_TIME: {
    updateMode: 'append',
    maxDataPoints: 1000,
    updateThrottle: 16, // 60fps
    batchDelay: 0,
    enableCompression: true,
    samplingRate: 1,
    autoCleanup: true,
    retentionTime: 5 * 60 * 1000 // 5分钟
  } as DataStreamConfig,

  /** 历史数据 */
  HISTORICAL: {
    updateMode: 'replace',
    maxDataPoints: 10000,
    updateThrottle: 100,
    batchDelay: 200,
    enableCompression: true,
    samplingRate: 0.8,
    autoCleanup: false,
    retentionTime: 24 * 60 * 60 * 1000 // 24小时
  } as DataStreamConfig,

  /** 大数据集 */
  BIG_DATA: {
    updateMode: 'replace',
    maxDataPoints: 5000,
    updateThrottle: 200,
    batchDelay: 500,
    enableCompression: true,
    samplingRate: 0.5,
    autoCleanup: true,
    retentionTime: 60 * 60 * 1000 // 1小时
  } as DataStreamConfig
}

export default ChartDataManager
