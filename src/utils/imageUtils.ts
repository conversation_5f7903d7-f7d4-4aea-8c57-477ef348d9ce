/**
 * @name imageUtils
 * @description 图片处理工具函数
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */

/**
 * 获取图片缩略图URL
 * @param url 原始图片URL
 * @param width 宽度
 * @param height 高度
 * @param quality 质量 (0-100)
 */
export function getThumbnailUrl(
  url: string,
  width: number,
  height?: number,
  quality = 85
): string {
  // 如果是本地图片，直接返回
  if (url.startsWith('/') || url.startsWith('./')) {
    return url
  }

  // 如果已经是处理过的URL，返回原URL
  if (url.includes('?x-oss-process') || url.includes('imageView2')) {
    return url
  }

  // 阿里云OSS图片处理
  if (url.includes('aliyuncs.com')) {
    const h = height || width
    return `${url}?x-oss-process=image/resize,m_fill,w_${width},h_${h}/quality,q_${quality}`
  }

  // 七牛云图片处理
  if (url.includes('qiniu') || url.includes('qnssl')) {
    const h = height || width
    return `${url}?imageView2/1/w/${width}/h/${h}/q/${quality}`
  }

  // 其他CDN，返回原图
  return url
}

/**
 * 预加载图片
 * @param urls 图片URL数组
 */
export function preloadImages(urls: string[]): Promise<void[]> {
  return Promise.all(
    urls.map(
      (url) =>
        new Promise<void>((resolve, reject) => {
          const img = new Image()
          img.onload = () => resolve()
          img.onerror = () => reject(new Error(`Failed to load image: ${url}`))
          img.src = url
        })
    )
  )
}

/**
 * 获取图片尺寸
 * @param url 图片URL
 */
export function getImageSize(
  url: string
): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({ width: img.width, height: img.height })
    }
    img.onerror = () => reject(new Error(`Failed to load image: ${url}`))
    img.src = url
  })
}

/**
 * 将图片转换为Base64
 * @param file 图片文件
 */
export function imageToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsDataURL(file)
  })
}

/**
 * 压缩图片
 * @param file 图片文件
 * @param maxWidth 最大宽度
 * @param quality 质量 (0-1)
 */
export function compressImage(
  file: File,
  maxWidth = 1920,
  quality = 0.85
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!

      let { width, height } = img
      if (width > maxWidth) {
        height = (maxWidth / width) * height
        width = maxWidth
      }

      canvas.width = width
      canvas.height = height
      ctx.drawImage(img, 0, 0, width, height)

      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to compress image'))
          }
        },
        file.type,
        quality
      )
    }
    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = URL.createObjectURL(file)
  })
}
