/**
 * 工具函数统一导出
 *
 * 组织结构：
 * - core/        核心工具函数（数组、对象、字符串、数字、日期、树形结构）
 * - function/    函数工具（防抖、节流、重试、缓存等）
 * - validation/  验证工具（表单验证、数据验证）
 * - format/      格式化工具（日期、数字、文件大小等）
 * - dom/         DOM操作工具（事件、样式、元素操作）
 * - browser/     浏览器工具（存储、剪贴板、下载等）
 * - cache/       缓存系统（统一缓存接口）
 * - export/      导出服务（Excel、PDF等）
 * - request/     网络请求（axios封装）
 */

// 核心工具函数
export * from './core'
export * as core from './core'

// 函数工具
export * from './function-utils'

// DOM操作工具
export * from './dom'
export * as dom from './dom'

// 浏览器工具
export * from './browser'
export * as browser from './browser'

// 验证工具
export * from './validation'
export * as validation from './validation'

// 格式化工具
export * from './format'
export * as format from './format'

// 缓存系统
export * from './cache'

// 导出服务
export * from './export'

// 请求工具
export { default as request } from './request'

// 剪贴板
export * from './clipboard'

// 加密工具
export * from './encryption'

// 主题工具
export * from './theme'

// 性能工具
export * from './performance'

// 验证器（向后兼容）
export * from './validators'

// 滚动工具
export * from './scroll'

// 为了向后兼容，从common和enhanced-utils导出已迁移的函数
export {
  deepClone,
  generateId,
  chunk,
  formatFileSize,
  isEmpty,
  retry,
  debounce,
  throttle,
  sleep,
  promiseLimit
} from './common'

export {
  // 数据处理
  objectDiff,
  uniqueArray,
  groupBy as groupByKey,
  flattenTree,
  arrayToTree as buildTree,

  // 字符串处理
  camelToSnake as toSnakeCase,
  snakeToCamel as toCamelCase,
  capitalize as capitalizeFirst,
  randomString,
  highlightText,

  // 数字处理
  formatNumber,
  calculatePercentage,

  // 日期处理
  formatRelativeTime,
  formatTimeRange,

  // 验证
  isValidEmail,
  isValidPhone,
  isValidIdCard,
  isValidUrl,

  // 浏览器工具
  copyToClipboard,
  downloadFile,
  getBrowserInfo,

  // 业务工具
  confirmAction,
  generateUUID
} from './enhanced-utils'
