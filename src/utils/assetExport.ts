interface AssetRecord {
  id: string
  assetName: string
  assetNumber: string
  assetType: string
  responsibleDepartment: string
  responsiblePerson: string
  responsiblePersonId: string
  acquisitionDate: string
  acquisitionValue: number
  currentValue: number
  location: string
  status: 'normal' | 'damaged' | 'lost' | 'retired' | 'transferred'
  isRecovered: boolean
  recoveryDate?: string
  damageLossStatus?: string
  lastMaintenanceDate?: string
  warrantyExpiry?: string
  remarks?: string
  createTime: string
  updateTime: string
}

interface AssetRecoveryChecklist {
  checklistId: string
  employeeId: string
  employeeName: string
  departmentName: string
  assetName: string
  assetNumber: string
  responsibleDepartment: string
  isRecovered: boolean
  recoveryDate?: string
  damageLossStatus?: string
  damagePhotos?: string[]
  compensationAmount?: number
  confirmedBy?: string
  confirmedAt?: string
  remarks?: string
}

interface AssetExportFilter {
  departmentIds?: string[]
  assetTypes?: string[]
  status?: string[]
  dateRange?: {
    start: string
    end: string
    type: 'acquisition' | 'recovery' | 'maintenance'
  }
  responsiblePersonId?: string
  keyword?: string
  valueRange?: {
    min: number
    max: number
  }
}

interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf'
  includeImages: boolean
  includeHistory: boolean
  groupBy?: 'department' | 'type' | 'status' | 'person'
  summaryLevel: 'none' | 'basic' | 'detailed'
  template?: string
}

interface ExportResult {
  success: boolean
  blob?: Blob
  filename: string
  recordCount: number
  totalValue?: number
  error?: string
}

// 导出数据的通用类型
interface ExportDataItem {
  [key: string]: string | number | boolean | undefined
  资产状态?: string
  资产类型?: string
  责任部门?: string
  当前价值?: number
}

// 汇总数据类型
interface SummaryData {
  导出时间: string
  记录总数: number
  总价值: number
  按状态统计?: Record<string, number>
  按类型统计?: Record<string, number>
  按部门统计?: Record<string, number>
}

/**
 * CLEAN-AUX-006: 资产清单导出功能
 * 支持多条件筛选和多格式导出的资产管理系统
 */
export class AssetExport {
  private assetRecords: Map<string, AssetRecord> = new Map()
  private recoveryChecklists: Map<string, AssetRecoveryChecklist> = new Map()

  // 预定义资产类型
  private readonly assetTypes = [
    { value: 'computer', label: '计算机设备' },
    { value: 'office_furniture', label: '办公家具' },
    { value: 'office_equipment', label: '办公设备' },
    { value: 'vehicle', label: '车辆' },
    { value: 'instrument', label: '仪器设备' },
    { value: 'book', label: '图书资料' },
    { value: 'software', label: '软件许可' },
    { value: 'other', label: '其他资产' }
  ]

  // 资产状态映射
  private readonly statusLabels = {
    normal: '正常',
    damaged: '损坏',
    lost: '丢失',
    retired: '报废',
    transferred: '调拨'
  }

  constructor() {
    this.initializeMockData()
  }

  // 初始化模拟数据
  private initializeMockData() {
    // 模拟资产数据
    const mockAssets: AssetRecord[] = [
      {
        id: 'asset_001',
        assetName: '戴尔台式机Dell-3080',
        assetNumber: 'PC-2024-001',
        assetType: 'computer',
        responsibleDepartment: '技术部',
        responsiblePerson: '张三',
        responsiblePersonId: 'emp_001',
        acquisitionDate: '2024-01-15',
        acquisitionValue: 5000,
        currentValue: 4000,
        location: '技术部-201室',
        status: 'normal',
        isRecovered: false,
        lastMaintenanceDate: '2024-06-15',
        warrantyExpiry: '2027-01-15',
        createTime: '2024-01-15T09:00:00.000Z',
        updateTime: '2024-06-15T14:30:00.000Z'
      },
      {
        id: 'asset_002',
        assetName: '激光打印机HP-P1108',
        assetNumber: 'PR-2024-001',
        assetType: 'office_equipment',
        responsibleDepartment: '人事部',
        responsiblePerson: '李四',
        responsiblePersonId: 'emp_002',
        acquisitionDate: '2024-03-20',
        acquisitionValue: 1200,
        currentValue: 800,
        location: '人事部-102室',
        status: 'damaged',
        isRecovered: false,
        damageLossStatus: '打印头故障，需要更换',
        createTime: '2024-03-20T10:00:00.000Z',
        updateTime: '2024-11-15T16:45:00.000Z'
      }
    ]

    mockAssets.forEach(asset => {
      this.assetRecords.set(asset.id, asset)
    })

    // 模拟回收清单数据
    const mockChecklists: AssetRecoveryChecklist[] = [
      {
        checklistId: 'checklist_001',
        employeeId: 'emp_003',
        employeeName: '王五',
        departmentName: '市场部',
        assetName: '联想笔记本ThinkPad-E14',
        assetNumber: 'LT-2023-005',
        responsibleDepartment: '市场部',
        isRecovered: true,
        recoveryDate: '2024-12-15',
        damageLossStatus: '屏幕有轻微划痕',
        compensationAmount: 200,
        confirmedBy: '赵六',
        confirmedAt: '2024-12-16T09:30:00.000Z',
        remarks: '已完成交接，设备状态良好'
      }
    ]

    mockChecklists.forEach(checklist => {
      this.recoveryChecklists.set(checklist.checklistId, checklist)
    })
  }

  // 导出资产清单
  async exportAssetList(
    filter: AssetExportFilter = {},
    options: ExportOptions = {
      format: 'excel',
      includeImages: false,
      includeHistory: false,
      summaryLevel: 'basic'
    }
  ): Promise<ExportResult> {
    try {
      // 筛选资产数据
      const filteredAssets = this.filterAssets(filter)

      if (filteredAssets.length === 0) {
        return {
          success: false,
          filename: '',
          recordCount: 0,
          error: '没有符合条件的资产数据'
        }
      }

      // 生成导出数据
      const exportData = this.prepareExportData(filteredAssets, options)

      // 根据格式生成文件
      let blob: Blob
      let filename: string

      switch (options.format) {
        case 'excel':
          blob = await this.generateExcelFile(exportData, options)
          filename = this.generateFilename('资产清单', 'xlsx')
          break
        case 'csv':
          blob = this.generateCsvFile(exportData)
          filename = this.generateFilename('资产清单', 'csv')
          break
        case 'pdf':
          blob = await this.generatePdfFile(exportData, options)
          filename = this.generateFilename('资产清单', 'pdf')
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }

      // 计算汇总信息
      const totalValue = filteredAssets.reduce((sum, asset) => sum + asset.currentValue, 0)

      return {
        success: true,
        blob,
        filename,
        recordCount: filteredAssets.length,
        totalValue
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        recordCount: 0,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  // 导出回收清单
  async exportRecoveryChecklist(
    employeeId?: string,
    departmentId?: string,
    options: ExportOptions = {
      format: 'excel',
      includeImages: true,
      includeHistory: false,
      summaryLevel: 'detailed'
    }
  ): Promise<ExportResult> {
    try {
      let checklists = Array.from(this.recoveryChecklists.values())

      // 按条件筛选
      if (employeeId) {
        checklists = checklists.filter(item => item.employeeId === employeeId)
      }

      if (departmentId) {
        checklists = checklists.filter(item => item.departmentName === departmentId)
      }

      if (checklists.length === 0) {
        return {
          success: false,
          filename: '',
          recordCount: 0,
          error: '没有符合条件的回收记录'
        }
      }

      // 生成导出数据
      const exportData = this.prepareRecoveryExportData(checklists, options)

      let blob: Blob
      let filename: string

      switch (options.format) {
        case 'excel':
          blob = await this.generateRecoveryExcelFile(exportData, options)
          filename = this.generateFilename('资产回收清单', 'xlsx')
          break
        case 'csv':
          blob = this.generateRecoveryCsvFile(exportData)
          filename = this.generateFilename('资产回收清单', 'csv')
          break
        case 'pdf':
          blob = await this.generateRecoveryPdfFile(exportData, options)
          filename = this.generateFilename('资产回收清单', 'pdf')
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }

      // 计算补偿金额总计
      const totalCompensation = checklists.reduce(
        (sum, item) => sum + (item.compensationAmount || 0),
        0
      )

      return {
        success: true,
        blob,
        filename,
        recordCount: checklists.length,
        totalValue: totalCompensation
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        recordCount: 0,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  // 筛选资产数据
  private filterAssets(filter: AssetExportFilter): AssetRecord[] {
    let assets = Array.from(this.assetRecords.values())

    // 按部门筛选
    if (filter.departmentIds && filter.departmentIds.length > 0) {
      assets = assets.filter(asset => filter.departmentIds!.includes(asset.responsibleDepartment))
    }

    // 按资产类型筛选
    if (filter.assetTypes && filter.assetTypes.length > 0) {
      assets = assets.filter(asset => filter.assetTypes!.includes(asset.assetType))
    }

    // 按状态筛选
    if (filter.status && filter.status.length > 0) {
      assets = assets.filter(asset => filter.status!.includes(asset.status))
    }

    // 按责任人筛选
    if (filter.responsiblePersonId) {
      assets = assets.filter(asset => asset.responsiblePersonId === filter.responsiblePersonId)
    }

    // 按关键词筛选
    if (filter.keyword) {
      const keyword = filter.keyword.toLowerCase()
      assets = assets.filter(
        asset =>
          asset.assetName.toLowerCase().includes(keyword) ||
          asset.assetNumber.toLowerCase().includes(keyword) ||
          asset.responsiblePerson.toLowerCase().includes(keyword)
      )
    }

    // 按价值范围筛选
    if (filter.valueRange) {
      assets = assets.filter(
        asset =>
          asset.currentValue >= filter.valueRange!.min &&
          asset.currentValue <= filter.valueRange!.max
      )
    }

    // 按日期范围筛选
    if (filter.dateRange) {
      const { start, end, type } = filter.dateRange
      assets = assets.filter(asset => {
        let dateToCompare: string
        switch (type) {
          case 'acquisition':
            dateToCompare = asset.acquisitionDate
            break
          case 'recovery':
            dateToCompare = asset.recoveryDate || ''
            break
          case 'maintenance':
            dateToCompare = asset.lastMaintenanceDate || ''
            break
          default:
            dateToCompare = asset.acquisitionDate
        }
        return dateToCompare >= start && dateToCompare <= end
      })
    }

    return assets
  }

  // 准备导出数据
  private prepareExportData(assets: AssetRecord[], options: ExportOptions): ExportDataItem[] {
    return assets.map(asset => ({
      资产编号: asset.assetNumber,
      资产名称: asset.assetName,
      资产类型: this.getAssetTypeLabel(asset.assetType),
      责任部门: asset.responsibleDepartment,
      责任人: asset.responsiblePerson,
      取得日期: asset.acquisitionDate,
      取得价值: asset.acquisitionValue,
      当前价值: asset.currentValue,
      位置: asset.location,
      资产状态: this.statusLabels[asset.status],
      备注: asset.remarks || ''
    }))
  }

  // 准备回收导出数据
  private prepareRecoveryExportData(
    checklists: AssetRecoveryChecklist[],
    options: ExportOptions
  ): ExportDataItem[] {
    return checklists.map(checklist => ({
      员工姓名: checklist.employeeName,
      部门: checklist.departmentName,
      资产名称: checklist.assetName,
      资产编号: checklist.assetNumber,
      是否回收: checklist.isRecovered ? '是' : '否',
      回收日期: checklist.recoveryDate || '',
      损坏情况: checklist.damageLossStatus || '',
      补偿金额: checklist.compensationAmount || 0,
      确认人: checklist.confirmedBy || '',
      备注: checklist.remarks || ''
    }))
  }

  // 生成Excel文件
  private async generateExcelFile(data: ExportDataItem[], options: ExportOptions): Promise<Blob> {
    // 这里应该使用Excel库，如ExcelJS
    // 暂时返回CSV格式
    const csvContent = this.arrayToCsv(data)
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'application/vnd.ms-excel;charset=utf-8' })
  }

  // 生成CSV文件
  private generateCsvFile(data: ExportDataItem[]): Blob {
    const csvContent = this.arrayToCsv(data)
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
  }

  // 生成回收清单Excel文件
  private async generateRecoveryExcelFile(
    data: ExportDataItem[],
    options: ExportOptions
  ): Promise<Blob> {
    // 暂时返回CSV格式
    return this.generateCsvFile(data)
  }

  // 生成回收清单CSV文件
  private generateRecoveryCsvFile(data: ExportDataItem[]): Blob {
    const csvContent = this.arrayToCsv(data)
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
  }

  // 数组转CSV
  private arrayToCsv(data: ExportDataItem[]): string {
    if (data.length === 0) return ''

    const headers = Object.keys(data[0])
    const rows = data.map(item =>
      headers.map(header => `"${String(item[header] || '').replace(/"/g, '""')}"`)
    )

    return [headers.map(h => `"${h}"`).join(','), ...rows.map(row => row.join(','))].join('\n')
  }

  // 生成PDF文件
  private async generatePdfFile(
    data: ExportDataItem[] | Record<string, ExportDataItem[]>,
    options: ExportOptions
  ): Promise<Blob> {
    // 这里应该使用PDF库，如jsPDF
    const pdfContent = {
      title: '资产清单报表',
      data,
      options,
      generateTime: new Date().toISOString()
    }

    return new Blob([JSON.stringify(pdfContent)], { type: 'application/pdf' })
  }

  // 生成回收清单PDF文件
  private async generateRecoveryPdfFile(
    data: ExportDataItem[],
    options: ExportOptions
  ): Promise<Blob> {
    const pdfContent = {
      title: '资产回收清单',
      data,
      options,
      generateTime: new Date().toISOString()
    }

    return new Blob([JSON.stringify(pdfContent)], { type: 'application/pdf' })
  }

  // 生成汇总数据
  private generateSummaryData(
    data: ExportDataItem[] | Record<string, ExportDataItem[]>,
    level: string
  ): SummaryData[] {
    const summary: SummaryData = {
      导出时间: new Date().toLocaleString('zh-CN'),
      记录总数: 0,
      总价值: 0
    }

    // 计算统计信息
    if (typeof data === 'object' && !Array.isArray(data)) {
      for (const groupData of Object.values(data)) {
        const records = groupData as ExportDataItem[]
        summary['记录总数'] += records.length
        summary['总价值'] += records.reduce((sum, item) => sum + (Number(item['当前价值']) || 0), 0)
      }
    } else {
      const records = data as ExportDataItem[]
      summary['记录总数'] = records.length
      summary['总价值'] = records.reduce((sum, item) => sum + (Number(item['当前价值']) || 0), 0)
    }

    // 详细统计
    if (level === 'detailed') {
      summary['按状态统计'] = this.getStatusStatistics(data)
      summary['按类型统计'] = this.getTypeStatistics(data)
      summary['按部门统计'] = this.getDepartmentStatistics(data)
    }

    return [summary]
  }

  // 获取状态统计
  private getStatusStatistics(
    data: ExportDataItem[] | Record<string, ExportDataItem[]>
  ): Record<string, number> {
    const stats: Record<string, number> = {}
    const records = this.flattenData(data)

    records.forEach(item => {
      const status = String(item['资产状态'] || '未知')
      stats[status] = (stats[status] || 0) + 1
    })

    return stats
  }

  // 获取类型统计
  private getTypeStatistics(
    data: ExportDataItem[] | Record<string, ExportDataItem[]>
  ): Record<string, number> {
    const stats: Record<string, number> = {}
    const records = this.flattenData(data)

    records.forEach(item => {
      const type = String(item['资产类型'] || '未知')
      stats[type] = (stats[type] || 0) + 1
    })

    return stats
  }

  // 获取部门统计
  private getDepartmentStatistics(
    data: ExportDataItem[] | Record<string, ExportDataItem[]>
  ): Record<string, number> {
    const stats: Record<string, number> = {}
    const records = this.flattenData(data)

    records.forEach(item => {
      const dept = String(item['责任部门'] || '未知')
      stats[dept] = (stats[dept] || 0) + 1
    })

    return stats
  }

  // 扁平化数据
  private flattenData(data: ExportDataItem[] | Record<string, ExportDataItem[]>): ExportDataItem[] {
    if (Array.isArray(data)) {
      return data
    } else if (typeof data === 'object') {
      return Object.values(data).flat() as ExportDataItem[]
    }
    return []
  }

  // 获取资产类型标签
  private getAssetTypeLabel(type: string): string {
    const typeConfig = this.assetTypes.find(t => t.value === type)
    return typeConfig ? typeConfig.label : type
  }

  // 生成文件名
  private generateFilename(prefix: string, extension: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19)
    return `${prefix}_${timestamp}.${extension}`
  }

  // 获取资产类型列表
  getAssetTypes() {
    return this.assetTypes
  }

  // 获取状态标签
  getStatusLabels() {
    return this.statusLabels
  }

  // 添加资产记录
  addAssetRecord(asset: AssetRecord) {
    this.assetRecords.set(asset.id, asset)
  }

  // 添加回收清单
  addRecoveryChecklist(checklist: AssetRecoveryChecklist) {
    this.recoveryChecklists.set(checklist.checklistId, checklist)
  }

  // 获取资产统计
  getAssetStatistics() {
    const assets = Array.from(this.assetRecords.values())

    return {
      total: assets.length,
      byStatus: Object.entries(this.statusLabels).map(([key, label]) => ({
        status: key,
        label,
        count: assets.filter(a => a.status === key).length
      })),
      byType: this.assetTypes.map(type => ({
        type: type.value,
        label: type.label,
        count: assets.filter(a => a.assetType === type.value).length
      })),
      totalValue: assets.reduce((sum, a) => sum + a.currentValue, 0),
      avgValue:
        assets.length > 0 ? assets.reduce((sum, a) => sum + a.currentValue, 0) / assets.length : 0
    }
  }
}

// 全局实例
export const assetExporter = new AssetExport()

// 便捷函数
export async function exportAssets(
  filter?: AssetExportFilter,
  options?: Partial<ExportOptions>
): Promise<ExportResult> {
  return assetExporter.exportAssetList(filter, {
    format: 'excel',
    includeImages: false,
    includeHistory: false,
    summaryLevel: 'basic',
    ...options
  })
}

export async function exportAssetRecovery(
  employeeId?: string,
  departmentId?: string,
  options?: Partial<ExportOptions>
): Promise<ExportResult> {
  return assetExporter.exportRecoveryChecklist(employeeId, departmentId, {
    format: 'excel',
    includeImages: true,
    includeHistory: false,
    summaryLevel: 'detailed',
    ...options
  })
}
