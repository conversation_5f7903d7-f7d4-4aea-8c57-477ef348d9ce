# 工具函数库

本目录包含项目中所有的工具函数，按功能域进行了模块化组织。

## 目录结构

```
utils/
├── core/           # 核心工具函数
│   ├── array.ts    # 数组操作
│   ├── object.ts   # 对象操作
│   ├── string.ts   # 字符串操作
│   ├── number.ts   # 数字操作
│   ├── date.ts     # 日期操作
│   └── tree.ts     # 树形结构操作
├── function/       # 函数工具
│   └── index.ts    # 防抖、节流、重试等
├── dom/            # DOM操作
│   └── index.ts    # DOM查询、事件、样式等
├── browser/        # 浏览器相关
│   └── index.ts    # 存储、剪贴板、网络状态等
├── validation/     # 验证工具
│   └── index.ts    # 各种数据验证
├── format/         # 格式化工具
│   └── index.ts    # 日期、数字、金额等格式化
├── cache/          # 缓存系统
│   ├── core/       # 缓存核心
│   ├── strategies/ # 缓存策略
│   └── decorators/ # 缓存装饰器
├── export/         # 导出服务
│   └── ...         # Excel、PDF等导出
└── index.ts        # 统一导出入口
```

## 使用方式

### 1. 导入单个函数

```typescript
import { deepClone, formatDate, debounce } from '@/utils'
```

### 2. 导入模块

```typescript
import { array, object, string } from '@/utils/core'
import * as validation from '@/utils/validation'
import * as dom from '@/utils/dom'
```

### 3. 具体功能导入

```typescript
// 只导入需要的数组工具
import { unique, groupBy, chunk } from '@/utils/core/array'

// 只导入需要的验证函数
import { isEmail, isPhone, isIdCard } from '@/utils/validation'
```

## 功能模块说明

### Core 核心工具

#### Array 数组工具

- `unique` - 数组去重
- `groupBy` - 数组分组
- `chunk` - 数组分块
- `flatten` - 数组扁平化
- `difference` - 数组差集
- `intersection` - 数组交集
- `union` - 数组并集
- `sum/average/max/min` - 数学计算
- `shuffle` - 数组洗牌
- `sortBy/multiSort` - 排序

#### Object 对象工具

- `deepClone` - 深拷贝
- `diff` - 对象差异比较
- `merge` - 深度合并
- `pick/omit` - 选择/排除属性
- `get/set/has` - 嵌套属性操作
- `isEmpty` - 空值判断
- `keysToSnakeCase/keysToCamelCase` - 键名转换

#### String 字符串工具

- `camelToSnake/snakeToCamel` - 命名风格转换
- `capitalize/uncapitalize` - 首字母大小写
- `uuid/uniqueId` - ID生成
- `truncate` - 文本截断
- `highlight` - 文本高亮
- `escapeHtml/unescapeHtml` - HTML转义
- `template` - 模板替换

#### Number 数字工具

- `format` - 数字格式化
- `formatBytes` - 文件大小格式化
- `formatCurrency` - 货币格式化
- `add/subtract/multiply/divide` - 精确计算
- `round/ceil/floor` - 取整
- `clamp` - 范围限制
- `percentage` - 百分比计算

#### Date 日期工具

- `format` - 日期格式化
- `formatRelative` - 相对时间
- `formatDuration` - 时长格式化
- `getDateRange` - 获取日期范围
- `add/subtract` - 日期计算
- `startOf/endOf` - 起止时间
- `isSame/isBetween` - 日期比较
- `getWorkdays` - 工作日计算

#### Tree 树形结构工具

- `fromList/toList` - 列表与树形转换
- `find/findAll/findPath` - 节点查找
- `filter/map/traverse` - 遍历操作
- `getDepth/getLeaves` - 结构分析
- `sort/move` - 节点操作

### Function 函数工具

- `debounce` - 防抖（支持立即执行、最大等待时间）
- `throttle` - 节流（支持首尾调用控制）
- `retry` - 重试（支持递增延迟、条件判断）
- `memoize` - 函数缓存
- `once` - 单次执行
- `delay` - 延迟执行
- `withTimeout` - 超时控制
- `promiseLimit` - 并发限制
- `pipe/compose` - 函数组合

### DOM 操作工具

- `on/off/delegate` - 事件处理
- `getStyle/setStyle` - 样式操作
- `addClass/removeClass/toggleClass` - 类名操作
- `getRect/getOffset` - 位置尺寸
- `isInViewport` - 可见性判断
- `scrollToElement` - 滚动定位
- `createElement` - 元素创建
- `query/queryAll` - 元素查询
- `observeResize/observeIntersection` - 观察器
- `fullscreen` - 全屏控制

### Browser 浏览器工具

- `storage` - localStorage封装（支持过期时间）
- `sessionStorage` - sessionStorage封装
- `cookie` - Cookie操作
- `clipboard` - 剪贴板操作
- `download` - 文件下载
- `getBrowserInfo` - 浏览器信息
- `getUrlParams/buildUrl` - URL处理
- `preventClose` - 阻止页面关闭
- `isSupported` - 特性检测
- `sendNotification` - 通知发送
- `getNetworkStatus` - 网络状态

### Validation 验证工具

- `isEmail/isPhone/isTel` - 联系方式验证
- `isIdCard` - 身份证验证
- `isUrl/isIP` - 网络地址验证
- `isBankCard` - 银行卡验证
- `isSocialCreditCode` - 社会信用代码验证
- `isPlateNumber` - 车牌号验证
- `isUsername/getPasswordStrength` - 账号密码验证
- `isInteger/isDecimal` - 数字验证
- `isChinese/isEnglish` - 语言验证
- `isBase64/isMD5/isUUID` - 格式验证

### Format 格式化工具

- `formatPhone` - 手机号格式化（隐藏中间4位）
- `formatBankCard` - 银行卡格式化
- `formatIdCard` - 身份证格式化
- `formatName` - 姓名格式化（隐私保护）
- `formatAmountChinese` - 金额中文大写
- `formatDuration` - 时长格式化
- `formatStorage` - 存储单位格式化
- `formatNumberShort` - 数字缩写（K/M/B）
- `formatRank` - 排名格式化（1st/2nd/3rd）
- `formatTemperature/formatDistance/formatSpeed` - 单位格式化
- `formatCoordinate` - 坐标格式化
- `formatJSON/formatXML` - 代码格式化

### Cache 缓存系统

详见 [缓存系统文档](./cache/README.md)

### Export 导出服务

详见 [导出服务文档](./export/README.md)

## 向后兼容

为了保证现有代码的兼容性，以下函数仍然从根目录导出：

```typescript
// 从 common.ts 和 enhanced-utils.ts 迁移的函数
import {
  deepClone, // 使用 core/object 中的版本
  generateId, // 使用 core/string.uniqueId
  chunk, // 使用 core/array 中的版本
  formatFileSize // 使用 format.formatBytes
  // ... 其他兼容导出
} from '@/utils'
```

## 最佳实践

1. **按需导入**：只导入需要的函数，避免打包无用代码
2. **模块导入**：当需要多个相关函数时，导入整个模块
3. **类型安全**：所有函数都有完整的TypeScript类型定义
4. **错误处理**：工具函数内部处理错误，返回合理的默认值
5. **性能优化**：计算密集型函数使用缓存和优化算法

## 迁移指南

如果你的代码使用了旧的工具函数：

```typescript
// 旧代码
import { deepClone } from '@/utils/common'
import { formatNumber } from '@/utils/enhanced-utils'

// 新代码（推荐）
import { deepClone } from '@/utils/core/object'
import { formatNumber } from '@/utils/format'

// 或者使用统一导出（兼容）
import { deepClone, formatNumber } from '@/utils'
```

## 扩展指南

添加新的工具函数时：

1. 确定函数所属的功能域
2. 在对应模块中添加函数实现
3. 添加完整的JSDoc注释和类型定义
4. 在模块的index文件中导出
5. 添加单元测试
6. 更新此文档

## 版本历史

- v2.0.0 - 工具函数模块化重构
- v1.x.x - 原始版本（common.ts + enhanced-utils.ts）
