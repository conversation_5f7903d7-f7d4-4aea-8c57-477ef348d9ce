/**
 * 批量操作框架
 * 提供通用的批量处理能力，支持大数据量的分批处理、进度跟踪、错误处理等
 */

import { ref, shallowRef } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'

// 批量操作选项
export interface BatchOperationOptions<T = unknown> {
  // 批次大小
  batchSize?: number
  // 并发数
  concurrency?: number
  // 是否在错误时继续
  continueOnError?: boolean
  // 重试次数
  retryTimes?: number
  // 重试延迟(ms)
  retryDelay?: number
  // 操作前的验证函数
  validate?: (items: T[]) => Promise<ValidationResult>
  // 数据预处理函数
  preprocess?: (item: T) => Promise<T>
  // 操作完成后的处理
  postprocess?: (results: BatchResult<T>) => Promise<void>
  // 进度回调
  onProgress?: (progress: BatchProgress) => void
  // 错误回调
  onError?: (error: BatchError<T>) => void
  // 成功回调
  onSuccess?: (result: BatchSuccessResult<T>) => void
  // 是否显示通知
  showNotification?: boolean
  // 操作名称（用于通知）
  operationName?: string
}

// 验证结果
export interface ValidationResult {
  valid: boolean
  errors?: Array<{
    item: unknown
    message: string
  }>
  warnings?: Array<{
    item: unknown
    message: string
  }>
}

// 批量操作进度
export interface BatchProgress {
  total: number
  processed: number
  succeeded: number
  failed: number
  skipped: number
  percentage: number
  currentBatch: number
  totalBatches: number
  estimatedTimeRemaining: number
  averageTimePerItem: number
}

// 批量操作错误
export interface BatchError<T = unknown> {
  item: T
  index: number
  error: Error
  retryCount: number
}

// 批量操作成功结果
export interface BatchSuccessResult<T = unknown> {
  item: T
  index: number

  result: unknown
  duration: number
}

// 批量操作结果
export interface BatchResult<T = unknown> {
  total: number
  succeeded: number
  failed: number
  skipped: number
  duration: number
  successes: BatchSuccessResult<T>[]
  errors: BatchError<T>[]
  cancelled: boolean
}

// 批量操作状态
export interface BatchOperationState {
  running: boolean
  cancelled: boolean
  paused: boolean
  progress: BatchProgress
}

// 创建批量操作
export function useBatchOperation<T = unknown>(
  operation: (item: T, index: number) => Promise<unknown>,
  options: BatchOperationOptions<T> = {}
) {
  // 默认选项
  const defaultOptions: Required<BatchOperationOptions<T>> = {
    batchSize: 100,
    concurrency: 5,
    continueOnError: true,
    retryTimes: 3,
    retryDelay: 1000,
    validate: async () => ({ valid: true }),
    preprocess: async _item => item,
    postprocess: async () => {},
    onProgress: () => {},
    onError: () => {},
    onSuccess: () => {},
    showNotification: true,
    operationName: '批量操作'
  }

  const finalOptions = { ...defaultOptions, ...options }

  // 状态
  const state = ref<BatchOperationState>({
    running: false,
    cancelled: false,
    paused: false,
    progress: {
      total: 0,
      processed: 0,
      succeeded: 0,
      failed: 0,
      skipped: 0,
      percentage: 0,
      currentBatch: 0,
      totalBatches: 0,
      estimatedTimeRemaining: 0,
      averageTimePerItem: 0
    }
  })

  // 结果
  const result = shallowRef<BatchResult<T> | null>(null)

  // 时间跟踪
  let startTime = 0
  let itemStartTimes: number[] = []

  // 取消标记
  let cancelToken = false
  let pauseToken = false

  // 执行单个项目
  const executeItem = async (
    item: T,
    index: number,
    retryCount = 0
  ): Promise<BatchSuccessResult<T> | BatchError<T>> => {
    const itemStartTime = Date.now()

    try {
      // 预处理
      const processedItem = await finalOptions.preprocess(item)

      // 执行操作
      const operationResult = await operation(processedItem, index)

      const duration = Date.now() - itemStartTime
      itemStartTimes.push(duration)

      const successResult: BatchSuccessResult<T> = {
        item,
        index,
        result: operationResult,
        duration
      }

      finalOptions.onSuccess(successResult)
      return successResult
    } catch (error: unknown) {
      const batchError: BatchError<T> = {
        item,
        index,
        error: error instanceof Error ? error : new Error(String(error)),
        retryCount
      }

      // 重试逻辑
      if (retryCount < finalOptions.retryTimes) {
        await new Promise(resolve => setTimeout(resolve, finalOptions.retryDelay))
        return executeItem(item, index, retryCount + 1)
      }

      finalOptions.onError(batchError)
      return batchError
    }
  }

  // 执行批次
  const executeBatch = async (
    items: T[],
    startIndex: number
  ): Promise<Array<BatchSuccessResult<T> | BatchError<T>>> => {
    const promises: Promise<BatchSuccessResult<T> | BatchError<T>>[] = []
    const executing: Promise<void>[] = []

    for (let i = 0; i < items.length; i++) {
      // 检查取消和暂停
      if (cancelToken) break
      while (pauseToken && !cancelToken) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      const index = startIndex + i
      const item = items[i]

      const promise = executeItem(item, index).then(() => {
        // 更新进度
        state.value.progress.processed++

        if ('error' in result.value) {
          state.value.progress.failed++
        } else {
          state.value.progress.succeeded++
        }

        updateProgress()
        return result
      })

      promises.push(promise)

      // 并发控制
      if (finalOptions.concurrency > 0) {
        const executePromise = promise.then(() => {
          executing.splice(executing.indexOf(executePromise), 1)
        })
        executing.push(executePromise)

        if (executing.length >= finalOptions.concurrency) {
          await Promise.race(executing)
        }
      }
    }

    // 等待所有操作完成
    return Promise.all(promises)
  }

  // 更新进度
  const updateProgress = () => {
    const progress = state.value.progress
    progress.percentage = Math.round((progress.processed / progress.total) * 100)

    // 计算平均时间和剩余时间
    if (itemStartTimes.length > 0) {
      progress.averageTimePerItem =
        itemStartTimes.reduce((a, b) => a + b, 0) / itemStartTimes.length
      const remainingItems = progress.total - progress.processed
      progress.estimatedTimeRemaining = Math.round(
        (remainingItems * progress.averageTimePerItem) / 1000
      ) // 秒
    }

    finalOptions.onProgress(progress)
  }

  // 执行批量操作
  const execute = async (items: T[]): Promise<BatchResult<T>> => {
    if (state.value.running) {
      throw new Error('批量操作正在进行中')
    }

    // 重置状态
    state.value.running = true
    state.value.cancelled = false
    state.value.paused = false
    cancelToken = false
    pauseToken = false
    startTime = Date.now()
    itemStartTimes = []

    // 初始化进度
    state.value.progress = {
      total: items.length,
      processed: 0,
      succeeded: 0,
      failed: 0,
      skipped: 0,
      percentage: 0,
      currentBatch: 0,
      totalBatches: Math.ceil(items.length / finalOptions.batchSize),
      estimatedTimeRemaining: 0,
      averageTimePerItem: 0
    }

    // 初始化结果
    const batchResult: BatchResult<T> = {
      total: items.length,
      succeeded: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      successes: [],
      errors: [],
      cancelled: false
    }

    try {
      // 验证
      const validation = await finalOptions.validate(items)
      if (!validation.valid) {
        if (validation.errors && validation.errors.length > 0) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          throw new Error(`验证失败: ${errorMessage}`)
        }
      }

      // 显示警告
      if (validation.warnings && validation.warnings.length > 0 && finalOptions.showNotification) {
        ElNotification({
          title: '批量操作警告',
          message: `发现 ${validation.warnings.length} 个警告`,
          type: 'warning'
        })
      }

      // 分批处理
      for (let i = 0; i < items.length; i += finalOptions.batchSize) {
        if (cancelToken) {
          batchResult.cancelled = true
          break
        }

        state.value.progress.currentBatch++
        const batch = items.slice(i, i + finalOptions.batchSize)
        const results = await executeBatch(batch, i)

        // 收集结果
        results.forEach(r => {
          if ('error' in r) {
            batchResult.errors.push(r)
            batchResult.failed++

            if (!finalOptions.continueOnError) {
              throw r.error
            }
          } else {
            batchResult.successes.push(r)
            batchResult.succeeded++
          }
        })
      }

      // 计算持续时间
      batchResult.duration = (Date.now() - startTime) / 1000

      // 后处理
      await finalOptions.postprocess(batchResult)

      // 显示通知
      if (finalOptions.showNotification) {
        if (batchResult.failed === 0) {
          ElNotification({
            title: `${finalOptions.operationName}完成`,
            message: `成功处理 ${batchResult.succeeded} 条记录`,
            type: 'success'
          })
        } else {
          ElNotification({
            title: `${finalOptions.operationName}完成`,
            message: `成功: ${batchResult.succeeded} 条, 失败: ${batchResult.failed} 条`,
            type: 'warning'
          })
        }
      }

      result.value = batchResult
      return batchResult
    } catch (error: unknown) {
      const errorMessage = error.message || '批量操作失败'

      if (finalOptions.showNotification) {
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      state.value.running = false
    }
  }

  // 取消操作
  const cancel = () => {
    if (state.value.running) {
      cancelToken = true
      state.value.cancelled = true

      if (finalOptions.showNotification) {
        ElMessage.warning('批量操作已取消')
      }
    }
  }

  // 暂停操作
  const pause = () => {
    if (state.value.running && !state.value.paused) {
      pauseToken = true
      state.value.paused = true
    }
  }

  // 恢复操作
  const resume = () => {
    if (state.value.running && state.value.paused) {
      pauseToken = false
      state.value.paused = false
    }
  }

  // 获取错误报告
  const getErrorReport = () => {
    if (!result.value) return null

    return result.value.errors.map(e => ({
      index: e.index + 1, // 转换为1-based索引
      item: e.item,
      error: e.error.message,
      retryCount: e.retryCount
    }))
  }

  return {
    state,
    result,
    execute,
    cancel,
    pause,
    resume,
    getErrorReport
  }
}

// 预定义的批量操作
export const batchOperations = {
  // 批量删除
  delete: <T extends { id: string | number }>(
    deleteApi: (id: string | number) => Promise<void>,
    options?: BatchOperationOptions<T>
  ) => {
    return useBatchOperation<T>(
      async _item => {
        await deleteApi(item.id)
      },
      {
        operationName: '批量删除',
        ...options
      }
    )
  },

  // 批量更新
  update: <T extends { id: string | number }>(
    updateApi: (id: string | number, data: Partial<T>) => Promise<void>,
    updateData: Partial<T> | ((item: T) => Partial<T>),
    options?: BatchOperationOptions<T>
  ) => {
    return useBatchOperation<T>(
      async _item => {
        const data = typeof updateData === 'function' ? updateData(item) : updateData
        await updateApi(item.id, data)
      },
      {
        operationName: '批量更新',
        ...options
      }
    )
  },

  // 批量导入
  import: <T>(importApi: (data: T[]) => Promise<unknown>, options?: BatchOperationOptions<T>) => {
    return useBatchOperation<T>(
      async _item => {
        // 单条导入逻辑
        await importApi([item])
      },
      {
        operationName: '批量导入',
        batchSize: 500,
        ...options
      }
    )
  },

  // 批量审核
  review: <T extends { id: string | number }>(
    reviewApi: (id: string | number, approved: boolean, comment?: string) => Promise<void>,
    approved: boolean,
    comment?: string,
    options?: BatchOperationOptions<T>
  ) => {
    return useBatchOperation<T>(
      async _item => {
        await reviewApi(item.id, approved, comment)
      },
      {
        operationName: approved ? '批量通过' : '批量驳回',
        ...options
      }
    )
  }
}

// 批量操作管理器
export class BatchOperationManager {
  private operations: Map<string, ReturnType<typeof useBatchOperation>> = new Map()

  // 注册操作
  register(name: string, operation: ReturnType<typeof useBatchOperation>) {
    this.operations.set(name, operation)
  }

  // 获取操作
  get(name: string) {
    return this.operations.get(name)
  }

  // 取消所有操作
  cancelAll() {
    this.operations.forEach(op => op.cancel())
  }

  // 获取运行中的操作
  getRunning() {
    return Array.from(this.operations.entries())
      .filter(([_, op]) => op.state.value.running)
      .map(([name, op]) => ({ name, operation: op }))
  }
}

// 全局批量操作管理器实例
export const batchOperationManager = new BatchOperationManager()
