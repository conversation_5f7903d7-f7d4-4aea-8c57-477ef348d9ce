/**
 * 移动端触摸手势管理器
 * 支持滑动、缩放、旋转、长按等手势
 */

export interface TouchPoint {
  x: number
  y: number
  id: number
  timestamp: number
}

export interface GestureEvent {
  type: string
  target: HTMLElement
  touches: TouchPoint[]
  changedTouches: TouchPoint[]
  center: { x: number; y: number }
  deltaX: number
  deltaY: number
  deltaTime: number
  distance?: number
  angle?: number
  scale?: number
  rotation?: number
  velocity?: { x: number; y: number }
  direction?: 'left' | 'right' | 'up' | 'down'
}

export interface GestureOptions {
  // 触发阈值
  threshold?: {
    tap?: number // 点击时间阈值(ms)
    swipe?: number // 滑动距离阈值(px)
    pinch?: number // 缩放阈值
    rotate?: number // 旋转角度阈值(度)
    press?: number // 长按时间阈值(ms)
  }

  // 启用的手势
  enable?: {
    tap?: boolean
    doubleTap?: boolean
    swipe?: boolean
    pinch?: boolean
    rotate?: boolean
    press?: boolean
    pan?: boolean
  }

  // 其他选项
  preventDefault?: boolean
  stopPropagation?: boolean
}

export class TouchGestures {
  private element: HTMLElement
  private options: Required<GestureOptions>
  private handlers: Map<string, Set<(event: GestureEvent) => void>> = new Map()

  // 触摸状态
  private touches: Map<number, TouchPoint> = new Map()
  private startTouches: Map<number, TouchPoint> = new Map()
  private lastTapTime = 0
  private lastTapTarget: EventTarget | null = null
  private isPressing = false
  private pressTimer: NodeJS.Timeout | null = null

  // 手势状态
  private gestureState = {
    isGesturing: false,
    startDistance: 0,
    startAngle: 0,
    lastScale: 1,
    lastRotation: 0,
    lastCenter: { x: 0, y: 0 }
  }

  constructor(element: HTMLElement, options: GestureOptions = {}) {
    this.element = element
    this.options = {
      threshold: {
        tap: 250,
        swipe: 30,
        pinch: 0.1,
        rotate: 15,
        press: 500,
        ...options.threshold
      },
      enable: {
        tap: true,
        doubleTap: true,
        swipe: true,
        pinch: true,
        rotate: true,
        press: true,
        pan: true,
        ...options.enable
      },
      preventDefault: true,
      stopPropagation: false,
      ...options
    }

    this.bindEvents()
  }

  /**
   * 绑定事件监听
   */
  private bindEvents(): void {
    // 触摸事件
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), {
      passive: false
    })
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false })
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false })
    this.element.addEventListener('touchcancel', this.handleTouchCancel.bind(this), {
      passive: false
    })

    // 鼠标事件（用于调试）
    if (import.meta.env.DEV) {
      this.element.addEventListener('mousedown', this.handleMouseDown.bind(this))
      this.element.addEventListener('mousemove', this.handleMouseMove.bind(this))
      this.element.addEventListener('mouseup', this.handleMouseUp.bind(this))
    }
  }

  /**
   * 处理触摸开始
   */
  private handleTouchStart(event: TouchEvent): void {
    if (this.options.preventDefault) {
      event.preventDefault()
    }

    if (this.options.stopPropagation) {
      event.stopPropagation()
    }

    // 记录触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i]
      const point: TouchPoint = {
        x: touch.clientX,
        y: touch.clientY,
        id: touch.identifier,
        timestamp: Date.now()
      }

      this.touches.set(touch.identifier, point)
      this.startTouches.set(touch.identifier, { ...point })
    }

    // 检测长按
    if (this.options.enable.press && this.touches.size === 1) {
      this.isPressing = true
      this.pressTimer = setTimeout(() => {
        if (this.isPressing && this.touches.size === 1) {
          this.emit('press', this.createGestureEvent('press', event))
        }
      }, this.options.threshold.press)
    }

    // 多点触摸手势初始化
    if (this.touches.size === 2) {
      const points = Array.from(this.touches.values())
      this.gestureState.isGesturing = true
      this.gestureState.startDistance = this.getDistance(points[0], points[1])
      this.gestureState.startAngle = this.getAngle(points[0], points[1])
      this.gestureState.lastScale = 1
      this.gestureState.lastRotation = 0
      this.gestureState.lastCenter = this.getCenter(points)
    }

    this.emit('touchstart', this.createGestureEvent('touchstart', event))
  }

  /**
   * 处理触摸移动
   */
  private handleTouchMove(event: TouchEvent): void {
    if (this.options.preventDefault) {
      event.preventDefault()
    }

    if (this.options.stopPropagation) {
      event.stopPropagation()
    }

    // 取消长按
    if (this.isPressing) {
      this.isPressing = false
      if (this.pressTimer) {
        clearTimeout(this.pressTimer)
        this.pressTimer = null
      }
    }

    // 更新触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i]
      const point = this.touches.get(touch.identifier)

      if (point) {
        point.x = touch.clientX
        point.y = touch.clientY
        point.timestamp = Date.now()
      }
    }

    // 单点移动（Pan）
    if (this.options.enable.pan && this.touches.size === 1) {
      const touch = Array.from(this.touches.values())[0]
      const startTouch = this.startTouches.get(touch.id)

      if (startTouch) {
        const gestureEvent = this.createGestureEvent('pan', event)
        gestureEvent.deltaX = touch.x - startTouch.x
        gestureEvent.deltaY = touch.y - startTouch.y
        gestureEvent.velocity = this.getVelocity(startTouch, touch)

        this.emit('pan', gestureEvent)
      }
    }

    // 多点手势（Pinch & Rotate）
    if (this.gestureState.isGesturing && this.touches.size === 2) {
      const points = Array.from(this.touches.values())
      const distance = this.getDistance(points[0], points[1])
      const angle = this.getAngle(points[0], points[1])
      const center = this.getCenter(points)

      // 缩放手势
      if (this.options.enable.pinch) {
        const scale = distance / this.gestureState.startDistance
        const deltaScale = scale - this.gestureState.lastScale

        if (Math.abs(deltaScale) > this.options.threshold.pinch) {
          const gestureEvent = this.createGestureEvent('pinch', event)
          gestureEvent.scale = scale
          gestureEvent.center = center

          this.emit('pinch', gestureEvent)
          this.gestureState.lastScale = scale
        }
      }

      // 旋转手势
      if (this.options.enable.rotate) {
        const rotation = angle - this.gestureState.startAngle
        const deltaRotation = rotation - this.gestureState.lastRotation

        if (Math.abs(deltaRotation) > this.options.threshold.rotate) {
          const gestureEvent = this.createGestureEvent('rotate', event)
          gestureEvent.rotation = rotation
          gestureEvent.center = center

          this.emit('rotate', gestureEvent)
          this.gestureState.lastRotation = rotation
        }
      }

      this.gestureState.lastCenter = center
    }

    this.emit('touchmove', this.createGestureEvent('touchmove', event))
  }

  /**
   * 处理触摸结束
   */
  private handleTouchEnd(event: TouchEvent): void {
    if (this.options.preventDefault) {
      event.preventDefault()
    }

    if (this.options.stopPropagation) {
      event.stopPropagation()
    }

    // 取消长按
    if (this.isPressing) {
      this.isPressing = false
      if (this.pressTimer) {
        clearTimeout(this.pressTimer)
        this.pressTimer = null
      }
    }

    // 处理结束的触摸点
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i]
      const endPoint = this.touches.get(touch.identifier)
      const startPoint = this.startTouches.get(touch.identifier)

      if (endPoint && startPoint) {
        const deltaTime = endPoint.timestamp - startPoint.timestamp
        const deltaX = endPoint.x - startPoint.x
        const deltaY = endPoint.y - startPoint.y
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

        // 检测点击
        if (this.options.enable.tap && deltaTime < this.options.threshold.tap && distance < 10) {
          // 双击检测
          if (this.options.enable.doubleTap) {
            const now = Date.now()
            if (this.lastTapTarget === event.target && now - this.lastTapTime < 300) {
              this.emit('doubletap', this.createGestureEvent('doubletap', event))
              this.lastTapTime = 0
              this.lastTapTarget = null
            } else {
              this.lastTapTime = now
              this.lastTapTarget = event.target

              setTimeout(() => {
                if (this.lastTapTime === now) {
                  this.emit('tap', this.createGestureEvent('tap', event))
                }
              }, 300)
            }
          } else {
            this.emit('tap', this.createGestureEvent('tap', event))
          }
        }

        // 检测滑动
        if (this.options.enable.swipe && distance > this.options.threshold.swipe) {
          const velocity = this.getVelocity(startPoint, endPoint)
          const direction = this.getDirection(deltaX, deltaY)

          const gestureEvent = this.createGestureEvent('swipe', event)
          gestureEvent.deltaX = deltaX
          gestureEvent.deltaY = deltaY
          gestureEvent.velocity = velocity
          gestureEvent.direction = direction

          this.emit('swipe', gestureEvent)
          this.emit(`swipe${direction}`, gestureEvent)
        }
      }

      this.touches.delete(touch.identifier)
      this.startTouches.delete(touch.identifier)
    }

    // 结束多点手势
    if (this.touches.size < 2) {
      this.gestureState.isGesturing = false
    }

    this.emit('touchend', this.createGestureEvent('touchend', event))
  }

  /**
   * 处理触摸取消
   */
  private handleTouchCancel(event: TouchEvent): void {
    this.handleTouchEnd(event)
  }

  /**
   * 鼠标事件处理（用于调试）
   */
  private handleMouseDown(event: MouseEvent): void {
    const fakeTouch = {
      identifier: 0,
      clientX: event.clientX,
      clientY: event.clientY,
      target: event.target
    }

    const fakeTouchEvent = {
      ...event,
      touches: [fakeTouch],
      changedTouches: [fakeTouch],
      targetTouches: [fakeTouch]
    } as unknown

    this.handleTouchStart(fakeTouchEvent)
  }

  private handleMouseMove(event: MouseEvent): void {
    if (this.touches.size === 0) return

    const fakeTouch = {
      identifier: 0,
      clientX: event.clientX,
      clientY: event.clientY,
      target: event.target
    }

    const fakeTouchEvent = {
      ...event,
      touches: [fakeTouch],
      changedTouches: [fakeTouch],
      targetTouches: [fakeTouch]
    } as unknown

    this.handleTouchMove(fakeTouchEvent)
  }

  private handleMouseUp(event: MouseEvent): void {
    if (this.touches.size === 0) return

    const fakeTouch = {
      identifier: 0,
      clientX: event.clientX,
      clientY: event.clientY,
      target: event.target
    }

    const fakeTouchEvent = {
      ...event,
      touches: [],
      changedTouches: [fakeTouch],
      targetTouches: []
    } as unknown

    this.handleTouchEnd(fakeTouchEvent)
  }

  /**
   * 创建手势事件
   */
  private createGestureEvent(type: string, originalEvent: TouchEvent | MouseEvent): GestureEvent {
    const touches = Array.from(this.touches.values())
    const changedTouches =
      'changedTouches' in originalEvent
        ? Array.from(originalEvent.changedTouches).map(t => ({
            x: t.clientX,
            y: t.clientY,
            id: t.identifier,
            timestamp: Date.now()
          }))
        : []

    const center = this.getCenter(touches)

    return {
      type,
      target: originalEvent.target as HTMLElement,
      touches,
      changedTouches,
      center,
      deltaX: 0,
      deltaY: 0,
      deltaTime: 0
    }
  }

  /**
   * 计算两点距离
   */
  private getDistance(p1: TouchPoint, p2: TouchPoint): number {
    const dx = p2.x - p1.x
    const dy = p2.y - p1.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 计算两点角度
   */
  private getAngle(p1: TouchPoint, p2: TouchPoint): number {
    return (Math.atan2(p2.y - p1.y, p2.x - p1.x) * 180) / Math.PI
  }

  /**
   * 计算中心点
   */
  private getCenter(points: TouchPoint[]): { x: number; y: number } {
    if (points.length === 0) {
      return { x: 0, y: 0 }
    }

    const sum = points.reduce(
      (acc, p) => ({
        x: acc.x + p.x,
        y: acc.y + p.y
      }),
      { x: 0, y: 0 }
    )

    return {
      x: sum.x / points.length,
      y: sum.y / points.length
    }
  }

  /**
   * 计算速度
   */
  private getVelocity(start: TouchPoint, end: TouchPoint): { x: number; y: number } {
    const deltaTime = (end.timestamp - start.timestamp) / 1000

    if (deltaTime === 0) {
      return { x: 0, y: 0 }
    }

    return {
      x: (end.x - start.x) / deltaTime,
      y: (end.y - start.y) / deltaTime
    }
  }

  /**
   * 获取滑动方向
   */
  private getDirection(deltaX: number, deltaY: number): 'left' | 'right' | 'up' | 'down' {
    const absX = Math.abs(deltaX)
    const absY = Math.abs(deltaY)

    if (absX > absY) {
      return deltaX > 0 ? 'right' : 'left'
    } else {
      return deltaY > 0 ? 'down' : 'up'
    }
  }

  /**
   * 注册事件监听
   */
  on(event: string, handler: (event: GestureEvent) => void): void {
    if (!this.handlers.has(event)) {
      this.handlers.set(event, new Set())
    }

    this.handlers.get(event)!.add(handler)
  }

  /**
   * 注销事件监听
   */
  off(event: string, handler?: (event: GestureEvent) => void): void {
    if (!handler) {
      this.handlers.delete(event)
    } else {
      const handlers = this.handlers.get(event)
      if (handlers) {
        handlers.delete(handler)
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data: GestureEvent): void {
    const handlers = this.handlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (__error) {
          }
      })
    }
  }

  /**
   * 销毁
   */
  destroy(): void {
    // 清理定时器
    if (this.pressTimer) {
      clearTimeout(this.pressTimer)
    }

    // 移除事件监听
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this))
    this.element.removeEventListener('touchmove', this.handleTouchMove.bind(this))
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this))
    this.element.removeEventListener('touchcancel', this.handleTouchCancel.bind(this))

    if (import.meta.env.DEV) {
      this.element.removeEventListener('mousedown', this.handleMouseDown.bind(this))
      this.element.removeEventListener('mousemove', this.handleMouseMove.bind(this))
      this.element.removeEventListener('mouseup', this.handleMouseUp.bind(this))
    }

    // 清理数据
    this.handlers.clear()
    this.touches.clear()
    this.startTouches.clear()
  }
}

// 导出Vue组合式API
export function useTouchGestures(options?: GestureOptions) {
  let gestures: TouchGestures | null = null

  const init = (element: HTMLElement) => {
    if (gestures) {
      gestures.destroy()
    }

    gestures = new TouchGestures(element, options)
    return gestures
  }

  const destroy = () => {
    if (gestures) {
      gestures.destroy()
      gestures = null
    }
  }

  return {
    init,
    destroy,
    get instance() {
      return gestures
    }
  }
}

// 导出便捷方法
export function enableGestures(element: HTMLElement, options?: GestureOptions): TouchGestures {
  return new TouchGestures(element, options)
}
