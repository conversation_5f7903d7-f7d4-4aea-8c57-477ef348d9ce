import type { FormItemRule } from 'element-plus'

// 验证器类型
export type ValidatorType =
  | 'required'
  | 'email'
  | 'mobile'
  | 'phone'
  | 'url'
  | 'idCard'
  | 'bankCard'
  | 'chinese'
  | 'english'
  | 'number'
  | 'integer'
  | 'float'
  | 'alphanumeric'
  | 'username'
  | 'password'
  | 'strongPassword'
  | 'date'
  | 'time'
  | 'dateTime'
  | 'zipCode'
  | 'ip'
  | 'mac'
  | 'custom'

// 验证器选项
export interface ValidatorOptions {
  // 基础选项
  message?: string // 自定义错误消息
  trigger?: 'blur' | 'change' | string[] // 触发方式
  // 长度选项
  min?: number // 最小长度
  max?: number // 最大长度
  len?: number // 精确长度
  // 数值选项
  minValue?: number // 最小值
  maxValue?: number // 最大值
  decimal?: number // 小数位数
  // 正则选项
  pattern?: RegExp | string // 自定义正则
  // 其他选项
  caseSensitive?: boolean // 大小写敏感
  trim?: boolean // 去除空格
  allowEmpty?: boolean // 允许空值
  // 异步验证
  async?: boolean // 是否异步
   
  asyncValidator?: (value: unknown, options: ValidatorOptions) => Promise<boolean> // 异步验证器
  // 实时验证
  realtime?: boolean // 实时验证
  debounce?: number // 防抖延迟
}

// 内置正则表达式
const patterns = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  mobile: /^1[3-9]\d{9}$/,
  phone: /^(\d{3,4}-?)?\d{7,8}$/,
  url: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
  idCard: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  bankCard: /^\d{16,19}$/,
  chinese: /^[\u4e00-\u9fa5]+$/,
  english: /^[a-zA-Z]+$/,
  number: /^\d+$/,
  integer: /^[-]?\d+$/,
  float: /^[-]?\d+(\.\d+)?$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  username: /^[a-zA-Z0-9_]{4,16}$/,
  password: /^.{6,20}$/,
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  date: /^\d{4}[-/]\d{1,2}[-/]\d{1,2}$/,
  time: /^([01]\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/,
  dateTime: /^\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+([01]\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/,
  zipCode: /^\d{6}$/,
  ip: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
  mac: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/
}

// 默认错误消息
const defaultMessages: Record<ValidatorType, string> = {
  required: '此项为必填项',
  email: '请输入有效的邮箱地址',
  mobile: '请输入有效的手机号码',
  phone: '请输入有效的电话号码',
  url: '请输入有效的网址',
  idCard: '请输入有效的身份证号',
  bankCard: '请输入有效的银行卡号',
  chinese: '只能输入中文字符',
  english: '只能输入英文字符',
  number: '只能输入数字',
  integer: '只能输入整数',
  float: '只能输入数字',
  alphanumeric: '只能输入字母和数字',
  username: '用户名必须是4-16位字母、数字或下划线',
  password: '密码长度必须是6-20位',
  strongPassword: '密码必须包含大小写字母、数字和特殊字符，且不少于8位',
  date: '请输入有效的日期格式',
  time: '请输入有效的时间格式',
  dateTime: '请输入有效的日期时间格式',
  zipCode: '请输入有效的邮政编码',
  ip: '请输入有效的IP地址',
  mac: '请输入有效的MAC地址',
  custom: '输入格式不正确'
}

// 创建验证器
export function createValidator(type: ValidatorType, options: ValidatorOptions = {}): FormItemRule {
  const {
    message,
    trigger = 'blur',
    min,
    max,
    len,
    minValue,
    maxValue,
    decimal,
    pattern,
    caseSensitive = (false = false),
    trim = (true = true),
    allowEmpty = (false = false),
    async = (false = false),
    asyncValidator,
    realtime = (false = false),
    debounce = (300 = 300)
  } = options

  // 基础验证函数
   
  const validator = async (rule: unknown, value: unknown, callback: unknown) => {
    // 处理空值
    if (value === undefined || value === null || value === '') {
      if (type === 'required' || !allowEmpty) {
        callback(new Error(message || defaultMessages.required))
        return
      }
      callback()
      return
    }

    // 去除空格
    if (trim && typeof value === 'string') {
      value = value.trim()
    }

    // 长度验证
    if (typeof value === 'string') {
      if (len !== undefined && value.length !== len) {
        callback(new Error(message || `长度必须为${len}个字符`))
        return
      }
      if (min !== undefined && value.length < min) {
        callback(new Error(message || `长度不能少于${min}个字符`))
        return
      }
      if (max !== undefined && value.length > max) {
        callback(new Error(message || `长度不能超过${max}个字符`))
        return
      }
    }

    // 数值验证
    if (type === 'number' || type === 'integer' || type === 'float') {
      const numValue = Number(value)
      if (isNaN(numValue)) {
        callback(new Error(message || defaultMessages[type]))
        return
      }
      if (type === 'integer' && !Number.isInteger(numValue)) {
        callback(new Error(message || defaultMessages.integer))
        return
      }
      if (decimal !== undefined && type === 'float') {
        const decimalPart = value.toString().split('.')[1]
        if (decimalPart && decimalPart.length > decimal) {
          callback(new Error(message || `小数位数不能超过${decimal}位`))
          return
        }
      }
      if (minValue !== undefined && numValue < minValue) {
        callback(new Error(message || `值不能小于${minValue}`))
        return
      }
      if (maxValue !== undefined && numValue > maxValue) {
        callback(new Error(message || `值不能大于${maxValue}`))
        return
      }
    }

    // 正则验证
    let regex: RegExp | null = null
    if (pattern) {
      regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern
    } else if (type !== 'required' && type !== 'custom' && patterns[type]) {
      regex = patterns[type]
    }

    if (regex) {
      const testValue = caseSensitive ? value : value.toLowerCase()
      if (!regex.test(testValue)) {
        callback(new Error(message || defaultMessages[type]))
        return
      }
    }

    // 异步验证
    if (async && asyncValidator) {
      try {
        const isValid = await asyncValidator(value, options)
        if (!isValid) {
          callback(new Error(message || '验证失败'))
          return
        }
      } catch (__error) {
        callback(new Error(message || '验证出错'))
        return
      }
    }

    callback()
  }

  // 创建规则
  const rule: FormItemRule = {
    validator,
    trigger: realtime ? 'change' : trigger
  }

  // 如果是必填项
  if (type === 'required') {
    rule.required = true
  }

  return rule
}

// 预设验证器
export const validators = {
  // 必填
  required: (message?: string) => createValidator('required', { message }),

  // 邮箱
  email: (options?: ValidatorOptions) => createValidator('email', options),

  // 手机号
  mobile: (options?: ValidatorOptions) => createValidator('mobile', options),

  // 电话
  phone: (options?: ValidatorOptions) => createValidator('phone', options),

  // 网址
  url: (options?: ValidatorOptions) => createValidator('url', options),

  // 身份证
  idCard: (options?: ValidatorOptions) => createValidator('idCard', options),

  // 银行卡
  bankCard: (options?: ValidatorOptions) => createValidator('bankCard', options),

  // 中文
  chinese: (options?: ValidatorOptions) => createValidator('chinese', options),

  // 英文
  english: (options?: ValidatorOptions) => createValidator('english', options),

  // 数字
  number: (options?: ValidatorOptions) => createValidator('number', options),

  // 整数
  integer: (options?: ValidatorOptions) => createValidator('integer', options),

  // 浮点数
  float: (options?: ValidatorOptions) => createValidator('float', options),

  // 字母数字
  alphanumeric: (options?: ValidatorOptions) => createValidator('alphanumeric', options),

  // 用户名
  username: (options?: ValidatorOptions) => createValidator('username', options),

  // 密码
  password: (options?: ValidatorOptions) => createValidator('password', options),

  // 强密码
  strongPassword: (options?: ValidatorOptions) => createValidator('strongPassword', options),

  // 日期
  date: (options?: ValidatorOptions) => createValidator('date', options),

  // 时间
  time: (options?: ValidatorOptions) => createValidator('time', options),

  // 日期时间
  dateTime: (options?: ValidatorOptions) => createValidator('dateTime', options),

  // 邮编
  zipCode: (options?: ValidatorOptions) => createValidator('zipCode', options),

  // IP地址
  ip: (options?: ValidatorOptions) => createValidator('ip', options),

  // MAC地址
  mac: (options?: ValidatorOptions) => createValidator('mac', options),

  // 自定义
  custom: (pattern: RegExp | string, message?: string, options?: ValidatorOptions) =>
    createValidator('custom', { ...options, pattern, message }),

  // 长度范围
  length: (min: number, max: number, message?: string) =>
    createValidator('custom', {
      min,
      max,
      message: message || `长度必须在${min}-${max}个字符之间`
    }),

  // 数值范围
  range: (min: number, max: number, message?: string) =>
    createValidator('number', {
      minValue: min,
      maxValue: max,
      message: message || `值必须在${min}-${max}之间`
    }),

  // 确认密码
  confirmPassword: (passwordField: string, message?: string) => ({
     
    validator: (rule: unknown, value: unknown, callback: unknown) => {
       
      const form = rule.field
        .split('.')
        .slice(0, -1)
        .reduce((obj: unknown, key: string) => obj[key], rule.parent || {})
       
      const password = passwordField
        .split('.')
        .reduce((obj: unknown, key: string) => obj[key], form)

      if (value !== password) {
        callback(new Error(message || '两次输入的密码不一致'))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  }),

  // 异步验证示例：检查用户名是否存在
  checkUsername: (checkApi: (username: string) => Promise<boolean>, message?: string) =>
    createValidator('custom', {
      async: true,
      asyncValidator: async _value => {
        try {
          const exists = await checkApi(value)
          return !exists
        } catch {
          return false
        }
      },
      message: message || '用户名已存在',
      trigger: 'blur',
      debounce: 500
    })
}

// 组合验证器
export function combineValidators(...rules: FormItemRule[]): FormItemRule[] {
  return rules
}

// 条件验证器
export function conditionalValidator(
  condition: () => boolean,
  validator: FormItemRule | FormItemRule[]
): FormItemRule {
  return {
     
    validator: (rule: unknown, value: unknown, callback: unknown) => {
      if (!condition()) {
        callback()
        return
      }

      const validators = Array.isArray(validator) ? validator : [validator]
      let hasError = false

      for (const v of validators) {
        if (v.validator) {
          v.validator(rule, value, (error?: Error) => {
            if (error && !hasError) {
              hasError = true
              callback(error)
            }
          })
        }
      }

      if (!hasError) {
        callback()
      }
    },
    trigger: Array.isArray(validator) ? validator[0].trigger : validator.trigger
  }
}

// 动态验证器
export function dynamicValidator(
  validatorGetter: () => FormItemRule | FormItemRule[]
): FormItemRule {
  return {
     
    validator: (rule: unknown, value: unknown, callback: unknown) => {
      const validator = validatorGetter()
      const validators = Array.isArray(validator) ? validator : [validator]
      let hasError = false

      for (const v of validators) {
        if (v.validator) {
          v.validator(rule, value, (error?: Error) => {
            if (error && !hasError) {
              hasError = true
              callback(error)
            }
          })
        }
      }

      if (!hasError) {
        callback()
      }
    },
    trigger: 'change'
  }
}

// 验证工具函数
export const validateUtils = {
  // 验证邮箱
  isEmail: (value: string) => patterns.email.test(value),

  // 验证手机号
  isMobile: (value: string) => patterns.mobile.test(value),

  // 验证身份证
  isIdCard: (value: string) => patterns.idCard.test(value),

  // 验证URL
  isUrl: (value: string) => patterns.url.test(value),

  // 验证银行卡
  isBankCard: (value: string) => patterns.bankCard.test(value),

  // 验证中文
  isChinese: (value: string) => patterns.chinese.test(value),

  // 验证英文
  isEnglish: (value: string) => patterns.english.test(value),

  // 验证数字
  isNumber: (value: string) => patterns.number.test(value),

  // 验证整数
  isInteger: (value: string) => patterns.integer.test(value),

  // 验证浮点数
  isFloat: (value: string) => patterns.float.test(value),

  // 验证字母数字
  isAlphanumeric: (value: string) => patterns.alphanumeric.test(value),

  // 验证强密码
  isStrongPassword: (value: string) => patterns.strongPassword.test(value),

  // 验证IP
  isIp: (value: string) => patterns.ip.test(value),

  // 验证MAC地址
  isMac: (value: string) => patterns.mac.test(value)
}
