/**
 * 批量数据处理器
 * 解决大数据量处理时的超时问题
 */

import { ElMessage } from 'element-plus'

interface BatchProcessOptions<T> {
  // 数据源
  data: T[]
  // 处理函数
  processor: (item: T, index: number) => Promise<unknown>
  // 批次大小
  batchSize?: number
  // 并发数
  concurrency?: number
  // 超时时间(毫秒)
  timeout?: number
  // 进度回调
  onProgress?: (progress: number, current: number, total: number) => void
  // 错误处理
  onError?: (error: Error, item: T, index: number) => void
  // 是否继续处理错误项
  continueOnError?: boolean
  // 重试次数
  retryCount?: number
  // 重试延迟(毫秒)
  retryDelay?: number
}

interface BatchResult<T> {
  success: number
  failed: number
  total: number
  results: Array<{
    index: number
    item: T
    result?: unknown
    error?: Error
  }>
  duration: number
}

export class BatchProcessor {
  /**
   * 执行批量处理
   */
  static async process<T>(options: BatchProcessOptions<T>): Promise<BatchResult<T>> {
    const {
      data,
      processor,
      batchSize = 100,
      concurrency = 5,
      timeout = 30000,
      onProgress,
      onError,
      continueOnError = true,
      retryCount = 2,
      retryDelay = 1000
    } = options

    const startTime = Date.now()
    const total = data.length
    let processed = 0
    let success = 0
    let failed = 0
    const results: BatchResult<T>['results'] = []

    // 创建任务队列
    const taskQueue = data.map((item, index) => ({ item, index }))

    // 处理单个任务
    const processTask = async (task: { item: T; index: number }) => {
      let lastError: Error | undefined

      // 重试逻辑
      for (let attempt = 0; attempt <= retryCount; attempt++) {
        try {
          // 使用Promise.race实现超时控制
          const result = await Promise.race([
            processor(task.item, task.index),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('处理超时')), timeout)
            )
          ])

          results.push({
            index: task.index,
            item: task.item,
            result
          })

          success++
          return

        } catch (error) {
          lastError = error as Error

          // 如果不是最后一次重试，等待后继续
          if (attempt < retryCount) {
            await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)))
            continue
          }
        }
      }

      // 所有重试都失败
      failed++
      results.push({
        index: task.index,
        item: task.item,
        error: lastError
      })

      if (onError) {
        onError(lastError!, task.item, task.index)
      }

      if (!continueOnError) {
        throw lastError
      }
    }

    // 批次处理
    for (let i = 0; i < taskQueue.length; i += batchSize) {
      const batch = taskQueue.slice(i, i + batchSize)

      // 并发处理当前批次
      const chunks = []
      for (let j = 0; j < batch.length; j += concurrency) {
        const chunk = batch.slice(j, j + concurrency)
        chunks.push(Promise.all(chunk.map(processTask)))
      }

      await Promise.all(chunks)

      // 更新进度
      processed += batch.length
      const progress = Math.round((processed / total) * 100)

      if (onProgress) {
        onProgress(progress, processed, total)
      }

      // 让出控制权，避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 0))
    }

    const duration = Date.now() - startTime

    return {
      success,
      failed,
      total,
      results,
      duration
    }
  }

  /**
   * 分片处理大数据
   */
  static async processInChunks<T, R>(
    data: T[],
    processor: (chunk: T[]) => Promise<R[]>,
    chunkSize = 1000
  ): Promise<R[]> {
    const results: R[] = []

    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize)
      const chunkResults = await processor(chunk)
      results.push(...chunkResults)

      // 让出控制权
      await new Promise(resolve => setTimeout(resolve, 0))
    }

    return results
  }

  /**
   * 流式处理
   */
  static async *stream<T, R>(
    data: T[],
    processor: (item: T) => Promise<R>,
    batchSize = 10
  ): AsyncGenerator<R, void, unknown> {
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize)
      const results = await Promise.all(batch.map(processor))

      for (const result of results) {
        yield result
      }
    }
  }

  /**
   * 使用Web Worker处理CPU密集型任务
   */
  static async processWithWorker<T, R>(
    data: T[],
    workerScript: string,
    options: {
      workers?: number
      timeout?: number
    } = {}
  ): Promise<R[]> {
    const {
      workers = navigator.hardwareConcurrency || 4,
      timeout = 60000
    } = options

    return new Promise<R[]>((resolve, reject) => {
      const workerPool: Worker[] = []
      const results: R[] = []
      let completed = 0

      // 创建Worker池
      for (let i = 0; i < workers; i++) {
        const worker = new Worker(workerScript)
        workerPool.push(worker)

        worker.onmessage = (e) => {
          results.push(e.data)
          completed++

          if (completed === data.length) {
            // 清理Workers
            workerPool.forEach(w => w.terminate())
            resolve(results)
          }
        }

        worker.onerror = (error) => {
          workerPool.forEach(w => w.terminate())
          reject(error)
        }
      }

      // 分配任务给Workers
      data.forEach((item, index) => {
        const worker = workerPool[index % workers]
        worker.postMessage(item)
      })

      // 超时处理
      setTimeout(() => {
        workerPool.forEach(w => w.terminate())
        reject(new Error('Worker处理超时'))
      }, timeout)
    })
  }

  /**
   * 优化的批量导入
   */
  static async batchImport<T>(
    data: T[],
    importFn: (items: T[]) => Promise<unknown>,
    options: {
      batchSize?: number
      onProgress?: (progress: number) => void
      validateFn?: (item: T) => boolean
    } = {}
  ): Promise<{
    success: number
    failed: number
    errors: Array<{ index: number; error: string }>
  }> {
    const { batchSize = 500, onProgress, validateFn } = options
    let success = 0
    let failed = 0
    const errors: Array<{ index: number; error: string }> = []

    // 验证数据
    if (validateFn) {
      data.forEach((item, index) => {
        if (!validateFn(item)) {
          failed++
          errors.push({ index, error: '数据验证失败' })
        }
      })

      // 过滤出有效数据
      data = data.filter((_, index) => !errors.some(e => e.index === index))
    }

    // 分批导入
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize)

      try {
        await importFn(batch)
        success += batch.length
      } catch (error) {
        failed += batch.length
        batch.forEach((_, index) => {
          errors.push({
            index: i + index,
            error: error instanceof Error ? error.message : '导入失败'
          })
        })
      }

      if (onProgress) {
        const progress = Math.round(((i + batch.length) / data.length) * 100)
        onProgress(progress)
      }

      // 避免阻塞
      await new Promise(resolve => setTimeout(resolve, 10))
    }

    return { success, failed, errors }
  }
}

/**
 * 便捷函数：批量处理
 */
export async function processBatch<T>(
  data: T[],
  processor: (item: T) => Promise<unknown>,
  options: {
    batchSize?: number
    onProgress?: (progress: number) => void
  } = {}
): Promise<BatchResult<T>> {
  return BatchProcessor.process({
    data,
    processor,
    batchSize: options.batchSize,
    onProgress: options.onProgress
  })
}

/**
 * 便捷函数：带进度提示的批量处理
 */
export async function processBatchWithProgress<T>(
  data: T[],
  processor: (item: T) => Promise<unknown>,
  message = '正在处理'
): Promise<BatchResult<T>> {
  let currentMessage = `${message}...0%`
  const progressMessage = ElMessage({
    message: currentMessage,
    duration: 0,
    type: 'info'
  })

  try {
    const result = await BatchProcessor.process({
      data,
      processor,
      onProgress: (progress) => {
        currentMessage = `${message}...${progress}%`
        progressMessage.close()
        ElMessage({
          message: currentMessage,
          duration: 0,
          type: 'info'
        })
      }
    })

    progressMessage.close()

    if (result.failed > 0) {
      ElMessage.warning(`处理完成，成功: ${result.success}，失败: ${result.failed}`)
    } else {
      ElMessage.success(`处理完成，共处理 ${result.success} 条数据`)
    }

    return result
  } catch (error) {
    progressMessage.close()
    ElMessage.error('批量处理失败')
    throw error
  }
}

export default BatchProcessor