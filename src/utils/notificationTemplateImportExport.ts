interface NotificationTemplate {
  id: string
  name: string
  type: 'system' | 'email' | 'sms' | 'dingtalk' | 'wechat'
  category: string
  title: string
  content: string
  variables: TemplateVariable[]
  settings: NotificationSettings
  status: 'active' | 'inactive'
  createTime: string
  updateTime: string
}

interface TemplateVariable {
  name: string
  label: string
  type: 'string' | 'number' | 'date' | 'boolean'
  required: boolean

  defaultValue?: unknown
  description?: string
}

interface NotificationSettings {
  priority: 'high' | 'normal' | 'low'
  retryCount: number
  timeout: number
  enableSchedule: boolean
  scheduleRules?: ScheduleRule[]
}

interface ScheduleRule {
  type: 'immediate' | 'delay' | 'scheduled'
  delay?: number
  scheduleTime?: string
  condition?: string
}

interface ImportResult {
  success: boolean
  total: number
  successCount: number
  failureCount: number
  errors: ImportError[]
  data?: NotificationTemplate[]
}

interface ImportError {
  row: number
  field: string

  value: unknown
  message: string
}

interface ExportOptions {
  format: 'excel' | 'json' | 'csv'
  includeInactive: boolean
  selectedIds?: string[]
  categories?: string[]
  compress: boolean
}

/**
 * CLEAN-AUX-001 & CLEAN-AUX-002: 通知模板导入导出功能
 * 支持通知模板的批量导入导出，包含多渠道通知模板管理
 */
export class NotificationTemplateImportExport {
  private templates: Map<string, NotificationTemplate> = new Map()

  // 预定义模板类别
  private readonly templateCategories = [
    { value: 'approval', label: '审批通知' },
    { value: 'reminder', label: '提醒通知' },
    { value: 'system', label: '系统通知' },
    { value: 'workflow', label: '流程通知' },
    { value: 'announcement', label: '公告通知' }
  ]

  // 预定义变量
  private readonly systemVariables = [
    { name: 'user.name', label: '用户姓名', type: 'string' as const },
    { name: 'user.department', label: '用户部门', type: 'string' as const },
    { name: 'process.name', label: '流程名称', type: 'string' as const },
    { name: 'process.instance', label: '流程实例', type: 'string' as const },
    { name: 'task.name', label: '任务名称', type: 'string' as const },
    { name: 'current.time', label: '当前时间', type: 'date' as const },
    { name: 'due.time', label: '截止时间', type: 'date' as const }
  ]

  // 导入通知模板
  async importTemplates(file: File): Promise<ImportResult> {
    try {
      const fileContent = await this.readFileContent(file)

      let templates: unknown[]

      // 根据文件类型解析内容
      if (file.name.endsWith('.json')) {
        templates = JSON.parse(fileContent)
      } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        templates = await this.parseExcelFile(file)
      } else if (file.name.endsWith('.csv')) {
        templates = this.parseCsvContent(fileContent)
      } else {
        throw new Error('不支持的文件格式，仅支持 JSON、Excel、CSV 格式')
      }

      return this.validateAndImportTemplates(templates)
    } catch (__error) {
      return {
        success: false,
        total: 0,
        successCount: 0,
        failureCount: 0,
        errors: [
          {
            row: 0,
            field: 'file',
            value: file.name,
            message: error instanceof Error ? error.message : '未知错误'
          }
        ]
      }
    }
  }

  // 验证并导入模板

  private async validateAndImportTemplates(templates: unknown[]): Promise<ImportResult> {
    const result: ImportResult = {
      success: true,
      total: templates.length,
      successCount: 0,
      failureCount: 0,
      errors: [],
      data: []
    }

    for (let i = 0; i < templates.length; i++) {
      const templateData = templates[i]
      const rowIndex = i + 1

      try {
        // 验证必填字段
        const validationResult = this.validateTemplate(templateData, rowIndex)
        if (validationResult.errors.length > 0) {
          result.errors.push(...validationResult.errors)
          result.failureCount++
          continue
        }

        // 转换为标准模板格式
        const template = this.normalizeTemplate(templateData)

        // 检查是否已存在
        const existingTemplate = this.templates.get(template.id)
        if (existingTemplate) {
          // 更新现有模板
          this.templates.set(template.id, {
            ...existingTemplate,
            ...template,
            updateTime: new Date().toISOString()
          })
        } else {
          // 创建新模板
          this.templates.set(template.id, template)
        }

        result.data!.push(template)
        result.successCount++
      } catch (__error) {
        result.errors.push({
          row: rowIndex,
          field: 'template',
          value: templateData.name || '未知',
          message: error instanceof Error ? error.message : '处理失败'
        })
        result.failureCount++
      }
    }

    result.success = result.errors.length === 0
    return result
  }

  // 验证模板数据

  private validateTemplate(data: unknown, row: number): { errors: ImportError[] } {
    const errors: ImportError[] = []

    // 必填字段检查
    const requiredFields = ['name', 'type', 'title', 'content']
    for (const field of requiredFields) {
      if (!data[field] || data[field].toString().trim() === '') {
        errors.push({
          row,
          field,
          value: data[field],
          message: `${field} 为必填字段`
        })
      }
    }

    // 类型检查
    if (data.type && !['system', 'email', 'sms', 'dingtalk', 'wechat'].includes(data.type)) {
      errors.push({
        row,
        field: 'type',
        value: data.type,
        message: '通知类型无效，支持：system, email, sms, dingtalk, wechat'
      })
    }

    // 变量格式检查
    if (data.variables && !Array.isArray(data.variables)) {
      errors.push({
        row,
        field: 'variables',
        value: data.variables,
        message: '变量配置必须是数组格式'
      })
    }

    return { errors }
  }

  // 标准化模板数据

  private normalizeTemplate(data: unknown): NotificationTemplate {
    const now = new Date().toISOString()

    return {
      id: data.id || `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: data.name.trim(),
      type: data.type,
      category: data.category || 'system',
      title: data.title.trim(),
      content: data.content.trim(),
      variables: this.normalizeVariables(data.variables || []),
      settings: this.normalizeSettings(data.settings || {}),
      status: data.status || 'active',
      createTime: data.createTime || now,
      updateTime: now
    }
  }

  // 标准化变量配置

  private normalizeVariables(variables: unknown[]): TemplateVariable[] {
    return variables.map(variable => ({
      name: variable.name || '',
      label: variable.label || variable.name || '',
      type: variable.type || 'string',
      required: Boolean(variable.required),
      defaultValue: variable.defaultValue,
      description: variable.description || ''
    }))
  }

  // 标准化设置配置

  private normalizeSettings(settings: unknown): NotificationSettings {
    return {
      priority: settings.priority || 'normal',
      retryCount: Number(settings.retryCount) || 3,
      timeout: Number(settings.timeout) || 30000,
      enableSchedule: Boolean(settings.enableSchedule),
      scheduleRules: Array.isArray(settings.scheduleRules) ? settings.scheduleRules : []
    }
  }

  // 导出通知模板
  async exportTemplates(
    options: ExportOptions = { format: 'excel', includeInactive: false, compress: false }
  ): Promise<Blob> {
    try {
      // 获取要导出的模板
      const templates = this.getTemplatesForExport(options)

      let blob: Blob

      switch (options.format) {
        case 'json':
          blob = this.exportAsJson(templates)
          break
        case 'csv':
          blob = this.exportAsCsv(templates)
          break
        case 'excel':
        default:
          blob = await this.exportAsExcel(templates)
          break
      }

      // 如果需要压缩
      if (options.compress && templates.length > 100) {
        return this.compressFile(blob, `templates.${options.format}`)
      }

      return blob
    } catch (__error) {
      throw error
    }
  }

  // 获取要导出的模板
  private getTemplatesForExport(options: ExportOptions): NotificationTemplate[] {
    let templates = Array.from(this.templates.values())

    // 过滤非激活状态
    if (!options.includeInactive) {
      templates = templates.filter(template => template.status === 'active')
    }

    // 按指定ID过滤
    if (options.selectedIds && options.selectedIds.length > 0) {
      templates = templates.filter(template => options.selectedIds!.includes(template.id))
    }

    // 按类别过滤
    if (options.categories && options.categories.length > 0) {
      templates = templates.filter(template => options.categories!.includes(template.category))
    }

    return templates.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
  }

  // 导出为JSON格式
  private exportAsJson(templates: NotificationTemplate[]): Blob {
    const data = {
      exportTime: new Date().toISOString(),
      version: '1.0',
      total: templates.length,
      templates
    }

    return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json;charset=utf-8' })
  }

  // 导出为CSV格式
  private exportAsCsv(templates: NotificationTemplate[]): Blob {
    const headers = [
      'ID',
      '模板名称',
      '通知类型',
      '类别',
      '标题',
      '内容',
      '状态',
      '创建时间',
      '更新时间'
    ]

    const rows = templates.map(template => [
      template.id,
      template.name,
      template.type,
      template.category,
      template.title,
      template.content.replace(/\n/g, '\\n'),
      template.status,
      template.createTime,
      template.updateTime
    ])

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
      .join('\n')

    // 添加BOM以支持中文
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
  }

  // 导出为Excel格式（简化实现）
  private async exportAsExcel(templates: NotificationTemplate[]): Promise<Blob> {
    // 这里应该使用真实的Excel库，如xlsx或exceljs
    // 为了演示，这里返回一个模拟的Excel格式
    const data = this.convertToExcelData(templates)

    return new Blob([JSON.stringify(data)], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
  }

  // 转换为Excel数据格式
  private convertToExcelData(templates: NotificationTemplate[]) {
    return {
      sheets: {
        通知模板: {
          headers: [
            'ID',
            '模板名称',
            '通知类型',
            '类别',
            '标题',
            '内容',
            '变量数量',
            '状态',
            '创建时间'
          ],
          data: templates.map(template => [
            template.id,
            template.name,
            template.type,
            template.category,
            template.title,
            template.content,
            template.variables.length,
            template.status,
            template.createTime
          ])
        },
        变量详情: {
          headers: ['模板ID', '模板名称', '变量名', '变量标签', '变量类型', '是否必填', '默认值'],
          data: templates.flatMap(template =>
            template.variables.map(variable => [
              template.id,
              template.name,
              variable.name,
              variable.label,
              variable.type,
              variable.required ? '是' : '否',
              variable.defaultValue || ''
            ])
          )
        }
      }
    }
  }

  // 压缩文件
  private async compressFile(blob: Blob, filename: string): Promise<Blob> {
    // 这里应该使用真实的压缩库，如JSZip
    // 为了演示，这里返回原始文件
    return blob
  }

  // 读取文件内容
  private readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file, 'utf-8')
    })
  }

  // 解析Excel文件
  private async parseExcelFile(file: File): Promise<unknown[]> {
    // 这里应该使用真实的Excel解析库
    // 为了演示，返回模拟数据
    return []
  }

  // 解析CSV内容

  private parseCsvContent(content: string): unknown[] {
    const lines = content.split('\n').filter(line => line.trim())
    if (lines.length < 2) return []

    const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim())
    const data = []

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim())

      const row: unknown = {}

      headers.forEach((header, index) => {
        row[header] = values[index] || ''
      })

      data.push(row)
    }

    return data
  }

  // 获取模板类别列表
  getTemplateCategories() {
    return this.templateCategories
  }

  // 获取系统变量列表
  getSystemVariables() {
    return this.systemVariables
  }

  // 获取所有模板
  getAllTemplates(): NotificationTemplate[] {
    return Array.from(this.templates.values())
  }

  // 根据ID获取模板
  getTemplateById(id: string): NotificationTemplate | undefined {
    return this.templates.get(id)
  }

  // 根据类型获取模板
  getTemplatesByType(type: string): NotificationTemplate[] {
    return Array.from(this.templates.values()).filter(template => template.type === type)
  }

  // 根据类别获取模板
  getTemplatesByCategory(category: string): NotificationTemplate[] {
    return Array.from(this.templates.values()).filter(template => template.category === category)
  }

  // 删除模板
  deleteTemplate(id: string): boolean {
    return this.templates.delete(id)
  }

  // 批量删除模板
  deleteTemplates(ids: string[]): number {
    let deletedCount = 0
    for (const id of ids) {
      if (this.templates.delete(id)) {
        deletedCount++
      }
    }
    return deletedCount
  }

  // 清空所有模板
  clearAllTemplates() {
    this.templates.clear()
  }
}

// 全局实例
export const notificationTemplateManager = new NotificationTemplateImportExport()

// 便捷函数
export async function importNotificationTemplates(file: File): Promise<ImportResult> {
  return notificationTemplateManager.importTemplates(file)
}

export async function exportNotificationTemplates(options?: Partial<ExportOptions>): Promise<Blob> {
  return notificationTemplateManager.exportTemplates({
    format: 'excel',
    includeInactive: false,
    compress: false,
    ...options
  })
}
