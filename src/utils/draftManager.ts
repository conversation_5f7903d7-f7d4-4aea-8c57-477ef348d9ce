/**
 * 统一草稿管理器
 * 提供跨模块的草稿存储、加载、清理功能
 */
import request from '@/utils/request'

// 草稿类型定义
export type DraftType = 
  | 'leave_application'      // 请假申请
  | 'leave_approval'        // 请假审批
  | 'leave_hr_approval'     // 请假人事审批
  | 'onboarding_initial'    // 入职初审
  | 'onboarding_dept'       // 入职部门审批
  | 'onboarding_final'      // 入职终审
  | 'transfer_application'  // 调岗申请
  | 'transfer_dept_approval' // 调岗原部门审批
  | 'transfer_target_approval' // 调岗目标部门审批
  | 'resignation_application' // 离职申请
  | 'resignation_hr_approval' // 离职人事审批
  | 'resignation_handover'   // 工作交接
  | 'resignation_asset'      // 资产交接
  | 'todo_task'             // 待办任务

// 草稿元数据
export interface DraftMeta {
  id: string
  type: DraftType
  businessId: string
  title: string
  createTime: string
  updateTime: string
  expireTime?: string
  size: number
  status: 'active' | 'expired' | 'deleted'
}

// 草稿数据
export interface DraftData<T = unknown> {
  meta: DraftMeta
  content: T
}

// 草稿管理配置
export interface DraftConfig {
  maxSize?: number          // 单个草稿最大大小（字节）
  maxCount?: number         // 最大草稿数量
  autoSaveInterval?: number // 自动保存间隔（毫秒）
  expireDays?: number       // 过期天数
  enableCompression?: boolean // 启用压缩
  enableEncryption?: boolean  // 启用加密
}

// 默认配置
const DEFAULT_CONFIG: DraftConfig = {
  maxSize: 5 * 1024 * 1024, // 5MB
  maxCount: 100,
  autoSaveInterval: 30000,  // 30秒
  expireDays: 30,
  enableCompression: true,
  enableEncryption: false
}

class DraftManager {
  private config: DraftConfig
  private autoSaveTimers: Map<string, NodeJS.Timeout> = new Map()
  private savingQueue: Set<string> = new Set()

  constructor(config: DraftConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * 保存草稿
   */
  async saveDraft<T = unknown>(
    type: DraftType,
    businessId: string,
    content: T,
    options?: {
      title?: string
      expireDays?: number
      immediate?: boolean
    }
  ): Promise<string> {
    const draftKey = this.generateDraftKey(type, businessId)
    
    // 检查是否正在保存
    if (this.savingQueue.has(draftKey)) {
      return draftKey
    }

    this.savingQueue.add(draftKey)

    try {
      // 创建草稿元数据
      const meta: DraftMeta = {
        id: draftKey,
        type,
        businessId,
        title: options?.title || this.getDefaultTitle(type),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        expireTime: this.calculateExpireTime(options?.expireDays || this.config.expireDays),
        size: JSON.stringify(content).length,
        status: 'active'
      }

      // 构建草稿数据
      const draftData: DraftData<T> = {
        meta,
        content
      }

      // 保存到后端
      await this.saveToBackend(draftData)

      // 同时保存到本地存储
      this.saveToLocal(draftKey, draftData)

      // 设置自动保存
      if (!options?.immediate) {
        this.setupAutoSave(type, businessId, () => content)
      }

      return draftKey
    } finally {
      this.savingQueue.delete(draftKey)
    }
  }

  /**
   * 加载草稿
   */
  async loadDraft<T = unknown>(
    type: DraftType,
    businessId: string
  ): Promise<T | null> {
    const draftKey = this.generateDraftKey(type, businessId)

    try {
      // 优先从后端加载
      const backendDraft = await this.loadFromBackend<T>(draftKey)
      if (backendDraft && backendDraft.meta.status === 'active') {
        // 同步到本地
        this.saveToLocal(draftKey, backendDraft)
        return backendDraft.content
      }

      // 后端无数据时从本地加载
      const localDraft = this.loadFromLocal<T>(draftKey)
      if (localDraft && localDraft.meta.status === 'active') {
        // 检查是否过期
        if (!this.isExpired(localDraft.meta)) {
          return localDraft.content
        }
      }

      return null
    } catch (__error) {
      return null
    }
  }

  /**
   * 删除草稿
   */
  async deleteDraft(type: DraftType, businessId: string): Promise<void> {
    const draftKey = this.generateDraftKey(type, businessId)

    // 清除自动保存
    this.clearAutoSave(draftKey)

    // 从后端删除
    try {
      await this.deleteFromBackend(draftKey)
    } catch (__error) {
      }

    // 从本地删除
    this.deleteFromLocal(draftKey)
  }

  /**
   * 获取草稿列表
   */
  async listDrafts(options?: {
    type?: DraftType
    status?: 'active' | 'expired' | 'deleted'
    startDate?: string
    endDate?: string
  }): Promise<DraftMeta[]> {
    try {
      const {data} =  await request({
        url: '/api/workflow/drafts',
        method: 'get',
        params: options
      })
      return data
    } catch (__error) {
      // 降级到本地列表
      return this.listLocalDrafts(options)
    }
  }

  /**
   * 清理过期草稿
   */
  async cleanExpiredDrafts(): Promise<number> {
    let cleanedCount  i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith('draft_')) {
        try {
          const draft = this.loadFromLocal<DraftData>(key)
          if (draft && draft.meta) {
            // 应用过滤条件
            if (options?.type && draft.meta.type !== options.type) continue
            if (options?.status && draft.meta.status !== options.status) continue
            
            drafts.push(draft.meta)
          }
        } catch (__error) {
          // 忽略解析错误的项
        }
      }
    }

    return drafts
  }

  private cleanLocalExpiredDrafts(): number {
    let cleanedCount = 0
    const keysToRemove: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith('draft_')) {
        try {
          const draft = this.loadFromLocal<DraftData>(key)
          if (draft && draft.meta && this.isExpired(draft.meta)) {
            keysToRemove.push(key)
          }
        } catch (__error) {
          // 移除无法解析的项
          keysToRemove.push(key)
        }
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      cleanedCount++
    })

    return cleanedCount
  }

  private compress(data: string): string {
    // 简单的压缩实现（实际项目中可以使用 pako 等库）
    return btoa(encodeURIComponent(data))
  }

  private decompress(data: string): string {
    // 简单的解压实现
    return decodeURIComponent(atob(data))
  }
}

// 导出单例
export const draftManager = new DraftManager()

// 导出 Vue 组合式函数
export function useDraftManager() {
  return {
    saveDraft: draftManager.saveDraft.bind(draftManager),
    loadDraft: draftManager.loadDraft.bind(draftManager),
    deleteDraft: draftManager.deleteDraft.bind(draftManager),
    listDrafts: draftManager.listDrafts.bind(draftManager),
    cleanExpiredDrafts: draftManager.cleanExpiredDrafts.bind(draftManager),
    setupAutoSave: draftManager.setupAutoSave.bind(draftManager),
    clearAutoSave: draftManager.clearAutoSave.bind(draftManager),
    clearAllAutoSave: draftManager.clearAllAutoSave.bind(draftManager)
  }
}