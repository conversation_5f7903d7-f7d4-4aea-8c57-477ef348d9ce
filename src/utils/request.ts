import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { setupEncryptionInterceptor } from './encryption'
import { recordApiTiming } from './performance'
import { createCacheInterceptor, memoryCache, cacheInvalidator } from './cache'
import { createRequestCacheInterceptor, CacheConfigs } from './request-cache'
import { errorHandler, ErrorType, ErrorLevel } from './errorHandler'

/**
 * 生成缓存键
 */
function generateCacheKey(config: AxiosRequestConfig): string {
  const { method = 'GET', url, params, data } = config
  const keyParts = [
    method.toUpperCase(),
    url,
    params ? JSON.stringify(params) : '',
    data ? JSON.stringify(data) : ''
  ]
  return keyParts.filter(Boolean).join('_')
}

// 创建 axios 实例
const service: AxiosInstance = axios.create({
  // 在开发环境且启用Mock时，使用空字符串，让请求直接发送到当前域
  baseURL:
    import.meta.env.VITE_ENABLE_MOCK === 'true' && import.meta.env.DEV
      ? ''
      : import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 设置加密拦截器
setupEncryptionInterceptor(service)

// 设置增强的缓存拦截器
const requestCacheInterceptor = createRequestCacheInterceptor({
  strategy: 'auto',
  defaultTTL: 5 * 60 * 1000, // 5分钟默认缓存
  showCacheHit: process.env.NODE_ENV === 'development',
  enabled: true
})

// 应用增强缓存请求拦截器
service.interceptors.request.use(requestCacheInterceptor.request, error => Promise.reject(error))

// 请求拦截器
service.interceptors.request.use(
  (config: unknown) => {
    // 临时修复Axios类型问题
    // 在发送请求之前做些什么
    const token = localStorage.getItem('access_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 记录请求开始时间（用于性能监控）
    config.metadata = { startTime: Date.now() }

    // 添加HTTP缓存相关头
    if (config.method?.toUpperCase() === 'GET' && (config as unknown).cache !== false) {
      // 支持浏览器缓存
      config.headers['Cache-Control'] = config.headers['Cache-Control'] || 'private, max-age=300' // 5分钟

      // 从缓存中获取HTTP头信息
      const cacheKey = generateCacheKey(config)
      const cachedMetadata = sessionStorage.getItem(`http_cache_${cacheKey}`)

      if (cachedMetadata) {
        try {
          const metadata = JSON.parse(cachedMetadata)

          // 如果有缓存的ETag，添加到请求头
          if (metadata.etag) {
            config.headers['If-None-Match'] = metadata.etag
          }

          // 如果有缓存的Last-Modified，添加到请求头
          if (metadata.lastModified) {
            config.headers['If-Modified-Since'] = metadata.lastModified
          }
        } catch (__e) {}
      }
    }

    return config
  },
  error => {
    // 对请求错误做些什么
    errorHandler.handleError({
      type: ErrorType.NETWORK,
      level: ErrorLevel.ERROR,
      message: '请求发送失败',
      details: error,
      method: error.config?.method,
      url: error.config?.url
    })
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  async (response: AxiosResponse) => {
    // 处理HTTP缓存头
    if (response.config.method?.toUpperCase() === 'GET') {
      // 保存ETag和Last-Modified以供下次请求使用
      const etag = response.headers['etag']
      const lastModified = response.headers['last-modified']
      const cacheControl = response.headers['cache-control']

      if (etag || lastModified) {
        // 存储到缓存元数据中
        const cacheKey = generateCacheKey(response.config)
        const metadata = {
          etag,
          lastModified,
          cacheControl,
          timestamp: Date.now()
        }
        // 可以存储到localStorage或内存中
        sessionStorage.setItem(`http_cache_${cacheKey}`, JSON.stringify(metadata))
      }
    }

    // 应用增强缓存响应拦截器
    await requestCacheInterceptor.response(response)

    // 记录API响应时间
    if ((response.config as unknown).metadata) {
      const duration = Date.now() - (response.config as unknown).metadata.startTime
      recordApiTiming(response.config.url || '', duration)
    }

    // 对响应数据做点什么
    const { data } = response

    // 如果是统一响应格式
    if (data && typeof data === 'object' && 'code' in data) {
      if (data.code === 200) {
        return data.data
      } else {
        ElMessage.error(data.message || '请求失败')
        return Promise.reject(new Error(data.message || '请求失败'))
      }
    }

    return data
  },
  async error => {
    // 处理缓存命中
    if (error?.__CACHE_HIT__) {
      const cachedResponse = await requestCacheInterceptor.error(error)
      return cachedResponse.data
    }

    // 记录失败的API响应时间
    if (error.config?.metadata) {
      const duration = Date.now() - error.config.metadata.startTime
      recordApiTiming(error.config.url || '', duration)
    }

    // 统一错误处理
    if (error.response) {
      const { status, data } = error.response
      let errorType = ErrorType.NETWORK
      let errorLevel = ErrorLevel.ERROR

      switch (status) {
        case 304:
          // 处理304 Not Modified - 使用本地缓存
          const cacheKey = generateCacheKey(error.config)
          const cachedData = memoryCache.get(cacheKey)
          if (cachedData) {
            return cachedData
          }
          // 如果内存缓存中没有，返回空数据
          return null
        case 401:
          errorType = ErrorType.PERMISSION
          errorHandler.handleError({
            type: errorType,
            level: errorLevel,
            code: '401',
            message: '未授权，请重新登录',
            url: error.config?.url,
            method: error.config?.method,
            details: error
          })
          break
        case 403:
          errorType = ErrorType.PERMISSION
          errorHandler.handleError({
            type: errorType,
            level: errorLevel,
            code: '403',
            message: '权限不足',
            url: error.config?.url,
            method: error.config?.method,
            details: error
          })
          break
        case 404:
          errorType = ErrorType.BUSINESS
          errorLevel = ErrorLevel.WARNING
          errorHandler.handleError({
            type: errorType,
            level: errorLevel,
            code: '404',
            message: '请求的资源不存在',
            url: error.config?.url,
            method: error.config?.method,
            details: error
          })
          break
        case 422:
          errorType = ErrorType.VALIDATION
          errorHandler.handleError({
            type: errorType,
            level: ErrorLevel.WARNING,
            code: '422',
            message: data?.message || '数据验证失败',
            url: error.config?.url,
            method: error.config?.method,
            details: data?.errors || error
          })
          break
        case 500:
        case 502:
        case 503:
        case 504:
          errorType = ErrorType.SYSTEM
          errorLevel = status === 500 ? ErrorLevel.CRITICAL : ErrorLevel.ERROR
          errorHandler.handleError({
            type: errorType,
            level: errorLevel,
            code: String(status),
            message: '服务器错误，请稍后重试',
            url: error.config?.url,
            method: error.config?.method,
            details: error
          })
          break
        default:
          // 业务错误
          if (status >= 400 && status < 500) {
            errorType = ErrorType.BUSINESS
            errorLevel = ErrorLevel.WARNING
          }
          errorHandler.handleError({
            type: errorType,
            level: errorLevel,
            code: String(status),
            message: data?.message || `请求失败 (${status})`,
            url: error.config?.url,
            method: error.config?.method,
            details: error
          })
      }
    } else if (error.request) {
      // 网络错误
      errorHandler.handleError({
        type: ErrorType.NETWORK,
        level: ErrorLevel.ERROR,
        message: '网络错误，请检查网络连接',
        url: error.config?.url,
        method: error.config?.method,
        details: error
      })
    } else {
      // 请求配置错误
      errorHandler.handleError({
        type: ErrorType.SYSTEM,
        level: ErrorLevel.ERROR,
        message: error.message || '请求配置错误',
        details: error
      })
    }

    return Promise.reject(error)
  }
)

export default service

// 导出缓存相关工具
export { cacheInvalidator, requestCacheInterceptor }

// 导出缓存统计方法
export const getCacheStats = () => requestCacheInterceptor.cache.getStats()

// 导出缓存管理方法
export const clearCache = () => requestCacheInterceptor.cache.config.cacheInstance.clear()

// 导出缓存预热方法
export const preloadCache = (requests: Array<{ url: string; params?: Record<string, unknown> }>) =>
  requestCacheInterceptor.cache.preload(requests)

// 导出缓存配置
export { CacheConfigs }
