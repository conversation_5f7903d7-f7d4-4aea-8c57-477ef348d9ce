interface LeaveRecord {
  id: string
  processInstanceId: string
  employeeId: string
  employeeName: string
  departmentId: string
  departmentName: string
  position: string
  leaveType:
    | 'sick'
    | 'personal'
    | 'annual'
    | 'maternity'
    | 'marriage'
    | 'bereavement'
    | 'compensatory'
    | 'study'
  reason: string
  startDate: string
  endDate: string
  startTime?: string
  endTime?: string
  leaveDays: number
  leaveHours?: number
  isHalfDay: boolean
  timeSlot?: 'morning' | 'afternoon'
  status: 'pending' | 'approved' | 'rejected' | 'withdrawn' | 'cancelled'
  statusName: string
  currentTaskName?: string
  currentAssignee?: string
  approvalHistory: ApprovalRecord[]
  attachments?: FileAttachment[]
  emergency: boolean
  substitutePersonId?: string
  substitutePersonName?: string
  annualLeaveBalance?: number
  createTime: string
  updateTime: string
  approvalTime?: string
  submissionTime: string
}

interface ApprovalRecord {
  taskId: string
  taskName: string
  assignee: string
  assigneeName: string
  status: 'pending' | 'approved' | 'rejected'
  opinion?: string
  approvalTime?: string
  duration?: number
}

interface FileAttachment {
  id: string
  fileName: string
  fileSize: number
  fileType: string
  uploadTime: string
  downloadUrl?: string
}

interface LeaveExportFilter {
  employeeName?: string
  employeeId?: string
  departmentIds?: string[]
  leaveTypes?: string[]
  status?: string[]
  dateRange?: {
    start: string
    end: string
    type: 'application' | 'leave' | 'approval'
  }
  emergency?: boolean
  approver?: string
  minDays?: number
  maxDays?: number
}

interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf'
  includeApprovalHistory: boolean
  includeAttachments: boolean
  includeStatistics: boolean
  groupBy?: 'department' | 'type' | 'status' | 'month'
  template?: string
  columns?: string[]
}

interface ExportResult {
  success: boolean
  blob?: Blob
  filename: string
  recordCount: number
  statisticsSummary?: {
    totalRecords: number
    totalDays: number
    avgDays: number
    byType: Record<string, number>
    byStatus: Record<string, number>
    byDepartment: Record<string, number>
  }
  error?: string
}

// 导出数据项类型
interface ExportDataItem {
  [key: string]: string | number | boolean | undefined
  员工姓名?: string
  员工工号?: string
  部门?: string
  职位?: string
  请假类型?: string
  请假天数?: number
  开始日期?: string
  结束日期?: string
  状态?: string
  审批人?: string
  审批时间?: string
  审批意见?: string
  审批状态?: string
}

// 统计数据类型
interface StatisticsData {
  项目: string
  数值: string | number
}

// 审批统计类型
interface ApprovalStatistics {
  总审批数: number
  已同意: number
  已拒绝: number
  待处理: number
  同意率: string
}

/**
 * CLEAN-AUX-009: 请假记录导出功能
 * 支持多条件筛选和多格式导出的请假记录管理系统
 */
export class LeaveRecordExport {
  private leaveRecords: Map<string, LeaveRecord> = new Map()

  // 预定义请假类型
  private readonly leaveTypes = [
    { value: 'sick', label: '病假', needAttachment: true },
    { value: 'personal', label: '事假', needAttachment: false },
    { value: 'annual', label: '年假', needAttachment: false },
    { value: 'maternity', label: '产假', needAttachment: true },
    { value: 'marriage', label: '婚假', needAttachment: true },
    { value: 'bereavement', label: '丧假', needAttachment: true },
    { value: 'compensatory', label: '调休', needAttachment: false },
    { value: 'study', label: '学习假', needAttachment: true }
  ]

  // 状态标签映射
  private readonly statusLabels = {
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    withdrawn: '已撤回',
    cancelled: '已撤销'
  }

  constructor() {
    this.initializeMockData()
  }

  // 初始化模拟数据
  private initializeMockData() {
    const mockRecords: LeaveRecord[] = [
      {
        id: 'leave_001',
        processInstanceId: 'proc_leave_001',
        employeeId: 'emp_001',
        employeeName: '张三',
        departmentId: 'dept_001',
        departmentName: '技术部',
        position: '软件工程师',
        leaveType: 'sick',
        reason: '感冒发烧，需要休息治疗',
        startDate: '2025-01-15',
        endDate: '2025-01-16',
        startTime: '09:00',
        endTime: '18:00',
        leaveDays: 2,
        leaveHours: 16,
        isHalfDay: false,
        status: 'approved',
        statusName: '已批准',
        currentTaskName: '',
        currentAssignee: '',
        approvalHistory: [
          {
            taskId: 'task_001',
            taskName: '部门审批',
            assignee: 'manager_001',
            assigneeName: '李经理',
            status: 'approved',
            opinion: '同意请假，注意休息',
            approvalTime: '2025-01-14T10:30:00.000Z',
            duration: 30
          }
        ],
        attachments: [
          {
            id: 'att_001',
            fileName: '医院证明.pdf',
            fileSize: 2048576,
            fileType: 'application/pdf',
            uploadTime: '2025-01-13T15:20:00.000Z'
          }
        ],
        emergency: false,
        substitutePersonId: 'emp_002',
        substitutePersonName: '李四',
        annualLeaveBalance: 8,
        createTime: '2025-01-13T14:00:00.000Z',
        updateTime: '2025-01-14T10:30:00.000Z',
        approvalTime: '2025-01-14T10:30:00.000Z',
        submissionTime: '2025-01-13T14:00:00.000Z'
      },
      {
        id: 'leave_002',
        processInstanceId: 'proc_leave_002',
        employeeId: 'emp_003',
        employeeName: '王五',
        departmentId: 'dept_002',
        departmentName: '人事部',
        position: '人事专员',
        leaveType: 'annual',
        reason: '年度旅行计划',
        startDate: '2025-02-01',
        endDate: '2025-02-05',
        leaveDays: 5,
        leaveHours: 40,
        isHalfDay: false,
        status: 'pending',
        statusName: '待审批',
        currentTaskName: '部门审批',
        currentAssignee: 'manager_002',
        approvalHistory: [],
        emergency: false,
        annualLeaveBalance: 12,
        createTime: '2025-01-13T09:00:00.000Z',
        updateTime: '2025-01-13T09:00:00.000Z',
        submissionTime: '2025-01-13T09:00:00.000Z'
      }
    ]

    mockRecords.forEach(record => {
      this.leaveRecords.set(record.id, record)
    })
  }

  // 导出请假记录
  async exportLeaveRecords(
    filter: LeaveExportFilter = {},
    options: ExportOptions = {
      format: 'excel',
      includeApprovalHistory: true,
      includeAttachments: false,
      includeStatistics: true
    }
  ): Promise<ExportResult> {
    try {
      // 筛选请假数据
      const filteredRecords = this.filterLeaveRecords(filter)

      if (filteredRecords.length === 0) {
        return {
          success: false,
          filename: '',
          recordCount: 0,
          error: '没有符合条件的请假记录'
        }
      }

      // 生成导出数据
      const exportData = this.prepareExportData(filteredRecords, options)

      // 计算统计信息
      const statistics = this.calculateStatistics(filteredRecords)

      // 根据格式生成文件
      let blob: Blob
      let filename: string

      switch (options.format) {
        case 'excel':
          blob = await this.generateExcelFile(exportData, statistics, options)
          filename = this.generateFilename('请假记录', 'xlsx')
          break
        case 'csv':
          blob = this.generateCsvFile(exportData)
          filename = this.generateFilename('请假记录', 'csv')
          break
        case 'pdf':
          blob = await this.generatePdfFile(exportData, statistics, options)
          filename = this.generateFilename('请假记录', 'pdf')
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }

      return {
        success: true,
        blob,
        filename,
        recordCount: filteredRecords.length,
        statisticsSummary: statistics
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        recordCount: 0,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  // 导出审批流程报告
  async exportApprovalReport(
    filter: LeaveExportFilter = {},
    options: ExportOptions = {
      format: 'excel',
      includeApprovalHistory: true,
      includeAttachments: false,
      includeStatistics: true
    }
  ): Promise<ExportResult> {
    try {
      const filteredRecords = this.filterLeaveRecords(filter)

      if (filteredRecords.length === 0) {
        return {
          success: false,
          filename: '',
          recordCount: 0,
          error: '没有符合条件的审批记录'
        }
      }

      // 生成审批流程数据
      const approvalData = this.prepareApprovalReportData(filteredRecords, options)

      let blob: Blob
      let filename: string

      switch (options.format) {
        case 'excel':
          blob = await this.generateApprovalExcelFile(approvalData, options)
          filename = this.generateFilename('请假审批流程报告', 'xlsx')
          break
        case 'csv':
          blob = this.generateApprovalCsvFile(approvalData)
          filename = this.generateFilename('请假审批流程报告', 'csv')
          break
        case 'pdf':
          blob = await this.generateApprovalPdfFile(approvalData, options)
          filename = this.generateFilename('请假审批流程报告', 'pdf')
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }

      return {
        success: true,
        blob,
        filename,
        recordCount: filteredRecords.length
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        recordCount: 0,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  // 筛选请假记录
  private filterLeaveRecords(filter: LeaveExportFilter): LeaveRecord[] {
    let records = Array.from(this.leaveRecords.values())

    // 按员工姓名筛选
    if (filter.employeeName) {
      const keyword = filter.employeeName.toLowerCase()
      records = records.filter(record => record.employeeName.toLowerCase().includes(keyword))
    }

    // 按员工工号筛选
    if (filter.employeeId) {
      records = records.filter(record => record.employeeId.includes(filter.employeeId!))
    }

    // 按部门筛选
    if (filter.departmentIds && filter.departmentIds.length > 0) {
      records = records.filter(record => filter.departmentIds!.includes(record.departmentId))
    }

    // 按请假类型筛选
    if (filter.leaveTypes && filter.leaveTypes.length > 0) {
      records = records.filter(record => filter.leaveTypes!.includes(record.leaveType))
    }

    // 按状态筛选
    if (filter.status && filter.status.length > 0) {
      records = records.filter(record => filter.status!.includes(record.status))
    }

    // 按紧急状态筛选
    if (filter.emergency !== undefined) {
      records = records.filter(record => record.emergency === filter.emergency)
    }

    // 按审批人筛选
    if (filter.approver) {
      records = records.filter(record =>
        record.approvalHistory.some(approval => approval.assigneeName.includes(filter.approver!))
      )
    }

    // 按请假天数筛选
    if (filter.minDays !== undefined) {
      records = records.filter(record => record.leaveDays >= filter.minDays!)
    }

    if (filter.maxDays !== undefined) {
      records = records.filter(record => record.leaveDays <= filter.maxDays!)
    }

    // 按日期范围筛选
    if (filter.dateRange) {
      const { start, end, type } = filter.dateRange
      records = records.filter(record => {
        let dateToCompare: string
        switch (type) {
          case 'application':
            dateToCompare = record.submissionTime
            break
          case 'leave':
            dateToCompare = record.startDate
            break
          case 'approval':
            dateToCompare = record.approvalTime || ''
            break
          default:
            dateToCompare = record.submissionTime
        }
        return dateToCompare >= start && dateToCompare <= end
      })
    }

    return records
  }

  // 准备导出数据
  private prepareExportData(records: LeaveRecord[], options: ExportOptions): ExportDataItem[] {
    return records.map(record => ({
      员工姓名: record.employeeName,
      员工工号: record.employeeId,
      部门: record.departmentName,
      职位: record.position,
      请假类型: this.getLeaveTypeLabel(record.leaveType),
      请假天数: record.leaveDays,
      开始日期: record.startDate,
      结束日期: record.endDate,
      状态: this.statusLabels[record.status],
      申请时间: new Date(record.submissionTime).toLocaleString('zh-CN'),
      审批时间: record.approvalTime ? new Date(record.approvalTime).toLocaleString('zh-CN') : '',
      紧急状态: record.emergency ? '是' : '否',
      代理人: record.substitutePersonName || '',
      请假原因: record.reason
    }))
  }

  // 准备审批报告数据
  private prepareApprovalReportData(
    records: LeaveRecord[],
    options: ExportOptions
  ): ExportDataItem[] {
    const approvalData: ExportDataItem[] = []

    records.forEach(record => {
      record.approvalHistory.forEach(approval => {
        approvalData.push({
          员工姓名: record.employeeName,
          员工工号: record.employeeId,
          部门: record.departmentName,
          请假类型: this.getLeaveTypeLabel(record.leaveType),
          请假天数: record.leaveDays,
          审批环节: approval.taskName,
          审批人: approval.assigneeName,
          审批状态:
            approval.status === 'approved'
              ? '同意'
              : approval.status === 'rejected'
                ? '拒绝'
                : '待处理',
          审批意见: approval.opinion || '',
          审批时间: approval.approvalTime
            ? new Date(approval.approvalTime).toLocaleString('zh-CN')
            : '',
          处理时长: approval.duration ? `${approval.duration}分钟` : ''
        })
      })
    })

    return approvalData
  }

  // 计算统计信息
  private calculateStatistics(records: LeaveRecord[]) {
    const totalRecords = records.length
    const totalDays = records.reduce((sum, record) => sum + record.leaveDays, 0)
    const avgDays = totalRecords > 0 ? Math.round((totalDays / totalRecords) * 10) / 10 : 0

    const byType: Record<string, number> = {}
    const byStatus: Record<string, number> = {}
    const byDepartment: Record<string, number> = {}

    records.forEach(record => {
      // 按类型统计
      const typeLabel = this.getLeaveTypeLabel(record.leaveType)
      byType[typeLabel] = (byType[typeLabel] || 0) + 1

      // 按状态统计
      const statusLabel = this.statusLabels[record.status]
      byStatus[statusLabel] = (byStatus[statusLabel] || 0) + 1

      // 按部门统计
      byDepartment[record.departmentName] = (byDepartment[record.departmentName] || 0) + 1
    })

    return {
      totalRecords,
      totalDays,
      avgDays,
      byType,
      byStatus,
      byDepartment
    }
  }

  // 生成Excel文件
  private async generateExcelFile(
    data: ExportDataItem[],
    statistics: ReturnType<typeof this.calculateStatistics>,
    options: ExportOptions
  ): Promise<Blob> {
    // 这里应该使用Excel库，如ExcelJS
    // 暂时返回CSV格式
    const csvContent = this.arrayToCsv(data)
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'application/vnd.ms-excel;charset=utf-8' })
  }

  // 生成CSV文件
  private generateCsvFile(data: ExportDataItem[]): Blob {
    const csvContent = this.arrayToCsv(data)
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
  }

  // 生成审批Excel文件
  private async generateApprovalExcelFile(
    data: ExportDataItem[],
    options: ExportOptions
  ): Promise<Blob> {
    // 暂时返回CSV格式
    return this.generateApprovalCsvFile(data)
  }

  // 生成审批CSV文件
  private generateApprovalCsvFile(data: ExportDataItem[]): Blob {
    const csvContent = this.arrayToCsv(data)
    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
  }

  // 数组转CSV
  private arrayToCsv(data: ExportDataItem[]): string {
    if (data.length === 0) return ''

    const headers = Object.keys(data[0])
    const rows = data.map(item =>
      headers.map(header => `"${String(item[header] || '').replace(/"/g, '""')}"`)
    )

    return [headers.map(h => `"${h}"`).join(','), ...rows.map(row => row.join(','))].join('\n')
  }

  // 生成PDF文件
  private async generatePdfFile(
    data: ExportDataItem[],
    statistics: ReturnType<typeof this.calculateStatistics>,
    options: ExportOptions
  ): Promise<Blob> {
    const pdfContent = {
      title: '请假记录报表',
      data,
      statistics,
      options,
      generateTime: new Date().toISOString()
    }

    return new Blob([JSON.stringify(pdfContent)], { type: 'application/pdf' })
  }

  // 生成审批PDF文件
  private async generateApprovalPdfFile(
    data: ExportDataItem[],
    options: ExportOptions
  ): Promise<Blob> {
    const pdfContent = {
      title: '请假审批流程报告',
      data,
      options,
      generateTime: new Date().toISOString()
    }

    return new Blob([JSON.stringify(pdfContent)], { type: 'application/pdf' })
  }

  // 生成统计表格
  private generateStatisticsSheet(
    statistics: ReturnType<typeof this.calculateStatistics>
  ): StatisticsData[] {
    return [
      {
        项目: '总记录数',
        数值: statistics.totalRecords
      },
      {
        项目: '总请假天数',
        数值: statistics.totalDays
      },
      {
        项目: '平均请假天数',
        数值: statistics.avgDays
      },
      ...Object.entries(statistics.byType).map(([type, count]) => ({
        项目: `${type}数量`,
        数值: count
      })),
      ...Object.entries(statistics.byStatus).map(([status, count]) => ({
        项目: `${status}数量`,
        数值: count
      }))
    ]
  }

  // 计算审批统计
  private calculateApprovalStatistics(data: ExportDataItem[]): ApprovalStatistics {
    const totalApprovals = data.length
    const approvedCount = data.filter(item => item['审批状态'] === '同意').length
    const rejectedCount = data.filter(item => item['审批状态'] === '拒绝').length
    const pendingCount = data.filter(item => item['审批状态'] === '待处理').length

    return {
      总审批数: totalApprovals,
      已同意: approvedCount,
      已拒绝: rejectedCount,
      待处理: pendingCount,
      同意率: totalApprovals > 0 ? `${Math.round((approvedCount / totalApprovals) * 100)}%` : '0%'
    }
  }

  // 获取请假类型标签
  private getLeaveTypeLabel(type: string): string {
    const typeConfig = this.leaveTypes.find(t => t.value === type)
    return typeConfig ? typeConfig.label : type
  }

  // 生成文件名
  private generateFilename(prefix: string, extension: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19)
    return `${prefix}_${timestamp}.${extension}`
  }

  // 获取请假类型列表
  getLeaveTypes() {
    return this.leaveTypes
  }

  // 获取状态标签
  getStatusLabels() {
    return this.statusLabels
  }

  // 添加请假记录
  addLeaveRecord(record: LeaveRecord) {
    this.leaveRecords.set(record.id, record)
  }

  // 获取请假统计
  getLeaveStatistics() {
    const records = Array.from(this.leaveRecords.values())
    return this.calculateStatistics(records)
  }

  // 获取审批效率统计
  getApprovalEfficiencyStats() {
    const records = Array.from(this.leaveRecords.values())
    const approvals = records.flatMap(r => r.approvalHistory)

    const avgProcessTime =
      approvals.length > 0
        ? approvals.reduce((sum, a) => sum + (a.duration || 0), 0) / approvals.length
        : 0

    const approvalRate =
      approvals.length > 0
        ? approvals.filter(a => a.status === 'approved').length / approvals.length
        : 0

    return {
      totalApprovals: approvals.length,
      avgProcessTime: Math.round(avgProcessTime),
      approvalRate: Math.round(approvalRate * 100),
      fastestApproval: Math.min(...approvals.map(a => a.duration || 0)),
      slowestApproval: Math.max(...approvals.map(a => a.duration || 0))
    }
  }
}

// 全局实例
export const leaveRecordExporter = new LeaveRecordExport()

// 便捷函数
export async function exportLeaveRecords(
  filter?: LeaveExportFilter,
  options?: Partial<ExportOptions>
): Promise<ExportResult> {
  return leaveRecordExporter.exportLeaveRecords(filter, {
    format: 'excel',
    includeApprovalHistory: true,
    includeAttachments: false,
    includeStatistics: true,
    ...options
  })
}

export async function exportApprovalReport(
  filter?: LeaveExportFilter,
  options?: Partial<ExportOptions>
): Promise<ExportResult> {
  return leaveRecordExporter.exportApprovalReport(filter, {
    format: 'excel',
    includeApprovalHistory: true,
    includeAttachments: false,
    includeStatistics: true,
    ...options
  })
}
