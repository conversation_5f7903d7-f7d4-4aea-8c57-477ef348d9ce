/**
 * 代码分割优化工具
 * 提供路由级别和组件级别的代码分割策略
 */

import type { RouteRecordRaw, RouteLocationNormalized } from 'vue-router'
import type { Component } from 'vue'

// 预加载状态管理
interface PreloadState {
  loaded: Set<string>
  loading: Set<string>
  failed: Set<string>
}

// 路由配置接口
interface RouteConfig {
  path: string
  component: () => Promise<Component>
  preload?: boolean
  priority?: 'high' | 'medium' | 'low'
  prefetch?: boolean
  dependencies?: string[]
}

// 预加载状态
const preloadState: PreloadState = {
  loaded: new Set(),
  loading: new Set(),
  failed: new Set()
}

// 模块优先级映射
const MODULE_PRIORITY: Record<string, number> = {
  dashboard: 100,
  employee: 90,
  organization: 80,
  salary: 70,
  attendance: 60,
  performance: 50,
  workflow: 40,
  system: 30,
  demo: 10
}

/**
 * 创建懒加载路由
 * @param path 路由路径
 * @param componentPath 组件路径
 * @param options 选项
 */
export function createLazyRoute(
  path: string,
  componentPath: string,
  options: Partial<RouteConfig> = {}
): RouteRecordRaw {
  const route: RouteRecordRaw = {
    path,
    component: () => {
      // 标记加载状态
      preloadState.loading.add(componentPath)

      return import(/* @vite-ignore */ componentPath)
        .then(module => {
          preloadState.loading.delete(componentPath)
          preloadState.loaded.add(componentPath)
          return module.default
        })
        .catch(error => {
          preloadState.loading.delete(componentPath)
          preloadState.failed.add(componentPath)
          throw error
        })
    },
    meta: {
      preload: options.preload ?? false,
      priority: options.priority ?? 'medium',
      prefetch: options.prefetch ?? false,
      ...options.meta
    }
  }

  return route
}

/**
 * 预加载组件
 * @param componentPath 组件路径
 * @param priority 优先级
 */
export async function preloadComponent(
  componentPath: string,
  priority: 'high' | 'medium' | 'low' = 'medium'
): Promise<void> {
  // 已加载或正在加载中，直接返回
  if (preloadState.loaded.has(componentPath) || preloadState.loading.has(componentPath)) {
    return
  }

  // 根据优先级设置延迟
  const delay = priority === 'high' ? 0 : priority === 'medium' ? 1000 : 3000

  await new Promise(resolve => setTimeout(resolve, delay))

  try {
    preloadState.loading.add(componentPath)
    await import(/* @vite-ignore */ componentPath)
    preloadState.loaded.add(componentPath)
  } catch (__error) {
    preloadState.failed.add(componentPath)
  } finally {
    preloadState.loading.delete(componentPath)
  }
}

/**
 * 预加载路由模块
 * @param route 路由对象
 */
export async function preloadRouteModules(route: RouteLocationNormalized): Promise<void> {
  const path = route.path
  const moduleName = path.split('/')[1] || ''

  // 获取模块优先级
  const priority = MODULE_PRIORITY[moduleName] || 0

  // 高优先级模块，预加载相关组件
  if (priority >= 70) {
    // 预加载通用组件
    const commonComponents = [
      () => import('@/components/common/HrDataTable.vue'),
      () => import('@/components/common/HrFormDialog.vue'),
      () => import('@/components/common/HrAdvancedSearch.vue')
    ]

    // 并发预加载
    await Promise.all(commonComponents.map(loader => loader().catch(() => {})))
  }

  // 预加载模块特定组件
  const moduleComponents = getModuleComponents(moduleName)
  if (moduleComponents.length > 0) {
    await Promise.all(
      moduleComponents.map(comp => preloadComponent(comp, priority >= 80 ? 'high' : 'medium'))
    )
  }
}

/**
 * 获取模块相关组件
 * @param moduleName 模块名称
 */
function getModuleComponents(moduleName: string): string[] {
  const moduleComponentMap: Record<string, string[]> = {
    employee: [
      '@/components/employee/EmployeeForm.vue',
      '@/components/employee/EmployeeDetail.vue',
      '@/components/employee/EmployeeImport.vue'
    ],
    organization: [
      '@/components/organization/OrgTree.vue',
      '@/components/organization/DepartmentForm.vue'
    ],
    salary: ['@/components/salary/SalaryCalculator.vue', '@/components/salary/PayrollForm.vue'],
    workflow: ['@/components/workflow/ProcessDesigner.vue', '@/components/workflow/TaskForm.vue']
  }

  return moduleComponentMap[moduleName] || []
}

/**
 * 创建路由级别的代码分割配置
 * @param routes 路由配置
 */
export function setupRouteLevelSplitting(routes: RouteRecordRaw[]): RouteRecordRaw[] {
  return routes.map(route => {
    if (route.component && typeof route.component === 'function') {
      // 添加魔法注释以优化chunk名称
      const originalComponent = route.component
      route.component = () => {
        return originalComponent()
      }
    }

    // 递归处理子路由
    if (route.children) {
      route.children = setupRouteLevelSplitting(route.children)
    }

    return route
  })
}

/**
 * 创建预加载链接
 * @param href 链接地址
 * @param as 资源类型
 */
export function createPreloadLink(href: string, as: 'script' | 'style' = 'script'): void {
  const link = document.createElement('link')
  link.rel = 'preload'
  link.as = as
  link.href = href

  // 设置CORS
  if (as === 'script') {
    link.crossOrigin = 'anonymous'
  }

  document.head.appendChild(link)
}

/**
 * 创建预取链接
 * @param href 链接地址
 * @param as 资源类型
 */
export function createPrefetchLink(href: string, as: 'script' | 'style' = 'script'): void {
  const link = document.createElement('link')
  link.rel = 'prefetch'
  link.as = as
  link.href = href

  document.head.appendChild(link)
}

/**
 * 智能预加载策略
 * 根据用户行为和网络状态决定预加载策略
 */
export class SmartPreloader {
  private networkSpeed: 'slow' | 'fast' | 'unknown' = 'unknown'
  private userBehavior: Map<string, number> = new Map()
  private preloadQueue: Set<string> = new Set()

  constructor() {
    this.detectNetworkSpeed()
    this.setupIntersectionObserver()
  }

  /**
   * 检测网络速度
   */
  private async detectNetworkSpeed(): Promise<void> {
    if ('connection' in navigator) {
      const connection = (navigator as unknown).connection
      const effectiveType = connection.effectiveType

      if (effectiveType === '4g') {
        this.networkSpeed = 'fast'
      } else if (effectiveType === '3g' || effectiveType === '2g') {
        this.networkSpeed = 'slow'
      }
    }
  }

  /**
   * 设置交叉观察器
   */
  private setupIntersectionObserver(): void {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const link = entry.target as HTMLAnchorElement
            const href = link.getAttribute('href')
            if (href) {
              this.predictNavigation(href)
            }
          }
        })
      },
      {
        rootMargin: '50px'
      }
    )

    // 观察所有链接
    document.querySelectorAll('a[href^="/"]').forEach(link => {
      observer.observe(link)
    })
  }

  /**
   * 预测导航
   * @param path 路径
   */
  private predictNavigation(path: string): void {
    // 记录用户行为
    const count = this.userBehavior.get(path) || 0
    this.userBehavior.set(path, count + 1)

    // 如果用户经常访问该路径，提高预加载优先级
    if (count >= 2 && this.networkSpeed === 'fast') {
      this.preloadRoute(path)
    }
  }

  /**
   * 预加载路由
   * @param path 路由路径
   */
  private async preloadRoute(path: string): Promise<void> {
    if (this.preloadQueue.has(path)) return

    this.preloadQueue.add(path)

    // 根据路径确定需要预加载的组件
    const moduleName = path.split('/')[1]
    const components = getModuleComponents(moduleName)

    // 分批预加载
    for (const component of components) {
      await preloadComponent(component, 'low')
      // 间隔一段时间，避免占用太多带宽
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  /**
   * 获取预加载状态
   */
  getPreloadStatus(): {
    loaded: string[]
    loading: string[]
    failed: string[]
    networkSpeed: string
  } {
    return {
      loaded: Array.from(preloadState.loaded),
      loading: Array.from(preloadState.loading),
      failed: Array.from(preloadState.failed),
      networkSpeed: this.networkSpeed
    }
  }
}

// 导出单例
export const smartPreloader = new SmartPreloader()

/**
 * Vue Router 插件
 * 自动处理路由级别的代码分割和预加载
 */
export function createCodeSplittingPlugin() {
  return {
    install(app: unknown, router: unknown) {
      // 路由前置守卫
      router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
        // 预加载目标路由模块
        if (to.meta.preload !== false) {
          await preloadRouteModules(to)
        }
      })

      // 路由后置守卫
      router.afterEach((to: RouteLocationNormalized) => {
        // 预加载可能的下一个路由
        requestIdleCallback(() => {
          const moduleName = to.path.split('/')[1]
          const priority = MODULE_PRIORITY[moduleName] || 0

          // 高优先级模块，预加载相邻模块
          if (priority >= 80) {
            const adjacentModules = getAdjacentModules(moduleName)
            adjacentModules.forEach(module => {
              const components = getModuleComponents(module)
              components.forEach(comp => preloadComponent(comp, 'low'))
            })
          }
        })
      })

      // 提供全局方法
      app.config.globalProperties.$codeSplitting = {
        preloadComponent,
        preloadRouteModules,
        getPreloadStatus: () => smartPreloader.getPreloadStatus()
      }
    }
  }
}

/**
 * 获取相邻模块
 * @param currentModule 当前模块
 */
function getAdjacentModules(currentModule: string): string[] {
  const moduleRelations: Record<string, string[]> = {
    employee: ['organization', 'position', 'salary'],
    organization: ['employee', 'position'],
    position: ['employee', 'organization'],
    salary: ['employee', 'attendance'],
    attendance: ['employee', 'salary'],
    performance: ['employee', 'salary']
  }

  return moduleRelations[currentModule] || []
}

// 导出所有工具
export { preloadState, MODULE_PRIORITY }
