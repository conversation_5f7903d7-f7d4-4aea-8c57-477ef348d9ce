// 事件总线实现
export class EventBus {
  private events: Map<string, Set<Function>> = new Map()

  on(event: string, handler: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, new Set())
    }
    this.events.get(event)!.add(handler)
  }

  off(event: string, handler: Function): void {
    const handlers = this.events.get(event)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.events.delete(event)
      }
    }
  }

  once(event: string, handler: Function): void {
    const onceHandler = (...args: unknown[]) => {
      handler(...args)
      this.off(event, onceHandler)
    }
    this.on(event, onceHandler)
  }

  emit(event: string, ...args: unknown[]): void {
    const handlers = this.events.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (__error) {
          }
      })
    }
  }

  clear(): void {
    this.events.clear()
  }

  hasListeners(event: string): boolean {
    return this.events.has(event) && this.events.get(event)!.size > 0
  }

  getEventNames(): string[] {
    return Array.from(this.events.keys())
  }

  getListenerCount(event: string): number {
    return this.events.get(event)?.size || 0
  }
}
