/**
 * @name echartsUtil
 * @description ECharts 按需引入配置
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */

// 引入 echarts 核心模块
import * as echarts from 'echarts/core'

// 引入需要的图表类型
import {
  HrBar<PERSON>hart,
  Hr<PERSON>ine<PERSON>hart,
  <PERSON>r<PERSON><PERSON><PERSON>hart,
  HrRadar<PERSON>hart,
  Gauge<PERSON>hart,
  type BarSeriesOption,
  type LineSeriesOption,
  type PieSeriesOption,
  type RadarSeriesOption,
  type GaugeSeriesOption
} from 'echarts/charts'

// 引入需要的组件
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  type TitleComponentOption,
  type TooltipComponentOption,
  type GridComponentOption,
  type LegendComponentOption,
  type DataZoomComponentOption,
  type ToolboxComponentOption
} from 'echarts/components'

// 引入 Canvas 渲染器
import { CanvasRenderer } from 'echarts/renderers'

// 引入标签布局
import { LabelLayout, UniversalTransition } from 'echarts/features'

// 组合 Option 类型
export type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | PieSeriesOption
  | RadarSeriesOption
  | GaugeSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LegendComponentOption
  | DataZoomComponentOption
  | ToolboxComponentOption
>

// 注册必须的组件
echarts.use([
  // 图表
  BarChart,
  LineChart,
  PieChart,
  RadarChart,
  GaugeChart,
  // 组件
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  // 其他
  CanvasRenderer,
  LabelLayout,
  UniversalTransition
])

// 导出配置好的 echarts
export default echarts

// 导出类型
export type { EChartsOption } from 'echarts'

/**
 * 创建图表实例
 * @param dom DOM 元素
 * @param theme 主题
 * @param opts 初始化选项
 */
export function createChart(
  dom: HTMLElement,
  theme?: string | object,
  opts?: echarts.EChartsInitOpts
) {
  return echarts.init(dom, theme, opts)
}

/**
 * 获取默认图表配置
 */
export function getDefaultOption(): Partial<ECOption> {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    color: [
      '#409EFF',
      '#67C23A',
      '#E6A23C',
      '#F56C6C',
      '#909399',
      '#B1D3F0',
      '#B3E19D',
      '#F3D19E',
      '#FAB6B6',
      '#C8C9CC'
    ]
  }
}

/**
 * 销毁图表实例
 * @param chart 图表实例
 */
export function disposeChart(chart: echarts.ECharts | null | undefined) {
  if (chart && !chart.isDisposed()) {
    chart.dispose()
  }
}

/**
 * 响应式图表大小调整
 * @param chart 图表实例
 */
export function resizeChart(chart: echarts.ECharts | null | undefined) {
  if (chart && !chart.isDisposed()) {
    chart.resize()
  }
}
