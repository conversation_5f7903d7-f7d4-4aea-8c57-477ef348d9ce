/**
 * 图表渲染优化工具类
 * 提供ECharts性能优化策略和最佳实践
 */

import echarts, { createChart } from '@/utils/echarts'
import type { ECOption } from '@/utils/echarts'

export interface OptimizationOptions {
  enableLazyUpdate?: boolean // 启用懒更新
  enableProgressive?: boolean // 启用渐进式渲染
  progressiveThreshold?: number // 渐进式渲染阈值
  enableSampling?: boolean // 启用数据采样
  samplingThreshold?: number // 采样阈值
  enableAnimation?: boolean // 启用动画
  animationDuration?: number // 动画时长
  enableDataZoom?: boolean // 启用数据缩放
  enableToolbox?: boolean // 启用工具箱
}

export class ChartOptimizer {
  private static instance: ChartOptimizer
  private chartPool: Map<string, echarts.ECharts> = new Map()
  private resizeObserver: ResizeObserver | null = null
  private defaultOptions: OptimizationOptions = {
    enableLazyUpdate: true,
    enableProgressive: true,
    progressiveThreshold: 5000,
    enableSampling: true,
    samplingThreshold: 10000,
    enableAnimation: true,
    animationDuration: 300,
    enableDataZoom: true,
    enableToolbox: true
  }

  private constructor() {
    this.initResizeObserver()
  }

  static getInstance(): ChartOptimizer {
    if (!ChartOptimizer.instance) {
      ChartOptimizer.instance = new ChartOptimizer()
    }
    return ChartOptimizer.instance
  }

  /**
   * 初始化或获取图表实例
   */
  getChart(container: HTMLElement, chartId: string, theme?: string | object): echarts.ECharts {
    // 尝试从池中获取
    let chart = this.chartPool.get(chartId)

    if (chart && !chart.isDisposed()) {
      // 检查容器是否变化
      if (chart.getDom() === container) {
        return chart
      } else {
        // 容器变化，销毁旧实例
        this.disposeChart(chartId)
      }
    }

    // 创建新实例
    chart = createChart(container, theme, {
      renderer: 'canvas', // 使用canvas渲染器，性能更好
      useDirtyRect: true // 启用脏矩形渲染
    })

    this.chartPool.set(chartId, chart)

    // 添加到resize观察器
    if (this.resizeObserver) {
      this.resizeObserver.observe(container)
    }

    return chart
  }

  /**
   * 优化图表配置
   */
  optimizeOption(
    option: EChartsOption,
    dataSize: number,
    customOptions?: OptimizationOptions
  ): EChartsOption {
    const opts = { ...this.defaultOptions, ...customOptions }
    const optimizedOption: EChartsOption = { ...option }

    // 基础优化
    this.applyBaseOptimization(optimizedOption, opts)

    // 根据数据量优化
    if (dataSize > opts.samplingThreshold!) {
      this.applyLargeDataOptimization(optimizedOption, dataSize, opts)
    } else if (dataSize > opts.progressiveThreshold!) {
      this.applyProgressiveOptimization(optimizedOption, opts)
    }

    // 系列优化
    if (optimizedOption.series) {
      this.optimizeSeries(optimizedOption.series as unknown[], dataSize, opts)
    }

    return optimizedOption
  }

  /**
   * 基础优化配置
   */
  private applyBaseOptimization(option: EChartsOption, opts: OptimizationOptions): void {
    // 动画优化
    if (
      !opts.enableAnimation ||
      (option.series && (option.series as unknown[]).some(s => s.data?.length > 1000))
    ) {
      option.animation = false
    } else {
      option.animation = true
      option.animationDuration = opts.animationDuration
      option.animationEasing = 'cubicOut'
    }

    // 工具箱优化
    if (opts.enableToolbox) {
      option.toolbox = {
        feature: {
          dataZoom: { show: opts.enableDataZoom },
          restore: { show: true },
          saveAsImage: { show: true, pixelRatio: 2 }
        }
      }
    }

    // 坐标轴优化
    if (option.xAxis) {
      this.optimizeAxis(option.xAxis as unknown)
    }
    if (option.yAxis) {
      this.optimizeAxis(option.yAxis as unknown)
    }
  }

  /**
   * 大数据量优化
   */
  private applyLargeDataOptimization(
    option: EChartsOption,
    dataSize: number,
    opts: OptimizationOptions
  ): void {
    // 禁用动画
    option.animation = false

    // 启用大数据模式
    option.progressive = Math.floor(dataSize / 10)
    option.progressiveThreshold = opts.progressiveThreshold

    // 简化视觉效果
    if (option.visualMap) {
      ;(option.visualMap as unknown).realtime = false
    }

    // 使用 WebGL 渲染器提示
    }

  /**
   * 渐进式渲染优化
   */
  private applyProgressiveOptimization(option: EChartsOption, opts: OptimizationOptions): void {
    option.progressive = opts.progressiveThreshold
    option.progressiveThreshold = opts.progressiveThreshold

    // 降低动画复杂度
    if (option.animation) {
      option.animationDuration = Math.min(opts.animationDuration!, 200)
    }
  }

  /**
   * 优化坐标轴
   */

  private optimizeAxis(axis: unknown): void {
    if (!axis) return

    const axisArray = Array.isArray(axis) ? axis : [axis]

    axisArray.forEach(ax => {
      // 优化轴线
      if (ax.axisLine) {
        ax.axisLine.lineStyle = {
          ...ax.axisLine.lineStyle,
          width: 1
        }
      }

      // 优化刻度
      if (ax.axisTick) {
        ax.axisTick.lineStyle = {
          ...ax.axisTick.lineStyle,
          width: 1
        }
      }

      // 优化标签
      if (ax.axisLabel) {
        // 自动隐藏重叠标签
        ax.axisLabel.hideOverlap = true

        // 大数据量时减少标签数量
        if (ax.data && ax.data.length > 100) {
          ax.axisLabel.interval = 'auto'
        }
      }

      // 优化分割线
      if (ax.splitLine) {
        ax.splitLine.lineStyle = {
          ...ax.splitLine.lineStyle,
          type: 'dashed',
          opacity: 0.3
        }
      }
    })
  }

  /**
   * 优化系列配置
   */
  private optimizeSeries(series: unknown[], dataSize: number, opts: OptimizationOptions): void {
    series.forEach(s => {
      // 大数据量优化
      if (s.data && s.data.length > opts.samplingThreshold!) {
        // 启用采样
        if (s.type === 'line') {
          s.sampling = 'lttb' // 使用 LTTB 采样算法
        }

        // 禁用标签
        s.label = { show: false }

        // 简化样式
        s.symbol = 'none'
        s.lineStyle = { width: 1 }
      }

      // 折线图优化
      if (s.type === 'line') {
        // 大数据量时禁用区域填充
        if (dataSize > 5000) {
          s.areaStyle = null
        }

        // 启用裁剪
        s.clip = true

        // 优化采样
        if (opts.enableSampling && dataSize > opts.samplingThreshold!) {
          s.sampling = 'lttb'
          s.progressive = opts.progressiveThreshold
        }
      }

      // 散点图优化
      if (s.type === 'scatter') {
        // 大数据量时使用更小的符号
        if (dataSize > 1000) {
          s.symbolSize = Math.max(2, 10 - Math.floor(dataSize / 1000))
        }

        // 启用大规模散点图优化
        s.large = dataSize > 5000
        s.largeThreshold = 5000
      }

      // 柱状图优化
      if (s.type === 'bar') {
        // 大数据量时简化渐变
        if (dataSize > 1000 && s.itemStyle?.color) {
          // 将渐变色改为纯色
          if (typeof s.itemStyle.color === 'object') {
            s.itemStyle.color = '#409eff'
          }
        }
      }

      // 饼图优化
      if (s.type === 'pie') {
        // 限制饼图项数
        if (s.data && s.data.length > 20) {
          // 合并小项
          const sortedData = [...s.data].sort((a, b) => b.value - a.value)
          const mainData = sortedData.slice(0, 19)
          const otherValue = sortedData.slice(19).reduce((sum, item) => sum + item.value, 0)

          s.data = [...mainData, { name: '其他', value: otherValue }]
        }
      }
    })
  }

  /**
   * 更新图表数据（增量更新）
   */
  updateData(
    chart: echarts.ECharts,

    newData: unknown[],
    seriesIndex: number = 0
  ): void {
    const option = chart.getOption() as unknown

    if (option.series && option.series[seriesIndex]) {
      // 使用 setOption 的 notMerge 模式进行增量更新
      chart.setOption(
        {
          series: [
            {
              id: option.series[seriesIndex].id || seriesIndex,
              data: newData
            }
          ]
        },
        {
          notMerge: false,
          lazyUpdate: true
        }
      )
    }
  }

  /**
   * 批量更新多个图表
   */
  batchUpdate(
    updates: Array<{
      chartId: string

      data: unknown[]
      seriesIndex?: number
    }>
  ): void {
    // 暂停所有图表渲染
    this.chartPool.forEach(chart => {
      chart.group = 'batch_update'
    })

    // 批量更新
    updates.forEach(({ chartId, data, seriesIndex }) => {
      const chart = this.chartPool.get(chartId)
      if (chart && !chart.isDisposed()) {
        this.updateData(chart, data, seriesIndex)
      }
    })

    // 统一触发渲染
    echarts.connect('batch_update')
  }

  /**
   * 销毁图表
   */
  disposeChart(chartId: string): void {
    const chart = this.chartPool.get(chartId)
    if (chart && !chart.isDisposed()) {
      // 移除resize观察
      if (this.resizeObserver) {
        const container = chart.getDom()
        if (container) {
          this.resizeObserver.unobserve(container)
        }
      }

      chart.dispose()
      this.chartPool.delete(chartId)
    }
  }

  /**
   * 销毁所有图表
   */
  disposeAll(): void {
    this.chartPool.forEach((_, chartId) => {
      this.disposeChart(chartId)
    })
  }

  /**
   * 初始化 ResizeObserver
   */
  private initResizeObserver(): void {
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(entries => {
        entries.forEach(entry => {
          // 查找对应的图表
          this.chartPool.forEach(chart => {
            if (chart.getDom() === entry.target && !chart.isDisposed()) {
              chart.resize()
            }
          })
        })
      })
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(chartId: string): {
    renderTime: number
    dataSize: number
    fps: number
  } | null {
    const chart = this.chartPool.get(chartId)
    if (!chart || chart.isDisposed()) {
      return null
    }

    // 这里可以集成实际的性能监控
    return {
      renderTime: 0,
      dataSize: 0,
      fps: 60
    }
  }

  /**
   * 导出优化后的图片
   */
  exportImage(
    chartId: string,
    options?: {
      pixelRatio?: number
      backgroundColor?: string
      excludeComponents?: string[]
    }
  ): string | null {
    const chart = this.chartPool.get(chartId)
    if (!chart || chart.isDisposed()) {
      return null
    }

    return chart.getDataURL({
      pixelRatio: options?.pixelRatio || 2,
      backgroundColor: options?.backgroundColor || '#fff',
      excludeComponents: options?.excludeComponents || ['toolbox']
    })
  }
}

// 导出单例
export const chartOptimizer = ChartOptimizer.getInstance()

// 导出优化预设
export const OptimizationPresets = {
  // 实时数据预设
  realtime: {
    enableAnimation: false,
    enableProgressive: false,
    enableLazyUpdate: true
  } as OptimizationOptions,

  // 大数据预设
  bigData: {
    enableAnimation: false,
    enableProgressive: true,
    progressiveThreshold: 5000,
    enableSampling: true,
    samplingThreshold: 10000
  } as OptimizationOptions,

  // 移动端预设
  mobile: {
    enableAnimation: true,
    animationDuration: 200,
    enableToolbox: false,
    enableDataZoom: false
  } as OptimizationOptions,

  // 打印预设
  print: {
    enableAnimation: false,
    enableToolbox: false,
    enableDataZoom: false
  } as OptimizationOptions
}
