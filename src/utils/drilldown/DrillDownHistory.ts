import type { DrillDownContext, DrillDownHistoryEntry } from '@/types/drilldown'

export class DrillDownHistory {
  private stack: DrillDownHistoryEntry[] = []
  private currentIndex: number = -1
  private maxSize: number = 50

  constructor(maxSize: number = 50) {
    this.maxSize = maxSize
  }

  push(entry: DrillDownHistoryEntry): void {
    // 如果不是在最后位置，删除当前位置之后的历史
    if (this.currentIndex < this.stack.length - 1) {
      this.stack = this.stack.slice(0, this.currentIndex + 1)
    }

    // 添加新记录
    this.stack.push(this.deepClone(entry))
    this.currentIndex++

    // 限制历史大小
    if (this.stack.length > this.maxSize) {
      this.stack.shift()
      this.currentIndex--
    }
  }

  back(): DrillDownHistoryEntry | null {
    if (this.canGoBack()) {
      this.currentIndex--
      return this.deepClone(this.stack[this.currentIndex])
    }
    return null
  }

  forward(): DrillDownHistoryEntry | null {
    if (this.canGoForward()) {
      this.currentIndex++
      return this.deepClone(this.stack[this.currentIndex])
    }
    return null
  }

  jumpTo(index: number): DrillDownHistoryEntry | null {
    if (index >= 0 && index < this.stack.length) {
      this.currentIndex = index
      return this.deepClone(this.stack[this.currentIndex])
    }
    return null
  }

  canGoBack(): boolean {
    return this.currentIndex > 0
  }

  canGoForward(): boolean {
    return this.currentIndex < this.stack.length - 1
  }

  getCurrentIndex(): number {
    return this.currentIndex
  }

  getLength(): number {
    return this.stack.length
  }

  getAll(): DrillDownHistoryEntry[] {
    return this.stack.map(entry => this.deepClone(entry))
  }

  clear(): void {
    this.stack = []
    this.currentIndex = -1
  }

  // 获取当前历史记录
  getCurrent(): DrillDownHistoryEntry | null {
    if (this.currentIndex >= 0 && this.currentIndex < this.stack.length) {
      return this.deepClone(this.stack[this.currentIndex])
    }
    return null
  }

  // 获取历史记录摘要
  getSummary(): {
    total: number
    currentIndex: number
    canGoBack: boolean
    canGoForward: boolean
  } {
    return {
      total: this.stack.length,
      currentIndex: this.currentIndex,
      canGoBack: this.canGoBack(),
      canGoForward: this.canGoForward()
    }
  }

  // 深拷贝
  private deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj))
  }
}
