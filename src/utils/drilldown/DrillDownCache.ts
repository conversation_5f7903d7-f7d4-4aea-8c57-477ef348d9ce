import type { DrillDownPath, DrillDownCacheEntry } from '@/types/drilldown'

export class DrillDownCache {
  private cache: Map<string, DrillDownCacheEntry> = new Map()
  private maxAge: number
  private maxSize: number = 100
  private cleanupInterval: number = 60000 // 1分钟
  private cleanupTimer: NodeJS.Timer | null = null

  constructor(maxAge: number = 5 * 60 * 1000) {
    this.maxAge = maxAge
    this.startCleanup()
  }

  set(key: string, data: unknown): void {
    const entry: DrillDownCacheEntry = {
      data: this.deepClone(data),
      timestamp: Date.now(),
      hits: 0
    }

    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      this.evictOldest()
    }

    this.cache.set(key, entry)
  }

  get(key: string): unknown | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return null
    }

    // 更新命中次数
    entry.hits++

    return this.deepClone(entry.data)
  }

  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  generateKey(path: DrillDownPath[]): string {
    if (path.length === 0) return 'root'

    return path.map(p => `${p.dimension}:${this.sanitizeValue(p.value)}`).join('/')
  }

  // 从路径生成部分键（用于批量清除）
  generatePartialKey(path: DrillDownPath[]): string {
    if (path.length === 0) return ''

    return path.map(p => `${p.dimension}:${this.sanitizeValue(p.value)}`).join('/')
  }

  // 清除指定路径下的所有缓存
  clearPath(path: DrillDownPath[]): void {
    const partialKey = this.generatePartialKey(path)
    const keysToDelete: string[] = []

    this.cache.forEach((_, key) => {
      if (key.startsWith(partialKey)) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => this.cache.delete(key))
  }

  // 获取缓存统计信息
  getStats(): {
    size: number
    hits: number
    misses: number
    hitRate: number
  } {
    let totalHits = 0
    let totalAccess = 0

    this.cache.forEach(entry => {
      totalHits += entry.hits
      totalAccess += entry.hits + 1 // +1 for initial set
    })

    return {
      size: this.cache.size,
      hits: totalHits,
      misses: totalAccess - totalHits,
      hitRate: totalAccess > 0 ? totalHits / totalAccess : 0
    }
  }

  // 获取缓存详情
  getDetails(): Array<{
    key: string
    size: number
    age: number
    hits: number
  }> {
    const now = Date.now()
    const details: unknown[] = []

    this.cache.forEach((entry, key) => {
      details.push({
        key,
        size: this.getDataSize(entry.data),
        age: now - entry.timestamp,
        hits: entry.hits
      })
    })

    return details.sort((a, b) => b.hits - a.hits)
  }

  // 预热缓存
  warmUp(keys: string[], fetcher: (key: string) => Promise<unknown>): Promise<void[]> {
    const promises = keys.map(async key => {
      if (!this.has(key)) {
        try {
          const data = await fetcher(key)
          this.set(key, data)
        } catch (__error) {
          }
      }
    })

    return Promise.all(promises)
  }

  // 停止清理定时器
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
  }

  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.cleanupInterval)
  }

  private cleanup(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    this.cache.forEach((entry, key) => {
      if (now - entry.timestamp > this.maxAge) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => this.cache.delete(key))
  }

  private evictOldest(): void {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    this.cache.forEach((entry, key) => {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp
        oldestKey = key
      }
    })

    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }

  private isExpired(entry: DrillDownCacheEntry): boolean {
    return Date.now() - entry.timestamp > this.maxAge
  }

  private sanitizeValue(value: unknown): string {
    if (value === null || value === undefined) return 'null'
    if (typeof value === 'object') return JSON.stringify(value)
    return String(value).replace(/[/:]/g, '_')
  }

  private deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj))
  }

  private getDataSize(data: unknown): number {
    // 简单估算数据大小（字节）
    return JSON.stringify(data).length
  }
}
