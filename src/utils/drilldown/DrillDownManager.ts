import type {
  DrillDownConfig,
  Drill<PERSON>ownContext,
  Drill<PERSON>ownPath,
  <PERSON>illDownParams,
  DrillDownEvent,
  BreadcrumbItem,
  DrillDownHistoryEntry,
  IDrillDownManager
} from '@/types/drilldown'
import { DrillDownHistory } from './DrillDownHistory'
import { DrillDownCache } from './DrillDownCache'
import { PermissionController } from './PermissionController'
import { EventBus } from '../EventBus'

export class DrillDownManager implements IDrillDownManager {
  private config: DrillDownConfig
  private context: DrillDownContext
  private history: DrillDownHistory
  private cache: DrillDownCache
  private permissionController: PermissionController
  private eventBus: EventBus

  private currentData: unknown[] = []

  constructor(config: DrillDownConfig) {
    this.config = config
    this.context = this.initContext()
    this.history = new DrillDownHistory()
    this.cache = new DrillDownCache(config.cacheTimeout)
    this.permissionController = new PermissionController(config.permissions || [])
    this.eventBus = new EventBus()
  }

  private initContext(): DrillDownContext {
    return {
      path: [],
      currentLevel: 0,
      maxLevel: this.config.maxDepth,
      canDrillDown: true,
      canDrillUp: false
    }
  }

  async drillDown(params: DrillDownParams): Promise<void> {
    // 检查是否可以继续下钻
    if (!this.canDrillDown()) {
      throw new Error('已达到最大钻取深度')
    }

    // 权限检查
    const hasPermission = await this.permissionController.checkPermission(
      params.dimension,
      this.context.currentLevel + 1,
      params.filters
    )

    if (!hasPermission) {
      throw new Error('无权限执行此钻取操作')
    }

    // 构建新路径节点
    const newPath: DrillDownPath = {
      id: `${params.dimension}_${params.value}_${Date.now()}`,
      level: this.context.currentLevel + 1,
      dimension: params.dimension,
      value: params.value,
      label: params.label,
      filters: { ...this.getCurrentFilters(), ...params.filters },
      metadata: params.metadata,
      timestamp: Date.now()
    }

    // 更新上下文
    this.context.path.push(newPath)
    this.context.currentLevel++
    this.updateContextFlags()

    // 保存历史
    if (this.config.enableHistory) {
      this.history.push({
        context: { ...this.context },
        timestamp: Date.now(),
        action: 'drill-down'
      })
    }

    // 触发事件
    this.emit('drill-down', {
      path: this.context.path,
      level: this.context.currentLevel,
      dimension: params.dimension
    })

    // 执行回调
    this.config.onDrillDown?.(this.context)
  }

  async drillUp(levels: number = 1): Promise<void> {
    if (!this.canDrillUp()) {
      return
    }

    const targetLevel = Math.max(0, this.context.currentLevel - levels)
    const removedPaths = []

    while (this.context.currentLevel > targetLevel && this.context.path.length > 0) {
      removedPaths.push(this.context.path.pop())
      this.context.currentLevel--
    }

    this.updateContextFlags()

    // 保存历史
    if (this.config.enableHistory) {
      this.history.push({
        context: { ...this.context },
        timestamp: Date.now(),
        action: 'drill-up'
      })
    }

    // 触发事件
    this.emit('drill-up', {
      path: this.context.path,
      level: this.context.currentLevel
    })

    // 执行回调
    this.config.onDrillUp?.(this.context)
  }

  async drillTo(path: DrillDownPath[]): Promise<void> {
    // 直接跳转到指定路径
    this.context.path = [...path]
    this.context.currentLevel = path.length
    this.updateContextFlags()

    // 保存历史
    if (this.config.enableHistory) {
      this.history.push({
        context: { ...this.context },
        timestamp: Date.now(),
        action: 'jump'
      })
    }

    // 触发事件
    this.emit('drill-jump', {
      path: this.context.path,
      level: this.context.currentLevel
    })
  }

  reset(): void {
    this.context = this.initContext()
    this.currentData = []

    if (this.config.enableHistory) {
      this.history.push({
        context: { ...this.context },
        timestamp: Date.now(),
        action: 'reset'
      })
    }

    this.emit('drill-reset', {
      path: [],
      level: 0
    })

    this.config.onReset?.()
  }

  // 导航操作
  goBack(): void {
    const previousContext = this.history.back()
    if (previousContext) {
      this.context = { ...previousContext.context }
      this.emit('drill-history', {
        path: this.context.path,
        level: this.context.currentLevel
      })
    }
  }

  goForward(): void {
    const nextContext = this.history.forward()
    if (nextContext) {
      this.context = { ...nextContext.context }
      this.emit('drill-history', {
        path: this.context.path,
        level: this.context.currentLevel
      })
    }
  }

  jumpTo(index: number): void {
    const targetContext = this.history.jumpTo(index)
    if (targetContext) {
      this.context = { ...targetContext.context }
      this.emit('drill-history', {
        path: this.context.path,
        level: this.context.currentLevel
      })
    }
  }

  // 状态查询
  getCurrentContext(): DrillDownContext {
    return { ...this.context }
  }

  getBreadcrumb(): BreadcrumbItem[] {
    const items: BreadcrumbItem[] = []

    // 添加根节点
    items.push({
      id: 'root',
      label: '全部',
      level: 0,
      dimension: '',
      clickable: this.context.currentLevel > 0,
      icon: 'home'
    })

    // 添加路径节点
    this.context.path.forEach((path, index) => {
      const dimension = this.config.dimensions.find(d => d.key === path.dimension)
      const level = dimension?.levels[path.level - 1]

      items.push({
        id: path.id,
        label: path.label,
        level: path.level,
        dimension: path.dimension,
        clickable: index < this.context.path.length - 1,
        icon: level?.component
      })
    })

    return items
  }

  getHistory(): DrillDownHistoryEntry[] {
    return this.history.getAll()
  }

  canDrillDown(): boolean {
    return this.context.canDrillDown
  }

  canDrillUp(): boolean {
    return this.context.canDrillUp
  }

  // 数据操作

  getCurrentData(): unknown[] {
    return this.currentData
  }

  setCurrentData(data: unknown[]): void {
    this.currentData = data
  }

  async refreshCurrentLevel(): Promise<void> {
    // 清除当前层级的缓存
    const cacheKey = this.cache.generateKey(this.context.path)
    this.cache.delete(cacheKey)

    // 触发数据刷新事件
    this.emit('drill-refresh', {
      path: this.context.path,
      level: this.context.currentLevel
    })
  }

  // 事件订阅
  on(event: string, handler: Function): void {
    this.eventBus.on(event, handler)
  }

  off(event: string, handler: Function): void {
    this.eventBus.off(event, handler)
  }

  // 辅助方法
  private updateContextFlags(): void {
    this.context.canDrillUp = this.context.currentLevel > 0
    this.context.canDrillDown = this.context.currentLevel < this.context.maxLevel
  }

  private getCurrentFilters(): Record<string, unknown> {
    return this.context.path.reduce((filters, path) => {
      return { ...filters, ...path.filters }
    }, {})
  }

  private emit(type: string, data: unknown): void {
    const event: DrillDownEvent = {
      type: type as unknown,
      timestamp: Date.now(),
      ...data
    }
    this.eventBus.emit(type, event)
  }

  // 获取当前维度配置
  getCurrentDimension(): string | null {
    const lastPath = this.context.path[this.context.path.length - 1]
    return lastPath?.dimension || null
  }

  // 获取下一层级信息
  getNextLevelInfo(): { dimension: string; level: DrillDownLevel } | null {
    if (!this.canDrillDown()) return null

    const currentDimension = this.getCurrentDimension()
    if (!currentDimension) {
      // 如果当前没有维度，返回第一个维度的第一层
      const firstDimension = this.config.dimensions[0]
      if (firstDimension && firstDimension.levels[0]) {
        return {
          dimension: firstDimension.key,
          level: firstDimension.levels[0]
        }
      }
    } else {
      // 查找当前维度的下一层
      const dimension = this.config.dimensions.find(d => d.key === currentDimension)
      if (dimension) {
        const nextLevel = dimension.levels[this.context.currentLevel]
        if (nextLevel) {
          return {
            dimension: currentDimension,
            level: nextLevel
          }
        }
      }
    }

    return null
  }

  // 生成缓存键
  getCacheKey(): string {
    return this.cache.generateKey(this.context.path)
  }

  // 从缓存获取数据

  getCachedData(): unknown | null {
    if (!this.config.enableCache) return null
    return this.cache.get(this.getCacheKey())
  }

  // 设置缓存数据

  setCachedData(data: unknown): void {
    if (this.config.enableCache) {
      this.cache.set(this.getCacheKey(), data)
    }
  }
}
