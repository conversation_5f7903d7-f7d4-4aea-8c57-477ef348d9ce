import type { DrillDownPermission, PermissionCondition } from '@/types/drilldown'
import { useUserStore } from '@/stores/modules/user'

export class PermissionController {
  private permissions: DrillDownPermission[]

  constructor(permissions: DrillDownPermission[]) {
    this.permissions = permissions
  }

  async checkPermission(
    dimension: string,
    targetLevel: number,
    context?: Record<string, unknown>
  ): Promise<boolean> {
    const userStore = useUserStore()
    const userRole = userStore.currentUser?.role || 'guest'

    // 查找匹配的权限规则
    const permission = this.permissions.find(p => p.role === userRole && p.dimension === dimension)

    // 如果没有配置权限，默认允许
    if (!permission) {
      return true
    }

    // 检查层级限制
    if (targetLevel > permission.maxLevel) {
      return false
    }

    // 检查条件限制
    if (permission.conditions && context) {
      return this.checkConditions(permission.conditions, context)
    }

    return true
  }

  checkConditions(conditions: PermissionCondition[], context: Record<string, unknown>): boolean {
    return conditions.every(condition => {
      const value = context[condition.field]

      switch (condition.operator) {
        case 'eq':
          return value === condition.value

        case 'ne':
          return value !== condition.value

        case 'in':
          return Array.isArray(condition.value) && condition.value.includes(value)

        case 'notIn':
          return Array.isArray(condition.value) && !condition.value.includes(value)

        case 'gt':
          return Number(value) > Number(condition.value)

        case 'gte':
          return Number(value) >= Number(condition.value)

        case 'lt':
          return Number(value) < Number(condition.value)

        case 'lte':
          return Number(value) <= Number(condition.value)

        default:
          return false
      }
    })
  }

  // 获取用户在指定维度的最大钻取深度
  getMaxLevel(dimension: string): number {
    const userStore = useUserStore()
    const userRole = userStore.currentUser?.role || 'guest'

    const permission = this.permissions.find(p => p.role === userRole && p.dimension === dimension)

    return permission?.maxLevel ?? Infinity
  }

  // 获取用户可访问的维度列表
  getAllowedDimensions(): string[] {
    const userStore = useUserStore()
    const userRole = userStore.currentUser?.role || 'guest'

    return this.permissions.filter(p => p.role === userRole).map(p => p.dimension)
  }

  // 添加权限规则
  addPermission(permission: DrillDownPermission): void {
    const index = this.permissions.findIndex(
      p => p.role === permission.role && p.dimension === permission.dimension
    )

    if (index >= 0) {
      this.permissions[index] = permission
    } else {
      this.permissions.push(permission)
    }
  }

  // 移除权限规则
  removePermission(role: string, dimension: string): void {
    const index = this.permissions.findIndex(p => p.role === role && p.dimension === dimension)

    if (index >= 0) {
      this.permissions.splice(index, 1)
    }
  }

  // 获取所有权限规则
  getAllPermissions(): DrillDownPermission[] {
    return [...this.permissions]
  }

  // 批量更新权限
  updatePermissions(permissions: DrillDownPermission[]): void {
    this.permissions = [...permissions]
  }
}
