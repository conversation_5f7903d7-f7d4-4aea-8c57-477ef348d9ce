interface EmployeeNumberRule {
  id: string
  ruleName: string
  ruleCode: string
  ruleType: 'auto' | 'manual' | 'hybrid'
  description: string
  enabled: boolean
  priority: number
  applicableTypes: string[]
  rulePattern: RulePattern
  validationRules: ValidationRule[]
  assignmentSettings: AssignmentSettings
  createTime: string
  updateTime: string
  createdBy: string
  updatedBy: string
}

interface RulePattern {
  format: string
  segments: PatternSegment[]
  separator?: string
  totalLength?: number
  padding?: PaddingConfig
}

interface PatternSegment {
  type: 'fixed' | 'year' | 'month' | 'department' | 'type' | 'sequence' | 'custom'
  value?: string
  length: number
  format?: string
  source?: string
  required: boolean
  position: number
}

interface PaddingConfig {
  character: string
  position: 'left' | 'right'
  enabled: boolean
}

interface ValidationRule {
  id: string
  type: 'uniqueness' | 'format' | 'length' | 'range' | 'custom'
  expression: string
  errorMessage: string
  enabled: boolean
}

interface AssignmentSettings {
  autoAssign: boolean
  allowManualOverride: boolean
  reservationPool: {
    enabled: boolean
    poolSize: number
    warningThreshold: number
  }
  sequenceSettings: {
    startValue: number
    increment: number
    resetFrequency: 'never' | 'yearly' | 'monthly'
    resetDate?: string
  }
}

interface ImportOptions {
  format: 'json' | 'excel' | 'csv' | 'xml'
  validateOnly: boolean
  overwriteExisting: boolean
  enableBackup: boolean
  batchSize: number
  skipErrors: boolean
}

interface ImportResult {
  success: boolean
  totalRecords: number
  successCount: number
  failureCount: number
  skippedCount: number
  validationErrors: ValidationError[]
  importedRules: EmployeeNumberRule[]
  backupFile?: string
  processTime: number
  error?: string
}

interface ValidationError {
  rowIndex: number
  field: string

  value: unknown
  errorType: 'required' | 'format' | 'duplicate' | 'invalid' | 'reference'
  message: string
  severity: 'error' | 'warning'
}

interface ExistingRuleCheck {
  exists: boolean
  conflictRule?: EmployeeNumberRule
  conflictType: 'code' | 'pattern' | 'priority'
}

/**
 * CLEAN-AUX-012: 工号规则导入功能
 * 支持多格式导入和完整验证的员工工号规则管理系统
 */
export class EmployeeNumberRuleImport {
  private existingRules: Map<string, EmployeeNumberRule> = new Map()

  // 预定义员工类型
  private readonly employeeTypes = [
    { value: 'regular', label: '在编员工', prefix: 'R' },
    { value: 'contract', label: '非编员工', prefix: 'C' },
    { value: 'temporary', label: '临时工', prefix: 'T' },
    { value: 'intern', label: '实习生', prefix: 'I' },
    { value: 'consultant', label: '顾问', prefix: 'X' },
    { value: 'retired', label: '返聘人员', prefix: 'H' }
  ]

  // 预定义部门代码
  private readonly departmentCodes = [
    { value: 'tech', label: '技术部', code: '01' },
    { value: 'hr', label: '人事部', code: '02' },
    { value: 'finance', label: '财务部', code: '03' },
    { value: 'admin', label: '行政部', code: '04' },
    { value: 'sales', label: '销售部', code: '05' }
  ]

  constructor() {
    this.initializeMockData()
  }

  // 初始化模拟数据
  private initializeMockData() {
    const mockRules: EmployeeNumberRule[] = [
      {
        id: 'rule_001',
        ruleName: '在编员工工号规则',
        ruleCode: 'REGULAR_EMP_RULE',
        ruleType: 'auto',
        description: '在编员工自动分配工号规则：R+年份+部门代码+4位序号',
        enabled: true,
        priority: 1,
        applicableTypes: ['regular'],
        rulePattern: {
          format: 'R{YYYY}{DEPT}{XXXX}',
          segments: [
            {
              type: 'fixed',
              value: 'R',
              length: 1,
              required: true,
              position: 1
            },
            {
              type: 'year',
              format: 'YYYY',
              length: 4,
              required: true,
              position: 2
            },
            {
              type: 'department',
              length: 2,
              source: 'department_code',
              required: true,
              position: 3
            },
            {
              type: 'sequence',
              length: 4,
              required: true,
              position: 4
            }
          ],
          totalLength: 11,
          padding: {
            character: '0',
            position: 'left',
            enabled: true
          }
        },
        validationRules: [
          {
            id: 'val_001',
            type: 'uniqueness',
            expression: 'unique',
            errorMessage: '工号必须唯一',
            enabled: true
          },
          {
            id: 'val_002',
            type: 'length',
            expression: 'length==11',
            errorMessage: '工号长度必须为11位',
            enabled: true
          }
        ],
        assignmentSettings: {
          autoAssign: true,
          allowManualOverride: false,
          reservationPool: {
            enabled: true,
            poolSize: 100,
            warningThreshold: 20
          },
          sequenceSettings: {
            startValue: 1,
            increment: 1,
            resetFrequency: 'yearly',
            resetDate: '01-01'
          }
        },
        createTime: '2025-01-01T00:00:00.000Z',
        updateTime: '2025-01-01T00:00:00.000Z',
        createdBy: 'admin',
        updatedBy: 'admin'
      }
    ]

    mockRules.forEach(rule => {
      this.existingRules.set(rule.ruleCode, rule)
    })
  }

  // 导入工号规则
  async importRules(
    data: unknown[],
    options: ImportOptions = {
      format: 'excel',
      validateOnly: false,
      overwriteExisting: false,
      enableBackup: true,
      batchSize: 100,
      skipErrors: false
    }
  ): Promise<ImportResult> {
    const startTime = Date.now()

    try {
      // 1. 数据预处理
      const processedData = this.preprocessData(data, options.format)

      // 2. 数据验证
      const validationResult = await this.validateImportData(processedData, options)

      if (validationResult.hasErrors && !options.skipErrors) {
        return {
          success: false,
          totalRecords: processedData.length,
          successCount: 0,
          failureCount: processedData.length,
          skippedCount: 0,
          validationErrors: validationResult.errors,
          importedRules: [],
          processTime: Date.now() - startTime,
          error: '数据验证失败，请修正错误后重试'
        }
      }

      // 3. 仅验证模式
      if (options.validateOnly) {
        return {
          success: true,
          totalRecords: processedData.length,
          successCount: validationResult.validCount,
          failureCount: validationResult.errorCount,
          skippedCount: 0,
          validationErrors: validationResult.errors,
          importedRules: [],
          processTime: Date.now() - startTime
        }
      }

      // 4. 备份现有规则
      let backupFile: string | undefined
      if (options.enableBackup) {
        backupFile = await this.createBackup()
      }

      // 5. 批量导入
      const importResult = await this.performBatchImport(validationResult.validData, options)

      return {
        success: true,
        totalRecords: processedData.length,
        successCount: importResult.successCount,
        failureCount: importResult.failureCount,
        skippedCount: validationResult.errorCount,
        validationErrors: [...validationResult.errors, ...importResult.errors],
        importedRules: importResult.importedRules,
        backupFile,
        processTime: Date.now() - startTime
      }
    } catch (__error) {
      return {
        success: false,
        totalRecords: data.length,
        successCount: 0,
        failureCount: data.length,
        skippedCount: 0,
        validationErrors: [],
        importedRules: [],
        processTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : '导入失败'
      }
    }
  }

  // 从文件导入
  async importFromFile(file: File, options: Partial<ImportOptions> = {}): Promise<ImportResult> {
    try {
      const format = this.detectFileFormat(file.name)
      const data = await this.parseFile(file, format)

      return this.importRules(data, {
        format,
        validateOnly: false,
        overwriteExisting: false,
        enableBackup: true,
        batchSize: 100,
        skipErrors: false,
        ...options
      })
    } catch (__error) {
      return {
        success: false,
        totalRecords: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        validationErrors: [],
        importedRules: [],
        processTime: 0,
        error: error instanceof Error ? error.message : '文件解析失败'
      }
    }
  }

  // 预处理数据

  private preprocessData(data: unknown[], format: string): unknown[] {
    return data.map((item, index) => {
      // 标准化字段名
      const processed = this.normalizeFieldNames(item)

      // 添加行索引
      processed._rowIndex = index + 1

      // 数据类型转换
      if (processed.priority) {
        processed.priority = parseInt(processed.priority)
      }

      if (processed.enabled !== undefined) {
        processed.enabled = Boolean(processed.enabled)
      }

      // 处理数组字段
      if (processed.applicableTypes && typeof processed.applicableTypes === 'string') {
        processed.applicableTypes = processed.applicableTypes.split(',').map(s => s.trim())
      }

      return processed
    })
  }

  // 标准化字段名

  private normalizeFieldNames(item: unknown): unknown {
    const fieldMapping = {
      规则名称: 'ruleName',
      规则代码: 'ruleCode',
      规则类型: 'ruleType',
      描述: 'description',
      是否启用: 'enabled',
      优先级: 'priority',
      适用类型: 'applicableTypes',
      格式模式: 'format',
      总长度: 'totalLength'
    }

    const normalized: unknown = {}

    Object.entries(item).forEach(([key, value]) => {
      const mappedKey = fieldMapping[key] || key
      normalized[mappedKey] = value
    })

    return normalized
  }

  // 验证导入数据
  private async validateImportData(
    data: unknown[],
    options: ImportOptions
  ): Promise<{
    hasErrors: boolean
    validCount: number
    errorCount: number
    errors: ValidationError[]

    validData: unknown[]
  }> {
    const errors: ValidationError[] = []

    const validData: unknown[] = []

    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      const rowErrors: ValidationError[] = []

      // 必填字段验证
      this.validateRequiredFields(item, rowErrors)

      // 格式验证
      this.validateDataFormat(item, rowErrors)

      // 业务规则验证
      await this.validateBusinessRules(item, rowErrors, options)

      if (rowErrors.length === 0) {
        validData.push(item)
      } else {
        errors.push(...rowErrors)
      }
    }

    return {
      hasErrors: errors.some(e => e.severity === 'error'),
      validCount: validData.length,
      errorCount: data.length - validData.length,
      errors,
      validData
    }
  }

  // 验证必填字段

  private validateRequiredFields(item: unknown, errors: ValidationError[]) {
    const requiredFields = [
      { field: 'ruleName', name: '规则名称' },
      { field: 'ruleCode', name: '规则代码' },
      { field: 'ruleType', name: '规则类型' },
      { field: 'applicableTypes', name: '适用类型' }
    ]

    requiredFields.forEach(({ field, name }) => {
      if (!item[field] || (Array.isArray(item[field]) && item[field].length === 0)) {
        errors.push({
          rowIndex: item._rowIndex,
          field,
          value: item[field],
          errorType: 'required',
          message: `${name}不能为空`,
          severity: 'error'
        })
      }
    })
  }

  // 验证数据格式

  private validateDataFormat(item: unknown, errors: ValidationError[]) {
    // 规则代码格式验证
    if (item.ruleCode && !/^[A-Z_][A-Z0-9_]*$/.test(item.ruleCode)) {
      errors.push({
        rowIndex: item._rowIndex,
        field: 'ruleCode',
        value: item.ruleCode,
        errorType: 'format',
        message: '规则代码只能包含大写字母、数字和下划线，且必须以字母或下划线开头',
        severity: 'error'
      })
    }

    // 规则类型验证
    if (item.ruleType && !['auto', 'manual', 'hybrid'].includes(item.ruleType)) {
      errors.push({
        rowIndex: item._rowIndex,
        field: 'ruleType',
        value: item.ruleType,
        errorType: 'invalid',
        message: '规则类型必须是 auto、manual 或 hybrid 之一',
        severity: 'error'
      })
    }

    // 优先级验证
    if (item.priority !== undefined) {
      const priority = parseInt(item.priority)
      if (isNaN(priority) || priority < 1 || priority > 100) {
        errors.push({
          rowIndex: item._rowIndex,
          field: 'priority',
          value: item.priority,
          errorType: 'range',
          message: '优先级必须是1-100之间的整数',
          severity: 'error'
        })
      }
    }

    // 适用类型验证
    if (item.applicableTypes && Array.isArray(item.applicableTypes)) {
      const validTypes = this.employeeTypes.map(t => t.value)
      const invalidTypes = item.applicableTypes.filter(type => !validTypes.includes(type))

      if (invalidTypes.length > 0) {
        errors.push({
          rowIndex: item._rowIndex,
          field: 'applicableTypes',
          value: invalidTypes,
          errorType: 'invalid',
          message: `无效的员工类型: ${invalidTypes.join(', ')}`,
          severity: 'error'
        })
      }
    }
  }

  // 验证业务规则
  private async validateBusinessRules(
    item: unknown,
    errors: ValidationError[],
    options: ImportOptions
  ) {
    // 检查规则代码唯一性
    if (item.ruleCode) {
      const existingCheck = this.checkExistingRule(item.ruleCode, item)

      if (existingCheck.exists && !options.overwriteExisting) {
        errors.push({
          rowIndex: item._rowIndex,
          field: 'ruleCode',
          value: item.ruleCode,
          errorType: 'duplicate',
          message: `规则代码 ${item.ruleCode} 已存在`,
          severity: 'error'
        })
      }
    }

    // 验证格式模式
    if (item.format) {
      const patternValidation = this.validateRulePattern(item.format)
      if (!patternValidation.valid) {
        errors.push({
          rowIndex: item._rowIndex,
          field: 'format',
          value: item.format,
          errorType: 'format',
          message: patternValidation.message,
          severity: 'error'
        })
      }
    }
  }

  // 检查现有规则

  private checkExistingRule(ruleCode: string, newRule: unknown): ExistingRuleCheck {
    const existing = this.existingRules.get(ruleCode)

    if (!existing) {
      return { exists: false, conflictType: 'code' }
    }

    return {
      exists: true,
      conflictRule: existing,
      conflictType: 'code'
    }
  }

  // 验证规则模式
  private validateRulePattern(pattern: string): { valid: boolean; message: string } {
    // 检查模式格式
    const validSegments = ['YYYY', 'MM', 'DD', 'DEPT', 'TYPE', 'XXXX', 'XXX', 'XX', 'X']
    const segments = pattern.match(/\{[^}]+\}/g) || []

    for (const segment of segments) {
      const segmentContent = segment.slice(1, -1)
      if (!validSegments.includes(segmentContent) && !/^X+$/.test(segmentContent)) {
        return {
          valid: false,
          message: `无效的模式段: ${segment}`
        }
      }
    }

    // 检查是否包含序号段
    const hasSequence = segments.some(s => /\{X+\}/.test(s))
    if (!hasSequence) {
      return {
        valid: false,
        message: '规则模式必须包含序号段 (如 {XXXX})'
      }
    }

    return { valid: true, message: '' }
  }

  // 执行批量导入
  private async performBatchImport(
    validData: unknown[],
    options: ImportOptions
  ): Promise<{
    successCount: number
    failureCount: number
    errors: ValidationError[]
    importedRules: EmployeeNumberRule[]
  }> {
    const errors: ValidationError[] = []
    const importedRules: EmployeeNumberRule[] = []
    let successCount = 0
    let failureCount = 0

    // 分批处理
    for (let i = 0; i < validData.length; i += options.batchSize) {
      const batch = validData.slice(i, i + options.batchSize)

      for (const item of batch) {
        try {
          const rule = this.convertToEmployeeNumberRule(item)

          // 保存规则
          if (options.overwriteExisting || !this.existingRules.has(rule.ruleCode)) {
            this.existingRules.set(rule.ruleCode, rule)
            importedRules.push(rule)
            successCount++
          } else {
            failureCount++
            errors.push({
              rowIndex: item._rowIndex,
              field: 'ruleCode',
              value: item.ruleCode,
              errorType: 'duplicate',
              message: '规则已存在且未启用覆盖模式',
              severity: 'error'
            })
          }
        } catch (__error) {
          failureCount++
          errors.push({
            rowIndex: item._rowIndex,
            field: 'conversion',
            value: item,
            errorType: 'invalid',
            message: error instanceof Error ? error.message : '数据转换失败',
            severity: 'error'
          })
        }
      }
    }

    return {
      successCount,
      failureCount,
      errors,
      importedRules
    }
  }

  // 转换为工号规则对象

  private convertToEmployeeNumberRule(item: unknown): EmployeeNumberRule {
    const now = new Date().toISOString()

    return {
      id: `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ruleName: item.ruleName,
      ruleCode: item.ruleCode,
      ruleType: item.ruleType || 'auto',
      description: item.description || '',
      enabled: item.enabled !== undefined ? item.enabled : true,
      priority: item.priority || 50,
      applicableTypes: item.applicableTypes || [],
      rulePattern: this.parseRulePattern(item.format || ''),
      validationRules: this.createDefaultValidationRules(),
      assignmentSettings: this.createDefaultAssignmentSettings(),
      createTime: now,
      updateTime: now,
      createdBy: 'import',
      updatedBy: 'import'
    }
  }

  // 解析规则模式
  private parseRulePattern(format: string): RulePattern {
    const segments: PatternSegment[] = []
    let position = 1

    // 提取所有段
    const parts = format.split(/(\{[^}]+\})/)

    parts.forEach(part => {
      if (part.startsWith('{') && part.endsWith('}')) {
        // 动态段
        const content = part.slice(1, -1)
        let type: PatternSegment['type'] = 'custom'
        let length = content.length

        if (content === 'YYYY') {
          type = 'year'
          length = 4
        } else if (content === 'MM') {
          type = 'month'
          length = 2
        } else if (content === 'DEPT') {
          type = 'department'
          length = 2
        } else if (content === 'TYPE') {
          type = 'type'
          length = 1
        } else if (/^X+$/.test(content)) {
          type = 'sequence'
          length = content.length
        }

        segments.push({
          type,
          length,
          format: content,
          required: true,
          position: position++
        })
      } else if (part) {
        // 固定段
        segments.push({
          type: 'fixed',
          value: part,
          length: part.length,
          required: true,
          position: position++
        })
      }
    })

    return {
      format,
      segments,
      totalLength: segments.reduce((sum, seg) => sum + seg.length, 0),
      padding: {
        character: '0',
        position: 'left',
        enabled: true
      }
    }
  }

  // 创建默认验证规则
  private createDefaultValidationRules(): ValidationRule[] {
    return [
      {
        id: `val_${Date.now()}`,
        type: 'uniqueness',
        expression: 'unique',
        errorMessage: '工号必须唯一',
        enabled: true
      }
    ]
  }

  // 创建默认分配设置
  private createDefaultAssignmentSettings(): AssignmentSettings {
    return {
      autoAssign: true,
      allowManualOverride: false,
      reservationPool: {
        enabled: true,
        poolSize: 100,
        warningThreshold: 20
      },
      sequenceSettings: {
        startValue: 1,
        increment: 1,
        resetFrequency: 'yearly',
        resetDate: '01-01'
      }
    }
  }

  // 创建备份
  private async createBackup(): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19)
    const filename = `employee_number_rules_backup_${timestamp}.json`

    // 模拟保存备份文件
    return filename
  }

  // 检测文件格式
  private detectFileFormat(filename: string): ImportOptions['format'] {
    const ext = filename.toLowerCase().split('.').pop()

    switch (ext) {
      case 'json':
        return 'json'
      case 'xlsx':
      case 'xls':
        return 'excel'
      case 'csv':
        return 'csv'
      case 'xml':
        return 'xml'
      default:
        return 'json'
    }
  }

  // 解析文件
  private async parseFile(file: File, format: ImportOptions['format']): Promise<unknown[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = _e => {
        try {
          const content = e.target?.result as string

          let data: unknown[]

          switch (format) {
            case 'json':
              data = JSON.parse(content)
              break
            case 'csv':
              data = this.parseCsv(content)
              break
            case 'xml':
              data = this.parseXml(content)
              break
            default:
              throw new Error(`不支持的文件格式: ${format}`)
          }

          resolve(Array.isArray(data) ? data : [data])
        } catch (__error) {
          reject(error)
        }
      }

      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file)
    })
  }

  // 解析CSV

  private parseCsv(content: string): unknown[] {
    const lines = content.split('\n').filter(line => line.trim())
    if (lines.length < 2) return []

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))

    const data: unknown[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))

      const item: unknown = {}

      headers.forEach((header, index) => {
        item[header] = values[index] || ''
      })

      data.push(item)
    }

    return data
  }

  // 解析XML (简化版)

  private parseXml(content: string): unknown[] {
    // 这里实现简化的XML解析
    // 实际项目中应该使用专业的XML解析库

    const data: unknown[] = []

    // 模拟XML解析结果
    data.push({
      ruleName: 'XML导入测试规则',
      ruleCode: 'XML_TEST_RULE',
      ruleType: 'auto',
      description: '从XML文件导入的测试规则',
      enabled: true,
      priority: 10,
      applicableTypes: ['regular']
    })

    return data
  }

  // 生成导入模板
  generateImportTemplate(format: ImportOptions['format']): Blob {
    const templateData = [
      {
        规则名称: '在编员工工号规则',
        规则代码: 'REGULAR_EMP_RULE',
        规则类型: 'auto',
        描述: '在编员工自动分配工号规则',
        是否启用: true,
        优先级: 1,
        适用类型: 'regular',
        格式模式: 'R{YYYY}{DEPT}{XXXX}',
        总长度: 11
      },
      {
        规则名称: '非编员工工号规则',
        规则代码: 'CONTRACT_EMP_RULE',
        规则类型: 'auto',
        描述: '非编员工自动分配工号规则',
        是否启用: true,
        优先级: 2,
        适用类型: 'contract',
        格式模式: 'C{YYYY}{DEPT}{XXXX}',
        总长度: 11
      }
    ]

    switch (format) {
      case 'json':
        return new Blob([JSON.stringify(templateData, null, 2)], {
          type: 'application/json'
        })
      case 'csv':
        return this.generateCsvTemplate(templateData)
      case 'xml':
        return this.generateXmlTemplate(templateData)
      default:
        return new Blob([JSON.stringify(templateData, null, 2)], {
          type: 'application/json'
        })
    }
  }

  // 生成CSV模板

  private generateCsvTemplate(data: unknown[]): Blob {
    if (data.length === 0) return new Blob([''], { type: 'text/csv' })

    const headers = Object.keys(data[0])
    const rows = data.map(item => headers.map(header => `"${item[header]}"`).join(','))

    const csvContent = [headers.map(h => `"${h}"`).join(','), ...rows].join('\n')

    const bom = '\uFEFF'
    return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
  }

  // 生成XML模板

  private generateXmlTemplate(data: unknown[]): Blob {
    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<rules>
${data
  .map(
    item => `  <rule>
${Object.entries(item)
  .map(([key, value]) => `    <${key}>${value}</${key}>`)
  .join('\n')}
  </rule>`
  )
  .join('\n')}
</rules>`

    return new Blob([xmlContent], { type: 'application/xml' })
  }

  // 获取现有规则
  getExistingRules(): EmployeeNumberRule[] {
    return Array.from(this.existingRules.values())
  }

  // 获取员工类型
  getEmployeeTypes() {
    return this.employeeTypes
  }

  // 获取部门代码
  getDepartmentCodes() {
    return this.departmentCodes
  }

  // 验证规则配置
  validateRuleConfiguration(rule: Partial<EmployeeNumberRule>): ValidationError[] {
    const errors: ValidationError[] = []

    // 这里可以添加更详细的规则配置验证
    if (!rule.ruleName) {
      errors.push({
        rowIndex: 0,
        field: 'ruleName',
        value: rule.ruleName,
        errorType: 'required',
        message: '规则名称不能为空',
        severity: 'error'
      })
    }

    return errors
  }
}

// 全局实例
export const employeeNumberRuleImporter = new EmployeeNumberRuleImport()

// 便捷函数
export async function importEmployeeNumberRules(
  data: unknown[],
  options?: Partial<ImportOptions>
): Promise<ImportResult> {
  return employeeNumberRuleImporter.importRules(data, {
    format: 'excel',
    validateOnly: false,
    overwriteExisting: false,
    enableBackup: true,
    batchSize: 100,
    skipErrors: false,
    ...options
  })
}

export async function importFromFile(
  file: File,
  options?: Partial<ImportOptions>
): Promise<ImportResult> {
  return employeeNumberRuleImporter.importFromFile(file, options)
}

export function generateTemplate(format: ImportOptions['format'] = 'excel'): Blob {
  return employeeNumberRuleImporter.generateImportTemplate(format)
}
