interface CertificateRecord {
  id: string
  certificateType: 'employment' | 'resignation' | 'income' | 'leave' | 'legal' | 'custom'
  applicantId: string
  applicantName: string
  departmentId: string
  departmentName: string
  position: string
  content: string
  issueDate: string
  expiryDate?: string
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'issued' | 'expired'
  approvalProcessId?: string
  fileUrl?: string
  templateId?: string
  digitalSignature?: DigitalSignature
  metadata: CertificateMetadata
  createTime: string
  updateTime: string
  issuedBy: string
}

interface DigitalSignature {
  signatureId: string
  signatureUrl: string
  signatureTime: string
  signatureType: 'digital' | 'electronic' | 'manual'
  verificationCode: string
  legalEffectiveness: boolean
}

interface CertificateMetadata {
  purpose: string
  requestReason: string
  urgency: 'low' | 'medium' | 'high' | 'urgent'
  validityPeriod?: number
  confidentialityLevel: 'public' | 'internal' | 'confidential' | 'secret'
  relatedDocuments?: string[]
  customFields?: Record<string, unknown>
}

interface CertificateTemplate {
  id: string
  name: string
  type: string
  template: string
  fields: TemplateField[]
  settings: TemplateSettings
  enabled: boolean
}

interface TemplateField {
  name: string
  label: string
  type: 'text' | 'date' | 'number' | 'select' | 'textarea'
  required: boolean
  defaultValue?: unknown
  validation?: string
  options?: string[]
}

interface TemplateSettings {
  pageSize: 'A4' | 'A5' | 'Letter'
  orientation: 'portrait' | 'landscape'
  margin: { top: number; right: number; bottom: number; left: number }
  headerFooter: boolean
  watermark?: string
  digitalSignaturePosition?: { x: number; y: number }
}

interface BatchExportFilter {
  applicantIds?: string[]
  departmentIds?: string[]
  certificateTypes?: string[]
  status?: string[]
  dateRange?: {
    start: string
    end: string
    type: 'issue' | 'create' | 'approval'
  }
  urgency?: string[]
  templateId?: string
  confidentialityLevel?: string[]
  hasDigitalSignature?: boolean
}

interface BatchExportOptions {
  format: 'pdf' | 'word' | 'zip' | 'excel'
  includeMetadata: boolean
  includeSignature: boolean
  mergeToSingle: boolean
  customTemplate?: string
  watermark?: string
  passwordProtect?: boolean
  password?: string
  compressionLevel?: 'none' | 'fast' | 'standard' | 'maximum'
}

interface BatchExportResult {
  success: boolean
  totalCount: number
  successCount: number
  failureCount: number
  exportedFiles: ExportedFile[]
  mergedFile?: ExportedFile
  errors: BatchExportError[]
  statisticsSummary: {
    byType: Record<string, number>
    byStatus: Record<string, number>
    byDepartment: Record<string, number>
    totalFileSize: number
    processTime: number
  }
  downloadUrl?: string
  error?: string
}

interface ExportedFile {
  id: string
  originalId: string
  fileName: string
  fileType: string
  fileSize: number
  filePath: string
  downloadUrl: string
  checksum: string
}

interface BatchExportError {
  certificateId: string
  applicantName: string
  errorType: 'template' | 'data' | 'signature' | 'permission' | 'system'
  message: string
  details?: unknown
}

/**
 * CLEAN-AUX-011: 证明批量导出功能
 * 支持多类型证明文件的批量生成、导出和管理系统
 */
export class CertificateBatchExport {
  private certificates: Map<string, CertificateRecord> = new Map()
  private templates: Map<string, CertificateTemplate> = new Map()

  // 预定义证明类型
  private readonly certificateTypes = [
    {
      value: 'employment',
      label: '在职证明',
      templates: ['employment_standard', 'employment_detailed'],
      approvalRequired: false,
      validityDays: 30
    },
    {
      value: 'resignation',
      label: '离职证明',
      templates: ['resignation_standard', 'resignation_legal'],
      approvalRequired: true,
      validityDays: 0
    },
    {
      value: 'income',
      label: '收入证明',
      templates: ['income_annual', 'income_monthly'],
      approvalRequired: true,
      validityDays: 90
    },
    {
      value: 'leave',
      label: '请假证明',
      templates: ['leave_sick', 'leave_personal'],
      approvalRequired: false,
      validityDays: 365
    },
    {
      value: 'legal',
      label: '法律证明',
      templates: ['legal_contract', 'legal_signature'],
      approvalRequired: true,
      validityDays: 365
    },
    {
      value: 'custom',
      label: '自定义证明',
      templates: ['custom_template'],
      approvalRequired: true,
      validityDays: 30
    }
  ]

  // 状态标签映射
  private readonly statusLabels = {
    draft: '草稿',
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    issued: '已出具',
    expired: '已过期'
  }

  constructor() {
    this.initializeMockData()
    this.initializeTemplates()
  }

  // 初始化模拟数据
  private initializeMockData() {
    const mockCertificates: CertificateRecord[] = [
      {
        id: 'cert_001',
        certificateType: 'employment',
        applicantId: 'emp_001',
        applicantName: '张三',
        departmentId: 'dept_001',
        departmentName: '技术部',
        position: '软件工程师',
        content:
          '兹证明张三同志，工号EMP001，自2023年1月起在我校技术部担任软件工程师职务，至今仍在职。特此证明。',
        issueDate: '2025-01-10',
        expiryDate: '2025-02-09',
        status: 'issued',
        templateId: 'employment_standard',
        digitalSignature: {
          signatureId: 'sig_001',
          signatureUrl: '/signatures/sig_001.png',
          signatureTime: '2025-01-10T14:30:00.000Z',
          signatureType: 'digital',
          verificationCode: 'HKY20250110001',
          legalEffectiveness: true
        },
        metadata: {
          purpose: '银行贷款申请',
          requestReason: '个人住房贷款所需',
          urgency: 'medium',
          validityPeriod: 30,
          confidentialityLevel: 'internal',
          customFields: {
            loanAmount: '500万元',
            bankName: '中国银行'
          }
        },
        createTime: '2025-01-10T10:00:00.000Z',
        updateTime: '2025-01-10T14:30:00.000Z',
        issuedBy: 'hr_001'
      },
      {
        id: 'cert_002',
        certificateType: 'income',
        applicantId: 'emp_002',
        applicantName: '李四',
        departmentId: 'dept_002',
        departmentName: '人事部',
        position: '人事专员',
        content:
          '兹证明李四同志，工号EMP002，在我校人事部担任人事专员，2024年度税前年收入为12万元。特此证明。',
        issueDate: '2025-01-12',
        status: 'approved',
        templateId: 'income_annual',
        metadata: {
          purpose: '子女入学申请',
          requestReason: '证明家庭收入状况',
          urgency: 'high',
          validityPeriod: 90,
          confidentialityLevel: 'confidential',
          customFields: {
            annualIncome: 120000,
            schoolName: '杭州市第一小学'
          }
        },
        createTime: '2025-01-12T09:00:00.000Z',
        updateTime: '2025-01-12T15:00:00.000Z',
        issuedBy: 'hr_002'
      }
    ]

    mockCertificates.forEach(cert => {
      this.certificates.set(cert.id, cert)
    })
  }

  // 初始化模板
  private initializeTemplates() {
    const mockTemplates: CertificateTemplate[] = [
      {
        id: 'employment_standard',
        name: '标准在职证明模板',
        type: 'employment',
        template: `
          <div class="certificate-header">
            <h1>在职证明</h1>
            <div class="school-info">杭州科技职业技术学院</div>
          </div>
          <div class="certificate-body">
            <p>兹证明 <strong>{{applicantName}}</strong> 同志，工号 <strong>{{employeeId}}</strong>，自 <strong>{{hireDate}}</strong> 起在我校 <strong>{{departmentName}}</strong> 担任 <strong>{{position}}</strong> 职务，至今仍在职。</p>
            <p>特此证明。</p>
          </div>
          <div class="certificate-footer">
            <div class="issue-info">
              <p>出具日期：{{issueDate}}</p>
              <p>有效期至：{{expiryDate}}</p>
            </div>
            <div class="signature-area">
              <p>杭州科技职业技术学院</p>
              <p>（盖章）</p>
            </div>
          </div>
        `,
        fields: [
          { name: 'applicantName', label: '姓名', type: 'text', required: true },
          { name: 'employeeId', label: '工号', type: 'text', required: true },
          { name: 'hireDate', label: '入职日期', type: 'date', required: true },
          { name: 'departmentName', label: '部门', type: 'text', required: true },
          { name: 'position', label: '职位', type: 'text', required: true },
          { name: 'issueDate', label: '出具日期', type: 'date', required: true },
          { name: 'expiryDate', label: '有效期', type: 'date', required: false }
        ],
        settings: {
          pageSize: 'A4',
          orientation: 'portrait',
          margin: { top: 20, right: 20, bottom: 20, left: 20 },
          headerFooter: true,
          watermark: '杭州科技职业技术学院',
          digitalSignaturePosition: { x: 400, y: 600 }
        },
        enabled: true
      },
      {
        id: 'income_annual',
        name: '年度收入证明模板',
        type: 'income',
        template: `
          <div class="certificate-header">
            <h1>收入证明</h1>
            <div class="school-info">杭州科技职业技术学院</div>
          </div>
          <div class="certificate-body">
            <p>兹证明 <strong>{{applicantName}}</strong> 同志，工号 <strong>{{employeeId}}</strong>，在我校 <strong>{{departmentName}}</strong> 担任 <strong>{{position}}</strong>。</p>
            <p>{{year}}年度税前年收入为人民币 <strong>{{annualIncome}}</strong> 元（大写：{{annualIncomeText}}）。</p>
            <p>特此证明。</p>
          </div>
          <div class="certificate-footer">
            <div class="issue-info">
              <p>出具日期：{{issueDate}}</p>
              <p>有效期至：{{expiryDate}}</p>
            </div>
            <div class="signature-area">
              <p>杭州科技职业技术学院</p>
              <p>人事处（盖章）</p>
            </div>
          </div>
        `,
        fields: [
          { name: 'applicantName', label: '姓名', type: 'text', required: true },
          { name: 'employeeId', label: '工号', type: 'text', required: true },
          { name: 'departmentName', label: '部门', type: 'text', required: true },
          { name: 'position', label: '职位', type: 'text', required: true },
          { name: 'year', label: '年度', type: 'number', required: true },
          { name: 'annualIncome', label: '年收入', type: 'number', required: true },
          { name: 'annualIncomeText', label: '年收入大写', type: 'text', required: true }
        ],
        settings: {
          pageSize: 'A4',
          orientation: 'portrait',
          margin: { top: 25, right: 25, bottom: 25, left: 25 },
          headerFooter: true,
          watermark: '杭州科技职业技术学院机密',
          digitalSignaturePosition: { x: 400, y: 650 }
        },
        enabled: true
      }
    ]

    mockTemplates.forEach(template => {
      this.templates.set(template.id, template)
    })
  }

  // 批量导出证明
  async batchExportCertificates(
    filter: BatchExportFilter = {},
    options: BatchExportOptions = {
      format: 'pdf',
      includeMetadata: true,
      includeSignature: true,
      mergeToSingle: false,
      compressionLevel: 'standard'
    }
  ): Promise<BatchExportResult> {
    const startTime = Date.now()

    try {
      // 筛选证明记录
      const filteredCertificates = this.filterCertificates(filter)

      if (filteredCertificates.length === 0) {
        return {
          success: false,
          totalCount: 0,
          successCount: 0,
          failureCount: 0,
          exportedFiles: [],
          errors: [],
          statisticsSummary: {
            byType: {},
            byStatus: {},
            byDepartment: {},
            totalFileSize: 0,
            processTime: 0
          },
          error: '没有符合条件的证明记录'
        }
      }

      // 批量生成文件
      const exportResult = await this.generateBatchFiles(filteredCertificates, options)

      // 计算统计信息
      const statistics = this.calculateStatistics(filteredCertificates, exportResult.exportedFiles)

      return {
        success: true,
        totalCount: filteredCertificates.length,
        successCount: exportResult.successCount,
        failureCount: exportResult.failureCount,
        exportedFiles: exportResult.exportedFiles,
        mergedFile: exportResult.mergedFile,
        errors: exportResult.errors,
        statisticsSummary: {
          ...statistics,
          processTime: Date.now() - startTime
        },
        downloadUrl: exportResult.downloadUrl
      }
    } catch (error) {
      return {
        success: false,
        totalCount: 0,
        successCount: 0,
        failureCount: 0,
        exportedFiles: [],
        errors: [],
        statisticsSummary: {
          byType: {},
          byStatus: {},
          byDepartment: {},
          totalFileSize: 0,
          processTime: Date.now() - startTime
        },
        error: error instanceof Error ? error.message : '批量导出失败'
      }
    }
  }

  // 生成单个证明
  async generateSingleCertificate(
    certificateId: string,
    options: Partial<BatchExportOptions> = {}
  ): Promise<ExportedFile | null> {
    const certificate = this.certificates.get(certificateId)
    if (!certificate) {
      throw new Error(`证明记录不存在: ${certificateId}`)
    }

    const template = this.templates.get(certificate.templateId || 'employment_standard')
    if (!template) {
      throw new Error(`模板不存在: ${certificate.templateId}`)
    }

    // 准备模板数据
    const templateData = this.prepareTemplateData(certificate)

    // 渲染证明内容
    const renderedContent = this.renderTemplate(template.template, templateData)

    // 生成文件
    const file = await this.generateCertificateFile(certificate, renderedContent, template, options)

    return file
  }

  // 筛选证明记录
  private filterCertificates(filter: BatchExportFilter): CertificateRecord[] {
    let certificates = Array.from(this.certificates.values())

    // 按申请人筛选
    if (filter.applicantIds && filter.applicantIds.length > 0) {
      certificates = certificates.filter(cert => filter.applicantIds!.includes(cert.applicantId))
    }

    // 按部门筛选
    if (filter.departmentIds && filter.departmentIds.length > 0) {
      certificates = certificates.filter(cert => filter.departmentIds!.includes(cert.departmentId))
    }

    // 按证明类型筛选
    if (filter.certificateTypes && filter.certificateTypes.length > 0) {
      certificates = certificates.filter(cert =>
        filter.certificateTypes!.includes(cert.certificateType)
      )
    }

    // 按状态筛选
    if (filter.status && filter.status.length > 0) {
      certificates = certificates.filter(cert => filter.status!.includes(cert.status))
    }

    // 按紧急程度筛选
    if (filter.urgency && filter.urgency.length > 0) {
      certificates = certificates.filter(cert => filter.urgency!.includes(cert.metadata.urgency))
    }

    // 按机密等级筛选
    if (filter.confidentialityLevel && filter.confidentialityLevel.length > 0) {
      certificates = certificates.filter(cert =>
        filter.confidentialityLevel!.includes(cert.metadata.confidentialityLevel)
      )
    }

    // 按是否有数字签名筛选
    if (filter.hasDigitalSignature !== undefined) {
      certificates = certificates.filter(cert =>
        filter.hasDigitalSignature ? !!cert.digitalSignature : !cert.digitalSignature
      )
    }

    // 按模板筛选
    if (filter.templateId) {
      certificates = certificates.filter(cert => cert.templateId === filter.templateId)
    }

    // 按日期范围筛选
    if (filter.dateRange) {
      const { start, end, type } = filter.dateRange
      certificates = certificates.filter(cert => {
        let dateToCheck: string
        switch (type) {
          case 'issue':
            dateToCheck = cert.issueDate
            break
          case 'create':
            dateToCheck = cert.createTime.split('T')[0]
            break
          case 'approval':
            dateToCheck = cert.updateTime.split('T')[0]
            break
          default:
            dateToCheck = cert.createTime.split('T')[0]
        }
        return dateToCheck >= start && dateToCheck <= end
      })
    }

    return certificates
  }

  // 批量生成文件
  private async generateBatchFiles(
    certificates: CertificateRecord[],
    options: BatchExportOptions
  ): Promise<{
    successCount: number
    failureCount: number
    exportedFiles: ExportedFile[]
    errors: BatchExportError[]
    mergedFile?: ExportedFile
    downloadUrl?: string
  }> {
    const exportedFiles: ExportedFile[] = []
    const errors: BatchExportError[] = []
    let successCount = 0
    let failureCount = 0

    // 逐个生成文件
    for (const certificate of certificates) {
      try {
        const file = await this.generateSingleCertificate(certificate.id, options)
        if (file) {
          exportedFiles.push(file)
          successCount++
        } else {
          failureCount++
          errors.push({
            certificateId: certificate.id,
            applicantName: certificate.applicantName,
            errorType: 'system',
            message: '文件生成失败'
          })
        }
      } catch (error) {
        failureCount++
        errors.push({
          certificateId: certificate.id,
          applicantName: certificate.applicantName,
          errorType: 'system',
          message: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    return {
      successCount,
      failureCount,
      exportedFiles,
      errors,
      downloadUrl: exportedFiles.length > 0 ? '/download/batch_certificates.zip' : undefined
    }
  }

  // 计算统计信息
  private calculateStatistics(
    certificates: CertificateRecord[],
    exportedFiles: ExportedFile[]
  ): {
    byType: Record<string, number>
    byStatus: Record<string, number>
    byDepartment: Record<string, number>
    totalFileSize: number
  } {
    const byType: Record<string, number> = {}
    const byStatus: Record<string, number> = {}
    const byDepartment: Record<string, number> = {}

    certificates.forEach(cert => {
      // 按类型统计
      byType[cert.certificateType] = (byType[cert.certificateType] || 0) + 1
      // 按状态统计
      byStatus[cert.status] = (byStatus[cert.status] || 0) + 1
      // 按部门统计
      byDepartment[cert.departmentName] = (byDepartment[cert.departmentName] || 0) + 1
    })

    const totalFileSize = exportedFiles.reduce((sum, file) => sum + file.fileSize, 0)

    return {
      byType,
      byStatus,
      byDepartment,
      totalFileSize
    }
  }

  // 准备模板数据
  private prepareTemplateData(certificate: CertificateRecord): Record<string, unknown> {
    const data: Record<string, unknown> = {
      ...certificate,
      employeeId: certificate.applicantId,
      year: new Date().getFullYear(),
      hireDate: '2023-01-01', // 模拟入职日期
      annualIncome: certificate.metadata.customFields?.annualIncome || 120000,
      annualIncomeText: this.numberToChinese(
        Number(certificate.metadata.customFields?.annualIncome) || 120000
      )
    }

    return data
  }

  // 渲染模板
  private renderTemplate(template: string, data: Record<string, unknown>): string {
    let rendered = template

    // 简单的模板替换
    Object.entries(data).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g')
      rendered = rendered.replace(regex, String(value || ''))
    })

    return rendered
  }

  // 生成证明文件
  private async generateCertificateFile(
    certificate: CertificateRecord,
    content: string,
    template: CertificateTemplate,
    options: Partial<BatchExportOptions>
  ): Promise<ExportedFile> {
    const fileName = `${certificate.certificateType}_${certificate.applicantName}_${certificate.id}.pdf`
    const mockFileSize = content.length + Math.floor(Math.random() * 10000)
    const checksum = this.generateChecksum(content)

    return {
      id: `file_${certificate.id}`,
      originalId: certificate.id,
      fileName,
      fileType: options.format || 'pdf',
      fileSize: mockFileSize,
      filePath: `/exports/${fileName}`,
      downloadUrl: `/download/${fileName}`,
      checksum
    }
  }

  // 生成校验和
  private generateChecksum(content: string): string {
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16)
  }

  // 数字转中文
  private numberToChinese(num: number): string {
    const units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿']
    const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']

    if (num === 0) return '零'

    let result = ''
    let unitIndex = 0

    while (num > 0) {
      const digit = num % 10
      if (digit !== 0 || (result !== '' && !result.startsWith('零'))) {
        result = digits[digit] + (digit !== 0 ? units[unitIndex] : '') + result
      }
      num = Math.floor(num / 10)
      unitIndex++
    }

    return result
  }

  // 获取证明类型标签
  private getCertificateTypeLabel(type: string): string {
    const typeConfig = this.certificateTypes.find(t => t.value === type)
    return typeConfig ? typeConfig.label : type
  }

  // 获取证明类型列表
  getCertificateTypes() {
    return this.certificateTypes
  }

  // 获取状态标签
  getStatusLabels() {
    return this.statusLabels
  }

  // 获取模板列表
  getTemplates(type?: string) {
    let templates = Array.from(this.templates.values())
    if (type) {
      templates = templates.filter(t => t.type === type)
    }
    return templates
  }

  // 添加证明记录
  addCertificateRecord(certificate: CertificateRecord) {
    this.certificates.set(certificate.id, certificate)
  }

  // 获取证明统计
  getCertificateStatistics() {
    const certificates = Array.from(this.certificates.values())
    return this.calculateStatistics(certificates, [])
  }

  // 验证导出权限
  validateExportPermission(
    certificateIds: string[],
    userId: string
  ): {
    allowed: string[]
    denied: string[]
    reasons: Record<string, string>
  } {
    const allowed: string[] = []
    const denied: string[] = []
    const reasons: Record<string, string> = {}

    certificateIds.forEach(id => {
      const certificate = this.certificates.get(id)
      if (!certificate) {
        denied.push(id)
        reasons[id] = '证明记录不存在'
        return
      }

      // 检查权限（简化版权限检查）
      if (certificate.metadata.confidentialityLevel === 'secret' && userId !== 'admin') {
        denied.push(id)
        reasons[id] = '机密级证明需要管理员权限'
      } else if (certificate.status === 'draft' && certificate.applicantId !== userId) {
        denied.push(id)
        reasons[id] = '草稿状态的证明只能由申请人导出'
      } else {
        allowed.push(id)
      }
    })

    return { allowed, denied, reasons }
  }
}

// 全局实例
export const certificateBatchExporter = new CertificateBatchExport()

// 便捷函数
export async function batchExportCertificates(
  filter?: BatchExportFilter,
  options?: Partial<BatchExportOptions>
): Promise<BatchExportResult> {
  return certificateBatchExporter.batchExportCertificates(filter, {
    format: 'pdf',
    includeMetadata: true,
    includeSignature: true,
    mergeToSingle: false,
    compressionLevel: 'standard',
    ...options
  })
}

export async function exportSingleCertificate(
  certificateId: string,
  options?: Partial<BatchExportOptions>
): Promise<ExportedFile | null> {
  return certificateBatchExporter.generateSingleCertificate(certificateId, options)
}
