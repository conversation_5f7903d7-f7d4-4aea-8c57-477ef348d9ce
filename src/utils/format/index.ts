/**
 * 格式化工具函数
 */

import { format as formatDateCore } from '../core/date'
import {
  format as formatNumberCore,
  formatBytes,
  formatCurrency,
  formatPercent
} from '../core/number'

// 重新导出核心格式化函数
export {
  formatDateCore as formatDate,
  formatNumberCore as formatNumber,
  formatBytes,
  formatCurrency,
  formatPercent
}

/**
 * 格式化手机号（隐藏中间4位）
 * @param phone 手机号
 */
export function formatPhone(phone: string): string {
  if (!phone || phone.length !== 11) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 格式化银行卡号（每4位一个空格）
 * @param cardNumber 银行卡号
 */
export function formatBankCard(cardNumber: string): string {
  return cardNumber.replace(/\s/g, '').replace(/(\d{4})(?=\d)/g, '$1 ')
}

/**
 * 格式化身份证号（隐藏中间部分）
 * @param idCard 身份证号
 * @param hideLength 隐藏长度
 */
export function formatIdCard(idCard: string, hideLength: number = 8): string {
  if (!idCard) return idCard
  const start = Math.floor((idCard.length - hideLength) / 2)
  const end = start + hideLength
  return idCard.substring(0, start) + '*'.repeat(hideLength) + idCard.substring(end)
}

/**
 * 格式化姓名（隐藏部分）
 * @param name 姓名
 * @param showFirst 是否显示第一个字
 */
export function formatName(name: string, showFirst: boolean = true): string {
  if (!name) return name
  if (name.length === 1) return name

  if (showFirst) {
    return name[0] + '*'.repeat(name.length - 1)
  } else {
    return '*'.repeat(name.length - 1) + name[name.length - 1]
  }
}

/**
 * 格式化金额（中文大写）
 * @param amount 金额
 */
export function formatAmountChinese(amount: number): string {
  const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  const units = ['', '拾', '佰', '仟']
  const bigUnits = ['', '万', '亿', '万亿']

  if (amount === 0) return '零元整'

  const isNegative = amount < 0
  amount = Math.abs(amount)

  // 处理小数部分
  const decimal = Math.round((amount % 1) * 100)
  const jiao = Math.floor(decimal / 10)
  const fen = decimal % 10

  // 处理整数部分
  let integer = Math.floor(amount)
  let result = ''
  let unitIndex = 0

  while (integer > 0) {
    const section = integer % 10000
    if (section > 0) {
      let sectionStr = ''
      let tempSection = section
      let tempUnitIndex = 0

      while (tempSection > 0) {
        const digit = tempSection % 10
        if (digit > 0) {
          sectionStr = digits[digit] + units[tempUnitIndex] + sectionStr
        } else if (sectionStr && !sectionStr.startsWith('零')) {
          sectionStr = '零' + sectionStr
        }
        tempSection = Math.floor(tempSection / 10)
        tempUnitIndex++
      }

      result = sectionStr + bigUnits[unitIndex] + result
    }

    integer = Math.floor(integer / 10000)
    unitIndex++
  }

  // 添加元
  result = result + '元'

  // 添加小数部分
  if (jiao > 0 || fen > 0) {
    if (jiao > 0) {
      result += digits[jiao] + '角'
    }
    if (fen > 0) {
      result += digits[fen] + '分'
    }
  } else {
    result += '整'
  }

  // 清理多余的零
  result = result.replace(/零+/g, '零')
  result = result.replace(/零(万|亿)/g, '$1')
  result = result.replace(/亿万/g, '亿')

  return (isNegative ? '负' : '') + result
}

/**
 * 格式化时长
 * @param seconds 秒数
 * @param format 格式
 */
export function formatDuration(seconds: number, format: 'short' | 'long' = 'short'): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (format === 'short') {
    if (hours > 0) {
      return `${hours}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
    } else {
      return `${minutes}:${String(secs).padStart(2, '0')}`
    }
  } else {
    const parts = []
    if (hours > 0) parts.push(`${hours}小时`)
    if (minutes > 0) parts.push(`${minutes}分钟`)
    if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`)
    return parts.join('')
  }
}

/**
 * 格式化存储单位
 * @param bytes 字节数
 * @param binary 是否使用二进制单位
 */
export function formatStorage(bytes: number, binary: boolean = true): string {
  const base = binary ? 1024 : 1000
  const units = binary
    ? ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB']
    : ['B', 'KB', 'MB', 'GB', 'TB', 'PB']

  if (bytes === 0) return '0 B'

  const i = Math.floor(Math.log(bytes) / Math.log(base))
  const value = bytes / Math.pow(base, i)

  return `${value.toFixed(2)} ${units[i]}`
}

/**
 * 格式化数字（缩写）
 * @param num 数字
 * @param decimals 小数位数
 */
export function formatNumberShort(num: number, decimals: number = 1): string {
  if (Math.abs(num) < 1000) return String(num)

  const units = ['', 'K', 'M', 'B', 'T']
  const isNegative = num < 0
  num = Math.abs(num)

  let unitIndex = 0
  while (num >= 1000 && unitIndex < units.length - 1) {
    num /= 1000
    unitIndex++
  }

  const formatted = num.toFixed(decimals).replace(/\.0+$/, '')
  return (isNegative ? '-' : '') + formatted + units[unitIndex]
}

/**
 * 格式化比率
 * @param value 当前值
 * @param total 总值
 * @param decimals 小数位数
 */
export function formatRatio(value: number, total: number, decimals: number = 1): string {
  if (total === 0) return '0%'
  const ratio = (value / total) * 100
  return ratio.toFixed(decimals) + '%'
}

/**
 * 格式化排名
 * @param rank 排名
 */
export function formatRank(rank: number): string {
  if (rank <= 0) return '-'

  const lastDigit = rank % 10
  const lastTwoDigits = rank % 100

  let suffix = 'th'
  if (lastTwoDigits < 11 || lastTwoDigits > 13) {
    switch (lastDigit) {
      case 1:
        suffix = 'st'
        break
      case 2:
        suffix = 'nd'
        break
      case 3:
        suffix = 'rd'
        break
    }
  }

  return rank + suffix
}

/**
 * 格式化温度
 * @param celsius 摄氏度
 * @param unit 单位
 */
export function formatTemperature(celsius: number, unit: 'C' | 'F' = 'C'): string {
  if (unit === 'C') {
    return `${celsius.toFixed(1)}°C`
  } else {
    const fahrenheit = (celsius * 9) / 5 + 32
    return `${fahrenheit.toFixed(1)}°F`
  }
}

/**
 * 格式化距离
 * @param meters 米
 * @param unit 单位系统
 */
export function formatDistance(meters: number, unit: 'metric' | 'imperial' = 'metric'): string {
  if (unit === 'metric') {
    if (meters < 1000) {
      return `${meters.toFixed(0)}m`
    } else {
      return `${(meters / 1000).toFixed(2)}km`
    }
  } else {
    const feet = meters * 3.28084
    if (feet < 5280) {
      return `${feet.toFixed(0)}ft`
    } else {
      const miles = feet / 5280
      return `${miles.toFixed(2)}mi`
    }
  }
}

/**
 * 格式化速度
 * @param mps 米/秒
 * @param unit 单位
 */
export function formatSpeed(mps: number, unit: 'kmh' | 'mph' = 'kmh'): string {
  if (unit === 'kmh') {
    const kmh = mps * 3.6
    return `${kmh.toFixed(1)} km/h`
  } else {
    const mph = mps * 2.23694
    return `${mph.toFixed(1)} mph`
  }
}

/**
 * 格式化角度
 * @param degrees 度数
 * @param format 格式
 */
export function formatAngle(degrees: number, format: 'deg' | 'rad' | 'dms' = 'deg'): string {
  degrees = degrees % 360
  if (degrees < 0) degrees += 360

  switch (format) {
    case 'deg':
      return `${degrees.toFixed(2)}°`

    case 'rad':
      const radians = (degrees * Math.PI) / 180
      return `${radians.toFixed(4)} rad`

    case 'dms':
      const d = Math.floor(degrees)
      const m = Math.floor((degrees - d) * 60)
      const s = ((degrees - d) * 60 - m) * 60
      return `${d}°${m}'${s.toFixed(1)}"`
  }
}

/**
 * 格式化坐标
 * @param lat 纬度
 * @param lng 经度
 * @param format 格式
 */
export function formatCoordinate(
  lat: number,
  lng: number,
  format: 'decimal' | 'dms' = 'decimal'
): string {
  if (format === 'decimal') {
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
  } else {
    const latDir = lat >= 0 ? 'N' : 'S'
    const lngDir = lng >= 0 ? 'E' : 'W'

    const formatDMS = (deg: number) => {
      deg = Math.abs(deg)
      const d = Math.floor(deg)
      const m = Math.floor((deg - d) * 60)
      const s = ((deg - d) * 60 - m) * 60
      return `${d}°${m}'${s.toFixed(1)}"`
    }

    return `${formatDMS(lat)}${latDir} ${formatDMS(lng)}${lngDir}`
  }
}

/**
 * 格式化JSON
 * @param data 数据
 * @param indent 缩进
 */

export function formatJSON(data: unknown, indent: number = 2): string {
  return JSON.stringify(data, null, indent)
}

/**
 * 格式化XML（简单格式化）
 * @param xml XML字符串
 * @param indent 缩进
 */
export function formatXML(xml: string, indent: string = '  '): string {
  let formatted = ''
  let pad = 0

  xml.split(/>\s*</).forEach(node => {
    if (node.match(/^\/\w/)) pad-- // 结束标签

    formatted += indent.repeat(Math.max(0, pad)) + '<' + node + '>\n'

    if (node.match(/^<?\w[^>]*[^\/]$/)) pad++ // 开始标签
  })

  return formatted.substring(1, formatted.length - 2)
}
