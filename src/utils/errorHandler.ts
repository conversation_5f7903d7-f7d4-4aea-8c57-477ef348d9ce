/**
 * 统一错误处理中心
 * 提供全局错误捕获、处理、上报功能
 */

import { ElMessage, ElNotification, ElMessageBox } from 'element-plus'
import router from '@/router'
import { useUserStore } from '@/stores'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'NETWORK',
  BUSINESS = 'BUSINESS',
  PERMISSION = 'PERMISSION',
  VALIDATION = 'VALIDATION',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN'
}

// 错误级别枚举
export enum ErrorLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  level: ErrorLevel
  code?: string
  message: string

  details?: unknown
  stack?: string
  url?: string
  method?: string

  params?: unknown
  timestamp: number
  userId?: string
  sessionId?: string
  userAgent?: string
}

// 错误处理配置
interface ErrorHandlerConfig {
  // 是否显示错误提示
  showMessage: boolean
  // 是否上报错误
  reportError: boolean
  // 错误上报地址
  reportUrl?: string
  // 最大错误日志数
  maxLogSize: number
  // 忽略的错误消息
  ignoreMessages: string[]
  // 自定义错误处理器
  customHandlers: Map<ErrorType, (error: ErrorInfo) => void>
}

class ErrorHandler {
  private config: ErrorHandlerConfig
  private errorLogs: ErrorInfo[] = []
  private reportQueue: ErrorInfo[] = []
  private reportTimer: number | null = null

  constructor() {
    this.config = {
      showMessage: true,
      reportError: true,
      reportUrl: import.meta.env.VITE_ERROR_REPORT_URL,
      maxLogSize: 100,
      ignoreMessages: [
        'Network Error', 
        'Request aborted', 
        'canceled', 
        'timeout',
        'ResizeObserver loop completed with undelivered notifications',
        'ResizeObserver loop limit exceeded'
      ],
      customHandlers: new Map()
    }

    // 初始化全局错误处理
    this.setupGlobalHandlers()
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalHandlers() {
    // Vue错误处理
    if (typeof window !== 'undefined') {
      // 捕获未处理的Promise错误
      window.addEventListener('unhandledrejection', event => {
        this.handleError({
          type: ErrorType.SYSTEM,
          level: ErrorLevel.ERROR,
          message: event.reason?.message || 'Unhandled Promise rejection',
          details: event.reason,
          stack: event.reason?.stack
        })
        event.preventDefault()
      })

      // 捕获全局错误
      window.addEventListener('error', (event: ErrorEvent) => {
        // 忽略 ResizeObserver 相关错误
        if (event.message && (
          event.message.includes('ResizeObserver loop') ||
          event.message.includes('ResizeObserver loop limit exceeded')
        )) {
          event.preventDefault()
          return
        }
        
        this.handleError({
          type: ErrorType.SYSTEM,
          level: ErrorLevel.ERROR,
          message: event.message || 'Unknown error',
          details: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          },
          stack: event.error?.stack
        })
        event.preventDefault()
      })
    }
  }

  /**
   * 处理错误
   */
  handleError(error: Partial<ErrorInfo>) {
    // 补充错误信息
    const errorInfo: ErrorInfo = {
      type: error.type || ErrorType.UNKNOWN,
      level: error.level || ErrorLevel.ERROR,
      message: error.message || '未知错误',
      timestamp: Date.now(),
      ...error
    }

    // 添加上下文信息
    this.enrichErrorInfo(errorInfo)

    // 检查是否应该忽略
    if (this.shouldIgnoreError(errorInfo)) {
      return
    }

    // 记录错误日志
    this.logError(errorInfo)

    // 显示错误提示
    if (this.config.showMessage) {
      this.showErrorMessage(errorInfo)
    }

    // 执行自定义处理器
    const customHandler = this.config.customHandlers.get(errorInfo.type)
    if (customHandler) {
      customHandler(errorInfo)
    }

    // 处理特定类型错误
    this.handleSpecificError(errorInfo)

    // 上报错误
    if (this.config.reportError && errorInfo.level >= ErrorLevel.ERROR) {
      this.reportError(errorInfo)
    }
  }

  /**
   * 补充错误上下文信息
   */
  private enrichErrorInfo(error: ErrorInfo) {
    try {
      const userStore = useUserStore()
      error.userId = userStore.userInfo?.id
      error.sessionId = sessionStorage.getItem('sessionId') || undefined
      error.userAgent = navigator.userAgent
      error.url = window.location.href
    } catch (e) {
      }
  }

  /**
   * 判断是否应该忽略错误
   */
  private shouldIgnoreError(error: ErrorInfo): boolean {
    return this.config.ignoreMessages.some(msg =>
      error.message.toLowerCase().includes(msg.toLowerCase())
    )
  }

  /**
   * 记录错误日志
   */
  private logError(error: ErrorInfo) {
    this.errorLogs.push(error)

    // 限制日志大小
    if (this.errorLogs.length > this.config.maxLogSize) {
      this.errorLogs = this.errorLogs.slice(-this.config.maxLogSize)
    }

    // 控制台输出
    const logMethod = this.getConsoleMethod(error.level)
    if (console && typeof console[logMethod] === 'function') {
      console[logMethod](`[${error.type}]`, error.message, error)
    } else {
      console.log(`[${error.type}]`, error.message, error)
    }
  }

  /**
   * 获取控制台方法
   */
  private getConsoleMethod(level: ErrorLevel): keyof Console {
    const methodMap: Record<ErrorLevel, keyof Console> = {
      [ErrorLevel.DEBUG]: 'debug',
      [ErrorLevel.INFO]: 'info',
      [ErrorLevel.WARNING]: 'warn',
      [ErrorLevel.ERROR]: 'error',
      [ErrorLevel.CRITICAL]: 'error'
    }
    return methodMap[level] || 'log'
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(error: ErrorInfo) {
    const messageMap: Record<ErrorType, string> = {
      [ErrorType.NETWORK]: '网络连接异常，请检查网络设置',
      [ErrorType.BUSINESS]: error.message,
      [ErrorType.PERMISSION]: '您没有权限执行此操作',
      [ErrorType.VALIDATION]: '数据验证失败，请检查输入',
      [ErrorType.SYSTEM]: '系统错误，请稍后重试',
      [ErrorType.UNKNOWN]: '发生未知错误'
    }

    const message = messageMap[error.type] || error.message

    // 根据错误级别选择提示方式
    if (error.level >= ErrorLevel.ERROR) {
      ElNotification({
        title: '错误',
        message,
        type: 'error',
        duration: 5000,
        position: 'top-right'
      })
    } else if (error.level === ErrorLevel.WARNING) {
      ElMessage.warning(message)
    } else {
      ElMessage.info(message)
    }
  }

  /**
   * 处理特定类型错误
   */
  private handleSpecificError(error: ErrorInfo) {
    switch (error.type) {
      case ErrorType.PERMISSION:
        // 权限错误，可能需要重新登录
        if (error.code === '401' || error.code === 'UNAUTHORIZED') {
          this.handleUnauthorized()
        } else if (error.code === '403' || error.code === 'FORBIDDEN') {
          this.handleForbidden()
        }
        break

      case ErrorType.NETWORK:
        // 网络错误，可能需要重试
        this.handleNetworkError(error)
        break

      case ErrorType.BUSINESS:
        // 业务错误，通常只需要提示
        break

      case ErrorType.SYSTEM:
        // 系统错误，可能需要刷新页面
        if (error.level === ErrorLevel.CRITICAL) {
          this.handleCriticalError(error)
        }
        break
    }
  }

  /**
   * 处理未授权错误
   */
  private handleUnauthorized() {
    const userStore = useUserStore()
    userStore.resetUser()

    ElMessage.error('登录已过期，请重新登录')

    router.push({
      path: '/login',
      query: { redirect: router.currentRoute.value.fullPath }
    })
  }

  /**
   * 处理禁止访问错误
   */
  private handleForbidden() {
    ElMessage.error('您没有权限访问此资源')
    router.push('/')
  }

  /**
   * 处理网络错误
   */
  private handleNetworkError(error: ErrorInfo) {
    // 可以实现重试机制
    ElNotification({
      title: '网络错误',
      type: 'error',
      duration: 0,
      position: 'bottom-right',
      dangerouslyUseHTMLString: true,
      message: `
        <div>
          <p>网络连接失败，请检查网络设置</p>
          <p>${error.message}</p>
          <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 10px; background: #409EFF; color: white; border: none; border-radius: 3px; cursor: pointer;">
            刷新页面
          </button>
        </div>
      `
    })
  }

  /**
   * 处理严重错误
   */
  private handleCriticalError(_error: ErrorInfo) {
    ElMessageBox.alert(
      '系统发生严重错误，建议刷新页面重试。如果问题持续存在，请联系技术支持。',
      '系统错误',
      {
        confirmButtonText: '刷新页面',
        type: 'error',
        center: true,
        callback: () => {
          window.location.reload()
        }
      }
    )
  }

  /**
   * 上报错误
   */
  private async reportError(error: ErrorInfo) {
    if (!this.config.reportUrl) return

    // 添加到上报队列
    this.reportQueue.push(error)

    // 批量上报，避免频繁请求
    if (!this.reportTimer) {
      this.reportTimer = window.setTimeout(() => {
        this.flushReportQueue()
      }, 5000) // 5秒后上报
    }

    // 如果队列过大，立即上报
    if (this.reportQueue.length >= 10) {
      this.flushReportQueue()
    }
  }

  /**
   * 批量上报错误
   */
  private async flushReportQueue() {
    if (this.reportQueue.length === 0) return

    const errors = [...this.reportQueue]
    this.reportQueue = []

    if (this.reportTimer) {
      clearTimeout(this.reportTimer)
      this.reportTimer = null
    }

    try {
      const response = await fetch(this.config.reportUrl!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          errors,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      })

      if (!response.ok) {
        }
    } catch (e) {
      // 上报失败，将错误放回队列
      this.reportQueue.unshift(...errors)
    }
  }

  /**
   * 配置错误处理器
   */
  configure(options: Partial<ErrorHandlerConfig>) {
    this.config = { ...this.config, ...options }
  }

  /**
   * 注册自定义错误处理器
   */
  registerHandler(type: ErrorType, handler: (error: ErrorInfo) => void) {
    this.config.customHandlers.set(type, handler)
  }

  /**
   * 获取错误日志
   */
  getErrorLogs(filter?: { type?: ErrorType; level?: ErrorLevel }) {
    if (!filter) return [...this.errorLogs]

    return this.errorLogs.filter(log => {
      if (filter.type && log.type !== filter.type) return false
      if (filter.level && log.level < filter.level) return false
      return true
    })
  }

  /**
   * 清空错误日志
   */
  clearErrorLogs() {
    this.errorLogs = []
  }

  /**
   * 导出错误日志
   */
  exportErrorLogs(): string {
    const logs = this.errorLogs.map(log => ({
      ...log,
      timestamp: new Date(log.timestamp).toISOString()
    }))

    return JSON.stringify(logs, null, 2)
  }
}

// 创建单例
export const errorHandler = new ErrorHandler()

// 便捷方法

export function handleError(error: unknown, type: ErrorType = ErrorType.UNKNOWN) {
  if (error instanceof Error) {
    errorHandler.handleError({
      type,
      message: error.message,
      stack: error.stack,
      details: error
    })
  } else if (typeof error === 'string') {
    errorHandler.handleError({
      type,
      message: error
    })
  } else {
    errorHandler.handleError({
      type,
      message: (error as { message?: string })?.message || '未知错误',
      details: error
    })
  }
}

// 网络错误处理

export function handleNetworkError(error: unknown) {
  handleError(error, ErrorType.NETWORK)
}

// 业务错误处理

export function handleBusinessError(error: unknown) {
  handleError(error, ErrorType.BUSINESS)
}

// 权限错误处理

export function handlePermissionError(error: unknown) {
  handleError(error, ErrorType.PERMISSION)
}

// 验证错误处理

export function handleValidationError(error: unknown) {
  handleError(error, ErrorType.VALIDATION)
}

// 系统错误处理

export function handleSystemError(error: unknown) {
  handleError(error, ErrorType.SYSTEM)
}

// Vue应用错误处理器
import type { App, ComponentPublicInstance } from 'vue'

export function setupErrorHandler(app: App) {
  app.config.errorHandler = (err: Error, instance: ComponentPublicInstance | null, info: string) => {
    errorHandler.handleError({
      type: ErrorType.SYSTEM,
      level: ErrorLevel.ERROR,
      message: err.message,
      stack: err.stack,
      details: {
        componentInfo: info,
        componentName: instance?.$options?.name || 'Unknown'
      }
    })
  }

  app.config.warnHandler = (msg: string, instance: ComponentPublicInstance | null, trace: string) => {
    errorHandler.handleError({
      type: ErrorType.SYSTEM,
      level: ErrorLevel.WARNING,
      message: msg,
      details: {
        componentTrace: trace,
        componentName: instance?.$options?.name || 'Unknown'
      }
    })
  }
}

export default errorHandler
