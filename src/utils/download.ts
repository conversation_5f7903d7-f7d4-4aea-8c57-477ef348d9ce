import { ElMessage } from 'element-plus'
import request from './request'
import J<PERSON><PERSON><PERSON> from 'jszip'

/**
 * 简单下载Blob文件
 * @param blob Blob对象
 * @param filename 文件名
 */
export function downloadFile(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'

  document.body.appendChild(link)
  link.click()

  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * 下载文件（支持权限验证）
 * @param url 文件URL
 * @param filename 文件名（可选）
 * @param options 下载选项
 */
export async function downloadFileWithAuth(
  url: string,
  filename?: string,
  options?: {
    method?: 'GET' | 'POST'
    data?: unknown
    onProgress?: (progress: number) => void
  }
) {
  try {
    const response = await request({
      url,
      method: options?.method || 'GET',
      data: options?.data,
      responseType: 'blob',
      onDownloadProgress: progressEvent => {
        if (options?.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          options.onProgress(progress)
        }
      }
    })

    // 从响应头获取文件名
    if (!filename) {
      const contentDisposition = response.headers['content-disposition']
      if (contentDisposition) {
        const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition)
        if (matches != null && matches[1]) {
          filename = matches[1].replace(/['"]/g, '')
          // 解码文件名
          try {
            filename = decodeURIComponent(filename)
          } catch (error) {
            // 如果解码失败，使用原始文件名
            console.warn('Failed to decode filename:', error)
          }
        }
      }
    }

    // 创建Blob URL并下载
    const blob = new Blob([response.data])
    const blobUrl = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = blobUrl
    link.download = filename || 'download'
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 释放Blob URL
    setTimeout(() => URL.revokeObjectURL(blobUrl), 100)

    return true
  } catch (error: unknown) {
    // 处理特定错误
    const axiosError = error as { response?: { status?: number }; message?: string }

    if (axiosError.response?.status === 404) {
      ElMessage.error('文件不存在')
    } else if (axiosError.response?.status === 403) {
      ElMessage.error('没有下载权限')
    } else {
      ElMessage.error(axiosError.message || '文件下载失败')
    }

    return false
  }
}

/**
 * 批量下载文件（别名）
 * @param files 文件列表
 * @param options 下载选项
 */
export async function downloadFiles(
  files: Array<{ url: string; filename?: string }>,
  options?: {
    onProgress?: (current: number, total: number) => void
    delay?: number // 下载间隔（毫秒）
  }
) {
  return batchDownloadFiles(files, options)
}

/**
 * 批量下载文件
 * @param files 文件列表
 * @param options 下载选项
 */
export async function batchDownloadFiles(
  files: Array<{ url: string; filename?: string }>,
  options?: {
    onProgress?: (current: number, total: number) => void
    delay?: number // 下载间隔（毫秒）
  }
) {
  const results: Array<{ success: boolean; filename?: string; error?: string }> = []

  for (let i = 0; i < files.length; i++) {
    const file = files[i]

    if (options?.onProgress) {
      options.onProgress(i + 1, files.length)
    }

    const success = await downloadFileWithAuth(file.url, file.filename)
    results.push({
      success,
      filename: file.filename,
      error: success ? undefined : '下载失败'
    })

    // 添加延迟，避免同时发起太多请求
    if (options?.delay && i < files.length - 1) {
      await new Promise(resolve => setTimeout(resolve, options.delay))
    }
  }

  // 显示下载结果
  const successCount = results.filter(r => r.success).length
  const failedCount = results.length - successCount

  if (failedCount === 0) {
    ElMessage.success(`成功下载 ${successCount} 个文件`)
  } else {
    ElMessage.warning(`成功下载 ${successCount} 个文件，失败 ${failedCount} 个`)
  }

  return results
}

/**
 * 下载Base64文件
 * @param base64Data Base64数据
 * @param filename 文件名
 * @param mimeType MIME类型
 */
export function downloadBase64File(
  base64Data: string,
  filename: string,
  mimeType: string = 'application/octet-stream'
) {
  try {
    // 移除Base64前缀（如果有）
    const base64 = base64Data.replace(/^data:[^;]+;base64,/, '')

    // 将Base64转换为Blob
    const byteCharacters = atob(base64)
    const byteNumbers = new Array(byteCharacters.length)

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i)
    }

    const byteArray = new Uint8Array(byteNumbers)
    const blob = new Blob([byteArray], { type: mimeType })

    // 创建下载链接
    const blobUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = filename

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 释放Blob URL
    setTimeout(() => URL.revokeObjectURL(blobUrl), 100)

    return true
  } catch (error) {
    console.error('Base64 file download failed:', error)
    ElMessage.error('文件下载失败')
    return false
  }
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 */
export function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.')
  if (lastDotIndex === -1) return ''
  return filename.substring(lastDotIndex + 1).toLowerCase()
}

/**
 * 获取文件MIME类型
 * @param filename 文件名
 */
export function getFileMimeType(filename: string): string {
  const ext = getFileExtension(filename)
  const mimeTypes: Record<string, string> = {
    // 文档
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',

    // 图片
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    bmp: 'image/bmp',
    svg: 'image/svg+xml',

    // 文本
    txt: 'text/plain',
    csv: 'text/csv',
    json: 'application/json',
    xml: 'application/xml',

    // 压缩包
    zip: 'application/zip',
    rar: 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed',

    // 其他
    mp3: 'audio/mpeg',
    mp4: 'video/mp4',
    avi: 'video/x-msvideo'
  }

  return mimeTypes[ext] || 'application/octet-stream'
}

/**
 * 档案文件信息接口
 */
export interface ArchiveFile {
  id: string
  name: string
  url: string
  size?: number
  category?: string
  createTime?: string
  description?: string
}

/**
 * 压缩包下载选项
 */
export interface ArchiveDownloadOptions {
  filename?: string
  includeMetadata?: boolean
  onProgress?: (progress: number, current: string) => void
  compression?: 'STORE' | 'DEFLATE'
  compressionLevel?: number
}

/**
 * 创建档案压缩包并下载
 * @param files 档案文件列表
 * @param options 下载选项
 */
export async function downloadArchiveAsZip(
  files: ArchiveFile[],
  options: ArchiveDownloadOptions = {}
): Promise<boolean> {
  if (files.length === 0) {
    ElMessage.warning('没有可下载的档案文件')
    return false
  }

  try {
    const zip = new JSZip()
    const {
      filename = '档案压缩包.zip',
      includeMetadata = true,
      onProgress,
      compression = 'DEFLATE',
      compressionLevel = 6
    } = options

    // 添加档案文件到压缩包
    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      if (onProgress) {
        onProgress(
          Math.floor((i / files.length) * 100),
          `正在处理第 ${i + 1}/${files.length} 个档案文件`
        )
      }

      try {
        // 下载文件内容
        const response = await request({
          url: file.url,
          method: 'GET',
          responseType: 'blob'
        })

        // 创建分类文件夹
        const category = file.category || '其他文件'
        const categoryFolder = zip.folder(category)!

        // 添加文件到压缩包
        categoryFolder.file(file.name, response.data)

        // 如果需要，添加元数据
        if (includeMetadata && file.description) {
          const metadataFile = `${file.name}_metadata.json`
          const metadata = {
            id: file.id,
            name: file.name,
            size: file.size,
            category: file.category,
            createTime: file.createTime,
            description: file.description
          }
          categoryFolder.file(metadataFile, JSON.stringify(metadata, null, 2))
        }
      } catch (fileError) {
        console.warn(`Failed to download file ${file.name}:`, fileError)
      }
    }

    // 生成压缩包
    if (onProgress) {
      onProgress(90, '正在生成压缩包')
    }

    const zipBlob = await zip.generateAsync({
      type: 'blob',
      compression,
      compressionOptions: { level: compressionLevel }
    })

    // 下载压缩包
    const blobUrl = URL.createObjectURL(zipBlob)
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = filename
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    setTimeout(() => URL.revokeObjectURL(blobUrl), 100)

    if (onProgress) {
      onProgress(100, '档案压缩包下载完成')
    }

    ElMessage.success(`档案压缩包下载完成，包含 ${files.length} 个文件`)
    return true
  } catch (error: unknown) {
    const errorMessage = (error as { message?: string }).message || '未知错误'
    console.error('Archive download failed:', error)
    ElMessage.error(`档案压缩包下载失败: ${errorMessage}`)
    return false
  }
}
