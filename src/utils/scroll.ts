/**
 * 滚动行为优化工具
 * 提供平滑滚动、锚点定位、滚动监听等功能
 */

import { ref, unref, type Ref } from 'vue'

// 滚动配置接口
export interface ScrollOptions {
  // 滚动行为
  behavior?: ScrollBehavior
  // 垂直对齐方式
  block?: ScrollLogicalPosition
  // 水平对齐方式
  inline?: ScrollLogicalPosition
  // 偏移量
  offset?: number
  // 动画时长（仅自定义滚动时有效）
  duration?: number
  // 缓动函数
  easing?: EasingFunction
  // 滚动完成回调
  onComplete?: () => void
}

// 缓动函数类型
export type EasingFunction = (t: number) => number

// 预定义缓动函数
export const easings = {
  // 线性
  linear: (t: number) => t,
  // 缓入
  easeIn: (t: number) => t * t,
  // 缓出
  easeOut: (t: number) => t * (2 - t),
  // 缓入缓出
  easeInOut: (t: number) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),
  // 弹性
  easeInElastic: (t: number) => {
    const c4 = (2 * Math.PI) / 3
    return t === 0
      ? 0
      : t === 1
      ? 1
      : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4)
  },
  // 回弹
  easeOutBounce: (t: number) => {
    const n1 = 7.5625
    const d1 = 2.75
    if (t < 1 / d1) {
      return n1 * t * t
    } else if (t < 2 / d1) {
      return n1 * (t -= 1.5 / d1) * t + 0.75
    } else if (t < 2.5 / d1) {
      return n1 * (t -= 2.25 / d1) * t + 0.9375
    } else {
      return n1 * (t -= 2.625 / d1) * t + 0.984375
    }
  }
}

/**
 * 平滑滚动到指定位置
 */
export function scrollTo(x: number, y: number, options: ScrollOptions = {}): Promise<void> {
  const { behavior = 'smooth', duration = 500, easing = easings.easeInOut, onComplete } = options

  return new Promise((resolve) => {
    if (behavior === 'smooth' && 'scrollTo' in window) {
      window.scrollTo({ left: x, top: y, behavior })
      // 原生smooth滚动无法精确控制完成时间，这里用近似值
      setTimeout(() => {
        onComplete?.()
        resolve()
      }, duration)
    } else {
      // 自定义滚动动画
      smoothScrollTo({ x, y }, { duration, easing }).then(() => {
        onComplete?.()
        resolve()
      })
    }
  })
}

/**
 * 滚动到页面顶部
 */
export function scrollToTop(options: ScrollOptions = {}): Promise<void> {
  return scrollTo(0, 0, options)
}

/**
 * 滚动到页面底部
 */
export function scrollToBottom(options: ScrollOptions = {}): Promise<void> {
  const maxScrollTop = document.documentElement.scrollHeight - window.innerHeight
  return scrollTo(0, maxScrollTop, options)
}

/**
 * 平滑滚动到指定元素
 */
export function scrollToElement(element: HTMLElement | string, options: ScrollOptions = {}): Promise<void> {
  return new Promise((resolve) => {
    const {
      behavior = 'smooth',
      block = 'start',
      inline = 'nearest',
      offset = 0,
      duration = 500,
      easing = easings.easeInOut,
      onComplete
    } = options

    const targetElement = typeof element === 'string' ? document.querySelector(element) as HTMLElement : element

    if (!targetElement) {
      console.warn('Target element not found')
      resolve()
      return
    }

    if (behavior === 'smooth' && 'scrollIntoView' in targetElement) {
      targetElement.scrollIntoView({ behavior, block, inline })
      setTimeout(() => {
        onComplete?.()
        resolve()
      }, duration)
    } else {
      // 自定义滚动
      const targetPosition = getElementPosition(targetElement, block as ScrollLogicalPosition, offset)
      smoothScrollTo(targetPosition, { duration, easing }).then(() => {
        onComplete?.()
        resolve()
      })
    }
  })
}

/**
 * 创建锚点导航
 */
export function useAnchorNavigation(
  anchors: Ref<string[]> | string[],
  options: {
    offset?: number
    activeClass?: string
    onActiveChange?: (anchor: string, index: number) => void
  } = {}
) {
  const { offset = 0, activeClass = 'active', onActiveChange } = options
  
  const activeAnchor = ref('')
  const activeIndex = ref(-1)

  // 更新活动锚点
  const updateActiveAnchor = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const anchorList = unref(anchors)
    const elements = anchorList.map(anchor => document.querySelector(anchor) as HTMLElement).filter(Boolean)

    // 从下往上查找第一个在视口上方的元素
    for (let i = elements.length - 1; i >= 0; i--) {
      const element = elements[i]
      const elementTop = element.getBoundingClientRect().top + scrollTop - offset

      if (scrollTop >= elementTop) {
        const anchor = unref(anchors)[i]
        if (activeAnchor.value !== anchor) {
          // 移除旧的活动类
          if (activeAnchor.value) {
            const oldElement = document.querySelector(activeAnchor.value)
            oldElement?.classList.remove(activeClass)
          }

          // 添加新的活动类
          activeAnchor.value = anchor
          activeIndex.value = i
          element.classList.add(activeClass)
          onActiveChange?.(anchor, i)
        }
        break
      }
    }

    // 如果都不满足，清除活动状态
    if (scrollTop < (elements[0]?.getBoundingClientRect().top + scrollTop - offset)) {
      if (activeAnchor.value) {
        const oldElement = document.querySelector(activeAnchor.value)
        oldElement?.classList.remove(activeClass)
        activeAnchor.value = ''
        activeIndex.value = -1
      }
    }
  }

  // 滚动到指定锚点
  const scrollToAnchor = (anchor: string | number) => {
    const anchorList = unref(anchors)
    const targetAnchor = typeof anchor === 'number' ? anchorList[anchor] : anchor

    if (targetAnchor) {
      scrollToElement(targetAnchor, { offset, behavior: 'smooth' })
    }
  }

  // 监听滚动
  let scrollHandler: (() => void) | null = null

  const start = () => {
    scrollHandler = throttle(updateActiveAnchor, 100)
    window.addEventListener('scroll', scrollHandler)
    // 初始更新
    updateActiveAnchor()
  }

  const stop = () => {
    if (scrollHandler) {
      window.removeEventListener('scroll', scrollHandler)
      scrollHandler = null
    }
  }

  return {
    activeAnchor,
    activeIndex,
    scrollToAnchor,
    start,
    stop,
    updateActiveAnchor
  }
}

/**
 * 创建滚动监听器
 */
export function useScrollListener(options: {
  threshold?: number
  onScroll?: (event: Event) => void
  onScrollUp?: (distance: number) => void
  onScrollDown?: (distance: number) => void
  onReachTop?: () => void
  onReachBottom?: () => void
} = {}) {
  const {
    threshold = 50,
    onScroll,
    onScrollUp,
    onScrollDown,
    onReachTop,
    onReachBottom
  } = options

  let lastScrollTop = 0
  let isListening = false

  const handleScroll = (event: Event) => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollHeight = document.documentElement.scrollHeight
    const clientHeight = window.innerHeight
    const distance = Math.abs(scrollTop - lastScrollTop)

    // 滚动事件
    onScroll?.(event)

    // 滚动方向检测
    if (distance > threshold) {
      if (scrollTop > lastScrollTop) {
        onScrollDown?.(distance)
      } else {
        onScrollUp?.(distance)
      }
      lastScrollTop = scrollTop
    }

    // 到达顶部
    if (scrollTop <= threshold) {
      onReachTop?.()
    }

    // 到达底部
    if (scrollTop + clientHeight >= scrollHeight - threshold) {
      onReachBottom?.()
    }
  }

  const start = () => {
    if (!isListening) {
      window.addEventListener('scroll', handleScroll, { passive: true })
      isListening = true
      lastScrollTop = window.pageYOffset || document.documentElement.scrollTop
    }
  }

  const stop = () => {
    if (isListening) {
      window.removeEventListener('scroll', handleScroll)
      isListening = false
    }
  }

  return { start, stop }
}

/**
 * 获取元素位置
 */
function getElementPosition(element: HTMLElement, block: ScrollLogicalPosition, offset: number): { x: number; y: number } {
  const rect = element.getBoundingClientRect()
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

  let y = rect.top + scrollTop
  const x = rect.left + scrollLeft

  // 根据对齐方式调整位置
  switch (block) {
    case 'center':
      y = y + rect.height / 2 - window.innerHeight / 2
      break
    case 'end':
      y = y + rect.height - window.innerHeight
      break
    case 'nearest':
      // 如果元素在视口内，不滚动
      if (rect.top >= 0 && rect.bottom <= window.innerHeight) {
        y = scrollTop
      } else if (rect.top < 0) {
        y = rect.top + scrollTop
      } else {
        y = rect.bottom + scrollTop - window.innerHeight
      }
      break
  }

  // 应用偏移量
  y -= offset

  return { x, y }
}

/**
 * 自定义平滑滚动实现
 */
function smoothScrollTo(target: { x: number; y: number }, options: { duration: number; easing: EasingFunction }): Promise<void> {
  return new Promise((resolve) => {
    const startX = window.pageXOffset || document.documentElement.scrollLeft
    const startY = window.pageYOffset || document.documentElement.scrollTop
    const distanceX = target.x - startX
    const distanceY = target.y - startY
    const startTime = performance.now()

    const scroll = (currentTime: number) => {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / options.duration, 1)
      const easedProgress = options.easing(progress)

      window.scrollTo(
        startX + distanceX * easedProgress,
        startY + distanceY * easedProgress
      )

      if (progress < 1) {
        requestAnimationFrame(scroll)
      } else {
        resolve()
      }
    }

    requestAnimationFrame(scroll)
  })
}

/**
 * 节流函数
 */
function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  delay: number
): T {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let lastExecTime = 0

  return ((...args: Parameters<T>) => {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      lastExecTime = currentTime
      func(...args)
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = setTimeout(() => {
        lastExecTime = Date.now()
        func(...args)
      }, delay - (currentTime - lastExecTime))
    }
  }) as T
}

/**
 * 获取滚动容器
 */
export function getScrollContainer(element?: HTMLElement): HTMLElement | Window {
  if (!element) return window

  let parent = element.parentElement
  while (parent) {
    const { overflow, overflowY } = window.getComputedStyle(parent)
    if (overflow === 'auto' || overflow === 'scroll' || overflowY === 'auto' || overflowY === 'scroll') {
      return parent
    }
    parent = parent.parentElement
  }

  return window
}

/**
 * 锁定/解锁滚动
 */
export function lockScroll(lock = true) {
  if (lock) {
    document.body.style.overflow = 'hidden'
    document.body.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`
  } else {
    document.body.style.overflow = ''
    document.body.style.paddingRight = ''
  }
}

// 导出默认滚动器实例
export default {
  scrollTo,
  scrollToTop,
  scrollToBottom,
  scrollToElement,
  lockScroll,
  easings
}