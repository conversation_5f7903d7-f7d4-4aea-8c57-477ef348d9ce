/**
 * 机构编码生成器
 * 支持多种编码规则
 */

import type { OrganizationType } from '@/types/organization'

// 编码规则配置
export interface CodeRule {
  prefix: string // 前缀
  separator?: string // 分隔符
  length: number // 数字部分长度
  startNumber?: number // 起始编号
}

// 机构类型编码前缀映射
const TYPE_PREFIX_MAP: Record<OrganizationType, string> = {
  SCHOOL: 'SCH', // 学校
  COLLEGE: 'COL', // 学院
  DEPARTMENT: 'DEP', // 部门
  TEACHING_DEPT: 'TEA', // 教学部
  RESEARCH_INST: 'RES', // 科研机构
  ADMIN_DEPT: 'ADM', // 行政机构
  DIRECT_UNIT: 'DIR', // 直属单位
  TEMP_ORG: 'TMP', // 临时组织
  OTHER: 'OTH' // 其他
}

// 默认编码规则
const DEFAULT_RULE: CodeRule = {
  prefix: 'ORG',
  separator: '',
  length: 4,
  startNumber: 1
}

export class OrgCodeGenerator {
  private rules: Map<OrganizationType, CodeRule>
  private counters: Map<string, number>

  constructor() {
    this.rules = new Map()
    this.counters = new Map()
    this.initDefaultRules()
  }

  /**
   * 初始化默认规则
   */
  private initDefaultRules() {
    // 为每种类型设置默认规则
    Object.entries(TYPE_PREFIX_MAP).forEach(([type, prefix]) => {
      this.rules.set(type as OrganizationType, {
        prefix,
        separator: '',
        length: 3,
        startNumber: 1
      })
    })
  }

  /**
   * 设置类型规则
   */
  setRule(type: OrganizationType, rule: CodeRule) {
    this.rules.set(type, rule)
  }

  /**
   * 生成机构编码
   */
  generate(type: OrganizationType, parentCode?: string): string {
    const rule = this.rules.get(type) || DEFAULT_RULE

    if (parentCode) {
      // 基于父级编码生成子编码
      return this.generateChildCode(parentCode, rule)
    } else {
      // 生成顶级编码
      return this.generateTopCode(type, rule)
    }
  }

  /**
   * 生成顶级编码
   */
  private generateTopCode(type: OrganizationType, rule: CodeRule): string {
    const counterKey = `${type}_TOP`
    const currentCount = this.counters.get(counterKey) || (rule.startNumber || 1) - 1
    const nextCount = currentCount + 1
    this.counters.set(counterKey, nextCount)

    const numberStr = this.padNumber(nextCount, rule.length)
    return `${rule.prefix}${rule.separator || ''}${numberStr}`
  }

  /**
   * 生成子级编码
   */
  private generateChildCode(parentCode: string, rule: CodeRule): string {
    const counterKey = `CHILD_${parentCode}`
    const currentCount = this.counters.get(counterKey) || 0
    const nextCount = currentCount + 1
    this.counters.set(counterKey, nextCount)

    const numberStr = this.padNumber(nextCount, 2)
    return `${parentCode}${numberStr}`
  }

  /**
   * 填充数字
   */
  private padNumber(num: number, length: number): string {
    return num.toString().padStart(length, '0')
  }

  /**
   * 验证编码格式
   */
  validate(code: string, type?: OrganizationType): boolean {
    if (!code || code.length < 3) return false

    // 基本格式验证：只包含字母和数字
    if (!/^[A-Z0-9]+$/.test(code)) return false

    // 如果指定了类型，验证前缀
    if (type) {
      const rule = this.rules.get(type)
      if (rule && !code.startsWith(rule.prefix)) {
        return false
      }
    }

    return true
  }

  /**
   * 解析编码信息
   */
  parse(code: string): {
    type?: OrganizationType
    level: number
    parentCode?: string
  } {
    // 识别类型
    let type: OrganizationType | undefined
    for (const [orgType, prefix] of Object.entries(TYPE_PREFIX_MAP)) {
      if (code.startsWith(prefix)) {
        type = orgType as OrganizationType
        break
      }
    }

    // 计算层级（简化算法：每2位为一个层级）
    const level = Math.ceil((code.length - 3) / 2) + 1

    // 获取父级编码
    let parentCode: string | undefined
    if (level > 1) {
      parentCode = code.substring(0, code.length - 2)
    }

    return { type, level, parentCode }
  }

  /**
   * 批量验证编码唯一性
   */
  checkUniqueness(
    codes: string[],
    existingCodes: Set<string>
  ): {
    valid: boolean
    duplicates: string[]
  } {
    const duplicates: string[] = []
    const seen = new Set<string>()

    for (const code of codes) {
      if (seen.has(code) || existingCodes.has(code)) {
        duplicates.push(code)
      }
      seen.add(code)
    }

    return {
      valid: duplicates.length === 0,
      duplicates
    }
  }

  /**
   * 设置计数器（用于恢复状态）
   */
  setCounter(key: string, value: number) {
    this.counters.set(key, value)
  }

  /**
   * 获取所有计数器（用于持久化）
   */
  getCounters(): Record<string, number> {
    const result: Record<string, number> = {}
    this.counters.forEach((value, key) => {
      result[key] = value
    })
    return result
  }

  /**
   * 重置计数器
   */
  reset() {
    this.counters.clear()
  }
}

// 单例实例
export const orgCodeGenerator = new OrgCodeGenerator()

// 便捷方法
export function generateOrgCode(type: OrganizationType, parentCode?: string): string {
  return orgCodeGenerator.generate(type, parentCode)
}

export function validateOrgCode(code: string, type?: OrganizationType): boolean {
  return orgCodeGenerator.validate(code, type)
}

export function parseOrgCode(code: string) {
  return orgCodeGenerator.parse(code)
}
