/**
 * 工具模块类型定义
 */

// 脚本引擎相关类型
export interface ScriptVariable {
  name: string
  value: unknown
  type?: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'date'
  description?: string
}

export interface ScriptUser {
  id: string
  name: string
  username: string
  department?: string
  role?: string[]
  [key: string]: unknown
}

export interface ScriptProcess {
  id: string
  name: string
  instanceId?: string
  businessKey?: string
  variables: Record<string, unknown>
  [key: string]: unknown
}

export interface ScriptTask {
  id: string
  name: string
  assignee?: string
  candidateUsers?: string[]
  candidateGroups?: string[]
  dueDate?: Date
  [key: string]: unknown
}

export interface ServiceResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  code?: number
}

// 性能优化相关类型
export interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  scriptTime: number
  networkTime: number
  totalTime: number
}

export interface PerformanceThreshold {
  metric: keyof PerformanceMetrics
  warning: number
  critical: number
}

// 缓存相关类型
export interface CacheEntry<T = unknown> {
  key: string
  value: T
  expires: number
  tags?: string[]
  metadata?: Record<string, unknown>
}

export interface CacheOptions {
  ttl?: number
  tags?: string[]
  serializer?: (value: unknown) => string
  deserializer?: (value: string) => unknown
}

// 批处理相关类型
export interface BatchTask<T = unknown, R = unknown> {
  id: string
  data: T
  result?: R
  error?: Error
  status: 'pending' | 'processing' | 'completed' | 'failed'
  retryCount?: number
}

export interface BatchOptions {
  concurrency?: number
  retryAttempts?: number
  retryDelay?: number
  onProgress?: (progress: number, completed: number, total: number) => void
  onError?: (error: Error, task: BatchTask) => void
}

// 图表优化相关类型
export interface ChartData {
  labels: string[]
  datasets: ChartDataset[]
  metadata?: Record<string, unknown>
}

export interface ChartDataset {
  label: string
  data: number[]
  backgroundColor?: string | string[]
  borderColor?: string | string[]
  [key: string]: unknown
}

export interface ChartOptions {
  responsive?: boolean
  maintainAspectRatio?: boolean
  plugins?: Record<string, unknown>
  scales?: Record<string, unknown>
  [key: string]: unknown
}

// Worker相关类型
export interface WorkerMessage<T = unknown> {
  id: string
  type: 'request' | 'response' | 'error'
  method: string
  params?: T
  result?: unknown
  error?: string
}

export interface WorkerTask<T = unknown, R = unknown> {
  id: string
  method: string
  params: T
  resolve: (result: R) => void
  reject: (error: Error) => void
  timeout?: NodeJS.Timeout
}

// 对象工具类型
export interface DeepMergeOptions {
  arrays?: 'merge' | 'replace' | 'concat'
  customMerge?: (key: string, target: unknown, source: unknown) => unknown
  isMergeable?: (value: unknown) => boolean
}

export interface ObjectPath {
  path: string
  value: unknown
  parent?: Record<string, unknown>
  key?: string
}

// 懒加载相关类型
export interface LazyLoadOptions {
  root?: HTMLElement | null
  rootMargin?: string
  threshold?: number | number[]
  preload?: number
  placeholder?: string
  error?: string
  attempt?: number
  throttleWait?: number
}

export interface LazyLoadResult {
  element: HTMLElement
  loaded: boolean
  error?: Error
  attempts: number
}

// 移动端优化类型
export interface MobileOptimizationOptions {
  enableGestures?: boolean
  enableVirtualScroll?: boolean
  enableImageLazyLoad?: boolean
  enablePreload?: boolean
  touchThreshold?: number
  scrollThreshold?: number
}

export interface TouchGesture {
  type: 'tap' | 'swipe' | 'pinch' | 'rotate'
  startX: number
  startY: number
  endX?: number
  endY?: number
  deltaX?: number
  deltaY?: number
  scale?: number
  rotation?: number
  duration?: number
}

// 键盘指令类型
export interface KeyboardShortcut {
  key: string
  modifiers?: ('ctrl' | 'alt' | 'shift' | 'meta')[]
  action: (event: KeyboardEvent) => void
  description?: string
  preventDefault?: boolean
  stopPropagation?: boolean
}

export interface KeyboardOptions {
  shortcuts: KeyboardShortcut[]
  enabled?: boolean
  target?: HTMLElement | Window
}

// 通用工具类型
export type ValueOf<T> = T[keyof T]
export type Entries<T> = Array<[keyof T, T[keyof T]]>
export type DeepPartial<T> = T extends object ? { [P in keyof T]?: DeepPartial<T[P]> } : T
export type DeepReadonly<T> = T extends object ? { readonly [P in keyof T]: DeepReadonly<T[P]> } : T
export type Nullable<T> = T | null | undefined
export type NonNullableDeep<T> = T extends object 
  ? { [P in keyof T]-?: NonNullableDeep<NonNullable<T[P]>> } 
  : NonNullable<T>