/**
 * 浏览器相关工具函数
 */

/**
 * 本地存储封装
 */
export const storage = {
  /**
   * 设置存储
   * @param key 键
   * @param value 值
   * @param expires 过期时间（毫秒）
   */

  set(key: string, value: unknown, expires?: number): void {
    const data = {
      value,
      expires: expires ? Date.now() + expires : null
    }
    localStorage.setItem(key, JSON.stringify(data))
  },

  /**
   * 获取存储
   * @param key 键
   * @param defaultValue 默认值
   */
  get<T = unknown>(key: string, defaultValue?: T): T | undefined {
    const item = localStorage.getItem(key)
    if (!item) return defaultValue

    try {
      const data = JSON.parse(item)
      if (data.expires && data.expires < Date.now()) {
        localStorage.removeItem(key)
        return defaultValue
      }
      return data.value
    } catch {
      return defaultValue
    }
  },

  /**
   * 移除存储
   * @param key 键
   */
  remove(key: string): void {
    localStorage.removeItem(key)
  },

  /**
   * 清空存储
   */
  clear(): void {
    localStorage.clear()
  },

  /**
   * 获取所有键
   */
  keys(): string[] {
    return Object.keys(localStorage)
  }
}

/**
 * 会话存储封装
 */
export const sessionStorage = {
  set(key: string, value: unknown): void {
    window.sessionStorage.setItem(key, JSON.stringify(value))
  },

  get<T = unknown>(key: string, defaultValue?: T): T | undefined {
    const item = window.sessionStorage.getItem(key)
    if (!item) return defaultValue

    try {
      return JSON.parse(item)
    } catch {
      return defaultValue
    }
  },

  remove(key: string): void {
    window.sessionStorage.removeItem(key)
  },

  clear(): void {
    window.sessionStorage.clear()
  },

  keys(): string[] {
    return Object.keys(window.sessionStorage)
  }
}

/**
 * Cookie操作
 */
export const cookie = {
  /**
   * 设置Cookie
   * @param name 名称
   * @param value 值
   * @param options 选项
   */
  set(
    name: string,
    value: string,
    options: {
      expires?: number | Date
      path?: string
      domain?: string
      secure?: boolean
      sameSite?: 'strict' | 'lax' | 'none'
    } = {}
  ): void {
    let cookieStr = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`

    if (options.expires) {
      const expires =
        options.expires instanceof Date ? options.expires : new Date(Date.now() + options.expires)
      cookieStr += `; expires=${expires.toUTCString()}`
    }

    if (options.path) {
      cookieStr += `; path=${options.path}`
    }

    if (options.domain) {
      cookieStr += `; domain=${options.domain}`
    }

    if (options.secure) {
      cookieStr += '; secure'
    }

    if (options.sameSite) {
      cookieStr += `; samesite=${options.sameSite}`
    }

    document.cookie = cookieStr
  },

  /**
   * 获取Cookie
   * @param name 名称
   */
  get(name: string): string | null {
    const cookies = document.cookie.split('; ')
    const prefix = `${encodeURIComponent(name)}=`

    for (const cookie of cookies) {
      if (cookie.startsWith(prefix)) {
        return decodeURIComponent(cookie.slice(prefix.length))
      }
    }

    return null
  },

  /**
   * 删除Cookie
   * @param name 名称
   * @param options 选项
   */
  remove(name: string, options: { path?: string; domain?: string } = {}): void {
    this.set(name, '', {
      ...options,
      expires: new Date(0)
    })
  },

  /**
   * 获取所有Cookie
   */
  all(): Record<string, string> {
    const cookies: Record<string, string> = {}
    document.cookie.split('; ').forEach(cookie => {
      const [name, value] = cookie.split('=')
      if (name) {
        cookies[decodeURIComponent(name)] = decodeURIComponent(value || '')
      }
    })
    return cookies
  }
}

/**
 * 剪贴板操作
 */
export const clipboard = {
  /**
   * 复制文本
   * @param text 文本
   */
  async copy(text: string): Promise<boolean> {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(text)
        return true
      }

      // 降级方案
      const textarea = document.createElement('textarea')
      textarea.value = text
      textarea.style.position = 'fixed'
      textarea.style.opacity = '0'
      document.body.appendChild(textarea)
      textarea.select()
      const success = document.execCommand('copy')
      document.body.removeChild(textarea)
      return success
    } catch {
      return false
    }
  },

  /**
   * 读取文本
   */
  async read(): Promise<string | null> {
    try {
      if (navigator.clipboard) {
        return await navigator.clipboard.readText()
      }
      return null
    } catch {
      return null
    }
  }
}

/**
 * 下载文件
 * @param url URL或Blob
 * @param filename 文件名
 */
export function download(url: string | Blob, filename?: string): void {
  const link = document.createElement('a')

  if (url instanceof Blob) {
    url = URL.createObjectURL(url)
    link.href = url
  } else {
    link.href = url
  }

  if (filename) {
    link.download = filename
  }

  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  if (url.startsWith('blob:')) {
    URL.revokeObjectURL(url)
  }
}

/**
 * 获取浏览器信息
 */
export function getBrowserInfo(): {
  name: string
  version: string
  os: string
  mobile: boolean
  language: string
} {
  const ua = navigator.userAgent

  // 浏览器检测
  let name = 'Unknown'
  let version = 'Unknown'

  if (ua.includes('Chrome')) {
    name = 'Chrome'
    version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown'
  } else if (ua.includes('Firefox')) {
    name = 'Firefox'
    version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown'
  } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
    name = 'Safari'
    version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown'
  } else if (ua.includes('Edge')) {
    name = 'Edge'
    version = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown'
  }

  // 操作系统检测
  let os = 'Unknown'
  if (ua.includes('Windows')) os = 'Windows'
  else if (ua.includes('Mac')) os = 'macOS'
  else if (ua.includes('Linux')) os = 'Linux'
  else if (ua.includes('Android')) os = 'Android'
  else if (ua.includes('iOS') || ua.includes('iPhone') || ua.includes('iPad')) os = 'iOS'

  // 移动设备检测
  const mobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua)

  return {
    name,
    version,
    os,
    mobile,
    language: navigator.language
  }
}

/**
 * 获取URL参数
 * @param url URL字符串
 */
export function getUrlParams(url: string = window.location.href): Record<string, string> {
  const params: Record<string, string> = {}
  const urlObj = new URL(url)

  urlObj.searchParams.forEach((value, key) => {
    params[key] = value
  })

  return params
}

/**
 * 构建URL
 * @param base 基础URL
 * @param params 参数
 */
export function buildUrl(base: string, params?: Record<string, unknown>): string {
  const url = new URL(base)

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.set(key, String(value))
      }
    })
  }

  return url.toString()
}

/**
 * 防止页面关闭
 * @param message 提示消息
 */
export function preventClose(message?: string): () => void {
  const handler = (e: BeforeUnloadEvent) => {
    e.preventDefault()
    e.returnValue = message || ''
    return message || ''
  }

  window.addEventListener('beforeunload', handler)

  return () => {
    window.removeEventListener('beforeunload', handler)
  }
}

/**
 * 判断是否支持某个特性
 * @param feature 特性名
 */
export function isSupported(
  feature:
    | 'localStorage'
    | 'sessionStorage'
    | 'webWorker'
    | 'serviceWorker'
    | 'notification'
    | 'geolocation'
    | 'clipboard'
    | 'webGL'
): boolean {
  switch (feature) {
    case 'localStorage':
      return 'localStorage' in window
    case 'sessionStorage':
      return 'sessionStorage' in window
    case 'webWorker':
      return 'Worker' in window
    case 'serviceWorker':
      return 'serviceWorker' in navigator
    case 'notification':
      return 'Notification' in window
    case 'geolocation':
      return 'geolocation' in navigator
    case 'clipboard':
      return 'clipboard' in navigator
    case 'webGL':
      try {
        const canvas = document.createElement('canvas')
        return !!(
          window.WebGLRenderingContext &&
          (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
        )
      } catch {
        return false
      }
  }
}

/**
 * 请求通知权限
 */
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!isSupported('notification')) {
    throw new Error('Notification API is not supported')
  }

  return await Notification.requestPermission()
}

/**
 * 发送通知
 * @param title 标题
 * @param options 选项
 */
export async function sendNotification(
  title: string,
  options?: NotificationOptions
): Promise<Notification | null> {
  if (!isSupported('notification')) {
    return null
  }

  const permission = await requestNotificationPermission()
  if (permission === 'granted') {
    return new Notification(title, options)
  }

  return null
}

/**
 * 获取网络状态
 */
export function getNetworkStatus(): {
  online: boolean
  type?: string
  downlink?: number
  rtt?: number
} {
  const connection =
    (navigator as unknown).connection ||
    (navigator as unknown).mozConnection ||
    (navigator as unknown).webkitConnection

  return {
    online: navigator.onLine,
    type: connection?.effectiveType,
    downlink: connection?.downlink,
    rtt: connection?.rtt
  }
}

/**
 * 监听网络状态变化
 * @param callback 回调函数
 */
export function watchNetworkStatus(callback: (status: { online: boolean }) => void): () => void {
  const handleOnline = () => callback({ online: true })
  const handleOffline = () => callback({ online: false })

  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)

  return () => {
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
  }
}
