/**
 * 导出工具函数
 */

import * as XLSX from 'xlsx'
interface ExportColumn {
  header: string
  key: string

  formatter?: (value: unknown) => string
  width?: number
}

/**
 * 标准版导出数据到Excel文件
 * @param data 要导出的数据
 * @param columns 列配置
 * @param filename 文件名（不含扩展名）
 * @param sheetName 工作表名称
 */
export async function exportToExcelStandard(
  data: unknown[],
  columns: ExportColumn[],
  filename: string,
  sheetName = 'Sheet1'
) {
  try {
    // 准备导出数据
    const exportData = data.map(row => {
      const exportRow: unknown = {}
      columns.forEach(col => {
        let value = row[col.key]
        if (col.formatter) {
          value = col.formatter(value)
        }
        exportRow[col.header] = value ?? ''
      })
      return exportRow
    })

    // 创建工作簿
    const ws = XLSX.utils.json_to_sheet(exportData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, sheetName)

    // 设置列宽
    if (columns.some(col => col.width)) {
      const colWidths = columns.map(col => ({ wch: col.width || 15 }))
      ws['!cols'] = colWidths
    }

    // 导出文件
    const date = new Date().toISOString().split('T')[0]
    const fullFilename = `${filename}_${date}.xlsx`
    XLSX.writeFile(wb, fullFilename)

    return true
  } catch (__error) {
    throw error
  }
}

/**
 * 导出数据到CSV文件
 * @param data 要导出的数据
 * @param columns 列配置
 * @param filename 文件名（不含扩展名）
 */
export function exportToCSV(data: unknown[], columns: ExportColumn[], filename: string) {
  try {
    // 准备CSV内容
    const headers = columns.map(col => col.header).join(',')
    const rows = data.map(row => {
      return columns
        .map(col => {
          let value = row[col.key]
          if (col.formatter) {
            value = col.formatter(value)
          }
          // 处理包含逗号或换行的值
          if (typeof value === 'string' && (value.includes(',') || value.includes('\n'))) {
            value = `"${value.replace(/"/g, '""')}"`
          }
          return value ?? ''
        })
        .join(',')
    })

    const csvContent = [headers, ...rows].join('\n')

    // 创建Blob并下载
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)

    const date = new Date().toISOString().split('T')[0]
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}_${date}.csv`)
    link.style.visibility = 'hidden'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    return true
  } catch (__error) {
    throw error
  }
}

/**
 * 下载文件
 * @param url 文件URL
 * @param filename 文件名
 */
export function downloadFile(url: string, filename: string) {
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.visibility = 'hidden'

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 将Blob保存为文件
 * @param blob Blob对象
 * @param filename 文件名
 */
export function saveBlobAsFile(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob)
  downloadFile(url, filename)
  URL.revokeObjectURL(url)
}

/**
 * 读取Excel文件
 * @param file 文件对象
 * @returns 解析后的数据
 */
export function readExcelFile(file: File): Promise<unknown[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = _e => {
      try {
        const data = e.target?.result
        const workbook = XLSX.read(data, { type: 'binary' })

        // 读取第一个工作表
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]

        // 转换为JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        resolve(jsonData)
      } catch (__error) {
        reject(error)
      }
    }

    reader.onerror = _error => {
      reject(error)
    }

    reader.readAsBinaryString(file)
  })
}

/**
 * 解析CSV文件
 * @param file 文件对象
 * @returns 解析后的数据
 */
export function parseCSVFile(file: File): Promise<unknown[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = _e => {
      try {
        const text = e.target?.result as string
        const lines = text.split('\n')
        const headers = lines[0].split(',')

        const data = []
        for (let i = 1; i < lines.length; i++) {
          if (lines[i].trim()) {
            const values = lines[i].split(',')

            const row: unknown = {}
            headers.forEach((header, index) => {
              row[header.trim()] = values[index]?.trim()
            })
            data.push(row)
          }
        }

        resolve(data)
      } catch (__error) {
        reject(error)
      }
    }

    reader.onerror = _error => {
      reject(error)
    }

    reader.readAsText(file, 'UTF-8')
  })
}

/**
 * 简化版导出Excel（兼容旧代码）
 * @param headers 表头数组
 * @param data 数据数组（二维数组）
 * @param filename 文件名
 */

export function exportToExcel(headers: string[], data: unknown[][], filename: string): void

export function exportToExcel(data: unknown[], filename: string): void

export function exportToExcel(arg1: unknown, arg2: unknown, arg3?: unknown): void {
  if (Array.isArray(arg1) && Array.isArray(arg1[0])) {
    // 旧版调用方式：headers, data, filename
    const headers = arg1 as string[]
    const data = arg2 as unknown[][]
    const filename = arg3 as string

    // 创建工作簿
    const ws = XLSX.utils.aoa_to_sheet([headers, ...data])
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

    // 设置列宽
    const colWidths = headers.map(() => ({ wch: 15 }))
    ws['!cols'] = colWidths

    // 导出文件
    const date = new Date().toISOString().split('T')[0]
    const fullFilename = `${filename}_${date}.xlsx`
    XLSX.writeFile(wb, fullFilename)
  } else {
    // 新版调用方式：data, filename
    const data = arg1 as unknown[]
    const filename = arg2 as string

    // 创建工作簿
    const ws = XLSX.utils.json_to_sheet(data)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

    // 设置列宽
    if (data.length > 0) {
      const headers = Object.keys(data[0])
      const colWidths = headers.map(() => ({ wch: 15 }))
      ws['!cols'] = colWidths
    }

    // 导出文件
    XLSX.writeFile(wb, `${filename}.xlsx`)
  }
}
