import { createApp, h } from 'vue'
import HrFilePreview from '@/components/common/HrFilePreview.vue'

/**
 * 文件预览配置
 */
export interface FilePreviewOptions {
  url: string
  name: string
  size?: number
  type?: string
  fileList?: Array<{
    url: string
    name: string
    size?: number
    type?: string
  }>
  currentIndex?: number
  onDownload?: () => void
  onChange?: (index: number) => void
}

/**
 * 打开文件预览
 * @param options 预览选项
 */
export function openFilePreview(options: FilePreviewOptions) {
  // 创建挂载点
  const mountNode = document.createElement('div')
  document.body.appendChild(mountNode)

  // 创建应用实例
  const app = createApp({
    data() {
      return {
        visible: true
      }
    },
    render() {
      return h(FilePreview, {
        modelValue: this.visible,
        'onUpdate:modelValue': (val: boolean) => {
          this.visible = val
          if (!val) {
            // 延迟销毁，等待动画结束
            setTimeout(() => {
              app.unmount()
              document.body.removeChild(mountNode)
            }, 300)
          }
        },
        fileUrl: options.url,
        fileName: options.name,
        fileSize: options.size,
        fileType: options.type,
        fileList: options.fileList,
        currentIndex: options.currentIndex,
        onDownload: options.onDownload,
        onChange: options.onChange
      })
    }
  })

  // 挂载应用
  app.mount(mountNode)
}

/**
 * 判断文件是否支持预览
 * @param fileName 文件名
 */
export function canPreview(fileName: string): boolean {
  const ext = fileName.split('.').pop()?.toLowerCase() || ''
  const supportedExts = [
    // 图片
    'jpg',
    'jpeg',
    'png',
    'gif',
    'bmp',
    'webp',
    'svg',
    'ico',
    'tiff',
    // 文档
    'pdf',
    'txt',
    'json',
    'xml',
    'csv',
    'log',
    'md',
    'yml',
    'yaml',
    // 代码文件
    'js',
    'ts',
    'jsx',
    'tsx',
    'vue',
    'css',
    'scss',
    'less',
    'html',
    'htm',
    'java',
    'py',
    'go',
    'rs',
    'c',
    'cpp',
    'h',
    'hpp',
    'cs',
    'php',
    'rb',
    'swift',
    'kt',
    'dart',
    'r',
    'sql',
    'sh',
    'bash',
    'ps1',
    'bat',
    // Office（需要在线服务）
    'doc',
    'docx',
    'xls',
    'xlsx',
    'ppt',
    'pptx',
    // 音视频
    'mp4',
    'webm',
    'ogg',
    'mov',
    'avi',
    'wmv',
    'flv',
    'm4v',
    'mp3',
    'wav',
    'ogg',
    'flac',
    'm4a',
    'aac',
    'wma'
  ]
  return supportedExts.includes(ext)
}

/**
 * 获取文件预览类型
 * @param fileName 文件名
 */
export function getPreviewType(fileName: string): string {
  const ext = fileName.split('.').pop()?.toLowerCase() || ''

  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
    return 'image'
  }

  if (ext === 'pdf') {
    return 'pdf'
  }

  if (['txt', 'json', 'xml', 'csv', 'log', 'md'].includes(ext)) {
    return 'text'
  }

  if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {
    return 'office'
  }

  if (['mp4', 'webm', 'ogg', 'mov', 'avi'].includes(ext)) {
    return 'video'
  }

  if (['mp3', 'wav', 'ogg', 'flac', 'm4a'].includes(ext)) {
    return 'audio'
  }

  return 'unknown'
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
