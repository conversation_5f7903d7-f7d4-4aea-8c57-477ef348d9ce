import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import duration from 'dayjs/plugin/duration'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.extend(duration)
dayjs.locale('zh-cn')

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: string | Date | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化日期时间
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(
  date: string | Date | number,
  format = 'YYYY-MM-DD HH:mm:ss'
): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param date 日期
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: string | Date | number): string {
  if (!date) return ''
  return dayjs(date).fromNow()
}

/**
 * 获取时间差（显示多久前）
 * @param date 日期
 * @returns 时间差字符串
 */
export function getTimeAgo(date: string | Date | number): string {
  if (!date) return ''
  return dayjs(date).fromNow()
}

/**
 * 获取剩余时间
 * @param date 目标日期
 * @returns 剩余时间字符串
 */
export function getTimeRemaining(date: string | Date | number): string {
  if (!date) return ''

  const now = dayjs()
  const target = dayjs(date)
  const diff = target.diff(now)

  if (diff < 0) {
    return `已超时 ${dayjs.duration(-diff).humanize()}`
  }

  if (diff < 60 * 60 * 1000) {
    // 小于1小时
    return `剩余 ${Math.floor(diff / 60000)} 分钟`
  } else if (diff < 24 * 60 * 60 * 1000) {
    // 小于1天
    return `剩余 ${Math.floor(diff / 3600000)} 小时`
  } else {
    // 大于等于1天
    return `剩余 ${Math.floor(diff / 86400000)} 天`
  }
}

/**
 * 格式化持续时间
 * @param start 开始时间
 * @param end 结束时间
 * @returns 持续时间字符串
 */
export function formatDuration(
  start: string | Date | number,
  end?: string | Date | number
): string {
  if (!start) return ''

  const startTime = dayjs(start)
  const endTime = end ? dayjs(end) : dayjs()
  const diff = endTime.diff(startTime)

  if (diff < 0) return '0秒'

  const duration = dayjs.duration(diff)

  const days = duration.days()
  const hours = duration.hours()
  const minutes = duration.minutes()
  const seconds = duration.seconds()

  let result = ''

  if (days > 0) {
    result += `${days}天`
  }
  if (hours > 0) {
    result += `${hours}小时`
  }
  if (minutes > 0) {
    result += `${minutes}分钟`
  }
  if (seconds > 0 || result === '') {
    result += `${seconds}秒`
  }

  return result
}

/**
 * 获取今天的开始时间
 * @returns 今天开始时间
 */
export function getStartOfToday(): Date {
  return dayjs().startOf('day').toDate()
}

/**
 * 获取今天的结束时间
 * @returns 今天结束时间
 */
export function getEndOfToday(): Date {
  return dayjs().endOf('day').toDate()
}

/**
 * 获取本周的开始时间
 * @returns 本周开始时间
 */
export function getStartOfWeek(): Date {
  return dayjs().startOf('week').toDate()
}

/**
 * 获取本周的结束时间
 * @returns 本周结束时间
 */
export function getEndOfWeek(): Date {
  return dayjs().endOf('week').toDate()
}

/**
 * 获取本月的开始时间
 * @returns 本月开始时间
 */
export function getStartOfMonth(): Date {
  return dayjs().startOf('month').toDate()
}

/**
 * 获取本月的结束时间
 * @returns 本月结束时间
 */
export function getEndOfMonth(): Date {
  return dayjs().endOf('month').toDate()
}

/**
 * 判断是否为今天
 * @param date 日期
 * @returns 是否为今天
 */
export function isToday(date: string | Date | number): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为本周
 * @param date 日期
 * @returns 是否为本周
 */
export function isThisWeek(date: string | Date | number): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 判断是否为本月
 * @param date 日期
 * @returns 是否为本月
 */
export function isThisMonth(date: string | Date | number): boolean {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 获取日期范围的快捷选项
 * @returns 日期范围选项
 */
export function getDateRangeShortcuts() {
  return [
    {
      text: '今天',
      value: () => [getStartOfToday(), getEndOfToday()]
    },
    {
      text: '昨天',
      value: () => {
        const yesterday = dayjs().subtract(1, 'day')
        return [yesterday.startOf('day').toDate(), yesterday.endOf('day').toDate()]
      }
    },
    {
      text: '最近7天',
      value: () => [dayjs().subtract(6, 'day').startOf('day').toDate(), getEndOfToday()]
    },
    {
      text: '最近30天',
      value: () => [dayjs().subtract(29, 'day').startOf('day').toDate(), getEndOfToday()]
    },
    {
      text: '本周',
      value: () => [getStartOfWeek(), getEndOfWeek()]
    },
    {
      text: '本月',
      value: () => [getStartOfMonth(), getEndOfMonth()]
    },
    {
      text: '上月',
      value: () => {
        const lastMonth = dayjs().subtract(1, 'month')
        return [lastMonth.startOf('month').toDate(), lastMonth.endOf('month').toDate()]
      }
    }
  ]
}

/**
 * 计算两个日期之间的工作日数量
 * @param start 开始日期
 * @param end 结束日期
 * @returns 工作日数量
 */
export function getWorkDays(start: string | Date | number, end: string | Date | number): number {
  if (!start || !end) return 0

  const startDate = dayjs(start)
  const endDate = dayjs(end)

  if (endDate.isBefore(startDate)) return 0

  let workDays = 0
  let current = startDate

  while ((current as unknown).isSameOrBefore(endDate, 'day')) {
    // 临时修复dayjs插件类型问题
    const dayOfWeek = current.day()
    // 0 = 周日, 6 = 周六
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      workDays++
    }
    current = current.add(1, 'day')
  }

  return workDays
}

/**
 * 添加工作日
 * @param date 起始日期
 * @param days 要添加的工作日数
 * @returns 新日期
 */
export function addWorkDays(date: string | Date | number, days: number): Date {
  if (!date || days <= 0) return dayjs(date).toDate()

  let current = dayjs(date)
  let remainingDays = days

  while (remainingDays > 0) {
    current = current.add(1, 'day')
    const dayOfWeek = current.day()

    // 如果不是周末，减少剩余天数
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      remainingDays--
    }
  }

  return current.toDate()
}

/**
 * 格式化时间段
 * @param minutes 分钟数
 * @returns 格式化的时间段字符串
 */
export function formatTimeSpan(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}分钟`
  }

  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60

  if (hours < 24) {
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }

  const days = Math.floor(hours / 24)
  const remainingHours = hours % 24

  let result = `${days}天`
  if (remainingHours > 0) {
    result += `${remainingHours}小时`
  }
  if (remainingMinutes > 0) {
    result += `${remainingMinutes}分钟`
  }

  return result
}

/**
 * 获取时间戳
 * @param date 日期
 * @returns 时间戳
 */
export function getTimestamp(date?: string | Date | number): number {
  return dayjs(date).valueOf()
}

/**
 * 从时间戳创建日期
 * @param timestamp 时间戳
 * @returns 日期对象
 */
export function fromTimestamp(timestamp: number): Date {
  return dayjs(timestamp).toDate()
}

/**
 * 计算请假天数（支持节假日）
 * @param start 开始日期
 * @param end 结束日期
 * @param includeWeekends 是否包含周末
 * @param includeHolidays 是否包含节假日
 * @returns 天数
 */
export function calculateWorkDays(
  start: Date,
  end: Date,
  includeWeekends: boolean = false,
  includeHolidays: boolean = false
): number {
  if (!start || !end) return 0

  const startDate = dayjs(start)
  const endDate = dayjs(end)

  if (endDate.isBefore(startDate)) return 0

  // 如果包含所有日期，直接计算总天数
  if (includeWeekends && includeHolidays) {
    return endDate.diff(startDate, 'day') + 1
  }

  let days = 0
  let current = startDate

  while (current.isSameOrBefore(endDate, 'day')) {
    const dayOfWeek = current.day()
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
    const isHoliday = checkIsHoliday(current.toDate())

    // 计算是否算作工作日
    if (!isWeekend || includeWeekends) {
      if (!isHoliday || includeHolidays) {
        days++
      }
    }

    current = current.add(1, 'day')
  }

  return days
}

/**
 * 检查是否为节假日（简化版本）
 * @param date 日期
 * @returns 是否为节假日
 */
function checkIsHoliday(date: Date): boolean {
  // 这里应该调用节假日API或查询配置
  // 暂时使用固定的节假日列表
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  // 2025年节假日示例
  const holidays2025 = [
    { month: 1, days: [1, 2, 3] }, // 元旦
    { month: 2, days: [10, 11, 12, 13, 14, 15, 16, 17] }, // 春节
    { month: 4, days: [4, 5, 6] }, // 清明节
    { month: 5, days: [1, 2, 3, 4, 5] }, // 劳动节
    { month: 6, days: [12, 13, 14] }, // 端午节
    { month: 9, days: [19, 20, 21] }, // 中秋节
    { month: 10, days: [1, 2, 3, 4, 5, 6, 7] } // 国庆节
  ]

  if (year === 2025) {
    return holidays2025.some(h => h.month === month && h.days.includes(day))
  }

  return false
}

export default {
  formatDate,
  formatDateTime,
  formatRelativeTime,
  formatDuration,
  getStartOfToday,
  getEndOfToday,
  getStartOfWeek,
  getEndOfWeek,
  getStartOfMonth,
  getEndOfMonth,
  isToday,
  isThisWeek,
  isThisMonth,
  getDateRangeShortcuts,
  getWorkDays,
  addWorkDays,
  formatTimeSpan,
  getTimestamp,
  fromTimestamp,
  calculateWorkDays
}
