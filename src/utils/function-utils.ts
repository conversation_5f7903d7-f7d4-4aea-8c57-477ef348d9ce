/**
 * 函数工具集 - 防抖、节流、重试等
 */

/**
 * 防抖函数配置项
 */
interface DebounceOptions {
  /** 是否立即执行 */
  immediate?: boolean
  /** 最大等待时间 */
  maxWait?: number
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @param options 配置项
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number,
  options: DebounceOptions = {}
): (...args: Parameters<T>) => void {
  const { immediate = false, maxWait } = options
  let timeoutId: number | undefined
  let lastCallTime: number | undefined
  let lastInvokeTime = 0
  let result: ReturnType<T>

  const invokeFunc = (time: number) => {
    const args = lastArgs
    const thisArg = lastThis

    lastArgs = undefined
    lastThis = undefined
    lastInvokeTime = time
    result = func.apply(thisArg, args)
    return result
  }

  const leadingEdge = (time: number) => {
    lastInvokeTime = time
    timeoutId = window.setTimeout(timerExpired, wait)
    return immediate ? invokeFunc(time) : result
  }

  const remainingWait = (time: number) => {
    const timeSinceLastCall = time - (lastCallTime || 0)
    const timeSinceLastInvoke = time - lastInvokeTime
    const timeWaiting = wait - timeSinceLastCall

    return maxWait !== undefined
      ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting
  }

  const shouldInvoke = (time: number) => {
    const timeSinceLastCall = time - (lastCallTime || 0)
    const timeSinceLastInvoke = time - lastInvokeTime

    return (
      lastCallTime === undefined ||
      timeSinceLastCall >= wait ||
      timeSinceLastCall < 0 ||
      (maxWait !== undefined && timeSinceLastInvoke >= maxWait)
    )
  }

  const timerExpired = () => {
    const time = Date.now()
    if (shouldInvoke(time)) {
      return trailingEdge(time)
    }
    timeoutId = window.setTimeout(timerExpired, remainingWait(time))
  }

  const trailingEdge = (time: number) => {
    timeoutId = undefined

    if (lastArgs) {
      return invokeFunc(time)
    }
    lastArgs = undefined
    lastThis = undefined
    return result
  }

  let lastArgs: Parameters<T> | undefined
  let lastThis: unknown

  const debounced = function (this: unknown, ...args: Parameters<T>) {
    const time = Date.now()
    const isInvoking = shouldInvoke(time)

    lastArgs = args
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    lastThis = this
    lastCallTime = time

    if (isInvoking) {
      if (timeoutId === undefined) {
        return leadingEdge(time)
      }
      if (maxWait !== undefined) {
        timeoutId = window.setTimeout(timerExpired, wait)
        return invokeFunc(time)
      }
    }
    if (timeoutId === undefined) {
      timeoutId = window.setTimeout(timerExpired, wait)
    }
    return result
  }

  debounced.cancel = () => {
    if (timeoutId !== undefined) {
      clearTimeout(timeoutId)
    }
    lastInvokeTime = 0
    lastArgs = undefined
    lastThis = undefined
    lastCallTime = undefined
    timeoutId = undefined
  }

  debounced.flush = () => {
    return timeoutId === undefined ? result : trailingEdge(Date.now())
  }

  return debounced as (...args: Parameters<T>) => void
}

/**
 * 节流函数配置项
 */
interface ThrottleOptions {
  /** 是否在前沿执行 */
  leading?: boolean
  /** 是否在后沿执行 */
  trailing?: boolean
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间（毫秒）
 * @param options 配置项
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number,
  options: ThrottleOptions = {}
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  const { leading = true, trailing: _trailing = true } = options
  return debounce(func, wait, {
    immediate: leading,
    maxWait: wait
  }) as (...args: Parameters<T>) => ReturnType<T> | undefined
}

/**
 * 重试配置选项
 */
interface RetryOptions {
  /** 最大重试次数 */
  maxAttempts?: number
  /** 重试延迟（毫秒） */
  delay?: number
  /** 延迟倍数 */
  delayMultiplier?: number
  /** 最大延迟（毫秒） */
  maxDelay?: number
  /** 判断是否应该重试的函数 */
  shouldRetry?: (error: Error, attempt: number) => boolean
  /** 重试时的回调 */
  onRetry?: (error: Error, attempt: number) => void
}

/**
 * 重试函数
 * @param fn 要重试的异步函数
 * @param options 重试配置
 * @returns Promise
 */
export async function retry<T>(fn: () => Promise<T>, options: RetryOptions = {}): Promise<T> {
  const {
    maxAttempts = 3,
    delay = 1000,
    delayMultiplier = 2,
    maxDelay = 30000,
    shouldRetry = () => true,
    onRetry
  } = options

  let lastError: Error

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error

      if (attempt === maxAttempts || !shouldRetry(lastError, attempt)) {
        throw lastError
      }

      if (onRetry) {
        onRetry(lastError, attempt)
      }

      const currentDelay = Math.min(delay * Math.pow(delayMultiplier, attempt - 1), maxDelay)

      await new Promise(resolve => setTimeout(resolve, currentDelay))
    }
  }

  throw lastError
}

/**
 * 函数缓存（记忆化）
 * @param fn 要缓存的函数
 * @param resolver 缓存键生成函数
 * @returns 缓存后的函数
 */
export function memoize<T extends (...args: unknown[]) => unknown>(
  fn: T,
  resolver?: (...args: Parameters<T>) => string
): T & { cache: Map<string, ReturnType<T>> } {
  const cache = new Map<string, ReturnType<T>>()

  const memoized = function (this: unknown, ...args: Parameters<T>): ReturnType<T> {
    const key = resolver ? resolver.apply(this, args) : JSON.stringify(args)

    if (cache.has(key)) {
      return cache.get(key)!
    }

    const result = fn.apply(this, args)
    cache.set(key, result)
    return result
  } as T

  // 暴露缓存以便清理
  ;(memoized as T & { cache: Map<string, ReturnType<T>> }).cache = cache

  return memoized as T & { cache: Map<string, ReturnType<T>> }
}

/**
 * 函数只执行一次
 * @param fn 要执行的函数
 * @returns 包装后的函数
 */
export function once<T extends (...args: unknown[]) => unknown>(fn: T): T {
  let called = false
  let result: ReturnType<T>

  return function (this: unknown, ...args: Parameters<T>): ReturnType<T> {
    if (!called) {
      called = true
      result = fn.apply(this, args)
    }
    return result
  } as T
}

/**
 * 延迟执行
 * @param ms 延迟时间（毫秒）
 * @returns Promise
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 异步函数超时控制
 * @param promise 要控制的Promise
 * @param timeoutMs 超时时间（毫秒）
 * @param timeoutError 超时错误信息
 * @returns Promise
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutError = '操作超时'
): Promise<T> {
  let timeoutId: number

  const timeoutPromise = new Promise<never>((_, reject) => {
    timeoutId = window.setTimeout(() => {
      reject(new Error(timeoutError))
    }, timeoutMs)
  })

  try {
    const result = await Promise.race([promise, timeoutPromise])
    clearTimeout(timeoutId!)
    return result
  } catch (error) {
    clearTimeout(timeoutId!)
    throw error
  }
}

/**
 * 批量执行Promise并限制并发数
 * @param tasks 任务数组
 * @param limit 并发限制
 * @returns Promise数组
 */
export async function promiseLimit<T>(tasks: (() => Promise<T>)[], limit: number): Promise<T[]> {
  const results: T[] = []
  const executing: Promise<void>[] = []

  for (const task of tasks) {
    const promise = task().then(result => {
      results.push(result)
    })

    executing.push(promise)

    if (executing.length >= limit) {
      await Promise.race(executing)
      executing.splice(
        executing.findIndex(p => p === promise),
        1
      )
    }
  }

  await Promise.all(executing)
  return results
}

/**
 * 创建一个可取消的Promise
 * @param promise 原始Promise
 * @returns 可取消的Promise和取消函数
 */
export function makeCancelable<T>(promise: Promise<T>): {
  promise: Promise<T>
  cancel: () => void
} {
  let hasCanceled = false

  const wrappedPromise = new Promise<T>((resolve, reject) => {
    promise
      .then(val => (hasCanceled ? reject({ isCanceled: true }) : resolve(val)))
      .catch(error => (hasCanceled ? reject({ isCanceled: true }) : reject(error)))
  })

  return {
    promise: wrappedPromise,
    cancel() {
      hasCanceled = true
    }
  }
}

/**
 * 函数管道组合
 * @param fns 函数数组
 * @returns 组合后的函数
 */
export function pipe<T>(...fns: Array<(arg: T) => T>): (arg: T) => T {
  return (arg: T) => fns.reduce((prev, fn) => fn(prev), arg)
}

/**
 * 函数组合（从右到左）
 * @param fns 函数数组
 * @returns 组合后的函数
 */
export function compose<T>(...fns: Array<(arg: T) => T>): (arg: T) => T {
  return pipe(...fns.reverse())
}
