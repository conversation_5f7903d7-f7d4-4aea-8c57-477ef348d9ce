/**
 * 颜色对比度检查工具
 * @description 基于WCAG 2.1标准，检查颜色对比度是否满足无障碍要求
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 颜色对比度等级
 */
export enum ContrastLevel {
  /** AA级 - 普通文本4.5:1，大文本3:1 */
  AA = 'AA',
  /** AAA级 - 普通文本7:1，大文本4.5:1 */
  AAA = 'AAA'
}

/**
 * 文本类型
 */
export enum TextType {
  /** 普通文本 - 小于18pt或14pt粗体 */
  Normal = 'normal',
  /** 大文本 - 18pt及以上，或14pt及以上粗体 */
  Large = 'large'
}

/**
 * 对比度检查结果
 */
export interface ContrastResult {
  /** 对比度值 */
  ratio: number
  /** 是否满足AA标准 */
  passAA: boolean
  /** 是否满足AAA标准 */
  passAAA: boolean
  /** 推荐的文字颜色（如果不满足标准） */
  suggestedColor?: string
  /** 详细信息 */
  details: {
    normalTextAA: boolean
    normalTextAAA: boolean
    largeTextAA: boolean
    largeTextAAA: boolean
  }
}

/**
 * 颜色信息
 */
export interface ColorInfo {
  hex: string
  rgb: { r: number; g: number; b: number }
  luminance: number
}

/**
 * 将十六进制颜色转换为RGB
 * @param hex - 十六进制颜色值
 * @returns RGB值
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } {
  // 移除#号
  hex = hex.replace(/^#/, '')
  
  // 处理3位十六进制
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('')
  }
  
  const num = parseInt(hex, 16)
  return {
    r: (num >> 16) & 255,
    g: (num >> 8) & 255,
    b: num & 255
  }
}

/**
 * 将RGB转换为十六进制
 * @param r - 红色值
 * @param g - 绿色值
 * @param b - 蓝色值
 * @returns 十六进制颜色值
 */
export function rgbToHex(r: number, g: number, b: number): string {
  return '#' + [r, g, b].map(x => {
    const hex = x.toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }).join('')
}

/**
 * 计算相对亮度
 * @param rgb - RGB颜色值
 * @returns 相对亮度（0-1）
 * @see https://www.w3.org/TR/WCAG20-TECHS/G17.html
 */
export function getLuminance(rgb: { r: number; g: number; b: number }): number {
  const {r, g, b} =  rgb
  
  // 转换为sRGB
  const [rs, gs, bs]  g: number; b: number }, color2: string | { r: number; g: number; b: number }): number {
  const rgb1 = typeof color1 === 'string' ? hexToRgb(color1) : color1
  const rgb2 = typeof color2 === 'string' ? hexToRgb(color2) : color2
  
  const lum1 = getLuminance(rgb1)
  const lum2 = getLuminance(rgb2)
  
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)
  
  return (brightest + 0.05) / (darkest + 0.05)
}

/**
 * 检查对比度是否满足WCAG标准
 * @param ratio - 对比度值
 * @param level - 检查等级（AA或AAA）
 * @param textType - 文本类型
 * @returns 是否满足标准
 */
export function meetsWCAG(ratio: number, level: ContrastLevel = ContrastLevel.AA, textType: TextType = TextType.Normal): boolean {
  if (level === ContrastLevel.AA) {
    return textType === TextType.Normal ? ratio >= 4.5 : ratio >= 3
  } else {
    return textType === TextType.Normal ? ratio >= 7 : ratio >= 4.5
  }
}

/**
 * 检查颜色对比度
 * @param foreground - 前景色
 * @param background - 背景色
 * @returns 对比度检查结果
 */
export function checkContrast(foreground: string, background: string): ContrastResult {
  const ratio = getContrastRatio(foreground, background)
  
  const result: ContrastResult = {
    ratio: Math.round(ratio * 100) / 100,
    passAA: meetsWCAG(ratio, ContrastLevel.AA, TextType.Normal),
    passAAA: meetsWCAG(ratio, ContrastLevel.AAA, TextType.Normal),
    details: {
      normalTextAA: meetsWCAG(ratio, ContrastLevel.AA, TextType.Normal),
      normalTextAAA: meetsWCAG(ratio, ContrastLevel.AAA, TextType.Normal),
      largeTextAA: meetsWCAG(ratio, ContrastLevel.AA, TextType.Large),
      largeTextAAA: meetsWCAG(ratio, ContrastLevel.AAA, TextType.Large)
    }
  }
  
  // 如果不满足AA标准，建议一个更好的颜色
  if (!result.passAA) {
    result.suggestedColor = suggestBetterColor(foreground, background)
  }
  
  return result
}

/**
 * 建议一个满足对比度要求的颜色
 * @param foreground - 当前前景色
 * @param background - 背景色
 * @param targetRatio - 目标对比度（默认4.5）
 * @returns 建议的颜色
 */
export function suggestBetterColor(foreground: string, background: string, targetRatio: number = 4.5): string {
  const fgRgb = hexToRgb(foreground)
  const bgLum = getLuminance(hexToRgb(background))
  
  // 确定是应该变亮还是变暗
  const shouldDarken = bgLum > 0.5
  
  let { r, g, b } = fgRgb
  let step = shouldDarken ? -5 : 5
  let attempts = 0
  const maxAttempts = 100
  
  while (attempts < maxAttempts) {
    // 调整颜色
    r = Math.max(0, Math.min(255, r + step))
    g = Math.max(0, Math.min(255, g + step))
    b = Math.max(0, Math.min(255, b + step))
    
    const newRatio = getContrastRatio({ r, g, b }, background)
    
    if (newRatio >= targetRatio) {
      return rgbToHex(r, g, b)
    }
    
    // 如果已经到达极限，尝试相反方向
    if ((shouldDarken && r === 0 && g === 0 && b === 0) ||
        (!shouldDarken && r === 255 && g === 255 && b === 255)) {
      step = -step
    }
    
    attempts++
  }
  
  // 如果无法找到满足要求的颜色，返回黑色或白色
  return bgLum > 0.5 ? '#000000' : '#FFFFFF'
}

/**
 * 批量检查颜色对比度
 * @param colorPairs - 颜色对数组
 * @returns 检查结果数组
 */
export function checkContrastBatch(colorPairs: Array<{ name: string; foreground: string; background: string }>): Array<{ name: string; result: ContrastResult }> {
  return colorPairs.map(pair => ({
    name: pair.name,
    result: checkContrast(pair.foreground, pair.background)
  }))
}

/**
 * 从CSS变量获取颜色值
 * @param varName - CSS变量名
 * @param element - 元素（默认为根元素）
 * @returns 颜色值
 */
export function getCSSVariableColor(varName: string, element: HTMLElement = document.documentElement): string {
  const value = getComputedStyle(element).getPropertyValue(varName).trim()
  
  // 如果是另一个CSS变量的引用
  if (value.startsWith('var(')) {
    const innerVar = value.match(/var\((--[^)]+)\)/)?.[1]
    if (innerVar) {
      return getCSSVariableColor(innerVar, element)
    }
  }
  
  return value
}

/**
 * 检查Element Plus主题颜色对比度
 * @returns 检查结果
 */
export function checkElementPlusThemeContrast(): Record<string, ContrastResult> {
  const results: Record<string, ContrastResult> = {}
  
  // 获取背景色
  const bgColor = getCSSVariableColor('--el-bg-color') || '#ffffff'
  
  // 文本颜色
  const textColors = {
    '主要文本': { var: '--el-text-color-primary', bg: bgColor },
    '常规文本': { var: '--el-text-color-regular', bg: bgColor },
    '次要文本': { var: '--el-text-color-secondary', bg: bgColor },
    '占位文本': { var: '--el-text-color-placeholder', bg: bgColor },
    '禁用文本': { var: '--el-text-color-disabled', bg: bgColor }
  }
  
  // 功能色
  const functionalColors = {
    '主色文本': { var: '--el-color-primary', bg: bgColor },
    '成功文本': { var: '--el-color-success', bg: bgColor },
    '警告文本': { var: '--el-color-warning', bg: bgColor },
    '危险文本': { var: '--el-color-danger', bg: bgColor },
    '信息文本': { var: '--el-color-info', bg: bgColor }
  }
  
  // 检查所有颜色对
  Object.entries({ ...textColors, ...functionalColors }).forEach(([name, config]) => {
    const fgColor = getCSSVariableColor(config.var)
    if (fgColor && config.bg) {
      results[name] = checkContrast(fgColor, config.bg)
    }
  })
  
  return results
}

/**
 * 生成对比度报告
 * @param results - 检查结果
 * @returns HTML格式的报告
 */
export function generateContrastReport(results: Record<string, ContrastResult>): string {
  let html = '<div class="contrast-report">'
  html += '<h2>颜色对比度检查报告</h2>'
  html += '<table>'
  html += '<thead><tr>'
  html += '<th>颜色名称</th>'
  html += '<th>对比度</th>'
  html += '<th>AA标准</th>'
  html += '<th>AAA标准</th>'
  html += '<th>建议</th>'
  html += '</tr></thead>'
  html += '<tbody>'
  
  Object.entries(results).forEach(([name, result]) => {
    const status = result.passAA ? 'pass' : 'fail'
    html += `<tr class="${status}">`
    html += `<td>${name}</td>`
    html += `<td>${result.ratio}:1</td>`
    html += `<td>${result.passAA ? '✅' : '❌'}</td>`
    html += `<td>${result.passAAA ? '✅' : '❌'}</td>`
    html += `<td>`
    if (!result.passAA) {
      html += `建议颜色: ${result.suggestedColor}`
    } else if (!result.passAAA) {
      html += '满足AA标准，但不满足AAA标准'
    } else {
      html += '满足所有标准'
    }
    html += `</td>`
    html += '</tr>'
  })
  
  html += '</tbody></table>'
  html += '</div>'
  
  return html
}

// 导出WCAG标准常量
export const WCAG_STANDARDS = {
  AA: {
    normalText: 4.5,
    largeText: 3
  },
  AAA: {
    normalText: 7,
    largeText: 4.5
  }
}