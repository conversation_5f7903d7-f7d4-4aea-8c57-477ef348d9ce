 

/**
 * ResourceLoader 单元测试
 * @description 自动生成的测试文件
 */

import { describe, it, expect } from 'vitest'
import { useResourceLoader } from '../ResourceLoader'
describe('useResourceLoader', () => {
  it('应该被正确导出', () => {
    expect(useResourceLoader).toBeDefined()
    expect(typeof useResourceLoader).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = useResourceLoader()
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    const result = useResourceLoader()
    expect(result).toBeDefined()
  })
})
