/**
 * 资源加载优化器
 * 实现按需加载、预加载、懒加载等优化策略
 */

import { ref } from 'vue'

// 资源类型
export enum ResourceType {
  SCRIPT = 'script',
  STYLE = 'style',
  IMAGE = 'image',
  FONT = 'font',
  DATA = 'data'
}

// 加载优先级
export enum LoadPriority {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  LAZY = 'lazy'
}

// 资源配置
export interface ResourceConfig {
  url: string
  type: ResourceType
  priority: LoadPriority
  async?: boolean
  defer?: boolean
  preload?: boolean
  prefetch?: boolean
  critical?: boolean
  retry?: number
  timeout?: number
  fallback?: string
}

// 加载状态
export interface LoadStatus {
  url: string
  status: 'pending' | 'loading' | 'loaded' | 'failed'
  progress: number
  size: number
  duration: number
  error?: Error
}

export class ResourceLoader {
  private static instance: ResourceLoader
  private loadQueue: Map<LoadPriority, ResourceConfig[]> = new Map()
  private loadStatus: Map<string, LoadStatus> = new Map()
  private loadedResources: Set<string> = new Set()
  private observers: Map<string, IntersectionObserver> = new Map()
  
  // 加载状态
  public loading = ref(false)
  public progress = ref(0)
  
  private constructor() {
    this.initQueues()
    this.startLoading()
  }
  
  static getInstance(): ResourceLoader {
    if (!ResourceLoader.instance) {
      ResourceLoader.instance = new ResourceLoader()
    }
    return ResourceLoader.instance
  }
  
  /**
   * 初始化队列
   */
  private initQueues(): void {
    this.loadQueue.set(LoadPriority.HIGH, [])
    this.loadQueue.set(LoadPriority.MEDIUM, [])
    this.loadQueue.set(LoadPriority.LOW, [])
    this.loadQueue.set(LoadPriority.LAZY, [])
  }
  
  /**
   * 开始加载
   */
  private startLoading(): void {
    // 按优先级顺序加载
    this.loadByPriority(LoadPriority.HIGH)
      .then(() => this.loadByPriority(LoadPriority.MEDIUM))
      .then(() => this.loadByPriority(LoadPriority.LOW))
      .then(() => this.setupLazyLoading())
  }
  
  /**
   * 按优先级加载
   */
  private async loadByPriority(priority: LoadPriority): Promise<void> {
    const resources = this.loadQueue.get(priority) || []
    
    if (resources.length === 0) return
    
    // 并行加载同优先级资源
    const promises = resources.map(resource => this.loadResource(resource))
    await Promise.allSettled(promises)
  }
  
  /**
   * 加载资源
   */
  private async loadResource(config: ResourceConfig): Promise<void> {
    const {url, type, retry = 3 = 3, timeout = 30000 = 30000} =  3: _retry 
          src: url('${config.url}');
          font-display: swap;
        }
      `
      document.head.appendChild(style)
      return
    }
    
    // 使用Font Loading API
    const fontFace = new FontFace('CustomFont', `url(${config.url})`, {
      display: 'swap'
    })
    
    await fontFace.load()
    document.fonts.add(fontFace)
  }
  
  /**
   * 加载数据
   */
  private async loadData(config: ResourceConfig): Promise<void> {
    const response = await fetch(config.url, {
      method: 'GET',
      cache: 'default',
      signal: AbortSignal.timeout(config.timeout || 30000)
    })
    
    if (!response.ok) {
      throw new Error(`Failed to load data: ${response.statusText}`)
    }
    
    const data = await response.json()
    
    // 缓存数据
    if ('caches' in window) {
      const cache = await caches.open('data-cache')
      await cache.put(config.url, new Response(JSON.stringify(data)))
    }
  }
  
  /**
   * 设置懒加载
   */
  private setupLazyLoading(): void {
    const lazyResources = this.loadQueue.get(LoadPriority.LAZY) || []
    
    lazyResources.forEach(resource => {
      if (resource.type === ResourceType.IMAGE) {
        this.setupImageLazyLoading(resource)
      } else {
        this.setupGenericLazyLoading(resource)
      }
    })
  }
  
  /**
   * 设置图片懒加载
   */
  private setupImageLazyLoading(config: ResourceConfig): void {
    const images = document.querySelectorAll(`img[data-src="${config.url}"]`)
    
    if (images.length === 0) return
    
    const observer = new IntersectionObserver(
      (_entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            img.src = config.url
            observer.unobserve(img)
            
            // 更新加载状态
            this.loadResource(config)
          }
        })
      },
      {
        rootMargin: '50px',
        threshold: 0.01
      }
    )
    
    images.forEach(img => observer.observe(img))
    this.observers.set(config.url, observer)
  }
  
  /**
   * 设置通用懒加载
   */
  private setupGenericLazyLoading(config: ResourceConfig): void {
    // 使用Intersection Observer监听触发元素
    const trigger = document.querySelector(`[data-lazy-load="${config.url}"]`)
    
    if (!trigger) return
    
    const observer = new IntersectionObserver(
      (_entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.loadResource(config)
            observer.disconnect()
          }
        })
      },
      {
        rootMargin: '100px',
        threshold: 0
      }
    )
    
    observer.observe(trigger)
    this.observers.set(config.url, observer)
  }
  
  /**
   * 预加载资源
   */
  preload(resources: ResourceConfig[]): void {
    resources.forEach(resource => {
      if (resource.preload !== false) {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.href = resource.url
        
        // 设置as属性
        switch (resource.type) {
          case ResourceType.SCRIPT:
            link.as = 'script'
            break
          case ResourceType.STYLE:
            link.as = 'style'
            break
          case ResourceType.IMAGE:
            link.as = 'image'
            break
          case ResourceType.FONT:
            link.as = 'font'
            link.crossOrigin = 'anonymous'
            break
        }
        
        document.head.appendChild(link)
      }
    })
  }
  
  /**
   * 预取资源
   */
  prefetch(resources: ResourceConfig[]): void {
    if (!('requestIdleCallback' in window)) return
    
    requestIdleCallback(() => {
      resources.forEach(resource => {
        if (resource.prefetch !== false) {
          const link = document.createElement('link')
          link.rel = 'prefetch'
          link.href = resource.url
          document.head.appendChild(link)
        }
      })
    })
  }
  
  /**
   * 添加资源到队列
   */
  addResource(config: ResourceConfig): void {
    const queue = this.loadQueue.get(config.priority)
    if (queue) {
      queue.push(config)
    }
  }
  
  /**
   * 批量添加资源
   */
  addResources(resources: ResourceConfig[]): void {
    resources.forEach(resource => this.addResource(resource))
  }
  
  /**
   * 立即加载资源
   */
  async loadNow(config: ResourceConfig): Promise<void> {
    await this.loadResource(config)
  }
  
  /**
   * 更新进度
   */
  private updateProgress(): void {
    const total = this.loadStatus.size
    const loaded = Array.from(this.loadStatus.values())
      .filter(status => status.status === 'loaded').length
    
    this.progress.value = total > 0 ? (loaded / total) * 100 : 0
  }
  
  /**
   * 延迟
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  /**
   * 获取加载状态
   */
  getStatus(url: string): LoadStatus | undefined {
    return this.loadStatus.get(url)
  }
  
  /**
   * 获取所有状态
   */
  getAllStatus(): LoadStatus[] {
    return Array.from(this.loadStatus.values())
  }
  
  /**
   * 清理
   */
  cleanup(): void {
    // 断开所有观察器
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
    
    // 清理状态
    this.loadStatus.clear()
    this.loadedResources.clear()
    this.initQueues()
  }
}

// 导出单例
export const resourceLoader = ResourceLoader.getInstance()

// 导出Vue组合式API
export function useResourceLoader() {
  const loader = resourceLoader
  
  return {
    loading: loader.loading,
    progress: loader.progress,
    addResource: (config: ResourceConfig) => loader.addResource(config),
    addResources: (resources: ResourceConfig[]) => loader.addResources(resources),
    loadNow: (config: ResourceConfig) => loader.loadNow(config),
    preload: (resources: ResourceConfig[]) => loader.preload(resources),
    prefetch: (resources: ResourceConfig[]) => loader.prefetch(resources),
    getStatus: (url: string) => loader.getStatus(url),
    getAllStatus: () => loader.getAllStatus()
  }
}