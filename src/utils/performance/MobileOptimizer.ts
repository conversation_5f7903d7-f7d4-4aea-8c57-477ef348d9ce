/**
 * 移动端性能优化器
 * 提供资源加载优化、渲染优化、内存管理等功能
 */

import { ref, computed } from 'vue'

// 性能指标
export interface PerformanceMetrics {
  FCP: number // First Contentful Paint
  LCP: number // Largest Contentful Paint
  FID: number // First Input Delay
  CLS: number // Cumulative Layout Shift
  TTI: number // Time to Interactive
  TBT: number // Total Blocking Time
}

// 资源优化配置
export interface OptimizationConfig {
  lazyLoad: boolean
  preload: string[]
  prefetch: string[]
  imageOptimization: {
    webp: boolean
    lazy: boolean
    quality: number
    sizes: string[]
  }
  codeSpitting: boolean
  minify: boolean
  compress: boolean
  cache: {
    maxAge: number
    staleWhileRevalidate: boolean
  }
}

export class MobileOptimizer {
  private static instance: MobileOptimizer
  private observer: PerformanceObserver | null = null

  // 性能指标
  public metrics = ref<PerformanceMetrics>({
    FCP: 0,
    LCP: 0,
    FID: 0,
    CLS: 0,
    TTI: 0,
    TBT: 0
  })

  // 配置
  private config: OptimizationConfig = {
    lazyLoad: true,
    preload: ['/fonts/main.woff2', '/css/critical.css'],
    prefetch: ['/js/charts.js', '/js/workflow.js'],
    imageOptimization: {
      webp: true,
      lazy: true,
      quality: 85,
      sizes: ['320w', '640w', '1024w']
    },
    codeSpitting: true,
    minify: true,
    compress: true,
    cache: {
      maxAge: 3600000,
      staleWhileRevalidate: true
    }
  }

  private constructor() {
    this.init()
  }

  static getInstance(): MobileOptimizer {
    if (!MobileOptimizer.instance) {
      MobileOptimizer.instance = new MobileOptimizer()
    }
    return MobileOptimizer.instance
  }

  /**
   * 初始化
   */
  private init(): void {
    // 监听性能指标
    this.observePerformance()

    // 应用优化策略
    this.applyOptimizations()

    // 监听网络状态
    this.observeNetwork()
  }

  /**
   * 监听性能指标
   */
  private observePerformance(): void {
    if (!('PerformanceObserver' in window)) return

    // FCP & LCP
    this.observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          this.metrics.value.FCP = entry.startTime
        }
        if ((entry as unknown).entryType === 'largest-contentful-paint') {
          this.metrics.value.LCP = entry.startTime
        }
      }
    })

    this.observer.observe({
      entryTypes: ['paint', 'largest-contentful-paint']
    })

    // FID
    const fidObserver = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if ((entry as unknown).entryType === 'first-input') {
          this.metrics.value.FID = (entry as unknown).processingStart - entry.startTime
        }
      }
    })

    fidObserver.observe({ entryTypes: ['first-input'] })

    // CLS
    let clsValue = 0
    const clsObserver = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (!(entry as unknown).hadRecentInput) {
          clsValue += (entry as unknown).value
          this.metrics.value.CLS = clsValue
        }
      }
    })

    clsObserver.observe({ entryTypes: ['layout-shift'] })
  }

  /**
   * 应用优化策略
   */
  private applyOptimizations(): void {
    // 预加载关键资源
    this.preloadResources()

    // 预取非关键资源
    this.prefetchResources()

    // 启用图片懒加载
    if (this.config.lazyLoad) {
      this.enableLazyLoading()
    }

    // 优化字体加载
    this.optimizeFonts()

    // 减少重排重绘
    this.optimizeRendering()
  }

  /**
   * 预加载资源
   */
  private preloadResources(): void {
    this.config.preload.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = url

      // 自动检测资源类型
      if (url.endsWith('.css')) {
        link.as = 'style'
      } else if (url.endsWith('.js')) {
        link.as = 'script'
      } else if (url.match(/\.(woff2?|ttf|otf)$/)) {
        link.as = 'font'
        link.type = 'font/woff2'
        link.crossOrigin = 'anonymous'
      }

      document.head.appendChild(link)
    })
  }

  /**
   * 预取资源
   */
  private prefetchResources(): void {
    if (!('requestIdleCallback' in window)) return

    requestIdleCallback(() => {
      this.config.prefetch.forEach(url => {
        const link = document.createElement('link')
        link.rel = 'prefetch'
        link.href = url
        document.head.appendChild(link)
      })
    })
  }

  /**
   * 启用图片懒加载
   */
  private enableLazyLoading(): void {
    if (!('IntersectionObserver' in window)) return

    const imageObserver = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            const src = img.dataset.src

            if (src) {
              // 加载图片
              this.loadImage(img, src)
              imageObserver.unobserve(img)
            }
          }
        })
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01
      }
    )

    // 监听所有懒加载图片
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img)
    })
  }

  /**
   * 加载图片
   */
  private loadImage(img: HTMLImageElement, src: string): void {
    // 检测WebP支持
    if (this.config.imageOptimization.webp && this.supportsWebP()) {
      src = src.replace(/\.(jpg|jpeg|png)$/, '.webp')
    }

    // 创建新图片对象
    const newImg = new Image()

    newImg.onload = () => {
      img.src = src
      img.classList.add('loaded')
    }

    newImg.onerror = () => {
      // 降级到原始格式
      img.src = src.replace('.webp', '.jpg')
    }

    newImg.src = src
  }

  /**
   * 检测WebP支持
   */
  private supportsWebP(): boolean {
    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1

    return canvas.toDataURL('image/webp').indexOf('image/webp') === 5
  }

  /**
   * 优化字体加载
   */
  private optimizeFonts(): void {
    // 使用font-display: swap
    const style = document.createElement('style')
    style.textContent = `
      @font-face {
        font-display: swap;
      }
    `
    document.head.appendChild(style)

    // 预加载字体子集
    if ('fonts' in document) {
      document.fonts.load('1em PingFangSC')
    }
  }

  /**
   * 优化渲染
   */
  private optimizeRendering(): void {
    // 使用CSS contain属性
    const style = document.createElement('style')
    style.textContent = `
      .chart-container {
        contain: layout style paint;
      }
      
      .virtual-list {
        contain: strict;
      }
      
      .mobile-card {
        contain: layout;
        will-change: transform;
      }
    `
    document.head.appendChild(style)

    // 使用passive事件监听
    const passive = { passive: true }
    document.addEventListener('touchstart', () => {}, passive)
    document.addEventListener('touchmove', () => {}, passive)
    document.addEventListener('wheel', () => {}, passive)
  }

  /**
   * 监听网络状态
   */
  private observeNetwork(): void {
    if (!('connection' in navigator)) return

    const connection = (navigator as unknown).connection

    // 根据网络状况调整策略
    const updateStrategy = () => {
      const effectiveType = connection.effectiveType

      switch (effectiveType) {
        case 'slow-2g':
        case '2g':
          // 极限优化模式
          this.enableDataSaver()
          break
        case '3g':
          // 平衡模式
          this.enableBalancedMode()
          break
        case '4g':
        default:
          // 正常模式
          this.enableNormalMode()
      }
    }

    connection.addEventListener('change', updateStrategy)
    updateStrategy()
  }

  /**
   * 启用省流模式
   */
  private enableDataSaver(): void {
    // 禁用图片自动加载
    document.querySelectorAll('img[data-src]').forEach(img => {
      ;(img as HTMLImageElement).loading = 'lazy'
    })

    // 禁用视频自动播放
    document.querySelectorAll('video').forEach(video => {
      video.pause()
      video.preload = 'none'
    })

    // 降低图片质量
    this.config.imageOptimization.quality = 60
  }

  /**
   * 启用平衡模式
   */
  private enableBalancedMode(): void {
    this.config.imageOptimization.quality = 75
  }

  /**
   * 启用正常模式
   */
  private enableNormalMode(): void {
    this.config.imageOptimization.quality = 85
  }

  /**
   * 优化组件
   */

  optimizeComponent(component: unknown): void {
    // 组件级别优化
    const options = {
      // 使用函数式组件
      functional: true,

      // 禁用不必要的特性
      inheritAttrs: false,

      // 使用v-once优化静态内容
      staticRenderFns: true
    }

    Object.assign(component, options)
  }

  /**
   * 优化列表渲染
   */
  optimizeList(options: { itemHeight: number; buffer: number; throttle: number }): unknown {
    return {
      itemHeight: options.itemHeight || 50,
      buffer: options.buffer || 5,
      throttle: options.throttle || 16,

      // 使用虚拟滚动
      virtual: true,

      // 使用track-by优化
      keyField: 'id',

      // 禁用深度响应
      deep: false
    }
  }

  /**
   * 获取优化建议
   */
  getOptimizationSuggestions(): string[] {
    const suggestions: string[] = []
    const metrics = this.metrics.value

    // FCP优化建议
    if (metrics.FCP > 1800) {
      suggestions.push('首次内容绘制时间过长，建议优化关键渲染路径')
    }

    // LCP优化建议
    if (metrics.LCP > 2500) {
      suggestions.push('最大内容绘制时间过长，建议优化主要内容加载')
    }

    // FID优化建议
    if (metrics.FID > 100) {
      suggestions.push('首次输入延迟过高，建议减少JavaScript执行时间')
    }

    // CLS优化建议
    if (metrics.CLS > 0.1) {
      suggestions.push('累积布局偏移过大，建议为图片和广告预留空间')
    }

    return suggestions
  }

  /**
   * 导出性能报告
   */

  exportReport(): unknown {
    return {
      metrics: this.metrics.value,
      suggestions: this.getOptimizationSuggestions(),
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      connection: (navigator as unknown).connection?.effectiveType || 'unknown'
    }
  }
}

// 导出单例
export const mobileOptimizer = MobileOptimizer.getInstance()

// 导出Vue组合式API
export function useMobileOptimizer() {
  const optimizer = mobileOptimizer

  const metrics = computed(() => optimizer.metrics.value)
  const suggestions = computed(() => optimizer.getOptimizationSuggestions())

  const score = computed(() => {
    const m = metrics.value
    let total = 100

    // 根据指标扣分
    if (m.FCP > 1800) total -= 10
    if (m.LCP > 2500) total -= 15
    if (m.FID > 100) total -= 10
    if (m.CLS > 0.1) total -= 15

    return Math.max(0, total)
  })

  return {
    metrics,
    suggestions,
    score,

    optimizeComponent: (component: unknown) => optimizer.optimizeComponent(component),

    optimizeList: (options: unknown) => optimizer.optimizeList(options),
    exportReport: () => optimizer.exportReport()
  }
}
