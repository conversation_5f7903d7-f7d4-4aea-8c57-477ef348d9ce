import type {
  AssessmentPlan,
  AssessmentTask,
  AssessmentMetric,
  AssessmentResult,
  MetricScore,
  GradeDistribution
} from '@/types/performance'

/**
 * 绩效分数计算引擎
 */
export class PerformanceScoreCalculator {
  /**
   * 计算考核任务的总分
   */
  calculateTotalScore(task: AssessmentTask, metrics: AssessmentMetric[]): AssessmentResult {
    // 按维度分组计算
    const dimensionScores = this.calculateDimensionScores(task, metrics)

    // 计算总分
    const totalScore = this.calculateWeightedScore(dimensionScores)

    // 确定等级
    const grade = this.determineGrade(totalScore)

    // 生成结果
    return {
      totalScore,
      grade,
      dimensionScores,
      comments: {
        self: task.evaluators.self ? '已完成自评' : '',
        supervisor: task.evaluators.supervisor ? '已完成主管评价' : ''
      }
    }
  }

  /**
   * 计算各维度得分
   */

  private calculateDimensionScores(task: AssessmentTask, metrics: AssessmentMetric[]): unknown[] {
    const dimensions = this.groupMetricsByDimension(metrics)

    return Object.entries(dimensions).map(([dimension, dimensionMetrics]) => {
      const metricScores = dimensionMetrics.map(metric => this.calculateMetricScore(metric, task))
      const dimensionScore = this.calculateWeightedScore(
        metricScores.map(ms => ({
          score: ms.score,
          weight: metric.weight
        }))
      )

      return {
        dimension,
        score: dimensionScore,
        weight: this.getDimensionWeight(dimension),
        metrics: metricScores
      }
    })
  }

  /**
   * 计算单个指标得分
   */
  calculateMetricScore(metric: AssessmentMetric, task: AssessmentTask): MetricScore {
    let score = 0
    let actualValue: number | string = 0

    if (metric.type === 'quantitative') {
      // 定量指标：根据实际值和目标值计算
      actualValue = this.getActualValue(metric, task)
      score = this.calculateQuantitativeScore(actualValue as number, metric)
    } else {
      // 定性指标：根据评分等级计算
      const level = this.getQualitativeLevel(metric, task)
      score = this.calculateQualitativeScore(level, metric)
      actualValue = `等级${level}`
    }

    return {
      metricId: metric.id,
      metricName: metric.name,
      actualValue,
      targetValue: metric.targetValue || metric.scoringCriteria[0]?.description,
      score,
      weight: metric.weight
    }
  }

  /**
   * 计算定量指标得分
   */
  private calculateQuantitativeScore(actualValue: number, metric: AssessmentMetric): number {
    if (!metric.targetValue) return 0

    // 计算完成率
    const completionRate = (actualValue / metric.targetValue) * 100

    // 根据评分标准查找对应分数
    for (const criterion of metric.scoringCriteria) {
      if (criterion.minValue !== undefined && criterion.maxValue !== undefined) {
        if (completionRate >= criterion.minValue && completionRate <= criterion.maxValue) {
          return criterion.score
        }
      }
    }

    // 如果超出范围，使用线性插值
    if (completionRate > 100) {
      return Math.min(100, metric.scoringCriteria[0].score * (completionRate / 100))
    }

    return 0
  }

  /**
   * 计算定性指标得分
   */
  private calculateQualitativeScore(level: number, metric: AssessmentMetric): number {
    const criterion = metric.scoringCriteria.find(c => c.level === level)
    return criterion?.score || 0
  }

  /**
   * 计算加权平均分
   */
  private calculateWeightedScore(items: Array<{ score: number; weight: number }>): number {
    const totalWeight = items.reduce((sum, item) => sum + item.weight, 0)
    if (totalWeight === 0) return 0

    const weightedSum = items.reduce((sum, item) => sum + item.score * item.weight, 0)
    return Number((weightedSum / totalWeight).toFixed(2))
  }

  /**
   * 确定绩效等级
   */
  determineGrade(score: number, distribution?: GradeDistribution): string {
    // 默认等级分布
    const defaultGrades = [
      { grade: 'A', min: 90, max: 100 },
      { grade: 'B', min: 80, max: 89.99 },
      { grade: 'C', min: 70, max: 79.99 },
      { grade: 'D', min: 60, max: 69.99 },
      { grade: 'E', min: 0, max: 59.99 }
    ]

    for (const grade of defaultGrades) {
      if (score >= grade.min && score <= grade.max) {
        return grade.grade
      }
    }

    return 'E'
  }

  /**
   * 强制分布校准
   */
  calibrateScores(
    scores: Array<{ employeeId: string; score: number }>,
    distribution: GradeDistribution
  ): Array<{ employeeId: string; score: number; grade: string; calibrated: boolean }> {
    // 排序
    const sorted = [...scores].sort((a, b) => b.score - a.score)
    const total = sorted.length
    const result = []

    let currentIndex = 0

    // 按比例分配等级
    for (const [grade, range] of Object.entries(distribution)) {
      const minCount = Math.floor((total * range.min) / 100)
      const maxCount = Math.ceil((total * range.max) / 100)
      const targetCount = Math.round((minCount + maxCount) / 2)

      for (let i = 0; i < targetCount && currentIndex < total; i++) {
        result.push({
          ...sorted[currentIndex],
          grade,
          calibrated: true
        })
        currentIndex++
      }
    }

    // 处理剩余人员
    while (currentIndex < total) {
      const score = sorted[currentIndex].score
      result.push({
        ...sorted[currentIndex],
        grade: this.determineGrade(score),
        calibrated: false
      })
      currentIndex++
    }

    return result
  }

  /**
   * 计算排名
   */
  calculateRanking(
    scores: Array<{ employeeId: string; score: number }>,
    groupBy?: 'department' | 'position'
  ): Map<string, number> {
    const sorted = [...scores].sort((a, b) => b.score - a.score)
    const rankings = new Map<string, number>()

    sorted.forEach((item, index) => {
      rankings.set(item.employeeId, index + 1)
    })

    return rankings
  }

  /**
   * 生成改进建议
   */
  generateImprovements(result: AssessmentResult): string[] {
    const improvements: string[] = []

    // 分析各维度得分
    result.dimensionScores.forEach(dimension => {
      if (dimension.score < 70) {
        improvements.push(`加强${dimension.dimension}方面的能力提升`)
      }

      // 分析具体指标
      dimension.metrics.forEach((metric: MetricScore) => {
        if (metric.score < 60) {
          improvements.push(`重点改进${metric.metricName}`)
        }
      })
    })

    // 根据等级给出建议
    if (result.grade === 'D' || result.grade === 'E') {
      improvements.push('建议制定个人改进计划')
      improvements.push('加强与上级的沟通反馈')
    }

    return improvements
  }

  /**
   * 分组指标
   */
  private groupMetricsByDimension(metrics: AssessmentMetric[]): Record<string, AssessmentMetric[]> {
    return metrics.reduce(
      (groups, metric) => {
        const dimension = this.getMetricDimension(metric)
        if (!groups[dimension]) {
          groups[dimension] = []
        }
        groups[dimension].push(metric)
        return groups
      },
      {} as Record<string, AssessmentMetric[]>
    )
  }

  /**
   * 获取指标维度
   */
  private getMetricDimension(metric: AssessmentMetric): string {
    // 根据指标类别映射到维度
    const dimensionMap: Record<string, string> = {
      kpi: '工作业绩',
      okr: '目标达成',
      behavior: '工作态度',
      skill: '工作能力',
      custom: '其他'
    }

    return dimensionMap[metric.category] || '其他'
  }

  /**
   * 获取维度权重
   */
  private getDimensionWeight(dimension: string): number {
    // 默认维度权重配置
    const weightMap: Record<string, number> = {
      工作业绩: 50,
      工作能力: 20,
      工作态度: 20,
      团队协作: 10
    }

    return weightMap[dimension] || 10
  }

  /**
   * 获取实际值（模拟）
   */
  private getActualValue(metric: AssessmentMetric, task: AssessmentTask): number {
    // 实际应该从数据源获取
    return Math.random() * 100 + 50
  }

  /**
   * 获取定性评级（模拟）
   */
  private getQualitativeLevel(metric: AssessmentMetric, task: AssessmentTask): number {
    // 实际应该从评价数据获取
    return Math.floor(Math.random() * 5) + 1
  }
}

/**
 * 绩效趋势分析
 */
export class PerformanceTrendAnalyzer {
  /**
   * 计算绩效趋势
   */
  calculateTrend(historicalScores: number[]): {
    trend: 'up' | 'down' | 'stable'
    percentage: number
    forecast: number
  } {
    if (historicalScores.length < 2) {
      return { trend: 'stable', percentage: 0, forecast: historicalScores[0] || 0 }
    }

    const recent = historicalScores[historicalScores.length - 1]
    const previous = historicalScores[historicalScores.length - 2]
    const change = recent - previous
    const percentage = (change / previous) * 100

    // 简单线性预测
    const forecast = recent + change

    return {
      trend: change > 0 ? 'up' : change < 0 ? 'down' : 'stable',
      percentage: Math.abs(percentage),
      forecast: Math.max(0, Math.min(100, forecast))
    }
  }

  /**
   * 分析绩效稳定性
   */
  analyzeStability(scores: number[]): {
    mean: number
    standardDeviation: number
    coefficientOfVariation: number
    stability: 'high' | 'medium' | 'low'
  } {
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length

    const variance =
      scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length
    const standardDeviation = Math.sqrt(variance)
    const coefficientOfVariation = (standardDeviation / mean) * 100

    const stability =
      coefficientOfVariation < 10 ? 'high' : coefficientOfVariation < 20 ? 'medium' : 'low'

    return {
      mean,
      standardDeviation,
      coefficientOfVariation,
      stability
    }
  }
}

/**
 * 360度评价聚合器
 */
export class Review360Aggregator {
  /**
   * 聚合360度评价结果
   */
  aggregate(
    reviews: Array<{
      evaluatorType: string
      scores: Record<string, number>
      weight: number
    }>
  ): {
    totalScore: number
    dimensionScores: Record<string, number>
    consensus: number
  } {
    // 计算各维度平均分
    const dimensionScores: Record<string, number[]> = {}

    reviews.forEach(review => {
      Object.entries(review.scores).forEach(([dimension, score]) => {
        if (!dimensionScores[dimension]) {
          dimensionScores[dimension] = []
        }
        dimensionScores[dimension].push(score)
      })
    })

    // 计算加权平均
    const aggregatedScores: Record<string, number> = {}
    let totalScore = 0
    let totalWeight = 0

    Object.entries(dimensionScores).forEach(([dimension, scores]) => {
      const avg = scores.reduce((sum, s) => sum + s, 0) / scores.length
      aggregatedScores[dimension] = avg
      totalScore += avg
      totalWeight += 1
    })

    // 计算共识度（标准差的倒数）
    const consensus = this.calculateConsensus(dimensionScores)

    return {
      totalScore: totalScore / totalWeight,
      dimensionScores: aggregatedScores,
      consensus
    }
  }

  /**
   * 计算评价共识度
   */
  private calculateConsensus(dimensionScores: Record<string, number[]>): number {
    const variances: number[] = []

    Object.values(dimensionScores).forEach(scores => {
      if (scores.length > 1) {
        const mean = scores.reduce((sum, s) => sum + s, 0) / scores.length
        const variance = scores.reduce((sum, s) => sum + Math.pow(s - mean, 2), 0) / scores.length
        variances.push(variance)
      }
    })

    const avgVariance = variances.reduce((sum, v) => sum + v, 0) / variances.length

    // 共识度：100 - 平均标准差
    return Math.max(0, 100 - Math.sqrt(avgVariance))
  }
}

// 导出单例
export const scoreCalculator = new PerformanceScoreCalculator()
export const trendAnalyzer = new PerformanceTrendAnalyzer()
export const review360Aggregator = new Review360Aggregator()
