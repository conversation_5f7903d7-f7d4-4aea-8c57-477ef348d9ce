/**
 * 剪贴板工具函数
 */

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns 是否复制成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    // 优先使用 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text)
      return true
    }

    // 降级方案：使用 document.execCommand
    const textarea = document.createElement('textarea')
    textarea.value = text
    textarea.style.position = 'fixed'
    textarea.style.opacity = '0'
    textarea.style.left = '-9999px'

    document.body.appendChild(textarea)
    textarea.select()

    try {
      const success = document.execCommand('copy')
      document.body.removeChild(textarea)
      return success
    } catch {
      document.body.removeChild(textarea)
      return false
    }
  } catch (__error) {
    return false
  }
}

/**
 * 从剪贴板读取文本
 * @returns 剪贴板中的文本内容
 */
export async function readFromClipboard(): Promise<string | null> {
  try {
    // 检查是否支持 Clipboard API
    if (navigator.clipboard && navigator.clipboard.readText) {
      const text = await navigator.clipboard.readText()
      return text
    }

    // 不支持的情况下返回 null
    return null
  } catch (__error) {
    return null
  }
}

/**
 * 复制图片到剪贴板
 * @param blob 图片 Blob 对象
 * @returns 是否复制成功
 */
export async function copyImageToClipboard(blob: Blob): Promise<boolean> {
  try {
    // 检查是否支持 Clipboard API
    if (!navigator.clipboard || !navigator.clipboard.write) {
      return false
    }

    // 创建 ClipboardItem
    const item = new ClipboardItem({
      [blob.type]: blob
    })

    await navigator.clipboard.write([item])
    return true
  } catch (__error) {
    return false
  }
}

/**
 * 从剪贴板读取图片
 * @returns 图片 Blob 对象或 null
 */
export async function readImageFromClipboard(): Promise<Blob | null> {
  try {
    // 检查是否支持 Clipboard API
    if (!navigator.clipboard || !navigator.clipboard.read) {
      return null
    }

    const items = await navigator.clipboard.read()

    for (const item of items) {
      // 查找图片类型
      const imageTypes = item.types.filter(type => type.startsWith('image/'))

      if (imageTypes.length > 0) {
        const blob = await item.getType(imageTypes[0])
        return blob
      }
    }

    return null
  } catch (__error) {
    return null
  }
}

/**
 * 检查剪贴板 API 是否可用
 * @returns API 可用性状态
 */
export function checkClipboardSupport(): {
  writeText: boolean
  readText: boolean
  writeImage: boolean
  readImage: boolean
} {
  return {
    writeText: !!(navigator.clipboard && navigator.clipboard.writeText),
    readText: !!(navigator.clipboard && navigator.clipboard.readText),
    writeImage: !!(navigator.clipboard && navigator.clipboard.write),
    readImage: !!(navigator.clipboard && navigator.clipboard.read)
  }
}

/**
 * 复制 HTML 内容到剪贴板
 * @param html HTML 字符串
 * @param text 纯文本备选内容
 * @returns 是否复制成功
 */
export async function copyHTMLToClipboard(html: string, text?: string): Promise<boolean> {
  try {
    // 检查是否支持 Clipboard API
    if (navigator.clipboard && navigator.clipboard.write) {
      const htmlBlob = new Blob([html], { type: 'text/html' })
      const textBlob = new Blob([text || html], { type: 'text/plain' })

      const item = new ClipboardItem({
        'text/html': htmlBlob,
        'text/plain': textBlob
      })

      await navigator.clipboard.write([item])
      return true
    }

    // 降级方案：只复制纯文本
    return copyToClipboard(text || html)
  } catch (__error) {
    // 降级到纯文本复制
    return copyToClipboard(text || html)
  }
}

/**
 * 复制表格数据到剪贴板（支持 Excel 粘贴）
 * @param data 表格数据
 * @param headers 表头
 * @returns 是否复制成功
 */
export async function copyTableToClipboard(
  data: unknown[][],
  headers?: string[]
): Promise<boolean> {
  try {
    // 构建 TSV 格式（Tab 分隔值）
    let tsv = ''

    // 添加表头
    if (headers && headers.length > 0) {
      tsv += headers.join('\t') + '\n'
    }

    // 添加数据行
    data.forEach(row => {
      tsv +=
        row
          .map(cell => {
            // 处理特殊字符
            const value = String(cell ?? '')
            // 如果包含 tab、换行或双引号，需要用双引号包裹
            if (value.includes('\t') || value.includes('\n') || value.includes('"')) {
              return `"${value.replace(/"/g, '""')}"`
            }
            return value
          })
          .join('\t') + '\n'
    })

    // 同时准备 HTML 表格格式
    let html = '<table>'

    if (headers && headers.length > 0) {
      html += '<thead><tr>'
      headers.forEach(header => {
        html += `<th>${header}</th>`
      })
      html += '</tr></thead>'
    }

    html += '<tbody>'
    data.forEach(row => {
      html += '<tr>'
      row.forEach(cell => {
        html += `<td>${cell ?? ''}</td>`
      })
      html += '</tr>'
    })
    html += '</tbody></table>'

    // 复制多种格式
    if (navigator.clipboard && navigator.clipboard.write) {
      const htmlBlob = new Blob([html], { type: 'text/html' })
      const textBlob = new Blob([tsv], { type: 'text/plain' })

      const item = new ClipboardItem({
        'text/html': htmlBlob,
        'text/plain': textBlob
      })

      await navigator.clipboard.write([item])
      return true
    }

    // 降级方案
    return copyToClipboard(tsv)
  } catch (__error) {
    return false
  }
}
