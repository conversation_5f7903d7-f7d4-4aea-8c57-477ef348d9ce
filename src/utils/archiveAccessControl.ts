import type { User } from '@/types/user'

interface AccessPolicy {
  id: string
  name: string
  description: string
  resourceTypes: string[]
  permissions: Permission[]
  conditions: AccessCondition[]
  priority: number
  enabled: boolean
}

interface Permission {
  action: 'read' | 'write' | 'delete' | 'export' | 'share' | 'modify'
  allowed: boolean
  conditions?: string[]
}

interface AccessCondition {
  type: 'role' | 'department' | 'position' | 'level' | 'custom'
  field: string
  operator: 'eq' | 'ne' | 'in' | 'contains' | 'gt' | 'lt'

  value: unknown
}

interface AccessRequest {
  user: User
  resource: ArchiveResource
  action: 'read' | 'write' | 'delete' | 'export' | 'share' | 'modify'
  context?: Record<string, unknown>
}

interface ArchiveResource {
  id: string
  type: 'employee_file' | 'contract' | 'salary_record' | 'performance' | 'training' | 'discipline'
  ownerId: string
  ownerDepartment: string
  securityLevel: 'public' | 'internal' | 'confidential' | 'secret'
  metadata: Record<string, unknown>
}

interface AccessResult {
  granted: boolean
  reason: string
  appliedPolicies: string[]
  restrictions?: AccessRestriction[]
  auditInfo: AuditInfo
}

interface AccessRestriction {
  type: 'field_mask' | 'time_limit' | 'export_disable' | 'watermark'
  config: Record<string, unknown>
}

interface AuditInfo {
  timestamp: string
  userId: string
  userName: string
  action: string
  resourceId: string
  result: boolean
  ip?: string
  userAgent?: string
  sessionId?: string
}

/**
 * CLEAN-FUNC-051: 档案访问控制
 * 实现基于角色和权限的档案访问控制系统
 */
export class ArchiveAccessControl {
  private policies: Map<string, AccessPolicy> = new Map()
  private auditLogs: AuditInfo[] = []
  private maxAuditLogs = 10000

  constructor() {
    this.initializeDefaultPolicies()
  }

  // 初始化默认访问策略
  private initializeDefaultPolicies() {
    const defaultPolicies: AccessPolicy[] = [
      {
        id: 'self_access',
        name: '员工自访问',
        description: '员工可以查看自己的档案信息',
        resourceTypes: ['employee_file', 'salary_record', 'performance'],
        permissions: [
          { action: 'read', allowed: true },
          { action: 'export', allowed: false },
          { action: 'write', allowed: false },
          { action: 'delete', allowed: false },
          { action: 'share', allowed: false }
        ],
        conditions: [{ type: 'custom', field: 'is_owner', operator: 'eq', value: true }],
        priority: 100,
        enabled: true
      },
      {
        id: 'hr_full_access',
        name: '人事全权限',
        description: '人事部门对所有档案的完整访问权限',
        resourceTypes: [
          'employee_file',
          'contract',
          'salary_record',
          'performance',
          'training',
          'discipline'
        ],
        permissions: [
          { action: 'read', allowed: true },
          { action: 'write', allowed: true },
          { action: 'delete', allowed: true },
          { action: 'export', allowed: true },
          { action: 'share', allowed: true },
          { action: 'modify', allowed: true }
        ],
        conditions: [
          { type: 'role', field: 'roles', operator: 'in', value: ['hr_manager', 'hr_specialist'] }
        ],
        priority: 90,
        enabled: true
      },
      {
        id: 'manager_department_access',
        name: '部门经理权限',
        description: '部门经理可以查看本部门员工档案',
        resourceTypes: ['employee_file', 'performance', 'training'],
        permissions: [
          { action: 'read', allowed: true },
          { action: 'export', allowed: true, conditions: ['watermark'] },
          { action: 'write', allowed: false },
          { action: 'delete', allowed: false },
          { action: 'share', allowed: false }
        ],
        conditions: [
          { type: 'role', field: 'roles', operator: 'in', value: ['manager', 'dept_manager'] },
          { type: 'department', field: 'same_department', operator: 'eq', value: true }
        ],
        priority: 80,
        enabled: true
      },
      {
        id: 'finance_salary_access',
        name: '财务薪资权限',
        description: '财务部门可以访问薪资相关档案',
        resourceTypes: ['salary_record', 'contract'],
        permissions: [
          { action: 'read', allowed: true },
          { action: 'write', allowed: true },
          { action: 'export', allowed: true },
          { action: 'delete', allowed: false },
          { action: 'share', allowed: false }
        ],
        conditions: [
          {
            type: 'role',
            field: 'roles',
            operator: 'in',
            value: ['finance_manager', 'finance_specialist']
          }
        ],
        priority: 70,
        enabled: true
      },
      {
        id: 'confidential_restriction',
        name: '机密档案限制',
        description: '机密级别档案的访问限制',
        resourceTypes: ['employee_file', 'contract', 'salary_record', 'discipline'],
        permissions: [
          { action: 'read', allowed: false },
          { action: 'write', allowed: false },
          { action: 'delete', allowed: false },
          { action: 'export', allowed: false },
          { action: 'share', allowed: false }
        ],
        conditions: [
          {
            type: 'custom',
            field: 'security_level',
            operator: 'in',
            value: ['confidential', 'secret']
          },
          { type: 'role', field: 'roles', operator: 'ne', value: 'admin' }
        ],
        priority: 95,
        enabled: true
      }
    ]

    defaultPolicies.forEach(policy => {
      this.policies.set(policy.id, policy)
    })
  }

  // 检查访问权限
  async checkAccess(request: AccessRequest): Promise<AccessResult> {
    const startTime = Date.now()

    try {
      // 获取适用的策略
      const applicablePolicies = this.getApplicablePolicies(request)

      // 按优先级排序
      applicablePolicies.sort((a, b) => b.priority - a.priority)

      let granted = false
      const appliedPolicies: string[] = []
      const restrictions: AccessRestriction[] = []
      let finalReason = '默认拒绝访问'

      // 遍历策略进行评估
      for (const policy of applicablePolicies) {
        const policyResult = this.evaluatePolicy(policy, request)

        if (policyResult.applicable) {
          appliedPolicies.push(policy.id)

          if (policyResult.granted) {
            granted = true
            finalReason = `根据策略 "${policy.name}" 允许访问`

            // 添加限制条件
            if (policyResult.restrictions) {
              restrictions.push(...policyResult.restrictions)
            }

            // 高优先级策略生效后，可以终止评估
            break
          } else {
            finalReason = `根据策略 "${policy.name}" 拒绝访问: ${policyResult.reason}`
            // 拒绝策略也要记录，但继续评估其他策略
          }
        }
      }

      // 创建审计信息
      const auditInfo: AuditInfo = {
        timestamp: new Date().toISOString(),
        userId: request.user.id,
        userName: request.user.name,
        action: request.action,
        resourceId: request.resource.id,
        result: granted,
        ip: request.context?.ip,
        userAgent: request.context?.userAgent,
        sessionId: request.context?.sessionId
      }

      // 记录审计日志
      this.recordAudit(auditInfo)

      const result: AccessResult = {
        granted,
        reason: finalReason,
        appliedPolicies,
        restrictions: restrictions.length > 0 ? restrictions : undefined,
        auditInfo
      }

      - startTime}ms):`, {
        user: request.user.name,
        resource: request.resource.id,
        action: request.action,
        granted,
        reason: finalReason
      })

      return result
    } catch (__error) {
      const auditInfo: AuditInfo = {
        timestamp: new Date().toISOString(),
        userId: request.user.id,
        userName: request.user.name,
        action: request.action,
        resourceId: request.resource.id,
        result: false
      }

      this.recordAudit(auditInfo)

      return {
        granted: false,
        reason: `访问控制检查失败: ${error instanceof Error ? error.message : '未知错误'}`,
        appliedPolicies: [],
        auditInfo
      }
    }
  }

  // 获取适用的策略
  private getApplicablePolicies(request: AccessRequest): AccessPolicy[] {
    const applicablePolicies: AccessPolicy[] = []

    for (const policy of this.policies.values()) {
      if (!policy.enabled) continue

      // 检查资源类型是否匹配
      if (!policy.resourceTypes.includes(request.resource.type)) continue

      // 检查是否有对应的权限定义
      const permission = policy.permissions.find(p => p.action === request.action)
      if (!permission) continue

      applicablePolicies.push(policy)
    }

    return applicablePolicies
  }

  // 评估策略
  private evaluatePolicy(
    policy: AccessPolicy,
    request: AccessRequest
  ): {
    applicable: boolean
    granted: boolean
    reason: string
    restrictions?: AccessRestriction[]
  } {
    try {
      // 检查策略条件
      const conditionsResult = this.evaluateConditions(policy.conditions, request)
      if (!conditionsResult.satisfied) {
        return {
          applicable: false,
          granted: false,
          reason: conditionsResult.reason
        }
      }

      // 获取权限配置
      const permission = policy.permissions.find(p => p.action === request.action)
      if (!permission) {
        return {
          applicable: false,
          granted: false,
          reason: '策略中未定义此操作权限'
        }
      }

      const restrictions: AccessRestriction[] = []

      // 检查权限条件
      if (permission.conditions) {
        for (const condition of permission.conditions) {
          switch (condition) {
            case 'watermark':
              restrictions.push({
                type: 'watermark',
                config: {
                  text: `${request.user.name} - ${new Date().toLocaleString()}`,
                  position: 'bottom-right'
                }
              })
              break
            case 'field_mask':
              restrictions.push({
                type: 'field_mask',
                config: {
                  fields: ['idNumber', 'bankAccount', 'emergencyContact']
                }
              })
              break
            case 'time_limit':
              restrictions.push({
                type: 'time_limit',
                config: {
                  duration: 3600000, // 1小时
                  message: '访问将在1小时后过期'
                }
              })
              break
          }
        }
      }

      return {
        applicable: true,
        granted: permission.allowed,
        reason: permission.allowed ? '策略允许访问' : '策略拒绝访问',
        restrictions: restrictions.length > 0 ? restrictions : undefined
      }
    } catch (__error) {
      return {
        applicable: false,
        granted: false,
        reason: `策略评估失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  // 评估条件
  private evaluateConditions(
    conditions: AccessCondition[],
    request: AccessRequest
  ): {
    satisfied: boolean
    reason: string
  } {
    for (const condition of conditions) {
      const result = this.evaluateCondition(condition, request)
      if (!result.satisfied) {
        return result
      }
    }

    return { satisfied: true, reason: '所有条件都满足' }
  }

  // 评估单个条件
  private evaluateCondition(
    condition: AccessCondition,
    request: AccessRequest
  ): {
    satisfied: boolean
    reason: string
  } {
    try {
      let actualValue: unknown

      switch (condition.type) {
        case 'role':
          actualValue = request.user.roles || []
          break
        case 'department':
          if (condition.field === 'same_department') {
            actualValue = request.user.departmentId === request.resource.ownerDepartment
          } else {
            actualValue = request.user.departmentId
          }
          break
        case 'position':
          actualValue = request.user.positionId
          break
        case 'level':
          actualValue = (request.user as unknown).level || 0
          break
        case 'custom':
          if (condition.field === 'is_owner') {
            actualValue = request.user.id === request.resource.ownerId
          } else if (condition.field === 'security_level') {
            actualValue = request.resource.securityLevel
          } else {
            actualValue = request.resource.metadata[condition.field]
          }
          break
        default:
          return { satisfied: false, reason: `不支持的条件类型: ${condition.type}` }
      }

      const satisfied = this.evaluateOperator(actualValue, condition.operator, condition.value)

      return {
        satisfied,
        reason: satisfied
          ? `条件满足: ${condition.field} ${condition.operator} ${condition.value}`
          : `条件不满足: ${condition.field} ${condition.operator} ${condition.value} (实际值: ${actualValue})`
      }
    } catch (__error) {
      return {
        satisfied: false,
        reason: `条件评估失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  // 评估操作符

  private evaluateOperator(
    actualValue: unknown,
    operator: string,
    expectedValue: unknown
  ): boolean {
    switch (operator) {
      case 'eq':
        return actualValue === expectedValue
      case 'ne':
        return actualValue !== expectedValue
      case 'in':
        if (Array.isArray(actualValue)) {
          return actualValue.some(v =>
            Array.isArray(expectedValue) ? expectedValue.includes(v) : v === expectedValue
          )
        }
        return Array.isArray(expectedValue)
          ? expectedValue.includes(actualValue)
          : actualValue === expectedValue
      case 'contains':
        if (typeof actualValue === 'string') {
          return actualValue.includes(expectedValue)
        }
        if (Array.isArray(actualValue)) {
          return actualValue.includes(expectedValue)
        }
        return false
      case 'gt':
        return actualValue > expectedValue
      case 'lt':
        return actualValue < expectedValue
      default:
        return false
    }
  }

  // 记录审计日志
  private recordAudit(auditInfo: AuditInfo) {
    this.auditLogs.push(auditInfo)

    // 限制日志数量
    if (this.auditLogs.length > this.maxAuditLogs) {
      this.auditLogs = this.auditLogs.slice(-this.maxAuditLogs)
    }

    // 实际项目中这里应该将审计日志发送到后端或外部审计系统
    }

  // 批量检查权限
  async checkBatchAccess(requests: AccessRequest[]): Promise<AccessResult[]> {
    const results = await Promise.all(requests.map(request => this.checkAccess(request)))

    return results
  }

  // 预检查权限（不记录审计日志）
  async preCheckAccess(request: AccessRequest): Promise<boolean> {
    const applicablePolicies = this.getApplicablePolicies(request)
    applicablePolicies.sort((a, b) => b.priority - a.priority)

    for (const policy of applicablePolicies) {
      const policyResult = this.evaluatePolicy(policy, request)
      if (policyResult.applicable && policyResult.granted) {
        return true
      }
    }

    return false
  }

  // 获取用户可访问的资源列表
  async getUserAccessibleResources(
    user: User,
    resourceType: string,
    action: string = 'read'
  ): Promise<string[]> {
    // 实际项目中这里应该从数据库查询用户可访问的资源
    // 这里只是示例实现
    const mockResources = this.getMockResourcesByType(resourceType)
    const accessibleResources: string[] = []

    for (const resource of mockResources) {
      const canAccess = await this.preCheckAccess({
        user,
        resource,
        action: action as unknown
      })

      if (canAccess) {
        accessibleResources.push(resource.id)
      }
    }

    return accessibleResources
  }

  // 获取模拟资源数据
  private getMockResourcesByType(resourceType: string): ArchiveResource[] {
    // 模拟数据，实际项目中从数据库获取
    return [
      {
        id: 'resource_001',
        type: resourceType as unknown,
        ownerId: 'user_001',
        ownerDepartment: 'dept_tech',
        securityLevel: 'internal',
        metadata: {}
      }
    ]
  }

  // 添加策略
  addPolicy(policy: AccessPolicy) {
    this.policies.set(policy.id, policy)
    }

  // 删除策略
  removePolicy(policyId: string) {
    const removed = this.policies.delete(policyId)
    if (removed) {
      }
    return removed
  }

  // 更新策略
  updatePolicy(policyId: string, updates: Partial<AccessPolicy>) {
    const policy = this.policies.get(policyId)
    if (policy) {
      const updatedPolicy = { ...policy, ...updates }
      this.policies.set(policyId, updatedPolicy)
      return updatedPolicy
    }
    return null
  }

  // 获取所有策略
  getAllPolicies(): AccessPolicy[] {
    return Array.from(this.policies.values())
  }

  // 获取策略
  getPolicy(policyId: string): AccessPolicy | undefined {
    return this.policies.get(policyId)
  }

  // 获取审计日志
  getAuditLogs(filters?: {
    userId?: string
    resourceId?: string
    action?: string
    startTime?: string
    endTime?: string
    result?: boolean
  }): AuditInfo[] {
    let logs = [...this.auditLogs]

    if (filters) {
      logs = logs.filter(log => {
        if (filters.userId && log.userId !== filters.userId) return false
        if (filters.resourceId && log.resourceId !== filters.resourceId) return false
        if (filters.action && log.action !== filters.action) return false
        if (filters.result !== undefined && log.result !== filters.result) return false
        if (filters.startTime && log.timestamp < filters.startTime) return false
        if (filters.endTime && log.timestamp > filters.endTime) return false
        return true
      })
    }

    return logs.sort((a, b) => b.timestamp.localeCompare(a.timestamp))
  }

  // 清除审计日志
  clearAuditLogs() {
    this.auditLogs = []
    }

  // 导出审计日志
  exportAuditLogs(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['时间', '用户ID', '用户名', '操作', '资源ID', '结果', 'IP地址']
      const rows = this.auditLogs.map(log => [
        log.timestamp,
        log.userId,
        log.userName,
        log.action,
        log.resourceId,
        log.result ? '允许' : '拒绝',
        log.ip || ''
      ])

      return [headers, ...rows].map(row => row.join(',')).join('\n')
    } else {
      return JSON.stringify(this.auditLogs, null, 2)
    }
  }
}

// 全局档案访问控制实例
export const archiveAccessControl = new ArchiveAccessControl()

// 便捷函数
export async function checkArchiveAccess(
  user: User,
  resourceId: string,
  resourceType: string,
  action: string,
  context?: Record<string, unknown>
): Promise<AccessResult> {
  // 构造资源对象（实际项目中应该从数据库获取）
  const resource: ArchiveResource = {
    id: resourceId,
    type: resourceType as unknown,
    ownerId: 'unknown',
    ownerDepartment: 'unknown',
    securityLevel: 'internal',
    metadata: {}
  }

  return archiveAccessControl.checkAccess({
    user,
    resource,
    action: action as unknown,
    context
  })
}

export async function checkUserCanAccessResource(
  user: User,
  resourceId: string,
  action: string = 'read'
): Promise<boolean> {
  const result = await checkArchiveAccess(user, resourceId, 'employee_file', action)
  return result.granted
}
