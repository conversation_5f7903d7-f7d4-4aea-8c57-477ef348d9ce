/**
 * 语音播报管理器
 * 基于Web Speech Synthesis API实现语音合成和播报功能
 */

import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 播报配置
export interface VoiceConfig {
  lang: string // 语言
  voice: string // 声音名称
  volume: number // 音量 (0-1)
  rate: number // 语速 (0.1-10)
  pitch: number // 音调 (0-2)
  pauseBetween: number // 段落间隔(毫秒)
}

// 播报任务
export interface AnnounceTask {
  id: string
  text: string
  priority: 'high' | 'normal' | 'low'
  category: string
  timestamp: Date
  status: 'pending' | 'speaking' | 'completed' | 'cancelled'
}

// 播报模板
export interface AnnounceTemplate {
  id: string
  name: string
  category: string
  template: string
  variables?: string[]
  voice?: Partial<VoiceConfig>
}

export class VoiceAnnouncer {
  private static instance: VoiceAnnouncer
  private synthesis: SpeechSynthesis | null = null
  private voices: SpeechSynthesisVoice[] = []
  private isSupported = false
  private queue: AnnounceTask[] = []
  private templates: Map<string, AnnounceTemplate> = new Map()

  // 状态管理
  public isSpeaking = ref(false)
  public isPaused = ref(false)
  public currentTask = ref<AnnounceTask | null>(null)
  public error = ref<string | null>(null)

  // 配置
  private config: VoiceConfig = {
    lang: 'zh-CN',
    voice: '',
    volume: 1.0,
    rate: 1.0,
    pitch: 1.0,
    pauseBetween: 500
  }

  // 播报历史
  public history = ref<AnnounceTask[]>([])

  // 默认模板
  private defaultTemplates: AnnounceTemplate[] = [
    {
      id: 'metric-change',
      name: '指标变化',
      category: 'metric',
      template: '{metric}当前值为{value}{unit}，{trend}{change}{unit}',
      variables: ['metric', 'value', 'unit', 'trend', 'change']
    },
    {
      id: 'alert-warning',
      name: '预警通知',
      category: 'alert',
      template: '注意，{alertType}预警：{content}，请及时处理',
      variables: ['alertType', 'content'],
      voice: { rate: 1.2, pitch: 1.2 }
    },
    {
      id: 'report-summary',
      name: '报告摘要',
      category: 'report',
      template: '{reportName}已生成。{summary}',
      variables: ['reportName', 'summary']
    },
    {
      id: 'task-complete',
      name: '任务完成',
      category: 'task',
      template: '{taskName}已完成，耗时{duration}',
      variables: ['taskName', 'duration']
    }
  ]

  private constructor() {
    this.init()
  }

  static getInstance(): VoiceAnnouncer {
    if (!VoiceAnnouncer.instance) {
      VoiceAnnouncer.instance = new VoiceAnnouncer()
    }
    return VoiceAnnouncer.instance
  }

  /**
   * 初始化
   */
  private init(): void {
    // 检查浏览器支持
    if (!('speechSynthesis' in window)) {
      this.isSupported = false
      this.error.value = '当前浏览器不支持语音合成功能'
      return
    }

    this.isSupported = true
    this.synthesis = window.speechSynthesis

    // 加载声音列表
    this.loadVoices()

    // 监听声音加载
    if (this.synthesis.onvoiceschanged !== undefined) {
      this.synthesis.onvoiceschanged = () => this.loadVoices()
    }

    // 初始化模板
    this.initTemplates()

    // 启动队列处理
    this.processQueue()
  }

  /**
   * 加载声音列表
   */
  private loadVoices(): void {
    if (!this.synthesis) return

    this.voices = this.synthesis.getVoices()

    // 选择默认中文声音
    if (!this.config.voice && this.voices.length > 0) {
      const chineseVoice = this.voices.find(
        voice => voice.lang.includes('zh') || voice.lang.includes('CN')
      )

      if (chineseVoice) {
        this.config.voice = chineseVoice.name
      }
    }
  }

  /**
   * 初始化模板
   */
  private initTemplates(): void {
    this.defaultTemplates.forEach(template => {
      this.templates.set(template.id, template)
    })
  }

  /**
   * 开始播报
   */
  speak(text: string, options?: Partial<VoiceConfig>): string {
    if (!this.isSupported) {
      ElMessage.error('语音播报功能不可用')
      return ''
    }

    const task: AnnounceTask = {
      id: `announce-${Date.now()}`,
      text,
      priority: 'normal',
      category: 'custom',
      timestamp: new Date(),
      status: 'pending'
    }

    // 添加到队列
    this.addToQueue(task, options)

    return task.id
  }

  /**
   * 使用模板播报
   */
  speakWithTemplate(
    templateId: string,
    data: Record<string, unknown>,
    options?: Partial<VoiceConfig>
  ): string {
    const template = this.templates.get(templateId)
    if (!template) {
      return ''
    }

    // 替换变量
    let text = template.template
    if (template.variables) {
      template.variables.forEach(variable => {
        const value = data[variable] || ''
        text = text.replace(new RegExp(`\\{${variable}\\}`, 'g'), String(value))
      })
    }

    // 合并配置
    const mergedOptions = {
      ...options,
      ...template.voice
    }

    const task: AnnounceTask = {
      id: `announce-${Date.now()}`,
      text,
      priority: 'normal',
      category: template.category,
      timestamp: new Date(),
      status: 'pending'
    }

    this.addToQueue(task, mergedOptions)

    return task.id
  }

  /**
   * 添加到队列
   */
  private addToQueue(task: AnnounceTask, options?: Partial<VoiceConfig>): void {
    // 按优先级插入
    const priorityOrder = { high: 0, normal: 1, low: 2 }
    const insertIndex = this.queue.findIndex(
      t => priorityOrder[t.priority] > priorityOrder[task.priority]
    )

    if (insertIndex === -1) {
      this.queue.push(task)
    } else {
      this.queue.splice(insertIndex, 0, task)
    }

    // 保存配置
    if (options) {
      ;(task as unknown).voiceConfig = options
    }
  }

  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    while (true) {
      if (this.queue.length === 0 || this.isSpeaking.value) {
        await this.delay(100)
        continue
      }

      const task = this.queue.shift()
      if (!task) continue

      await this.performSpeak(task)
    }
  }

  /**
   * 执行播报
   */
  private async performSpeak(task: AnnounceTask): Promise<void> {
    if (!this.synthesis) return

    this.currentTask.value = task
    task.status = 'speaking'
    this.isSpeaking.value = true

    try {
      const utterance = new SpeechSynthesisUtterance(task.text)

      // 应用配置
      const config = { ...this.config, ...(task as unknown).voiceConfig }
      utterance.lang = config.lang
      utterance.volume = config.volume
      utterance.rate = config.rate
      utterance.pitch = config.pitch

      // 设置声音
      if (config.voice) {
        const voice = this.voices.find(v => v.name === config.voice)
        if (voice) {
          utterance.voice = voice
        }
      }

      // 设置事件监听
      utterance.onend = () => {
        task.status = 'completed'
        this.isSpeaking.value = false
        this.currentTask.value = null

        // 添加到历史
        this.history.value.unshift(task)
        if (this.history.value.length > 100) {
          this.history.value.pop()
        }
      }

      utterance.onerror = _event => {
        this.error.value = '语音播报失败'
        task.status = 'cancelled'
        this.isSpeaking.value = false
        this.currentTask.value = null
      }

      // 开始播报
      this.synthesis.speak(utterance)

      // 等待播报完成
      await new Promise<void>(_resolve => {
        utterance.onend = () => {
          task.status = 'completed'
          resolve()
        }
        utterance.onerror = () => {
          task.status = 'cancelled'
          resolve()
        }
      })

      // 段落间隔
      if (config.pauseBetween > 0) {
        await this.delay(config.pauseBetween)
      }
    } finally {
      this.isSpeaking.value = false
      this.currentTask.value = null
    }
  }

  /**
   * 暂停播报
   */
  pause(): void {
    if (!this.synthesis || !this.isSpeaking.value) return

    this.synthesis.pause()
    this.isPaused.value = true
  }

  /**
   * 恢复播报
   */
  resume(): void {
    if (!this.synthesis || !this.isPaused.value) return

    this.synthesis.resume()
    this.isPaused.value = false
  }

  /**
   * 停止播报
   */
  stop(): void {
    if (!this.synthesis) return

    this.synthesis.cancel()
    this.isSpeaking.value = false
    this.isPaused.value = false

    if (this.currentTask.value) {
      this.currentTask.value.status = 'cancelled'
      this.currentTask.value = null
    }
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    this.queue.forEach(task => {
      task.status = 'cancelled'
    })
    this.queue = []
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<VoiceConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 获取可用声音
   */
  getAvailableVoices(): SpeechSynthesisVoice[] {
    return this.voices
  }

  /**
   * 获取中文声音
   */
  getChineseVoices(): SpeechSynthesisVoice[] {
    return this.voices.filter(voice => voice.lang.includes('zh') || voice.lang.includes('CN'))
  }

  /**
   * 注册模板
   */
  registerTemplate(template: AnnounceTemplate): void {
    this.templates.set(template.id, template)
  }

  /**
   * 移除模板
   */
  removeTemplate(id: string): void {
    this.templates.delete(id)
  }

  /**
   * 获取所有模板
   */
  getAllTemplates(): AnnounceTemplate[] {
    return Array.from(this.templates.values())
  }

  /**
   * 清空历史
   */
  clearHistory(): void {
    this.history.value = []
  }

  /**
   * 检查支持状态
   */
  checkSupport(): boolean {
    return this.isSupported
  }

  /**
   * 延迟
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 播报指标变化
   */
  announceMetricChange(metric: string, value: number, unit: string, change: number): string {
    const trend = change > 0 ? '上升' : '下降'
    return this.speakWithTemplate('metric-change', {
      metric,
      value,
      unit,
      trend,
      change: Math.abs(change)
    })
  }

  /**
   * 播报预警
   */
  announceAlert(alertType: string, content: string): string {
    return this.speakWithTemplate(
      'alert-warning',
      {
        alertType,
        content
      },
      {
        rate: 1.2,
        volume: 1.0
      }
    )
  }

  /**
   * 播报报告摘要
   */
  announceReport(reportName: string, summary: string): string {
    return this.speakWithTemplate('report-summary', {
      reportName,
      summary
    })
  }

  /**
   * 播报任务完成
   */
  announceTaskComplete(taskName: string, duration: string): string {
    return this.speakWithTemplate('task-complete', {
      taskName,
      duration
    })
  }
}

// 导出单例
export const voiceAnnouncer = VoiceAnnouncer.getInstance()

// 导出Vue组合式API
export function useVoiceAnnouncer() {
  const announcer = voiceAnnouncer

  const isSupported = computed(() => announcer.checkSupport())

  const speak = (text: string, options?: Partial<VoiceConfig>) => {
    return announcer.speak(text, options)
  }

  const speakWithTemplate = (
    templateId: string,
    data: Record<string, unknown>,
    options?: Partial<VoiceConfig>
  ) => {
    return announcer.speakWithTemplate(templateId, data, options)
  }

  return {
    // 状态
    isSupported,
    isSpeaking: announcer.isSpeaking,
    isPaused: announcer.isPaused,
    currentTask: announcer.currentTask,
    error: announcer.error,
    history: announcer.history,

    // 方法
    speak,
    speakWithTemplate,
    pause: () => announcer.pause(),
    resume: () => announcer.resume(),
    stop: () => announcer.stop(),
    clearQueue: () => announcer.clearQueue(),
    updateConfig: (config: Partial<VoiceConfig>) => announcer.updateConfig(config),
    getAvailableVoices: () => announcer.getAvailableVoices(),
    getChineseVoices: () => announcer.getChineseVoices(),
    registerTemplate: (template: AnnounceTemplate) => announcer.registerTemplate(template),

    // 快捷方法
    announceMetricChange: (metric: string, value: number, unit: string, change: number) =>
      announcer.announceMetricChange(metric, value, unit, change),
    announceAlert: (alertType: string, content: string) =>
      announcer.announceAlert(alertType, content),
    announceReport: (reportName: string, summary: string) =>
      announcer.announceReport(reportName, summary),
    announceTaskComplete: (taskName: string, duration: string) =>
      announcer.announceTaskComplete(taskName, duration)
  }
}
