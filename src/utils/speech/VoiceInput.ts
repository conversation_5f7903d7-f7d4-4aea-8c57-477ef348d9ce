/**
 * 语音输入管理器
 * 基于Web Speech API实现语音识别功能
 */

import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 语音识别配置
export interface VoiceInputConfig {
  lang: string // 识别语言
  continuous: boolean // 是否持续识别
  interimResults: boolean // 是否返回中间结果
  maxAlternatives: number // 最大备选结果数
  grammars?: string[] // 语法规则
  confidence: number // 置信度阈值
}

// 识别结果
export interface RecognitionResult {
  transcript: string // 识别文本
  confidence: number // 置信度
  isFinal: boolean // 是否最终结果
  alternatives: Array<{
    // 备选结果
    transcript: string
    confidence: number
  }>
  timestamp: Date // 时间戳
}

// 语音命令
export interface VoiceCommand {
  pattern: RegExp // 匹配模式
  action: (match: RegExpMatchArray) => void // 执行动作
  description: string // 命令描述
  category: string // 命令分类
}

export class VoiceInputManager {
  private static instance: VoiceInputManager

  private recognition: unknown = null
  private isSupported = false
  private commands: Map<string, VoiceCommand> = new Map()

  // 状态管理
  public isListening = ref(false)
  public isProcessing = ref(false)
  public currentTranscript = ref('')
  public finalTranscript = ref('')
  public error = ref<string | null>(null)

  // 配置
  private config: VoiceInputConfig = {
    lang: 'zh-CN',
    continuous: false,
    interimResults: true,
    maxAlternatives: 3,
    confidence: 0.7
  }

  // 识别历史
  public history = ref<RecognitionResult[]>([])

  private constructor() {
    this.initRecognition()
    this.registerDefaultCommands()
  }

  static getInstance(): VoiceInputManager {
    if (!VoiceInputManager.instance) {
      VoiceInputManager.instance = new VoiceInputManager()
    }
    return VoiceInputManager.instance
  }

  /**
   * 初始化语音识别
   */
  private initRecognition(): void {
    // 检查浏览器支持
    const SpeechRecognition =
      (window as unknown).SpeechRecognition || (window as unknown).webkitSpeechRecognition

    if (!SpeechRecognition) {
      this.isSupported = false
      this.error.value = '当前浏览器不支持语音识别功能'
      return
    }

    this.isSupported = true
    this.recognition = new SpeechRecognition()

    // 配置识别器
    this.updateConfig(this.config)

    // 设置事件监听
    this.setupEventListeners()
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.recognition) return

    // 开始识别
    this.recognition.onstart = () => {
      this.isListening.value = true
      this.error.value = null
      this.currentTranscript.value = ''
    }

    // 识别结果

    this.recognition.onresult = (event: unknown) => {
      this.processResults(event)
    }

    // 识别错误

    this.recognition.onerror = (event: unknown) => {
      this.handleError(event)
    }

    // 识别结束
    this.recognition.onend = () => {
      this.isListening.value = false

      // 如果是持续识别模式且没有错误，自动重启
      if (this.config.continuous && !this.error.value) {
        setTimeout(() => this.start(), 100)
      }
    }

    // 无语音检测
    this.recognition.onspeechend = () => {
      if (!this.config.continuous) {
        this.stop()
      }
    }
  }

  /**
   * 处理识别结果
   */

  private processResults(event: unknown): void {
    let interimTranscript = ''
    let finalTranscript = ''

    // 处理所有结果
    for (let i = event.resultIndex; i < event.results.length; i++) {
      const result = event.results[i]
      const transcript = result[0].transcript

      if (result.isFinal) {
        finalTranscript += transcript

        // 创建识别结果对象
        const recognitionResult: RecognitionResult = {
          transcript: transcript.trim(),
          confidence: result[0].confidence || 1,
          isFinal: true,

          alternatives: Array.from(result).map((alt: unknown) => ({
            transcript: alt.transcript,
            confidence: alt.confidence || 0
          })),
          timestamp: new Date()
        }

        // 添加到历史记录
        this.history.value.unshift(recognitionResult)
        if (this.history.value.length > 100) {
          this.history.value.pop()
        }

        // 检查是否匹配命令
        this.checkCommands(transcript)

        // 触发最终结果事件
        this.onFinalResult(recognitionResult)
      } else {
        interimTranscript += transcript
      }
    }

    // 更新当前转录
    this.currentTranscript.value = interimTranscript
    this.finalTranscript.value = finalTranscript
  }

  /**
   * 检查语音命令
   */
  private checkCommands(transcript: string): void {
    const normalizedText = transcript.toLowerCase().trim()

    for (const [id, command] of this.commands) {
      const match = normalizedText.match(command.pattern)
      if (match) {
        try {
          command.action(match)
          ElMessage.success(`执行命令: ${command.description}`)
        } catch (__error) {
          ElMessage.error('命令执行失败')
        }
        break
      }
    }
  }

  /**
   * 处理错误
   */

  private handleError(event: unknown): void {
    let message = '语音识别错误'

    switch (event.error) {
      case 'no-speech':
        message = '未检测到语音输入'
        break
      case 'audio-capture':
        message = '未找到麦克风设备'
        break
      case 'not-allowed':
        message = '麦克风权限被拒绝'
        break
      case 'network':
        message = '网络连接错误'
        break
      case 'aborted':
        message = '语音识别被中断'
        break
      default:
        message = `语音识别错误: ${event.error}`
    }

    this.error.value = message
    ElMessage.error(message)
  }

  /**
   * 注册默认命令
   */
  private registerDefaultCommands(): void {
    // 查询命令
    this.registerCommand({
      pattern: /查询(.+)的?(.+)?/,
      action: _match => {
        },
      description: '查询数据',
      category: 'query'
    })

    // 打开命令
    this.registerCommand({
      pattern: /打开(.+)/,
      action: _match => {
        },
      description: '打开页面',
      category: 'navigation'
    })

    // 显示命令
    this.registerCommand({
      pattern: /显示(.+)图表/,
      action: _match => {
        },
      description: '显示图表',
      category: 'display'
    })

    // 导出命令
    this.registerCommand({
      pattern: /导出(.+)数据/,
      action: _match => {
        },
      description: '导出数据',
      category: 'export'
    })
  }

  /**
   * 开始语音识别
   */
  start(): void {
    if (!this.isSupported) {
      ElMessage.error('语音识别功能不可用')
      return
    }

    if (this.isListening.value) {
      return
    }

    try {
      this.recognition.start()
    } catch (__error) {
      if ((error as unknown).name === 'InvalidStateError') {
        // 已经在识别中，忽略
        return
      }
      ElMessage.error('启动语音识别失败')
    }
  }

  /**
   * 停止语音识别
   */
  stop(): void {
    if (!this.recognition || !this.isListening.value) {
      return
    }

    try {
      this.recognition.stop()
    } catch (__error) {
      }
  }

  /**
   * 中止语音识别
   */
  abort(): void {
    if (!this.recognition) {
      return
    }

    try {
      this.recognition.abort()
      this.isListening.value = false
    } catch (__error) {
      }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<VoiceInputConfig>): void {
    this.config = { ...this.config, ...config }

    if (this.recognition) {
      this.recognition.lang = this.config.lang
      this.recognition.continuous = this.config.continuous
      this.recognition.interimResults = this.config.interimResults
      this.recognition.maxAlternatives = this.config.maxAlternatives

      // 设置语法
      if (this.config.grammars && (window as unknown).SpeechGrammarList) {
        const grammarList = new (window as unknown).SpeechGrammarList()
        this.config.grammars.forEach(grammar => {
          grammarList.addFromString(grammar, 1)
        })
        this.recognition.grammars = grammarList
      }
    }
  }

  /**
   * 注册语音命令
   */
  registerCommand(command: VoiceCommand): void {
    const id = `${command.category}-${Date.now()}`
    this.commands.set(id, command)
  }

  /**
   * 移除语音命令
   */
  removeCommand(id: string): void {
    this.commands.delete(id)
  }

  /**
   * 获取所有命令
   */
  getCommands(): VoiceCommand[] {
    return Array.from(this.commands.values())
  }

  /**
   * 清空历史记录
   */
  clearHistory(): void {
    this.history.value = []
  }

  /**
   * 最终结果回调
   */
  private onFinalResult(result: RecognitionResult): void {
    // 可以在这里添加自定义处理逻辑
    }

  /**
   * 检查支持状态
   */
  checkSupport(): boolean {
    return this.isSupported
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages(): string[] {
    return [
      'zh-CN', // 中文（简体）
      'zh-TW', // 中文（繁体）
      'en-US', // 英语（美国）
      'en-GB', // 英语（英国）
      'ja-JP', // 日语
      'ko-KR', // 韩语
      'es-ES', // 西班牙语
      'fr-FR', // 法语
      'de-DE', // 德语
      'ru-RU' // 俄语
    ]
  }
}

// 导出单例
export const voiceInput = VoiceInputManager.getInstance()

// 导出Vue组合式API
export function useVoiceInput() {
  const manager = voiceInput

  const isSupported = computed(() => manager.checkSupport())

  const startListening = () => manager.start()
  const stopListening = () => manager.stop()
  const abortListening = () => manager.abort()

  return {
    // 状态
    isSupported,
    isListening: manager.isListening,
    isProcessing: manager.isProcessing,
    currentTranscript: manager.currentTranscript,
    finalTranscript: manager.finalTranscript,
    error: manager.error,
    history: manager.history,

    // 方法
    startListening,
    stopListening,
    abortListening,
    updateConfig: (config: Partial<VoiceInputConfig>) => manager.updateConfig(config),
    registerCommand: (command: VoiceCommand) => manager.registerCommand(command),
    clearHistory: () => manager.clearHistory(),
    getSupportedLanguages: () => manager.getSupportedLanguages()
  }
}
