 

/**
 * VoiceAnnouncer 单元测试
 * @description 自动生成的测试文件
 */

import { describe, it, expect } from 'vitest'
import { useVoiceAnnouncer } from '../VoiceAnnouncer'
describe('useVoiceAnnouncer', () => {
  it('应该被正确导出', () => {
    expect(useVoiceAnnouncer).toBeDefined()
    expect(typeof useVoiceAnnouncer).toBe('function')
  })

  it('应该正确处理正常输入', () => {
    const result = useVoiceAnnouncer()
    expect(result).toBeDefined()
  })

  it('应该正确处理空值', () => {
    const result = useVoiceAnnouncer()
    expect(result).toBeDefined()
  })
})
