import type {
  AIAnalysisConfig,
  ModelConfig,
  PredictionResult,
  AnomalyResult,
  PredictionInput,
  FeatureConfig
} from '@/types/ai'
import { ModelManager } from './ModelManager'
import { DataProcessor } from './DataProcessor'
import { InferenceEngine } from './InferenceEngine'
import { ResultInterpreter } from './ResultInterpreter'
import { AIPerformanceMonitor } from './AIPerformanceMonitor'

export class AIEngine {
  private config: AIAnalysisConfig
  private modelManager: ModelManager
  private dataProcessor: DataProcessor
  private inferenceEngine: InferenceEngine
  private interpreter: ResultInterpreter
  private monitor: AIPerformanceMonitor
  private worker: Worker | null = null
  private initialized = false

  constructor(config: AIAnalysisConfig) {
    this.config = config
    this.modelManager = new ModelManager()
    this.dataProcessor = new DataProcessor(config.features)
    this.inferenceEngine = new InferenceEngine()
    this.interpreter = new ResultInterpreter()
    this.monitor = new AIPerformanceMonitor()

    // 初始化WebWorker
    if (config.useWebWorker && typeof Worker !== 'undefined') {
      this.worker = new Worker(new URL('./ai.worker.ts', import.meta.url), { type: 'module' })
    }
  }

  /**
   * 初始化AI引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // 加载所有配置的模型
      await this.modelManager.loadModels(this.config.models)

      // 初始化推理引擎
      await this.inferenceEngine.initialize()

      // 预热模型
      if (this.config.warmUp) {
        await this.warmUpModels()
      }

      this.initialized = true
    } catch (error) {
      throw error
    }
  }

  /**
   * 执行预测
   */
  async predict(modelId: string, input: PredictionInput): Promise<PredictionResult> {
    if (!this.initialized) {
      await this.initialize()
    }

    const startTime = performance.now()

    try {
      // 获取模型
      const model = this.modelManager.getModel(modelId)
      if (!model) {
        throw new Error(`Model ${modelId} not found`)
      }

      // 数据预处理
      const processedData = await this.dataProcessor.process(input, model.config)

      // 执行推理
      let prediction
      if (this.worker && this.config.useWebWorker) {
        prediction = await this.runInWorker('predict', {
          modelId,
          data: processedData
        })
      } else {
        prediction = await this.inferenceEngine.run(model, processedData)
      }

      // 解释结果
      const result = await this.interpreter.interpret(prediction, model.config, input)

      // 记录性能
      const duration = performance.now() - startTime
      this.monitor.trackInference(modelId, duration)

      return result
    } catch (error) {
      throw error
    }
  }

  /**
   * 批量预测
   */
  async batchPredict(modelId: string, inputs: PredictionInput[]): Promise<PredictionResult[]> {
    if (!this.initialized) {
      await this.initialize()
    }

    const model = this.modelManager.getModel(modelId)
    if (!model) {
      throw new Error(`Model ${modelId} not found`)
    }

    // 批量处理数据
    const processedBatch = await Promise.all(
      inputs.map(input => this.dataProcessor.process(input, model.config))
    )

    // 批量推理
    const predictions = await this.inferenceEngine.runBatch(model, processedBatch)

    // 批量解释
    return Promise.all(
      predictions.map((pred, index) =>
        this.interpreter.interpret(pred, model.config, inputs[index])
      )
    )
  }

  /**
   * 异常检测
   */
  async detectAnomalies(
    data: Record<string, unknown>[],
    type: string,
    options?: {
      threshold?: number
      method?: 'isolation' | 'statistical' | 'clustering'
    }
  ): Promise<AnomalyResult[]> {
    if (!this.initialized) {
      await this.initialize()
    }

    const startTime = performance.now()

    try {
      // 选择检测方法
      const method = options?.method || 'statistical'
      const threshold = options?.threshold || this.config.thresholds?.[type] || 0.95

      // 执行异常检测
      let anomalies
      if (this.worker && this.config.useWebWorker) {
        anomalies = await this.runInWorker('detectAnomalies', {
          data,
          type,
          method,
          threshold
        })
      } else {
        anomalies = await this.inferenceEngine.detectAnomalies(data, method, threshold)
      }

      // 解释异常结果
      const results = await this.interpreter.interpretAnomalies(anomalies, type, data)

      // 记录性能
      const duration = performance.now() - startTime
      this.monitor.trackAnomalyDetection(type, duration, results.length)

      return results
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取模型解释
   */
  async explainPrediction(
    modelId: string,
    input: PredictionInput,
    prediction: PredictionResult
  ): Promise<{
    features: Array<{
      name: string
      importance: number
      value: string | number | boolean
      contribution: number
    }>
    visualization?: Record<string, unknown>
  }> {
    const model = this.modelManager.getModel(modelId)
    if (!model) {
      throw new Error(`Model ${modelId} not found`)
    }

    return this.interpreter.explainPrediction(model, input, prediction)
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    return this.monitor.getMetrics()
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.modelManager.dispose()
    this.inferenceEngine.dispose()

    if (this.worker) {
      this.worker.terminate()
      this.worker = null
    }

    this.initialized = false
  }

  /**
   * 预热模型
   */
  private async warmUpModels(): Promise<void> {
    const models = this.modelManager.getAllModels()

    for (const model of models) {
      try {
        // 使用虚拟数据预热
        const dummyInput = this.generateDummyInput(model.config)
        const processedData = await this.dataProcessor.process(dummyInput, model.config)
        await this.inferenceEngine.run(model, processedData)
      } catch (error) {
        console.warn(`Failed to warm up model ${model.id}:`, error)
      }
    }
  }

  /**
   * 生成虚拟输入数据
   */
  private generateDummyInput(config: ModelConfig): PredictionInput {
    const input: Record<string, number> = {}

    // 根据模型输入形状生成数据
    if (config.inputShape) {
      const [features] = config.inputShape
      for (let i = 0; i < features; i++) {
        input[`feature_${i}`] = Math.random()
      }
    }

    return input
  }

  /**
   * 在Worker中运行任务
   */
  private runInWorker<T = unknown>(type: string, data: unknown): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      if (!this.worker) {
        reject(new Error('Worker not initialized'))
        return
      }

      const messageHandler = (event: MessageEvent) => {
        if (event.data.type === type) {
          this.worker!.removeEventListener('message', messageHandler)
          if (event.data.error) {
            reject(new Error(event.data.error))
          } else {
            resolve(event.data.result as T)
          }
        }
      }

      this.worker.addEventListener('message', messageHandler)
      this.worker.postMessage({ type, data })
    })
  }
}
