/**
 * 自然语言查询解析引擎
 * 用于解析用户的自然语言查询，识别意图和提取实体
 */

export interface QueryIntent {
  type: string          // 意图类型
  confidence: number    // 置信度
  entities: QueryEntity[] // 提取的实体
  timeRange?: DateRange // 时间范围
  filters?: QueryFilter[] // 过滤条件
}

export interface QueryEntity {
  type: string      // 实体类型：department/metric/person/time等
  value: string     // 实体值
  original: string  // 原始文本
  position: [number, number] // 在原文中的位置
}

export interface DateRange {
  start?: Date
  end?: Date
  preset?: string // 预设时间范围：lastMonth/lastQuarter/lastYear等
}

export interface QueryFilter {
  field: string
  operator: string
   
  value: unknown
}

export class QueryParser {
  private patterns: Map<string, RegExp[]>
  private entityPatterns: Map<string, RegExp[]>
  private timePatterns: Map<string, RegExp>
  
  constructor() {
    this.patterns = this.initializePatterns()
    this.entityPatterns = this.initializeEntityPatterns()
    this.timePatterns = this.initializeTimePatterns()
  }
  
  /**
   * 解析查询文本
   */
  async parse(query: string): Promise<QueryIntent> {
    // 预处理
    const normalizedQuery = this.normalize(query)
    
    // 意图识别
    const intent = this.identifyIntent(normalizedQuery)
    
    // 实体提取
    const entities = this.extractEntities(normalizedQuery)
    
    // 时间解析
    const timeRange = this.parseTimeRange(normalizedQuery)
    
    // 条件提取
    const filters = this.extractFilters(normalizedQuery, entities)
    
    return {
      type: intent.type,
      confidence: intent.confidence,
      entities,
      timeRange,
      filters
    }
  }
  
  /**
   * 初始化意图模式
   */
  private initializePatterns(): Map<string, RegExp[]> {
    const patterns = new Map<string, RegExp[]>()
    
    // 查询类意图
    patterns.set('query.turnover', [
      /离职率|离职|流失/,
      /辞职|离开公司/
    ])
    
    patterns.set('query.salary', [
      /薪资|工资|薪酬|收入/,
      /平均.*工资|工资.*平均/
    ])
    
    patterns.set('query.performance', [
      /绩效|考核|评分|表现/,
      /优秀|良好|合格/
    ])
    
    patterns.set('query.attendance', [
      /考勤|出勤|打卡|签到/,
      /迟到|早退|旷工|请假/
    ])
    
    patterns.set('query.employee', [
      /员工|人员|职工|人数/,
      /多少人|几个人|总人数/
    ])
    
    patterns.set('query.overtime', [
      /加班|超时|加点/,
      /加班时长|加班统计/
    ])
    
    patterns.set('query.contract', [
      /合同|劳动合同|协议/,
      /到期|续签|终止/
    ])
    
    patterns.set('query.recruitment', [
      /招聘|招人|招募/,
      /入职|新员工|新人/
    ])
    
    // 分析类意图
    patterns.set('analysis.trend', [
      /趋势|变化|走势/,
      /增长|下降|波动/
    ])
    
    patterns.set('analysis.compare', [
      /对比|比较|相比/,
      /高于|低于|超过/
    ])
    
    patterns.set('analysis.distribution', [
      /分布|分配|构成/,
      /占比|比例|结构/
    ])
    
    return patterns
  }
  
  /**
   * 初始化实体模式
   */
  private initializeEntityPatterns(): Map<string, RegExp[]> {
    const patterns = new Map<string, RegExp[]>()
    
    // 部门实体
    patterns.set('department', [
      /技术部|研发部|开发部/,
      /销售部|市场部|营销部/,
      /人事部|人力资源部|HR/,
      /财务部|财务中心/,
      /行政部|后勤部/,
      /产品部|产品中心/
    ])
    
    // 指标实体
    patterns.set('metric', [
      /离职率|流失率/,
      /平均薪资|薪资水平/,
      /绩效评分|绩效等级/,
      /出勤率|考勤率/,
      /加班时长|加班小时/
    ])
    
    // 时间实体
    patterns.set('time', [
      /今天|昨天|前天/,
      /本周|上周|下周/,
      /本月|上月|上个月|下月/,
      /本季度|上季度|第[一二三四]季度/,
      /今年|去年|前年/,
      /\d{4}年\d{1,2}月/,
      /\d{1,2}月份?/
    ])
    
    // 数量实体
    patterns.set('number', [
      /\d+人|\d+名|\d+位/,
      /\d+%|\d+个百分点/,
      /\d+万|\d+千|\d+百/
    ])
    
    // 等级实体
    patterns.set('level', [
      /优秀|卓越|杰出/,
      /良好|合格|达标/,
      /一般|普通|中等/,
      /待改进|不合格|差/
    ])
    
    return patterns
  }
  
  /**
   * 初始化时间模式
   */
  private initializeTimePatterns(): Map<string, RegExp> {
    const patterns = new Map<string, RegExp>()
    
    patterns.set('today', /今天|今日/)
    patterns.set('yesterday', /昨天|昨日/)
    patterns.set('thisWeek', /本周|这周|这个星期/)
    patterns.set('lastWeek', /上周|上个星期/)
    patterns.set('thisMonth', /本月|这个月/)
    patterns.set('lastMonth', /上月|上个月/)
    patterns.set('thisQuarter', /本季度|这个季度/)
    patterns.set('lastQuarter', /上季度|上个季度/)
    patterns.set('thisYear', /今年|本年/)
    patterns.set('lastYear', /去年|上年/)
    
    return patterns
  }
  
  /**
   * 文本标准化
   */
  private normalize(query: string): string {
    return query
      .toLowerCase()
      .replace(/[？！。，、]/g, '')
      .replace(/\s+/g, ' ')
      .trim()
  }
  
  /**
   * 意图识别
   */
  private identifyIntent(query: string): { type: string; confidence: number } {
    let bestMatch = { type: 'unknown', confidence: 0 }
    
    for (const [intentType, patterns] of this.patterns) {
      let matchCount = 0
      
      for (const pattern of patterns) {
        if (pattern.test(query)) {
          matchCount++
        }
      }
      
      const confidence = matchCount / patterns.length
      
      if (confidence > bestMatch.confidence) {
        bestMatch = { type: intentType, confidence }
      }
    }
    
    // 如果置信度太低，尝试使用关键词匹配
    if (bestMatch.confidence < 0.3) {
      bestMatch = this.fallbackIntentMatching(query)
    }
    
    return bestMatch
  }
  
  /**
   * 降级意图匹配
   */
  private fallbackIntentMatching(query: string): { type: string; confidence: number } {
    const keywords = {
      'query.general': ['多少', '几个', '查询', '查看', '显示', '告诉我'],
      'analysis.general': ['分析', '统计', '报表', '数据']
    }
    
    for (const [intent, words] of Object.entries(keywords)) {
      for (const word of words) {
        if (query.includes(word)) {
          return { type: intent, confidence: 0.5 }
        }
      }
    }
    
    return { type: 'unknown', confidence: 0 }
  }
  
  /**
   * 实体提取
   */
  private extractEntities(query: string): QueryEntity[] {
    const entities: QueryEntity[] = []
    
    for (const [entityType, patterns] of this.entityPatterns) {
      for (const pattern of patterns) {
        const matches = query.matchAll(new RegExp(pattern, 'g'))
        
        for (const match of matches) {
          if (match.index !== undefined) {
            entities.push({
              type: entityType,
              value: this.normalizeEntityValue(match[0], entityType),
              original: match[0],
              position: [match.index, match.index + match[0].length]
            })
          }
        }
      }
    }
    
    // 去重
    return this.deduplicateEntities(entities)
  }
  
  /**
   * 实体值标准化
   */
  private normalizeEntityValue(value: string, type: string): string {
    const mappings: Record<string, Record<string, string>> = {
      department: {
        '技术部': 'tech',
        '研发部': 'tech',
        '开发部': 'tech',
        '销售部': 'sales',
        '市场部': 'sales',
        '营销部': 'sales',
        '人事部': 'hr',
        '人力资源部': 'hr',
        'hr': 'hr',
        '财务部': 'finance',
        '财务中心': 'finance',
        '行政部': 'admin',
        '后勤部': 'admin',
        '产品部': 'product',
        '产品中心': 'product'
      }
    }
    
    if (mappings[type] && mappings[type][value]) {
      return mappings[type][value]
    }
    
    return value
  }
  
  /**
   * 实体去重
   */
  private deduplicateEntities(entities: QueryEntity[]): QueryEntity[] {
    const seen = new Set<string>()
    return entities.filter(entity => {
      const key = `${entity.type}:${entity.value}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }
  
  /**
   * 解析时间范围
   */
  private parseTimeRange(query: string): DateRange | undefined {
    // 检查预设时间
    for (const [preset, pattern] of this.timePatterns) {
      if (pattern.test(query)) {
        return this.getPresetDateRange(preset)
      }
    }
    
    // 检查具体日期
    const datePattern = /(\d{4})年(\d{1,2})月/g
    const dateMatch = datePattern.exec(query)
    if (dateMatch) {
      const year = parseInt(dateMatch[1])
      const month = parseInt(dateMatch[2]) - 1
      return {
        start: new Date(year, month, 1),
        end: new Date(year, month + 1, 0)
      }
    }
    
    // 检查时间段
    const rangePattern = /从(.+)到(.+)/
    const rangeMatch = rangePattern.exec(query)
    if (rangeMatch) {
      // 这里需要更复杂的日期解析逻辑
    }
    
    return undefined
  }
  
  /**
   * 获取预设时间范围
   */
  private getPresetDateRange(preset: string): DateRange {
    const now = new Date()
    const ranges: Record<string, DateRange> = {
      today: {
        start: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
        end: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59),
        preset: 'today'
  },
      yesterday: {
        start: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1),
        end: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59),
        preset: 'yesterday'
  },
      thisMonth: {
        start: new Date(now.getFullYear(), now.getMonth(), 1),
        end: new Date(now.getFullYear(), now.getMonth() + 1, 0),
        preset: 'thisMonth'
  },
      lastMonth: {
        start: new Date(now.getFullYear(), now.getMonth() - 1, 1),
        end: new Date(now.getFullYear(), now.getMonth(), 0),
        preset: 'lastMonth'
  },
      thisYear: {
        start: new Date(now.getFullYear(), 0, 1),
        end: new Date(now.getFullYear(), 11, 31),
        preset: 'thisYear'
  },
      lastYear: {
        start: new Date(now.getFullYear() - 1, 0, 1),
        end: new Date(now.getFullYear() - 1, 11, 31),
        preset: 'lastYear'
      }
    }
    
    return ranges[preset] || ranges.thisMonth
  }
  
  /**
   * 提取过滤条件
   */
  private extractFilters(query: string, entities: QueryEntity[]): QueryFilter[] {
    const filters: QueryFilter[] = []
    
    // 基于实体构建过滤条件
    for (const entity of entities) {
      if (entity.type === 'department') {
        filters.push({
          field: 'department',
          operator: '=',
          value: entity.value
        })
      }
      
      if (entity.type === 'level') {
        filters.push({
          field: 'level',
          operator: '=',
          value: entity.value
        })
      }
    }
    
    // 提取比较条件
    const comparePatterns = [
      { pattern: /大于(\d+)/, operator: '>'
  },
      { pattern: /小于(\d+)/, operator: '<'
  },
      { pattern: /超过(\d+)/, operator: '>'
  },
      { pattern: /低于(\d+)/, operator: '<'
  },
      { pattern: /等于(\d+)/, operator: '=' }
    ]
    
    for (const { pattern, operator } of comparePatterns) {
      const match = pattern.exec(query)
      if (match) {
        // 需要根据上下文确定field
        const contextField = this.inferFieldFromContext(query, match.index!)
        if (contextField) {
          filters.push({
            field: contextField,
            operator,
            value: parseInt(match[1])
          })
        }
      }
    }
    
    return filters
  }
  
  /**
   * 从上下文推断字段
   */
  private inferFieldFromContext(query: string, position: number): string | null {
    // 在比较词前查找可能的字段名
    const before = query.substring(Math.max(0, position - 20), position)
    
    const fieldMappings: Record<string, string> = {
      '薪资': 'salary',
      '工资': 'salary',
      '年龄': 'age',
      '工龄': 'tenure',
      '绩效': 'performance',
      '评分': 'score',
      '人数': 'count'
    }
    
    for (const [keyword, field] of Object.entries(fieldMappings)) {
      if (before.includes(keyword)) {
        return field
      }
    }
    
    return null
  }
  
  /**
   * 生成查询SQL（示例）
   */
  generateSQL(intent: QueryIntent): string {
    // 这里可以根据解析结果生成相应的SQL查询
    // 实际应用中应该使用参数化查询防止SQL注入
    let sql = 'SELECT * FROM employees'
    const conditions: string[] = []
    
    if (intent.filters && intent.filters.length > 0) {
      for (const filter of intent.filters) {
        conditions.push(`${filter.field} ${filter.operator} '${filter.value}'`)
      }
    }
    
    if (intent.timeRange) {
      if (intent.timeRange.start) {
        conditions.push(`date >= '${intent.timeRange.start.toISOString()}'`)
      }
      if (intent.timeRange.end) {
        conditions.push(`date <= '${intent.timeRange.end.toISOString()}'`)
      }
    }
    
    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ')
    }
    
    return sql
  }
}

// 单例导出
export const queryParser = new QueryParser()