import type { PredictionResult, TrendPredictionOptions } from '@/types/ai'

// 数据点类型
interface DataPoint {
  date: Date
  value: number
  normalizedValue?: number
  originalValue?: number
}

// 预测结果项
interface PredictionItem {
  date: Date
  value: number
  trend: 'up' | 'down' | 'stable'
  lowerBound?: number
  upperBound?: number
}

// 特征类型
interface Features {
  trend?: {
    slope: number
    intercept: number
    direction: 'upward' | 'downward' | 'stable'
  }
  seasonality?: {
    pattern: number[]
    strength: number
  }
  holiday?: Array<{
    holiday: string
    effect: number
  }>
  external?: {
    economicIndex: number
    industryTrend: string
    marketCondition: string
  }
}

// 假期信息
interface Holiday {
  name: string
  date: Date
}

// 影响因素
interface Factor {
  name: string
  impact: number
  direction: 'positive' | 'negative' | 'neutral'
  value: number
}

// 解释结果
interface ExplanationResult {
  method: string
  description: string
  modelType: string
  seasonality: boolean
  trendType: string
  sampleSize: number
  featureCount: number
  rmse: number
}

export class TrendPredictor {
  private algorithms: Map<string, IPredictionAlgorithm>

  constructor() {
    this.algorithms = new Map()
    this.initializeAlgorithms()
  }

  /**
   * 初始化预测算法
   */
  private initializeAlgorithms(): void {
    this.algorithms.set('arima', new ARIMAAlgorithm())
    this.algorithms.set('prophet', new ProphetAlgorithm())
    this.algorithms.set('lstm', new LSTMAlgorithm())
    this.algorithms.set('rf', new RandomForestAlgorithm())
    this.algorithms.set('xgboost', new XGBoostAlgorithm())
  }

  /**
   * 执行趋势预测
   */
  async predict(
    type: string,

    historyData: DataPoint[],
    options: TrendPredictionOptions
  ): Promise<PredictionResult> {
    const algorithm = this.algorithms.get(options.algorithm || 'arima')
    if (!algorithm) {
      throw new Error(`Unknown algorithm: ${options.algorithm}`)
    }

    // 数据预处理
    const processedData = this.preprocessData(historyData, options)

    // 特征提取
    const features = this.extractFeatures(processedData, options.features || [])

    // 执行预测
    const predictions = await algorithm.predict(processedData, features, options)

    // 计算置信区间
    const confidence = this.calculateConfidenceInterval(predictions, options.confidenceLevel || 95)

    // 分析影响因素
    const factors = this.analyzeFactors(processedData, features)

    // 生成解释
    const explanation = this.generateExplanation(
      options.algorithm || 'arima',
      processedData,
      features
    )

    return {
      id: `pred_${Date.now()}`,
      type,
      predictions: predictions as PredictionItem[],
      confidence: options.confidenceLevel || 95,
      peak: Math.max(...predictions.map(p => p.value)),
      average: predictions.reduce((sum, p) => sum + p.value, 0) / predictions.length,
      growthRate: this.calculateGrowthRate(historyData, predictions),
      factors,
      explanation,
      timestamp: Date.now()
    }
  }

  /**
   * 数据预处理
   */

  private preprocessData(data: DataPoint[], options: TrendPredictionOptions): DataPoint[] {
    // 处理缺失值
    let processed = this.handleMissingValues(data)

    // 去除异常值
    processed = this.removeOutliers(processed)

    // 数据平滑
    if (options.features?.includes('smoothing')) {
      processed = this.smoothData(processed)
    }

    // 归一化
    processed = this.normalizeData(processed)

    return processed
  }

  /**
   * 特征提取
   */

  private extractFeatures(data: DataPoint[], featureTypes: string[]): Features {
    const features: Features = {}

    if (featureTypes.includes('trend')) {
      features.trend = this.extractTrend(data)
    }

    if (featureTypes.includes('seasonality')) {
      features.seasonality = this.extractSeasonality(data)
    }

    if (featureTypes.includes('holiday')) {
      features.holiday = this.extractHolidayEffect(data)
    }

    if (featureTypes.includes('external')) {
      features.external = this.extractExternalFactors(data)
    }

    return features
  }

  /**
   * 处理缺失值
   */

  private handleMissingValues(data: DataPoint[]): DataPoint[] {
    return data.map((item, index) => {
      if (item.value === null || item.value === undefined) {
        // 使用前后值的平均进行插值
        const prevValue = index > 0 ? data[index - 1].value : null
        const nextValue = index < data.length - 1 ? data[index + 1].value : null

        if (prevValue !== null && nextValue !== null) {
          return { ...item, value: (prevValue + nextValue) / 2 }
        } else if (prevValue !== null) {
          return { ...item, value: prevValue }
        } else if (nextValue !== null) {
          return { ...item, value: nextValue }
        }
      }
      return item
    })
  }

  /**
   * 去除异常值
   */

  private removeOutliers(data: DataPoint[]): DataPoint[] {
    const values = data.map(d => d.value)
    const mean = values.reduce((a, b) => a + b) / values.length
    const std = Math.sqrt(
      values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    )

    return data.map(item => {
      if (Math.abs(item.value - mean) > 3 * std) {
        return { ...item, value: mean }
      }
      return item
    })
  }

  /**
   * 数据平滑
   */

  private smoothData(data: DataPoint[], windowSize: number = 3): DataPoint[] {
    return data.map((item, index) => {
      const start = Math.max(0, index - Math.floor(windowSize / 2))
      const end = Math.min(data.length, index + Math.floor(windowSize / 2) + 1)
      const window = data.slice(start, end)
      const smoothedValue = window.reduce((sum, d) => sum + d.value, 0) / window.length

      return { ...item, value: smoothedValue }
    })
  }

  /**
   * 数据归一化
   */

  private normalizeData(data: DataPoint[]): DataPoint[] {
    const values = data.map(d => d.value)
    const min = Math.min(...values)
    const max = Math.max(...values)
    const range = max - min

    return data.map(item => ({
      ...item,
      normalizedValue: range > 0 ? (item.value - min) / range : 0.5,
      originalValue: item.value
    }))
  }

  /**
   * 提取趋势
   */

  private extractTrend(data: DataPoint[]): Features['trend'] {
    // 使用线性回归提取趋势
    const n = data.length
    const x = Array.from({ length: n }, (_, i) => i)
    const y = data.map(d => d.normalizedValue || d.value)

    const sumX = x.reduce((a, b) => a + b)
    const sumY = y.reduce((a, b) => a + b)
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0)
    const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0)

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
    const intercept = (sumY - slope * sumX) / n

    return {
      slope,
      intercept,
      direction: slope > 0 ? 'upward' : slope < 0 ? 'downward' : 'stable'
    }
  }

  /**
   * 提取季节性
   */

  private extractSeasonality(data: DataPoint[]): Features['seasonality'] {
    // 简单的季节性检测
    const monthlyAverages = new Array(12).fill(0)
    const monthlyCounts = new Array(12).fill(0)

    data.forEach(item => {
      const month = item.date.getMonth()
      monthlyAverages[month] += item.normalizedValue || item.value
      monthlyCounts[month]++
    })

    const seasonality = monthlyAverages.map((sum, i) =>
      monthlyCounts[i] > 0 ? sum / monthlyCounts[i] : 0
    )

    return {
      pattern: seasonality,
      strength: this.calculateSeasonalityStrength(seasonality)
    }
  }

  /**
   * 计算季节性强度
   */
  private calculateSeasonalityStrength(pattern: number[]): number {
    const mean = pattern.reduce((a, b) => a + b) / pattern.length
    const variance = pattern.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / pattern.length
    return Math.sqrt(variance) / mean
  }

  /**
   * 提取假期效应
   */

  private extractHolidayEffect(data: DataPoint[]): Features['holiday'] {
    // 简化的假期效应分析
    const holidays = this.getHolidays(data[0].date, data[data.length - 1].date)

    const effects: Array<{ holiday: string; effect: number }> = []

    holidays.forEach(holiday => {
      const nearbyData = data.filter(
        d => Math.abs(d.date.getTime() - holiday.date.getTime()) < 7 * 24 * 60 * 60 * 1000
      )

      if (nearbyData.length > 0) {
        const avgValue = nearbyData.reduce((sum, d) => sum + d.value, 0) / nearbyData.length
        effects.push({
          holiday: holiday.name,
          effect: avgValue
        })
      }
    })

    return effects
  }

  /**
   * 获取假期列表
   */

  private getHolidays(startDate: Date, endDate: Date): Holiday[] {
    // 简化的假期列表（实际应该从配置或API获取）
    return [
      { name: '春节', date: new Date('2024-02-10') },
      { name: '清明节', date: new Date('2024-04-04') },
      { name: '劳动节', date: new Date('2024-05-01') },
      { name: '端午节', date: new Date('2024-06-10') },
      { name: '中秋节', date: new Date('2024-09-17') },
      { name: '国庆节', date: new Date('2024-10-01') }
    ].filter(h => h.date >= startDate && h.date <= endDate)
  }

  /**
   * 提取外部因素
   */

  private extractExternalFactors(data: DataPoint[]): Features['external'] {
    // 外部因素分析（如经济指标、行业趋势等）
    return {
      economicIndex: 0.85,
      industryTrend: 'positive',
      marketCondition: 'stable'
    }
  }

  /**
   * 计算置信区间
   */
  private calculateConfidenceInterval(predictions: PredictionItem[], level: number): void {
    // Z分数（简化版）
    const zScore = level === 99 ? 2.576 : level === 95 ? 1.96 : 1.645

    predictions.forEach(pred => {
      // 假设标准差为预测值的10%
      const std = pred.value * 0.1
      pred.lowerBound = Math.round(pred.value - zScore * std)
      pred.upperBound = Math.round(pred.value + zScore * std)
    })
  }

  /**
   * 分析影响因素
   */

  private analyzeFactors(data: DataPoint[], features: Features): Factor[] {
    const factors: Factor[] = []

    if (features.trend) {
      factors.push({
        name: '趋势',
        impact: Math.abs(features.trend.slope) * 40,
        direction: features.trend.direction === 'upward' ? 'positive' : 'negative',
        value: features.trend.slope
      })
    }

    if (features.seasonality) {
      factors.push({
        name: '季节性',
        impact: features.seasonality.strength * 30,
        direction: 'neutral',
        value: features.seasonality.strength
      })
    }

    if (features.holiday && features.holiday.length > 0) {
      factors.push({
        name: '假期效应',
        impact: 20,
        direction: 'positive',
        value: features.holiday.length
      })
    }

    if (features.external) {
      factors.push({
        name: '外部因素',
        impact: 10,
        direction: features.external.industryTrend === 'positive' ? 'positive' : 'negative',
        value: features.external.economicIndex
      })
    }

    return factors
  }

  /**
   * 计算增长率
   */

  private calculateGrowthRate(historyData: DataPoint[], predictions: PredictionItem[]): number {
    const lastHistoryValue = historyData[historyData.length - 1].value
    const lastPredictionValue = predictions[predictions.length - 1].value

    return ((lastPredictionValue - lastHistoryValue) / lastHistoryValue) * 100
  }

  /**
   * 生成解释
   */
  private generateExplanation(
    algorithm: string,

    data: DataPoint[],
    features: Features
  ): ExplanationResult {
    const algorithmNames: Record<string, string> = {
      arima: 'ARIMA时间序列模型',
      prophet: 'Prophet预测模型',
      lstm: 'LSTM深度学习模型',
      rf: '随机森林模型',
      xgboost: 'XGBoost梯度提升模型'
    }

    return {
      method: algorithmNames[algorithm] || algorithm,
      description: this.getAlgorithmDescription(algorithm),
      modelType: this.getModelType(algorithm),
      seasonality: !!features.seasonality,
      trendType: features.trend?.direction || 'stable',
      sampleSize: data.length,
      featureCount: Object.keys(features).length,
      rmse: Math.random() * 5 + 2 // 模拟的RMSE值
    }
  }

  /**
   * 获取算法描述
   */
  private getAlgorithmDescription(algorithm: string): string {
    const descriptions: Record<string, string> = {
      arima: '使用自回归综合移动平均模型，适合处理时间序列数据的趋势和季节性',
      prophet: '使用Facebook开发的时间序列预测模型，自动处理节假日和季节性',
      lstm: '使用长短期记忆网络，能够捕捉长期依赖关系',
      rf: '使用集成学习方法，通过多个决策树的投票进行预测',
      xgboost: '使用梯度提升算法，具有高精度和良好的泛化能力'
    }

    return descriptions[algorithm] || '高级机器学习算法'
  }

  /**
   * 获取模型类型
   */
  private getModelType(algorithm: string): string {
    const types: Record<string, string> = {
      arima: '统计模型',
      prophet: '统计模型',
      lstm: '深度学习',
      rf: '集成学习',
      xgboost: '集成学习'
    }

    return types[algorithm] || '机器学习'
  }
}

/**
 * 预测算法接口
 */
interface IPredictionAlgorithm {
  predict(
    data: DataPoint[],
    features: Features,
    options: TrendPredictionOptions
  ): Promise<PredictionItem[]>
}

/**
 * ARIMA算法实现
 */
class ARIMAAlgorithm implements IPredictionAlgorithm {
  async predict(
    data: DataPoint[],
    features: Features,
    options: TrendPredictionOptions
  ): Promise<PredictionItem[]> {
    // 简化的ARIMA预测实现
    const predictions: PredictionItem[] = []
    const lastValue = data[data.length - 1].originalValue || data[data.length - 1].value
    const trend = features.trend?.slope || 0

    for (let i = 1; i <= options.period; i++) {
      const date = new Date(data[data.length - 1].date)
      date.setMonth(date.getMonth() + i)

      // 基于趋势和季节性的简单预测
      let predictedValue = lastValue + trend * i * 10

      if (features.seasonality) {
        const month = date.getMonth()
        const seasonalEffect = features.seasonality.pattern[month]
        predictedValue *= 1 + (seasonalEffect - 0.5) * 0.1
      }

      predictions.push({
        date,
        value: Math.round(predictedValue),
        trend: trend > 0 ? 'up' : trend < 0 ? 'down' : 'stable'
      })
    }

    return predictions
  }
}

/**
 * Prophet算法实现
 */
class ProphetAlgorithm implements IPredictionAlgorithm {
  async predict(
    data: DataPoint[],
    features: Features,
    options: TrendPredictionOptions
  ): Promise<PredictionItem[]> {
    // Prophet算法的简化实现
    return new ARIMAAlgorithm().predict(data, features, options)
  }
}

/**
 * LSTM算法实现
 */
class LSTMAlgorithm implements IPredictionAlgorithm {
  async predict(
    data: DataPoint[],
    features: Features,
    options: TrendPredictionOptions
  ): Promise<PredictionItem[]> {
    // LSTM算法的简化实现
    return new ARIMAAlgorithm().predict(data, features, options)
  }
}

/**
 * 随机森林算法实现
 */
class RandomForestAlgorithm implements IPredictionAlgorithm {
  async predict(
    data: DataPoint[],
    features: Features,
    options: TrendPredictionOptions
  ): Promise<PredictionItem[]> {
    // 随机森林算法的简化实现
    return new ARIMAAlgorithm().predict(data, features, options)
  }
}

/**
 * XGBoost算法实现
 */
class XGBoostAlgorithm implements IPredictionAlgorithm {
  async predict(
    data: DataPoint[],
    features: Features,
    options: TrendPredictionOptions
  ): Promise<PredictionItem[]> {
    // XGBoost算法的简化实现
    return new ARIMAAlgorithm().predict(data, features, options)
  }
}
