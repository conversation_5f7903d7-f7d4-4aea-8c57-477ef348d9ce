/**
 * 移动端性能优化工具
 */

// 图片懒加载配置
export const lazyLoadConfig = {
  // 预加载阈值（像素）
  threshold: 100,
  // 根边距
  rootMargin: '50px',
  // 加载占位图
  placeholder:
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5加载中...</text></svg>'
}

// 虚拟滚动配置
export const virtualScrollConfig = {
  // 默认项目高度
  itemHeight: 60,
  // 缓冲区大小
  bufferSize: 5,
  // 最小渲染数量
  minRenderCount: 10
}

// 防抖延迟配置
export const debounceConfig = {
  search: 300,
  resize: 100,
  scroll: 16,
  input: 200
}

// 节流延迟配置
export const throttleConfig = {
  scroll: 16,
  resize: 100,
  touchmove: 16
}

/**
 * 图片懒加载实现
 */
export class LazyImageLoader {
  private observer: IntersectionObserver
  private images: Set<HTMLImageElement> = new Set()

  constructor(config = lazyLoadConfig) {
    this.observer = new IntersectionObserver(
      _entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            this.loadImage(img)
          }
        })
      },
      {
        threshold: 0.1,
        rootMargin: config.rootMargin
      }
    )
  }

  // 观察图片
  observe(img: HTMLImageElement, src: string) {
    img.dataset.src = src
    img.src = lazyLoadConfig.placeholder
    this.images.add(img)
    this.observer.observe(img)
  }

  // 加载图片
  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src
    if (src) {
      const newImg = new Image()
      newImg.onload = () => {
        img.src = src
        img.classList.add('loaded')
      }
      newImg.onerror = () => {
        img.classList.add('error')
      }
      newImg.src = src
    }
    this.observer.unobserve(img)
    this.images.delete(img)
  }

  // 销毁
  destroy() {
    this.observer.disconnect()
    this.images.clear()
  }
}

/**
 * 虚拟滚动实现
 */
export class VirtualScroller<T = unknown> {
  private container: HTMLElement
  private items: T[]
  private itemHeight: number
  private visibleCount: number
  private startIndex = 0
  private endIndex = 0
  private scrollTop = 0

  constructor(container: HTMLElement, items: T[], itemHeight = virtualScrollConfig.itemHeight) {
    this.container = container
    this.items = items
    this.itemHeight = itemHeight
    this.visibleCount =
      Math.ceil(container.clientHeight / itemHeight) + virtualScrollConfig.bufferSize

    this.updateVisibleRange()
    this.bindEvents()
  }

  // 更新可见范围
  private updateVisibleRange() {
    this.startIndex = Math.floor(this.scrollTop / this.itemHeight)
    this.endIndex = Math.min(this.startIndex + this.visibleCount, this.items.length)
  }

  // 绑定事件
  private bindEvents() {
    this.container.addEventListener('scroll', this.handleScroll.bind(this))
  }

  // 处理滚动
  private handleScroll() {
    this.scrollTop = this.container.scrollTop
    this.updateVisibleRange()
  }

  // 获取可见项目
  getVisibleItems(): { items: T[]; startIndex: number; endIndex: number; offsetY: number } {
    return {
      items: this.items.slice(this.startIndex, this.endIndex),
      startIndex: this.startIndex,
      endIndex: this.endIndex,
      offsetY: this.startIndex * this.itemHeight
    }
  }

  // 获取总高度
  getTotalHeight(): number {
    return this.items.length * this.itemHeight
  }

  // 更新数据
  updateItems(items: T[]) {
    this.items = items
    this.updateVisibleRange()
  }

  // 销毁
  destroy() {
    this.container.removeEventListener('scroll', this.handleScroll.bind(this))
  }
}

/**
 * 触摸手势识别
 */
export class TouchGestureRecognizer {
  private element: HTMLElement
  private startPoint: { x: number; y: number } = { x: 0, y: 0 }
  private endPoint: { x: number; y: number } = { x: 0, y: 0 }
  private startTime = 0
  private endTime = 0
  private minSwipeDistance = 50
  private maxSwipeTime = 300

  constructor(element: HTMLElement) {
    this.element = element
    this.bindEvents()
  }

  private bindEvents() {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this))
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this))
  }

  private handleTouchStart(event: TouchEvent) {
    const touch = event.touches[0]
    this.startPoint = { x: touch.clientX, y: touch.clientY }
    this.startTime = Date.now()
  }

  private handleTouchEnd(event: TouchEvent) {
    const touch = event.changedTouches[0]
    this.endPoint = { x: touch.clientX, y: touch.clientY }
    this.endTime = Date.now()

    this.recognizeGesture()
  }

  private recognizeGesture() {
    const deltaX = this.endPoint.x - this.startPoint.x
    const deltaY = this.endPoint.y - this.startPoint.y
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const duration = this.endTime - this.startTime

    // 点击
    if (distance < 10 && duration < 200) {
      this.onTap?.(this.startPoint)
      return
    }

    // 滑动
    if (distance >= this.minSwipeDistance && duration <= this.maxSwipeTime) {
      const direction = this.getSwipeDirection(deltaX, deltaY)
      this.onSwipe?.(direction, distance, duration)
      return
    }

    // 长按
    if (distance < 10 && duration >= 500) {
      this.onLongPress?.(this.startPoint)
      return
    }
  }

  private getSwipeDirection(deltaX: number, deltaY: number): 'left' | 'right' | 'up' | 'down' {
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      return deltaX > 0 ? 'right' : 'left'
    } else {
      return deltaY > 0 ? 'down' : 'up'
    }
  }

  // 事件回调
  onTap?: (point: { x: number; y: number }) => void
  onSwipe?: (
    direction: 'left' | 'right' | 'up' | 'down',
    distance: number,
    duration: number
  ) => void
  onLongPress?: (point: { x: number; y: number }) => void

  destroy() {
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this))
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this))
  }
}

/**
 * 移动端性能监控
 */
export class MobilePerformanceMonitor {
  private metrics: {
    fps: number[]
    memoryUsage: number[]
    loadTime: number
    renderTime: number[]
  } = {
    fps: [],
    memoryUsage: [],
    loadTime: 0,
    renderTime: []
  }

  private lastFrameTime = 0
  private frameCount = 0
  private isMonitoring = false

  // 开始监控
  start() {
    this.isMonitoring = true
    this.metrics.loadTime = performance.now()
    this.monitorFPS()
    this.monitorMemory()
  }

  // 停止监控
  stop() {
    this.isMonitoring = false
  }

  // 监控FPS
  private monitorFPS() {
    const measureFPS = (currentTime: number) => {
      if (!this.isMonitoring) return

      if (this.lastFrameTime) {
        const fps = 1000 / (currentTime - this.lastFrameTime)
        this.metrics.fps.push(fps)

        // 保持最近100帧的数据
        if (this.metrics.fps.length > 100) {
          this.metrics.fps.shift()
        }
      }

      this.lastFrameTime = currentTime
      requestAnimationFrame(measureFPS)
    }

    requestAnimationFrame(measureFPS)
  }

  // 监控内存使用
  private monitorMemory() {
    const measureMemory = () => {
      if (!this.isMonitoring) return

      if (performance.memory) {
        const memoryInfo = performance.memory
        this.metrics.memoryUsage.push(memoryInfo.usedJSHeapSize / 1024 / 1024) // MB

        // 保持最近50个数据点
        if (this.metrics.memoryUsage.length > 50) {
          this.metrics.memoryUsage.shift()
        }
      }

      setTimeout(measureMemory, 1000) // 每秒检查一次
    }

    measureMemory()
  }

  // 记录渲染时间
  recordRenderTime(startTime: number) {
    const renderTime = performance.now() - startTime
    this.metrics.renderTime.push(renderTime)

    if (this.metrics.renderTime.length > 100) {
      this.metrics.renderTime.shift()
    }
  }

  // 获取性能报告
  getReport() {
    const avgFPS =
      this.metrics.fps.length > 0
        ? this.metrics.fps.reduce((a, b) => a + b, 0) / this.metrics.fps.length
        : 0

    const avgMemory =
      this.metrics.memoryUsage.length > 0
        ? this.metrics.memoryUsage.reduce((a, b) => a + b, 0) / this.metrics.memoryUsage.length
        : 0

    const avgRenderTime =
      this.metrics.renderTime.length > 0
        ? this.metrics.renderTime.reduce((a, b) => a + b, 0) / this.metrics.renderTime.length
        : 0

    return {
      averageFPS: Math.round(avgFPS),
      averageMemoryUsage: Math.round(avgMemory * 100) / 100,
      averageRenderTime: Math.round(avgRenderTime * 100) / 100,
      totalLoadTime: Math.round(this.metrics.loadTime * 100) / 100,
      frameDrops: this.metrics.fps.filter(fps => fps < 30).length,
      memoryPeaks: Math.max(...this.metrics.memoryUsage),
      renderTimeP95: this.getPercentile(this.metrics.renderTime, 95)
    }
  }

  private getPercentile(arr: number[], percentile: number): number {
    const sorted = [...arr].sort((a, b) => a - b)
    const index = Math.ceil((percentile / 100) * sorted.length) - 1
    return Math.round((sorted[index] || 0) * 100) / 100
  }
}

/**
 * 移动端网络优化
 */
export class MobileNetworkOptimizer {
  private requestQueue: Array<() => Promise<unknown>> = []
  private isProcessing = false
  private maxConcurrent = 3
  private currentRequests = 0

  // 添加请求到队列
  addRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await requestFn()
          resolve(result)
        } catch (__error) {
          reject(error)
        }
      })

      this.processQueue()
    })
  }

  // 处理请求队列
  private async processQueue() {
    if (this.isProcessing || this.currentRequests >= this.maxConcurrent) {
      return
    }

    this.isProcessing = true

    while (this.requestQueue.length > 0 && this.currentRequests < this.maxConcurrent) {
      const request = this.requestQueue.shift()
      if (request) {
        this.currentRequests++
        request().finally(() => {
          this.currentRequests--
          this.processQueue()
        })
      }
    }

    this.isProcessing = false
  }

  // 获取网络状态
  getNetworkInfo() {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection

    if (connection) {
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      }
    }

    return null
  }

  // 根据网络状况调整策略
  optimizeForNetwork() {
    const networkInfo = this.getNetworkInfo()

    if (networkInfo) {
      // 慢网络时减少并发请求
      if (networkInfo.effectiveType === 'slow-2g' || networkInfo.effectiveType === '2g') {
        this.maxConcurrent = 1
      } else if (networkInfo.effectiveType === '3g') {
        this.maxConcurrent = 2
      } else {
        this.maxConcurrent = 3
      }

      // 省流量模式
      if (networkInfo.saveData) {
        this.maxConcurrent = Math.min(this.maxConcurrent, 2)
      }
    }
  }
}
