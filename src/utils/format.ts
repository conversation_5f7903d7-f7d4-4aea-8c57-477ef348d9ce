/**
 * 格式化工具函数
 */

import dayjs from 'dayjs'

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: string | Date | null | undefined, format = 'YYYY-MM-DD'): string {
  if (!date) return '-'
  return dayjs(date).format(format)
}

/**
 * 格式化日期时间
 * @param dateTime 日期时间字符串或Date对象
 * @param format 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(
  dateTime: string | Date | null | undefined,
  format = 'YYYY-MM-DD HH:mm:ss'
): string {
  if (!dateTime) return '-'
  return dayjs(dateTime).format(format)
}

/**
 * 格式化金额
 * @param amount 金额
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的金额字符串
 */
export function formatMoney(amount: number | string | null | undefined, decimals = 2): string {
  if (amount === null || amount === undefined || amount === '') return '-'

  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  if (isNaN(num)) return '-'

  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化百分比
 * @param value 数值
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的百分比字符串
 */
export function formatPercent(value: number | string | null | undefined, decimals = 2): string {
  if (value === null || value === undefined || value === '') return '-'

  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return '-'

  return `${(num * 100).toFixed(decimals)}%`
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
}

/**
 * 格式化手机号（隐藏中间4位）
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
export function formatPhone(phone: string | null | undefined): string {
  if (!phone) return '-'
  if (phone.length !== 11) return phone

  return `${phone.slice(0, 3)}****${phone.slice(7)}`
}

/**
 * 格式化身份证号（隐藏中间部分）
 * @param idNumber 身份证号
 * @returns 格式化后的身份证号
 */
export function formatIdNumber(idNumber: string | null | undefined): string {
  if (!idNumber) return '-'
  if (idNumber.length < 8) return idNumber

  const start = idNumber.slice(0, 4)
  const end = idNumber.slice(-4)
  const middle = '*'.repeat(idNumber.length - 8)

  return `${start}${middle}${end}`
}

/**
 * 截断文本
 * @param text 文本内容
 * @param maxLength 最大长度
 * @param suffix 后缀，默认为'...'
 * @returns 截断后的文本
 */
export function truncateText(
  text: string | null | undefined,
  maxLength: number,
  suffix = '...'
): string {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength - suffix.length) + suffix
}

/**
 * 计算时间段持续时间
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param unit 单位（days, hours, minutes, seconds）
 * @returns 持续时间
 */
export function calculateDuration(
  startTime: string | Date | null | undefined,
  endTime: string | Date | null | undefined,
  unit: 'days' | 'hours' | 'minutes' | 'seconds' = 'hours'
): number {
  if (!startTime || !endTime) return 0

  const start = dayjs(startTime)
  const end = dayjs(endTime)

  return end.diff(start, unit)
}

/**
 * 格式化时长
 * @param hours 小时数
 * @returns 格式化后的时长字符串
 */
export function formatDuration(hours: number): string {
  if (!hours || hours <= 0) return '-'

  if (hours < 1) {
    // 小于1小时，显示分钟
    const minutes = Math.round(hours * 60)
    return `${minutes}分钟`
  } else if (hours < 24) {
    // 小于24小时，显示小时
    return `${hours.toFixed(1)}小时`
  } else {
    // 大于等于24小时，显示天和小时
    const days = Math.floor(hours / 24)
    const restHours = hours % 24
    if (restHours === 0) {
      return `${days}天`
    } else {
      return `${days}天${restHours.toFixed(1)}小时`
    }
  }
}

/**
 * 格式化数字（千分位）
 * @param num 数字
 * @param decimals 小数位数
 * @returns 格式化后的数字字符串
 */
export function formatNumber(num: number | string | null | undefined, decimals = 0): string {
  if (num === null || num === undefined || num === '') return '-'

  const number = typeof num === 'string' ? parseFloat(num) : num
  if (isNaN(number)) return '-'

  return number.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化字节大小
 * @param bytes 字节数
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的大小字符串
 */
export function formatBytes(bytes: number | null | undefined, decimals = 2): string {
  if (bytes === null || bytes === undefined || bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}
