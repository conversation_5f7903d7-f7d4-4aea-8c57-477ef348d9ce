/**
 * @name logger
 * @description 统一日志管理工具
 * <AUTHOR> Assistant
 * @since 2024-01-01
 */

// 日志级别
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

// 日志配置
interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableRemote: boolean
  remoteUrl?: string
  prefix?: string
}

// 默认配置
const defaultConfig: LoggerConfig = {
  level: import.meta.env.PROD ? LogLevel.ERROR : LogLevel.DEBUG,
  enableConsole: !import.meta.env.PROD,
  enableRemote: import.meta.env.PROD,
  prefix: '[HR-System]'
}

class Logger {
  private config: LoggerConfig
  private logBuffer: unknown[] = []
  private readonly maxBufferSize = 100

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config }
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel) {
    this.config.level = level
  }

  /**
   * 调试日志
   */
  debug(...args: unknown[]) {
    if (this.config.level <= LogLevel.DEBUG) {
      this.log('debug', ...args)
    }
  }

  /**
   * 信息日志
   */
  info(...args: unknown[]) {
    if (this.config.level <= LogLevel.INFO) {
      this.log('info', ...args)
    }
  }

  /**
   * 警告日志
   */
  warn(...args: unknown[]) {
    if (this.config.level <= LogLevel.WARN) {
      this.log('warn', ...args)
    }
  }

  /**
   * 错误日志
   */
  error(...args: unknown[]) {
    if (this.config.level <= LogLevel.ERROR) {
      this.log('error', ...args)
    }
  }

  /**
   * 计时开始
   */
  time(label: string) {
    if (this.config.enableConsole && this.config.level <= LogLevel.DEBUG) {
      console.time(`${this.config.prefix} ${label}`)
    }
  }

  /**
   * 计时结束
   */
  timeEnd(label: string) {
    if (this.config.enableConsole && this.config.level <= LogLevel.DEBUG) {
      console.timeEnd(`${this.config.prefix} ${label}`)
    }
  }

  /**
   * 表格输出
   */
  table(data: unknown) {
    if (this.config.enableConsole && this.config.level <= LogLevel.DEBUG) {
      console.table(data)
    }
  }

  /**
   * 分组开始
   */
  group(label: string) {
    if (this.config.enableConsole && this.config.level <= LogLevel.DEBUG) {
      console.group(`${this.config.prefix} ${label}`)
    }
  }

  /**
   * 分组结束
   */
  groupEnd() {
    if (this.config.enableConsole && this.config.level <= LogLevel.DEBUG) {
      console.groupEnd()
    }
  }

  /**
   * 清空控制台
   */
  clear() {
    if (this.config.enableConsole) {
      console.clear()
    }
  }

  /**
   * 获取日志缓冲
   */
  getBuffer() {
    return [...this.logBuffer]
  }

  /**
   * 清空日志缓冲
   */
  clearBuffer() {
    this.logBuffer = []
  }

  /**
   * 内部日志方法
   */
  private log(level: keyof Console, ...args: unknown[]) {
    const timestamp = new Date().toISOString()
    const message = [
      `[${timestamp}]`,
      this.config.prefix,
      `[${level.toUpperCase()}]`,
      ...args
    ]

    // 控制台输出
    if (this.config.enableConsole) {
      const consoleFn = console[level] as (...args: unknown[]) => void
      consoleFn(...message)
    }

    // 缓冲日志
    this.bufferLog({ timestamp, level, args })

    // 远程日志
    if (this.config.enableRemote && this.config.remoteUrl) {
      this.sendToRemote({ timestamp, level, args })
    }
  }

  /**
   * 缓冲日志
   */
  private bufferLog(log: { timestamp: string; level: string; args: unknown[] }) {
    this.logBuffer.push(log)
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer.shift()
    }
  }

  /**
   * 发送远程日志
   */
  private async sendToRemote(log: { timestamp: string; level: string; args: unknown[] }) {
    if (!this.config.remoteUrl) return

    try {
      // 使用 sendBeacon 或 fetch
      const data = JSON.stringify({
        ...log,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: Date.now()
      })

      if (navigator.sendBeacon) {
        navigator.sendBeacon(this.config.remoteUrl, data)
      } else {
        fetch(this.config.remoteUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: data,
          keepalive: true
        }).catch(() => {
          // 忽略远程日志错误
        })
      }
    } catch {
      // 忽略远程日志错误
    }
  }
}

// 创建默认实例
export const logger = new Logger()

// 开发环境将 logger 挂载到 window
if (import.meta.env.DEV) {
  (window as unknown as { logger: Logger }).logger = logger
}

// 导出类型和类
export { Logger }
export type { LoggerConfig }
