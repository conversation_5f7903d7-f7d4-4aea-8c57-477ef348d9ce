/**
 * 草稿存储工具类
 * 提供统一的草稿保存、加载、删除功能
 */

import { ElMessage } from 'element-plus'

// 草稿数据结构
export interface DraftData {
  id: string
  type: string
   
  data: unknown
  timestamp: string
  expireTime?: string
  userId?: string
  businessId?: string
  description?: string
}

// 草稿存储配置
export interface DraftStorageConfig {
  prefix?: string
  expireDays?: number
  autoSave?: boolean
  autoSaveInterval?: number
  storage?: 'local' | 'session'
}

class DraftStorage {
  private prefix: string
  private expireDays: number
  private storage: Storage
  private autoSaveTimers: Map<string, NodeJS.Timeout> = new Map()

  constructor(config?: DraftStorageConfig) {
    this.prefix = config?.prefix || 'draft_'
    this.expireDays = config?.expireDays || 7
    this.storage = config?.storage === 'session' ? sessionStorage : localStorage
  }

  /**
   * 保存草稿
   */
   
  save(
    key: string,
    data: unknown,
    options?: {
      type?: string
      businessId?: string
      description?: string
      expireDays?: number
    }
  ): boolean {
    try {
      const draftKey = this.getDraftKey(key)
      const expireDays = options?.expireDays || this.expireDays
      const expireTime = new Date()
      expireTime.setDate(expireTime.getDate() + expireDays)

      const draftData: DraftData = {
        id: key,
        type: options?.type || 'unknown',
        data,
        timestamp: new Date().toISOString(),
        expireTime: expireTime.toISOString(),
        userId: this.getCurrentUserId(),
        businessId: options?.businessId,
        description: options?.description
      }

      this.storage.setItem(draftKey, JSON.stringify(draftData))
      return true
    } catch (__error) {
      return false
    }
  }

  /**
   * 加载草稿
   */
  load(key: string): DraftData | null {
    try {
      const draftKey = this.getDraftKey(key)
      const item = this.storage.getItem(draftKey)

      if (!item) return null

      const draftData: DraftData = JSON.parse(item)

      // 检查是否过期
      if (draftData.expireTime && new Date(draftData.expireTime) < new Date()) {
        this.remove(key)
        return null
      }

      return draftData
    } catch (__error) {
      return null
    }
  }

  /**
   * 删除草稿
   */
  remove(key: string): boolean {
    try {
      const draftKey = this.getDraftKey(key)
      this.storage.removeItem(draftKey)

      // 停止自动保存
      this.stopAutoSave(key)

      return true
    } catch (__error) {
      return false
    }
  }

  /**
   * 获取所有草稿
   */
  getAllDrafts(type?: string): DraftData[] {
    const drafts: DraftData[] = []

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key && key.startsWith(this.prefix)) {
        try {
          const item = this.storage.getItem(key)
          if (item) {
            const draftData: DraftData = JSON.parse(item)

            // 过滤过期草稿
            if (draftData.expireTime && new Date(draftData.expireTime) < new Date()) {
              this.storage.removeItem(key)
              continue
            }

            // 过滤类型
            if (!type || draftData.type === type) {
              drafts.push(draftData)
            }
          }
        } catch (__error) {
          }
      }
    }

    // 按时间倒序
    return drafts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }

  /**
   * 清理过期草稿
   */
  cleanExpiredDrafts(): number {
    let cleaned = 0
    const now = new Date()

    for (let i = this.storage.length - 1; i >= 0; i--) {
      const key = this.storage.key(i)
      if (key && key.startsWith(this.prefix)) {
        try {
          const item = this.storage.getItem(key)
          if (item) {
            const draftData: DraftData = JSON.parse(item)
            if (draftData.expireTime && new Date(draftData.expireTime) < now) {
              this.storage.removeItem(key)
              cleaned++
            }
          }
        } catch (__error) {
          // 删除无法解析的数据
          this.storage.removeItem(key)
          cleaned++
        }
      }
    }

    return cleaned
  }

  /**
   * 检查是否存在草稿
   */
  hasDraft(key: string): boolean {
    const draftData = this.load(key)
    return draftData !== null
  }

  /**
   * 开始自动保存
   */
  startAutoSave(
    key: string,
    getData: () => unknown,
    interval: number = 30000,
    options?: {
      type?: string
      businessId?: string
      description?: string
      onSave?: (success: boolean) => void
    }
  ): void {
    // 停止之前的自动保存
    this.stopAutoSave(key)

    // 设置新的自动保存
    const timer = setInterval(() => {
      const data = getData()
      if (data) {
        const success = this.save(key, data, options)
        options?.onSave?.(success)
      }
    }, interval)

    this.autoSaveTimers.set(key, timer)
  }

  /**
   * 停止自动保存
   */
  stopAutoSave(key: string): void {
    const timer = this.autoSaveTimers.get(key)
    if (timer) {
      clearInterval(timer)
      this.autoSaveTimers.delete(key)
    }
  }

  /**
   * 停止所有自动保存
   */
  stopAllAutoSave(): void {
    this.autoSaveTimers.forEach(timer => clearInterval(timer))
    this.autoSaveTimers.clear()
  }

  /**
   * 导出草稿
   */
  exportDraft(key: string): string | null {
    const draftData = this.load(key)
    if (!draftData) return null

    return JSON.stringify(draftData, null, 2)
  }

  /**
   * 导入草稿
   */
  importDraft(jsonString: string): boolean {
    try {
      const draftData: DraftData = JSON.parse(jsonString)
      return this.save(draftData.id, draftData.data, {
        type: draftData.type,
        businessId: draftData.businessId,
        description: draftData.description
      })
    } catch (__error) {
      return false
    }
  }

  /**
   * 获取草稿存储大小
   */
  getStorageSize(): { count: number; size: number } {
    let count = 0
    let size = 0

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key && key.startsWith(this.prefix)) {
        count++
        const item = this.storage.getItem(key)
        if (item) {
          size += item.length * 2 // 字符串在JS中占用2字节
        }
      }
    }

    return { count, size }
  }

  /**
   * 获取草稿key
   */
  private getDraftKey(key: string): string {
    return `${this.prefix}${key}`
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string {
    // 从store或其他地方获取当前用户ID
    return localStorage.getItem('userId') || 'anonymous'
  }
}

// 创建默认实例
export const draftStorage = new DraftStorage()

// Vue组合式函数
export function useDraftStorage(
  key: string,
  options?: {
    type?: string
    businessId?: string
    autoSave?: boolean
    autoSaveInterval?: number
    onAutoSave?: (success: boolean) => void
  }
) {
  const {
    type,
    businessId,
    autoSave = (false = false),
    autoSaveInterval = (30000 = 30000),
    onAutoSave
  } = options || {}

  /**
   * 保存草稿
   */
   
  const saveDraft = (data: unknown, showMessage = true): boolean => {
    const success = draftStorage.save(key, data, { type, businessId })
    if (showMessage) {
      if (success) {
        ElMessage.success('草稿已保存')
      } else {
        ElMessage.error('草稿保存失败')
      }
    }
    return success
  }

  /**
   * 加载草稿
   */
   
  const loadDraft = (showMessage = true): unknown => {
    const draft = draftStorage.load(key)
    if (draft && showMessage) {
      ElMessage.success('已加载草稿')
    }
    return draft?.data || null
  }

  /**
   * 删除草稿
   */
  const removeDraft = (showMessage = true): boolean => {
    const success = draftStorage.remove(key)
    if (showMessage && success) {
      ElMessage.success('草稿已删除')
    }
    return success
  }

  /**
   * 检查是否有草稿
   */
  const hasDraft = (): boolean => {
    return draftStorage.hasDraft(key)
  }

  /**
   * 开始自动保存
   */
  const startAutoSave = (getData: () => unknown): void => {
    if (autoSave) {
      draftStorage.startAutoSave(key, getData, autoSaveInterval, {
        type,
        businessId,
        onSave: onAutoSave
      })
    }
  }

  /**
   * 停止自动保存
   */
  const stopAutoSave = (): void => {
    draftStorage.stopAutoSave(key)
  }

  return {
    saveDraft,
    loadDraft,
    removeDraft,
    hasDraft,
    startAutoSave,
    stopAutoSave
  }
}
