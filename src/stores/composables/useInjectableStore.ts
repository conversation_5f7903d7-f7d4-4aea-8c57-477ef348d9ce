/**
 * 使用可注入Store的组合式函数
 */

import { container } from '@/api/core/injectable'
import type { InjectionToken } from '@/api/core/types'
import type { BaseStore } from '../core/injectable-store'

/**
 * 使用可注入的Store
 * @param token Store的注入令牌
 * @returns Store实例
 */
export function useInjectableStore<T extends BaseStore>(
  token: InjectionToken<T>
): ReturnType<T['getStore']> {
  const storeInstance = container.get(token)
  return storeInstance.getStore()
}

/**
 * 创建Store的组合式函数工厂
 * @param token Store的注入令牌
 * @returns 返回一个组合式函数
 */
export function createStoreComposable<T extends BaseStore>(token: InjectionToken<T>) {
  return () => useInjectableStore(token)
}

// 导出预定义的组合式函数
import {
  EMPLOYEE_STORE,
  ORGANIZATION_STORE,
  USER_STORE,
  APP_STORE,
  PERMISSION_STORE
} from '../core/tokens'

export const useEmployeeStore = createStoreComposable(EMPLOYEE_STORE)
