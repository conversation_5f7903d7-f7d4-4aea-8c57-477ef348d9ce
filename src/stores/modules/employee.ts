/**
 * 员工管理状态存储
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import employeeApi from '@/api/modules/employee'
import type {
  Employee,
  EmployeeDetail,
  EmployeeSearchParams,
  PersonnelStatus
} from '@/types/employee'

export const useEmployeeStore = defineStore('employee', () => {
  // 状态
  const employeeList = ref<Employee[]>([])
  const currentEmployee = ref<EmployeeDetail | null>(null)
  const statistics = ref<{
    totalCount: number
    activeCount: number
    resignedCount: number
    retiredCount: number
    rehiredCount: number
    byDepartment: Array<{ name: string; count: number }>
    byEducation: Array<{ name: string; count: number }>
    byTitle: Array<{ name: string; count: number }>
  }>({
    totalCount: 0,
    activeCount: 0,
    resignedCount: 0,
    retiredCount: 0,
    rehiredCount: 0,
    byDepartment: [],
    byEducation: [],
    byTitle: []
  })

  const completenessStats = ref<{
    totalCount: number
    avgCompleteness: number
    distribution: Array<{
      range: string
      count: number
      percentage: number
    }>
  }>({
    totalCount: 0,
    avgCompleteness: 0,
    distribution: []
  })

  // 计算属性
  const activeEmployees = computed(() =>
    employeeList.value.filter(emp => emp.personnelStatus === 'active')
  )

  const employeeMap = computed(() => {
    const map = new Map<string, Employee>()
    employeeList.value.forEach(emp => {
      map.set(emp.employeeId, emp)
    })
    return map
  })

  const departmentEmployeeCount = computed(() => {
    const countMap = new Map<string, number>()
    employeeList.value.forEach(emp => {
      if (emp.institutionId) {
        const count = countMap.get(emp.institutionId) || 0
        countMap.set(emp.institutionId, count + 1)
      }
    })
    return countMap
  })

  // Actions
  // 获取员工列表（用于缓存常用数据）
  const fetchEmployeeList = async (params?: EmployeeSearchParams) => {
    try {
      const res = await employeeApi.getList(
        params || {
          page: 1,
          pageSize: 1000,
          personnelStatus: 'active'
        }
      )
      employeeList.value = res.data.list
      return res.data
    } catch (___error) {
      throw error
    }
  }

  // 获取员工详情
  const fetchEmployeeDetail = async (employeeId: string) => {
    try {
      const res = await employeeApi.getDetail(employeeId)
      currentEmployee.value = res.data
      return res.data
    } catch (___error) {
      throw error
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const res = await employeeApi.getStatistics()
      statistics.value = res.data
      return res.data
    } catch (___error) {
      throw error
    }
  }

  // 获取信息完整度统计
  const fetchCompletenessStats = async () => {
    try {
      const res = await employeeApi.getCompletenessStats()
      completenessStats.value = res.data
      return res.data
    } catch (___error) {
      throw error
    }
  }

  // 根据ID获取员工信息
  const getEmployeeById = (employeeId: string): Employee | undefined => {
    return employeeMap.value.get(employeeId)
  }

  // 根据部门获取员工列表
  const getEmployeesByDepartment = (departmentId: string): Employee[] => {
    return employeeList.value.filter(emp => emp.institutionId === departmentId)
  }

  // 根据状态获取员工列表
  const getEmployeesByStatus = (status: PersonnelStatus): Employee[] => {
    return employeeList.value.filter(emp => emp.personnelStatus === status)
  }

  // 搜索员工
  const searchEmployees = async (params: { keyword: string; size?: number }) => {
    try {
      const res = await employeeApi.getList({
        keyword: params.keyword,
        page: 1,
        pageSize: params.size || 20
      })
      return res.data
    } catch (___error) {
      throw error
    }
  }

  // 更新员工信息（本地缓存）
  const updateEmployeeCache = (employee: Employee) => {
    const index = employeeList.value.findIndex(emp => emp.employeeId === employee.employeeId)
    if (index !== -1) {
      employeeList.value[index] = { ...employeeList.value[index], ...employee }
    } else {
      employeeList.value.push(employee)
    }
  }

  // 删除员工（本地缓存）
  const removeEmployeeCache = (employeeId: string) => {
    const index = employeeList.value.findIndex(emp => emp.employeeId === employeeId)
    if (index !== -1) {
      employeeList.value.splice(index, 1)
    }
  }

  // 清空缓存
  const clearCache = () => {
    employeeList.value = []
    currentEmployee.value = null
  }

  // 刷新缓存
  const refreshCache = async () => {
    await Promise.all([fetchEmployeeList(), fetchStatistics(), fetchCompletenessStats()])
  }

  return {
    // 状态
    employeeList,
    currentEmployee,
    statistics,
    completenessStats,

    // 计算属性
    activeEmployees,
    employeeMap,
    departmentEmployeeCount,

    // Actions
    fetchEmployeeList,
    fetchEmployeeDetail,
    fetchStatistics,
    fetchCompletenessStats,
    getEmployeeById,
    getEmployeesByDepartment,
    getEmployeesByStatus,
    searchEmployees,
    updateEmployeeCache,
    removeEmployeeCache,
    clearCache,
    refreshCache
  }
})
