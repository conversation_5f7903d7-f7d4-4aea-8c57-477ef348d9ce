/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * 工作流状态管理
 * @module stores/modules/workflow
 */

import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import { workflowApi } from '@/workflow/api'
import type {
  ProcessDefinition,
  ProcessInstance,
  Task,
  TaskQuery,
  ProcessInstanceQuery,
  ProcessStatistics,
  TaskListItem,
  ProcessInstanceStatus,
  TaskStatus,
  ApprovalAction,
  ApprovalData
} from '@/workflow/types'
import { ElMessage } from 'element-plus'

/**
 * 工作流Store
 */
export const useWorkflowStore = defineStore(
  'workflow',
  () => {
    // 状态定义
    const processDefinitions = ref<ProcessDefinition[]>([])
    const processInstances = ref<ProcessInstance[]>([])
    const todoTasks = ref<TaskListItem[]>([])
    const doneTasks = ref<TaskListItem[]>([])
    const myApplications = ref<ProcessInstance[]>([])
    const currentTask = ref<Task | null>(null)
    const currentInstance = ref<ProcessInstance | null>(null)
    const statistics = ref<ProcessStatistics | null>(null)
    const taskStatistics = ref({
      total: 0,
      urgent: 0,
      overdue: 0,
      today: 0
    })

    // 加载状态
    const loading = reactive({
      definitions: false,
      instances: false,
      tasks: false,
      task: false,
      instance: false,
      statistics: false
    })

    // 分页信息
    const pagination = reactive({
      tasks: {
        page: 1,
        size: 20,
        total: 0
      },
      instances: {
        page: 1,
        size: 20,
        total: 0
      },
      applications: {
        page: 1,
        size: 20,
        total: 0
      }
    })

    // 查询条件
    const taskQuery = reactive<TaskQuery>({
      assignee: '',
      candidateUser: '',
      candidateGroup: '',
      processDefinitionKey: '',
      processInstanceId: '',
      includeProcessVariables: false,
      page: 1,
      size: 20
    })

    const instanceQuery = reactive<ProcessInstanceQuery>({
      processDefinitionKey: '',
      businessKey: '',
      startedBy: '',
      status: undefined,
      page: 1,
      size: 20
    })

    // 计算属性
    const todoCount = computed(() => todoTasks.value.length)
    const doneCount = computed(() => doneTasks.value.length)
    const runningInstanceCount = computed(
      () => processInstances.value.filter(i => i.status === 'RUNNING').length
    )

    // 获取流程定义列表
    async function fetchProcessDefinitions() {
      loading.definitions = true
      try {
        const { data } = await workflowApi.getProcessDefinitions({ latest: true })
        processDefinitions.value = data.list
      } catch (___error) {
        ElMessage.error('获取流程定义失败')
      } finally {
        loading.definitions = false
      }
    }

    // 获取待办任务
    async function fetchTodoTasks(query?: Partial<TaskQuery>) {
      loading.tasks = true
      try {
        const params = { ...taskQuery, ...query }
        const { data } = await workflowApi.getTasks(params)
        todoTasks.value = data.list
        pagination.tasks.total = data.total
        pagination.tasks.page = data.page
        pagination.tasks.size = data.size
      } catch (___error) {
        ElMessage.error('获取待办任务失败')
      } finally {
        loading.tasks = false
      }
    }

    // 获取已办任务
    async function fetchDoneTasks(query?: Partial<TaskQuery>) {
      loading.tasks = true
      try {
        const params = {
          ...taskQuery,
          ...query,
          finished: true
        }
        const { data } = await workflowApi.getHistoricTasks(params)
        doneTasks.value = data.list.map(
          task =>
            ({
              ...task,
              status: TaskStatus.COMPLETED
            }) as TaskListItem
        )
        pagination.tasks.total = data.total
        pagination.tasks.page = data.page
        pagination.tasks.size = data.size
      } catch (___error) {
        ElMessage.error('获取已办任务失败')
      } finally {
        loading.tasks = false
      }
    }

    // 获取我的申请
    async function fetchMyApplications(query?: Partial<ProcessInstanceQuery>) {
      loading.instances = true
      try {
        const params = {
          ...instanceQuery,
          ...query,
          startedBy: 'currentUser' // 这里应该从用户信息中获取
        }
        const { data } = await workflowApi.getProcessInstances(params)
        myApplications.value = data.list
        pagination.applications.total = data.total
        pagination.applications.page = data.page
        pagination.applications.size = data.size
      } catch (___error) {
        ElMessage.error('获取我的申请失败')
      } finally {
        loading.instances = false
      }
    }

    // 获取流程实例列表
    async function fetchProcessInstances(query?: Partial<ProcessInstanceQuery>) {
      loading.instances = true
      try {
        const params = { ...instanceQuery, ...query }
        const { data } = await workflowApi.getProcessInstances(params)
        processInstances.value = data.list
        pagination.instances.total = data.total
        pagination.instances.page = data.page
        pagination.instances.size = data.size
      } catch (___error) {
        ElMessage.error('获取流程实例失败')
      } finally {
        loading.instances = false
      }
    }

    // 获取任务详情
    async function fetchTaskDetail(taskId: string) {
      loading.task = true
      try {
        const { data } = await workflowApi.getTask(taskId)
        currentTask.value = data
        return data
      } catch (___error) {
        ElMessage.error('获取任务详情失败')
        throw error
      } finally {
        loading.task = false
      }
    }

    // 获取流程实例详情
    async function fetchInstanceDetail(instanceId: string) {
      loading.instance = true
      try {
        const { data } = await workflowApi.getProcessInstance(instanceId)
        currentInstance.value = data
        return data
      } catch (___error) {
        ElMessage.error('获取流程实例详情失败')
        throw error
      } finally {
        loading.instance = false
      }
    }

    // 启动流程
    async function startProcess(
      processKey: string,
      businessKey?: string,
      variables?: Record<string, _any>
    ) {
      try {
        const { data } = await workflowApi.startProcess(processKey, businessKey, variables)
        ElMessage.success('流程启动成功')
        return data
      } catch (___error) {
        ElMessage.error('启动流程失败')
        throw error
      }
    }

    // 完成任务
    async function completeTask(taskId: string, approvalData: ApprovalData) {
      try {
        await workflowApi.completeTask(taskId, approvalData)
        ElMessage.success('任务完成成功')
        // 刷新待办列表
        await fetchTodoTasks()
      } catch (___error) {
        ElMessage.error('完成任务失败')
        throw error
      }
    }

    // 认领任务
    async function claimTask(taskId: string, userId: string) {
      try {
        await workflowApi.claimTask(taskId, userId)
        ElMessage.success('任务认领成功')
        // 刷新任务详情
        await fetchTaskDetail(taskId)
      } catch (___error) {
        ElMessage.error('认领任务失败')
        throw error
      }
    }

    // 取消认领任务
    async function unclaimTask(taskId: string) {
      try {
        await workflowApi.unclaimTask(taskId)
        ElMessage.success('取消认领成功')
        // 刷新任务详情
        await fetchTaskDetail(taskId)
      } catch (___error) {
        ElMessage.error('取消认领失败')
        throw error
      }
    }

    // 委托任务
    async function delegateTask(taskId: string, userId: string) {
      try {
        await workflowApi.delegateTask(taskId, userId)
        ElMessage.success('任务委托成功')
        // 刷新待办列表
        await fetchTodoTasks()
      } catch (___error) {
        ElMessage.error('委托任务失败')
        throw error
      }
    }

    // 删除流程实例
    async function deleteProcessInstance(instanceId: string, reason?: string) {
      try {
        await workflowApi.deleteProcessInstance(instanceId, reason)
        ElMessage.success('流程实例删除成功')
        // 刷新流程实例列表
        await fetchProcessInstances()
      } catch (___error) {
        ElMessage.error('删除流程实例失败')
        throw error
      }
    }

    // 挂起流程实例
    async function suspendProcessInstance(instanceId: string) {
      try {
        await workflowApi.suspendProcessInstance(instanceId)
        ElMessage.success('流程实例挂起成功')
        // 刷新流程实例详情
        await fetchInstanceDetail(instanceId)
      } catch (___error) {
        ElMessage.error('挂起流程实例失败')
        throw error
      }
    }

    // 激活流程实例
    async function activateProcessInstance(instanceId: string) {
      try {
        await workflowApi.activateProcessInstance(instanceId)
        ElMessage.success('流程实例激活成功')
        // 刷新流程实例详情
        await fetchInstanceDetail(instanceId)
      } catch (___error) {
        ElMessage.error('激活流程实例失败')
        throw error
      }
    }

    // 获取流程统计
    async function fetchProcessStatistics(processDefinitionId?: string) {
      loading.statistics = true
      try {
        const { data } = await workflowApi.getProcessStatistics(processDefinitionId)
        statistics.value = data
        return data
      } catch (___error) {
        ElMessage.error('获取流程统计失败')
        throw error
      } finally {
        loading.statistics = false
      }
    }

    // 获取任务统计
    async function fetchTaskStatistics() {
      try {
        const { data } = await workflowApi.getTaskStatistics()
        taskStatistics.value = data
        return data
      } catch (___error) {
        // 不显示错误消息，静默失败
        return taskStatistics.value
      }
    }

    // 获取任务列表（通用方法）

    async function fetchTasks(params: unknown) {
      loading.tasks = true
      try {
        const { data } = await workflowApi.getTasks(params)
        return {
          data: data.list,
          total: data.total
        }
      } catch (___error) {
        ElMessage.error('获取任务列表失败')
        throw error
      } finally {
        loading.tasks = false
      }
    }

    // 批量完成任务
    async function batchCompleteTasks(config: {
      taskIds: string[]
      action: ApprovalAction
      comment?: string
    }) {
      try {
        await workflowApi.batchCompleteTasks(config)
        ElMessage.success('批量处理成功')
        // 刷新待办列表
        await fetchTodoTasks()
      } catch (___error) {
        ElMessage.error('批量处理失败')
        throw error
      }
    }

    // 导出任务

    async function exportTasks(params: unknown) {
      try {
        const response = await workflowApi.exportTasks(params)
        // 处理文件下载
        const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `任务列表_${new Date().getTime()}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
      } catch (___error) {
        throw error
      }
    }

    // 催办任务

    async function urgeTask(taskId: string, urgeData: unknown) {
      try {
        await workflowApi.urgeTask(taskId, urgeData)
        ElMessage.success('催办通知已发送')
      } catch (___error) {
        throw error
      }
    }

    // 批量催办任务

    async function batchUrgeTasks(urgeData: unknown) {
      try {
        const { data } = await workflowApi.batchUrgeTasks(urgeData)
        ElMessage.success(`成功催办 ${data.successCount} 个任务`)
        return data
      } catch (___error) {
        throw error
      }
    }

    // 获取催办历史

    async function getUrgeHistory(params: unknown) {
      try {
        return await workflowApi.getUrgeHistory(params)
      } catch (___error) {
        throw error
      }
    }

    // 获取催办统计

    async function getUrgeStatistics(params: unknown) {
      try {
        return await workflowApi.getUrgeStatistics(params)
      } catch (___error) {
        throw error
      }
    }

    // 获取任务历史
    async function getTaskHistory(taskIdOrInstanceId: string) {
      try {
        const { data } = await workflowApi.getTaskHistory(taskIdOrInstanceId)
        return data
      } catch (___error) {
        throw error
      }
    }

    // 获取流程定义XML
    async function getProcessDefinition(processIdOrInstanceId: string) {
      try {
        const { data } = await workflowApi.getProcessDefinitionXml(processIdOrInstanceId)
        return data.xml || data
      } catch (___error) {
        throw error
      }
    }

    // 撤回流程
    async function withdrawProcess(withdrawData: {
      processInstanceId: string
      reason: string
      urgency: string
      notifyParticipants: boolean
      notifyCurrentHandler: boolean
      afterAction: 'draft' | 'cancel'
      remark?: string
      withdrawTime: string
      withdrawUser: string
    }) {
      try {
        await workflowApi.withdrawProcess(withdrawData.processInstanceId, withdrawData)
        ElMessage.success('流程撤回成功')

        // 刷新我的申请列表
        await fetchMyApplications()
      } catch (___error) {
        throw error
      }
    }

    // 检查流程是否可撤回
    async function checkCanWithdraw(instanceId: string) {
      try {
        const { data } = await workflowApi.checkCanWithdraw(instanceId)
        return data
      } catch (___error) {
        return {
          canWithdraw: false,
          message: '检查失败',
          reasons: ['系统错误']
        }
      }
    }

    // 获取撤回历史
    async function getWithdrawHistory(params: {
      processTitle?: string
      withdrawUser?: string
      startTime?: string
      endTime?: string
      urgency?: string
      afterAction?: string
      page?: number
      size?: number
    }) {
      try {
        const { data } = await workflowApi.getWithdrawHistory(params)
        return data
      } catch (___error) {
        throw error
      }
    }

    // 获取撤回统计
    async function getWithdrawStatistics() {
      try {
        const { data } = await workflowApi.getWithdrawStatistics()
        return data
      } catch (___error) {
        return {
          total: 0,
          monthCount: 0,
          draftCount: 0,
          cancelCount: 0
        }
      }
    }

    // 获取任务候选人
    async function getTaskCandidates(taskId: string) {
      try {
        const { data } = await workflowApi.getTaskCandidates(taskId)
        return data
      } catch (___error) {
        throw error
      }
    }

    // 获取用户任务数
    async function getUserTaskCount(userId: string) {
      try {
        const { data } = await workflowApi.getUserTaskCount(userId)
        return data.count || 0
      } catch (___error) {
        return 0
      }
    }

    // 批量认领任务

    async function batchClaimTasks(config: unknown) {
      try {
        await workflowApi.batchClaimTasks(config)
        ElMessage.success('批量认领成功')
      } catch (___error) {
        throw error
      }
    }

    // 分配认领任务

    async function distributeClaimTasks(config: unknown) {
      try {
        await workflowApi.distributeClaimTasks(config)
        ElMessage.success('任务分配成功')
      } catch (___error) {
        throw error
      }
    }

    // 平均分配任务

    async function averageClaimTasks(config: unknown) {
      try {
        await workflowApi.averageClaimTasks(config)
        ElMessage.success('任务平均分配成功')
      } catch (___error) {
        throw error
      }
    }

    // 获取认领历史

    async function getClaimHistory(params: unknown) {
      try {
        return await workflowApi.getClaimHistory(params)
      } catch (___error) {
        throw error
      }
    }

    // 获取认领统计

    async function getClaimStats(params: unknown) {
      try {
        return await workflowApi.getClaimStats(params)
      } catch (___error) {
        throw error
      }
    }

    // 导出认领历史

    async function exportClaimHistory(params: unknown) {
      try {
        const response = await workflowApi.exportClaimHistory(params)
        // 处理文件下载
        const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `认领历史_${new Date().getTime()}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
      } catch (___error) {
        throw error
      }
    }

    // 获取推荐委托人

    async function getRecommendDelegateUsers(params: unknown) {
      try {
        return await workflowApi.getRecommendDelegateUsers(params)
      } catch (___error) {
        throw error
      }
    }

    // 获取委托历史

    async function getDelegateHistory(params: unknown) {
      try {
        return await workflowApi.getDelegateHistory(params)
      } catch (___error) {
        throw error
      }
    }

    // 获取委托统计

    async function getDelegateStats(params: unknown) {
      try {
        return await workflowApi.getDelegateStats(params)
      } catch (___error) {
        throw error
      }
    }

    // 撤回委托
    async function revokeDelegation(delegationId: string) {
      try {
        await workflowApi.revokeDelegation(delegationId)
        ElMessage.success('委托已撤回')
      } catch (___error) {
        throw error
      }
    }

    // 导出委托历史

    async function exportDelegateHistory(params: unknown) {
      try {
        const response = await workflowApi.exportDelegateHistory(params)
        // 处理文件下载
        const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `委托历史_${new Date().getTime()}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
      } catch (___error) {
        throw error
      }
    }

    // 回退任务

    async function rollbackTask(taskId: string, rollbackData: unknown) {
      try {
        // 适配API参数格式
        const apiData = {
          nodeId: rollbackData.targetNodeId,
          reason: rollbackData.reason
        }
        await workflowApi.rollbackTask(taskId, apiData)
        // 刷新待办列表
        await fetchTodoTasks()
      } catch (___error) {
        throw error
      }
    }

    // 获取回退历史

    async function getRollbackHistory(params: unknown) {
      try {
        return await workflowApi.getRollbackHistory(params)
      } catch (___error) {
        throw error
      }
    }

    // 获取回退统计

    async function getRollbackStats(params: unknown) {
      try {
        return await workflowApi.getRollbackStats(params)
      } catch (___error) {
        throw error
      }
    }

    // 导出回退历史

    async function exportRollbackHistory(params: unknown) {
      try {
        const response = await workflowApi.exportRollbackHistory(params)
        // 处理文件下载
        const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `回退历史_${new Date().getTime()}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
      } catch (___error) {
        throw error
      }
    }

    // 获取推荐加签用户

    async function getRecommendAddSignUsers(params: unknown) {
      try {
        return await workflowApi.getRecommendAddSignUsers(params)
      } catch (___error) {
        throw error
      }
    }

    // 任务加签

    async function addSign(taskId: string, addSignData: unknown) {
      try {
        await workflowApi.addSign(taskId, addSignData)
        ElMessage.success('加签成功')
        // 刷新任务详情
        await fetchTaskDetail(taskId)
      } catch (___error) {
        throw error
      }
    }

    // 检查减签权限
    async function checkRemoveSignPermission(taskId: string) {
      try {
        const { data } = await workflowApi.checkRemoveSignPermission(taskId)
        return data.hasPermission || false
      } catch (___error) {
        return false
      }
    }

    // 任务减签

    async function removeSign(taskId: string, removeSignData: unknown) {
      try {
        await workflowApi.removeSign(taskId, removeSignData)
        ElMessage.success('减签成功')
        // 刷新任务详情
        await fetchTaskDetail(taskId)
      } catch (___error) {
        throw error
      }
    }

    // 获取加减签历史

    async function getSignHistory(params: unknown) {
      try {
        return await workflowApi.getSignHistory(params)
      } catch (___error) {
        throw error
      }
    }

    // 批量审批任务
    async function batchApproveTasks(data: {
      taskIds: string[]
      action: 'approve' | 'reject' | 'return'
      comment: string
      returnToNodeId?: string
      continueOnError: boolean
      notifyStarter: boolean
      autoForward: boolean
      strategy: 'parallel' | 'serial'
    }) {
      try {
        // 调用批量审批API
        const result = await workflowApi.batchApproveTasks(data)

        // 刷新待办列表
        await fetchTodoTasks()

        return result
      } catch (___error) {
        throw error
      }
    }

    // 导出批量审批结果

    async function exportBatchApprovalResult(data: any) {
      try {
        // 实际实现应该调用后端API生成Excel

        // 模拟导出
        const exportData = [
          ['任务名称', '流程名称', '审批结果', '错误信息'],
          ...data.details.map((item: any) => [item.任务名称, item.流程名称, item.审批结果, item.错误信息])
        ]

        console.log('Export data:', exportData)
        // 这里应该生成真实的Excel文件
        // 模拟下载
        ElMessage.success('导出成功')
      } catch (error) {
        throw error
      }
    }

    // 获取意见模板列表

    async function getCommentTemplates(params?: unknown) {
      try {
        // 实际应该调用API

        // 模拟返回
        return []
      } catch (___error) {
        throw error
      }
    }

    // 创建意见模板

    async function createCommentTemplate(template: unknown) {
      try {
        // 实际应该调用API
        // await workflowApi.createCommentTemplate(template)
        ElMessage.success('创建成功')
      } catch (___error) {
        throw error
      }
    }

    // 更新意见模板

    async function updateCommentTemplate(id: string, template: unknown) {
      try {
        // 实际应该调用API
        // await workflowApi.updateCommentTemplate(id, template)
        ElMessage.success('更新成功')
      } catch (___error) {
        throw error
      }
    }

    // 删除意见模板
    async function deleteCommentTemplate(id: string) {
      try {
        // 实际应该调用API
        // await workflowApi.deleteCommentTemplate(id)
        ElMessage.success('删除成功')
      } catch (___error) {
        throw error
      }
    }

    // 作废流程
    async function terminateProcess(terminateData: {
      processInstanceId: string
      reasonType: string
      reason: string
      urgency: string
      notifyStarter: boolean
      notifyParticipants: boolean
      notifyCurrentHandler: boolean
      notifyChannels: string[]
      lockRelatedData: boolean
      generateReport: boolean
      operatorRemark?: string
      terminateTime: string
      terminateUser: string
      terminateUserRole: string
      processStatus: string
      currentTaskName: string
    }) {
      try {
        await workflowApi.terminateProcess(terminateData.processInstanceId, terminateData)
        ElMessage.success('流程作废成功')

        // 刷新流程实例列表
        await fetchProcessInstances()
      } catch (___error) {
        throw error
      }
    }

    // 检查流程是否可作废
    async function checkCanTerminate(instanceId: string, userRole: string) {
      try {
        const { data } = await workflowApi.checkCanTerminate(instanceId, userRole)
        return data
      } catch (___error) {
        return {
          canTerminate: false,
          message: '权限检查失败'
        }
      }
    }

    // 获取作废历史
    async function getTerminateHistory(params: {
      processTitle?: string
      terminateUser?: string
      startTime?: string
      endTime?: string
      reasonType?: string
      urgency?: string
      page?: number
      size?: number
    }) {
      try {
        const { data } = await workflowApi.getTerminateHistory(params)
        return data
      } catch (___error) {
        throw error
      }
    }

    // 获取作废统计
    async function getTerminateStatistics() {
      try {
        const { data } = await workflowApi.getTerminateStatistics()
        return data
      } catch (___error) {
        return {
          total: 0,
          monthCount: 0,
          dataErrorCount: 0,
          violationCount: 0
        }
      }
    }

    // 导出作废历史

    async function exportTerminateHistory(params: unknown) {
      try {
        const response = await workflowApi.exportTerminateHistory(params)
        // 处理文件下载
        const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `作废历史_${new Date().getTime()}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
      } catch (___error) {
        throw error
      }
    }

    // 生成打印预览
    async function generatePrintPreview(params: {
      taskIds: string[]
      options: {
        printContent: string[]
        format: string
        paperSize: string
        orientation: string
        margin: string
        showPageNumber: boolean
        showPrintTime: boolean
        showWatermark: boolean
        watermarkText?: string
        timestamp?: number
      }
    }) {
      try {
        // 模拟生成预览URL
        // 实际实现应该调用后端API生成预览
        const baseUrl = '/api/workflow/print/preview'
        const queryParams = new URLSearchParams({
          taskIds: params.taskIds.join(','),
          ...params.options,
          printContent: params.options.printContent.join(',')
        })

        return `${baseUrl}?${queryParams.toString()}`
      } catch (___error) {
        throw error
      }
    }

    // 重置状态
    function resetState() {
      processDefinitions.value = []
      processInstances.value = []
      todoTasks.value = []
      doneTasks.value = []
      myApplications.value = []
      currentTask.value = null
      currentInstance.value = null
      statistics.value = null
    }

    return {
      // 状态
      processDefinitions,
      processInstances,
      todoTasks,
      doneTasks,
      myApplications,
      currentTask,
      currentInstance,
      statistics,
      taskStatistics,
      loading,
      pagination,
      taskQuery,
      instanceQuery,

      // 计算属性
      todoCount,
      doneCount,
      runningInstanceCount,

      // 方法
      fetchProcessDefinitions,
      fetchTodoTasks,
      fetchDoneTasks,
      fetchMyApplications,
      fetchProcessInstances,
      fetchTaskDetail,
      fetchInstanceDetail,
      startProcess,
      completeTask,
      claimTask,
      unclaimTask,
      delegateTask,
      deleteProcessInstance,
      suspendProcessInstance,
      activateProcessInstance,
      fetchProcessStatistics,
      fetchTaskStatistics,
      fetchTasks,
      batchCompleteTasks,
      exportTasks,
      urgeTask,
      batchUrgeTasks,
      getUrgeHistory,
      getUrgeStatistics,
      getTaskHistory,
      getProcessDefinition,
      withdrawProcess,
      checkCanWithdraw,
      getWithdrawHistory,
      getWithdrawStatistics,
      getTaskCandidates,
      getUserTaskCount,
      batchClaimTasks,
      distributeClaimTasks,
      averageClaimTasks,
      getClaimHistory,
      getClaimStats,
      exportClaimHistory,
      getRecommendDelegateUsers,
      getDelegateHistory,
      getDelegateStats,
      revokeDelegation,
      exportDelegateHistory,
      rollbackTask,
      getRollbackHistory,
      getRollbackStats,
      exportRollbackHistory,
      getRecommendAddSignUsers,
      addSign,
      checkRemoveSignPermission,
      removeSign,
      getSignHistory,
      batchApproveTasks,
      exportBatchApprovalResult,
      getCommentTemplates,
      createCommentTemplate,
      updateCommentTemplate,
      deleteCommentTemplate,
      terminateProcess,
      checkCanTerminate,
      getTerminateHistory,
      getTerminateStatistics,
      exportTerminateHistory,
      resetState
    }
  },
  {
    persist: {
      // 持久化配置
      key: 'workflow-store',
      storage: sessionStorage,
      paths: ['taskQuery', 'instanceQuery', 'pagination']
    }
  }
)
