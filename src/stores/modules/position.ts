/**
 * 岗位管理状态管理
 * 管理岗位数据、岗位序列、岗位族群等状态
 */

import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import positionApi from '@/api/modules/position'
import type {
  Position,
  PositionSequence,
  PositionFamily,
  PositionStaffing,
  PositionMatchResult,
  PositionValuation,
  PositionStatistics,
  PositionQueryParams,
  PositionCategory,
  PositionLevel,
  PositionType,
  PositionStatus,
  PositionChange,
  PositionBatchRequest
} from '@/types/position'

interface PositionState {
  // 岗位列表
  positions: Position[]
  // 当前岗位
  currentPosition: Position | null
  // 岗位序列列表
  sequences: PositionSequence[]
  // 岗位族群列表
  families: PositionFamily[]
  // 岗位统计数据
  statistics: PositionStatistics | null
  // 加载状态
  loading: boolean
  // 查询参数
  queryParams: PositionQueryParams
  // 分页信息
  pagination: {
    total: number
    current: number
    pageSize: number
  }
}

export const usePositionStore = defineStore('position', {
  state: (): PositionState => ({
    positions: [],
    currentPosition: null,
    sequences: [],
    families: [],
    statistics: null,
    loading: false,
    queryParams: {
      page: 1,
      size: 20
    },
    pagination: {
      total: 0,
      current: 1,
      pageSize: 20
    }
  }),

  getters: {
    // 获取有效岗位列表
    activePositions: _state => {
      return state.positions.filter(pos => pos.status === PositionStatus.ACTIVE)
    },

    // 根据类别分组岗位
    positionsByCategory: _state => {
      const grouped: Record<PositionCategory, Position[]> = {} as unknown
      state.positions.forEach(pos => {
        if (!grouped[pos.positionCategory]) {
          grouped[pos.positionCategory] = []
        }
        grouped[pos.positionCategory].push(pos)
      })
      return grouped
    },

    // 根据机构分组岗位
    positionsByInstitution: _state => {
      const grouped: Record<string, Position[]> = {}
      state.positions.forEach(pos => {
        if (!grouped[pos.institutionId]) {
          grouped[pos.institutionId] = []
        }
        grouped[pos.institutionId].push(pos)
      })
      return grouped
    },

    // 获取空缺岗位
    vacantPositions: _state => {
      return state.positions.filter(pos => pos.vacancyCount && pos.vacancyCount > 0)
    },

    // 根据ID获取岗位
    getPositionById: _state => {
      return (id: string): Position | undefined => {
        return state.positions.find(pos => pos.positionId === id)
      }
    },

    // 根据序列ID获取岗位
    getPositionsBySequence: _state => {
      return (sequenceId: string): Position[] => {
        return state.positions.filter(pos =>
          state.families.find(f => f.familyId === pos.positionId && f.sequenceId === sequenceId)
        )
      }
    },

    // 根据族群ID获取岗位
    getPositionsByFamily: _state => {
      return (familyId: string): Position[] => {
        return state.positions.filter(pos => pos.positionId === familyId)
      }
    }
  },

  actions: {
    /**
     * 获取岗位列表
     */
    async fetchPositions(params?: PositionQueryParams) {
      this.loading = true
      try {
        const queryParams = { ...this.queryParams, ...params }
        const { data } = await positionApi.getList(queryParams)

        this.positions = data.list
        this.pagination = {
          total: data.total,
          current: queryParams.page || 1,
          pageSize: queryParams.size || 20
        }
        this.queryParams = queryParams

        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '获取岗位列表失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取岗位详情
     */
    async fetchPositionDetail(id: string) {
      try {
        const { data } = await positionApi.getDetail(id)
        this.currentPosition = data
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '获取岗位详情失败')
        throw error
      }
    },

    /**
     * 创建岗位
     */
    async createPosition(data: Partial<Position>) {
      try {
        const { data: newPosition } = await positionApi.create(data)

        // 刷新列表
        await this.fetchPositions()

        ElMessage.success('岗位创建成功')
        return newPosition
      } catch (error: unknown) {
        ElMessage.error(error.message || '创建岗位失败')
        throw error
      }
    },

    /**
     * 更新岗位
     */
    async updatePosition(id: string, data: Partial<Position>) {
      try {
        const { data: updatedPosition } = await positionApi.update(id, data)

        // 更新列表中的岗位
        const index = this.positions.findIndex(pos => pos.positionId === id)
        if (index !== -1) {
          this.positions[index] = updatedPosition
        }

        // 如果是当前岗位，更新当前岗位
        if (this.currentPosition?.positionId === id) {
          this.currentPosition = updatedPosition
        }

        ElMessage.success('岗位更新成功')
        return updatedPosition
      } catch (error: unknown) {
        ElMessage.error(error.message || '更新岗位失败')
        throw error
      }
    },

    /**
     * 删除岗位
     */
    async deletePosition(id: string) {
      try {
        await positionApi.delete(id)

        // 从列表中移除
        this.positions = this.positions.filter(pos => pos.positionId !== id)

        // 如果是当前岗位，清空
        if (this.currentPosition?.positionId === id) {
          this.currentPosition = null
        }

        ElMessage.success('岗位删除成功')
      } catch (error: unknown) {
        ElMessage.error(error.message || '删除岗位失败')
        throw error
      }
    },

    /**
     * 批量操作岗位
     */
    async batchOperation(request: PositionBatchRequest) {
      try {
        const { data } = await positionApi.batchOperation(request)

        // 刷新列表
        await this.fetchPositions()

        ElMessage.success(data.message || '批量操作成功')
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '批量操作失败')
        throw error
      }
    },

    /**
     * 获取岗位序列列表
     */
    async fetchSequences() {
      try {
        const { data } = await positionApi.getSequences()
        this.sequences = data
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '获取岗位序列失败')
        throw error
      }
    },

    /**
     * 创建岗位序列
     */
    async createSequence(data: Partial<PositionSequence>) {
      try {
        const { data: newSequence } = await positionApi.createSequence(data)
        this.sequences.push(newSequence)
        ElMessage.success('岗位序列创建成功')
        return newSequence
      } catch (error: unknown) {
        ElMessage.error(error.message || '创建岗位序列失败')
        throw error
      }
    },

    /**
     * 更新岗位序列
     */
    async updateSequence(id: string, data: Partial<PositionSequence>) {
      try {
        const { data: updatedSequence } = await positionApi.updateSequence(id, data)
        const index = this.sequences.findIndex(seq => seq.sequenceId === id)
        if (index !== -1) {
          this.sequences[index] = updatedSequence
        }
        ElMessage.success('岗位序列更新成功')
        return updatedSequence
      } catch (error: unknown) {
        ElMessage.error(error.message || '更新岗位序列失败')
        throw error
      }
    },

    /**
     * 删除岗位序列
     */
    async deleteSequence(id: string) {
      try {
        await positionApi.deleteSequence(id)
        this.sequences = this.sequences.filter(seq => seq.sequenceId !== id)
        ElMessage.success('岗位序列删除成功')
      } catch (error: unknown) {
        ElMessage.error(error.message || '删除岗位序列失败')
        throw error
      }
    },

    /**
     * 获取岗位族群列表
     */
    async fetchFamilies(sequenceId?: string) {
      try {
        const { data } = await positionApi.getFamilies(sequenceId)
        this.families = data
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '获取岗位族群失败')
        throw error
      }
    },

    /**
     * 创建岗位族群
     */
    async createFamily(data: Partial<PositionFamily>) {
      try {
        const { data: newFamily } = await positionApi.createFamily(data)
        this.families.push(newFamily)
        ElMessage.success('岗位族群创建成功')
        return newFamily
      } catch (error: unknown) {
        ElMessage.error(error.message || '创建岗位族群失败')
        throw error
      }
    },

    /**
     * 更新岗位族群
     */
    async updateFamily(id: string, data: Partial<PositionFamily>) {
      try {
        const { data: updatedFamily } = await positionApi.updateFamily(id, data)
        const index = this.families.findIndex(fam => fam.familyId === id)
        if (index !== -1) {
          this.families[index] = updatedFamily
        }
        ElMessage.success('岗位族群更新成功')
        return updatedFamily
      } catch (error: unknown) {
        ElMessage.error(error.message || '更新岗位族群失败')
        throw error
      }
    },

    /**
     * 删除岗位族群
     */
    async deleteFamily(id: string) {
      try {
        await positionApi.deleteFamily(id)
        this.families = this.families.filter(fam => fam.familyId !== id)
        ElMessage.success('岗位族群删除成功')
      } catch (error: unknown) {
        ElMessage.error(error.message || '删除岗位族群失败')
        throw error
      }
    },

    /**
     * 获取岗位人员配置
     */
    async fetchPositionStaffing(positionId: string): Promise<PositionStaffing> {
      try {
        const { data } = await positionApi.getStaffing(positionId)
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '获取岗位人员配置失败')
        throw error
      }
    },

    /**
     * 分配员工到岗位
     */
    async assignEmployee(positionId: string, employeeId: string, isPrimary = true) {
      try {
        await positionApi.assignEmployee(positionId, { employeeId, isPrimary })
        ElMessage.success('员工分配成功')
      } catch (error: unknown) {
        ElMessage.error(error.message || '分配员工失败')
        throw error
      }
    },

    /**
     * 移除岗位上的员工
     */
    async removeEmployee(positionId: string, employeeId: string) {
      try {
        await positionApi.removeEmployee(positionId, employeeId)
        ElMessage.success('员工移除成功')
      } catch (error: unknown) {
        ElMessage.error(error.message || '移除员工失败')
        throw error
      }
    },

    /**
     * 岗位匹配度评估
     */
    async evaluateMatch(positionId: string, employeeId: string): Promise<PositionMatchResult> {
      try {
        const { data } = await positionApi.evaluateMatch(positionId, employeeId)
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '匹配度评估失败')
        throw error
      }
    },

    /**
     * 岗位价值评估
     */
    async evaluateValue(positionId: string): Promise<PositionValuation> {
      try {
        const { data } = await positionApi.evaluateValue(positionId)
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '价值评估失败')
        throw error
      }
    },

    /**
     * 批量评估岗位价值
     */
    async batchEvaluateValue(positionIds: string[]) {
      try {
        const { data } = await positionApi.batchEvaluateValue(positionIds)
        ElMessage.success('批量评估完成')
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '批量评估失败')
        throw error
      }
    },

    /**
     * 获取岗位统计数据
     */

    async fetchStatistics(params?: unknown): Promise<PositionStatistics> {
      try {
        const { data } = await positionApi.getStatistics(params)
        this.statistics = data
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '获取统计数据失败')
        throw error
      }
    },

    /**
     * 获取岗位变更历史
     */
    async fetchPositionChanges(positionId: string): Promise<PositionChange[]> {
      try {
        const { data } = await positionApi.getChanges(positionId)
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '获取变更历史失败')
        throw error
      }
    },

    /**
     * 导入岗位
     */
    async importPositions(file: File) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        const { data } = await positionApi.import(formData)

        // 刷新列表
        await this.fetchPositions()

        ElMessage.success(`成功导入 ${data.successCount} 个岗位`)
        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '导入失败')
        throw error
      }
    },

    /**
     * 导出岗位
     */
    async exportPositions(params?: PositionQueryParams) {
      try {
        const blob = await positionApi.export(params)
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `岗位列表_${new Date().toISOString().split('T')[0]}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)

        ElMessage.success('导出成功')
      } catch (error: unknown) {
        ElMessage.error(error.message || '导出失败')
        throw error
      }
    },

    /**
     * 设置当前岗位
     */
    setCurrentPosition(position: Position | null) {
      this.currentPosition = position
    },

    /**
     * 清空状态
     */
    clearState() {
      this.positions = []
      this.currentPosition = null
      this.sequences = []
      this.families = []
      this.statistics = null
      this.loading = false
      this.queryParams = {
        page: 1,
        size: 20
      }
      this.pagination = {
        total: 0,
        current: 1,
        pageSize: 20
      }
    }
  },

  // 持久化配置
  persist: {
    enabled: true,
    paths: ['queryParams']
  }
})
