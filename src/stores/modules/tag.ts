/**
 * 标签管理状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import tagApi from '@/api/modules/tag'
import type {
  Tag,
  EmployeeTag,
  TagStatistics,
  EmployeePortrait,
  TagCategory,
  TagType,
  TagSearchParams,
  EmployeeTagSearchParams
} from '@/types/tag'

export const useTagStore = defineStore('tag', () => {
  // 标签列表
  const tags = ref<Tag[]>([])
  const tagTree = ref<Tag[]>([])
  const tagTotal = ref(0)
  const tagLoading = ref(false)

  // 员工标签
  const employeeTags = ref<EmployeeTag[]>([])
  const employeeTagTotal = ref(0)
  const employeeTagLoading = ref(false)

  // 标签统计
  const tagStatistics = ref<TagStatistics[]>([])
  const statisticsLoading = ref(false)

  // 员工画像
  const currentPortrait = ref<EmployeePortrait | null>(null)
  const portraitLoading = ref(false)

  // 缓存
  const tagCache = new Map<string, Tag>()
  const portraitCache = new Map<string, EmployeePortrait>()

  // 计算属性
  const tagMap = computed(() => {
    const map = new Map<string, Tag>()
    tags.value.forEach(tag => {
      map.set(tag.tagId, tag)
    })
    return map
  })

  const tagsByCategory = computed(() => {
    const categoryMap = new Map<TagCategory, Tag[]>()
    tags.value.forEach(tag => {
      const categoryTags = categoryMap.get(tag.category) || []
      categoryTags.push(tag)
      categoryMap.set(tag.category, categoryTags)
    })
    return categoryMap
  })

  const systemTags = computed(() => tags.value.filter(tag => tag.type === 'system'))
  const manualTags = computed(() => tags.value.filter(tag => tag.type === 'manual'))
  const computedTags = computed(() => tags.value.filter(tag => tag.type === 'computed'))

  // Actions

  /**
   * 获取标签列表
   */
  async function fetchTags(params?: TagSearchParams) {
    tagLoading.value = true
    try {
      const response = await tagApi.getTagList(
        params || {
          page: 1,
          pageSize: 999
        }
      )
      tags.value = response.data.list
      tagTotal.value = response.data.total

      // 更新缓存
      tags.value.forEach(tag => {
        tagCache.set(tag.tagId, tag)
      })
    } catch (___error) {
      throw error
    } finally {
      tagLoading.value = false
    }
  }

  /**
   * 获取标签树
   */
  async function fetchTagTree() {
    try {
      const response = await tagApi.getTagTree()
      tagTree.value = response.data
    } catch (___error) {
      throw error
    }
  }

  /**
   * 创建标签
   */
  async function createTag(data: Partial<Tag>) {
    try {
      const response = await tagApi.createTag(data)
      const newTag = response.data
      tags.value.push(newTag)
      tagCache.set(newTag.tagId, newTag)
      return newTag
    } catch (___error) {
      throw error
    }
  }

  /**
   * 更新标签
   */
  async function updateTag(tagId: string, data: Partial<Tag>) {
    try {
      const response = await tagApi.updateTag(tagId, data)
      const updatedTag = response.data

      const index = tags.value.findIndex(tag => tag.tagId === tagId)
      if (index > -1) {
        tags.value[index] = updatedTag
      }
      tagCache.set(tagId, updatedTag)

      return updatedTag
    } catch (___error) {
      throw error
    }
  }

  /**
   * 删除标签
   */
  async function deleteTag(tagId: string) {
    try {
      await tagApi.deleteTag(tagId)
      tags.value = tags.value.filter(tag => tag.tagId !== tagId)
      tagCache.delete(tagId)
    } catch (___error) {
      throw error
    }
  }

  /**
   * 合并标签
   */
  async function mergeTags(sourceTagIds: string[], targetTagId: string) {
    try {
      await tagApi.mergeTags(sourceTagIds, targetTagId)
      // 删除源标签
      tags.value = tags.value.filter(tag => !sourceTagIds.includes(tag.tagId))
      sourceTagIds.forEach(id => tagCache.delete(id))
    } catch (___error) {
      throw error
    }
  }

  /**
   * 获取标签统计
   */
  async function fetchTagStatistics(tagId?: string) {
    statisticsLoading.value = true
    try {
      const response = await tagApi.getTagStatistics(tagId)
      tagStatistics.value = response.data
    } catch (___error) {
      throw error
    } finally {
      statisticsLoading.value = false
    }
  }

  /**
   * 获取员工标签
   */
  async function fetchEmployeeTags(params: EmployeeTagSearchParams) {
    employeeTagLoading.value = true
    try {
      const response = await tagApi.getEmployeeTags(params)
      employeeTags.value = response.data.list
      employeeTagTotal.value = response.data.total
    } catch (___error) {
      throw error
    } finally {
      employeeTagLoading.value = false
    }
  }

  /**
   * 给员工添加标签
   */
  async function addEmployeeTag(data: {
    employeeId: string
    tagId: string
    score?: number
    reason?: string
    validUntil?: string
  }) {
    try {
      const response = await tagApi.addEmployeeTag(data)
      employeeTags.value.push(response.data)

      // 清除画像缓存
      portraitCache.delete(data.employeeId)

      return response.data
    } catch (___error) {
      throw error
    }
  }

  /**
   * 更新员工标签
   */
  async function updateEmployeeTag(
    id: string,
    data: {
      score?: number
      reason?: string
      validUntil?: string
      verified?: boolean
    }
  ) {
    try {
      const response = await tagApi.updateEmployeeTag(id, data)
      const index = employeeTags.value.findIndex(et => et.id === id)
      if (index > -1) {
        employeeTags.value[index] = response.data
      }
      return response.data
    } catch (___error) {
      throw error
    }
  }

  /**
   * 移除员工标签
   */
  async function removeEmployeeTag(id: string) {
    try {
      await tagApi.removeEmployeeTag(id)
      const tag = employeeTags.value.find(et => et.id === id)
      if (tag) {
        employeeTags.value = employeeTags.value.filter(et => et.id !== id)
        // 清除画像缓存
        portraitCache.delete(tag.employeeId)
      }
    } catch (___error) {
      throw error
    }
  }

  /**
   * 批量操作员工标签
   */

  async function batchOperateEmployeeTags(data: unknown) {
    try {
      const response = await tagApi.batchOperateEmployeeTags(data)

      // 清除相关员工的画像缓存
      data.employeeIds.forEach((id: string) => {
        portraitCache.delete(id)
      })

      return response.data
    } catch (___error) {
      throw error
    }
  }

  /**
   * 获取员工画像
   */
  async function fetchEmployeePortrait(employeeId: string, _forceRefresh = false) {
    // 检查缓存
    if (!forceRefresh && portraitCache.has(employeeId)) {
      currentPortrait.value = portraitCache.get(employeeId) || null
      return currentPortrait.value
    }

    portraitLoading.value = true
    try {
      const response = await tagApi.getEmployeePortrait(employeeId)
      currentPortrait.value = response.data

      // 更新缓存
      portraitCache.set(employeeId, response.data)

      return response.data
    } catch (___error) {
      throw error
    } finally {
      portraitLoading.value = false
    }
  }

  /**
   * 获取标签推荐
   */
  async function getTagRecommendations(employeeId: string, _limit = 10) {
    try {
      const response = await tagApi.getTagRecommendations(employeeId, limit)
      return response.data
    } catch (___error) {
      throw error
    }
  }

  /**
   * 计算员工相似度
   */

  async function calculateSimilarity(employeeId: string, params?: unknown) {
    try {
      const response = await tagApi.calculateSimilarity(employeeId, params)
      return response.data
    } catch (___error) {
      throw error
    }
  }

  /**
   * 触发系统标签计算
   */

  async function triggerSystemTagCalculation(params?: unknown) {
    try {
      const response = await tagApi.triggerSystemTagCalculation(params)
      return response.data
    } catch (___error) {
      throw error
    }
  }

  /**
   * 清除缓存
   */
  function clearCache() {
    tagCache.clear()
    portraitCache.clear()
  }

  /**
   * 重置状态
   */
  function reset() {
    tags.value = []
    tagTree.value = []
    tagTotal.value = 0
    employeeTags.value = []
    employeeTagTotal.value = 0
    tagStatistics.value = []
    currentPortrait.value = null
    clearCache()
  }

  return {
    // State
    tags,
    tagTree,
    tagTotal,
    tagLoading,
    employeeTags,
    employeeTagTotal,
    employeeTagLoading,
    tagStatistics,
    statisticsLoading,
    currentPortrait,
    portraitLoading,

    // Computed
    tagMap,
    tagsByCategory,
    systemTags,
    manualTags,
    computedTags,

    // Actions
    fetchTags,
    fetchTagTree,
    createTag,
    updateTag,
    deleteTag,
    mergeTags,
    fetchTagStatistics,
    fetchEmployeeTags,
    addEmployeeTag,
    updateEmployeeTag,
    removeEmployeeTag,
    batchOperateEmployeeTags,
    fetchEmployeePortrait,
    getTagRecommendations,
    calculateSimilarity,
    triggerSystemTagCalculation,
    clearCache,
    reset
  }
})
