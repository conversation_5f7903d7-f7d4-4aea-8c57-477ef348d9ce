/**
 * 认证和权限管理状态存储
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import authApi from '@/api/modules/auth'
import type { UserInfo, Role, Permission } from '@/api/types/business'

export const useAuthStore = defineStore('auth', () => {
  // ==================== 状态定义 ====================

  // 用户信息
  const userInfo = ref<UserInfo | null>(null)

  // 权限列表
  const permissions = ref<string[]>([])

  // 角色列表
  const roles = ref<Role[]>([])

  // 菜单权限
  const menuPermissions = ref<Permission[]>([])

  // 按钮权限
  const buttonPermissions = ref<string[]>([])

  // 数据权限范围
  const dataScope = ref<{
    organizationIds: string[]
    departmentIds: string[]
    selfOnly: boolean
  }>({
    organizationIds: [],
    departmentIds: [],
    selfOnly: false
  })

  // 登录状态
  const isAuthenticated = ref(false)

  // 权限加载状态
  const permissionsLoading = ref(false)

  // ==================== 计算属性 ====================

  // 是否是管理员
  const isAdmin = computed(() => {
    return roles.value.some(role => role.code === 'admin' || role.code === 'super_admin')
  })

  // 权限码Set（用于快速查找）
  const permissionSet = computed(() => {
    return new Set(permissions.value)
  })

  // 按钮权限Set
  const buttonPermissionSet = computed(() => {
    return new Set(buttonPermissions.value)
  })

  // 菜单权限Map
  const menuPermissionMap = computed(() => {
    const map = new Map<string, Permission>()
    const traverse = (items: Permission[]) => {
      items.forEach(item => {
        if (item.path) {
          map.set(item.path, item)
        }
        if (item.children) {
          traverse(item.children)
        }
      })
    }
    traverse(menuPermissions.value)
    return map
  })

  // ==================== Actions ====================

  // 设置用户信息
  const setUserInfo = (info: UserInfo | null) => {
    userInfo.value = info
    isAuthenticated.value = !!info

    if (info) {
      // 提取权限信息
      permissions.value = info.permissions || []
      roles.value = info.roles || []

      // 分离菜单权限和按钮权限
      const allPermissions: Permission[] = []
      roles.value.forEach(role => {
        allPermissions.push(...(role.permissions || []))
      })

      menuPermissions.value = allPermissions.filter(p => p.type === 'menu')
      buttonPermissions.value = allPermissions.filter(p => p.type === 'button').map(p => p.code)

      // 设置数据权限范围
      updateDataScope()
    } else {
      clearPermissions()
    }
  }

  // 更新数据权限范围
  const updateDataScope = () => {
    // 根据用户角色和部门信息计算数据权限范围
    if (isAdmin.value) {
      // 管理员有全部数据权限
      dataScope.value = {
        organizationIds: ['*'],
        departmentIds: ['*'],
        selfOnly: false
      }
    } else if (userInfo.value) {
      // 普通用户根据角色配置确定数据范围
      const scopeConfig = roles.value.find(r => r.dataScope)?.dataScope || {}

      dataScope.value = {
        organizationIds: scopeConfig.organizationIds || [userInfo.value.departmentId],
        departmentIds: scopeConfig.departmentIds || [userInfo.value.departmentId],
        selfOnly: scopeConfig.selfOnly || false
      }
    }
  }

  // 检查权限
  const hasPermission = (permission: string | string[]): boolean => {
    if (isAdmin.value) return true

    if (Array.isArray(permission)) {
      // 检查是否有任一权限
      return permission.some(p => permissionSet.value.has(p))
    }

    return permissionSet.value.has(permission)
  }

  // 检查按钮权限
  const hasButtonPermission = (permission: string): boolean => {
    if (isAdmin.value) return true
    return buttonPermissionSet.value.has(permission)
  }

  // 检查菜单权限
  const hasMenuPermission = (path: string): boolean => {
    if (isAdmin.value) return true
    return menuPermissionMap.value.has(path)
  }

  // 检查角色
  const hasRole = (roleCode: string | string[]): boolean => {
    if (Array.isArray(roleCode)) {
      return roleCode.some(code => roles.value.some(role => role.code === code))
    }
    return roles.value.some(role => role.code === roleCode)
  }

  // 检查数据权限
  const hasDataPermission = (type: 'organization' | 'department', id: string): boolean => {
    if (isAdmin.value) return true

    const scopeIds =
      type === 'organization' ? dataScope.value.organizationIds : dataScope.value.departmentIds

    // 如果包含通配符，则有全部权限
    if (scopeIds.includes('*')) return true

    // 检查是否在权限范围内
    return scopeIds.includes(id)
  }

  // 获取数据权限过滤条件
  const getDataPermissionFilter = () => {
    if (isAdmin.value) {
      return {} // 管理员无需过滤
    }

    const filter: unknown = {}

    if (dataScope.value.selfOnly && userInfo.value) {
      // 仅本人数据
      filter.employeeId = userInfo.value.employeeId
    } else {
      // 部门/组织数据
      if (!dataScope.value.organizationIds.includes('*')) {
        filter.organizationIds = dataScope.value.organizationIds
      }
      if (!dataScope.value.departmentIds.includes('*')) {
        filter.departmentIds = dataScope.value.departmentIds
      }
    }

    return filter
  }

  // 刷新权限信息
  const refreshPermissions = async () => {
    if (!userInfo.value) return

    permissionsLoading.value = true
    try {
      // 这里可以调用API重新获取权限信息
      // 暂时使用用户信息中的权限
      setUserInfo(userInfo.value)
    } catch (___error) {
      } finally {
      permissionsLoading.value = false
    }
  }

  // 清除权限信息
  const clearPermissions = () => {
    permissions.value = []
    roles.value = []
    menuPermissions.value = []
    buttonPermissions.value = []
    dataScope.value = {
      organizationIds: [],
      departmentIds: [],
      selfOnly: false
    }
  }

  // 登出清理
  const logout = () => {
    setUserInfo(null)
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  }

  return {
    // 状态
    userInfo,
    permissions,
    roles,
    menuPermissions,
    buttonPermissions,
    dataScope,
    isAuthenticated,
    permissionsLoading,

    // 计算属性
    isAdmin,
    permissionSet,
    buttonPermissionSet,
    menuPermissionMap,

    // Actions
    setUserInfo,
    hasPermission,
    hasButtonPermission,
    hasMenuPermission,
    hasRole,
    hasDataPermission,
    getDataPermissionFilter,
    refreshPermissions,
    logout
  }
})

// 权限指令辅助函数
export const checkPermission = (permission: string | string[]): boolean => {
  const authStore = useAuthStore()
  return authStore.hasPermission(permission)
}

export const checkButtonPermission = (permission: string): boolean => {
  const authStore = useAuthStore()
  return authStore.hasButtonPermission(permission)
}

export const checkRole = (roleCode: string | string[]): boolean => {
  const authStore = useAuthStore()
  return authStore.hasRole(roleCode)
}
