/**
 * 工作流设计器状态管理
 * @module stores/modules/workflowDesigner
 */

import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'
import type BpmnModeler from 'bpmn-js/lib/Modeler'
import { workflowApi } from '@/workflow/api'
import type { ProcessDefinition, Model } from '@/workflow/types'
import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 设计器状态
 */
interface DesignerState {
  isDirty: boolean
  isValid: boolean
  isSaving: boolean
  isDeploying: boolean
  validationErrors: string[]
}

/**
 * 设计器历史记录
 */
interface HistoryState {
  canUndo: boolean
  canRedo: boolean
  undoStack: string[]
  redoStack: string[]
}

/**
 * 工作流设计器Store
 */
export const useWorkflowDesignerStore = defineStore('workflowDesigner', () => {
  // BPMN Modeler实例
  const modeler = ref<BpmnModeler | null>(null)

  // 当前编辑的模型
  const currentModel = ref<Model | null>(null)

  // 当前编辑的流程定义
  const currentDefinition = ref<ProcessDefinition | null>(null)

  // 设计器状态
  const state = reactive<DesignerState>({
    isDirty: false,
    isValid: true,
    isSaving: false,
    isDeploying: false,
    validationErrors: []
  })

  // 历史记录状态
  const history = reactive<HistoryState>({
    canUndo: false,
    canRedo: false,
    undoStack: [],
    redoStack: []
  })

  // 设计器配置
  const config = reactive({
    gridEnabled: true,
    snapToGrid: true,
    gridSize: 10,
    miniMapEnabled: true,
    propertiesPanelEnabled: true,
    keyboardShortcutsEnabled: true
  })

  // 选中的元素
  const selectedElements = ref<unknown[]>([])

  // 当前XML
  const currentXML = ref<string>('')

  // 计算属性
  const hasChanges = computed(() => state.isDirty)
  const canSave = computed(() => state.isDirty && state.isValid && !state.isSaving)
  const canDeploy = computed(() => state.isValid && !state.isDeploying)

  // 设置Modeler实例
  function setModeler(instance: BpmnModeler) {
    modeler.value = instance

    // 监听变更事件
    instance.on('commandStack.changed', () => {
      updateDirtyState()
      updateHistoryState()
    })

    // 监听选择事件

    instance.on('selection.changed', (event: unknown) => {
      selectedElements.value = event.newSelection
    })

    // 监听元素变更
    instance.on('element.changed', () => {
      validateDiagram()
    })
  }

  // 加载模型
  async function loadModel(modelId: string) {
    try {
      const { data } = await workflowApi.getModel(modelId)
      currentModel.value = data

      // 加载XML到设计器
      if (modeler.value && data.metaInfo) {
        const metaInfo = JSON.parse(data.metaInfo)
        if (metaInfo.xml) {
          await importXML(metaInfo.xml)
        }
      }

      state.isDirty = false
      return data
    } catch (___error) {
      ElMessage.error('加载模型失败')
      throw error
    }
  }

  // 加载流程定义
  async function loadDefinition(definitionId: string) {
    try {
      const { data: definition } = await workflowApi.getProcessDefinition(definitionId)
      currentDefinition.value = definition

      // 获取流程XML
      const { data: xml } = await workflowApi.getProcessDefinitionXml(definitionId)

      // 加载XML到设计器
      if (modeler.value) {
        await importXML(xml)
      }

      state.isDirty = false
      return definition
    } catch (___error) {
      ElMessage.error('加载流程定义失败')
      throw error
    }
  }

  // 导入XML
  async function importXML(xml: string) {
    if (!modeler.value) {
      throw new Error('Modeler未初始化')
    }

    try {
      const result = await modeler.value.importXML(xml)
      currentXML.value = xml

      if (result.warnings && result.warnings.length > 0) {
        ElMessage.warning('导入XML时出现警告，请检查流程定义')
      }

      // 重置历史记录
      history.undoStack = []
      history.redoStack = []
      updateHistoryState()

      // 验证流程图
      validateDiagram()

      return result
    } catch (___error) {
      ElMessage.error('导入XML失败，请检查格式')
      throw error
    }
  }

  // 导出XML
  async function exportXML(_format = false): Promise<string> {
    if (!modeler.value) {
      throw new Error('Modeler未初始化')
    }

    try {
      const result = await modeler.value.saveXML({ format })
      currentXML.value = result.xml
      return result.xml
    } catch (___error) {
      ElMessage.error('导出XML失败')
      throw error
    }
  }

  // 导出SVG
  async function exportSVG(): Promise<string> {
    if (!modeler.value) {
      throw new Error('Modeler未初始化')
    }

    try {
      const result = await modeler.value.saveSVG()
      return result.svg
    } catch (___error) {
      ElMessage.error('导出SVG失败')
      throw error
    }
  }

  // 保存模型
  async function saveModel() {
    if (!currentModel.value || !canSave.value) {
      return
    }

    state.isSaving = true

    try {
      const xml = await exportXML()

      const updateData = {
        name: currentModel.value.name,
        category: currentModel.value.category,
        content: xml,
        metaInfo: JSON.stringify({
          xml,
          lastModified: new Date().toISOString()
        })
      }

      const { data } = await workflowApi.updateModel(currentModel.value.id, updateData)
      currentModel.value = data
      state.isDirty = false

      ElMessage.success('保存成功')
      return data
    } catch (___error) {
      ElMessage.error('保存模型失败')
      throw error
    } finally {
      state.isSaving = false
    }
  }

  // 部署流程
  async function deployProcess(name?: string, category?: string) {
    if (!canDeploy.value) {
      return
    }

    const confirmed = await ElMessageBox.confirm(
      '确定要部署此流程吗？部署后将立即生效。',
      '部署确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).catch(() => false)

    if (!confirmed) {
      return
    }

    state.isDeploying = true

    try {
      const xml = await exportXML()
      const blob = new Blob([xml], { type: 'text/xml' })
      const file = new File([blob], 'process.bpmn20.xml', { type: 'text/xml' })

      const { data } = await workflowApi.deployProcess(file, name, category)

      ElMessage.success('流程部署成功')
      state.isDirty = false

      return data
    } catch (___error) {
      ElMessage.error('部署流程失败')
      throw error
    } finally {
      state.isDeploying = false
    }
  }

  // 验证流程图
  function validateDiagram() {
    if (!modeler.value) {
      return
    }

    const errors: string[] = []

    try {
      const elementRegistry = modeler.value.get('elementRegistry')
      const elements = elementRegistry.getAll()

      // 检查是否有开始事件

      const startEvents = elements.filter((e: unknown) => e.type === 'bpmn:StartEvent')
      if (startEvents.length === 0) {
        errors.push('流程必须包含至少一个开始事件')
      }

      // 检查是否有结束事件

      const endEvents = elements.filter((e: unknown) => e.type === 'bpmn:EndEvent')
      if (endEvents.length === 0) {
        errors.push('流程必须包含至少一个结束事件')
      }

      // 检查用户任务是否配置了审批人

      const userTasks = elements.filter((e: unknown) => e.type === 'bpmn:UserTask')

      userTasks.forEach((task: unknown) => {
        const bo = task.businessObject
        if (!bo.assignee && !bo.candidateUsers && !bo.candidateGroups) {
          errors.push(`用户任务"${bo.name || bo.id}"未配置审批人`)
        }
      })

      // 检查网关的输出连线

      const gateways = elements.filter(
        (e: unknown) => e.type === 'bpmn:ExclusiveGateway' || e.type === 'bpmn:ParallelGateway'
      )

      gateways.forEach((gateway: unknown) => {
        if (!gateway.outgoing || gateway.outgoing.length < 2) {
          errors.push(`网关"${gateway.businessObject.name || gateway.id}"至少需要两条输出连线`)
        }
      })

      state.validationErrors = errors
      state.isValid = errors.length === 0
    } catch (___error) {
      state.isValid = false
    }
  }

  // 更新脏状态
  function updateDirtyState() {
    if (!modeler.value) {
      return
    }

    const commandStack = modeler.value.get('commandStack')
    state.isDirty = commandStack.canUndo()
  }

  // 更新历史状态
  function updateHistoryState() {
    if (!modeler.value) {
      return
    }

    const commandStack = modeler.value.get('commandStack')
    history.canUndo = commandStack.canUndo()
    history.canRedo = commandStack.canRedo()
  }

  // 撤销
  function undo() {
    if (!modeler.value || !history.canUndo) {
      return
    }

    const commandStack = modeler.value.get('commandStack')
    commandStack.undo()
  }

  // 重做
  function redo() {
    if (!modeler.value || !history.canRedo) {
      return
    }

    const commandStack = modeler.value.get('commandStack')
    commandStack.redo()
  }

  // 缩放
  function zoom(scale?: number | 'fit-viewport') {
    if (!modeler.value) {
      return
    }

    const canvas = modeler.value.get('canvas')
    canvas.zoom(scale)
  }

  // 重置缩放
  function resetZoom() {
    zoom(1)
  }

  // 适应视口
  function fitViewport() {
    zoom('fit-viewport')
  }

  // 切换网格
  function toggleGrid() {
    config.gridEnabled = !config.gridEnabled

    if (modeler.value) {
      const canvas = modeler.value.get('canvas')

      // 获取画布容器
      const canvasContainer = canvas.getContainer()

      if (config.gridEnabled) {
        // 启用网格显示
        enableGridDisplay(canvasContainer)
      } else {
        // 禁用网格显示
        disableGridDisplay(canvasContainer)
      }

      // 更新吸附功能
      updateSnapToGrid()
    }
  }

  // 启用网格显示
  function enableGridDisplay(container: HTMLElement) {
    // 移除现有网格
    disableGridDisplay(container)

    // 创建网格背景
    const gridSize = config.gridSize
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
    svg.setAttribute('class', 'bpmn-grid-background')
    svg.style.position = 'absolute'
    svg.style.top = '0'
    svg.style.left = '0'
    svg.style.width = '100%'
    svg.style.height = '100%'
    svg.style.pointerEvents = 'none'
    svg.style.zIndex = '0'

    // 创建网格模式
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs')
    const pattern = document.createElementNS('http://www.w3.org/2000/svg', 'pattern')
    pattern.setAttribute('id', 'bpmn-grid-pattern')
    pattern.setAttribute('width', gridSize.toString())
    pattern.setAttribute('height', gridSize.toString())
    pattern.setAttribute('patternUnits', 'userSpaceOnUse')

    // 创建网格点
    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle')
    circle.setAttribute('cx', '1')
    circle.setAttribute('cy', '1')
    circle.setAttribute('r', '0.5')
    circle.setAttribute('fill', '#ddd')
    circle.setAttribute('opacity', '0.6')

    pattern.appendChild(circle)
    defs.appendChild(pattern)
    svg.appendChild(defs)

    // 创建网格矩形
    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
    rect.setAttribute('width', '100%')
    rect.setAttribute('height', '100%')
    rect.setAttribute('fill', 'url(#bpmn-grid-pattern)')

    svg.appendChild(rect)

    // 将网格插入到画布容器的最底层
    container.insertBefore(svg, container.firstChild)
  }

  // 禁用网格显示
  function disableGridDisplay(container: HTMLElement) {
    const existingGrid = container.querySelector('.bpmn-grid-background')
    if (existingGrid) {
      existingGrid.remove()
    }
  }

  // 更新吸附到网格功能
  function updateSnapToGrid() {
    if (modeler.value && config.snapToGrid && config.gridEnabled) {
      // 启用吸附功能
      const modeling = modeler.value.get('modeling')
      const eventBus = modeler.value.get('eventBus')

      // 监听元素移动事件，实现吸附效果

      eventBus.on('shape.move.end', (event: unknown) => {
        const shape = event.shape
        const newPosition = {
          x: Math.round(shape.x / config.gridSize) * config.gridSize,
          y: Math.round(shape.y / config.gridSize) * config.gridSize
        }

        // 如果位置发生变化，则更新元素位置
        if (newPosition.x !== shape.x || newPosition.y !== shape.y) {
          modeling.moveShape(shape, newPosition)
        }
      })
    }
  }

  // 切换小地图
  function toggleMiniMap() {
    config.miniMapEnabled = !config.miniMapEnabled

    // 通过事件通知视图层更新小地图显示状态
    if (modeler.value) {
      const eventBus = modeler.value.get('eventBus')
      eventBus.fire('minimap.toggle', {
        enabled: config.miniMapEnabled
      })
    }
  }

  // 重置状态
  function resetState() {
    modeler.value = null
    currentModel.value = null
    currentDefinition.value = null
    selectedElements.value = []
    currentXML.value = ''

    Object.assign(state, {
      isDirty: false,
      isValid: true,
      isSaving: false,
      isDeploying: false,
      validationErrors: []
    })

    Object.assign(history, {
      canUndo: false,
      canRedo: false,
      undoStack: [],
      redoStack: []
    })
  }

  return {
    // 状态
    modeler,
    currentModel,
    currentDefinition,
    state,
    history,
    config,
    selectedElements,
    currentXML,

    // 计算属性
    hasChanges,
    canSave,
    canDeploy,

    // 方法
    setModeler,
    loadModel,
    loadDefinition,
    importXML,
    exportXML,
    exportSVG,
    saveModel,
    deployProcess,
    validateDiagram,
    undo,
    redo,
    zoom,
    resetZoom,
    fitViewport,
    toggleGrid,
    toggleMiniMap,
    enableGridDisplay,
    disableGridDisplay,
    updateSnapToGrid,
    resetState
  }
})
