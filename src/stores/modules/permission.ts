/**
 * 权限状态管理
 * 管理动态路由、菜单权限、按钮权限等
 */

import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from './user'
import type { PermissionState, Permission, MenuItem, DataPermission } from '../types'

// 静态路由（不需要权限的路由）
const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { hidden: true, noAuth: true }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { hidden: true, noAuth: true }
  }
]

// 动态路由（需要根据权限动态加载）
const asyncRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layouts/MainLayout.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/employee',
    name: 'Employee',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { title: '员工管理', icon: 'people' },
    children: [
      {
        path: 'list',
        name: 'EmployeeList',
        component: () => import('@/views/employee/list/index.vue'),
        meta: { title: '员工列表', permission: 'employee:list' }
      },
      {
        path: 'detail/:id',
        name: 'EmployeeDetail',
        component: () => import('@/views/employee/detail/index.vue'),
        meta: { title: '员工详情', hidden: true, permission: 'employee:detail' }
      }
    ]
  },
  {
    path: '/organization',
    name: 'Organization',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { title: '组织管理', icon: 'tree' },
    children: [
      {
        path: 'structure',
        name: 'OrgStructure',
        component: () => import('@/views/organization/index.vue'),
        meta: { title: '组织架构', permission: 'organization:list' }
      },
      {
        path: 'position',
        name: 'Position',
        component: () => import('@/views/position/PositionManagement.vue'),
        meta: { title: '岗位管理', permission: 'position:list' }
      },
      {
        path: 'staffing',
        name: 'Staffing',
        component: () => import('@/views/establishment/EstablishmentManagement.vue'),
        meta: { title: '编制管理', permission: 'staffing:list' }
      }
    ]
  }
]

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routes: [],
    dynamicRoutes: [],
    permissions: new Map(),
    dataPermissions: [],
    menuList: []
  }),

  getters: {
    // 获取所有路由
    allRoutes: state => constantRoutes.concat(state.dynamicRoutes),

    // 获取菜单列表
    menus: state => state.menuList,

    // 获取权限Map
    permissionMap: state => state.permissions,

    // 判断是否有菜单权限
    hasMenuPermission: state => {
      return (path: string): boolean => {
        const findMenu = (menus: MenuItem[], targetPath: string): boolean => {
          for (const menu of menus) {
            if (menu.path === targetPath) return true
            if (menu.children && findMenu(menu.children, targetPath)) return true
          }
          return false
        }
        return findMenu(state.menuList, path)
      }
    },

    // 判断是否有按钮权限
    hasButtonPermission: () => {
      const userStore = useUserStore()
      return (permission: string): boolean => {
        return userStore.hasPermission(permission)
      }
    },

    // 判断是否有数据权限
    hasDataPermission: state => {
      return (resource: string, action: string): boolean => {
        return state.dataPermissions.some(dp => dp.resource === resource && dp.action === action)
      }
    }
  },

  actions: {
    /**
     * 生成路由
     */
    async generateRoutes() {
      const userStore = useUserStore()
      const permissions = userStore.permissions

      // 过滤有权限的路由
      const accessedRoutes = this.filterAsyncRoutes(asyncRoutes, permissions)

      // 设置动态路由
      this.dynamicRoutes = accessedRoutes
      this.routes = constantRoutes.concat(accessedRoutes)

      // 生成菜单
      this.menuList = this.generateMenuList(accessedRoutes)

      return accessedRoutes
    },

    /**
     * 过滤异步路由
     */
    filterAsyncRoutes(routes: RouteRecordRaw[], permissions: string[]): RouteRecordRaw[] {
      const res: RouteRecordRaw[] = []

      routes.forEach(route => {
        const tmp = { ...route }

        if (this.hasRoutePermission(tmp, permissions)) {
          if (tmp.children) {
            tmp.children = this.filterAsyncRoutes(tmp.children, permissions)
          }
          res.push(tmp)
        }
      })

      return res
    },

    /**
     * 判断是否有路由权限
     */
    hasRoutePermission(route: RouteRecordRaw, permissions: string[]): boolean {
      if (route.meta?.permission) {
        return permissions.includes(route.meta.permission as string)
      }
      return true
    },

    /**
     * 生成菜单列表
     */
    generateMenuList(routes: RouteRecordRaw[]): MenuItem[] {
      const res: MenuItem[] = []

      routes.forEach(route => {
        // 隐藏的路由不生成菜单
        if (route.meta?.hidden) return

        const menu: MenuItem = {
          id: route.name as string,
          path: route.path,
          name: route.name as string,
          icon: route.meta?.icon as string,
          meta: {
            title: route.meta?.title as string,
            icon: route.meta?.icon as string,
            hidden: route.meta?.hidden as boolean,
            alwaysShow: route.meta?.alwaysShow as boolean,
            roles: route.meta?.roles as string[],
            noCache: route.meta?.noCache as boolean,
            breadcrumb: route.meta?.breadcrumb !== false
          }
        }

        if (route.children && route.children.length > 0) {
          menu.children = this.generateMenuList(route.children)
        }

        res.push(menu)
      })

      return res
    },

    /**
     * 设置权限
     */
    setPermissions(permissions: Permission[]) {
      this.permissions.clear()
      permissions.forEach(perm => {
        this.permissions.set(perm.code, perm)
      })
    },

    /**
     * 设置数据权限
     */
    setDataPermissions(dataPermissions: DataPermission[]) {
      this.dataPermissions = dataPermissions
    },

    /**
     * 重置权限
     */
    resetPermission() {
      this.routes = []
      this.dynamicRoutes = []
      this.permissions.clear()
      this.dataPermissions = []
      this.menuList = []
    }
  }
})
