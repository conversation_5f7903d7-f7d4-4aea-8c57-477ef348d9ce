import { defineStore } from 'pinia'
import { ref } from 'vue'
import type {
  ScheduleTask,
  ScheduleSearchParams,
  ScheduleListResult,
  TaskExecution,
  ExecutionSearchParams,
  ExecutionListResult
} from '@/types/schedule'
import request from '@/utils/request'

export const useScheduleStore = defineStore('schedule', () => {
  // 状态
  const tasks = ref<ScheduleTask[]>([])
  const currentTask = ref<ScheduleTask | null>(null)
  const executions = ref<TaskExecution[]>([])

  // 任务管理
  const fetchScheduleTasks = async (params: ScheduleSearchParams): Promise<ScheduleListResult> => {
    const { data } = await request.get('/api/schedule/tasks', { params })
    tasks.value = data.list
    return data
  }

  const fetchAvailableReports = async () => {
    const { data } = await request.get('/api/reports/available')
    return data
  }

  const getTask = async (taskId: string): Promise<ScheduleTask> => {
    const { data } = await request.get(`/api/schedule/tasks/${taskId}`)
    currentTask.value = data
    return data
  }

  const createTask = async (task: Partial<ScheduleTask>): Promise<ScheduleTask> => {
    const { data } = await request.post('/api/schedule/tasks', task)
    return data
  }

  const updateTask = async (taskId: string, updates: Partial<ScheduleTask>): Promise<void> => {
    await request.put(`/api/schedule/tasks/${taskId}`, updates)
  }

  const deleteTask = async (taskId: string): Promise<void> => {
    await request.delete(`/api/schedule/tasks/${taskId}`)
  }

  const toggleTaskStatus = async (taskId: string, enabled: boolean): Promise<void> => {
    await request.put(`/api/schedule/tasks/${taskId}/toggle`, { enabled })
  }

  // 任务执行
  const executeTask = async (taskId: string): Promise<TaskExecution> => {
    const { data } = await request.post(`/api/schedule/tasks/${taskId}/execute`)
    return data
  }

  const cancelExecution = async (executionId: string): Promise<void> => {
    await request.post(`/api/schedule/executions/${executionId}/cancel`)
  }

  const fetchExecutions = async (params: ExecutionSearchParams): Promise<ExecutionListResult> => {
    const { data } = await request.get('/api/schedule/executions', { params })
    executions.value = data.list
    return data
  }

  const getExecution = async (executionId: string): Promise<TaskExecution> => {
    const { data } = await request.get(`/api/schedule/executions/${executionId}`)
    return data
  }

  const getExecutionLogs = async (executionId: string) => {
    const { data } = await request.get(`/api/schedule/executions/${executionId}/logs`)
    return data
  }

  // 其他功能
  const testPush = async (taskId: string): Promise<void> => {
    await request.post(`/api/schedule/tasks/${taskId}/test-push`)
  }

  const validateCron = async (expression: string): Promise<boolean> => {
    const { data } = await request.post('/api/schedule/validate-cron', { expression })
    return data.valid
  }

  const getNextRunTimes = async (expression: string, count: number = 5): Promise<Date[]> => {
    const { data } = await request.post('/api/schedule/next-run-times', { expression, count })
    return data.map((dateStr: string) => new Date(dateStr))
  }

  return {
    // 状态
    tasks,
    currentTask,
    executions,

    // 任务管理
    fetchScheduleTasks,
    fetchAvailableReports,
    getTask,
    createTask,
    updateTask,
    deleteTask,
    toggleTaskStatus,

    // 任务执行
    executeTask,
    cancelExecution,
    fetchExecutions,
    getExecution,
    getExecutionLogs,

    // 其他功能
    testPush,
    validateCron,
    getNextRunTimes
  }
})
