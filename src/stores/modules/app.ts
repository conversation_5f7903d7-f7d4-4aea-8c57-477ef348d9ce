/**
 * 应用状态管理
 * 管理应用级别的全局状态
 */

import { defineStore } from 'pinia'
import type { AppState, TagView, AppSettings } from '../types'

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    // 侧边栏状态
    sidebar: {
      opened: localStorage.getItem('sidebarStatus') !== 'closed',
      withoutAnimation: false
    },
    // 设备类型
    device: 'desktop',
    // 主题
    theme: (localStorage.getItem('theme') as 'light' | 'dark') || 'light',
    // 布局模式
    layout: (localStorage.getItem('layout') as 'default' | 'top-menu' | 'mix') || 'default',
    // 标签页
    tagViews: [],
    // 全局loading
    loading: false,
    // 系统设置
    settings: {
      title: '杭科院人事管理系统',
      logo: '/logo.svg',
      showLogo: true,
      showBreadcrumb: true,
      showTagsView: true,
      fixedHeader: true,
      sidebarLogo: true,
      errorLog: []
    }
  }),

  getters: {
    // 获取侧边栏状态
    sidebarOpened: _state => state.sidebar.opened,

    // 是否为移动设备
    isMobile: _state => state.device === 'mobile',

    // 获取主题
    currentTheme: _state => state.theme,

    // 获取布局模式
    currentLayout: _state => state.layout,

    // 获取已访问的标签页
    visitedViews: _state => state.tagViews,

    // 获取缓存的标签页
    cachedViews: _state => state.tagViews.filter(view => !view.meta?.noCache)
  },

  actions: {
    // 切换侧边栏
    toggleSidebar(withoutAnimation = false) {
      this.sidebar.opened = !this.sidebar.opened
      this.sidebar.withoutAnimation = withoutAnimation

      if (this.sidebar.opened) {
        localStorage.setItem('sidebarStatus', 'opened')
      } else {
        localStorage.setItem('sidebarStatus', 'closed')
      }
    },

    // 关闭侧边栏
    closeSidebar(withoutAnimation = false) {
      this.sidebar.opened = false
      this.sidebar.withoutAnimation = withoutAnimation
      localStorage.setItem('sidebarStatus', 'closed')
    },

    // 设置设备类型
    setDevice(device: 'desktop' | 'mobile') {
      this.device = device

      // 移动设备自动关闭侧边栏
      if (device === 'mobile') {
        this.closeSidebar(true)
      }
    },

    // 设置主题
    setTheme(theme: 'light' | 'dark') {
      this.theme = theme
      localStorage.setItem('theme', theme)

      // 更新HTML根元素的class
      document.documentElement.classList.remove('light', 'dark')
      document.documentElement.classList.add(theme)
    },

    // 设置布局模式
    setLayout(layout: 'default' | 'top-menu' | 'mix') {
      this.layout = layout
      localStorage.setItem('layout', layout)
    },

    // 添加标签页
    addTagView(view: TagView) {
      // 检查是否已存在
      const existView = this.tagViews.find(v => v.path === view.path)
      if (existView) {
        // 更新查询参数
        existView.fullPath = view.fullPath
        existView.query = view.query
        return
      }

      // 添加新标签页
      this.tagViews.push({
        ...view,
        title: view.meta?.title || view.title || 'no-name'
      })
    },

    // 删除标签页
    delTagView(view: TagView) {
      const index = this.tagViews.findIndex(v => v.path === view.path)
      if (index > -1) {
        this.tagViews.splice(index, 1)
      }
    },

    // 删除其他标签页
    delOthersTagViews(view: TagView) {
      this.tagViews = this.tagViews.filter(v => {
        return v.meta?.affix || v.path === view.path
      })
    },

    // 删除所有标签页
    delAllTagViews() {
      // 只保留固定的标签页
      this.tagViews = this.tagViews.filter(tag => tag.meta?.affix)
    },

    // 更新标签页
    updateTagView(view: TagView) {
      const index = this.tagViews.findIndex(v => v.path === view.path)
      if (index > -1) {
        this.tagViews[index] = { ...this.tagViews[index], ...view }
      }
    },

    // 设置loading状态
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // 更新设置
    updateSettings(settings: Partial<AppSettings>) {
      this.settings = { ...this.settings, ...settings }
    },

    // 添加错误日志

    addErrorLog(error: unknown) {
      this.settings.errorLog.push({
        time: new Date().toISOString(),
        error: error.toString(),
        stack: error.stack || '',
        info: error.info || ''
      })
    },

    // 清空错误日志
    clearErrorLog() {
      this.settings.errorLog = []
    }
  },

  // 持久化配置
  persist: {
    enabled: true,
    paths: ['sidebar.opened', 'theme', 'layout', 'settings']
  }
})
