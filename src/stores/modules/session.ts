/**
 * 会话管理状态
 * 管理登录状态持久化、会话超时、自动续期等
 */

import { defineStore } from 'pinia'
import { useUserStore } from './user'
import { ElMessageBox, ElMessage } from 'element-plus'
import router from '@/router'

interface SessionState {
  // 会话ID
  sessionId: string | null
  // 会话开始时间
  sessionStartTime: number | null
  // 最后活动时间
  lastActivityTime: number | null
  // 会话超时时间（毫秒）
  sessionTimeout: number
  // 是否自动续期
  autoRenew: boolean
  // 续期倒计时定时器
  renewTimer: number | null
  // 超时倒计时定时器
  timeoutTimer: number | null
  // 是否显示超时警告
  showTimeoutWarning: boolean
  // 警告显示时间（会话剩余多少时间时显示警告）
  warningTime: number
  // 是否记住登录状态
  rememberMe: boolean
  // 设备信息
  deviceInfo: {
    browser: string
    os: string
    device: string
    ip?: string
  } | null
  // 多设备登录管理
  activeDevices: Array<{
    sessionId: string

    deviceInfo: unknown
    loginTime: number
    lastActivityTime: number
  }>
  // 节流定时器
  _throttleTimer?: number | null
}

export const useSessionStore = defineStore('session', {
  state: (): SessionState => ({
    sessionId: localStorage.getItem('sessionId'),
    sessionStartTime: null,
    lastActivityTime: null,
    sessionTimeout: 30 * 60 * 1000, // 默认30分钟
    autoRenew: true,
    renewTimer: null,
    timeoutTimer: null,
    showTimeoutWarning: false,
    warningTime: 5 * 60 * 1000, // 剩余5分钟时警告
    rememberMe: localStorage.getItem('rememberMe') === 'true',
    deviceInfo: null,
    activeDevices: []
  }),

  getters: {
    // 会话是否有效
    isSessionValid: state => {
      if (!state.sessionId || !state.lastActivityTime) return false

      const now = Date.now()
      const elapsed = now - state.lastActivityTime
      return elapsed < state.sessionTimeout
    },

    // 会话剩余时间（毫秒）
    remainingTime: state => {
      if (!state.lastActivityTime) return 0

      const now = Date.now()
      const elapsed = now - state.lastActivityTime
      const remaining = state.sessionTimeout - elapsed

      return Math.max(0, remaining)
    },

    // 会话剩余时间（格式化）
    remainingTimeFormatted(): string {
      const remaining = this.remainingTime
      const minutes = Math.floor(remaining / 60000)
      const seconds = Math.floor((remaining % 60000) / 1000)

      return `${minutes}分${seconds}秒`
    },

    // 是否需要显示警告
    shouldShowWarning: state => {
      const remaining = state.sessionTimeout - (Date.now() - (state.lastActivityTime || 0))
      return remaining > 0 && remaining <= state.warningTime
    }
  },

  actions: {
    /**
     * 初始化会话
     */
    initSession(sessionId?: string) {
      const now = Date.now()

      this.sessionId = sessionId || this.generateSessionId()
      this.sessionStartTime = now
      this.lastActivityTime = now

      // 保存会话ID
      localStorage.setItem('sessionId', this.sessionId)

      // 获取设备信息
      this.detectDeviceInfo()

      // 启动会话监控
      this.startSessionMonitor()

      // 记录活动设备
      this.addActiveDevice()
    },

    /**
     * 生成会话ID
     */
    generateSessionId(): string {
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    },

    /**
     * 检测设备信息
     */
    detectDeviceInfo() {
      const ua = navigator.userAgent

      // 检测浏览器
      let browser = 'Unknown'
      if (ua.includes('Chrome')) browser = 'Chrome'
      else if (ua.includes('Firefox')) browser = 'Firefox'
      else if (ua.includes('Safari')) browser = 'Safari'
      else if (ua.includes('Edge')) browser = 'Edge'

      // 检测操作系统
      let os = 'Unknown'
      if (ua.includes('Windows')) os = 'Windows'
      else if (ua.includes('Mac')) os = 'macOS'
      else if (ua.includes('Linux')) os = 'Linux'
      else if (ua.includes('Android')) os = 'Android'
      else if (ua.includes('iOS')) os = 'iOS'

      // 检测设备类型
      let device = 'Desktop'
      if (/Mobile|Android|iPhone|iPad/.test(ua)) {
        device = 'Mobile'
      } else if (/Tablet|iPad/.test(ua)) {
        device = 'Tablet'
      }

      this.deviceInfo = {
        browser,
        os,
        device
      }
    },

    /**
     * 添加活动设备
     */
    addActiveDevice() {
      if (!this.sessionId || !this.deviceInfo) return

      const device = {
        sessionId: this.sessionId,
        deviceInfo: this.deviceInfo,
        loginTime: Date.now(),
        lastActivityTime: Date.now()
      }

      // 检查是否已存在
      const index = this.activeDevices.findIndex(d => d.sessionId === this.sessionId)
      if (index >= 0) {
        this.activeDevices[index] = device
      } else {
        this.activeDevices.push(device)
      }

      // 限制最多记录10个设备
      if (this.activeDevices.length > 10) {
        this.activeDevices = this.activeDevices.slice(-10)
      }
    },

    /**
     * 启动会话监控
     */
    startSessionMonitor() {
      // 清除已有定时器
      this.stopSessionMonitor()

      // 监听用户活动
      this.setupActivityListeners()

      // 启动超时检查
      this.timeoutTimer = window.setInterval(() => {
        this.checkSessionTimeout()
      }, 1000) // 每秒检查一次
    },

    /**
     * 停止会话监控
     */
    stopSessionMonitor() {
      if (this.timeoutTimer) {
        clearInterval(this.timeoutTimer)
        this.timeoutTimer = null
      }

      if (this.renewTimer) {
        clearTimeout(this.renewTimer)
        this.renewTimer = null
      }

      this.removeActivityListeners()
    },

    /**
     * 设置活动监听器
     */
    setupActivityListeners() {
      const events = ['mousedown', 'keydown', 'scroll', 'touchstart', 'click']

      events.forEach(event => {
        document.addEventListener(event, this.updateActivity, { passive: true })
      })
    },

    /**
     * 移除活动监听器
     */
    removeActivityListeners() {
      const events = ['mousedown', 'keydown', 'scroll', 'touchstart', 'click']

      events.forEach(event => {
        document.removeEventListener(event, this.updateActivity)
      })
    },

    /**
     * 更新活动时间
     */
    updateActivity() {
      // 使用闭包管理节流定时器
      if (!this._throttleTimer) {
        this._throttleTimer = window.setTimeout(() => {
          this.lastActivityTime = Date.now()
          this.showTimeoutWarning = false
          this._throttleTimer = null

          // 更新活动设备信息
          const device = this.activeDevices.find(d => d.sessionId === this.sessionId)
          if (device) {
            device.lastActivityTime = Date.now()
          }
        }, 1000) // 节流，1秒内只更新一次
      }
    },

    /**
     * 检查会话超时
     */
    async checkSessionTimeout() {
      if (!this.isSessionValid) {
        // 会话已超时
        this.handleSessionTimeout()
        return
      }

      // 检查是否需要显示警告
      if (this.shouldShowWarning && !this.showTimeoutWarning) {
        this.showTimeoutWarning = true
        this.showTimeoutWarningDialog()
      }
    },

    /**
     * 显示超时警告对话框
     */
    async showTimeoutWarningDialog() {
      try {
        await ElMessageBox.confirm(
          `您的会话将在 ${this.remainingTimeFormatted} 后过期，是否继续操作？`,
          '会话即将过期',
          {
            confirmButtonText: '继续操作',
            cancelButtonText: '退出登录',
            type: 'warning',
            center: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showClose: false
          }
        )

        // 用户选择继续，刷新会话
        await this.renewSession()
        this.showTimeoutWarning = false
      } catch {
        // 用户选择退出
        const userStore = useUserStore()
        await userStore.logout()
      }
    },

    /**
     * 处理会话超时
     */
    async handleSessionTimeout() {
      this.stopSessionMonitor()

      // 清除会话
      this.clearSession()

      const userStore = useUserStore()
      userStore.resetUser()

      // 显示超时提示
      ElMessage.warning('会话已过期，请重新登录')

      // 跳转到登录页
      router.push({
        path: '/login',
        query: { redirect: router.currentRoute.value.fullPath }
      })
    },

    /**
     * 续期会话
     */
    async renewSession() {
      const userStore = useUserStore()

      try {
        // 如果有refreshToken，尝试刷新token
        if (userStore.refreshToken) {
          await userStore.refreshToken()
        }

        // 更新活动时间
        this.lastActivityTime = Date.now()

        ElMessage.success('会话已续期')
      } catch (___error) {
        ElMessage.error('会话续期失败，请重新登录')
        this.handleSessionTimeout()
      }
    },

    /**
     * 设置会话超时时间
     */
    setSessionTimeout(timeout: number) {
      this.sessionTimeout = timeout

      // 重启监控
      if (this.sessionId) {
        this.startSessionMonitor()
      }
    },

    /**
     * 设置记住登录状态
     */
    setRememberMe(remember: boolean) {
      this.rememberMe = remember

      if (remember) {
        localStorage.setItem('rememberMe', 'true')
      } else {
        localStorage.removeItem('rememberMe')
      }
    },

    /**
     * 恢复会话
     */
    async restoreSession() {
      const sessionId = localStorage.getItem('sessionId')
      const rememberMe = localStorage.getItem('rememberMe') === 'true'

      if (!sessionId) return false

      const userStore = useUserStore()

      // 如果有token，尝试恢复会话
      if (userStore.token) {
        try {
          // 获取用户信息验证token有效性
          await userStore.getUserInfo()

          // 初始化会话
          this.initSession(sessionId)
          this.rememberMe = rememberMe

          return true
        } catch (___error) {
          this.clearSession()
          return false
        }
      }

      // 如果记住登录且有refreshToken，尝试刷新
      if (rememberMe && userStore.refreshToken) {
        try {
          await userStore.refreshToken()
          await userStore.getUserInfo()

          this.initSession(sessionId)
          this.rememberMe = rememberMe

          return true
        } catch (___error) {
          this.clearSession()
          return false
        }
      }

      return false
    },

    /**
     * 清除会话
     */
    clearSession() {
      this.sessionId = null
      this.sessionStartTime = null
      this.lastActivityTime = null
      this.showTimeoutWarning = false
      this.deviceInfo = null

      localStorage.removeItem('sessionId')

      this.stopSessionMonitor()
    },

    /**
     * 获取其他登录设备
     */
    getOtherDevices() {
      if (!this.sessionId) return []

      return this.activeDevices.filter(d => d.sessionId !== this.sessionId)
    },

    /**
     * 踢出其他设备
     */
    async kickOutDevice(sessionId: string) {
      try {
        // 这里应该调用后端API踢出设备
        // await authApi.kickOutDevice(sessionId)

        // 从列表中移除
        this.activeDevices = this.activeDevices.filter(d => d.sessionId !== sessionId)

        ElMessage.success('已踢出该设备')
      } catch (___error) {
        ElMessage.error('操作失败')
      }
    }
  },

  persist: {
    key: 'hr-session',
    storage: sessionStorage,
    paths: ['activeDevices']
  }
})

// 导出便捷函数
export function setupSession() {
  const sessionStore = useSessionStore()

  // 尝试恢复会话
  sessionStore.restoreSession()

  // 监听路由变化更新活动时间
  router.afterEach(() => {
    if (sessionStore.sessionId) {
      sessionStore.updateActivity()
    }
  })
}
