/**
 * 员工Store - 向后兼容导出
 * 提供与原有API相同的接口
 */

import { defineStore } from 'pinia'
import { container } from '@/api/core/injectable'
import { initializeApiModule } from '@/api/core'
import { initializeStoreModule } from '../core/store-module'
import { EMPLOYEE_STORE } from '../core/tokens'
// 确保模块已初始化
initializeApiModule()
initializeStoreModule()

// 获取Store实例
const employeeStoreInstance = container.get(EMPLOYEE_STORE)

// 创建兼容的Pinia store
export const useEmployeeStore = defineStore('employee', () => {
  return employeeStoreInstance.setup()
})
