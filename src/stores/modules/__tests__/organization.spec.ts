/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
 
/* eslint-disable-next-line @typescript-eslint/no-unused-vars */
import { describe, it, expect, beforeEach, vi } from 'vitest';
  import { setActivePinia, createPinia } from 'pinia'
  import type { OrganizationNode } from '../../types'

// Mock API模块
vi.mock('@/api/modules/organization', () => ({
  default: {
    getTree: vi.fn(),
    move: vi.fn()
  }
}))

// 在mock之后再导入store
const {useOrganizationStore} = await import('../organization')

describe('Organization Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('calculateSortValue', () => {
    it('should calculate sort value correctly when inserting before first node', () => {
      const store = useOrganizationStore()
      
      // 设置测试数据
      const parent: OrganizationNode = {
        id: 'parent-1',
        code: 'P001',
        name: '总部',
        parentId: null,
        type: 'department',
        level: 0,
        sort: 0,
        status: 1,
        children: [
          { id: 'child-1', code: 'C001', name: '部门1', parentId: 'parent-1', type: 'department', level: 1, sort: 1000, status: 1 },
          { id: 'child-2', code: 'C002', name: '部门2', parentId: 'parent-1', type: 'department', level: 1, sort: 2000, status: 1 },
          { id: 'child-3', code: 'C003', name: '部门3', parentId: 'parent-1', type: 'department', level: 1, sort: 3000, status: 1 }
        ]
      }
      
      // 添加到Map
      store.organizationMap.set(parent.id, parent)
      parent.children!.forEach(child => {
        store.organizationMap.set(child.id, child)
      })

      // 在第一个节点前插入
      const sortValue = store.calculateSortValue('child-1', 'before')
      expect(sortValue).toBe(500) // 1000 / 2
    })

    it('should calculate sort value correctly when inserting after last node', () => {
      const store = useOrganizationStore()
      
      // 设置测试数据
      const parent: OrganizationNode = {
        id: 'parent-1',
        code: 'P001',
        name: '总部',
        parentId: null,
        type: 'department',
        level: 0,
        sort: 0,
        status: 1,
        children: [
          { id: 'child-1', code: 'C001', name: '部门1', parentId: 'parent-1', type: 'department', level: 1, sort: 1000, status: 1 },
          { id: 'child-2', code: 'C002', name: '部门2', parentId: 'parent-1', type: 'department', level: 1, sort: 2000, status: 1 },
          { id: 'child-3', code: 'C003', name: '部门3', parentId: 'parent-1', type: 'department', level: 1, sort: 3000, status: 1 }
        ]
      }
      
      // 添加到Map
      store.organizationMap.set(parent.id, parent)
      parent.children!.forEach(child => {
        store.organizationMap.set(child.id, child)
      })

      // 在最后一个节点后插入
      const sortValue = store.calculateSortValue('child-3', 'after')
      expect(sortValue).toBe(4000) // 3000 + 1000
    })

    it('should calculate sort value correctly when inserting between nodes', () => {
      const store = useOrganizationStore()
      
      // 设置测试数据
      const parent: OrganizationNode = {
        id: 'parent-1',
        code: 'P001',
        name: '总部',
        parentId: null,
        type: 'department',
        level: 0,
        sort: 0,
        status: 1,
        children: [
          { id: 'child-1', code: 'C001', name: '部门1', parentId: 'parent-1', type: 'department', level: 1, sort: 1000, status: 1 },
          { id: 'child-2', code: 'C002', name: '部门2', parentId: 'parent-1', type: 'department', level: 1, sort: 2000, status: 1 },
          { id: 'child-3', code: 'C003', name: '部门3', parentId: 'parent-1', type: 'department', level: 1, sort: 3000, status: 1 }
        ]
      }
      
      // 添加到Map
      store.organizationMap.set(parent.id, parent)
      parent.children!.forEach(child => {
        store.organizationMap.set(child.id, child)
      })

      // 在第二个节点前插入
      const sortValueBefore = store.calculateSortValue('child-2', 'before')
      expect(sortValueBefore).toBe(1500) // (1000 + 2000) / 2

      // 在第二个节点后插入
      const sortValueAfter = store.calculateSortValue('child-2', 'after')
      expect(sortValueAfter).toBe(2500) // (2000 + 3000) / 2
    })

    it('should handle edge cases gracefully', () => {
      const store = useOrganizationStore()
      
      // 节点不存在
      const sortValue1 = store.calculateSortValue('non-existent', 'before')
      expect(sortValue1).toBe(0)

      // 没有父节点的根节点
      const rootNode: OrganizationNode = {
        id: 'root-1',
        code: 'R001',
        name: '根节点',
        parentId: null,
        type: 'department',
        level: 0,
        sort: 1000,
        status: 1
      },
  store.organizationMap.set(rootNode.id, rootNode)
      
      const sortValue2 = store.calculateSortValue('root-1', 'before')
      expect(sortValue2).toBe(0)
    })
  })
})