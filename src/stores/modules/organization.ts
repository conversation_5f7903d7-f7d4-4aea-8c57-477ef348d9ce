/**
 * 组织架构状态管理
 * 管理组织树、部门信息等状态
 */

import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import organizationApi from '@/api/modules/organization'
import type { OrganizationState, OrganizationNode } from '../types'
import type { Organization, OrganizationType, OrganizationStatus } from '@/types/organization'

export const useOrganizationStore = defineStore('organization', {
  state: (): OrganizationState => ({
    organizationTree: [],
    organizationMap: new Map(),
    currentOrganization: null,
    loading: false,
    statistics: null
  }),

  getters: {
    // 获取扁平化的组织列表
    flatOrganizationList: state => {
      const list: OrganizationNode[] = []
      const traverse = (nodes: OrganizationNode[]) => {
        nodes.forEach(node => {
          list.push(node)
          if (node.children) {
            traverse(node.children)
          }
        })
      }
      traverse(state.organizationTree)
      return list
    },

    // 根据ID获取组织
    getOrganizationById: state => {
      return (id: string): OrganizationNode | undefined => {
        return state.organizationMap.get(id)
      }
    },

    // 获取组织的完整路径
    getOrganizationPath: state => {
      return (id: string): string[] => {
        const org = state.organizationMap.get(id)
        if (!org) return []

        const path: string[] = [org.institutionName]
        let parent = org.parentInstitutionId
          ? state.organizationMap.get(org.parentInstitutionId)
          : null

        while (parent) {
          path.unshift(parent.institutionName)
          parent = parent.parentInstitutionId
            ? state.organizationMap.get(parent.parentInstitutionId)
            : null
        }

        return path
      }
    },

    // 获取子组织ID列表
    getChildrenIds: state => {
      return (id: string, includeAll = false): string[] => {
        const ids: string[] = []
        const org = state.organizationMap.get(id)

        if (!org) return ids

        const traverse = (node: OrganizationNode) => {
          if (node.children) {
            node.children.forEach(child => {
              ids.push(child.institutionId)
              if (includeAll) {
                traverse(child)
              }
            })
          }
        }

        traverse(org)
        return ids
      }
    },

    // 判断是否为叶子节点
    isLeafNode: state => {
      return (id: string): boolean => {
        const org = state.organizationMap.get(id)
        return !org?.children || org.children.length === 0
      }
    }
  },

  actions: {
    /**
     * 获取组织架构树
     */
    async fetchOrganizationTree(params?: { type?: OrganizationType; status?: OrganizationStatus }) {
      this.loading = true
      try {
        const { data } = await organizationApi.getTree(params)
        this.organizationTree = data
        // 构建Map以便快速查找
        this.buildOrganizationMap(data)
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取组织架构失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 移动组织机构
    async moveOrganization(params: { sourceId: string; targetId: string; position: string }) {
      try {
        await organizationApi.move(params.sourceId, params.targetId)
        ElMessage.success('移动成功')
        // 重新加载组织树
        await this.fetchOrganizationTree()
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '移动失败')
        throw error
      }
    },

    // 创建快照
    async createSnapshot(params: { versionNumber: string; description: string }) {
      try {
        const { data } = await organizationApi.createSnapshot(params)
        ElMessage.success('快照创建成功')
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '创建快照失败')
        throw error
      }
    },

    /**
     * 对比版本
     */
    async compareVersions(version1Id: string, version2Id: string) {
      try {
        const { data } = await organizationApi.compareVersions(version1Id, version2Id)
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '版本对比失败')
        throw error
      }
    },

    /**
     * 回滚到指定版本
     */
    async rollbackToVersion(versionId: string) {
      try {
        await organizationApi.rollbackToGlobalVersion(versionId)
        ElMessage.success('回滚成功')
        // 刷新组织树
        await this.fetchOrganizationTree()
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '回滚失败')
        throw error
      }
    },

    /**
     * 构建组织Map
     */
    buildOrganizationMap(organizations: Organization[]) {
      this.organizationMap.clear()
      const traverse = (nodes: Organization[]) => {
        nodes.forEach(node => {
          this.organizationMap.set(node.institutionId, node)
          if (node.children) {
            traverse(node.children)
          }
        })
      }
      traverse(organizations)
    },

    /**
     * 加载组织架构树（对外暴露的方法）
     */
    async loadOrganizationTree() {
      return this.fetchOrganizationTree()
    },

    /**
     * 加载组织子节点
     */
    async loadOrganizationChildren(parentId: string) {
      try {
        const { data } = await organizationApi.getChildren(parentId)
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '加载子节点失败')
        throw error
      }
    },

    /**
     * 删除组织
     */
    async deleteOrganization(id: string) {
      try {
        await organizationApi.delete(id)
        ElMessage.success('删除成功')
        // 刷新组织树
        await this.fetchOrganizationTree()
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '删除失败')
        throw error
      }
    },

    /**
     * 查询组织（多条件）
     */

    async queryOrganizations(params: unknown) {
      try {
        const { data } = await organizationApi.queryOrganizations(params)
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '查询组织失败')
        throw error
      }
    },

    /**
     * 查询组织历史
     */

    async queryOrganizationHistory(params: unknown) {
      try {
        const { data } = await organizationApi.queryHistory(params)
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '查询历史失败')
        throw error
      }
    },

    /**
     * 查询组织人员分布
     */

    async queryOrganizationEmployees(params: unknown) {
      try {
        const { data } = await organizationApi.queryEmployees(params)
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '查询人员分布失败')
        throw error
      }
    },

    /**
     * 获取组织详情
     */
    async getOrganizationDetail(id: string) {
      try {
        const { data } = await organizationApi.getDetail(id)
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取组织详情失败')
        throw error
      }
    },

    /**
     * 获取组织人员列表
     */

    async getOrganizationEmployees(id: string, params?: unknown) {
      try {
        const { data } = await organizationApi.getEmployees(id, params)
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取人员列表失败')
        throw error
      }
    },

    /**
     * 获取组织变更历史（分页）
     */

    async getOrganizationChangesPaged(id: string, params?: unknown): Promise<unknown> {
      try {
        const { data } = await organizationApi.getChanges(id, params)
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取变更历史失败')
        throw error
      }
    },

    /**
     * 获取总体统计数据
     */
    async fetchStatistics() {
      try {
        const { data } = await organizationApi.getOverallStatistics()
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取统计数据失败')
        throw error
      }
    },

    /**
     * 获取部门统计数据
     */
    async fetchDepartmentStatistics() {
      try {
        const { data } = await organizationApi.getDepartmentStatistics()
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取部门统计失败')
        throw error
      }
    },

    /**
     * 获取岗位统计数据
     */
    async fetchPositionStatistics() {
      try {
        const { data } = await organizationApi.getPositionStatistics()
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取岗位统计失败')
        throw error
      }
    },

    /**
     * 获取编制统计数据
     */
    async fetchEstablishmentStatistics() {
      try {
        const { data } = await organizationApi.getEstablishmentStatistics()
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取编制统计失败')
        throw error
      }
    },

    /**
     * 获取人员结构分析数据
     */
    async fetchStructureAnalysis() {
      try {
        const { data } = await organizationApi.getStructureAnalysis()
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取结构分析失败')
        throw error
      }
    },

    /**
     * 获取趋势分析数据
     */
    async fetchTrendAnalysis() {
      try {
        const { data } = await organizationApi.getTrendAnalysis()
        return data
      } catch (error: unknown) {
        ElMessage.error((error as Error).message || '获取趋势分析失败')
        throw error
      }
    }
  },

  // 持久化配置
  persist: {
    enabled: true,
    paths: ['currentOrganization']
  }
})
