import { defineStore } from 'pinia'
import type { DrillDownManager } from '@/utils/drilldown/DrillDownManager'
import type { DrillDownContext, DrillDownPath } from '@/types/drilldown'

interface DrillDownStoreState {
  managers: Map<string, DrillDownManager>
  activeManagerId: string | null
  globalContext: DrillDownContext | null
}

export const useDrillDownStore = defineStore('drilldown', {
  state: (): DrillDownStoreState => ({
    managers: new Map(),
    activeManagerId: null,
    globalContext: null
  }),

  getters: {
    activeManager(): DrillDownManager | null {
      if (!this.activeManagerId) return null
      return this.managers.get(this.activeManagerId) || null
    },

    currentPath(): DrillDownPath[] {
      return this.activeManager?.getCurrentContext().path || []
    },

    currentLevel(): number {
      return this.activeManager?.getCurrentContext().currentLevel || 0
    },

    canDrillDown(): boolean {
      return this.activeManager?.canDrillDown() || false
    },

    canDrillUp(): boolean {
      return this.activeManager?.canDrillUp() || false
    }
  },

  actions: {
    // 设置管理器
    setManager(manager: DrillDownManager, id: string = 'default'): void {
      this.managers.set(id, manager)
      if (!this.activeManagerId) {
        this.activeManagerId = id
      }
    },

    // 获取管理器
    getManager(id: string = 'default'): DrillDownManager | null {
      return this.managers.get(id) || null
    },

    // 切换活动管理器
    setActiveManager(id: string): void {
      if (this.managers.has(id)) {
        this.activeManagerId = id
      }
    },

    // 移除管理器
    removeManager(id: string): void {
      this.managers.delete(id)
      if (this.activeManagerId === id) {
        this.activeManagerId = this.managers.size > 0 ? Array.from(this.managers.keys())[0] : null
      }
    },

    // 清空所有管理器
    clearManagers(): void {
      this.managers.clear()
      this.activeManagerId = null
    },

    // 更新全局上下文
    updateGlobalContext(context: DrillDownContext): void {
      this.globalContext = context
    },

    // 同步所有管理器的状态
    syncManagers(): void {
      if (this.globalContext && this.managers.size > 1) {
        this.managers.forEach((manager, id) => {
          if (id !== this.activeManagerId) {
            // 可以在这里实现管理器间的状态同步逻辑
          }
        })
      }
    }
  }
})
