/**
 * 薪资管理状态存储
 * 包含薪资结构管理、工资计算、税务数据等完整功能
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  salaryStructureApi,
  salaryCalculateApi,
  salaryStatisticsApi,
  taxApi,
  personalSalaryApi,
  salaryStandardApi
} from '@/api/salaryStructure'
import type {
  SalaryStructure,
  SalaryStructureQuery,
  SalaryCalculateParams,
  SalaryCalculateResult,
  SalaryStatistics,
  PersonalSalary,
  SalaryStandard
} from '@/types/salary'

export const useSalaryStore = defineStore('salary', () => {
  // ==================== 状态定义 ====================

  // 薪资结构相关状态
  const structureList = ref<SalaryStructure[]>([])
  const currentStructure = ref<SalaryStructure | null>(null)
  const structureTotal = ref(0)
  const structureLoading = ref(false)

  // 工资计算相关状态
  const calculateLoading = ref(false)
  const calculateResult = ref<SalaryCalculateResult | null>(null)
  const calculateHistory = ref<SalaryCalculateResult[]>([])
  const batchCalculateProgress = ref({
    total: 0,
    completed: 0,
    failed: 0,
    isProcessing: false
  })

  // 税务数据相关状态
  const taxCalculateLoading = ref(false)
  const taxCalculateResult = ref<unknown>(null)
  const taxDeductionConfig = ref<unknown>(null)
  const taxDeclarationData = ref<unknown>(null)

  // 统计数据状态
  const salaryStatistics = ref<SalaryStatistics | null>(null)
  const departmentComparison = ref<unknown>(null)
  const trendAnalysis = ref<unknown>(null)

  // 个人薪资状态
  const personalSalaryList = ref<PersonalSalary[]>([])
  const currentPersonalSalary = ref<PersonalSalary | null>(null)

  // 薪资标准状态
  const salaryStandardList = ref<SalaryStandard[]>([])
  const currentStandard = ref<SalaryStandard | null>(null)

  // ==================== 计算属性 ====================

  // 薪资结构映射
  const structureMap = computed(() => {
    const map = new Map<string, SalaryStructure>()
    structureList.value.forEach(structure => {
      map.set(structure.id, structure)
    })
    return map
  })

  // 活跃的薪资结构
  const activeStructures = computed(() => structureList.value.filter(s => s.status === 'active'))

  // 计算进度百分比
  const calculateProgressPercentage = computed(() => {
    const { total, completed } = batchCalculateProgress.value
    return total > 0 ? Math.round((completed / total) * 100) : 0
  })

  // 是否有未保存的计算结果
  const hasUnsavedCalculateResult = ref(false)

  // 获取薪资结构列表
  const getStructureList = async (params?: { page?: number; pageSize?: number }) => {
    structureLoading.value = true
    try {
      const { data } = await salaryStructureApi.getStructureList({
        page: params?.page || 1,
        pageSize: params?.pageSize || 20,
        ...params
      })
      structureList.value = data.list
      structureTotal.value = data.total
      return data
    } catch (___error) {
      throw error
    } finally {
      structureLoading.value = false
    }
  }

  // 获取薪资结构详情
  const fetchStructureDetail = async (id: string) => {
    try {
      const { data } = await salaryStructureApi.getStructureDetail(id)
      currentStructure.value = data
      return data
    } catch (___error) {
      throw error
    }
  }

  // 创建薪资结构
   
  const createStructure = async (structureData: unknown) => {
    try {
      const { data } = await salaryStructureApi.createStructure(structureData)
      // 添加到列表
      structureList.value.unshift(data)
      structureTotal.value += 1
      return data
    } catch (___error) {
      throw error
    }
  }

  // 更新薪资结构
   
  const updateStructure = async (id: string, structureData: unknown) => {
    try {
      const { data } = await salaryStructureApi.updateStructure(id, structureData)
      // 更新列表中的数据
      const index = structureList.value.findIndex(s => s.id === id)
      if (index !== -1) {
        structureList.value[index] = data
      }
      // 更新当前结构
      if (currentStructure.value?.id === id) {
        currentStructure.value = data
      }
      return data
    } catch (___error) {
      throw error
    }
  }

  // 删除薪资结构
   
  const deleteStructure = async (id: string, options?: unknown) => {
    try {
      const { data } = await salaryStructureApi.deleteStructure(id, options)
      // 从列表中移除
      const index = structureList.value.findIndex(s => s.id === id)
      if (index !== -1) {
        structureList.value.splice(index, 1)
        structureTotal.value -= 1
      }
      // 清空当前结构
      if (currentStructure.value?.id === id) {
        currentStructure.value = null
      }
      return data
    } catch (___error) {
      throw error
    }
  }

  // 更新薪资结构缓存
  const updateStructureCache = (structure: SalaryStructure) => {
    const index = structureList.value.findIndex(s => s.id === structure.id)
    if (index !== -1) {
      structureList.value[index] = { ...structureList.value[index], ...structure }
    } else {
      structureList.value.push(structure)
    }
  }

  // ==================== 工资计算管理 Actions ====================

  // 计算薪资
  const calculateSalary = async (params: SalaryCalculateParams) => {
    calculateLoading.value = true
    try {
      const { data } = await salaryCalculateApi.calculate(params)
      calculateResult.value = data
      // 添加到历史记录
      calculateHistory.value.unshift(data)
      // 只保留最近10条记录
      if (calculateHistory.value.length > 10) {
        calculateHistory.value = calculateHistory.value.slice(0, 10)
      }
      return data
    } catch (___error) {
      throw error
    } finally {
      calculateLoading.value = false
    }
  }

  // 批量计算薪资
   
  const batchCalculateSalary = async (params: unknown) => {
    batchCalculateProgress.value = {
      total: params.employeeIds?.length || 0,
      completed: 0,
      failed: 0,
      isProcessing: true
    }
    try {
      const { data } = await salaryCalculateApi.batchCalculate(params)
      batchCalculateProgress.value.completed = data.success || 0
      batchCalculateProgress.value.failed = data.failed || 0
      return data
    } catch (___error) {
      throw error
    } finally {
      batchCalculateProgress.value.isProcessing = false
    }
  }

  // 保存计算结果
  const saveCalculateResult = async () => {
    if (!calculateResult.value) return

    try {
      const { data } = await salaryCalculateApi.saveResult(calculateResult.value)
      calculateResult.value.isSaved = true
      return data
    } catch (___error) {
      throw error
    }
  }

  // 清空计算结果
  const clearCalculateResult = () => {
    calculateResult.value = null
  }

  // ==================== 税务数据管理 Actions ====================

  // 计算个税
   
  const calculateTax = async (params: unknown) => {
    taxCalculateLoading.value = true
    try {
      const { data } = await taxApi.calculateTax(params)
      taxCalculateResult.value = data
      return data
    } catch (___error) {
      throw error
    } finally {
      taxCalculateLoading.value = false
    }
  }

  // 获取个税申报数据
  const fetchTaxDeclaration = async (period: string) => {
    try {
      const { data } = await taxApi.getTaxDeclaration(period)
      taxDeclarationData.value = data
      return data
    } catch (___error) {
      throw error
    }
  }

  // 获取专项扣除配置
  const fetchDeductionConfig = async (employeeId: string) => {
    try {
      const { data } = await taxApi.getDeductionConfig(employeeId)
      taxDeductionConfig.value = data
      return data
    } catch (___error) {
      throw error
    }
  }

  // 更新专项扣除配置
   
  const updateDeductionConfig = async (employeeId: string, config: unknown) => {
    try {
      const { data } = await taxApi.updateDeductionConfig(employeeId, config)
      taxDeductionConfig.value = data
      return data
    } catch (___error) {
      throw error
    }
  }

  // ==================== 统计数据管理 Actions ====================

  // 获取薪资统计
   
  const fetchSalaryStatistics = async (params: unknown) => {
    try {
      const { data } = await salaryStatisticsApi.getStatistics(params)
      salaryStatistics.value = data
      return data
    } catch (___error) {
      throw error
    }
  }

  // 获取部门薪资对比
  const fetchDepartmentComparison = async (period: string) => {
    try {
      const { data } = await salaryStatisticsApi.getDepartmentComparison(period)
      departmentComparison.value = data
      return data
    } catch (___error) {
      throw error
    }
  }

  // 获取薪资趋势分析
   
  const fetchTrendAnalysis = async (params: unknown) => {
    try {
      const { data } = await salaryStatisticsApi.getTrendAnalysis(params)
      trendAnalysis.value = data
      return data
    } catch (___error) {
      throw error
    }
  }

  // ==================== 工具方法 ====================

  // 根据ID获取薪资结构
  const getStructureById = (id: string): SalaryStructure | undefined => {
    return structureMap.value.get(id)
  }

  // 根据范围获取薪资结构
  const getStructuresByScope = (scope: string): SalaryStructure[] => {
    return structureList.value.filter(s => s.scope === scope)
  }

  // 清空所有缓存
  const clearAllCache = () => {
    structureList.value = []
    currentStructure.value = null
    calculateResult.value = null
    calculateHistory.value = []
    taxCalculateResult.value = null
    taxDeductionConfig.value = null
    salaryStatistics.value = null
    personalSalaryList.value = []
    salaryStandardList.value = []
  }

  // 刷新所有数据
  const refreshAllData = async () => {
    await Promise.all([fetchStructureList(), fetchSalaryStatistics({})])
  }

  return {
    // 状态
    structureList,
    currentStructure,
    structureTotal,
    structureLoading,
    calculateLoading,
    calculateResult,
    calculateHistory,
    batchCalculateProgress,
    taxCalculateLoading,
    taxCalculateResult,
    taxDeductionConfig,
    taxDeclarationData,
    salaryStatistics,
    departmentComparison,
    trendAnalysis,
    personalSalaryList,
    currentPersonalSalary,
    salaryStandardList,
    currentStandard,

    // 计算属性
    structureMap,
    activeStructures,
    calculateProgressPercentage,
    hasUnsavedCalculateResult,

    // 薪资结构管理
    fetchStructureList,
    fetchStructureDetail,
    createStructure,
    updateStructure,
    deleteStructure,
    updateStructureCache,

    // 工资计算管理
    calculateSalary,
    batchCalculateSalary,
    saveCalculateResult,
    clearCalculateResult,

    // 税务数据管理
    calculateTax,
    fetchTaxDeclaration,
    fetchDeductionConfig,
    updateDeductionConfig,

    // 统计数据管理
    fetchSalaryStatistics,
    fetchDepartmentComparison,
    fetchTrendAnalysis,

    // 工具方法
    getStructureById,
    getStructuresByScope,
    clearAllCache,
    refreshAllData
  }
})
