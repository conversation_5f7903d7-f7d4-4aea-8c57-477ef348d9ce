/**
 * 字典数据状态管理
 * 实现静态数据的本地缓存策略
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { localCache } from '@/utils/cache'
import dictionaryApi from '@/api/dictionary'
import type { DictItem, DictGroup } from '@/types/dictionary'

// 字典缓存键前缀
const DICT_CACHE_PREFIX = 'dict_'

// 字典缓存时间（7天）
const DICT_CACHE_TTL = 7 * 24 * 60 * 60 * 1000

// 获取字典数据（带降级处理）
async function fetchDictData(type: string): Promise<DictItem[]> {
  try {
    // 尝试调用API
    return await dictionaryApi.getDictData(type)
  } catch (___error) {
    // API失败时使用本地默认数据
    return new Promise(__resolve => {
      setTimeout(() => {
        // 模拟返回数据
        const mockData: Record<string, DictItem[]> = {
          gender: [
            { label: '男', value: '1', sortOrder: 1 },
            { label: '女', value: '2', sortOrder: 2 },
            { label: '其他', value: '3', sortOrder: 3 }
          ],
          education: [
            { label: '博士', value: 'phd', sortOrder: 1 },
            { label: '硕士', value: 'master', sortOrder: 2 },
            { label: '本科', value: 'bachelor', sortOrder: 3 },
            { label: '专科', value: 'college', sortOrder: 4 },
            { label: '高中', value: 'high_school', sortOrder: 5 },
            { label: '初中及以下', value: 'middle_school', sortOrder: 6 }
          ],
          maritalStatus: [
            { label: '未婚', value: 'single', sortOrder: 1 },
            { label: '已婚', value: 'married', sortOrder: 2 },
            { label: '离异', value: 'divorced', sortOrder: 3 },
            { label: '丧偶', value: 'widowed', sortOrder: 4 }
          ],
          employeeStatus: [
            { label: '在职', value: 'active', sortOrder: 1, color: '#67C23A' },
            { label: '试用期', value: 'probation', sortOrder: 2, color: '#E6A23C' },
            { label: '离职', value: 'resigned', sortOrder: 3, color: '#909399' },
            { label: '退休', value: 'retired', sortOrder: 4, color: '#409EFF' }
          ],
          contractType: [
            { label: '固定期限劳动合同', value: 'fixed', sortOrder: 1 },
            { label: '无固定期限劳动合同', value: 'permanent', sortOrder: 2 },
            { label: '以完成一定工作任务为期限', value: 'project', sortOrder: 3 },
            { label: '实习协议', value: 'intern', sortOrder: 4 },
            { label: '劳务协议', value: 'service', sortOrder: 5 }
          ],
          leaveType: [
            { label: '年假', value: 'annual', sortOrder: 1, color: '#409EFF' },
            { label: '事假', value: 'personal', sortOrder: 2, color: '#E6A23C' },
            { label: '病假', value: 'sick', sortOrder: 3, color: '#F56C6C' },
            { label: '婚假', value: 'marriage', sortOrder: 4, color: '#E6A23C' },
            { label: '产假', value: 'maternity', sortOrder: 5, color: '#67C23A' },
            { label: '陪产假', value: 'paternity', sortOrder: 6, color: '#67C23A' },
            { label: '丧假', value: 'bereavement', sortOrder: 7, color: '#909399' }
          ],
          approvalStatus: [
            { label: '待审批', value: 'pending', sortOrder: 1, color: '#E6A23C' },
            { label: '审批中', value: 'processing', sortOrder: 2, color: '#409EFF' },
            { label: '已通过', value: 'approved', sortOrder: 3, color: '#67C23A' },
            { label: '已拒绝', value: 'rejected', sortOrder: 4, color: '#F56C6C' },
            { label: '已撤销', value: 'cancelled', sortOrder: 5, color: '#909399' }
          ]
        }

        resolve(mockData[type] || [])
      }, 100)
    })
  }
}

// 批量获取字典数据（带降级处理）
async function fetchDictBatch(types: string[]): Promise<Record<string, DictItem[]>> {
  try {
    // 尝试调用批量API
    return await dictionaryApi.getDictBatch(types)
  } catch (___error) {
    // API失败时逐个获取
    const result: Record<string, DictItem[]> = {}

    await Promise.all(
      types.map(async _type => {
        result[type] = await fetchDictData(type)
      })
    )

    return result
  }
}

export const useDictionaryStore = defineStore('dictionary', () => {
  // 字典数据缓存
  const dictCache = ref<Record<string, DictItem[]>>({})

  // 加载状态
  const loading = ref<Record<string, boolean>>({})

  // 错误信息
  const errors = ref<Record<string, string>>({})

  // 获取字典数据
  async function getDict(type: string, _forceRefresh = false): Promise<DictItem[]> {
    const cacheKey = `${DICT_CACHE_PREFIX}${type}`

    // 如果不强制刷新，先从内存缓存获取
    if (!forceRefresh && dictCache.value[type]) {
      return dictCache.value[type]
    }

    // 从本地存储缓存获取
    if (!forceRefresh) {
      const cachedData = localCache.get<DictItem[]>(cacheKey)
      if (cachedData) {
        dictCache.value[type] = cachedData
        return cachedData
      }
    }

    // 如果正在加载，等待加载完成
    if (loading.value[type]) {
      return new Promise(__resolve => {
        const checkInterval = setInterval(() => {
          if (!loading.value[type]) {
            clearInterval(checkInterval)
            resolve(dictCache.value[type] || [])
          }
        }, 100)
      })
    }

    // 开始加载
    loading.value[type] = true
    errors.value[type] = ''

    try {
      // 从API获取数据
      const data = await fetchDictData(type)

      // 更新内存缓存
      dictCache.value[type] = data

      // 更新本地存储缓存
      localCache.set(cacheKey, data, DICT_CACHE_TTL)

      return data
    } catch (___error) {
      const errorMsg = error instanceof Error ? error.message : '获取字典数据失败'
      errors.value[type] = errorMsg
      return []
    } finally {
      loading.value[type] = false
    }
  }

  // 批量获取字典数据
  async function getDictBatch(
    types: string[],
    _forceRefresh = false
  ): Promise<Record<string, DictItem[]>> {
    const result: Record<string, DictItem[]> = {}

    // 分离需要加载的和已缓存的
    const typesToLoad: string[] = []

    for (const type of types) {
      if (!forceRefresh && dictCache.value[type]) {
        result[type] = dictCache.value[type]
      } else if (!forceRefresh) {
        const cacheKey = `${DICT_CACHE_PREFIX}${type}`
        const cachedData = localCache.get<DictItem[]>(cacheKey)
        if (cachedData) {
          dictCache.value[type] = cachedData
          result[type] = cachedData
        } else {
          typesToLoad.push(type)
        }
      } else {
        typesToLoad.push(type)
      }
    }

    // 批量加载需要的数据
    if (typesToLoad.length > 0) {
      const loadedData = await fetchDictBatch(typesToLoad)

      // 更新缓存
      for (const type of typesToLoad) {
        if (loadedData[type]) {
          dictCache.value[type] = loadedData[type]
          result[type] = loadedData[type]

          // 更新本地存储
          const cacheKey = `${DICT_CACHE_PREFIX}${type}`
          localCache.set(cacheKey, loadedData[type], DICT_CACHE_TTL)
        }
      }
    }

    return result
  }

  // 根据值获取标签
  function getLabel(type: string, value: string | number): string {
    const items = dictCache.value[type] || []
    const item = items.find(item => item.value === String(value))
    return item?.label || String(value)
  }

  // 根据值获取字典项
  function getItem(type: string, value: string | number): DictItem | undefined {
    const items = dictCache.value[type] || []
    return items.find(item => item.value === String(value))
  }

  // 清除指定类型的缓存
  function clearCache(type?: string) {
    if (type) {
      delete dictCache.value[type]
      localCache.delete(`${DICT_CACHE_PREFIX}${type}`)
    } else {
      // 清除所有字典缓存
      dictCache.value = {}
      localCache.deletePattern(new RegExp(`^${DICT_CACHE_PREFIX}`))
    }
  }

  // 预加载常用字典
  async function preloadCommonDicts() {
    const commonTypes = [
      'gender',
      'education',
      'maritalStatus',
      'employeeStatus',
      'contractType',
      'leaveType',
      'approvalStatus'
    ]

    await getDictBatch(commonTypes)
  }

  // 获取字典分组（用于字典管理页面）
  const dictGroups = computed<DictGroup[]>(() => {
    return [
      {
        name: '基础信息',
        code: 'basic',
        types: ['gender', 'education', 'maritalStatus']
      },
      {
        name: '员工信息',
        code: 'employee',
        types: ['employeeStatus', 'contractType']
      },
      {
        name: '考勤管理',
        code: 'attendance',
        types: ['leaveType']
      },
      {
        name: '流程管理',
        code: 'workflow',
        types: ['approvalStatus']
      }
    ]
  })

  // 获取加载进度
  const loadingProgress = computed(() => {
    const types = Object.keys(loading.value)
    if (types.length === 0) return 0

    const loadingCount = types.filter(type => loading.value[type]).length
    return ((types.length - loadingCount) / types.length) * 100
  })

  // 获取缓存统计信息
  const cacheStats = computed(() => {
    const types = Object.keys(dictCache.value)
    let totalItems = 0
    let totalSize = 0

    types.forEach(type => {
      const items = dictCache.value[type] || []
      totalItems += items.length
      totalSize += JSON.stringify(items).length * 2 // 估算内存占用
    })

    return {
      typeCount: types.length,
      itemCount: totalItems,
      estimatedSize: totalSize,
      formattedSize: formatBytes(totalSize)
    }
  })

  // 格式化字节大小
  function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return {
    // 状态
    dictCache,
    loading,
    errors,
    dictGroups,
    loadingProgress,
    cacheStats,

    // 方法
    getDict,
    getDictBatch,
    getLabel,
    getItem,
    clearCache,
    preloadCommonDicts
  }
})

// 导出类型
export interface DictItem {
  label: string
  value: string
  sortOrder?: number
  color?: string
  disabled?: boolean
  children?: DictItem[]

  [key: string]: unknown
}

export interface DictGroup {
  name: string
  code: string
  types: string[]
}
