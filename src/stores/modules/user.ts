/**
 * 用户状态管理
 * 管理用户登录、权限、个人信息等状态
 */

import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import router from '@/router'
import authApi from '@/api/modules/auth'
import userApi from '@/api/modules/user'
import type { UserState } from '../types'
import type { LoginParams, UserInfo } from '@/api/types/business'

// Token存储键名
const TOKEN_KEY = 'access_token'
const REFRESH_TOKEN_KEY = 'refresh_token'

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    token: localStorage.getItem(TOKEN_KEY) || '',
    refreshToken: localStorage.getItem(REFRESH_TOKEN_KEY) || '',
    permissions: [],
    roles: [],
    isLogin: false,
    lastLoginTime: null
  }),

  getters: {
    // 获取用户名
    username: state => state.userInfo?.username || '',

    // 获取真实姓名
    realName: state => state.userInfo?.realName || '',

    // 获取用户头像
    avatar: state => state.userInfo?.avatar || '',

    // 获取部门名称
    department: state => state.userInfo?.department || '',

    // 获取职位
    position: state => state.userInfo?.position || '',

    // 是否已登录
    isAuthenticated: state => !!state.token && state.isLogin,

    // 获取用户角色代码列表
    roleCodes: state => state.roles.map(role => role.code),

    // 获取用户权限代码列表
    permissionCodes: state => state.permissions,

    // 判断是否有指定权限
    hasPermission: state => {
      return (permission: string | string[]): boolean => {
        if (!permission) return true

        const permissions = Array.isArray(permission) ? permission : [permission]
        return permissions.some(p => state.permissions.includes(p))
      }
    },

    // 判断是否有指定角色
    hasRole: state => {
      return (role: string | string[]): boolean => {
        if (!role) return true

        const roles = Array.isArray(role) ? role : [role]
        const roleCodes = state.roles.map(r => r.code)
        return roles.some(r => roleCodes.includes(r))
      }
    },

    // 获取用户菜单（根据权限过滤）
    userMenus: _state => {
      // 这里需要配合权限store实现
      return []
    }
  },

  actions: {
    /**
     * 用户登录
     */
    async login(params: LoginParams) {
      try {
        const { data } = await authApi.login(params)

        // 保存token
        this.setToken(data.accessToken, data.refreshToken)

        // 保存用户信息
        this.setUserInfo(data.userInfo)

        // 记录登录时间
        this.lastLoginTime = new Date().toISOString()
        this.isLogin = true

        // 如果选择记住我，持久化refreshToken
        if (params.rememberMe) {
          localStorage.setItem('rememberMe', 'true')
        } else {
          localStorage.removeItem('rememberMe')
        }

        ElMessage.success('登录成功')

        // 跳转到首页或之前的页面
        const redirect = router.currentRoute.value.query.redirect as string
        router.push(redirect || '/')

        return data
      } catch (error: unknown) {
        ElMessage.error(error.message || '登录失败')
        throw error
      }
    },

    /**
     * 获取用户信息
     */
    async getUserInfo() {
      try {
        const { data } = await userApi.getCurrentUser()
        this.setUserInfo(data)
        this.isLogin = true
        return data
      } catch (error) {
        // 获取用户信息失败，清除登录状态
        this.resetUser()
        throw error
      }
    },

    /**
     * 用户登出
     */
    async logout() {
      try {
        await authApi.logout()
      } catch (___error) {
      } finally {
        this.resetUser()
        ElMessage.success('已退出登录')
        router.push('/login')
      }
    },

    /**
     * 修改密码
     */
    async changePassword(data: {
      oldPassword: string
      newPassword: string
      confirmPassword: string
    }) {
      try {
        await authApi.changePassword(data)
        ElMessage.success('密码修改成功，请重新登录')

        // 修改密码后需要重新登录
        this.logout()
      } catch (error: unknown) {
        ElMessage.error(error.message || '密码修改失败')
        throw error
      }
    },

    /**
     * 刷新token
     */
    async refreshToken() {
      try {
        if (!this.refreshToken) {
          throw new Error('No refresh token')
        }

        const { data } = await authApi.refreshToken(this.refreshToken)
        this.setToken(data.accessToken, data.refreshToken)

        return data.accessToken
      } catch (___error) {
        // 刷新失败，清除登录状态
        this.resetUser()
        throw error
      }
    },

    /**
     * 设置token
     */
    setToken(accessToken: string, refreshToken?: string) {
      this.token = accessToken
      localStorage.setItem(TOKEN_KEY, accessToken)

      if (refreshToken) {
        this.refreshToken = refreshToken
        localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
      }
    },

    /**
     * 设置用户信息
     */
    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo
      this.permissions = userInfo.permissions || []
      this.roles = userInfo.roles || []
    },

    /**
     * 更新用户信息
     */
    async updateUserInfo(data: Partial<UserInfo>) {
      try {
        const { data: updatedInfo } = await userApi.updateUserInfo(data)
        this.setUserInfo(updatedInfo)
        ElMessage.success('用户信息更新成功')
        return updatedInfo
      } catch (error: unknown) {
        ElMessage.error(error.message || '更新失败')
        throw error
      }
    },

    /**
     * 上传头像
     */
    async uploadAvatar(file: File) {
      try {
        const { data } = await userApi.uploadAvatar(file)

        // 更新用户头像
        if (this.userInfo) {
          this.userInfo.avatar = data.avatarUrl
        }

        ElMessage.success('头像上传成功')
        return data.avatarUrl
      } catch (error: unknown) {
        ElMessage.error(error.message || '头像上传失败')
        throw error
      }
    },

    /**
     * 重置用户状态
     */
    resetUser() {
      this.userInfo = null
      this.token = ''
      this.refreshToken = ''
      this.permissions = []
      this.roles = []
      this.isLogin = false
      this.lastLoginTime = null

      // 清除本地存储
      localStorage.removeItem(TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)
      localStorage.removeItem('rememberMe')
    },

    /**
     * 检查登录状态
     */
    async checkLogin() {
      // 如果没有token，直接返回
      if (!this.token) {
        return false
      }

      // 如果已经有用户信息，说明已登录
      if (this.userInfo && this.isLogin) {
        return true
      }

      // 尝试获取用户信息
      try {
        await this.getUserInfo()
        return true
      } catch (___error) {
        return false
      }
    },

    /**
     * 初始化用户状态（应用启动时调用）
     */
    async initUserState() {
      // 检查是否有记住登录状态
      const rememberMe = localStorage.getItem('rememberMe') === 'true'

      if (this.token && rememberMe) {
        try {
          await this.getUserInfo()
        } catch (___error) {}
      }
    }
  },

  // 持久化配置
  persist: {
    enabled: true,
    paths: ['userInfo', 'lastLoginTime', 'isLogin'],
    // token单独处理，已经在localStorage中
    beforeRestore: _context => {
      // 恢复前检查token
      const token = localStorage.getItem(TOKEN_KEY)
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY)

      if (token) {
        context.store.token = token
      }
      if (refreshToken) {
        context.store.refreshToken = refreshToken
      }
    }
  }
})
