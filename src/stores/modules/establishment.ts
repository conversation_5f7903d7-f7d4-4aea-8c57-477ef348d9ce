/* eslint-disable @typescript-eslint/no-unused-vars */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  EstablishmentPlan,
  EstablishmentStatistics,
  EstablishmentSnapshot,
  EstablishmentAdjustment,
  EstablishmentTransfer,
  EstablishmentChangeLog,
  EstablishmentQueryParams,
  EstablishmentStatQueryParams,
  EstablishmentWarning,
  EstablishmentWarningRule,
  EstablishmentTrend,
  EstablishmentComparison,
  EstablishmentType,
  EstablishmentApprovalStatus
} from '@/types/establishment'
import establishmentApi from '@/api/modules/establishment'

export const useEstablishmentStore = defineStore('establishment', () => {
  // 状态
  const plans = ref<EstablishmentPlan[]>([])
  const currentPlan = ref<EstablishmentPlan | null>(null)
  const statistics = ref<EstablishmentStatistics[]>([])
  const snapshots = ref<EstablishmentSnapshot[]>([])
  const adjustments = ref<EstablishmentAdjustment[]>([])
  const transfers = ref<EstablishmentTransfer[]>([])
  const changeLogs = ref<EstablishmentChangeLog[]>([])
  const warnings = ref<EstablishmentWarning[]>([])
  const warningRules = ref<EstablishmentWarningRule[]>([])
  const loading = ref(false)
  const total = ref(0)

  // 计算属性
  const totalEstablishment = computed(() => {
    return statistics.value.reduce((sum, item) => sum + item.approvedCount, 0)
  })

  const totalActual = computed(() => {
    return statistics.value.reduce((sum, item) => sum + item.actualCount, 0)
  })

  const totalOverstaffed = computed(() => {
    return statistics.value.reduce((sum, item) => sum + item.overstaffedCount, 0)
  })

  const totalUnderstaffed = computed(() => {
    return statistics.value.reduce((sum, item) => sum + item.understaffedCount, 0)
  })

  const overallUtilizationRate = computed(() => {
    if (totalEstablishment.value === 0) return 0
    return Math.round((totalActual.value / totalEstablishment.value) * 100)
  })

  // 获取编制规划列表
  async function fetchPlans(params?: EstablishmentQueryParams) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getPlans(params)
      plans.value = data.list
      total.value = data.total
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取编制规划详情
  async function fetchPlanDetail(planId: string) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getPlanDetail(planId)
      currentPlan.value = data
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建编制规划
  async function createPlan(plan: Partial<EstablishmentPlan>) {
    loading.value = true
    try {
      const { data } = await establishmentApi.createPlan(plan)
      await fetchPlans()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新编制规划
  async function updatePlan(planId: string, plan: Partial<EstablishmentPlan>) {
    loading.value = true
    try {
      const { data } = await establishmentApi.updatePlan(planId, plan)
      await fetchPlans()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除编制规划
  async function deletePlan(planId: string) {
    loading.value = true
    try {
      await establishmentApi.deletePlan(planId)
      await fetchPlans()
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 提交审批
  async function submitForApproval(planId: string) {
    loading.value = true
    try {
      const { data } = await establishmentApi.submitForApproval(planId)
      await fetchPlanDetail(planId)
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 批量导入编制规划
  async function importPlans(file: File) {
    loading.value = true
    try {
      const formData = new FormData()
      formData.append('file', file)
      const { data } = await establishmentApi.importPlans(formData)
      await fetchPlans()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 导出编制规划
  async function exportPlans(params?: EstablishmentQueryParams) {
    loading.value = true
    try {
      const blob = await establishmentApi.exportPlans(params)
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `编制规划_${new Date().getTime()}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取编制统计数据
  async function fetchStatistics(params?: EstablishmentStatQueryParams) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getStatistics(params)
      statistics.value = data
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取编制趋势数据
  async function fetchTrends(params: {
    institutionId?: string
    positionCategory?: EstablishmentType
    startDate: string
    endDate: string
  }) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getTrends(params)
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取编制对比数据
  async function fetchComparison(institutionIds: string[]) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getComparison(institutionIds)
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建编制调整申请
  async function createAdjustment(adjustment: Partial<EstablishmentAdjustment>) {
    loading.value = true
    try {
      const { data } = await establishmentApi.createAdjustment(adjustment)
      await fetchAdjustments()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取编制调整列表
  async function fetchAdjustments(params?: EstablishmentQueryParams) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getAdjustments(params)
      adjustments.value = data.list
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建编制划转
  async function createTransfer(transfer: Partial<EstablishmentTransfer>) {
    loading.value = true
    try {
      const { data } = await establishmentApi.createTransfer(transfer)
      await fetchTransfers()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取编制划转列表
  async function fetchTransfers(params?: EstablishmentQueryParams) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getTransfers(params)
      transfers.value = data.list
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取编制快照列表
  async function fetchSnapshots(params?: {
    institutionId?: string
    startDate?: string
    endDate?: string
  }) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getSnapshots(params)
      snapshots.value = data
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建编制快照
  async function createSnapshot(date?: string) {
    loading.value = true
    try {
      const { data } = await establishmentApi.createSnapshot(date)
      await fetchSnapshots()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取变更日志
  async function fetchChangeLogs(params?: {
    planId?: string
    institutionId?: string
    startDate?: string
    endDate?: string
  }) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getChangeLogs(params)
      changeLogs.value = data
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取预警配置
  async function fetchWarnings() {
    loading.value = true
    try {
      const { data } = await establishmentApi.getWarnings()
      warnings.value = data
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新预警配置
  async function updateWarning(warningId: string, warning: Partial<EstablishmentWarning>) {
    loading.value = true
    try {
      const { data } = await establishmentApi.updateWarning(warningId, warning)
      await fetchWarnings()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取预警规则详情
  async function fetchWarningDetail(warningId: string) {
    loading.value = true
    try {
      const warning = warnings.value.find(w => w.warningId === warningId)
      if (warning) {
        return warning
      }
      // 如果本地没有，从API获取
      const allWarnings = await establishmentApi.getWarnings()
      const foundWarning = allWarnings.data.find(w => w.warningId === warningId)
      if (!foundWarning) {
        throw new Error('预警规则不存在')
      }
      return foundWarning
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建预警规则
  async function createWarning(warning: Partial<EstablishmentWarning>) {
    loading.value = true
    try {
      const { data } = await establishmentApi.createWarning(warning)
      await fetchWarnings()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除预警规则
  async function deleteWarning(warningId: string) {
    loading.value = true
    try {
      await establishmentApi.deleteWarning(warningId)
      await fetchWarnings()
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取预警规则列表
  async function fetchWarningRules() {
    loading.value = true
    try {
      const { data } = await establishmentApi.getWarningRules()
      warningRules.value = data
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取预警规则详情
  async function fetchWarningRuleDetail(ruleId: string) {
    loading.value = true
    try {
      const { data } = await establishmentApi.getWarningRule(ruleId)
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建预警规则
  async function createWarningRule(rule: Partial<EstablishmentWarningRule>) {
    loading.value = true
    try {
      const { data } = await establishmentApi.createWarningRule(rule)
      await fetchWarningRules()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新预警规则
  async function updateWarningRule(ruleId: string, rule: Partial<EstablishmentWarningRule>) {
    loading.value = true
    try {
      const { data } = await establishmentApi.updateWarningRule(ruleId, rule)
      await fetchWarningRules()
      return data
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除预警规则
  async function deleteWarningRule(ruleId: string) {
    loading.value = true
    try {
      await establishmentApi.deleteWarningRule(ruleId)
      await fetchWarningRules()
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 编制校验（用于招聘、调动等场景）
  async function validateEstablishment(params: {
    institutionId: string
    positionCategory?: EstablishmentType
    count?: number
  }) {
    try {
      const { data } = await establishmentApi.validateEstablishment(params)
      return data
    } catch (___error) {
      throw error
    }
  }

  // 下载导入模板
  async function downloadTemplate(type: 'PLAN' | 'ADJUSTMENT') {
    loading.value = true
    try {
      const blob = await establishmentApi.downloadTemplate(type)
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = type === 'PLAN' ? '编制规划导入模板.xlsx' : '编制调整导入模板.xlsx'
      link.click()
      window.URL.revokeObjectURL(url)
    } catch (___error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置状态
  function resetState() {
    plans.value = []
    currentPlan.value = null
    statistics.value = []
    snapshots.value = []
    adjustments.value = []
    transfers.value = []
    changeLogs.value = []
    warnings.value = []
    loading.value = false
    total.value = 0
  }

  return {
    // 状态
    plans,
    currentPlan,
    statistics,
    snapshots,
    adjustments,
    transfers,
    changeLogs,
    warnings,
    warningRules,
    loading,
    total,

    // 计算属性
    totalEstablishment,
    totalActual,
    totalOverstaffed,
    totalUnderstaffed,
    overallUtilizationRate,

    // 方法
    fetchPlans,
    fetchPlanDetail,
    createPlan,
    updatePlan,
    deletePlan,
    submitForApproval,
    importPlans,
    exportPlans,
    fetchStatistics,
    fetchTrends,
    fetchComparison,
    createAdjustment,
    fetchAdjustments,
    createTransfer,
    fetchTransfers,
    fetchSnapshots,
    createSnapshot,
    fetchChangeLogs,
    fetchWarnings,
    fetchWarningDetail,
    createWarning,
    updateWarning,
    deleteWarning,
    fetchWarningRules,
    fetchWarningRuleDetail,
    createWarningRule,
    updateWarningRule,
    deleteWarningRule,
    validateEstablishment,
    downloadTemplate,
    resetState
  }
})
