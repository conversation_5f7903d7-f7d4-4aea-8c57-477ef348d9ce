import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import reportTemplateApi from '@/api/reportTemplate'
import type {
  ReportTemplate,
  TemplateCategory,
  TemplateSearchParams,
  TemplateListResult,
  TemplateVersion
} from '@/types/reportTemplate'
import { useUserStore } from './user'

interface ReportTemplateState {
  templates: ReportTemplate[]
  categories: TemplateCategory[]
  currentTemplate: ReportTemplate | null
  loading: boolean
}

export const useReportTemplateStore = defineStore('reportTemplate', {
  state: (): ReportTemplateState => ({
    templates: [],
    categories: [],
    currentTemplate: null,
    loading: false
  }),

  getters: {
    // 获取模板树形结构

    templateTree(): unknown[] {
      const categoryMap = new Map<string, unknown>()

      // 初始化分类
      this.categories.forEach(cat => {
        categoryMap.set(cat.id, {
          ...cat,
          children: [],
          templates: []
        })
      })

      // 添加模板到对应分类
      this.templates.forEach(template => {
        const category = categoryMap.get(template.categoryId)
        if (category) {
          category.templates.push(template)
        }
      })

      // 构建树形结构

      const tree: unknown[] = []
      categoryMap.forEach(cat => {
        if (!cat.parentId) {
          tree.push(cat)
        } else {
          const parent = categoryMap.get(cat.parentId)
          if (parent) {
            parent.children.push(cat)
          }
        }
      })

      return tree
    },

    // 获取我的模板
    myTemplates(): ReportTemplate[] {
      // 获取当前用户ID
      const userStore = useUserStore()
      const currentUserId = userStore.userInfo?.id || userStore.userInfo?.userId || ''

      if (!currentUserId) {
        return []
      }

      return this.templates.filter(t => t.createdBy === currentUserId)
    },

    // 获取公共模板
    publicTemplates(): ReportTemplate[] {
      return this.templates.filter(t => t.status === 'published')
    }
  },

  actions: {
    // 获取模板列表
    async fetchTemplates(params: TemplateSearchParams): Promise<TemplateListResult> {
      this.loading = true
      try {
        const result = await reportTemplateApi.getTemplates(params)
        this.templates = result.list
        return result
      } finally {
        this.loading = false
      }
    },

    // 获取分类列表
    async fetchCategories(): Promise<TemplateCategory[]> {
      try {
        const categories = await reportTemplateApi.getTemplateCategories()
        this.categories = categories
        return categories
      } catch (___error) {
        return []
      }
    },

    // 获取模板详情
    async getTemplateById(id: string): Promise<ReportTemplate | null> {
      try {
        const template = await reportTemplateApi.getTemplate(id)
        this.currentTemplate = template
        return template
      } catch (___error) {
        return null
      }
    },

    // 创建模板
    async createTemplate(template: Partial<ReportTemplate>): Promise<ReportTemplate> {
      try {
        const newTemplate = await reportTemplateApi.createTemplate(template)
        this.templates.push(newTemplate)
        ElMessage.success('模板创建成功')
        return newTemplate
      } catch (___error) {
        throw error
      }
    },

    // 更新模板
    async updateTemplate(id: string, updates: Partial<ReportTemplate>): Promise<void> {
      try {
        await reportTemplateApi.updateTemplate(id, updates)
        const index = this.templates.findIndex(t => t.id === id)
        if (index > -1) {
          this.templates[index] = {
            ...this.templates[index],
            ...updates,
            updatedAt: new Date()
          }
        }
        ElMessage.success('模板更新成功')
      } catch (___error) {
        throw error
      }
    },

    // 删除模板
    async deleteTemplate(id: string): Promise<void> {
      try {
        await reportTemplateApi.deleteTemplate(id)
        const index = this.templates.findIndex(t => t.id === id)
        if (index > -1) {
          this.templates.splice(index, 1)
        }
        ElMessage.success('模板删除成功')
      } catch (___error) {
        throw error
      }
    },

    // 克隆模板
    async cloneTemplate(id: string): Promise<ReportTemplate> {
      try {
        const clonedTemplate = await reportTemplateApi.cloneTemplate(id)
        this.templates.push(clonedTemplate)
        ElMessage.success('模板克隆成功')
        return clonedTemplate
      } catch (___error) {
        throw error
      }
    },

    // 导出模板
    async exportTemplate(id: string): Promise<void> {
      try {
        const template = this.templates.find(t => t.id === id)
        if (!template) {
          throw new Error('模板不存在')
        }

        const blob = await reportTemplateApi.exportTemplate(id)
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${template.name}_${new Date().toISOString().slice(0, 10)}.json`
        a.click()
        URL.revokeObjectURL(url)

        ElMessage.success('模板导出成功')
      } catch (___error) {
        throw error
      }
    },

    // 导入模板
    async importTemplate(file: File): Promise<ReportTemplate> {
      try {
        const newTemplate = await reportTemplateApi.importTemplate(file)
        this.templates.push(newTemplate)
        ElMessage.success('模板导入成功')
        return newTemplate
      } catch (___error) {
        throw error
      }
    },

    // 获取版本历史
    async getVersionHistory(templateId: string): Promise<TemplateVersion[]> {
      try {
        const versions = await reportTemplateApi.getTemplateVersions(templateId)
        return versions
      } catch (___error) {
        return []
      }
    },

    // 恢复版本
    async restoreVersion(templateId: string, versionId: string): Promise<void> {
      try {
        await reportTemplateApi.restoreTemplateVersion(templateId, versionId)
        ElMessage.success('版本恢复成功')
        // 重新加载模板详情
        await this.getTemplateById(templateId)
      } catch (___error) {
        throw error
      }
    },

    // 发布模板
    async publishTemplate(id: string): Promise<void> {
      try {
        await reportTemplateApi.publishTemplate(id)
        const index = this.templates.findIndex(t => t.id === id)
        if (index > -1) {
          this.templates[index].status = 'published'
        }
        ElMessage.success('模板发布成功')
      } catch (___error) {
        throw error
      }
    },

    // 撤销发布
    async unpublishTemplate(id: string): Promise<void> {
      try {
        await reportTemplateApi.unpublishTemplate(id)
        const index = this.templates.findIndex(t => t.id === id)
        if (index > -1) {
          this.templates[index].status = 'draft'
        }
        ElMessage.success('已撤销发布')
      } catch (___error) {
        throw error
      }
    },

    // 获取我的模板
    async fetchMyTemplates(params?: Partial<TemplateSearchParams>): Promise<TemplateListResult> {
      try {
        const result = await reportTemplateApi.getMyTemplates(params)
        return result
      } catch (___error) {
        throw error
      }
    },

    // 获取公共模板
    async fetchPublicTemplates(
      params?: Partial<TemplateSearchParams>
    ): Promise<TemplateListResult> {
      try {
        const result = await reportTemplateApi.getPublicTemplates(params)
        return result
      } catch (___error) {
        throw error
      }
    },

    // 增加使用次数
    async incrementUsageCount(id: string): Promise<void> {
      try {
        const usageCount = await reportTemplateApi.incrementUsageCount(id)
        const index = this.templates.findIndex(t => t.id === id)
        if (index > -1) {
          this.templates[index].usageCount = usageCount
        }
      } catch (___error) {
        // 不抛出错误，避免影响主流程
      }
    },

    // 搜索模板
    async searchTemplates(
      keyword: string,
      options?: {
        type?: ReportTemplate['type']
        categoryId?: string
        status?: ReportTemplate['status']
      }
    ): Promise<ReportTemplate[]> {
      try {
        const templates = await reportTemplateApi.searchTemplates(keyword, options)
        return templates
      } catch (___error) {
        return []
      }
    },

    // 清除当前模板
    clearCurrentTemplate(): void {
      this.currentTemplate = null
    },

    // 重置store状态
    resetState(): void {
      this.templates = []
      this.categories = []
      this.currentTemplate = null
      this.loading = false
    }
  }
})
