/**
 * Store模块配置和初始化
 */

import { container } from '@/api/core/injectable'
import { EmployeeService } from '@/api/services/employee.service'
import { EmployeeStore } from '../services/employee.store'
import { EMPLOYEE_STORE } from './tokens'
import type { ServiceProvider } from '@/api/core/types'

// Store提供者配置
const storeProviders: ServiceProvider[] = [
  // Employee Store
  {
    provide: EMPLOYEE_STORE,
    useFactory: () => {
      const employeeService = container.get(EmployeeService)
      return new EmployeeStore(employeeService)
    },
    deps: [EmployeeService]
  }
]

/**
 * 初始化Store模块
 */
export function initializeStoreModule(): void {
  // 注册所有Store提供者
  storeProviders.forEach(provider => {
    if (provider.useFactory) {
      container.register(provider.provide, provider.useFactory)
    }
  })
}

/**
 * 获取Store实例
 */

export function getStore<T>(token: unknown): T {
  return container.get(token)
}
