# Pinia Store 依赖注入系统

## 概述

本模块为Pinia Store提供了依赖注入支持，使Store可以注入API服务和其他依赖，提高了可测试性和代码复用性。

## 核心特性

1. **依赖注入**: Store可以注入API服务和其他依赖
2. **类型安全**: 完整的TypeScript类型支持
3. **向后兼容**: 保持与原有Pinia API的兼容性
4. **持久化支持**: 内置持久化配置选项
5. **易于测试**: 可以轻松mock依赖进行单元测试

## 使用方法

### 1. 创建可注入的Store

```typescript
import { ref, computed } from 'vue'
import { Store, BaseStore } from '@/stores/core/injectable-store'
import { Injectable, Inject } from '@/api/core/injectable'
import { EmployeeService } from '@/api/services/employee.service'

@Injectable()
@Store({
  name: 'employee',
  persist: {
    key: 'employee-store',
    storage: localStorage,
    paths: ['employeeList']
  }
})
export class EmployeeStore extends BaseStore {
  constructor(@Inject(EmployeeService) private employeeService: EmployeeService) {
    super()
  }

  setup() {
    // 定义状态
    const employeeList = ref([])
    const loading = ref(false)

    // 定义计算属性
    const employeeCount = computed(() => employeeList.value.length)

    // 定义方法
    const fetchEmployees = async () => {
      loading.value = true
      try {
        const res = await this.employeeService.getList({ page: 1 })
        employeeList.value = res.data.list
      } finally {
        loading.value = false
      }
    }

    // 返回store对象
    return {
      employeeList,
      loading,
      employeeCount,
      fetchEmployees
    }
  }
}
```

### 2. 注册Store

在 `store-module.ts` 中注册Store：

```typescript
import { EMPLOYEE_STORE } from './tokens'
import { EmployeeStore } from '../services/employee.store'
import { EmployeeService } from '@/api/services/employee.service'

const storeProviders: ServiceProvider[] = [
  {
    provide: EMPLOYEE_STORE,
    useFactory: () => {
      const employeeService = container.get(EmployeeService)
      return new EmployeeStore(employeeService)
    },
    deps: [EmployeeService]
  }
]
```

### 3. 在组件中使用

```typescript
import { useEmployeeStore } from '@/stores/composables/useInjectableStore'

export default {
  setup() {
    const employeeStore = useEmployeeStore()

    onMounted(() => {
      employeeStore.fetchEmployees()
    })

    return {
      employees: employeeStore.employeeList,
      loading: employeeStore.loading
    }
  }
}
```

### 4. Store装饰器选项

```typescript
@Store({
  name: 'myStore',           // Store名称（必需）
  persist: true              // 简单持久化
})

// 或者详细配置
@Store({
  name: 'myStore',
  persist: {
    key: 'my-store-key',     // 存储键名
    storage: localStorage,    // 存储介质
    paths: ['someState']     // 需要持久化的状态路径
  }
})
```

## 测试

### 单元测试示例

```typescript
import { container } from '@/api/core/injectable'
import { EmployeeService } from '@/api/services/employee.service'
import { EmployeeStore } from '@/stores/services/employee.store'

describe('EmployeeStore', () => {
  let mockEmployeeService: any
  let store: any

  beforeEach(() => {
    // 创建mock服务
    mockEmployeeService = {
      getList: vi.fn().mockResolvedValue({
        data: { list: [{ id: 1, name: '张三' }] }
      })
    }

    // 注册mock
    container.register(EmployeeService, mockEmployeeService)

    // 创建store实例
    const storeInstance = new EmployeeStore(mockEmployeeService)
    store = storeInstance.setup()
  })

  it('should fetch employees', async () => {
    await store.fetchEmployees()

    expect(mockEmployeeService.getList).toHaveBeenCalled()
    expect(store.employeeList.value).toHaveLength(1)
  })
})
```

## 迁移指南

### 从原有Pinia Store迁移

原有Store:

```typescript
export const useEmployeeStore = defineStore('employee', () => {
  const employeeList = ref([])

  const fetchEmployees = async () => {
    const res = await employeeApi.getList()
    employeeList.value = res.data.list
  }

  return { employeeList, fetchEmployees }
})
```

迁移后:

```typescript
@Injectable()
@Store({ name: 'employee' })
export class EmployeeStore extends BaseStore {
  constructor(@Inject(EmployeeService) private employeeService: EmployeeService) {
    super()
  }

  setup() {
    const employeeList = ref([])

    const fetchEmployees = async () => {
      const res = await this.employeeService.getList()
      employeeList.value = res.data.list
    }

    return { employeeList, fetchEmployees }
  }
}
```

## 最佳实践

1. **单一职责**: 每个Store负责一个业务领域
2. **依赖注入**: 通过构造函数注入服务，不要直接导入
3. **错误处理**: 在Store中统一处理错误
4. **加载状态**: 提供loading和error状态
5. **缓存策略**: 合理使用本地缓存减少API调用

## 高级特性

### 1. Store组合

```typescript
export function useEmployeeManagement() {
  const employeeStore = useEmployeeStore()
  const departmentStore = useDepartmentStore()

  // 组合多个store的功能
  const assignEmployeeToDepartment = async (employeeId, departmentId) => {
    await employeeStore.updateEmployee(employeeId, { departmentId })
    await departmentStore.refreshStatistics()
  }

  return {
    ...employeeStore,
    ...departmentStore,
    assignEmployeeToDepartment
  }
}
```

### 2. Store继承

```typescript
abstract class BaseEntityStore<T> extends BaseStore {
  setup() {
    const items = ref<T[]>([])
    const currentItem = ref<T | null>(null)

    // 通用CRUD方法
    const fetchList = async () => {
      /* ... */
    }
    const create = async (data: T) => {
      /* ... */
    }
    const update = async (id: string, data: T) => {
      /* ... */
    }
    const remove = async (id: string) => {
      /* ... */
    }

    return {
      items,
      currentItem,
      fetchList,
      create,
      update,
      remove,
      ...this.setupExtra() // 子类扩展
    }
  }

  abstract setupExtra(): any
}
```

## 常见问题

### Q: 如何处理Store之间的依赖？

A: 可以在构造函数中注入其他Store：

```typescript
constructor(
  @Inject(EmployeeService) private employeeService: EmployeeService,
  @Inject(DEPARTMENT_STORE) private departmentStore: DepartmentStore
) {
  super()
}
```

### Q: 如何处理Store的初始化顺序？

A: 使用懒加载模式，在需要时才获取Store实例：

```typescript
const getDepartmentStore = () => container.get(DEPARTMENT_STORE)

// 在方法中使用
const someMethod = () => {
  const departmentStore = getDepartmentStore()
  // 使用departmentStore
}
```

### Q: 如何在Store外部使用？

A: 可以直接从容器获取Store实例：

```typescript
import { container } from '@/api/core/injectable'
import { EMPLOYEE_STORE } from '@/stores/core/tokens'

const employeeStore = container.get(EMPLOYEE_STORE).getStore()
```
