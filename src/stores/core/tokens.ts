/**
 * Store依赖注入令牌
 */

import { InjectionToken } from '@/api/core/types'
import type { EmployeeStore } from '../services/employee.store'
import type { OrganizationStore } from '../services/organization.store'
import type { UserStore } from '../services/user.store'
import type { AppStore } from '../services/app.store'
import type { PermissionStore } from '../services/permission.store'

// Store令牌
export const EMPLOYEE_STORE = new InjectionToken<EmployeeStore>('EmployeeStore')
export const ORGANIZATION_STORE = new InjectionToken<OrganizationStore>('OrganizationStore')
export const USER_STORE = new InjectionToken<UserStore>('UserStore')
export const APP_STORE = new InjectionToken<AppStore>('AppStore')
export const PERMISSION_STORE = new InjectionToken<PermissionStore>('PermissionStore')
