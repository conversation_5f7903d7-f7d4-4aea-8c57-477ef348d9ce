/**
 * 可注入的Store基类和装饰器
 */

import { defineStore, type StoreDefinition } from 'pinia'
import { container } from '@/api/core/injectable'
import type { InjectionToken } from '@/api/core/types'

// Store元数据接口
interface StoreMetadata {
  name: string
  persist?:
    | boolean
    | {
        key?: string
        storage?: Storage
        paths?: string[]
      }
}

// Store选项接口
export interface StoreOptions<T = any> {
  name: string
  persist?: StoreMetadata['persist']
  setup: () => T
}

// Store装饰器元数据存储
const storeMetadataMap = new Map<any, StoreMetadata>()

/**
 * Store装饰器
 * 用于标记一个类为可注入的Store
 */
export function Store(options: { name: string; persist?: StoreMetadata['persist'] }) {
  return function <T extends { new (...args: unknown[]): {} }>(constructor: T) {
    // 存储元数据
    storeMetadataMap.set(constructor, {
      name: options.name,
      persist: options.persist
    })

    // 创建代理类以拦截构造函数
    return new Proxy(constructor, {
      construct(target, args) {
        const instance = new target(...args)

        // 获取元数据
        const metadata = storeMetadataMap.get(target)
        if (!metadata) {
          throw new Error('Store metadata not found')
        }

        // 创建Pinia store
        const storeDefinition = (defineStore(metadata.name, () => {
          // 调用setup方法
          if (typeof (instance as unknown).setup === 'function') {
            return (instance as unknown).setup()
          }

          // 如果没有setup方法，返回实例的所有公共属性和方法

          const storeObj: unknown = {}

          // 获取所有属性描述符
          const descriptors = Object.getOwnPropertyDescriptors(instance)
          const prototypeDescriptors = Object.getOwnPropertyDescriptors(
            Object.getPrototypeOf(instance)
          )

          // 合并实例和原型的描述符
          const allDescriptors = { ...prototypeDescriptors, ...descriptors }

          for (const [key, descriptor] of Object.entries(allDescriptors)) {
            // 跳过构造函数和私有属性
            if (key === 'constructor' || key.startsWith('_')) continue

            if (typeof descriptor.value === 'function') {
              // 绑定方法的this上下文
              storeObj[key] = descriptor.value.bind(instance)
            } else if (descriptor.get || descriptor.set) {
              // 处理getter/setter
              Object.defineProperty(storeObj, key, descriptor)
            } else {
              // 普通属性
              storeObj[key] = descriptor.value
            }
          }

          return storeObj
        })(
          // 将store定义存储到实例上
          instance as unknown
        ).__storeDefinition__ = storeDefinition)

        return instance
      }
    })
  }
}

/**
 * 基础Store类
 * 提供通用的store功能
 */
export abstract class BaseStore {
  protected __storeDefinition__?: StoreDefinition

  /**
   * 获取store实例
   */
  getStore() {
    if (!this.__storeDefinition__) {
      throw new Error('Store not initialized. Make sure to use @Store decorator')
    }
    return this.__storeDefinition__()
  }

  /**
   * 子类需要实现的setup方法
   * 返回store的状态、计算属性和方法
   */

  abstract setup(): unknown
}

/**
 * 创建可注入的Store
 */
export function createInjectableStore<T extends BaseStore>(
  StoreClass: new (...args: unknown[]) => T,

  ...deps: unknown[]
): StoreDefinition {
  const instance = new StoreClass(...deps)
  return (instance as unknown).__storeDefinition__
}

/**
 * 在容器中注册Store
 */
export function registerStore<T extends BaseStore>(
  token: InjectionToken<T>,

  StoreClass: new (...args: unknown[]) => T,

  ...deps: unknown[]
) {
  container.register(token, () => {
    const instance = new StoreClass(...deps)
    return instance
  })
}

/**
 * 获取Store实例的辅助函数
 */
export function useInjectableStore<T extends BaseStore>(
  token: InjectionToken<T>
): ReturnType<T['getStore']> {
  const storeInstance = container.get(token)
  return storeInstance.getStore()
}
