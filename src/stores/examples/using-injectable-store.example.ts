/**
 * 使用可注入Store的示例
 */

import { initializeApiModule } from '@/api/core'
import { initializeStoreModule } from '@/stores/core/store-module'
import { useEmployeeStore } from '@/stores/composables/useInjectableStore'

// 1. 初始化模块（通常在应用启动时执行）
export function setupStores() {
  initializeApiModule()
  initializeStoreModule()
}

// 2. 在Vue组件中使用
export function useEmployeeStoreExample() {
  // 使用组合式函数获取store
  const employeeStore = useEmployeeStore()

  // 使用store的状态和方法
  const fetchEmployees = async () => {
    await employeeStore.fetchEmployeeList()
    }

  const searchEmployee = async (keyword: string) => {
    }

  const createNewEmployee = async () => {
      name: '张三',
      employeeNo: 'EMP001',
      departmentId: 'DEPT001'
    })
    }

  return {
    employeeList: employeeStore.employeeList,
    isLoading: employeeStore.isLoading,
    error: employeeStore.error,
    fetchEmployees,
    searchEmployee,
    createNewEmployee
  }
}

// 3. 在测试中使用
import { container } from '@/api/core/injectable'
import { HTTP_CLIENT } from '@/api/core/tokens'
import { EmployeeService } from '@/api/services/employee.service'

export function setupStoreTest() {
  // 创建mock HTTP客户端
  const mockHttpClient = {
    get: vi.fn().mockResolvedValue({ data: { list: [], total: 0 } }),
    post: vi.fn().mockResolvedValue({ data: { id: '1', name: '张三' } }),
    put: vi.fn().mockResolvedValue({ data: {} }),
    delete: vi.fn().mockResolvedValue({ data: null })
  }

  // 注册mock
  container.register(HTTP_CLIENT, mockHttpClient)

  // 创建服务实例
  const employeeService = new EmployeeService()
  container.register(EmployeeService, employeeService)

  // 初始化store模块
  initializeStoreModule()

  // 获取store进行测试
  const employeeStore = useEmployeeStore()

  return {
    mockHttpClient,
    employeeStore
  }
}

// 4. 高级用法 - 自定义Store
import { Store, BaseStore } from '@/stores/core/injectable-store'
import { Injectable, Inject } from '@/api/core/injectable'
import { ref, computed } from 'vue'

@Injectable()
@Store({
  name: 'custom',
  persist: true
})
export class CustomStore extends BaseStore {
  constructor(@Inject(EmployeeService) private employeeService: EmployeeService) {
    super()
  }

  setup() {
    const data = ref<unknown[]>([])
    const loading = ref(false)

    const dataCount = computed(() => data.value.length)

    const fetchData = async () => {
      loading.value = true
      try {
        // 使用注入的服务
        const response = await this.employeeService.getList({ page: 1, pageSize: 10 })
        data.value = response.data.list
      } finally {
        loading.value = false
      }
    }

    return {
      data,
      loading,
      dataCount,
      fetchData
    }
  }
}

// 5. 组合多个Store
export function useMultipleStores() {
  const employeeStore = useEmployeeStore()

  // 组合使用多个store
  const fetchAllData = async () => {
    await Promise.all([
      employeeStore.fetchEmployeeList()
      // organizationStore.fetchOrganizationTree(),
      // userStore.fetchUserInfo()
    ])
  }

  return {
    employeeStore,
    // organizationStore,
    // userStore,
    fetchAllData
  }
}
