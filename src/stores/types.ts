/**
 * Store类型定义
 * 定义所有store模块的类型
 */

import type { UserInfo, Role, Permission } from '@/api/types/business'
import type { RouteRecordRaw } from 'vue-router'

// 用户Store状态类型
export interface UserState {
  userInfo: UserInfo | null
  token: string
  refreshToken: string
  permissions: string[]
  roles: Role[]
  isLogin: boolean
  lastLoginTime: string | null
}

// 权限Store状态类型
export interface PermissionState {
  routes: RouteRecordRaw[]
  dynamicRoutes: RouteRecordRaw[]
  permissions: Map<string, Permission>
  dataPermissions: DataPermission[]
  menuList: MenuItem[]
}

// 应用Store状态类型
export interface AppState {
  // 侧边栏
  sidebar: {
    opened: boolean
    withoutAnimation: boolean
  }
  // 设备类型
  device: 'desktop' | 'mobile'
  // 主题
  theme: 'light' | 'dark'
  // 布局模式
  layout: 'default' | 'top-menu' | 'mix'
  // 标签页
  tagViews: TagView[]
  // 全局loading
  loading: boolean
  // 系统设置
  settings: AppSettings
}

// 组织架构Store状态类型
export interface OrganizationState {
  // 组织树数据
  organizationTree: OrganizationNode[]
  // 扁平化的组织数据（用于快速查找）
  organizationMap: Map<string, OrganizationNode>
  // 当前选中的组织
  currentOrganization: OrganizationNode | null
  // 组织加载状态
  loading: boolean
  // 统计数据

  statistics: unknown | null
}

// 员工Store状态类型
export interface EmployeeState {
  // 员工列表数据缓存
  employeeListCache: Map<string, EmployeeListCache>
  // 员工详情缓存
  employeeDetailCache: Map<string, EmployeeDetail>
  // 当前编辑的员工
  currentEmployee: EmployeeDetail | null
  // 搜索历史
  searchHistory: string[]
}

// 数据权限类型
export interface DataPermission {
  resource: string
  action: string
  conditions?: Record<string, unknown>
}

// 菜单项类型
export interface MenuItem {
  id: string
  path: string
  name: string
  icon?: string
  component?: string
  meta?: {
    title: string
    icon?: string
    hidden?: boolean
    alwaysShow?: boolean
    roles?: string[]
    noCache?: boolean
    breadcrumb?: boolean
  }
  children?: MenuItem[]
}

// 标签页类型
export interface TagView {
  title: string
  name: string
  path: string
  fullPath: string
  query?: Record<string, unknown>
  meta?: Record<string, unknown>
  affix?: boolean
}

// 应用设置类型
export interface AppSettings {
  title: string
  logo: string
  showLogo: boolean
  showBreadcrumb: boolean
  showTagsView: boolean
  fixedHeader: boolean
  sidebarLogo: boolean
  errorLog: string[]
}

// 组织节点类型（使用基础 Organization 类型）
import type { Organization } from '@/types/organization'
export type OrganizationNode = Organization

// 员工列表缓存类型
export interface EmployeeListCache {
  list: unknown[]
  total: number
  page: number
  size: number
  timestamp: number
  params: Record<string, unknown>
}

// 员工详情类型
export interface EmployeeDetail {
  // 基本信息
  id: string
  employeeId: string
  name: string
  gender: string
  birthday: string
  idCard: string

  // 联系信息
  phone: string
  email: string
  address?: string

  // 工作信息
  department: string
  departmentId: string
  position: string
  positionId: string
  employeeType: string
  status: string

  // 时间信息
  joinDate: string
  regularDate?: string
  leaveDate?: string

  // 教育信息
  education: string
  degree: string
  major: string
  school: string

  // 其他信息
  politicalStatus: string
  nationality: string
  nation: string
  nativePlace: string
  maritalStatus: string
  photo?: string

  // 紧急联系人
  emergencyContact?: string
  emergencyPhone?: string

  // 系统信息
  createTime: string
  updateTime: string

  // 扩展信息

  educationHistory?: unknown[]

  workHistory?: unknown[]

  familyMembers?: unknown[]

  certificates?: unknown[]

  contracts?: unknown[]
  tags?: string[]
}
