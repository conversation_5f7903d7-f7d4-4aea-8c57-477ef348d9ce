/**
 * Pinia 日志插件
 * 记录 store 的状态变化和操作调用
 */

import type { PiniaPluginContext } from 'pinia'

export interface LoggerOptions {
  enabled?: boolean
  logLevel?: 'log' | 'info' | 'warn' | 'error'
  logger?: Console
  showTime?: boolean
  collapsed?: boolean
}

const defaultOptions: Required<LoggerOptions> = {
  enabled: process.env.NODE_ENV === 'development',
  logLevel: 'log',
  logger: console,
  showTime: true,
  collapsed: true
}

export function createLoggerPlugin(options: LoggerOptions = {}) {
  const opts = { ...defaultOptions, ...options }

  return (context: PiniaPluginContext) => {
    const { store } = context

    if (!opts.enabled) {
      return
    }

    // Subscribe to store changes
    store.$subscribe((mutation, state) => {
      const time = new Date().toLocaleTimeString()
      const message = `[${store.$id}] ${mutation.type}`

      if (opts.collapsed) {
        opts.logger[opts.logLevel].groupCollapsed(opts.showTime ? `${message} @ ${time}` : message)
      } else {
        opts.logger[opts.logLevel].group(opts.showTime ? `${message} @ ${time}` : message)
      }

      opts.logger[opts.logLevel]('mutation', mutation)
      opts.logger[opts.logLevel]('state', state)
      opts.logger.groupEnd()
    })
  }
}
