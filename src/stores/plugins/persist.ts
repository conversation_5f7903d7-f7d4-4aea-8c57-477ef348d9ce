/**
 * Pinia持久化插件
 * 自动将指定的store数据持久化到localStorage
 */

import type { PiniaPluginContext } from 'pinia'
// 持久化配置接口
export interface PersistOptions {
  // 是否启用持久化
  enabled?: boolean
  // 存储的key，默认为store的id
  key?: string
  // 需要持久化的路径，不指定则持久化整个state
  paths?: string[]
  // 存储方式，默认localStorage
  storage?: Storage
  // 序列化方法
  serializer?: {
     
    serialize: (value: unknown) => string
    deserialize: (value: string) => any
  }
  // 恢复前的钩子
  beforeRestore?: (context: PiniaPluginContext) => void
  // 恢复后的钩子
  afterRestore?: (context: PiniaPluginContext) => void
}

// 默认序列化器
const defaultSerializer = {
  serialize: JSON.stringify,
  deserialize: JSON.parse
}

// 获取嵌套属性值
 
function getNestedValue(obj: unknown, path: string): unknown {
  return path.split('.').reduce((value, key) => value?.[key], obj)
}

// 设置嵌套属性值
 
function setNestedValue(obj: unknown, path: string, value: unknown): void {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  const target = keys.reduce((current, key) => {
    if (!(key in current)) {
      current[key] = {}
    }
    return current[key]
  }, obj)
  target[lastKey] = value
}

// 持久化插件
export function createPersistedState(globalOptions: PersistOptions = {}) {
  return (context: PiniaPluginContext) => {
    const { store, options } = context

    // 获取store的持久化配置
    const persistOptions: PersistOptions = {
      enabled: true,
      storage: localStorage,
      serializer: defaultSerializer,
      ...globalOptions,
      ...((options.persist as PersistOptions) || {})
    }

    // 如果未启用持久化，直接返回
    if (!persistOptions.enabled) {
      return
    }

    const {
      key = store.$id,
      paths,
      storage = localStorage,
      serializer = defaultSerializer,
      beforeRestore,
      afterRestore
    } = persistOptions

    // 恢复数据
    try {
      beforeRestore?.(context)

      const savedState = storage.getItem(key)
      if (savedState) {
        const deserializedState = serializer.deserialize(savedState)

        if (paths && paths.length > 0) {
          // 只恢复指定路径的数据
          paths.forEach(path => {
            const value = getNestedValue(deserializedState, path)
            if (value !== undefined) {
              setNestedValue(store.$state, path, value)
            }
          })
        } else {
          // 恢复整个state
          store.$patch(deserializedState)
        }
      }

      afterRestore?.(context)
    } catch (___error) {
      }

    // 监听状态变化并保存
    let saveTimeout: ReturnType<typeof setTimeout> | null = null

    store.$subscribe((mutation, state) => {
      // 使用防抖避免频繁写入
      if (saveTimeout) {
        clearTimeout(saveTimeout)
      }

      saveTimeout = setTimeout(() => {
        try {
           
          let dataToSave: unknown

          if (paths && paths.length > 0) {
            // 只保存指定路径的数据
            dataToSave = {}
            paths.forEach(path => {
              const value = getNestedValue(state, path)
              if (value !== undefined) {
                setNestedValue(dataToSave, path, value)
              }
            })
          } else {
            // 保存整个state
            dataToSave = state
          }

          storage.setItem(key, serializer.serialize(dataToSave))
        } catch (___error) {
          }
      }, 100)
    })
  }
}

// 声明模块以支持store选项中的persist配置
declare module 'pinia' {
  export interface DefineStoreOptionsBase<S, Store> {
    persist?: boolean | PersistOptions
  }
}
