/**
 * 员工管理Store - 使用依赖注入模式
 */

import { ref, computed } from 'vue'
import { Store, BaseStore } from '../core/injectable-store'
import { Injectable, Inject } from '@/api/core/injectable'
import { EmployeeService } from '@/api/services/employee.service'
import type {
  Employee,
  EmployeeDetail,
  EmployeeSearchParams,
  PersonnelStatus
} from '@/types/employee'

@Injectable()
@Store({
  name: 'employee',
  persist: {
    key: 'employee-store',
    storage: localStorage,
    paths: ['employeeList', 'statistics']
  }
})
export class EmployeeStore extends BaseStore {
  constructor(@Inject(EmployeeService) private employeeService: EmployeeService) {
    super()
  }

  setup() {
    // 状态
    const employeeList = ref<Employee[]>([])
    const currentEmployee = ref<EmployeeDetail | null>(null)
    const loading = ref(false)
    const error = ref<string | null>(null)

    const statistics = ref<{
      totalCount: number
      activeCount: number
      resignedCount: number
      retiredCount: number
      rehiredCount: number
      byDepartment: Array<{ name: string; count: number }>
      byEducation: Array<{ name: string; count: number }>
      byTitle: Array<{ name: string; count: number }>
    }>({
      totalCount: 0,
      activeCount: 0,
      resignedCount: 0,
      retiredCount: 0,
      rehiredCount: 0,
      byDepartment: [],
      byEducation: [],
      byTitle: []
    })

    const completenessStats = ref<{
      totalCount: number
      avgCompleteness: number
      distribution: Array<{
        range: string
        count: number
        percentage: number
      }>
    }>({
      totalCount: 0,
      avgCompleteness: 0,
      distribution: []
    })

    // 计算属性
    const activeEmployees = computed(() =>
      employeeList.value.filter(emp => emp.personnelStatus === 'active')
    )

    const employeeMap = computed(() => {
      const map = new Map<string, Employee>()
      employeeList.value.forEach(emp => {
        map.set(emp.employeeId, emp)
      })
      return map
    })

    const departmentEmployeeCount = computed(() => {
      const countMap = new Map<string, number>()
      employeeList.value.forEach(emp => {
        if (emp.institutionId) {
          const count = countMap.get(emp.institutionId) || 0
          countMap.set(emp.institutionId, count + 1)
        }
      })
      return countMap
    })

    const isLoading = computed(() => loading.value)
    const hasError = computed(() => !!error.value)

    // Actions
    const fetchEmployeeList = async (params?: EmployeeSearchParams) => {
      loading.value = true
      error.value = null

      try {
        const res = await this.employeeService.getList({
          page: 1,
          pageSize: 1000,
          personnelStatus: 'active',
          ...params
        })
        employeeList.value = res.data.list
        return res.data
      } catch (___err) {
        error.value = '获取员工列表失败'
        throw err
      } finally {
        loading.value = false
      }
    }

    const fetchEmployeeDetail = async (employeeId: string) => {
      loading.value = true
      error.value = null

      try {
        const res = await this.employeeService.getDetail(employeeId)
        currentEmployee.value = res.data
        return res.data
      } catch (___err) {
        error.value = '获取员工详情失败'
        throw err
      } finally {
        loading.value = false
      }
    }

    const fetchStatistics = async () => {
      try {
        const res = await this.employeeService.getStatistics()
        statistics.value = res.data
        return res.data
      } catch (___err) {
        throw err
      }
    }

    const createEmployee = async (data: Partial<Employee>) => {
      loading.value = true
      error.value = null

      try {
        const res = await this.employeeService.create(data)
        // 添加到本地缓存
        employeeList.value.push(res.data)
        return res.data
      } catch (___err) {
        error.value = '创建员工失败'
        throw err
      } finally {
        loading.value = false
      }
    }

    const updateEmployee = async (id: string, data: Partial<Employee>) => {
      loading.value = true
      error.value = null

      try {
        const res = await this.employeeService.update(id, data)
        // 更新本地缓存
        updateEmployeeCache(res.data)
        return res.data
      } catch (___err) {
        error.value = '更新员工失败'
        throw err
      } finally {
        loading.value = false
      }
    }

    const deleteEmployee = async (id: string) => {
      loading.value = true
      error.value = null

      try {
        await this.employeeService.delete(id)
        // 从本地缓存移除
        removeEmployeeCache(id)
      } catch (___err) {
        error.value = '删除员工失败'
        throw err
      } finally {
        loading.value = false
      }
    }

    const getEmployeeById = (employeeId: string): Employee | undefined => {
      return employeeMap.value.get(employeeId)
    }

    const getEmployeesByDepartment = (departmentId: string): Employee[] => {
      return employeeList.value.filter(emp => emp.institutionId === departmentId)
    }

    const getEmployeesByStatus = (status: PersonnelStatus): Employee[] => {
      return employeeList.value.filter(emp => emp.personnelStatus === status)
    }

    const searchEmployees = async (params: { keyword: string; size?: number }) => {
      try {
        const res = await this.employeeService.getList({
          keyword: params.keyword,
          page: 1,
          pageSize: params.size || 20
        })
        return res.data
      } catch (___err) {
        throw err
      }
    }

    const updateEmployeeCache = (employee: Employee) => {
      const index = employeeList.value.findIndex(emp => emp.employeeId === employee.employeeId)
      if (index !== -1) {
        employeeList.value[index] = { ...employeeList.value[index], ...employee }
      } else {
        employeeList.value.push(employee)
      }
    }

    const removeEmployeeCache = (employeeId: string) => {
      const index = employeeList.value.findIndex(emp => emp.employeeId === employeeId)
      if (index !== -1) {
        employeeList.value.splice(index, 1)
      }
    }

    const clearCache = () => {
      employeeList.value = []
      currentEmployee.value = null
      error.value = null
    }

    const refreshCache = async () => {
      await Promise.all([fetchEmployeeList(), fetchStatistics()])
    }

    // 返回store对象
    return {
      // 状态
      employeeList,
      currentEmployee,
      statistics,
      completenessStats,
      loading,
      error,

      // 计算属性
      activeEmployees,
      employeeMap,
      departmentEmployeeCount,
      isLoading,
      hasError,

      // Actions
      fetchEmployeeList,
      fetchEmployeeDetail,
      fetchStatistics,
      createEmployee,
      updateEmployee,
      deleteEmployee,
      getEmployeeById,
      getEmployeesByDepartment,
      getEmployeesByStatus,
      searchEmployees,
      updateEmployeeCache,
      removeEmployeeCache,
      clearCache,
      refreshCache
    }
  }
}
