/**
 * Store统一出口
 * 配置和导出所有store模块
 */

import type { App } from 'vue'
import { createPinia } from 'pinia'
import { createPersistedState } from './plugins/persist'
import { createLoggerPlugin } from './plugins/logger'

// 创建pinia实例
const pinia = createPinia()

// 注册持久化插件
pinia.use(
  createPersistedState({
    // 全局持久化配置
    storage: localStorage,
    serializer: {
      serialize: JSON.stringify,
      deserialize: JSON.parse
    }
  })
)

// 开发环境注册日志插件
if (import.meta.env.DEV) {
  pinia.use(
    createLoggerPlugin({
      enabled: true,
      collapsed: true,
      logActions: true,
      logMutations: true
    })
  )
}

// 安装pinia
export function setupStore(app: App): void {
  app.use(pinia)
}

// 导出pinia实例
export { pinia }

// 导出所有store
export { useUserStore } from './modules/user'
export { usePermissionStore } from './modules/permission'
export { useAppStore } from './modules/app'
export { useOrganizationStore } from './modules/organization'
export { useEmployeeStore } from './modules/employee'
export { useTagStore } from './modules/tag'
export { usePositionStore } from './modules/position'
export { useEstablishmentStore } from './modules/establishment'
export { useWorkflowStore } from './modules/workflow'
export { useWorkflowDesignerStore } from './modules/workflowDesigner'
export { useSalaryStore } from './modules/salary'
export { useAuthStore } from './modules/auth'
export { useSessionStore } from './modules/session'

// 导出store类型
export type * from './types'
