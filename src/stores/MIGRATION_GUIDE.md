# Pinia Store 依赖注入迁移指南

## 概述

本指南帮助您从传统的Pinia Store迁移到基于依赖注入的Store模式。新模式提供了更好的可测试性、依赖管理和代码组织。

## 为什么要迁移？

1. **更好的测试性**: 可以轻松mock依赖进行单元测试
2. **依赖管理**: 明确的依赖声明，避免循环依赖
3. **代码复用**: 通过继承和组合复用Store逻辑
4. **类型安全**: 更好的TypeScript类型推导
5. **关注点分离**: Store只负责状态管理，业务逻辑委托给服务

## 迁移步骤

### 1. 安装和初始化

在应用入口（`main.ts`）初始化：

```typescript
import { initializeApiModule } from '@/api/core'
import { initializeStoreModule } from '@/stores/core/store-module'

// 初始化依赖注入系统
initializeApiModule()
initializeStoreModule()

const app = createApp(App)
app.use(pinia)
```

### 2. 迁移Store定义

#### 原有方式

```typescript
// stores/modules/employee.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import employeeApi from '@/api/modules/employee'

export const useEmployeeStore = defineStore('employee', () => {
  const employeeList = ref<Employee[]>([])
  const loading = ref(false)

  const activeEmployees = computed(() => employeeList.value.filter(e => e.status === 'active'))

  const fetchEmployees = async () => {
    loading.value = true
    try {
      const res = await employeeApi.getList()
      employeeList.value = res.data.list
    } finally {
      loading.value = false
    }
  }

  return {
    employeeList,
    loading,
    activeEmployees,
    fetchEmployees
  }
})
```

#### 新方式

```typescript
// stores/services/employee.store.ts
import { ref, computed } from 'vue'
import { Store, BaseStore } from '../core/injectable-store'
import { Injectable, Inject } from '@/api/core/injectable'
import { EmployeeService } from '@/api/services/employee.service'

@Injectable()
@Store({
  name: 'employee',
  persist: {
    paths: ['employeeList']
  }
})
export class EmployeeStore extends BaseStore {
  constructor(@Inject(EmployeeService) private employeeService: EmployeeService) {
    super()
  }

  setup() {
    const employeeList = ref<Employee[]>([])
    const loading = ref(false)

    const activeEmployees = computed(() => employeeList.value.filter(e => e.status === 'active'))

    const fetchEmployees = async () => {
      loading.value = true
      try {
        const res = await this.employeeService.getList()
        employeeList.value = res.data.list
      } finally {
        loading.value = false
      }
    }

    return {
      employeeList,
      loading,
      activeEmployees,
      fetchEmployees
    }
  }
}
```

### 3. 注册Store

在 `stores/core/store-module.ts` 中注册：

```typescript
import { EMPLOYEE_STORE } from './tokens'
import { EmployeeStore } from '../services/employee.store'

const storeProviders: ServiceProvider[] = [
  {
    provide: EMPLOYEE_STORE,
    useFactory: () => {
      const employeeService = container.get(EmployeeService)
      return new EmployeeStore(employeeService)
    },
    deps: [EmployeeService]
  }
]
```

### 4. 创建组合式函数

在 `stores/composables/useInjectableStore.ts` 中：

```typescript
import { createStoreComposable } from '../core/composables'
import { EMPLOYEE_STORE } from '../core/tokens'

export const useEmployeeStore = createStoreComposable(EMPLOYEE_STORE)
```

### 5. 更新组件使用

组件中的使用方式保持不变：

```vue
<script setup>
import { useEmployeeStore } from '@/stores/composables/useInjectableStore'

const employeeStore = useEmployeeStore()

onMounted(() => {
  employeeStore.fetchEmployees()
})
</script>
```

### 6. 创建兼容层（可选）

为了平滑迁移，可以创建兼容层：

```typescript
// stores/modules/employee-injected.ts
import { defineStore } from 'pinia'
import { container } from '@/api/core/injectable'
import { EMPLOYEE_STORE } from '../core/tokens'

const employeeStoreInstance = container.get(EMPLOYEE_STORE)

export const useEmployeeStore = defineStore('employee', () => {
  return employeeStoreInstance.setup()
})
```

## 迁移检查清单

- [ ] 初始化依赖注入系统
- [ ] 将API调用迁移到服务类
- [ ] 创建Store类并添加装饰器
- [ ] 实现setup方法
- [ ] 注册Store到容器
- [ ] 创建注入令牌
- [ ] 更新组合式函数导出
- [ ] 测试Store功能
- [ ] 更新单元测试
- [ ] 移除旧的Store文件

## 常见迁移场景

### 1. Store之间的依赖

原有方式：

```typescript
import { useUserStore } from './user'

export const useEmployeeStore = defineStore('employee', () => {
  const userStore = useUserStore()
  // 使用userStore
})
```

新方式：

```typescript
@Injectable()
@Store({ name: 'employee' })
export class EmployeeStore extends BaseStore {
  constructor(@Inject(USER_STORE) private userStore: UserStore) {
    super()
  }
}
```

### 2. 持久化配置

原有方式（使用pinia-plugin-persistedstate）：

```typescript
export const useEmployeeStore = defineStore(
  'employee',
  () => {
    // ...
  },
  {
    persist: {
      key: 'employee-store',
      storage: localStorage,
      paths: ['employeeList']
    }
  }
)
```

新方式：

```typescript
@Store({
  name: 'employee',
  persist: {
    key: 'employee-store',
    storage: localStorage,
    paths: ['employeeList']
  }
})
```

### 3. Getters迁移

原有方式：

```typescript
const getters = {
  getEmployeeById: state => (id: string) => {
    return state.employeeList.find(e => e.id === id)
  }
}
```

新方式：

```typescript
setup() {
  const employeeList = ref<Employee[]>([])

  const getEmployeeById = (id: string) => {
    return employeeList.value.find(e => e.id === id)
  }

  return {
    employeeList,
    getEmployeeById
  }
}
```

## 测试迁移

### 原有测试

```typescript
import { setActivePinia, createPinia } from 'pinia'
import { useEmployeeStore } from '@/stores/modules/employee'

vi.mock('@/api/modules/employee', () => ({
  default: {
    getList: vi.fn().mockResolvedValue({ data: { list: [] } })
  }
}))

describe('EmployeeStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should fetch employees', async () => {
    const store = useEmployeeStore()
    await store.fetchEmployees()
    expect(store.employeeList).toEqual([])
  })
})
```

### 新测试方式

```typescript
import { EmployeeStore } from '@/stores/services/employee.store'
import { EmployeeService } from '@/api/services/employee.service'

describe('EmployeeStore', () => {
  let mockEmployeeService: any
  let store: any

  beforeEach(() => {
    mockEmployeeService = {
      getList: vi.fn().mockResolvedValue({ data: { list: [] } })
    }

    const storeInstance = new EmployeeStore(mockEmployeeService)
    store = storeInstance.setup()
  })

  it('should fetch employees', async () => {
    await store.fetchEmployees()
    expect(mockEmployeeService.getList).toHaveBeenCalled()
    expect(store.employeeList.value).toEqual([])
  })
})
```

## 性能考虑

1. **懒加载**: Store实例在首次使用时创建
2. **单例模式**: 每个Store只创建一次实例
3. **选择性持久化**: 只持久化必要的状态
4. **计算属性缓存**: 使用computed确保派生状态的缓存

## 故障排除

### 问题: Store未定义

确保在使用前初始化了模块：

```typescript
initializeApiModule()
initializeStoreModule()
```

### 问题: 依赖注入失败

检查是否正确注册了依赖：

```typescript
container.register(EmployeeService, () => new EmployeeService())
```

### 问题: 持久化不工作

确保Store装饰器中配置了persist选项，并且指定了正确的paths。

## 总结

迁移到依赖注入的Store模式需要一些初始工作，但会带来长期的好处：

- 更清晰的依赖关系
- 更好的可测试性
- 更强的类型安全
- 更好的代码组织

建议逐步迁移，先从独立的Store开始，再处理有依赖关系的Store。
