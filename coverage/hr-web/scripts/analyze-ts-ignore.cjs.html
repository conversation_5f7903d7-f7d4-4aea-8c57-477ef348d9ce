
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for hr-web/scripts/analyze-ts-ignore.cjs</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">hr-web/scripts</a> analyze-ts-ignore.cjs</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/191</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/191</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >#!/usr/bin/env node</span></span></span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * 分析 @ts-ignore 使用情况</span>
<span class="cstat-no" title="statement not covered" > * 统计各模块中 @ts-ignore 的分布，为逐步清理提供数据支持</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >const fs = require('fs')</span>
<span class="cstat-no" title="statement not covered" >const path = require('path')</span>
<span class="cstat-no" title="statement not covered" >const glob = require('glob')</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >console.log('正在分析 @ts-ignore 使用情况...\n')</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 统计数据</span>
<span class="cstat-no" title="statement not covered" >const moduleStats = {}</span>
<span class="cstat-no" title="statement not covered" >const fileStats = []</span>
<span class="cstat-no" title="statement not covered" >let totalIgnores = 0</span>
<span class="cstat-no" title="statement not covered" >let totalTsExpectErrors = 0</span>
<span class="cstat-no" title="statement not covered" >let totalTsNocheck = 0</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 查找所有 TypeScript 和 Vue 文件</span>
<span class="cstat-no" title="statement not covered" >const files = glob.sync('src/**/*.{ts,tsx,vue}', {</span>
<span class="cstat-no" title="statement not covered" >  ignore: ['**/node_modules/**', '**/dist/**']</span>
<span class="cstat-no" title="statement not covered" >})</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 分析每个文件</span>
<span class="cstat-no" title="statement not covered" >files.forEach(file =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const content = fs.readFileSync(file, 'utf8')</span>
<span class="cstat-no" title="statement not covered" >  const lines = content.split('\n')</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  let fileIgnoreCount = 0</span>
<span class="cstat-no" title="statement not covered" >  let fileTsExpectErrorCount = 0</span>
<span class="cstat-no" title="statement not covered" >  let fileTsNocheckCount = 0</span>
<span class="cstat-no" title="statement not covered" >  const ignoreDetails = []</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  lines.forEach((line, index) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    // 匹配各种 TypeScript 注释</span>
<span class="cstat-no" title="statement not covered" >    if (line.includes('@ts-ignore')) {</span>
<span class="cstat-no" title="statement not covered" >      fileIgnoreCount++</span>
<span class="cstat-no" title="statement not covered" >      totalIgnores++</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      // 获取下一行代码作为上下文</span>
<span class="cstat-no" title="statement not covered" >      const nextLine = lines[index + 1] || ''</span>
<span class="cstat-no" title="statement not covered" >      ignoreDetails.push({</span>
<span class="cstat-no" title="statement not covered" >        line: index + 1,</span>
<span class="cstat-no" title="statement not covered" >        context: nextLine.trim().substring(0, 80)</span>
<span class="cstat-no" title="statement not covered" >      })</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    if (line.includes('@ts-expect-error')) {</span>
<span class="cstat-no" title="statement not covered" >      fileTsExpectErrorCount++</span>
<span class="cstat-no" title="statement not covered" >      totalTsExpectErrors++</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    if (line.includes('@ts-nocheck')) {</span>
<span class="cstat-no" title="statement not covered" >      fileTsNocheckCount++</span>
<span class="cstat-no" title="statement not covered" >      totalTsNocheck++</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  })</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  if (fileIgnoreCount &gt; 0 || fileTsExpectErrorCount &gt; 0 || fileTsNocheckCount &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >    // 提取模块名</span>
<span class="cstat-no" title="statement not covered" >    const relativePath = path.relative(process.cwd(), file)</span>
<span class="cstat-no" title="statement not covered" >    const parts = relativePath.split('/')</span>
<span class="cstat-no" title="statement not covered" >    const moduleName = parts[1] || 'root'</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // 更新模块统计</span>
<span class="cstat-no" title="statement not covered" >    if (!moduleStats[moduleName]) {</span>
<span class="cstat-no" title="statement not covered" >      moduleStats[moduleName] = {</span>
<span class="cstat-no" title="statement not covered" >        ignores: 0,</span>
<span class="cstat-no" title="statement not covered" >        expectErrors: 0,</span>
<span class="cstat-no" title="statement not covered" >        nochecks: 0,</span>
<span class="cstat-no" title="statement not covered" >        files: 0</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    moduleStats[moduleName].ignores += fileIgnoreCount</span>
<span class="cstat-no" title="statement not covered" >    moduleStats[moduleName].expectErrors += fileTsExpectErrorCount</span>
<span class="cstat-no" title="statement not covered" >    moduleStats[moduleName].nochecks += fileTsNocheckCount</span>
<span class="cstat-no" title="statement not covered" >    moduleStats[moduleName].files++</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // 记录文件详情</span>
<span class="cstat-no" title="statement not covered" >    fileStats.push({</span>
<span class="cstat-no" title="statement not covered" >      file: relativePath,</span>
<span class="cstat-no" title="statement not covered" >      ignores: fileIgnoreCount,</span>
<span class="cstat-no" title="statement not covered" >      expectErrors: fileTsExpectErrorCount,</span>
<span class="cstat-no" title="statement not covered" >      nochecks: fileTsNocheckCount,</span>
<span class="cstat-no" title="statement not covered" >      details: ignoreDetails</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >})</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 按 ignore 数量排序</span>
<span class="cstat-no" title="statement not covered" >fileStats.sort((a, b) =&gt; b.ignores - a.ignores)</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 生成报告</span>
<span class="cstat-no" title="statement not covered" >console.log('=== @ts-ignore 分析报告 ===\n')</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >console.log('📊 总体统计:')</span>
<span class="cstat-no" title="statement not covered" >console.log(`  - @ts-ignore 总数: ${totalIgnores}`)</span>
<span class="cstat-no" title="statement not covered" >console.log(`  - @ts-expect-error 总数: ${totalTsExpectErrors}`)</span>
<span class="cstat-no" title="statement not covered" >console.log(`  - @ts-nocheck 总数: ${totalTsNocheck}`)</span>
<span class="cstat-no" title="statement not covered" >console.log(`  - 受影响的文件数: ${fileStats.length}`)</span>
<span class="cstat-no" title="statement not covered" >console.log()</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >console.log('📁 按模块统计:')</span>
<span class="cstat-no" title="statement not covered" >Object.entries(moduleStats)</span>
<span class="cstat-no" title="statement not covered" >  .sort((a, b) =&gt; b[1].ignores - a[1].ignores)</span>
<span class="cstat-no" title="statement not covered" >  .forEach(([module, stats]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    console.log(`  ${module}:`)</span>
<span class="cstat-no" title="statement not covered" >    console.log(`    文件数: ${stats.files}`)</span>
<span class="cstat-no" title="statement not covered" >    console.log(`    @ts-ignore: ${stats.ignores}`)</span>
<span class="cstat-no" title="statement not covered" >    console.log(`    @ts-expect-error: ${stats.expectErrors}`)</span>
<span class="cstat-no" title="statement not covered" >    console.log(`    @ts-nocheck: ${stats.nochecks}`)</span>
<span class="cstat-no" title="statement not covered" >  })</span>
<span class="cstat-no" title="statement not covered" >console.log()</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >console.log('🔥 @ts-ignore 最多的文件 (Top 20):')</span>
<span class="cstat-no" title="statement not covered" >fileStats.slice(0, 20).forEach(fileStat =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  console.log(`  ${fileStat.file}: ${fileStat.ignores}`)</span>
<span class="cstat-no" title="statement not covered" >})</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 分析 ignore 的原因</span>
<span class="cstat-no" title="statement not covered" >console.log('\n📋 @ts-ignore 使用场景分析:')</span>
<span class="cstat-no" title="statement not covered" >const ignorePatterns = {</span>
<span class="cstat-no" title="statement not covered" >  'import': 0,</span>
<span class="cstat-no" title="statement not covered" >  'require': 0,</span>
<span class="cstat-no" title="statement not covered" >  'window': 0,</span>
<span class="cstat-no" title="statement not covered" >  'document': 0,</span>
<span class="cstat-no" title="statement not covered" >  'any': 0,</span>
<span class="cstat-no" title="statement not covered" >  'props': 0,</span>
<span class="cstat-no" title="statement not covered" >  'emit': 0,</span>
<span class="cstat-no" title="statement not covered" >  'refs': 0,</span>
<span class="cstat-no" title="statement not covered" >  'router': 0,</span>
<span class="cstat-no" title="statement not covered" >  'store': 0</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >fileStats.forEach(fileStat =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  fileStat.details.forEach(detail =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    Object.keys(ignorePatterns).forEach(pattern =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (detail.context.toLowerCase().includes(pattern)) {</span>
<span class="cstat-no" title="statement not covered" >        ignorePatterns[pattern]++</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >  })</span>
<span class="cstat-no" title="statement not covered" >})</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >console.log('常见的 @ts-ignore 场景:')</span>
<span class="cstat-no" title="statement not covered" >Object.entries(ignorePatterns)</span>
<span class="cstat-no" title="statement not covered" >  .sort((a, b) =&gt; b[1] - a[1])</span>
<span class="cstat-no" title="statement not covered" >  .forEach(([pattern, count]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (count &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      console.log(`  - ${pattern} 相关: ${count}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  })</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 生成详细报告</span>
<span class="cstat-no" title="statement not covered" >const report = {</span>
<span class="cstat-no" title="statement not covered" >  summary: {</span>
<span class="cstat-no" title="statement not covered" >    totalIgnores,</span>
<span class="cstat-no" title="statement not covered" >    totalTsExpectErrors,</span>
<span class="cstat-no" title="statement not covered" >    totalTsNocheck,</span>
<span class="cstat-no" title="statement not covered" >    affectedFiles: fileStats.length,</span>
<span class="cstat-no" title="statement not covered" >    analyzedFiles: files.length</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  moduleStats,</span>
<span class="cstat-no" title="statement not covered" >  topFiles: fileStats.slice(0, 50),</span>
<span class="cstat-no" title="statement not covered" >  patterns: ignorePatterns</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >const reportPath = path.join(process.cwd(), 'docs/修复二/ts-ignore-analysis.json')</span>
<span class="cstat-no" title="statement not covered" >fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))</span>
<span class="cstat-no" title="statement not covered" >console.log(`\n✅ 详细报告已保存到: ${reportPath}`)</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 生成清理建议</span>
<span class="cstat-no" title="statement not covered" >console.log('\n💡 清理建议:')</span>
<span class="cstat-no" title="statement not covered" >console.log('1. 优先处理 @ts-ignore 数量最多的模块')</span>
<span class="cstat-no" title="statement not covered" >console.log('2. 将可以使用类型断言的 @ts-ignore 替换为 as 语法')</span>
<span class="cstat-no" title="statement not covered" >console.log('3. 为第三方库创建类型定义文件')</span>
<span class="cstat-no" title="statement not covered" >console.log('4. 将部分 @ts-ignore 改为 @ts-expect-error 并添加说明')</span>
<span class="cstat-no" title="statement not covered" >console.log(`5. 目标：第一批减少 ${Math.floor(totalIgnores * 0.3)} 个（30%）`)</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 生成模块优先级</span>
<span class="cstat-no" title="statement not covered" >console.log('\n🎯 建议的清理顺序:')</span>
<span class="cstat-no" title="statement not covered" >const priorityModules = Object.entries(moduleStats)</span>
<span class="cstat-no" title="statement not covered" >  .sort((a, b) =&gt; b[1].ignores - a[1].ignores)</span>
<span class="cstat-no" title="statement not covered" >  .slice(0, 5)</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >priorityModules.forEach(([module, stats], index) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  console.log(`  ${index + 1}. ${module} 模块 (${stats.ignores} 个)`)</span>
<span class="cstat-no" title="statement not covered" >})</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-23T02:21:14.177Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    