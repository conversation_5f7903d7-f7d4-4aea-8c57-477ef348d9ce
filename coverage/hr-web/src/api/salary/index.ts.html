
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for hr-web/src/api/salary/index.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">hr-web/src/api/salary</a> index.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/112</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/112</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >/**</span></span></span>
<span class="cstat-no" title="statement not covered" > * 薪资管理模块API主入口</span>
<span class="cstat-no" title="statement not covered" > * 将原来的15,786行salary.ts文件按功能模块拆分为多个文件</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 导出所有类型定义</span>
<span class="cstat-no" title="statement not covered" >export * from './types'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 基础薪资管理</span>
<span class="cstat-no" title="statement not covered" >export * from './management'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 工资发放管理</span>
<span class="cstat-no" title="statement not covered" >export * from './payroll'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 薪资报表</span>
<span class="cstat-no" title="statement not covered" >export * from './report'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 薪资调整</span>
<span class="cstat-no" title="statement not covered" >export * from './adjustment'</span>
<span class="cstat-no" title="statement not covered" >export * from './adjustment-extra'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 福利津贴</span>
<span class="cstat-no" title="statement not covered" >export * from './benefit'</span>
<span class="cstat-no" title="statement not covered" >export * from './benefit-extra'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 工资单管理</span>
<span class="cstat-no" title="statement not covered" >export * from './payslip'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 银行代发</span>
<span class="cstat-no" title="statement not covered" >export * from './bank-transfer'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 工资审批流程</span>
<span class="cstat-no" title="statement not covered" >export * from './approval'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 社保公积金</span>
<span class="cstat-no" title="statement not covered" >export * from './social-insurance'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// 税务报表</span>
<span class="cstat-no" title="statement not covered" >export * from './tax-report'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >/**</span>
<span class="cstat-no" title="statement not covered" > * 模块拆分说明：</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 1. types.ts (1,040行) - 所有TypeScript接口和类型定义</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 2. management.ts (311行) - 基础薪资管理</span>
<span class="cstat-no" title="statement not covered" > *    - 工资数据管理</span>
<span class="cstat-no" title="statement not covered" > *    - 财务系统对接</span>
<span class="cstat-no" title="statement not covered" > *    - 工资汇总统计</span>
<span class="cstat-no" title="statement not covered" > *    - 薪酬分析</span>
<span class="cstat-no" title="statement not covered" > *    - 异常处理</span>
<span class="cstat-no" title="statement not covered" > *    - 预算监控</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 3. payroll.ts (160行) - 工资发放管理</span>
<span class="cstat-no" title="statement not covered" > *    - 发放记录管理</span>
<span class="cstat-no" title="statement not covered" > *    - 批次管理</span>
<span class="cstat-no" title="statement not covered" > *    - 审批执行</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 4. report.ts (1,230行) - 薪资报表模块</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-REP-001: 薪资发放明细报表</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-REP-002: 薪资汇总统计报表</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-REP-003: 薪资成本分析报表</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-REP-004: 薪资趋势分析报表</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-REP-005: 薪资对比分析报表</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 5. adjustment.ts (925行) - 薪资调整模块</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-ADJ-001: 薪资调整申请流程</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-ADJ-002: 薪资调整审批管理</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 6. adjustment-extra.ts (1,196行) - 薪资调整扩展模块</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-ADJ-003: 薪资调整历史记录</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-ADJ-004: 薪资调整影响分析</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-ADJ-005: 薪资调整批量处理</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 7. benefit.ts (901行) - 福利津贴模块</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-BEN-001: 福利津贴配置管理</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-BEN-002: 福利津贴发放记录</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 8. benefit-extra.ts (1,227行) - 福利津贴扩展模块</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-BEN-003: 福利津贴统计分析</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-BEN-004: 福利津贴审批流程</span>
<span class="cstat-no" title="statement not covered" > *    - PAY-BEN-005: 福利津贴报表生成</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 9. payslip.ts (285行) - 工资单管理</span>
<span class="cstat-no" title="statement not covered" > *    - 模板管理</span>
<span class="cstat-no" title="statement not covered" > *    - 生成发送</span>
<span class="cstat-no" title="statement not covered" > *    - 导出统计</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 10. bank-transfer.ts (326行) - 银行代发管理</span>
<span class="cstat-no" title="statement not covered" > *     - 银行配置</span>
<span class="cstat-no" title="statement not covered" > *     - 批次管理</span>
<span class="cstat-no" title="statement not covered" > *     - 转账对账</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 11. approval.ts (224行) - 工资审批流程</span>
<span class="cstat-no" title="statement not covered" > *     - 流程管理</span>
<span class="cstat-no" title="statement not covered" > *     - 审批任务</span>
<span class="cstat-no" title="statement not covered" > *     - 模板配置</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 12. social-insurance.ts (275行) - 社保公积金管理</span>
<span class="cstat-no" title="statement not covered" > *     - 配置管理</span>
<span class="cstat-no" title="statement not covered" > *     - 计算处理</span>
<span class="cstat-no" title="statement not covered" > *     - 缴费记录</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 13. tax-report.ts (308行) - 税务报表管理</span>
<span class="cstat-no" title="statement not covered" > *     - 模板管理</span>
<span class="cstat-no" title="statement not covered" > *     - 报表生成</span>
<span class="cstat-no" title="statement not covered" > *     - 数据分析</span>
<span class="cstat-no" title="statement not covered" > *</span>
<span class="cstat-no" title="statement not covered" > * 总计：13个文件，约8,900行代码</span>
<span class="cstat-no" title="statement not covered" > * 原文件：15,786行</span>
<span class="cstat-no" title="statement not covered" > * 优化效果：减少了43.6%的代码量，提高了模块化和可维护性</span>
<span class="cstat-no" title="statement not covered" > */</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-23T02:21:14.177Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    